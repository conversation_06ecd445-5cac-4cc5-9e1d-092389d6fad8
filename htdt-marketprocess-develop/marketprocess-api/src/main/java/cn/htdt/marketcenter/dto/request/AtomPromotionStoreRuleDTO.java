package cn.htdt.marketcenter.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 活动店铺规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="PromotionStoreRuleDomain对象", description="活动店铺规则表")
public class AtomPromotionStoreRuleDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "规则编号")
    private String ruleNo;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "活动店铺类型(1001:全部店铺 1002:报名活动)")
    private String storeType;

    @ApiModelProperty(value = "报名活动编号")
    private String enrollNo;

    @ApiModelProperty(value = "是否可用:默认1，1：可用，其余不可用")
    private Integer disableFlag;

    @ApiModelProperty(value = "活动店铺详情")
    private List<AtomPromotionStoreDetailDTO> list = new ArrayList<>();

}
