package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class AtomReqPointsConfigDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 配置编号
     */
    private String configNo;

    /**
     * 配置类型(1001:积分获得上限 1002:粉丝下单配置 1003:关注店铺赠积分  2001:积分兑换礼品 2002:积分兑换抽奖机会)
     */
    private String configType;

    /**
     * 积分上限
     */
    private Integer pointsLimit;

    /**
     * 订单实付金额每满（元）
     */
    private BigDecimal orderAmount;

    /**
     * 赠送积分数（个）
     */
    private Integer presentPoints;
    /**
     * 积分抵扣比例默认抵扣1分钱
     */
    private Integer deductionScale;
    /**
     * 订单金额门槛：1001-不限制，1002-限制
     */
    private String orderAmountLimit;
    /**
     * 订单应付金额（元）
     */
    private BigDecimal orderHandleAmount;
    /**
     * 抵扣金额门槛：1001-不限制，1002-限制
     */
    private String deductionAmountLimit;
    /**
     * 积分抵扣上限
     */
    private Integer pointsDeductionLimit;
    /**
     * 积分适用渠道(1001:一体机开单 1002:app及pc开单 1003:汇享购网店订单)
     */
    private String useChannel;
    /**
     * 是否可用:默认1，1：启用，2：禁用
     */
    private Integer disableFlag;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;

    /**

    /**
     * 数据逻辑删除标识，默认1未删除，其余已删除
     */
    private Integer deleteFlag;

    /**
     * 积分配置类型（1.门店，2.商家）
     */
    private String pointsType;

}
