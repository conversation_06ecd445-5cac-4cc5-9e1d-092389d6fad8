package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 短信发送记录 请求DTO
 *
 * <AUTHOR>
 * @date 2021-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AtomReqSmsSendRecordDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 批次编号
     */
    private String batchNo;

    /**
     * 渠道Id，系统标识，枚举BusinessSystemEnum。boss：超级老板，shop：汇掌柜，cash：一体机
     */
    private String channelId;

    /**
     * 发送类型（1000:短信营销，2000:优惠券，3000:粉丝导购）
     */
    private String sourceType;

    /**
     * 短信类型（1001:活动预热，1002:潜在人群，1003:常购人群，1004:粉丝分组，1005:自定义，1006:生日关怀，2001:店铺手动发粉丝券，2002:平台手动发粉丝券，2003:平台手动发代理人券，3001:今日成交粉丝场景导购，3002:最有可能成交的顾客场景导购）
     */
    private String smsType;

    /**
     * 发送内容
     */
    private String sendContent;

    /**
     * 发送状态（1001:发送中 1002:发送完成 1003:发送失败）
     */
    private String sendStatus;

    /**
     * 发送人数
     */
    private Integer sendPeopleNum;

    /**
     * 短信单次条数
     */
    private Integer smsSingleNum;

    /**
     * 发送总条数
     */
    private Integer sendTotalNum;

    /**
     * 发送成功条数
     */
    private Integer sendSuccessNum;

    /**
     * 发送失败条数
     */
    private Integer sendFailNum;

    /**
     * 发送对象（1001:店内所有粉丝,1002:有购买行为的粉丝,1003:加入购物车的粉丝,1004:分组粉丝,1005:自定义粉丝）
     */
    private String sendObject;

    /**
     * 发送对象参数
     */
    private String sendObjectParam;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 短信发送起始时间
     */
    private String sendTimeStart;

    /**
     * 短信发送截止时间
     */
    private String sendTimeEnd;

    /**
     * 手机号集合
     */
    private List<String> telephoneList;
    /**
     * 店铺编码、店铺编码
     */
    private String storeKeyWord;

    /**
     * 手机号集合
     */
    private List<String> dsTelephoneList;


    /**
     * 定时发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime timingSendTime;
    /**
     * 发送时间类型:1001-立即发送，1002-定时发送
     */
    private String sendTimeType;


    private Integer timingSendTimeFlag;

    /**
     * 生日节日配置编号
     */
    private String configNo;

    /**
     * 短信发送其他参数集合
     */
    private List<AtomReqSmsSendParamsDTO> paramsDTOList;

    /**
     * 发送失败原因
     */
    private String sendFailureReason;
}
