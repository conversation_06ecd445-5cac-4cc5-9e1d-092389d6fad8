package cn.htdt.marketcenter.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 优惠券转商城券规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CouponExchangeMallRuleDomain对象", description="优惠券转商城券规则表")
public class AtomReqCouponExchangeMallRuleDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "优惠券编号")
    private String mallCouponNo;

    @ApiModelProperty(value = "优惠券名称")
    private String mallCouponName;

    @ApiModelProperty(value = "优惠券描述")
    private String mallCouponDescribe;

    @ApiModelProperty(value = "优惠券发放方式（1001-自动发放，1002-会员领取，1003-触发返券）")
    private String mallCouponProvideType;

    @ApiModelProperty(value = "优惠券类型 （1001:满减券 1002:折扣券 1003:现金券）")
    private String mallCouponType;

    @ApiModelProperty(value = "使用门槛阈值（满减时表示满多少元）")
    private BigDecimal mallDiscountThreshold;

    @ApiModelProperty(value = "折扣券单次使用百分比值")
    private Integer mallDiscountPercent;

    @ApiModelProperty(value = "券面额")
    private BigDecimal mallCouponValue;

    @ApiModelProperty(value = "券有效期（1001：长期有效 1002：指定日期 1003:自领取后天数有效）")
    private String mallCouponPeriodValidity;

    @ApiModelProperty(value = "券开始时间")
    private LocalDateTime mallCouponEffectiveTime;

    @ApiModelProperty(value = "券结束时间")
    private LocalDateTime mallCouponInvalidTime;

    @ApiModelProperty(value = "券使用范围(1001:POP 1002:自营 1003:归属平台公司)")
    private String mallCouponUseScope;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "是否可用:默认1，1：可用，其余不可用")
    private Integer disableFlag;

    @ApiModelProperty(value = "活动编码list")
    private List<String> promotionNoList;


}
