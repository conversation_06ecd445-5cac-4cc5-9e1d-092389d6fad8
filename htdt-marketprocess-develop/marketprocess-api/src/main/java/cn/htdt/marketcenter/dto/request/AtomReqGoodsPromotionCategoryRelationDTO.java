package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 促销活动类目关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
public class AtomReqGoodsPromotionCategoryRelationDTO  extends ReqComPageDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     *折扣率
     */
    private String discountRate;
    /**
     * 活动编码
     */
    private String promotionNo;
    /**
     * 类目编码
     */
    private String categoryNo;
    /**
     * 类目名称
     */
    private String categoryName;
    /**
     * 是否可用:默认1，1：可用，2不可用
     */
    private Integer disableFlag;
    /**
     * 活动编码集合
     */
    private List<String> promotionNoList;
}