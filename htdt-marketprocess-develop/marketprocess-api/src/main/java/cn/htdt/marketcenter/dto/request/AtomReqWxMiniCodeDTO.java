package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 微信小程序二维码生成记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-27
 */
@Data
public class AtomReqWxMiniCodeDTO extends ReqComPageDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 二维码编号
     */
    private String codeNo;

    /**
     * 小程序编码
     */
    private String appId;

    /**
     * 页面
     */
    private String page;

    /**
     * 场景
     */
    private String shareScene;

    /**
     * 参数，如：?param1=value1&param2=value2
     */
    private String scene;

    /**
     * 点击次数
     */
    private Integer clickAmount;

    /**
     * 二维码图片地址
     */
    private String imgUrl;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

}
