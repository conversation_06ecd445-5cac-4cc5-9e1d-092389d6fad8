package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqBaseDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <p>
 * 活动规则
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-39
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "GoodsPromotionRuleDTO对象", description = "商品促销规则")
public class AtomReqGoodsPromotionRuleDTO extends ReqBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

//     1001 一口价  1002 折扣率 旧数据一律一口价计算
    private String goodsPromotionUserType;


    /**
     * 促销类型 2001:秒杀 2002:拼团 2003:预售 2004:单品促销 2005:满额促销 2006:满量促销 2007:满额赠 2008:套餐  2009:限时购
     */
    private String promotionType;

    /**
     * 活动编号
     */
    private String promotionNo;

    /**
     * 商品活动类型，1001：单品团 1002：抽奖团 1003:满额减 1004:满额折 1005:满金额赠送
     */
    private String goodsPromotionType;

    /**
     * 商品活动范围(商品活动范围(1001:店铺全场通用 1002:店铺指定类目 1003:店铺指定类目)
     */
    private String goodsPromotionScope;
    /**
     * 未中奖是否自动退款（1:否 2：是）
     */
    private Integer refundFlag;
    /**
     * 是否可叠加使用优惠券
     */
    private Integer couponUseFlag;
    /**
     * 团单时长
     */
    private Integer groupOrderDuration;

    /**
     * 是否限制用户参与活动（1:否 2：是）
     */
    private Integer userLimitFlag;

    /**
     * 用户限制参与活动次数
     */
    private Integer userLimitNum;

    /**
     * 自动成团时长
     */
    private Integer autoGroupDuration;

    /**
     * 预售类型(1001:有定金)
     */
    private String preSellType;

    /**
     * 定金支付开始时间
     */
    private LocalDate depositStartDate;

    /**
     * 定金支付结束时间
     */
    private LocalDate depositEndDate;

    /**
     * 尾款支付开始时间
     */
    private LocalDate balanceDueStartDate;

    /**
     * 尾款支付结束时间
     */
    private LocalDate balanceDueEndDate;

    /**
     * 满额阈值（满减满折时表示满多少元）
     */
    private BigDecimal fulfilQuotaMoney;

    /**
     * 优惠折扣(满减时是金额，满折时是折扣)
     */
    private BigDecimal discountMoney;

    /**
     * 赠送节点(1001:下单时赠送)
     */
    private String presentNode;

    /**
     * 是否全部商品（1:否 2:是）
     */
    private Integer allGoodsFlag;

    /**
     * 活动渠道(1001:汇享购下单)
     */
    private String promotionChannel;

    /**
     * 是否可以与优惠券使用
     */
    private Integer userCouponFlag;

    /**
     * 是否包邮(1:否 2:是)
     */
    private Integer freeDeliveryFlag;

    /**
     * 配送方式是否以商品自身配置为主(1:否 2:是)
     */
    private Integer deliveryFlag;

    /**
     * 配送方式(1000:自提 1100：配送;1000,1100:自提+配送)
     */
    private String deliveryWay;

    /**
     * 运费模板编号
     */
    private String freightTemplateNo;

    /**
     * 支付方式(1000:网店支付 1001:到店支付;1000,1001:到店支付+网店支付)
     */
    private String paymentMethod;

    /**
     * 尾款支付方式(1000:网店支付)
     */
    private String balanceDuePaymentMethod;

    /**
     * 单店总限购数量
     */
    private Integer storeTotalNum;

    /**
     * 单店每日限购数量
     */
    private Integer storeDailyNum;

    /**
     * 是否支持预约（1:否 2:是）
     */
    private Integer bookFlag;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;


    //活动表字段
    /**
     * 活动名称
     */
    private String promotionName;

    /**
     * 图片类型(1:默认图片 2:自定义图片)
     */
    private Integer promotionPictureType;

    /**
     * 图片url
     */
    private String promotionPictureUrl;

    /**
     * 图片活动名称
     */
    private String promotionActityName;

    /**
     * 活动对象 1001: 不限制 1002: 限制老用户 1003: 限制新用户
     */
    private String userScope;

    /**
     * 活动对象 1001: 不限制 1002: 限制老用户 1003: 限制新用户
     */
    private String joinUserScope;

    /**
     * 活动说明
     */
    private String promotionExplain;

    /**
     * 活动时间段类型 (1001:全天 1002:指定时间段)
     */
    private String effectivePeriodType;

    /**
     * 活动开始时间
     */
    private LocalDateTime effectiveTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime invalidTime;

    /**
     * 每日开始时间
     */
    private LocalTime dailyStartTime;

    /**
     * 每日结束时间
     */
    private LocalTime dailyEndTime;

    /**
     * 来源 1001：平台 1002：商家 1003：店铺
     */
    private String sourceType;

    /**
     * 活动类型 1000:抽奖  2000:商品促销 3000:优惠券
     */
    private String activityType;

    /**
     * 活动状态 1001:活动未开始 1002:活动进行中，1003:活动已结束
     */
    private String status;

    /**
     * 活动有效期（1001：长期有效 1002：指定日期）
     */
    private String periodValidity;

    /**
     * 上下架状态（1：未上架 2：上架）
     */
    private Integer upDownFlag;

    /**
     * 每人限购总数量
     */
    private Integer limitBuyTotalNum;

    /**
     * 礼品卡类型:1001实体卡;1002电子卡
     */
    private String cardType;

}
