package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-01-12
 * @Description 任务和营销清单请求DTO
 **/
@Data
public class AtomReqAgentMarketBillDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 清单表查询主键
     */
    private String marketBillNo;

    /**
     * 代理人编号
     */
    private String agentNo;

    /**
     * 店铺编号
     */
    private String storeNo;

    /**
     * 任务活动唯一编号或者是商品编号
     */
    private String taskOrGoodsNo;

    /**
     * 营销活动类型，对应枚举MarketTypeEnum
     */
    private String marketType;

    /**
     * 任务名称|商品名称
     */
    private String taskOrGoodsName;

    /**
     * 任务图片地址
     */
    private String taskImageUrl;

    /**
     * 清单表查询主键
     */
    List<String> marketBillNoList;

    /**
     * 店铺编号
     */
    List<String> storeNoList;

}
