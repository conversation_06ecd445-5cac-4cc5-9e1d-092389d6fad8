package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * 报名记录列表入参
 * <AUTHOR>
 * @date 2021/4/1 16:16
 */
@Data
public class AtomReqEnrollRecordDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 所属店铺
     */
    private String storeNo;

    /**
     * 活动编号
     */
    private String promotionNo;
    /**
     * 店铺名称
     */
    private String storeName;
    /**
     * 商家编码
     */
    private String merchantNo;
    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 活动店铺类型(1001:全部店铺 1002:报名活动)
     */
    private String storeType;
    /**
     * 受理状态（1000：不用受理记录 1001：待报名 1002：待受理 1003：已受理 1004：不受理）
     */
    private String applyStatus;

    /**
     * 活动交付状态(1: 已完成)
     */
    private String taskStatus;
    /**
     * 非店铺自己报名标识 2:是
     */
    private Integer applyNotFlag;
}
