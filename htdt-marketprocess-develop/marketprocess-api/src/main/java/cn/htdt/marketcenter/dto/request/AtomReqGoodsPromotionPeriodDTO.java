package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqBaseDTO;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalTime;
import java.util.List;

/**
 * 商品促销时间段请求实体类
 *
 * <AUTHOR>
 * @date 2021-06-28
 */
@Data
public class AtomReqGoodsPromotionPeriodDTO extends ReqBaseDTO implements Serializable {

    private static final long serialVersionUID = 2546381654797101836L;

    /**
     * 时间段编号
     */
    private String periodNo;

    /**
     * 活动编号
     */
    private String promotionNo;

    /**
     * 每场开始时间
     */
    private LocalTime startTime;

    /**
     * 每场结束时间
     */
    private LocalTime endTime;

    /**
     * 周期模式:1001每天重复,1002每周重复,1003每月重复
     */
    private String repectType;

    /**
     * 周期值:W1-W7周日-周六;M1-M31每月1-31日
     */
    private String repectVal;

    /**
     * 商品范围(1001:自定义配置商品 1002:复用其他时间段商品)，默认是1001
     */
    private String goodsScope;

    /**
     * 商品复用时段编号
     */
    private String copyGoodsPeriodNo;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 活动编号集合
     */
    private List<String> promotionNoList;

    /**
     * 活动场次编码集合
     */
    List<String> periodNoList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
