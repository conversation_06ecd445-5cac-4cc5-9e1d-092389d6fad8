package cn.htdt.marketcenter.dto.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/1/26 18:57
 */
@Data
public class AtomReqStoreAgentRewardSumDTO implements Serializable {
    /**
     * 序列化
     */
    private static final long serialVersionUID = 1L;

    /**
     * 酬劳状态   1 --- 预估  2 --- 已获得
     */
    private Integer rewardStatus;
    /**
     * 店铺编码集合
     */
    private List<String> storeNoList;

    /**
     * 代理人编码集合
     */
    private List<String> agentNoList;
}
