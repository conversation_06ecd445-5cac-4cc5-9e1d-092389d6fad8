package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 橙豆充值流水
 *
 * <AUTHOR>
 * @since 2022-01-25
 */
@Data
public class AtomReqVirtualCoinOrderFlowDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 交易流水号
     */
    private String outTradeNo;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 应付金额
     */
    private BigDecimal paymentAmount;

    /**
     * 实付金额
     */
    private BigDecimal realAmount;

    /**
     * 支付状态 OrderPaymentStatusEnum
     */
    private String paymentStatus;

    /**
     * 支付渠道 字典PAYMENT_CHANNEL
     */
    private String paymentChannel;

    /**
     * 支付渠道详情 字典PAYMENT_CHANNEL_DETAILS
     */
    private String paymentChannelDetails;

    /**
     * 支付方式,字典PAYMENT_METHOD
     */
    private String paymentMethod;

    /**
     * 付款时间
     */
    private LocalDateTime payTime;

    /**
     * 付款截止时间
     */
    private LocalDateTime endPayTime;

    /**
     * 付款成功回调时间
     */
    private LocalDateTime successTime;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称，对应orgName
     */
    private String storeName;

    /**
     * 是否可用:默认2，1：不可用2:可用
     */
    private Integer disableFlag;

    private String createNo;

    private String createName;

    private String modifyNo;

    private String modifyName;

    /**
     * 订单流水类型：1001-橙豆;1002-会员续费'
     */
    private String orderFlowType;
}
