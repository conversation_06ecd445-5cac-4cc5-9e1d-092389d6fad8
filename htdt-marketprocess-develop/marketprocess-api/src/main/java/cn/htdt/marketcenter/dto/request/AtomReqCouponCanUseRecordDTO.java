package cn.htdt.marketcenter.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 可用优惠券数据
 *
 * <AUTHOR>
 */
@Data
public class AtomReqCouponCanUseRecordDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = 1L;

    /**
     * 用户券编号
     */
    private String userCouponNo;

    /**
     * 优惠券编号
     */
    private String couponNo;

    /**
     * 用户编号
     */
    private String fanNo;

    /**
     * 代理人编号
     */
    private String agentNo;

    @ApiModelProperty(value = "手机号")
    private String phone;


    /**
     * 是否使用(1:否 2:是)
     */
    private Integer useFlag;


    /**
     * 门店编号，对应orgId
     */
    private String storeNo;


    /**
     * 商品编码集合
     */
    private List<String> goodsNoList;

    /**
     * 券使用渠道 (1001:汇享购下单 1002:门店下单)
     */
    private String couponUseChannel;

    @ApiModelProperty(value = "商详页查询优惠券标识")
    private Integer goodsDetailQuery;

    /**
     * 优惠券类型 （1001:满减券 1002:折扣券 1003:现金券 1004:代金券）
     */
    private String couponType;
}
