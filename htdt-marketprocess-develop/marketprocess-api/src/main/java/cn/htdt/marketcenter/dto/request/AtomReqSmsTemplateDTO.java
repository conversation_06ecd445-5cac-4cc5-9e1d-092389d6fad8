package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqBaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 短信模板 请求DTO
 *
 * <AUTHOR>
 * @date 2021-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AtomReqSmsTemplateDTO extends ReqBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 模板编号
     */
    private String templateNo;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板内容
     */
    private String templateContent;

    /**
     * 店铺编号
     */
    private String storeNo;

    /**
     * 数据来源类型 1001:平台自建   1002:商家自建  1003:店铺自建
     */
    private String sourceType;

    /**
     * 父级模板编号
     */
    private String parentTemplateNo;
}
