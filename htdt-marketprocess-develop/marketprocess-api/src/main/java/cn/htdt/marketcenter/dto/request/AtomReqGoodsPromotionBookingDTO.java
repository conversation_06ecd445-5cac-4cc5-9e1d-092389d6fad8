package cn.htdt.marketcenter.dto.request;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 商品活动预约表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "AtomReqGoodsPromotionBookingDTO对象", description = "商品活动预约")
public class AtomReqGoodsPromotionBookingDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 预约编码
     */
    private String bookingNo;
    /**
     * 活动编码
     */
    private String promotionNo;
    /**
     * 促销类型 2001:秒杀 2002:拼团 2003:预售 2004:单品促销 2005:满额促销 2006:满量促销 2007:满额赠 2008:套餐 2009:限时购
     */
    private String promotionType;
    /**
     * 粉丝编码
     */
    private String fanNo;

    /**
     * 商品编码
     */
    private String goodsNo;
    /**
     * 商品名称
     */
    private String goodsName;

    /*
     * 活动预约时间
     * */
    private LocalDate promotionDate;
    /**
     * 商品预约时间编码
     */
    private String periodNo;
    /**
     * 预约状态 1001:已预约 1002：未预约 1003：已结束
     */
    private String status;
    /**
     * 活动价格（快照展示）
     */
    private BigDecimal promotionPrice;
    /**
     * 最小活动价格（快照展示）
     */
    private BigDecimal minPromotionPrice;
    /**
     * 最大活动价格（快照展示）
     */
    private BigDecimal maxPromotionPrice;
    /**
     * 商家编码
     */
    private String merchantNo;
    /**
     * 商家名称
     */
    private String merchantName;
    /**
     * 店铺编码
     */
    private String storeNo;
    /**
     * 店铺名称
     */
    private String storeName;
    /**
     * 是否可用:默认1，1：可用，其余不可用
     */
    private Integer disableFlag;

    /**
     * 预约状态集合
     */
    private List<String> statusList;
}