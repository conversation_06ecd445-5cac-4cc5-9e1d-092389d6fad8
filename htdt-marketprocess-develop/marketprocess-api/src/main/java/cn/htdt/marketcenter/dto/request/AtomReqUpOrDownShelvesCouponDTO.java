package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqBaseDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * 上下架
 *
 * <AUTHOR>
 */
@Data
public class AtomReqUpOrDownShelvesCouponDTO extends ReqBaseDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = 6106445696403597364L;

    /**
     * 优惠券编号
     */
    private String couponNo;

    /**
     * 上下架状态（1：未上架 2：上架）
     */
    private Integer upDownFlag;

    /**
     * 店铺编号
     */
    private String storeNo;

    /**
     * 商家编号
     */
    private String merchantNo;
}
