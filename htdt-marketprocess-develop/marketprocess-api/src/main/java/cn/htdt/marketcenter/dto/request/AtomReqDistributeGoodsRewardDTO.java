package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-01-25
 * @Description 分销商品酬劳请求dto
 **/
@Data
public class AtomReqDistributeGoodsRewardDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 542390668207443678L;

    /**
     * 代理人姓名
     */
    private String agentName;

    /**
     * 代理人手机号
     */
    private String agentMobile;

    /**
     * 是否代理人查询 1：否 2：是
     */
    private Integer agentQueryFlag;

    /**
     * 营销活动类型，对应枚举MarketTypeEnum
     */
    private String marketType;

    /**
     * 代理人酬劳状态集合
     */
    private List<Integer> rewardStatusFlagList;

    /**
     * 分销店佣金状态集合
     */
    private List<Integer> distributionRewardStatusFlagList;
    /**
     * 任务和商品集合
     */
    private List<String> taskOrGoodsNoList;

    /**
     * 父商品集合
     */
    private List<String> parentGoodsNoList;

    /**
     * 系列商品父ID,非系列商品就是goods_no，方便查询
     */
    private String parentGoodsNo;

    /**
     * 任务和商品编码
     */
    private String taskOrGoodsNo;

    /**
     * 代理人编号集合
     */
    private List<String> agentNoList;
}