package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 膨胀红包发起记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Data
public class AtomReqExpandRecordDTO extends ReqComPageDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 膨胀编号
     */
    private String expandNo;

    /**
     * 活动编号
     */
    private String promotionNo;

    /**
     * 粉丝编号
     */
    private String fanNo;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 手机号
     */
    private String dsPhone;

    /**
     * 使用门槛阈值（满减时表示满多少元）
     */
    private BigDecimal discountThreshold;

    /**
     * 膨胀金额
     */
    private BigDecimal expandMoney;

    /**
     * 兑换门槛阈值（满多少元可兑换）
     */
    private BigDecimal exchangeThreshold;

    /**
     * 兑换状态(1:未兑换 2:已兑换)
     */
    private Integer exchangeStatus;

    /**
     * 券使用状态(1:未使用 2:已使用)
     */
    private Integer couponUseStatus;

    /**
     * 用户券编号
     */
    private String userCouponNo;

    /**
     * 是否可用:默认1，1：可用，其余不可用
     */
    private Integer disableFlag;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 活动更新时间-开始时间
     */
    private LocalDateTime startTime;
    /**
     * 活动更新时间-结束时间
     */
    private LocalDateTime endTime;


    /**
     * 排序类型 1001：创建时间
     */
    private String sortType;

    /**
     * 分享人头像
     */
    private String nickName;

    /**
     * 分享人昵称
     */
    private String headImg;

    /**
     * 兑换时间
     */
    private LocalDateTime exchangeTime;
}
