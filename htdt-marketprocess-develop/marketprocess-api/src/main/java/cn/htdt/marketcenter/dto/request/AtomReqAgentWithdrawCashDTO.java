package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;

import java.io.Serializable;

@Data
public class AtomReqAgentWithdrawCashDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 提现编号
     */
    private String cashNo;

    /**
     * 代理人编号
     */
    private String agentNo;

    /**
     * 提现请求数据
     */
    private String cashRequest;

    /**
     * 提现返回数据
     */
    private String cashResult;

    /**
     * 提现状态
     */
    private Integer status;

}
