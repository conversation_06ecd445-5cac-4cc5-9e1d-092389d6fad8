package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * <p>
 * 膨胀红包规则
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 *
 * 202503
 */
@Data
public class AtomReqExpandRuleDTO extends ReqComPageDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "门店编号集合")
    private List<String> storeNoList;

    /***膨胀红包规则**/
    /**
     * 活动编号
     */
    private String promotionNo;

    /**
     * 粉丝初始可领取金额
     */
    private BigDecimal firstMoney;

    /**
     * 邀请好友助力次数上限
     */
    private Integer inviteHelpTimes;

    /**
     * 最高可得红包金额
     */
    private BigDecimal maxMoney;

    /**
     * 优惠券类型 （1001:满减券 1002:折扣券 1003:现金券）
     */
    private String couponType;

    /**
     * 每个红包同一个人助力次数
     */
    private Integer userHelpLimitNum;

    /**
     * 使用门槛阈值（满减时表示满多少元）
     */
    private BigDecimal discountThreshold;

    /**
     * 兑换门槛阈值（满多少元可兑换）
     */
    private BigDecimal exchangeThreshold;

    /**
     * 券面额
     */
    private BigDecimal couponValue;

    /**
     * 券标识
     */
    private String couponIdentity;

    /**
     * 券有效期（1001：长期有效 1002：指定日期 1003:自领取后天数有效）
     */
    private String couponPeriodValidity;

    /**
     * 券有效天数（只有有效期为1003时使用）
     */
    private Integer couponEffectiveDay;

    /**
     * 券开始时间
     */
    private LocalDateTime couponEffectiveTime;

    /**
     * 券结束时间
     */
    private LocalDateTime couponInvalidTime;

    /**
     * 券使用渠道 (1001:汇享购下单 1002:门店下单)
     */
    private String couponUseChannel;

    /**
     * 券使用范围(1001:店铺全场通用 1002:云池商品通用 1003:指定云池商品 1004:店铺指定类目 1005:店铺指定品牌 1006:店铺指定商品 )
     */
    private String couponUseScope;

    /**
     * 券使用说明
     */
    private String couponUseExplain;

    /**
     * 用户每日可发起次数
     */
    private Integer userDailyTimes;

    /**
     * 用户累计可发起次数
     */
    private Integer userTotalTimes;

    /**
     * 总部每日可发起次数
     */
    private Integer dailyTimes;

    /**
     * 总部累计可发起次数
     */
    private Integer totalTimes;

    /**
     * 店铺每日可发起次数
     */
    private Integer storeDailyTimes;

    /**
     * 店铺累计可发起次数
     */
    private Integer storeTotalTimes;

    /**
     * 是否可用:默认1，1：可用，其余不可用
     */
    private Integer disableFlag;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;



    /***活动基本信息**/
    /**
     * 活动名称
     */
    private String promotionName;
    /**
     * 活动短链接
     */
    private String shortLink;
    /**
     * 图片类型(1:默认图片 2:自定义图片)
     */
    private Integer promotionPictureType;
    /**
     * 图片url
     */
    private String promotionPictureUrl;
    /**
     * 活动对象 1001: 不限制 1002: 限制老用户 1003: 限制新用户
     */
    private String userScope;
    /**
     * 活动对象 1001: 不限制 1002: 限制老用户 1003: 限制新用户
     */
    private String joinUserScope;
    /**
     * 活动说明
     */
    private String promotionExplain;
    /**
     * 活动时间段类型 (1001:全天 1002:指定时间段)
     */
    private String effectivePeriodType;
    /**
     * 活动开始时间
     */
    private LocalDateTime effectiveTime;
    /**
     * 活动结束时间
     */
    private LocalDateTime invalidTime;
    /**
     * 每日开始时间
     */
    private LocalTime dailyStartTime;
    /**
     * 每日结束时间
     */
    private LocalTime dailyEndTime;
    /**
     * 来源 1001：平台 1002：商家 1003：店铺
     */
    private String sourceType;
    /**
     * 活动类型 1000:抽奖  2000:商品促销 3000:优惠券
     */
    private String activityType;
    /**
     * 促销类型 1001:大转盘 1002:套圈圈 1003:打地鼠 1004:扭蛋机 1005:刮刮乐 1006:膨胀红包 2001:秒杀 2002:拼团 2003:预售 2004:单品促销 2005:满额促销 2006:满量促销 2007:满额赠 2008:套餐 3001:平台-手动发代理人券 3002::平台-手动发粉丝券 3003:平台-网店代理人领券 3004:平台-网店粉丝领券 3005:店铺-手动发粉丝券 3006:店铺-网店粉丝领券 3007:膨胀红包 3100:抽奖券
     */
    private String promotionType;
    /**
     * 活动状态 1001:活动未开始 1002:活动进行中，1003:活动已结束
     */
    private String status;
    /**
     * 活动有效期（1001：长期有效 1002：指定日期）
     */
    private String periodValidity;
    /**
     * 上下架状态（1：未上架 2：上架）
     */
    private Integer upDownFlag;
    /**
     * 店铺上下架状态（1：未上架 2：上架）
     */
    private Integer storeUpDownFlag;
    /**
     * 创建来源1000：pc 2000：app
     */
    private String createSource;


    /**其他**/
    /**
     * 活动更新时间-开始时间
     */
    private LocalDateTime startTime;
    /**
     * 活动更新时间-结束时间
     */
    private LocalDateTime endTime;

    /**
     * 活动结束时间判断用
     */
    private LocalDateTime expandEndTime;

    /**
     * 活动对象集合 1001: 不限制 1002: 仅限老用户 1003: 仅限新用户
     */
    private List<String> userScopeList;

    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件大小
     */
    private String fileSize;

    //20230818蛋品-盛守武-膨胀红包改动（start）
    /**
     * 是否给助力者发放优惠券
     */
    private Boolean helperCouponIssueFlag;

    /**
     * 助力者优惠门槛
     */
    private BigDecimal helperCouponThreshold;

    /**
     * 助力者优惠金额
     */
    private BigDecimal helperCouponValue;
    //20230818蛋品-盛守武-膨胀红包改动（end）


}
