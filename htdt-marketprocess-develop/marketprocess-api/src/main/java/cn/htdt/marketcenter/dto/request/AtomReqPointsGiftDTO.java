package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 积分商城礼品表
 *
 * <AUTHOR>
 * @since 2021-10-11
 */
@Data
public class AtomReqPointsGiftDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 礼品编号
     */
    private String giftNo;

    /**
     * 礼品名称
     */
    private String giftName;

    /**
     * 所需积分数（个）
     */
    private Integer points;

    /**
     * 礼品总库存（件）
     */
    private Integer giftStockNum;

    /**
     * 已兑换库存（件）
     */
    private Integer convertStockNum;

    /**
     * 礼品主图
     */
    private String giftPictureUrl;

    /**
     * 配送方式 （1001:自提）
     */
    private String deliveryType;

    /**
     * 兑换规则
     */
    private String convertRule;

    /**
     * 门店礼品上架状态（1:已上架 2:未上架）
     */
    private String giftStatus;

    /**
     * 最新上下架时间
     */
    private LocalDateTime latestUpDownTime;

    /**
     * 是否可用:默认1，1：可用，其余不可用
     */
    private Integer disableFlag;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 商家礼品状态（1:已上架 2:未上架）
     */
    private String merchantGiftStatus;

    /**
     * 共享店铺（1.否，2是）
     */
    private String sharingStore;

    /**
     * 礼品归属（1.门店，2.商家）
     */
    private String pointsGiftType;

    /**
     * 积分类型, 1-店铺, 2-商家
     */
    private String ruleType;

    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private String phone;
}
