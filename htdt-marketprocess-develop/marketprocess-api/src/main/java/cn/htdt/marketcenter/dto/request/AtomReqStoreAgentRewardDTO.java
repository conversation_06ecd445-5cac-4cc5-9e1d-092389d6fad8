package cn.htdt.marketcenter.dto.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 商家店铺代理人酬劳汇总dto
 */
@Data
public class AtomReqStoreAgentRewardDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = 1L;

    /**
     * 任务编码集合
     */
    private List<String> taskNoList;

    /**
     * 店铺编码集合 集合长度为1代表查询单店铺
     */
    private List<String> storeNoList;

}
