package cn.htdt.marketcenter.dto.request;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 报名活动dto
 * </p>
 *
 * @since 2021-04-01
 */
@Data
public class AtomReqEnrollRuleInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 活动编号
     */
    private String promotionNo;

    /**
     * 活动店铺类型(1001:需店铺报名参与 1002:指定店铺直接参与)
     */
    private String storeType;
    /**
     * 店铺报名范围（1001:全部店铺 1002：指定区域 1003：导入店铺）
     */
    private String applyType ;

    /**
     * 报名状态（1000：不用受理记录 1001：待报名
     * 1002：待受理 1003：已受理 1004：不受理）
     */
    private String applyStatus ;
    /**
     * APP图片类型(1:默认图片 2:自定义图片)
     */
    private Integer appPictureType;

    /**
     * APP图片url
     */
    private String appPictureUrl;
    /**
     * 报名区域范围
     */
    private String applyArea;
    /**
     * 报名区域范围名称
     */
    private String applyAreaName;
    /**
     * 报名协议编码
     */
    private String enrollAgreementCode;

    /**
     * 报名协议url
     */
    private String enrollAgreementUrl;
    /**
     * 图片类型(1:默认图片 2:自定义图片)
     */
    private Integer promotionPictureType;

    /**
     * 图片url
     */
    private String promotionPictureUrl;

    /**
     * 报名导入新增店铺列表
     */
    private List<AtomReqEnrollRecordInfoDTO> addList;
    /**
     * 报名导入删除店铺列表
     */
    private List<AtomReqEnrollRecordInfoDTO> delList;
    /**
     * 活动名称
     */
    private String promotionName;

    /**
     * 促销类型 1001:大转盘 1002:套圈圈 1003:打地鼠 1004:扭蛋机 1005:刮刮乐 1006:膨胀红包 2001:秒杀 2002:拼团 2003:预售 2004:单品促销 2005:满额促销 2006:满量促销 2007:满额赠 2008:套餐 3001:平台-手动发代理人券 3002::平台-手动发粉丝券 3003:平台-网店代理人领券 3004:平台-网店粉丝领券 3005:店铺-手动发粉丝券 3006:店铺-网店粉丝领券 3007:膨胀红包 3100:抽奖券 4001:报名活动
     */
    private String promotionType;

    /**
     * 活动类型 1000:抽奖  2000:商品促销 3000:优惠券 4000:报名活动
     */
    private String activityType;

    /**
     *  创建人no
     */
    private String createNo;
    /**
     * 创建人名称
     */
    private String createName;
    /**
     * 修改人no
     */
    private String modifyNo;
    /**
     * 修改人名称
     */
    private String modifyName;
    /**
     * 商家编号
     */
    private String merchantNo;
    /**
     * 商家名称
     */
    private String merchantName;
    /**
     * 门店编号，对应orgId
     */
    private String storeNo;
    /**
     * 店铺名称
     */
    private String storeName;
    /**
     * 报名说明
     */
    private String enrollExplain;
    /**
     * 2:是复制操作，状态为草稿
     */
    private int isCopy;

    /**
     * 活动开始时间
     */
    private LocalDateTime effectiveTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime invalidTime;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件大小
     */
    private String fileSize;
}
