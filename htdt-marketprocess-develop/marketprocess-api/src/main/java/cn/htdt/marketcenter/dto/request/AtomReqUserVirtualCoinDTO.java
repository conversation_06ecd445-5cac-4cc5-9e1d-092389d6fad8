package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 用户橙豆表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-20
 */
@Data
public class AtomReqUserVirtualCoinDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 粉丝编号
     */
    private String fanNo;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 手机号加密
     */
    private String mobileCipher;

    /**
     * 姓名
     */
    private String fansName;

    /**
     * 姓名加密
     */
    private String fansNameCipher;

    /**
     * 账户剩余橙豆
     */
    private BigDecimal accountRemainCoins;

    /**
     * 是否可用:默认1，1：可用，其余不可用
     */
    private Integer disableFlag;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 乐观锁版本号
     */
    private Integer virtualVersion;

    /**
     * ********蛋品-赵翔宇-商家橙豆-粉丝橙豆, 查询橙豆规则, 需要区分是店铺橙豆规则, 还是共享店铺橙豆规则
     * 橙豆规则类型, 1001-店铺规则, 1002-商家规则, 参考枚举: VirtualCoinRuleTypeEnum
     */
    private String ruleType;

}
