package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Date 2021-01-19
 * @Description 商家店铺代理人任务
 **/
@Data
public class AtomReqStoreAgentTaskDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户身份
     */
    private Integer loginIdentity;
    /**
     * 任务活动唯一编号
     */
    private String taskNo;


    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务开始时间
     */
    private LocalDate taskStartTime;

    /**
     * 任务结束时间
     */
    private LocalDate taskEndTime;

    /**
     * 平台任务状态，0：全部 1未开始 2进行中 3已过期
     */
    private Integer statusFlag;

    /**
     * 店铺任务上下架标识，1:上架 2:下架 3:未发布
     */
    private Integer status;
    /**
     * 商家编码
     */
    private String merchantNo;
    /**
     * 店铺编码
     */
    private String storeNo;
    /**
     * 平台任务状态： 1 未开始
     *  2 进行中
     *  3 已结束
     */
    private Integer taskStatus;

}
