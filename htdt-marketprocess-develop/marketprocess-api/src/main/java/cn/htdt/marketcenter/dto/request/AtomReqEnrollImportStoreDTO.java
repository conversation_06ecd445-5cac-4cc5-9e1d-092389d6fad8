package cn.htdt.marketcenter.dto.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 报名导入店铺记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-22
 */
@Data
public class AtomReqEnrollImportStoreDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 活动编号
     */
    private String promotionNo;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 门店编号，对应orgId
     */
    private List<String> storeNos;
}
