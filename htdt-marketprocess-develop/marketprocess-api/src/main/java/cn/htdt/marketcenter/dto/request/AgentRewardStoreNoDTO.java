package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * 店铺分销订单数金额
 **/
@Data
public class AgentRewardStoreNoDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 店铺编号
     */
    private String storeNo;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 1今日 2:昨日 3:近7日 4:近30日
     **/
    private Integer day;
}
