package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 橙豆订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-19
 */
@Data
public class AtomReqVirtualCoinOrderDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 橙豆订单编号
     */
    private String virtualOrderNo;

    /**
     * 橙豆规则编号
     */
    private String virtualRuleNo;

    /**
     * 粉丝编号
     */
    private String fanNo;

    /**
     * 支付方式,字典PaymentChannelEnum
     */
    private String paymentMethod;

    /**
     * 实际支付金额
     */
    private BigDecimal paymentAmount;

    /**
     * 橙豆金额
     */
    private BigDecimal availableAmount;

    /**
     * 支付状态,字典PAYMENTSTATUS
     */
    private String paymentStatus;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称，对应orgName
     */
    private String storeName;

    /**
     * 是否可用:默认2，1：不可用2:可用
     */
    private Integer disableFlag;

    private String createNo;

    private String createName;

    private String modifyNo;

    private String modifyName;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 20230928蛋品-赵翔宇-商家橙豆-粉丝橙豆, 查询橙豆规则, 需要区分是店铺橙豆规则, 还是共享店铺橙豆规则
     *
     * 橙豆规则类型, 1001-店铺规则, 1002-商家规则, 参考枚举: VirtualCoinRuleTypeEnum
     */
    private String ruleType;

}
