package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AtomReqUserPointsDTO extends ReqComPageDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    //粉丝编号
    private String fansNo;

    //手机号
    private String phone;

    //手机号-加密
    private String dsPhone;

    //姓名
    private String name;

    //累计获取积分
    private Integer accumulateGainPoints;

    //累计扣减积分
    private Integer accumulateReducePoints;

    //账户剩余积分
    private Integer accountRemainPoints;

    //是否可用:默认1，1：可用，其余不可用
    private Integer disableFlag;

    //商家编号
    private String merchantNo;

    //商家名称
    private String merchantName;

    //门店编号，对应orgId
    private String storeNo;

    //店铺名称
    private String storeName;

    //调整数量
    private Integer changeNum;

    //扣减的积分数量
    private Integer reducePoints;

    //调整原因
    private String adjustReason;

    //粉丝名字或手机号
    private String namePhone;

    /**
     * 粉丝编号集合
     */
    private List<String> fansNoList;

    /**
     * 共享店铺（1，否，2.是）
     */
    private String sharingStore;

    /**
     * 店铺编号集合
     */
    List<String> storeNoList;

    /**
     * 积分类型, 1-店铺, 2-商家
     */
    private String ruleType;

    /**
     * 当前登录用户身份 1-运营 2-商家 4-店铺 8-单店
     */
    private Integer loginIdentity;
}
