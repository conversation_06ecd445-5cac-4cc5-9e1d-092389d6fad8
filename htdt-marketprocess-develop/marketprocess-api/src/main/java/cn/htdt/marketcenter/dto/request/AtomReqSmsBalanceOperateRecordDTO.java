package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqBaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * 短信余额操作记录 请求DTO
 *
 * <AUTHOR>
 * @date 2021-06-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AtomReqSmsBalanceOperateRecordDTO extends ReqBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录编号
     */
    private String recordNo;

    /**
     * 操作类型(1000:增 2000:减)
     */
    private String operateType;

    /**
     * 变动数量
     */
    private Integer changeNum;

    /**
     * 变动前数量
     */
    private Integer beforeChangeNum;

    /**
     * 变动后数量
     */
    private Integer afterChangeNum;

    /**
     * 变动描述
     */
    private String changeDes;

    /**
     * 变动来源(1001:短信充值 1002:金币兑换 1003:套餐赠送 2001:短信群发)
     */
    private String changeSource;

    /**
     * 变动订单号(短信充值订单号、套餐订单号、金币兑换记录、短信群发记录)
     */
    private String changeOrderNo;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;

}
