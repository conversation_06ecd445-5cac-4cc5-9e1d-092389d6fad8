package cn.htdt.marketcenter.dto.request;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 抽奖活动表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="LotteryInfoDomain对象", description="抽奖活动表")
public class AtomReqLotteryInfoSaveDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 促销活动基本信息
     */
    private AtomReqPromotionInfoDTO atomPromotionInfoDTO;
    /**
     * 抽奖规则
     */
    private AtomLotteryRuleDTO atomLotteryRuleDTO;
    /**
     * 活动店铺规则表
     */
    private AtomPromotionStoreRuleDTO atomPromotionStoreRuleDTO;
    /**
     * 活动奖品信息
     */
    private AtomReqLotteryRewardInfoDTO atomReqLotteryRewardInfoDTO;


}
