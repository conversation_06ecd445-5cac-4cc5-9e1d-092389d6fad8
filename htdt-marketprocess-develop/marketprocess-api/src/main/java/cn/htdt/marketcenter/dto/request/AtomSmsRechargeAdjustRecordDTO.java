package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqBaseDTO;
import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 *  短信条数调整操作记录
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-19
 */
@Data
public class AtomSmsRechargeAdjustRecordDTO extends ReqBaseDTO implements Serializable {

    /**
     * 调整类型：1-增加；2-扣减
     */
    private Integer smsAdjustType;

    /**
     * 调整短信条数
     */
    private Long smsAdjustNum;

    /**
     * 短信剩余条数
     */
    private Long smsRemainingNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;
}
