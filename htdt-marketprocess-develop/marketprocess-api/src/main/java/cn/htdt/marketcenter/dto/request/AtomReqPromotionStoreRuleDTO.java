package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Program: marketcenter
 * @Description: 活动店铺规则请求数据
 * @Author： 刁阳
 * @CreateTime： 2021-04-19 13:59
 * @Version: 1.0
 */
@Data
public class AtomReqPromotionStoreRuleDTO extends ReqBaseDTO implements Serializable {

    private static final long serialVersionUID = -3967834910934142116L;

    @ApiModelProperty(value = "规则编号")
    private String ruleNo;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "活动店铺类型(1001:全部店铺 1002:报名活动)")
    private String storeType;

    @ApiModelProperty(value = "报名活动编号")
    private String enrollNo;

    @ApiModelProperty(value = "批量活动编号")
    private List<String> promotionNoList;

    @ApiModelProperty(value = "店铺编号")
    private String storeNo;

}
