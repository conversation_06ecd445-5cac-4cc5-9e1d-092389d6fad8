package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2021-01-12
 * @Description 代理人酬劳过程信息请求DTO
 **/
@Data
public class AtomReqAgentRewardRecordDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 查询主键
     */
    private String rewardRecordNo;

    /**
     * 代理人的编码
     */
    private String agentNo;

    /**
     * 代理人手机号
     */
    private String agentMobile;

    /**
     * 代理人手机号
     */
    private String dsAgentMobile;

    /**
     * 代理人姓名
     */
    private String agentName;

    /**
     * 粉丝编码
     */
    private String fanNo;

    /**
     * 粉丝手机号
     */
    private String fanMobile;

    /**
     * 粉丝手机号
     */
    private String dsFanMobile;

    /**
     * 粉丝姓名
     */
    private String fanName;

    /**
     * 营销活动类型，对应枚举MarketTypeEnum
     */
    private String marketType;

    /**
     * 任务活动唯一编号或者是商品编号
     */
    private String taskOrGoodsNo;

    /**
     * 任务名称|商品名称
     */
    private String taskOrGoodsName;

    /**
     * 任务|商品 图片地址
     */
    private String taskOrGoodsImageUrl;

    /**
     * 店铺编号
     */
    private String storeNo;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 父订单编号，用于涉及到订单的分销商品
     */
    private String parentOrderNo;

    /**
     * 订单编号，用于涉及到订单的分销商品
     */
    private String orderNo;

    /**
     * 订单状态，0:已支付 1:订单已经完成 2:订单失效
     */
    private Integer orderStatus;

    /**
     * 商品个数
     */
    private Integer goodsNum;

    /**
     * 商品的实付金额
     */
    private BigDecimal goodsPrice;

    /**
     * 每，总共酬劳上线， 0:未上限 1:已经达到上限 9:未达到要求
     */
    private Integer limitUpMZ;

    /**
     * 每，个人酬劳上线， 0:未上限 1:已经达到上限 9:未达到要求
     */
    private Integer limitUpMG;

    /**
     * 累计，总共酬劳上线，0:未上限 1:已经达到上限 9:未达到要求
     */
    private Integer limitUpLZ;

    /**
     * 累计，个人酬劳上线，0:未上限 1:已经达到上限 9:未达到要求
     */
    private Integer limitUpLG;

    /**
     * 任务上下架状态 ，1:未发布 2:上架 3:下架 4:删除
     */
    private Integer taskStatus;

    /**
     * 任务有效期，1:过期 2:有效期内
     */
    private Integer taskDateStatus;

    /**
     * 1:代理人酬劳
     */
    private Integer setType;

    /**
     * 商品规格属性
     */
    private String goodsAttribute;

    /**
     * 粉丝操作状态，对应枚举FansOperateStatusEnum
     */
    private Integer fansOperateStatus;

    /**
     * 粉丝操作时间
     */
    private LocalDateTime fansOperateDate;

    /**
     * 查询主键
     */
    private String agentAwardNo;

}
