package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * <p>
 * 促销活动
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "PromotionInfoDomain对象", description = "促销活动")
public class AtomReqPromotionInfoDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;





    @ApiModelProperty(value = "1001 一口价  1002 折扣率 旧数据一律一口价计算")
    private String goodsPromotionUserType;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "活动名称")
    private String promotionName;

    @ApiModelProperty(value = "图片类型(1:默认图片 2:自定义图片)")
    private Integer promotionPictureType;

    @ApiModelProperty(value = "图片url")
    private String promotionPictureUrl;

    @ApiModelProperty(value = "图片活动名称")
    private String promotionActityName;

    @ApiModelProperty(value = "活动对象 1001: 不限制 1002: 仅限老用户 1003: 仅限新用户")
    private String userScope;

    @ApiModelProperty(value = "活动对象集合 1001: 不限制 1002: 仅限老用户 1003: 仅限新用户")
    private List<String> userScopeList;

    @ApiModelProperty(value = "活动对象 1001: 不限制 1002: 仅限老用户 1003: 仅限新用户")
    private String joinUserScope;

    @ApiModelProperty(value = "活动说明")
    private String promotionExplain;

    @ApiModelProperty(value = "活动说明Json")
    private String promotionExplainJson;

    @ApiModelProperty(value = "活动时间段类型 (1001:全天 1002:指定时间段)")
    private String effectivePeriodType;

    @ApiModelProperty(value = "活动开始时间")
    private LocalDateTime effectiveTime;

    @ApiModelProperty(value = "活动结束时间")
    private LocalDateTime invalidTime;

    @ApiModelProperty(value = "每日开始时间")
    private LocalTime dailyStartTime;

    @ApiModelProperty(value = "每日结束时间")
    private LocalTime dailyEndTime;

    @ApiModelProperty(value = "来源 1001：平台 1002：商家 1003：店铺")
    private String sourceType;

    /**
     * todo xh 2023-06-08 目标二：营销管理 汇通数科体系不取平台营销活动
     */
    @ApiModelProperty(value = "来源 1001：平台 1002：商家 1003：店铺")
    private List<String> listSourceType;

    @ApiModelProperty(value = "活动类型 1000:抽奖  2000:商品促销 3000:优惠券")
    private String activityType;

    @ApiModelProperty(value = "促销类型 1001:大转盘 1002:套圈圈 1003:打地鼠 1004:扭蛋机 1005:刮刮乐 1006:膨胀红包 2001:秒杀 2002:拼团 2003:预售 2004:单品促销 2005:满额促销 2006:满量促销 2007:满额赠 2008:套餐 2009:限时购 3001:平台-手动发代理人券 3002::平台-手动发粉丝券 3003:平台-网店代理人领券 3004:平台-网店粉丝领券 3005:店铺-手动发粉丝券 3006:店铺-网店粉丝领券 3007:膨胀红包 3100:抽奖券 4001:报名活动")
    private String promotionType;

    @ApiModelProperty(value = "活动状态 1001:活动未开始 1002:活动进行中，1003:活动已结束")
    private String status;

    /**
     * 查询状态时是否精确匹配时间，1否2是，精确时匹配到时分秒
     */
    private Integer accurateTimeFlag;

    @ApiModelProperty(value = "活动有效期（1001：长期有效 1002：指定日期）")
    private String periodValidity;

    @ApiModelProperty(value = "上下架状态（1：未上架 2：上架）")
    private Integer upDownFlag;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "门店编号，对应orgId")
    private String storeNo;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "是否可用:默认1，1：可用，其余不可用")
    private Integer disableFlag;

    @ApiModelProperty(value = "短链接")
    private String shortLink;

    @ApiModelProperty(value = "抽奖类型 1001:直接抽奖 1002:订单抽奖")
    private String lotteryType;

    @ApiModelProperty(value = "创建来源 1000:PC  2000:APP")
    private String createSource;

    @ApiModelProperty(value = "活动类型集合")
    private List<String> promotionTypeList;

    @ApiModelProperty(value = "活动编码集合")
    private List<String> promotionNoList;

    @ApiModelProperty(value = "门店编号集合")
    private List<String> storeNoList;

    @ApiModelProperty(value = "创建人编号")
    private String createNo;

    @ApiModelProperty(value = "创建人名称")
    private String createName;

    @ApiModelProperty(value = "修改人编号")
    private String modifyNo;

    @ApiModelProperty(value = "修改人名称")
    private String modifyName;

    @ApiModelProperty(value = "店铺上下架状态（1：未上架 2：上架）")
    private Integer storeUpDownFlag;

    @ApiModelProperty(value = "更新活动状态之前的状态")
    private String oldStatus;

    @ApiModelProperty(value = "最新上下架时间")
    private LocalDateTime latestUpDownTime;
    /**
     * 积分配置
     */
    private Integer pointsConfig;

    @ApiModelProperty(value = "搜索来源 1001：秒杀首页 1002：活动列表页")
    private String searchType;

    @ApiModelProperty(notes = "统计开始时间")
    private LocalDate startTimeCount;

    @ApiModelProperty(notes = "统计结束时间")
    private LocalDate endTimeCount;

    @ApiModelProperty(value = "是否仅查询活动中的促销活动,传1时不管活动当前是否活动中")
    private Integer inTimeFlag;

    @ApiModelProperty(value = "礼品卡类型:1001实体卡;1002电子卡")
    private String cardType;

    /**
     * 忽略deleteflag,2是，其他否，查询被删除数据
     */
    private Integer ignoreDeleteFlag;

    /**
     * 活动通知推送开关1关闭2打开3已发送(1,2可编辑,3不可编辑)
     */
    private Integer sendMsgFlag;

    /**
     * 首次上架时间
     */
    private LocalDateTime firstUpTime;

    @ApiModelProperty(value = "搜索关键词，支持按商品名称、活动名称模糊查询，暂时用于HXG接龙活动列表")
    private String searchKey;

    @ApiModelProperty(value = "商家是否已上架活动 1否 2是")
    private Integer hasUpFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
