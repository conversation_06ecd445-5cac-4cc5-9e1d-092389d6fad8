package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * <p>
 * 优惠券配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-01
 * 202503 东启
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CouponSettingVo对象", description="优惠券配置表")
public class AtomReqCouponSettingDTO extends ReqComPageDTO {

    private static final long serialVersionUID = 1L;


    /**
     * 店铺编号
     */
    @ApiModelProperty(value = "店铺编号")
    private List<String> storeNoList;
    /***活动基本信息**/
    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "活动名称")
    private String promotionName;

    @ApiModelProperty(value = "活动短链接")
    private String shortLink;

    @ApiModelProperty(value = "图片类型(1:默认图片 2:自定义图片)")
    private Integer promotionPictureType;

    @ApiModelProperty(value = "图片url")
    private String promotionPictureUrl;

    @ApiModelProperty(value = "活动对象 1001: 不限制 1002: 仅限老用户 1003: 仅限新用户")
    private String userScope;

    @ApiModelProperty(value = "活动对象 1001: 不限制 1002: 仅限老用户 1003: 仅限新用户")
    private String joinUserScope;

    @ApiModelProperty(value = "活动说明")
    private String promotionExplain;

    @ApiModelProperty(value = "活动时间段类型 (1001:全天 1002:指定时间段)")
    private String effectivePeriodType;

    @ApiModelProperty(value = "活动开始时间")
    private LocalDateTime effectiveTime;

    @ApiModelProperty(value = "活动结束时间")
    private LocalDateTime invalidTime;

    @ApiModelProperty(value = "每日开始时间")
    private LocalTime dailyStartTime;

    @ApiModelProperty(value = "每日结束时间")
    private LocalTime dailyEndTime;

    @ApiModelProperty(value = "来源 1001：平台 1002：商家 1003：店铺")
    private String sourceType;

    /**
     * todo xh 2023-06-08 目标二：营销管理 代理人领券 汇通数科体系不取平台营销活动
     */
    @ApiModelProperty(value = "来源 1001：平台 1002：商家 1003：店铺")
    private List<String> listSourceType;

    @ApiModelProperty(value = "活动类型 1000:抽奖  2000:商品促销 3000:优惠券")
    private String activityType;

    @ApiModelProperty(value = "促销类型 1001:大转盘 1002:套圈圈 1003:打地鼠 1004:扭蛋机 1005:刮刮乐 1006:膨胀红包 2001:秒杀 2002:拼团 2003:预售 2004:单品促销 2005:满额促销 2006:满量促销 2007:满额赠 2008:套餐 3001:平台-手动发代理人券 3002::平台-手动发粉丝券 3003:平台-网店代理人领券 3004:平台-网店粉丝领券 3005:店铺-手动发粉丝券 3006:店铺-网店粉丝领券 3007:膨胀红包 3100:抽奖券")
    private String promotionType;

    @ApiModelProperty(value = "活动状态 1001:活动未开始 1002:活动进行中，1003:活动已结束")
    private String status;

    @ApiModelProperty(value = "活动有效期（1001：长期有效 1002：指定日期）")
    private String periodValidity;

    @ApiModelProperty(value = "上下架状态（1：未上架 2：上架）")
    private Integer upDownFlag;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "门店编号，对应orgId")
    private String storeNo;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "店铺上下架状态（1：未上架 2：上架）")
    private Integer storeUpDownFlag;



    /***优惠券规则**/

    @ApiModelProperty(value = "促销类型 3001:平台-手动发代理人券 3002::平台-手动发粉丝券 3003:平台-网店代理人领券 3004:平台-网店粉丝领券 3005:店铺-手动发粉丝券 3006:店铺-网店粉丝领券 3007:膨胀红包 3008:商家-手动发粉丝券 3009:商家-网店粉丝领券 3100:抽奖券 3200:代金券")
    private String promotionCouponType;

    @ApiModelProperty(value = "券编号")
    private String couponNo;

    @ApiModelProperty(value = "活动名称")
    private String couponName;

    @ApiModelProperty(value = "优惠券类型 （1001:满减券 1002:折扣券 1003:现金券）")
    private String couponType;

    @ApiModelProperty(value = "使用门槛阈值（满减时表示满多少元）")
    private BigDecimal discountThreshold;

    //20230928蛋品-吴鑫鑫-优惠券增加折扣-优惠券活动创建
    @ApiModelProperty(value = "券面额或者折扣")
    private BigDecimal couponValue;

    @ApiModelProperty(value = "券标识")
    private String couponIdentity;

    @ApiModelProperty(value = "券有效期（1001：长期有效 1002：指定日期 1003:自领取后天数有效）")
    private String couponPeriodValidity;

    @ApiModelProperty(value = "券有效天数（只有有效期为1003时使用）")
    private Integer couponEffectiveDay;

    @ApiModelProperty(value = "券开始时间")
    private LocalDateTime couponEffectiveTime;

    @ApiModelProperty(value = "券结束时间")
    private LocalDateTime couponInvalidTime;

    @ApiModelProperty(value = "券使用渠道 (1001:汇享购下单 1002:门店下单)")
    private String couponUseChannel;

    @ApiModelProperty(value = "券使用范围(1001:店铺全场通用 1002:云池商品通用 1003:指定云池商品 1004:店铺指定类目 1005:店铺指定品牌 1006:店铺指定商品 )")
    private String couponUseScope;

    @ApiModelProperty(value = "券使用说明")
    private String couponUseExplain;

    @ApiModelProperty(value = "是否支持代理人使用(1:否 2:是)")
    private Integer agentLimitFlag;

    @ApiModelProperty(value = "是否转换成商城券（1:否 2:是）")
    private Integer changePurchaseCouponFlag;

    @ApiModelProperty(value = "单个用户总共可获得张数")
    private Integer userTotalNum;

    @ApiModelProperty(value = "单个用户每日可获得张数")
    private Integer userDailyNum;

    @ApiModelProperty(value = "店铺用户每日可获得张数")
    private Integer storeDailyNum;

    @ApiModelProperty(value = "店铺用户总共可获得张数")
    private Integer storeTotalNum;

    @ApiModelProperty(value = "是否限制发行量（1:否 2:是）")
    private Integer circulationLimitFlag;

    @ApiModelProperty(value = "总发行量")
    private Integer totalNum;

    @ApiModelProperty(value = "剩余张数")
    private Integer remainNum;

    @ApiModelProperty(value = "是否可用:默认1，1：可用，其余不可用")
    private Integer disableFlag;



    /***优惠券转商城券规则**/
    @ApiModelProperty(value = "优惠券编号")
    private String mallCouponNo;

    @ApiModelProperty(value = "优惠券名称")
    private String mallCouponName;

    @ApiModelProperty(value = "优惠券描述")
    private String mallCouponDescribe;

    @ApiModelProperty(value = "优惠券发放方式（1001-自动发放，1002-会员领取，1003-触发返券）")
    private String mallCouponProvideType;

    @ApiModelProperty(value = "优惠券类型 （1001:满减券 1002:折扣券 1003:现金券）")
    private String mallCouponType;

    @ApiModelProperty(value = "使用门槛阈值（满减时表示满多少元）")
    private BigDecimal mallDiscountThreshold;

    @ApiModelProperty(value = "折扣券单次使用百分比值")
    private Integer mallDiscountPercent;

    @ApiModelProperty(value = "券面额")
    private BigDecimal mallCouponValue;

    @ApiModelProperty(value = "券有效期（1001：长期有效 1002：指定日期 1003:自领取后天数有效）")
    private String mallCouponPeriodValidity;

    @ApiModelProperty(value = "券开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime mallCouponEffectiveTime;

    @ApiModelProperty(value = "券结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime mallCouponInvalidTime;

    @ApiModelProperty(value = "券使用范围(1001:POP 1002:自营 1003:归属平台公司)")
    private String mallCouponUseScope;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;



    @ApiModelProperty(value = "活动更新时间-开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "活动更新时间-结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "优惠券活动参与商品")
    private List<AtomReqCouponGoodsRelationDTO> couponGoodsRelationDTOS;

    @ApiModelProperty(value = "活动编码集合")
    private List<String> promotionNoList;

    @ApiModelProperty(value = "券活动类型集合")
    private List<String> promotionCouponTypeList;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime modifyTime;

    @ApiModelProperty(value = "操作人")
    private String modifyName;

    @ApiModelProperty(value = "奖品编号")
    private String rewardNo;

    @ApiModelProperty(value = "创建来源 1000:PC  2000:APP")
    private String createSource;

    @ApiModelProperty(value = "活动对象集合 1001: 不限制 1002: 仅限老用户 1003: 仅限新用户")
    private List<String> userScopeList;

    @ApiModelProperty(value = "是否购物车查询，2-是，其他-否")
    private Integer carQueryFlag;

    /**
     * 售价
     */
    private BigDecimal salePrice;

    @ApiModelProperty(value = "忽略deleteflag,2是，其他否，查询被删除数据(HXG查询代金券详情)")
    private Integer ingoreDeleteFlag;

    @ApiModelProperty(value = "用户身份 1:运营 2:商家 4/8:店铺")
    private Integer loginIdentity;

    @ApiModelProperty(value = "商家是否已上架活动 1否 2是")
    private Integer hasUpFlag;

}