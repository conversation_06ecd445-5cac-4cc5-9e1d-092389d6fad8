package cn.htdt.marketcenter.dto.request;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021-01-18
 * @Description 通用酬劳设置记录表
 **/
@Data
public class AtomReqAgentMarketRewardsetRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务活动唯一编号或者是商品编号
     */
    private String taskOrGoodsNo;

    /**
     * 营销活动类型，对应枚举MarketTypeEnum
     */
    private String marketType;

    /**
     * 店铺编号，对应orgId
     * 店铺营销不为空
     */
    private String storeNo;

    /**
     * 商家编号
     * 店铺营销不为空
     */
    private String merchantNo;

    /**
     * 类型，1代理人酬劳 2额外酬劳 3新粉丝酬劳，对应枚举RewardSetTypeEnum
     */
    private Integer setType;

    /**
     * 酬劳类型，1佣金 2汇金币 3礼品 4专享现金券 5服务券 6话费券，对应枚举RewardTypeEnum
     */
    private Integer rewardType;

    /**
     * 修改前记录
     */
    private String modifyBeforeRecord;

    /**
     * 修改后记录
     */
    private String modifyAfterRecord;
}