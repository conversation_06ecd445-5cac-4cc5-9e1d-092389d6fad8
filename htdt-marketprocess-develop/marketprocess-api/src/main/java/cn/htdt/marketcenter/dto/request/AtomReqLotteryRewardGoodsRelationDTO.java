package cn.htdt.marketcenter.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 活动奖品商品关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="AtomReqLotteryRewardGoodsRelationDTO对象", description="活动奖品商品关联表")
public class AtomReqLotteryRewardGoodsRelationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "奖品编号")
    private String rewardNo;

    @ApiModelProperty(value = "奖品类型（1001:实物礼品 1002:优惠券 1003:话费券 1004:话费 1005:汇金币 1006:积分  9999:谢谢惠顾）")
    private String rewardType;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "是否可用:默认1，1：可用，其余不可用")
    private Integer disableFlag;

    /**
     * 批量用
     */
    private List<String> rewardNos;


}
