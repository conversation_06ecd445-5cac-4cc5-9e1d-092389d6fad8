package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AtomReqLotteryStoreDTO extends ReqComPageDTO {

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "抽奖类型 1001:直接抽奖 1002:订单抽奖")
    private String lotteryType;

    /**
     * 活动店铺规则表
     */
    private AtomReqLotteryStoreRuleDTO atomReqLotteryStoreRuleDTO;

}
