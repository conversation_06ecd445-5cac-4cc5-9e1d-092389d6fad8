package cn.htdt.marketcenter.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 活动奖品信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="LotteryRewardInfoDomain对象", description="活动奖品信息表")
public class AtomReqLotteryRewardInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "奖品编号")
    private String rewardNo;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "奖品类型（1001:实物礼品 1002:优惠券 1003:话费券 1004:话费 1005:汇金币 1006:积分  9999:谢谢惠顾）")
    private String rewardType;

    @ApiModelProperty(value = "奖品名称")
    private String rewardName;

    @ApiModelProperty(value = "奖品等级排序：1、2、3、4、5、6、7、8")
    private Integer rewardOrder;

    @ApiModelProperty(value = "券面额")
    private BigDecimal couponValue;

    @ApiModelProperty(value = "使用门槛阈值（满减时表示满多少元）")
    private BigDecimal discountThreshold;

    @ApiModelProperty(value = "券有效期（1001：长期有效 1002：指定日期 1003:自领取后天数有效）")
    private String couponPeriodValidity;

    @ApiModelProperty(value = "券编码")
    private String couponNo;

    @ApiModelProperty(value = "券标识")
    private String couponIdentity;

    @ApiModelProperty(value = "优惠券有效天数")
    private Integer couponEffectiveDay;

    @ApiModelProperty(value = "券开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime couponEffectiveTime;

    @ApiModelProperty(value = "券结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime couponInvalidTime;

    @ApiModelProperty(value = "券使用渠道 (1001:汇享购下单 1002:门店下单)")
    private String couponUseChannel;

    @ApiModelProperty(value = "券使用范围(1001:店铺全场通用 1002:云池商品通用 1003:指定云池商品)")
    private String couponUseScope;

    @ApiModelProperty(value = "配送方式 （1001:自提）")
    private String deliveryType;

    @ApiModelProperty(value = "奖品值 (话费、金币、积分)")
    private String rewardValue;

    @ApiModelProperty(value = "奖品总库存")
    private Integer totalCount;

    @ApiModelProperty(value = "每日可抽库存")
    private Integer dailyCount;

    @ApiModelProperty(value = "中奖概率")
    private BigDecimal probabilityValue;

    @ApiModelProperty(value = "是否可用:默认1，1：可用，其余不可用")
    private Integer disableFlag;

    @ApiModelProperty(value = "操作标识：1=其他；2=删除")
    private Integer operateFlag;

    /**
     * 活动奖品商品关联
     */
    private List<AtomReqLotteryRewardGoodsRelationDTO> relationDTOList;

}
