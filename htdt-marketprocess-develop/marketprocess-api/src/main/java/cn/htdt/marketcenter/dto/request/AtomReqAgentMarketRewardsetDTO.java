package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqBaseDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-01-13
 * @Description 通用酬劳设置表
 **/
@Data
public class AtomReqAgentMarketRewardsetDTO extends ReqBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务活动唯一编号或者是商品编号
     */
    private String taskOrGoodsNo;

    /**
     * 营销活动类型，对应枚举MarketTypeEnum
     */
    private String marketType;

    /**
     * 店铺编号，对应orgId
     * 店铺营销不为空
     */
    private String storeNo;

    /**
     * 商家编号
     * 店铺营销不为空
     */
    private String merchantNo;

    /**
     * 类型，1代理人酬劳 2额外酬劳 3新粉丝酬劳，对应枚举RewardSetTypeEnum
     */
    private Integer setType;

    /**
     * 额外酬劳序列
     */
    private Integer setIndex;

    /**
     * 代理人酬劳-每推荐的注册用户的个数
     */
    private Integer rewardCount;

    /**
     * 酬劳类型，1佣金 2汇金币 3礼品 4专享现金券 5服务券 6话费券，对应枚举RewardTypeEnum
     */
    private Integer rewardType;

    /**
     * 佣金元
     * 汇金币个
     */
    private BigDecimal yjOrHjb;

    /**
     * 礼品名称|服务券名称
     */
    private String relatedName;

    /**
     * 礼品名称|服务券|现金券|话费券有效时间类型 1长期有效 2自定义有效期 3自定义有效天数，对应枚举RewardRelatedDateTypeEnum
     */
    private Integer relatedDateType;

    /**
     * 礼品名称|服务券|现金券|话费券使用说明
     */
    private String relatedMemo;

    /**
     * 礼品名称|服务券|现金券|话费券有效期的天数
     */
    private Integer relatedEffDays;

    /**
     * 现金券|话费券，满多少
     */
    private BigDecimal relatedUp;

    /**
     * 现金券|话费券，减多少
     */
    private BigDecimal relatedLess;

    /**
     * 现金券使用范围，1全场通用
     */
    private Integer xjqRange;

    /**
     * 话费券开始时间
     */
    private LocalDate hfqStartTime;

    /**
     * 话费券结束时间
     */
    private LocalDate hfqEndTime;

    /**
     * 酬劳设置快照
     */
    private AtomReqAgentMarketRewardsetRecordDTO rewardSetRecordDTO;

    /**
     * 商品编号集合
     */
    private List<String> goodsNos;
}