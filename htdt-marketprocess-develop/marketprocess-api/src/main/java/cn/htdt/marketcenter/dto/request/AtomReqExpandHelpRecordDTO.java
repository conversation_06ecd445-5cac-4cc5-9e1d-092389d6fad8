package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <p>
 * 膨胀红包助力记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AtomReqExpandHelpRecordDTO extends ReqComPageDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 记录编号
     */
    private String helpNo;

    /**
     * 活动编号
     */
    private String promotionNo;

    /**
     * 膨胀编号
     */
    private String expandNo;

    /**
     * 粉丝编号
     */
    private String fanNo;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 是否发起者（1：否 2：是）
     */
    private Integer initiator;
    /**
     * 助力金额
     */
    private BigDecimal helpMoney;

    /**
     * 助力人头像
     */
    private String helpHeadImg;

    /**
     * 助力人昵称
     */
    private String helpNickName;

    /**
     * 助力人微信openid
     */
    private String helpOpenid;

    /**
     * 助力人微信openid
     */
    private String helpUnionid;

    /**
     * 是否可用:默认1，1：可用，其余不可用
     */
    private Integer disableFlag;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 用户券编号
     * // 20230818蛋品-盛守武-膨胀红包改动
     */
    private String userCouponNo;
}
