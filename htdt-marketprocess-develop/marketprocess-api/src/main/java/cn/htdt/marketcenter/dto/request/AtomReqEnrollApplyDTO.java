package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 报名活动 报名、受理入参
 * <AUTHOR>
 * @date 2022/3/24 09:53
 */
@Data
public class AtomReqEnrollApplyDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 所属店铺
     */
    private String storeNo;

    /**
     * 活动编号
     */
    private String promotionNo;
    /**
     * 店铺名称
     */
    private String storeName;
    /**
     * 商家编码
     */
    private String merchantNo;
    /**
     * 商家名称
     */
    private String merchantName;
    /**
     * 联系人姓名
     */
    private String name;

    /**
     * 联系电话
     */
    private String phone;
    /**
     * 报名状态（1000：不用受理记录 1001：待报名
     * 1002：待受理 1003：已受理 1004：不受理）
     */
    private String applyStatus;
    /**
     * 所属店铺编码数组
     */
    private List<String> storeNoList;
    /**
     * 报名记录编码集合
     */
    @ApiModelProperty(value = "报名记录编码集合")
    private List<String> recordNoList;
    /**
     * 受理备注
     */
    private String applyExplain;

    /**
     * 处理时间
     */
    private LocalDateTime applyTime;
}
