package cn.htdt.marketcenter.dto.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AtomReqFollowPointsDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 店铺编号
     */
    private String storeNo;
    /**
     * 手机列表
     */
    private List<String> phoneList;

    /**
     * 积分配置类型（1.门店，2.商家）
     */
    private String ruleType;
}
