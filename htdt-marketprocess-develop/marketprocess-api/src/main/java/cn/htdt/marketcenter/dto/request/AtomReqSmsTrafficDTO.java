package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqBaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 短信流量 请求DTO
 *
 * <AUTHOR>
 * @date 2021-06-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AtomReqSmsTrafficDTO extends ReqBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 短信剩余条数
     */
    private Long smsRemainingNum;

    /**
     * 发送成功条数
     */
    private Long sendSuccessNum;

    /**
     * 发送成功次数
     */
    private Long sendSuccessTimes;

    /**
     * 短信发送总次数
     */
    private Long smsSendingTimes;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;
}
