package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 推广申请处理表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-12
 */
@Data
public class AtomPromotionApplicationProcessingDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 推广申请编号
     */
    private String popularizeNo;

    /**
     * 推广类型
     */
    private String popularizeType;

    /**
     * 申请人
     */
    private String applicant;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 手机号
     */
    private String dsPhone;

    /**
     * 申请会员店编号
     */
    private String storeNo;

    /**
     * 申请会员店名称
     */
    private String storeName;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 申请备注
     */
    private String applyRemark;

    /**
     * 处理状态
     */
    private String handleStatus;

    /**
     * 处理结果备注
     */
    private String processResultRemark;

    /**
     * 处理时间
     */
    private LocalDateTime handleTime;


}
