package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户积分记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-26
 */
@Data
public class AtomReqUserPointsRecordDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录编号
     */
    private String recordNo;

    /**
     * 粉丝编号
     */
    private String fansNo;

    /**
     * 操作类型(1000:积分获得 2000:积分扣减)
     */
    private String operateType;

    /**
     * 变动数量
     */
    private Integer changeNum;

    /**
     * 变动前数量
     */
    private Integer beforeChangeNum;

    /**
     * 变动后数量
     */
    private Integer afterChangeNum;

    /**
     * 积分生效时间
     */
    private LocalDateTime effectTime;

    /**
     * 积分失效时间
     */
    private LocalDateTime invalidTime;

    /**
     * 积分变动描述
     */
    private String changeDes;

    /**
     * 积分变动事件(1001:粉丝下单得积分（门店），1002:粉丝下单得积分（网店），1003:关注店铺赠积分， 2001:积分兑换礼品，2002:大转盘抽奖（专属链接），2002:大转盘抽奖（网店抽奖活动专区）2003:摇奖机抽奖（专属链接），2004:摇奖机抽奖（网店抽奖活动专区），2005:小猫钓鱼抽奖（专属链接）2006:小猫钓鱼抽奖（网店抽奖活动专区）
     * 3001:积分调整)
     */
    private String pointChangeEvent;

    /**
     * 调整原因
     */
    private String adjustReason;

    /**
     * 订单/兑换/抽奖编号
     */
    private String orderConvertDrawNo;

    /**
     * 是否可用:默认1，1：可用，其余不可用
     */
    private Integer disableFlag;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 指定日期 （年月日）
     */
    private String assignDate;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 订单编号--仅限订单场景使用
     */
    private String orderNo;

    /**
     * 积分类型, 1-店铺, 2-商家
     */
    private String ruleType;
}
