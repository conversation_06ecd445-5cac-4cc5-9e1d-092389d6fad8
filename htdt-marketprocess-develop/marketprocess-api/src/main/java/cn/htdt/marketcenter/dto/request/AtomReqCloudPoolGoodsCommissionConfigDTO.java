package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqBaseDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 云池商品佣金配置表入参
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-21
 */
@Data
public class AtomReqCloudPoolGoodsCommissionConfigDTO extends ReqBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品编号，包含普通商品、套餐商品等所有商品
     */
    private String goodsNo;

    /**
     * 系列商品主品商品编号，非系列商品商品编号
     */
    private String parentGoodsNo;
    /**
     * 云池供货价
     */
    private BigDecimal cloudPoolSupplyPrice;
    /**
     * 云池售价
     */
    private BigDecimal retailPrice;

    /**
     * 云池市场价
     */
    private BigDecimal marketPrice;

    /**
     * 活动费用
     */
    private BigDecimal activityExpenses;

    /**
     * 代理人佣金比例（%）
     */
    private BigDecimal agentCommissionRatio;

    /**
     * 分销店佣金比例（%）
     */
    private BigDecimal distributionStoreCommissionRatio;

    /**
     * 平台服务费比例（%）
     */
    private BigDecimal platformServiceCommissionRatio;

    /**
     * 支付方式（默认1001。1001-网店支付）
     */
    private String paymentMethod;

    /**
     * 配送方式（默认1000,1100；1000-自提；1100-配送；1000,1100-自提+配送）
     */
    private String deliveryWay;
    /**
     * 商品编号，包含普通商品、套餐商品等所有商品集合，查询用 新增不用
     */
    private List<String> goodsNoList;
    /**
     * 系列商品主品查询用，查询用 新增不用
     */
    private List<String> parentGoodsNoList;
}