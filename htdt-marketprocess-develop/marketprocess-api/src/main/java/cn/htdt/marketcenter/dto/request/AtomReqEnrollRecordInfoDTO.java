package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqBaseDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * 报名记录入参
 * <AUTHOR>
 * @date 2021/4/7 16:16
 */
@Data
public class AtomReqEnrollRecordInfoDTO extends ReqBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 记录编号
     */
    private String recordNo;

    /**
     * 活动编号
     */
    private String promotionNo;

    /**
     * 活动名称
     */
    private String promotionName;

    /**
     * 联系人姓名
     */
    private String name;

    /**
     * 联系电话
     */
    private String phone;
    /**
     * 联系电话(加密)
     */
    private String dsPhone;
    /**
     * 收货人
     */
    private String deliveryName;

    /**
     * 收货人手机号
     */
    private String deliveryPhone;

    /**
     * 省编号
     */
    private String provinceCode;

    /**
     * 省名
     */
    private String provinceName;

    /**
     * 市编号
     */
    private String cityCode;

    /**
     * 市名
     */
    private String cityName;

    /**
     * 区域编号
     */
    private String regionCode;

    /**
     * 区域名称
     */
    private String regionName;

    /**
     * 街道编号
     */
    private String streetCode;

    /**
     * 街道名称
     */
    private String streetName;

    /**
     * 详细地址
     */
    private String detailAddress;

    /**
     * 详细地址-加密
     */
    private String dsDetailAddress;


    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 受理状态（1000：不用受理记录 1001：待报名 1002：待受理 1003：已受理 1004：不受理）
     */
    private String applyStatus;
    /**
     * 手机设备号
     */
    private String deviceNumber;
    /**
     * 受理备注
     */
    private String applyExplain;
    /**
     * 店铺报名备注
     */
    private String storeEnrollExplain;
}
