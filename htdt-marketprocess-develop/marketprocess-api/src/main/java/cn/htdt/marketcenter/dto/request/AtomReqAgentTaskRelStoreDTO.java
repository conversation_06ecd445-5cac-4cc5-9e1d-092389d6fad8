package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AtomReqAgentTaskRelStoreDTO extends ReqComPageDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = 1L;

    /**
     * 任务店铺关系唯一编号
     */
    private String taskRelStoreNo;

    /**
     * 任务活动唯一编号
     */
    private String taskNo;

    /**
     * 商家编码
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 店铺编码
     */
    private String storeNo;

    /**
     * 该任务在店铺的状态： 1:上架 2:下架 3:未发布
     */
    private Integer status;

    /**
     * 店铺编号
     */
    List<String> storeNoList;

    /**
     * 任务店铺关系唯一编号
     */
    List<String> taskRelStoreNoList;

}
