package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class AtomReqPointsGiftConvertRecordDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 兑换编号
     */
    private String convertNo;

    /**
     * 粉丝编号
     */
    private String fansNo;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 姓名
     */
    private String name;

    /**
     * 扣减积分
     */
    private Integer reducePoints;

    /**
     * 礼品编号
     */
    private String giftNo;

    /**
     * 礼品名称
     */
    private String giftName;

    /**
     * 兑换数量（件）
     */
    private Integer convertNum;

    /**
     * 是否可用:默认1，1：可用，其余不可用
     */
    private Integer disableFlag;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 创建人ID
     */
    private String createNo;

    /**
     * 最后一次修改人ID
     */
    private String modifyNo;

    /**
     * 手机号或姓名
     */
    private String namePhone;

    private String searchCondition;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    /**
     * 共享店铺（1，否，2.是）
     */
    private String sharingStore;

    /**
     * 礼品归属（1.门店，2.商家）
     */
    private String pointsGiftType;

    /**
     * 当前登录用户身份 1-运营 2-商家 4-店铺 8-单店
     */
    private Integer loginIdentity;

    /**
     * 积分类型, 1-店铺, 2-商家
     */
    private String ruleType;
}
