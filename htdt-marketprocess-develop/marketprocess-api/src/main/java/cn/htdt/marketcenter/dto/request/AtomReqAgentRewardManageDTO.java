package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * 代理人酬劳管理请求dto
 * <AUTHOR>
 * @date 2021/1/27 18:57
 */
@Data
public class AtomReqAgentRewardManageDTO extends ReqComPageDTO implements Serializable {
    /**
     * 序列化
     */
    private static final long serialVersionUID = 1L;
    /**
     * 商家编码
     */
    private String merchantNo;

    /**
     * 店铺编码
     */
    private String storeNo;

    /**
     * 代理人手机号
     */
    private String agentMobile;
}
