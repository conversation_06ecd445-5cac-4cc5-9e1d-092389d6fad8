package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 橙豆规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AtomReqVirtualCoinRuleDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 橙豆规则编号
     */
    private String virtualNo;

    /**
     * 实际支付金额
     */
    private BigDecimal payAmount;

    /**
     * 可用金额
     */
    private BigDecimal availableAmount;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称，对应orgName
     */
    private String storeName;

    /**
     * 是否可用:默认2，1：不可用2:可用
     */
    private Boolean disableFlag;

    /**
     * 20230928蛋品-赵翔宇-商家橙豆-粉丝橙豆, 查询橙豆规则, 需要区分是店铺橙豆规则, 还是共享店铺橙豆规则
     *
     * 橙豆规则类型, 1001-店铺规则, 1002-商家规则, 参考枚举: VirtualCoinRuleTypeEnum
     */
    private String ruleType;
}
