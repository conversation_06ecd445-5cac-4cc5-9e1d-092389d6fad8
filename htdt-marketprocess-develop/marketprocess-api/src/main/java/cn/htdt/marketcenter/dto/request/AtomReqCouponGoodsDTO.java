package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 优惠券商品信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="AtomReqCouponGoodsDTO对象", description="优惠券商品信息表")
public class AtomReqCouponGoodsDTO extends ReqBaseDTO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "促销编码")
    private String promotionNo;

    @ApiModelProperty(value = "优惠券编号")
    private String couponNo;

    @ApiModelProperty(value = "券使用范围(1001:店铺全场通用 1002:云池商品通用 1003:指定云池商品 1004:店铺指定类目 1005:店铺指定品牌 1006:店铺指定商品 )")
    private String couponUseScope;

    @ApiModelProperty(value = "优惠券商品list")
    private List<AtomReqCouponGoodsRelationDTO> reqCouponGoodsRelationDTOS;

    @ApiModelProperty(value = "优惠券品牌list")
    private List<AtomReqCouponBrandRelationDTO> reqCouponBrandRelationDTOS;

    @ApiModelProperty(value = "优惠券分类list")
    private List<AtomReqCouponCategoryRelationDTO> reqCouponCategoryRelationDTOS;

    @ApiModelProperty(value = "优惠券店铺类目list")
    private List<AtomReqCouponCategoryRelationDTO> reqCouponStoreCategoryRelationDTOS;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
