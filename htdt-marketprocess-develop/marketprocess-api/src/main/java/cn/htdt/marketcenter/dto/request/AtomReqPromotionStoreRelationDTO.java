package cn.htdt.marketcenter.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 活动店铺关联上下架表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="AtomReqPromotionStoreRelationDTO对象", description="活动店铺关联上下架表")
public class AtomReqPromotionStoreRelationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键编号")
    private String ruleNo;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "平台店铺类型(1001:平台 1003:店铺)")
    private String storeSourceType;

    @ApiModelProperty(value = "活动店铺的商家编码")
    private String merchantNo;

    @ApiModelProperty(value = "活动店铺编码")
    private String storeNo;

    @ApiModelProperty(value = "报名活动编号")
    private String enrollNo;

    @ApiModelProperty(value = "活动店铺类型(1001:全部店铺 1002:报名活动)")
    private String storeType;

    @ApiModelProperty(value = "店铺自主上下架状态（1：未上架 2：上架）")
    private Integer upDownFlag;

    @ApiModelProperty(value = "是否可用:默认1，1：可用，其余不可用")
    private Integer disableFlag;
    
    @ApiModelProperty(value = "数据逻辑删除标识，默认1未删除，其余已删除")
    private Integer deleteFlag;

    @ApiModelProperty(value = "创建人编号")
    private String createNo;
    @ApiModelProperty(value = "创建人姓名")
    private String createName;
    @ApiModelProperty(value = "修改人编号")
    private String modifyNo;
    @ApiModelProperty(value = "修改人姓名")
    private String modifyName;

    @ApiModelProperty(value = "最新上下架时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime latestUpDownTime;

    @ApiModelProperty(value = "活动编号集合")
    private List<String> promotionNoList;
}
