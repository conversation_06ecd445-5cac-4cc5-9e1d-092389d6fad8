package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 橙豆收入支出记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AtomReqVirtualCoinRecordDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 交易类型类型，字典
     */
    private String tradeType;

    /**
     * 实际支付金额
     */
    private BigDecimal paymentAmount;

    /**
     * 橙豆变动金额
     */
    private BigDecimal virtualCoinChange;

    /**
     * 橙豆总额
     */
    private BigDecimal totalCoin;

    /**
     * 支付方式,字典virtual_PAYMENT_METHOD
     */
    private String paymentMethod;

    /**
     * 支付渠道,字典paymentChannelEnum
     */
    private String paymentChannel;

    /**
     * 粉丝编号
     */
    private String fanNo;

    /**
     * 粉丝列表
     */
    private List<String> fansNoList;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 手机号加密
     */
    private String mobileCipher;

    /**
     * 用户名
     */
    private String fansName;

    /**
     * 用户姓名加密
     */
    private String fansNameCipher;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称，对应orgName
     */
    private String storeName;

    /**
     * 是否可用:默认2，1：不可用2:可用
     */
    private Boolean disableFlag;

    /**
     * 橙豆规则编号
     */
    private String virtualNo;

    /**
     * 1:今日 2:昨天 3近一周 4:近30天 5:自定义
     */
    private Integer dayType;

    /**
     * 5:自定义-开始时间
     * pattern = "yyyy-MM-dd"
     */
    private LocalDateTime countStartTime;

    /**
     * 5:自定义-结束时间
     * pattern = "yyyy-MM-dd"
     */
    private LocalDateTime countEndTime;

    /**
     * 0:全部 1000:汇享购 1001:超级老板PC 1002:超级老板APP 1003:一体机
     */
    private String appChannelSource;

    /**
     * 20230928蛋品-赵翔宇-商家橙豆-粉丝橙豆, 查询橙豆规则, 需要区分是店铺橙豆规则, 还是共享店铺橙豆规则
     *
     * 橙豆规则类型, 1001-店铺规则, 1002-商家规则, 参考枚举: VirtualCoinRuleTypeEnum
     */
    private String ruleType;
}
