package cn.htdt.marketcenter.dto.request;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021-01-27
 * @Description 代理人酬劳关联过程
 **/
@Data
public class AtomReqAgentRewardRelRecordDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 查询主键
     */
    private String agentAwardNo;

    /**
     * 查询主键
     */
    private String rewardRecordNo;

    /**
     * 类型，作用是为了统计拉新的粉丝统计，1代理人酬劳 2额外酬劳 3新粉丝酬劳，对应枚举RewardSetTypeEnum
     */
    private Integer setType;
}