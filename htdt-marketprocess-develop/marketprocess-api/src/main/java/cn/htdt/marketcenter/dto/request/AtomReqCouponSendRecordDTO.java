package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 优惠券发送记录表
 *
 * <AUTHOR>
 */
@Data
public class AtomReqCouponSendRecordDTO extends ReqComPageDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = -189493763904911627L;


    /**
     * 发送批次编号
     */
    private String sendBatchNo;

    @ApiModelProperty(value = "促销编码")
    private String promotionNo;

    /**
     * 优惠券编号
     */
    private String couponNo;

    /**
     * 是否短信通知(1:否 2:是)
     */
    private Integer smsNoticeFlag;

    /**
     * 是否可用:默认1，1：可用，其余不可用
     */
    private Integer disableFlag;

    /**
     * 短信发送批次
     */
    private String smsBatchNo;

    /**
     * 发券规则(1001:指定单人 1002:指定多人)
     */
    private String sendCouponRule;

    /**
     * 短信内容
     */
    private String smsContent;


    /**
     * 发送总数
     */
    private Integer sendCount;
}
