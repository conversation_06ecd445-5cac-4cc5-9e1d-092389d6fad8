package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 优惠券记录请求DTO
 *
 * <AUTHOR>
 */
@Data
public class AtomReqCouponUserRecordDTO extends ReqComPageDTO {

    /**
     * 序列化
     */
    private static final long serialVersionUID = -5631099019662787864L;

    /**
     * 用户券编号
     */
    private String userCouponNo;

    /**
     * 用户券编码集合
     */
    private List<String> userCouponNoList;

    /**
     * 发送批次编号
     */
    private String sendBatchNo;

    @ApiModelProperty(value = "促销编码")
    private String promotionNo;

    /**
     * 优惠券编号
     */
    private String couponNo;

    /**
     * 优惠券名称
     */
    private String couponName;

    @ApiModelProperty(value = "使用门槛阈值（满减时表示满多少元）")
    private BigDecimal discountThreshold;

    @ApiModelProperty(value = "用户使用券的券面额")
    private BigDecimal userCouponValue;

    /**
     * 用户编号
     */
    private String fanNo;

    /**
     * 代理人编号
     */
    private String agentNo;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "手机号-加密")
    private String dsPhone;

    /**
     * 来源类型（1001：自已领取 1002：发放自动领取）
     */
    private String sourceType;

    /**
     * 券有效期（1001：长期有效 1002：指定日期 1003:自领取后天数有效）
     */
    private String couponPeriodValidity;

    /**
     * 券有效天数（只有有效期为1003时使用）
     */
    private Integer couponEffectiveDay;

    /**
     * 券开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime couponEffectiveTime;

    /**
     * 券结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime couponInvalidTime;

    @ApiModelProperty("券使用说明")
    private String couponUseExplain;

    /**
     * 是否短信通知(1:否 2:是)
     */
    private Integer smsNoticeFlag;

    /**
     * 是否代理人赠送(1:否 2:是)
     */
    private Integer agentPresentFlag;

    /**
     * 是否自用(1:否 2:是)
     */
    private Integer selfUseFlag;

    /**
     * 是否使用(1:否 2:是)
     */
    private Integer useFlag;

    /**
     * 是否可用:默认1，1：可用，其余不可用
     */
    private Integer disableFlag;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 粉丝/代理人:1：粉丝，代理人
     */
    private Integer type;
    /**
     * 我的优惠券列表查看类型 1001：未使用 1002：已使用： 1003：已过期
     */
    private String myCouponType;

    /**
     * 批量优惠券编码
     */
    private List<String> couponNoList;
    /**
     * 券活动类型集合     促销类型 3001:平台-手动发代理人券 3002::平台-手动发粉丝券 3003:平台-网店代理人领券 3004:平台-网店粉丝领券 3005:店铺-手动发粉丝券 3006:店铺-网店粉丝领券 3007:膨胀红包\r\n            3100:抽奖券',
     */
    private List<String> promotionCouponTypeList;

    /**
     * 券展示位置：1001：我的优惠券列表  1002：代理人发粉丝列表
     */
    private String couponShowType;
    /**
     * 券类型
     */
    @ApiModelProperty(value = "券类型 1001:满减券 1002:折扣券 1003:现金券 1004:代金券", required = true)
    private String couponType;

    @ApiModelProperty("订单编号")
    private String orderNo;

}
