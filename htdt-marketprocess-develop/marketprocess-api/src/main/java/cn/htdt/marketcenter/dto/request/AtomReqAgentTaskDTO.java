package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-01-13
 * @Description 代理人任务
 **/
@Data
public class AtomReqAgentTaskDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务活动唯一编号
     */
    private String taskNo;

    /**
     * 任务活动类型，0001:拉新任务 枚举TaskTypeEnum
     */
    private String taskType;

    /**
     * 参与会员店类型，1：部分店铺 2：全部店铺 枚举TaskStoreTypeEnum
     */
    private Integer taskStoreType;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务图片地址
     */
    private String taskImageUrl;

    /**
     * 任务开始时间
     */
    private LocalDate taskStartTime;

    /**
     * 任务结束时间
     */
    private LocalDate taskEndTime;

    /**
     * 总共酬劳上限-佣金
     */
    private BigDecimal totalYj;

    /**
     * 总共酬劳上限-汇金币
     */
    private BigDecimal totalHjb;

    /**
     * 每个代理人酬劳上限-佣金
     */
    private BigDecimal totalAgentYj;

    /**
     * 每个代理人酬劳上限-汇金币
     */
    private BigDecimal totalAgentHjb;

    /**
     * 前台展示酬劳
     */
    private String showMessage;

    /**
     * 任务上下架标识，默认0未上架 1上架 2下架
     */
    private Integer statusFlag;

    /**
     * 任务复制标识，0:正常 1：复制
     */
    private Integer copyFlag;

    /**
     * 酬劳设置集合
     */
    List<AtomReqAgentMarketRewardsetDTO> listRewardSetDTO;

    /**
     * 任务规则说明
     */
    private byte[] taskMemo;

    /**
     * 酬劳设置快照集合
     */
    List<AtomReqAgentMarketRewardsetRecordDTO> listRewardSetRecordDTO;

    /**
     * 任务时间状态
     */
    private Integer taskTimeStatus;

    /**
     * 任务有效开始时间
     */
    private LocalDate taskValidBeginTime;

    /**
     * 任务有效结束时间
     */
    private LocalDate taskValidEndTime;

    /**
     * 任务创建开始时间
     */
    private LocalDateTime taskCreateStartTime;

    /**
     * 任务创建结束时间
     */
    private LocalDateTime taskCreateEndTime;

    /**
     * 店铺编号
     */
    private String storeNo;

    /**
     * 代理人编号
     */
    private String agentNo;

    /**
     * 酬劳类型
     */
    private Integer rewardType;

}