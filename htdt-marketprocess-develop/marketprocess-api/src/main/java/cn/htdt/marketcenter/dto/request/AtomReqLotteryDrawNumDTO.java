package cn.htdt.marketcenter.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 活动中奖记录数量
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-03
 */
@Data
public class AtomReqLotteryDrawNumDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "奖品编号")
    private String rewardNo;

    @ApiModelProperty(value = "商家编号")
    private String  merchantNo;

    @ApiModelProperty(value = "店铺编号")
    private String storeNo;

    @ApiModelProperty(value = "是否过滤已作废奖品 2：不过滤")
    private Integer filterFlag;
}
