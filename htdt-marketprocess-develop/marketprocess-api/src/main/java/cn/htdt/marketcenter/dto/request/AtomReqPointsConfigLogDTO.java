package cn.htdt.marketcenter.dto.request;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class AtomReqPointsConfigLogDTO extends AtomReqPointsConfigDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日志编号
     */
    private String logNo;

    /**
     * 操作类型:默认1，1：启用，2：禁用
     */
    private Integer operateType;

    /**
     * 操作内容
     */
    private String operateContent;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 订单应用程序来源
     */
    private String appChannelSource;
}
