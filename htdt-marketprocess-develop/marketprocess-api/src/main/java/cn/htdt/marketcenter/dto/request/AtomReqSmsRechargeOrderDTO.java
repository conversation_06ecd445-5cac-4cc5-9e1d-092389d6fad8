package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021-07-01
 * @description 短信充值订单请求DTO
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class AtomReqSmsRechargeOrderDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     **/
    private String orderNo;

    /**
     * 订单类型（1001套餐充值，1002自定义充值）
     **/
    private String orderType;

    /**
     * 变动来源(1001:短信充值 1002:金币兑换)
     */
    private String changeSource;

    /**
     * 充值条数
     **/
    private Integer rechargeNum;

    /**
     * 订单金额
     **/
    private BigDecimal orderAmount;

    /**
     * 实付金额
     **/
    private BigDecimal realAmount;

    /**
     * 支付订单号
     **/
    private String payOrderNo;

    /**
     * 支付渠道
     **/
    private String paymentChannelDetail;

    /**
     * 支付状态(1000:待支付 1001:部分支付 1002:已支付)
     **/
    private String orderPaymentStatus;

    /**
     * 是否可用:默认1，1：可用，其余不可用
     **/
    private Integer disableFlag;

    /**
     * 商家编号
     **/
    private String merchantNo;

    /**
     * 商家名称
     **/
    private String merchantName;

    /**
     * 门店编号，对应orgId
     **/
    private String storeNo;

    /**
     * 店铺名称
     **/
    private String storeName;

    /**
     * 统计开始时间
     */
    private LocalDateTime countStartTime;

    /**
     * 统计结束时间
     */
    private LocalDateTime countEndTime;

    /**
     * 店铺编码、店铺编码
     */
    private String storeKeyWord;


    /**
     * 选中合同模板编码,如有多个用','隔开
     */
    private String projectContract;

    /**
     * 合同状态 12:线上两方合同 13:线上3方合同 2:线下
     **/
    private Integer contractState;
}
