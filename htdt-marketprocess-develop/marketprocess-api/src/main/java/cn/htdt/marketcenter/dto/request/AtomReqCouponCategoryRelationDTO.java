package cn.htdt.marketcenter.dto.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 分类关联
 *
 * <AUTHOR>
 */
@Data
public class AtomReqCouponCategoryRelationDTO implements Serializable {

    private static final long serialVersionUID = 2176162328009549439L;

    /**
     * 促销编码
     */
    private String promotionNo;

    /**
     * 优惠券编号
     */
    private String couponNo;

    /**
     * 类目编号
     */
    private String categoryNo;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 操作标识:默认1，1：添加，2删除
     */
    private Integer operatFlag;
}
