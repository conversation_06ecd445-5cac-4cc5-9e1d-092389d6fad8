package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.annon.Encrypt;
import cn.htdt.common.dto.annon.EncryptField;
import cn.htdt.common.dto.enums.HandlerPolicy;
import cn.htdt.common.dto.enums.MaskType;
import cn.htdt.common.dto.request.ReqComPageDTO;
import cn.htdt.marketprocess.dto.request.ReqMarketGoodsWriteOffDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 活动中奖记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-03
 */
@Encrypt
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="LotteryDrawRecordDomain对象", description="活动中奖记录表")
public class AtomReqLotteryDrawRecordDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "记录编号")
    private String recordNo;

    @ApiModelProperty(value = "奖品编号")
    private String rewardNo;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "活动名称")
    private String promotionName;

    @ApiModelProperty(value = "奖品类型（1001:实物礼品 1002:优惠券 1003:话费券 1004:话费 1005:汇金币 1006:积分  9999:谢谢惠顾）")
    private String rewardType;

    @ApiModelProperty(value = "奖品名称")
    private String rewardName;

    @ApiModelProperty(value = "商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "订单中奖的原始订单编号")
    private String sourceOrderNo;

    @ApiModelProperty(value = "券面额")
    private BigDecimal couponValue;

    @ApiModelProperty(value = "使用门槛阈值（满减时表示满多少元）")
    private BigDecimal discountThreshold;

    @ApiModelProperty(value = "券标识")
    private String couponIdentity;

    @ApiModelProperty(value = "券有效期（1001：长期有效 1002：指定日期 1003:自领取后天数有效）")
    private String couponPeriodValidity;

    @ApiModelProperty(value = "券开始时间")
    private LocalDateTime couponEffectiveTime;

    @ApiModelProperty(value = "券结束时间")
    private LocalDateTime couponInvalidTime;

    @ApiModelProperty(value = "券使用渠道 (1001:汇享购下单 1002:门店下单)")
    private String couponUseChannel;

    @ApiModelProperty(value = "券使用范围(1001:店铺全场通用 1002:云池商品通用 1003:指定云池商品)")
    private String couponUseScope;

    @ApiModelProperty(value = "券编号")
    private String couponNo;

    @ApiModelProperty(value = "券使用状态(1:未使用 2:已使用)")
    private Integer couponUseStatus;

    @ApiModelProperty(value = "配送方式 （1001:自提）")
    private String deliveryType;

    /**
     * 蛋品, 商家抽奖订单配送方式(1101:自有配送, 1102: 快递配送), 参考DeliveryTypeSelectEnum
     */
    @ApiModelProperty(value = "商家抽奖订单配送方式, 1101: 自有配送, 1102: 快递配送")
    private String merchantLotteryDeliveryType;

    @ApiModelProperty(value = "奖品值 (话费、金币、积分)")
    private String rewardValue;

    @ApiModelProperty(value = "粉丝编号")
    private String fanNo;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "手机号")
    @EncryptField(handlerPolicy= HandlerPolicy.MASK, maskType = MaskType.MOBILE)
    private String phone;

    @ApiModelProperty(value = "手机号-加密")
    @EncryptField
    private String dsPhone;

    @ApiModelProperty(value = "充值手机号")
    private String rechargePhone;

    @ApiModelProperty(value = "充值状态（1001：未充值；1002：已充值），默认1001")
    private String rechargeStatus;

    @ApiModelProperty(value = "充值时间")
    private LocalDateTime rechargeTime;

    @ApiModelProperty(value = "收货人")
    private String deliveryName;

    @ApiModelProperty(value = "收货人手机号")
    @EncryptField(handlerPolicy= HandlerPolicy.MASK, maskType = MaskType.MOBILE)
    private String deliveryPhone;

    @ApiModelProperty(value = "收货人手机号-加密")
    @EncryptField
    private String dsDeliveryPhone;

    @ApiModelProperty(value = "收货人地址")
    @EncryptField(handlerPolicy= HandlerPolicy.MASK, maskType = MaskType.ADDRESS)
    private String deliveryAddress;

    @ApiModelProperty(value = "收货人地址-加密")
    @EncryptField
    private String dsDeliveryAddress;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "订单状态")
    private String orderStatus;

    @ApiModelProperty(value = "发放/领取状态(1001:已领取 1002:未领取 1003:已发放 1004:未发放 1005:发放失败 )")
    private String sendReceiveStatus;

    @ApiModelProperty(value = "是否可用:默认1，1：可用，其余不可用")
    private Integer disableFlag;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "门店编号，对应orgId")
    private String storeNo;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "奖品编号List")
    private List<String> rewardList = new ArrayList<>();

    @ApiModelProperty(value = "中奖开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "中奖结束时间")
    private LocalDateTime endTime;


    @ApiModelProperty(value = "来源 1001：平台 1002：商家 1003：店铺")
    private String sourceType;

    @ApiModelProperty(value = "核销状态:1：已核销 2：待核销")
    private Integer writeOffStatus;

    @ApiModelProperty(value = "核销时间")
    private LocalDateTime writeOffTime;

    @ApiModelProperty(value = "核销码")
    private String writeOffCode;

    @ApiModelProperty(value = "核销二维码地址")
    private String writeOffQrUrl;

    @ApiModelProperty(value = "活动类型 1000:抽奖  2000:商品促销 3000:优惠券")
    private String activityType;

    /**
     * 物流单号
     */
    private String trackingNumber;

    /**
     * 物流公司名称
     */
    private String expressDeliveryName;

    /**
     * 物流公司code
     */
    private String expressDeliveryCode;

    /**
     * 中奖记录集合
     */
    private List<String> recordNos;

    /**
     * 中奖记录集合
     */
    private List<String> rewardNos;

    /**
     * 订单编号集合
     */
    private List<String> orderNos;

    /**
     * 活动编号集合
     */
    private List<String> promotionNos;

    /**
     * 用户券编号
     */
    private String userCouponNo;

    /**
     * 是否过滤 9999 谢谢惠顾   1：否  2：是
     */
    private Integer losingFlag;

    @ApiModelProperty(value = "来源 1001：平台  1003：店铺")
    private List<String> sourceTypes;

    @ApiModelProperty("订单号或者商品名称综合字段")
    private String orderNoOrGoodsName;

    @ApiModelProperty("订单商品信息集合")
    private List<ReqMarketGoodsWriteOffDTO> marketGoodsWriteOffList;

    /**
     * 用户身份(1运营，2商家，4-店铺；8-单店)
     */
    private Integer loginIdentity;
}
