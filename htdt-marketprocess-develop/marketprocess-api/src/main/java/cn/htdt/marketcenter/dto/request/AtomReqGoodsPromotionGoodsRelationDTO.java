package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 促销商品参与商品的请求实体类
 *
 * <AUTHOR>
 * @date 2021-06-28
 *
 *
 *
 * 东启
 * 202503
 */
@Data
public class AtomReqGoodsPromotionGoodsRelationDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 5934956239338651187L;


    // 折扣率
    private String discountRate;


    /**
     * 活动编号
     */
    private String promotionNo;

    /**
     * 来源 1001：平台 1002：商家 1003：店铺
     */
    private String sourceType;

    /**
     * 促销类型 2001:秒杀 2002:拼团 2003:预售 2004:单品促销 2005:满额促销 2006:满量促销 2007:满额赠 2008:套餐
     */
    private String promotionType;

    /**
     * 时间段编号(promotion_type:2001秒杀时使用)
     */
    private String periodNo;

    /**
     * 套餐编号
     */
    private String packageNo;

    /**
     * 商品编号
     */
    private String goodsNo;

    /**
     * 系列商品主品商品编号，非系列商品商品编号
     */
    private String parentGoodsNo;

    /**
     * 套餐商品类型(1001:主商品 1002:搭配品)
     */
    private String packageGoodsType;

    /**
     * 赠品名称
     */
    private String presentGiftName;

    /**
     * 赠品是否关联商品(1:否 2:是)
     */
    private Integer presentGiftGoodsFlag;

    /**
     * 是否子品(1:否 2:是)
     */
    private Integer subGoodsFlag;

    /**
     * 活动费用
     */
    private BigDecimal activityExpenses;

    /**
     * 代理人佣金比例（%）
     */
    private BigDecimal agentCommissionRatio;

    /**
     * 分销店佣金比例（%）
     */
    private BigDecimal distributionStoreCommissionRatio;

    /**
     * 平台服务费比例（%）
     */
    private BigDecimal platformServiceCommissionRatio;

    /**
     * 活动价格（秒杀价、拼团价、预售价、搭配价、特惠促销）
     */
    private BigDecimal promotionPrice;

    /**
     * 定金价格
     */
    private BigDecimal depositPrice;

    /**
     * 定金抵扣价格
     */
    private BigDecimal depositDiscountPrice;

    /**
     * 尾款价格
     */
    private BigDecimal balanceDuePrice;

    /**
     * 最终实际售价
     */
    private BigDecimal realSalePrice;

    /**
     * 活动设置库存
     */
    private Integer settingStockNum;

    /**
     * 活动剩余库存
     */
    private Integer remainStockNum;

    /**
     * 成团人数
     */
    private Integer groupManNum;

    /**
     * 中奖人数
     */
    private Integer awardManNum;

    /**
     * 限购数
     */
    private Integer limitBuyNum;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 是否可用:默认1，1：可用，2不可用
     */
    private Integer disableFlag;

    /**
     * 活动编号集合
     */
    private List<String> promotionNoList;

    /**
     * 购买商品数
     */
    private Integer buyGoodsNum;

    /**
     * 每场开始时间字符串，格式为HH:mm:ss
     */
    private LocalTime startTime;

    /**
     * 每场结束时间字符串，格式为HH:mm:ss
     */
    private LocalTime endTime;

    /**
     * 查询子品标识：1-非子品、2-子品
     */
    private Integer childFlag;
    /**
     * 商品编码集合
     */
    private List<String> goodsNoList;

    /**
     * 查询类型：1-当前活动和其他活动互斥；2-非当前活动的其他活动互斥；3-时间有重合的互斥
     */
    private Integer queryType;

    /**
     * 活动有效期开始时间
     */
    private LocalDateTime effectiveTime;

    /**
     * 活动有效期结束时间
     */
    private LocalDateTime invalidTime;

    /**
     * 限时购活动的多场次时间段数据
     */
    List<AtomReqLimitTimePeriodDTO> periodList;

    /**
     * 活动场次编码集合
     */
    List<String> periodNoList;

    /**
     * 套餐分组编号
     */
    private String packageGroupingNo;

    /**
     * 套餐分组名称
     */
    private String packageGroupingName;

    /**
     * 套餐商品数量
     */
    private Integer packageGoodsNum;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
