package cn.htdt.marketcenter.dto.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AtomReqAgentTaskRelStoreBatchListDTO  implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = 1L;

    /**
     * 任务编号集合
     */
    private List<String> taskNoList;

    /**
     * 商家编码
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;


    /**
     * 店铺编号
     */
    List<String> storeNoList;
    /**
     * 创建人
     */
    private String createNo;
    /**
     * 创建人
     */
    private String createName;

}
