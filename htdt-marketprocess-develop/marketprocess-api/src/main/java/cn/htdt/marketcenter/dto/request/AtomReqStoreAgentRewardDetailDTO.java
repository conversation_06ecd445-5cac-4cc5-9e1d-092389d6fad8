package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/1/26 18:57
 */
@Data
public class AtomReqStoreAgentRewardDetailDTO   extends ReqComPageDTO implements Serializable {
    /**
     * 序列化
     */
    private static final long serialVersionUID = 1L;

    /**
     * 酬劳状态
     */
    private Integer rewardStatusFlag;
    /**
     * 是否发放，1:未发放或不需要发放 2:已发放
     */
    private Integer isReceive;
    /**
     * 店铺编码
     */
    private String storeNo;
    /**
     * 代理人编码
     */
    private String agentNo;

    /**
     * 酬劳类型
     */
    private Integer rewardType;

    /**
     * 营销活动类型
     */
    private String marketType;

    /**
     * 酬劳提供方 1 - 本店 2 - 平台
     */
    private Integer marketSource;

}
