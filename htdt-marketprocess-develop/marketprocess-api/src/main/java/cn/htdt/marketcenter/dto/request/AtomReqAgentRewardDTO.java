package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.annon.Encrypt;
import cn.htdt.common.dto.annon.EncryptField;
import cn.htdt.common.dto.enums.HandlerPolicy;
import cn.htdt.common.dto.enums.MaskType;
import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-01-14
 * @Description 代理人酬劳信息请求DTO
 **/
@Encrypt
@Data
public class AtomReqAgentRewardDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 查询主键
     */
    private String agentAwardNo;

    /**
     * AGENT_REWARD_RECORD主键，计算从哪一条过程数据得到的奖励
     */
    private String rewardRecordNo;

    /**
     * 代理人的编码
     */
    private String agentNo;

    /**
     * 代理人手机号
     */
    @EncryptField(handlerPolicy= HandlerPolicy.MASK, maskType = MaskType.MOBILE)
    private String agentMobile;

    /**
     * 代理人手机号-加密
     */
    @EncryptField
    private String dsAgentMobile;

    /**
     * 代理人姓名
     */
    private String agentName;

    /**
     * 任务活动唯一编号或者是商品编号
     */
    private String taskOrGoodsNo;


    /**
     * 系列商品父ID,非系列商品就是goods_no，方便查询
     */
    private String parentGoodsNo;

    /**
     * 营销活动类型，对应枚举MarketTypeEnum
     */
    private String marketType;

    /**
     * 任务名称|商品名称
     */
    private String taskOrGoodsName;

    /**
     * 分销店对应商户编号
     */
    private String distributionMerchantNo;

    /**
     * 分销店铺编号-对应老系统的orgId
     */
    private String distributionStoreNo;

    /**
     * 店铺编号
     */
    private String storeNo;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 父订单编号，用于涉及到订单的分销商品
     */
    private String parentOrderNo;

    /**
     * 订单编号，用于涉及到订单的分销商品
     */
    private String orderNo;

    /**
     * 酬劳类型，1佣金 2汇金币 3礼品 4专享现金券 5服务券 6话费券，对应枚举RewardTypeEnum
     */
    private Integer rewardType;

    /**
     * 酬劳内容，除了佣金是具体的元外，其余都是1张/件/个
     */
    private BigDecimal rewardValue;

    /**
     * 酬劳内容名称，礼品、现金券、服务券名称
     */
    private String rewardName;

    /**
     * 商品个数，涉及到商品才有
     */
    private Integer goodsNum;

    /**
     * 酬劳状态标识，对应枚举RewardStatusFlagEnum
     */
    private Integer rewardStatusFlag;

    /**
     * 酬劳状态标识，标识修改前的酬劳状态，对应枚举RewardStatusFlagEnum
     */
    private Integer beforeRewardStatusFlag;

    /**
     * 酬劳状态标识更新时间
     */
    private LocalDateTime rewardStatusTime;

    /**
     * 订单最终完成时间
     */
    private LocalDateTime orderTime;

    /**
     * 礼品名称|服务券|现金券|话费券有效时间类型 1长期有效 2自定义有效期 3自定义有效天数
     */
    private Integer rewardDateType;

    /**
     * 酬劳有效天数
     */
    private Integer rewardDays;

    /**
     * 酬劳开始时间
     */
    private LocalDate rewardStart;

    /**
     * 酬劳结束时间
     */
    private LocalDate rewardEnd;

    /**
     * 现金券|话费券，满多少
     */
    private BigDecimal rewardUp;

    /**
     * 现金券|话费券，减多少
     */
    private BigDecimal rewardLess;

    /**
     * 类型，1代理人酬劳 2额外酬劳 3新粉丝酬劳，对应枚举RewardSetTypeEnum
     */
    private Integer setType;

    /**
     * 是否发放，1:未发放或不需要发放 2:已发放
     */
    private Integer isReceive;

    /**
     * 所有奖品使用说明通用
     */
    private String rewardMemo;

    /**
     * 云池供货价
     */
    private BigDecimal cloudPoolSupplyPrice;

    /**
     * 商品的实付金额
     */
    private BigDecimal goodsPrice;

    /**
     * 订单应收总金额
     */
    private BigDecimal shouldAmount;

    /**
     * 支付确认时间
     */
    private LocalDateTime orderPaymentConfirmDate;

    /**
     * 分销店佣金
     */
    private BigDecimal distributionRewardValue;

    /**
     * 分销店佣金状态标识，对应枚举RewardSpecialStatusFlagEnum
     */
    private Integer distributionRewardStatusFlag;

    /**
     * 汇通数科服务费
     */
    private BigDecimal htdtRewardValue;

    /**
     * 供货店分账
     */
    private BigDecimal storeRewardValue;

    /**
     * 云池商品-平台服务费
     */
    private BigDecimal platformServiceCommission;

    /**
     * 云池商品-平台净利润
     */
    private BigDecimal platformNetProfit;

    /**
     * 云池商品-供货店分账
     */
    private BigDecimal supplyShopSharingProfit;

    /**
     * 云池商品-后返供货店金额
     */
    private BigDecimal returnSupplyShopMoney;

    /**
     * 云池商品-活动费用
     */
    private BigDecimal activityExpenses;

    /**
     * 云池商品-毛利润
     */
    private BigDecimal grossProfit;

    /**
     * 酬劳获得开始时间 yyyy-MM-dd HH:mm:ss
     */
    private LocalDateTime receiveStartTime;

    /**
     * 酬劳获得结束时间 yyyy-MM-dd HH:mm:ss
     */
    private LocalDateTime receiveEndTime;

    /**
     * 酬劳统计开始时间 yyyy-MM-dd HH:mm:ss
     */
    private LocalDateTime calculateStartTime;

    /**
     * 商品规格属性
     */
    private String goodsAttribute;

    /**
     * 粉丝操作状态 对应枚举
     */
    private Integer fansOperateStatus;

    /**
     * 粉丝操作时间
     */
    private LocalDateTime fansOperateDate;

    /**
     * 粉丝手机号
     */
    @EncryptField(handlerPolicy= HandlerPolicy.MASK, maskType = MaskType.MOBILE)
    private String fanMobile;
    /**
     * 粉丝手机号-加密
     */
    @EncryptField
    private String dsFanMobile;

    /**
     * 酬劳状态集合
     */
    private List<Integer> rewardStatusList;

    /**
     * 统计开始时间
     */
    private LocalDateTime countStartTime;

    /**
     * 统计结束时间
     */
    private LocalDateTime countEndTime;

    /**
     * 核销状态（1：待核销  2：已核销），对应枚举WhetherEnum
     */
    private Integer writeOffStatus;

    /**
     * 核销码
     */
    private String writeOffCode;

    /**
     * 核销日期
     */
    private LocalDateTime writeOffTime;

    /**
     * 代理人编号list
     */
    private List<String> agentNoList;

    /**
     * 店铺编号list
     */
    private List<String> storeNoList;

    /**
     * 酬劳类型list
     */
    private List<Integer> rewardTypeList;

    /**
     * 代理人酬劳状态集合
     */
    private List<Integer> rewardStatusFlagList;

    /**
     * 任务和商品集合
     */
    private List<String> taskOrGoodsNoList;

    /**
     * 营销类型list
     */
    private List<String> marketTypeList;

    /**
     * 订单编号list
     */
    private List<String> orderNoList;

    /**
     * 是否代理人查询 1：否 2：是
     */
    private Integer agentQueryFlag;

    /**
     * 排序
     */
    private Integer sortWay;

    /**
     * 代理人姓名或手机号
     */
    private String agentMobileOrName;

    /**
     * 分销店佣金状态集合
     */
    private List<Integer> distributionRewardStatusFlagList;

}
