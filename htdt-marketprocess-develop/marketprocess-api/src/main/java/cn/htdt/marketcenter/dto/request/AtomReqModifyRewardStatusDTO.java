package cn.htdt.marketcenter.dto.request;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2021-02-03
 * @Description
 **/
@Data
public class AtomReqModifyRewardStatusDTO implements Serializable {
    /**
     * 序列化
     */
    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 任务活动唯一编号或者是商品编号
     */
    private String taskOrGoodsNo;

    /**
     * 云池商品标识，1:NO 2:YES
     * 默认是否
     */
    private Integer cloudGoodsFlag = 1;

    /**
     * 代理人+数科酬劳状态
     */
    private Integer rewardStatusFlag;

    /**
     * 分销店佣金状态
     */
    private Integer distributionRewardStatusFlag;

    /**
     * 支付确认时间
     */
    private LocalDateTime orderPaymentConfirmDate;

    /**
     * 订单最终完成时间
     */
    private LocalDateTime orderTime;
}