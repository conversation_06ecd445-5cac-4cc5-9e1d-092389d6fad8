package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 报名活动列表入参
 *
 * <AUTHOR>
 * @date 2021/4/1 16:16
 */
@Data
public class AtomReqEnrollRuleListDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 活动所属店铺
     */
    private String storeNo;

    /**
     * 活动编号
     */
    private String promotionNo;
    /**
     * 活动名称
     */
    private String promotionName;

    /**
     * 更新开始时间
     */
    private LocalDateTime modifyStartTime;

    /**
     * 更新结束时间
     */
    private LocalDateTime modifyEndTime;

    /**
     * 活动开始时间
     */
    private LocalDateTime effectiveTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime invalidTime;

    /**
     * 活动店铺类型(1001:需店铺报名参与 1002:指定店铺直接参与)
     */
    private String storeType;

    /**
     * 上下架状态（1：未上架 2：上架）
     */
    private Integer upDownFlag;
    /**
     * 剔除的活动编号
     */
    private String notPromotionNoList;
    /**
     * 活动状态
     */
    private String status;

    /**
     * 不查草稿状态的报名活动标识：2-不查，其余-查
     */
    private Integer notOnlySave;
}
