package cn.htdt.marketcenter.dto.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 代理人或者任务的拉新总数
 * <AUTHOR>
 * @date 2021/2/20 18:57
 */
@Data
public class AtomReqJoinTaskNumDTO implements Serializable {
    /**
     * 序列化
     */
    private static final long serialVersionUID = 1L;
    /**
     * 代理人编码集合
     */
    private List<String> agentNoList;
    /**
     * 店铺集合
     */
    private List<String> storeNoList;
    /**
     * 任务集合
     */
    private List<String> taskNoList;
}
