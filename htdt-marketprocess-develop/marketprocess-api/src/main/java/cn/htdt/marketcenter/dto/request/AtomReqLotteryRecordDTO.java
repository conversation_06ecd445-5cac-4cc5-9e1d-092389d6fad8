package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 抽奖记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="LotteryRecordDomain对象", description="抽奖记录表")
public class AtomReqLotteryRecordDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "活动类型")
    private String promotionType;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "是否有效的订单抽奖(1:有效;2:无效)")
    private Integer effective;

    @ApiModelProperty(value = "是否有效的埋点数据(1:是;2:否)")
    private Integer trackingFlag;

}
