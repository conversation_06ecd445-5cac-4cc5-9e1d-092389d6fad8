package cn.htdt.marketcenter.dto.request;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;

/**
 * <p>
 * 用户优惠券适用店铺表请求DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@Data
public class AtomReqCouponUserSuitStoreDTO extends ReqComPageDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 用户券编号
     */
    private String userCouponNo;

    /**
     * 优惠券编号
     */
    private String couponNo;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

}
