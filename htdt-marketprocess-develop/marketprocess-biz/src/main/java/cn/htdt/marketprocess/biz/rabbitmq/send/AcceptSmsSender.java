package cn.htdt.marketprocess.biz.rabbitmq.send;

import cn.htdt.marketprocess.biz.rabbitmq.config.DirectDeplayConfig;
import cn.htdt.marketprocess.dto.request.message.ReqMwMsgResultDTO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/16 15:19
 */
@Slf4j
@Component
public class AcceptSmsSender {

    @Resource
    private AmqpTemplate rabbitTemplate;

    public void sendBySmsSend(List<ReqMwMsgResultDTO> reqMwMsgResultDTOList) {
        log.info("------AcceptSmsSender------Sender message：{}", JSON.toJSONString(reqMwMsgResultDTOList));
        this.rabbitTemplate.convertAndSend(DirectDeplayConfig.NORMAL_EXCHANGE_NAME, DirectDeplayConfig.NORMAL_QUEUE_SMS_ACCEPT_ROUTING_KEY, reqMwMsgResultDTOList);
    }
}
