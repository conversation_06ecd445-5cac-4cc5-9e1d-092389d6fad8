package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.ContractStateEnum;
import cn.htdt.common.enums.PaymentChannelEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqSmsRechargeOrderDTO;
import cn.htdt.marketcenter.dto.response.AtomResSmsRechargeOrderDTO;
import cn.htdt.marketprocess.api.analysis.SmsRechargeOrderAnalysisService;
import cn.htdt.marketprocess.biz.constant.ConstantConstant;
import cn.htdt.marketprocess.biz.conversion.SmsRechargeOrderAssert;
import cn.htdt.marketprocess.dto.request.ReqSmsRechargeOrderDTO;
import cn.htdt.marketprocess.dto.response.ResSmsRechargeOrderDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomSmsRechargeOrderAnalysisService;
import cn.htdt.ordercenter.dto.request.AtomReqPayProjectDealsContractDTO;
import cn.htdt.ordercenter.dto.response.AtomResPayProjectDealsContractDTO;
import cn.htdt.orderprocess.api.PayProjectAnalysisService;
import cn.htdt.orderprocess.dto.response.payproject.ResPayProjectDealsContractDTO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021-07-01
 * @description 短信充值订单服务实现
 **/
@Slf4j
@DubboService
public class SmsRechargeOrderAnalysisServiceImpl implements SmsRechargeOrderAnalysisService {

    @Resource
    private AtomSmsRechargeOrderAnalysisService atomSmsRechargeOrderAnalysisService;

    @Resource
    private SmsRechargeOrderAssert smsRechargeOrderAssert;

    @DubboReference
    private PayProjectAnalysisService payProjectAnalysisService;

    /**
     * 综合服务平台 -> 营销管理 -> 短信营销活动 -> 获取短信充值列表，支持分页
     * 超级老板APP -> 会员 -> 短信营销 -> 获取短信充值列表，支持分页
     *
     * @param reqDTO 查询参数
     * @return ExecuteDTO<ExecutePageDTO < ResSmsRechargeOrderDTO>>
     * <AUTHOR>
     * @date 2021-07-01 09:50:58
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResSmsRechargeOrderDTO>> getSmsRechargeOrderListByPage(ReqSmsRechargeOrderDTO reqDTO) {
        log.info("-------SmsRechargeOrderAnalysisServiceImpl-->getSmsRechargeOrderListByPage,分页查询短信充值订单列表--start----");

        //smsRechargeOrderAssert.getSmsRechargeOrderListByPageAssert(reqDTO);

        log.info("-----getSmsRechargeOrderListByPage---->,查询参数:{}", reqDTO);

        //返回结果集
        ExecutePageDTO<ResSmsRechargeOrderDTO> executePageDTO = new ExecutePageDTO<>();

        AtomReqSmsRechargeOrderDTO atomReqSmsRechargeOrderDTO = BeanCopierUtil.copy(reqDTO, AtomReqSmsRechargeOrderDTO.class);

        //分页查询短信充值订单列表
        ExecuteDTO<ExecutePageDTO<AtomResSmsRechargeOrderDTO>> executeDTO = atomSmsRechargeOrderAnalysisService.getSmsRechargeOrderListByPage(atomReqSmsRechargeOrderDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }

        //订单号','拼接
        StringBuilder ordernoSb = new StringBuilder();

        if (executeDTO.getData() != null) {
            List<ResSmsRechargeOrderDTO> resSmsRechargeOrderDTOList = new ArrayList<>();
            for (AtomResSmsRechargeOrderDTO atomResSmsRechargeOrderDTO : executeDTO.getData().getRows()) {
                ResSmsRechargeOrderDTO resSmsRechargeOrderDTO = BeanCopierUtil.copy(atomResSmsRechargeOrderDTO, ResSmsRechargeOrderDTO.class);
                PaymentChannelEnum paymentChannelEnum = PaymentChannelEnum.getByCode(atomResSmsRechargeOrderDTO.getPaymentChannelDetail());
                if (paymentChannelEnum != null) {
                    resSmsRechargeOrderDTO.setPaymentChannelDetailName(paymentChannelEnum.getName());
                }
                resSmsRechargeOrderDTOList.add(resSmsRechargeOrderDTO);

                //如果是线下签约,不拼接订单哈,不需要查询线上合同
                if (!ContractStateEnum.OFFLINE.getCode().equals(atomResSmsRechargeOrderDTO.getContractState())){
                    if (NumConstant.ZERO != ordernoSb.length()){
                        ordernoSb.append(ConstantConstant.COMMA);
                    }
                    //获取短信订单号
                    ordernoSb.append(atomResSmsRechargeOrderDTO.getOrderNo());
                }
            }
            //订单号不为空
            if (ordernoSb.length() > 0){
                List<AtomResPayProjectDealsContractDTO> resPayProjectDealsContractDTOS;

                //根据订单号批量查询合同列表
                //查询回调成功的合同列表
                AtomReqPayProjectDealsContractDTO reqPayProjectDealsContractDTO = new AtomReqPayProjectDealsContractDTO();
                reqPayProjectDealsContractDTO.setDealsNo(ordernoSb.toString());
                //查询返回成功的合同地址
                reqPayProjectDealsContractDTO.setIsNullContractUrl(NumConstant.ONE);
                ExecuteDTO<List<AtomResPayProjectDealsContractDTO>> payProjectDealsDealsContract = payProjectAnalysisService.getPayProjectDealsDealsContract(reqPayProjectDealsContractDTO);
                log.info("**SmsRechargeOrderAnalysisServiceImpl-getSmsRechargeOrderListByPage-返回值,{}",JSON.toJSONString(payProjectDealsDealsContract));
                if (payProjectDealsDealsContract.successFlag() && CollectionUtils.isNotEmpty(payProjectDealsDealsContract.getData())) {
                    for (AtomResPayProjectDealsContractDTO datum : payProjectDealsDealsContract.getData()) {
                        for (ResSmsRechargeOrderDTO resSmsRechargeOrderDTO : resSmsRechargeOrderDTOList) {
                            if (StringUtils.equals(datum.getDealsNo(),resSmsRechargeOrderDTO.getOrderNo())){
                                if (CollectionUtils.isEmpty(resSmsRechargeOrderDTO.getResPayProjectDealsContractDTOS())){
                                    resPayProjectDealsContractDTOS = new ArrayList<>();
                                    resPayProjectDealsContractDTOS.add(datum);
                                    resSmsRechargeOrderDTO.setResPayProjectDealsContractDTOS(resPayProjectDealsContractDTOS);
                                } else {
                                    resSmsRechargeOrderDTO.getResPayProjectDealsContractDTOS().add(datum);
                                }
                            }
                        }
                    }
                }
            }


            executePageDTO.setTotal(executeDTO.getData().getTotal());
            executePageDTO.setRows(resSmsRechargeOrderDTOList);
        }

        log.info("-------SmsRechargeOrderAnalysisServiceImpl-->getSmsRechargeOrderListByPage,分页查询短信充值订单列表----");
        return ExecuteDTO.success(executePageDTO);
    }

    @Override
    public ExecuteDTO<List<ResSmsRechargeOrderDTO>> getSmsRechargeOrderMonthCount(ReqSmsRechargeOrderDTO reqSmsRechargeOrderDTO) {
        AtomReqSmsRechargeOrderDTO atomReqSmsRechargeOrderDTO = BeanCopierUtil.copy(reqSmsRechargeOrderDTO, AtomReqSmsRechargeOrderDTO.class);
        ExecuteDTO<List<AtomResSmsRechargeOrderDTO>> executeDTO = atomSmsRechargeOrderAnalysisService.getSmsRechargeOrderMonthCount(atomReqSmsRechargeOrderDTO);
        List<ResSmsRechargeOrderDTO> resSmsRechargeOrderDTOList = BeanCopierUtil.copyList(executeDTO.getData(), ResSmsRechargeOrderDTO.class);
        return ExecuteDTO.success(resSmsRechargeOrderDTOList);
    }

}
