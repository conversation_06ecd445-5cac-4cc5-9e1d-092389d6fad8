package cn.htdt.marketprocess.biz.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.enums.OrderPaymentStatusEnum;
import cn.htdt.common.enums.SmsAdjustTypeEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.market.SmsRechargeOrderTypeEnum;
import cn.htdt.common.enums.market.SmsTrafficChangeSourceEnum;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqSmsTrafficDTO;
import cn.htdt.marketcenter.dto.request.AtomSmsRechargeAdjustRecordDTO;
import cn.htdt.marketprocess.api.operat.SmsRechargeAdjustRecordOperatService;
import cn.htdt.marketprocess.biz.utils.SmsUtil;
import cn.htdt.marketprocess.domain.SmsRechargeOrderDomain;
import cn.htdt.marketprocess.dto.request.ReqSmsRechargeAdjustRecordEditDTO;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomSmsRechargeAdjustRecordOperatService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomSmsRechargeOrderOperatService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomSmsTrafficOperatService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * 短信条数调整记录service 实现类
 *
 * <AUTHOR>
 * @since 2022-09-19
 */
@Slf4j
@DubboService
public class SmsRechargeAdjustRecordOperatServiceImpl implements SmsRechargeAdjustRecordOperatService {

    @Resource
    private AtomSmsTrafficOperatService atomSmsTrafficOperatService;

    @Resource
    private AtomSmsRechargeAdjustRecordOperatService atomSmsRechargeAdjustRecordOperatService;

    @Resource
    private AtomSmsRechargeOrderOperatService atomSmsRechargeOrderOperatService;

    @Resource
    private SmsUtil smsUtil;

    @Override
    public ExecuteDTO addSmsRechargeAdjustRecord(ReqSmsRechargeAdjustRecordEditDTO atomReqDTO) {
        log.info("-------SmsRechargeAdjustRecordOperatServiceImpl-->addSmsRechargeAdjustRecord, 保存短信条数修改的操作记录--start----");

        log.info("SmsRechargeAdjustRecordOperatServiceImpl-->addSmsRechargeAdjustRecord, atomReqDTO: {}", atomReqDTO);
        AtomReqSmsTrafficDTO atomReqSmsTrafficDTO = BeanCopierUtil.copy(atomReqDTO, AtomReqSmsTrafficDTO.class);

        log.info("SmsRechargeAdjustRecordOperatServiceImpl-->addSmsRechargeAdjustRecord, atomReqSmsTrafficDTO: {}", atomReqSmsTrafficDTO);

        // 先更新数据
        int updateCount = atomSmsTrafficOperatService.modifySmsRemainingNum(atomReqSmsTrafficDTO);
        // 数据可能不存在，更新失败，则保存
        if (updateCount <= 0) {
            log.info("-------SmsRechargeAdjustRecordOperatServiceImpl-->addSmsRechargeAdjustRecord, update store smsInfo fail....");
            ExecuteDTO executeDTO = atomSmsTrafficOperatService.addSmsTraffic(atomReqSmsTrafficDTO);
            if (!executeDTO.successFlag()) {
                log.info("-------SmsRechargeAdjustRecordOperatServiceImpl-->addSmsRechargeAdjustRecord, save store smsInfo fail....");
                return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
            }
        }
        AtomSmsRechargeAdjustRecordDTO atomSmsRechargeAdjustRecordDTO = BeanCopierUtil.copy(atomReqDTO, AtomSmsRechargeAdjustRecordDTO.class);
        // 再保存短信调整数量的记录
        atomSmsRechargeAdjustRecordOperatService.addSmsRechargeAdjustRecord(atomSmsRechargeAdjustRecordDTO);

        // 生成充值记录
        SmsRechargeOrderDomain smsRechargeOrderDomain = BeanCopierUtil.copy(atomReqDTO, SmsRechargeOrderDomain.class);
        smsRechargeOrderDomain.setOrderType(SmsRechargeOrderTypeEnum.PLATFORM.getCode());
        smsRechargeOrderDomain.setChangeSource(SmsTrafficChangeSourceEnum.PLATFORM_RECHARGE.getCode());
        smsRechargeOrderDomain.setOrderPaymentStatus(OrderPaymentStatusEnum.PAID.getCode());
        //短信合同状态
        smsRechargeOrderDomain.setContractState(atomReqDTO.getContractState());

        if (SmsAdjustTypeEnum.SMS_ADJUST_TYPE_ADD.getCode().equals(atomReqDTO.getSmsAdjustType())) {
            smsRechargeOrderDomain.setRechargeNum(atomReqDTO.getSmsAdjustNum().intValue());
        } else if (SmsAdjustTypeEnum.SMS_ADJUST_TYPE_MINUS.getCode().equals(atomReqDTO.getSmsAdjustType())) {
            // 如果为减少的话，充值条数设置为负数
            smsRechargeOrderDomain.setRechargeNum(NumConstant.ZERO - atomReqDTO.getSmsAdjustNum().intValue());
        }

        // 保存数据
        atomSmsRechargeOrderOperatService.addPlatformSmsRechargeOrder(smsRechargeOrderDomain);

        if (SmsAdjustTypeEnum.SMS_ADJUST_TYPE_ADD.getCode().equals(atomReqDTO.getSmsAdjustType())) {
            // 增加缓存里的店铺短信条数数据
            long redisIncreaseSmsBalance = smsUtil.redisIncreaseSmsBalance(atomReqDTO.getStoreNo(), atomReqDTO.getSmsAdjustNum().intValue());
            log.info("SmsRechargeAdjustRecordOperatServiceImpl-->addSmsRechargeAdjustRecord, redisIncreaseSmsBalance: {}", redisIncreaseSmsBalance);
        } else if (SmsAdjustTypeEnum.SMS_ADJUST_TYPE_MINUS.getCode().equals(atomReqDTO.getSmsAdjustType())) {
            // 减少缓存里的店铺短信条数数据
            ExecuteDTO<Integer> redisDeductSmsBalance = smsUtil.redisDeductSmsBalance(atomReqDTO.getStoreNo(), atomReqDTO.getSmsAdjustNum().intValue());
            log.info("SmsRechargeAdjustRecordOperatServiceImpl-->addSmsRechargeAdjustRecord, redisDeductSmsBalance: {}", redisDeductSmsBalance);
        }

        log.info("-------SmsRechargeAdjustRecordOperatServiceImpl-->addSmsRechargeAdjustRecord, 保存短信条数修改的操作记录--end----");
        return ExecuteDTO.success();
    }
}
