package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.enums.BossLogoffErrorEnum;
import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.enums.MarketErrorCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.goods.SourceTypeEnum;
import cn.htdt.common.enums.market.*;
import cn.htdt.common.redis.utils.RedisUtil;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.BigDecimalUtil;
import cn.htdt.goodsprocess.api.analysis.CloudPoolApplyAnalysisService;
import cn.htdt.goodsprocess.api.analysis.GoodsAnalysisService;
import cn.htdt.goodsprocess.dto.request.cloudpool.ReqCloudPoolApplyComPageDTO;
import cn.htdt.goodsprocess.dto.request.goods.ReqGoodsDTO;
import cn.htdt.goodsprocess.dto.response.goods.ResGoodsDTO;
import cn.htdt.marketcenter.dto.request.AtomReqGoodsPromotionGoodsRelationDTO;
import cn.htdt.marketcenter.dto.request.AtomReqLotteryRuleDTO;
import cn.htdt.marketcenter.dto.request.AtomReqPromotionInfoDTO;
import cn.htdt.marketcenter.dto.response.AtomResGoodsPromotionGoodsRelationDTO;
import cn.htdt.marketcenter.dto.response.AtomResLotteryRuleDTO;
import cn.htdt.marketcenter.dto.response.AtomResPromotionInfoDTO;
import cn.htdt.marketprocess.api.analysis.PreDeterminedCheckService;
import cn.htdt.marketprocess.api.analysis.PromotionInfoAnalysisService;
import cn.htdt.marketprocess.biz.constant.CommonConstant;
import cn.htdt.marketprocess.biz.constant.KeysConstant;
import cn.htdt.marketprocess.biz.conversion.PromotionInfoAssert;
import cn.htdt.marketprocess.biz.utils.LotteryDrawUtil;
import cn.htdt.marketprocess.biz.utils.UserInfoUtil;
import cn.htdt.marketprocess.dao.CouponSettingDao;
import cn.htdt.marketprocess.dao.LotteryRuleDao;
import cn.htdt.marketprocess.dao.PromotionInfoDao;
import cn.htdt.marketprocess.dao.PromotionStoreRelationDao;
import cn.htdt.marketprocess.domain.PromotionInfoDomain;
import cn.htdt.marketprocess.dto.request.ReqLotteryDrawTimesDTO;
import cn.htdt.marketprocess.dto.request.ReqOrderPromotionInfoDTO;
import cn.htdt.marketprocess.dto.request.ReqPromotionInfoDTO;
import cn.htdt.marketprocess.dto.request.ReqUsablePromotionInfoDTO;
import cn.htdt.marketprocess.dto.response.ResLotteryDrawTimesDTO;
import cn.htdt.marketprocess.dto.response.ResMarketingActivitiesCountDTO;
import cn.htdt.marketprocess.dto.response.ResMarketingActivitiesInfoDTO;
import cn.htdt.marketprocess.dto.response.ResOrderPromotionInfoDTO;
import cn.htdt.marketprocess.dto.response.ResPromotionInfoDTO;
import cn.htdt.marketprocess.dto.response.ResUsablePromotionInfoDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomGoodsPromotionGoodsRelationAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomLotteryDrawRecordAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomLotteryRuleAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomPromotionInfoAnalysisService;
import cn.htdt.marketprocess.vo.AtomCouponSettingVo;
import cn.htdt.marketprocess.vo.AtomLotteryRuleVo;
import cn.htdt.marketprocess.vo.PromotionInfoVO;
import cn.htdt.marketprocess.vo.PromotionStoreRelationVo;
import cn.htdt.ordercenter.dto.request.AtomReqSoDTO;
import cn.htdt.ordercenter.dto.request.AtomReqSoReturnDTO;
import cn.htdt.orderprocess.api.LegacyOrderCenterService;
import cn.htdt.orderprocess.api.OrderPromotionAnalysisService;
import cn.htdt.orderprocess.dto.response.ResSoPromotionItemDTO;
import cn.htdt.userprocess.dto.response.MerchantInfoResponseT;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 活动查询服务
 *
 * <AUTHOR>
 */
@DubboService
@Slf4j
public class PromotionInfoAnalysisServiceImpl implements PromotionInfoAnalysisService {

    @Resource
    private PromotionInfoAssert promotionInfoAssert;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private LotteryDrawUtil lotteryDrawUtil;

    @Autowired
    PreDeterminedCheckService preDeterminedCheckService;

    @Resource
    private AtomPromotionInfoAnalysisService atomPromotionInfoAnalysisService;

    @Resource
    private AtomLotteryRuleAnalysisService atomLotteryRuleAnalysisService;

    @Resource
    private AtomLotteryDrawRecordAnalysisService atomLotteryDrawRecordAnalysisService;

    @DubboReference
    private LegacyOrderCenterService legacyOrderCenterService;

    @DubboReference
    private OrderPromotionAnalysisService orderPromotionAnalysisService;

    @DubboReference
    private CloudPoolApplyAnalysisService cloudPoolApplyAnalysisService;

    @Resource
    private AtomGoodsPromotionGoodsRelationAnalysisService goodsRelationAnalysisService;

    @DubboReference
    private GoodsAnalysisService goodsAnalysisService;

    @Autowired
    private PromotionInfoDao promotionInfoDao;

    @Autowired
    private CouponSettingDao couponSettingDao;

    @Resource
    private LotteryRuleDao lotteryRuleDao;

    @Resource
    private UserInfoUtil userInfoUtil;

    @Resource
    private PromotionStoreRelationDao promotionStoreRelationDao;

    @Override
    public ExecuteDTO<ResPromotionInfoDTO> getAllPromotionInfo(ReqPromotionInfoDTO reqPromotionInfoDTO) {
        // 获取活动信息
        AtomReqPromotionInfoDTO atomReqPromotionInfoDTO = BeanCopierUtil.copy(reqPromotionInfoDTO, AtomReqPromotionInfoDTO.class);
        ExecuteDTO<AtomResPromotionInfoDTO> executeDTO = atomPromotionInfoAnalysisService.getPromotionInfo(atomReqPromotionInfoDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        // 活动不存在
        if (null == executeDTO.getData()) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000100);
        }
        return ExecuteDTO.success(BeanCopierUtil.copy(executeDTO.getData(), ResPromotionInfoDTO.class));
    }

    /**
     * 获取活动创建数量
     * @param reqPromotionInfoDTO
     * @return
     */
    @Override
    public ExecuteDTO<Integer> getPromotionInfoCount(ReqPromotionInfoDTO reqPromotionInfoDTO){
        PromotionInfoDomain domain = BeanCopierUtil.copy(reqPromotionInfoDTO, PromotionInfoDomain.class);
        //查询促销活动创建数量
        Integer count = promotionInfoDao.selectPromotionInfoCount(domain);
        return ExecuteDTO.success(count);
    }

    @Override
    public ExecuteDTO<ExecutePageDTO<ResPromotionInfoDTO>> getPromotionInfoPage(ReqPromotionInfoDTO reqPromotionInfoDTO) {
        log.info("MarketProcess-PromotionInfoAnalysisServiceImpl-getPromotionInfoPage-params-start ");
        log.info("MarketProcess-PromotionInfoAnalysisServiceImpl-getPromotionInfoPage-params={}", JSON.toJSONString(reqPromotionInfoDTO));
        AtomReqPromotionInfoDTO atomReqPromotionInfoDTO = BeanCopierUtil.copy(reqPromotionInfoDTO, AtomReqPromotionInfoDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResPromotionInfoDTO>> executeDTO = atomPromotionInfoAnalysisService.getPromotionInfoPage(atomReqPromotionInfoDTO);
        log.info("MarketProcess-PromotionInfoAnalysisServiceImpl-getPromotionInfoPage-return={}", JSON.toJSONString(executeDTO));
        log.info("MarketProcess-PromotionInfoAnalysisServiceImpl-getPromotionInfoPage-end");
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        //参数转换
        ExecutePageDTO<ResPromotionInfoDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResPromotionInfoDTO> resPromotionInfoDTOS = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResPromotionInfoDTO.class);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        executePageDTO.setRows(resPromotionInfoDTOS);
        return ExecuteDTO.success(executePageDTO);
    }

    @Override
    public ExecuteDTO<ExecutePageDTO<ResPromotionInfoDTO>> getPromotionNumPage(ReqPromotionInfoDTO reqPromotionInfoDTO) {
        log.info("MarketProcess-PromotionInfoAnalysisServiceImpl-getPromotionNumPage-params-start");
        log.info("MarketProcess-PromotionInfoAnalysisServiceImpl-getPromotionNumPage-params={}", JSON.toJSONString(reqPromotionInfoDTO));
        AtomReqPromotionInfoDTO atomReqPromotionInfoDTO = BeanCopierUtil.copy(reqPromotionInfoDTO, AtomReqPromotionInfoDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResPromotionInfoDTO>> executeDTO = atomPromotionInfoAnalysisService.selectPromotionNum(atomReqPromotionInfoDTO);
        log.info("MarketProcess-PromotionInfoAnalysisServiceImpl-getPromotionNumPage-return={}", JSON.toJSONString(executeDTO));
        log.info("MarketProcess-PromotionInfoAnalysisServiceImpl-getPromotionNumPage-end");
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        //设置活动类型名称
        List<AtomResPromotionInfoDTO> list = setPromotionTypeName(executeDTO);
        Iterator iterator = list.iterator();
        while (iterator.hasNext()) {
            AtomResPromotionInfoDTO atomResPromotionInfoDTO =(AtomResPromotionInfoDTO)iterator.next();
            //去掉数量为0的对象
            if(atomResPromotionInfoDTO.getPromotionNum()<=0){
                iterator.remove();
            }
        }

        //参数转换
        ExecutePageDTO<ResPromotionInfoDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResPromotionInfoDTO> resPromotionInfoDTOS = BeanCopierUtil.copyList(list, ResPromotionInfoDTO.class);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        executePageDTO.setRows(resPromotionInfoDTOS);
        return ExecuteDTO.success(executePageDTO);
    }


    @Override
    public ExecuteDTO<ExecutePageDTO<ResPromotionInfoDTO>> getPromotionInfoForDecorate(ReqPromotionInfoDTO reqPromotionInfoDTO) {
        log.info("MarketProcess-PromotionInfoAnalysisServiceImpl-getPromotionInfoForDecorate-params-start");
        log.info("MarketProcess-PromotionInfoAnalysisServiceImpl-getPromotionInfoForDecorate-params={}", JSON.toJSONString(reqPromotionInfoDTO));
        AtomReqPromotionInfoDTO atomReqPromotionInfoDTO = BeanCopierUtil.copy(reqPromotionInfoDTO, AtomReqPromotionInfoDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResPromotionInfoDTO>> executeDTO = atomPromotionInfoAnalysisService.getPromotionInfoForDecorate(atomReqPromotionInfoDTO);
        log.info("MarketProcess-PromotionInfoAnalysisServiceImpl-getPromotionInfoForDecorate-return={}", JSON.toJSONString(executeDTO));
        log.info("MarketProcess-PromotionInfoAnalysisServiceImpl-getPromotionInfoForDecorate-end");
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        List<AtomResPromotionInfoDTO> list = setPromotionTypeName(executeDTO);
        //参数转换
        ExecutePageDTO<ResPromotionInfoDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResPromotionInfoDTO> resPromotionInfoDTOS = BeanCopierUtil.copyList(list, ResPromotionInfoDTO.class);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        executePageDTO.setRows(resPromotionInfoDTOS);
        return ExecuteDTO.success(executePageDTO);
    }

    private List<AtomResPromotionInfoDTO> setPromotionTypeName(ExecuteDTO<ExecutePageDTO<AtomResPromotionInfoDTO>> executeDTO) {
        //设置活动类型名称
        List<AtomResPromotionInfoDTO> list = executeDTO.getData().getRows();
        if (CollectionUtils.isNotEmpty(list)) {
            for (AtomResPromotionInfoDTO atomResPromotionInfoDTO : list) {
                if (PromotionTypeEnum.BRANCHES_DRAW.getCode().equals(atomResPromotionInfoDTO.getPromotionType())) {
                    atomResPromotionInfoDTO.setPromotionTypeName(PromotionTypeEnum.BRANCHES_DRAW.getType());
                } else if (StringUtils.isNotEmpty(atomResPromotionInfoDTO.getPromotionType())){
                    atomResPromotionInfoDTO.setPromotionTypeName(PromotionTypeEnum.getByCode(atomResPromotionInfoDTO.getPromotionType()).getType());
                }
            }
        }
        return list;
    }


    @Override
    public ExecuteDTO<List<ResPromotionInfoDTO>> getPromotionInfoListLimitFive(ReqPromotionInfoDTO reqPromotionInfoDTO) {
        log.info("MarketProcess-PromotionInfoAnalysisServiceImpl-getPromotionInfoListLimitFive-params-start");
        log.info("MarketProcess-PromotionInfoAnalysisServiceImpl-getPromotionInfoListLimitFive-params={}", JSON.toJSONString(reqPromotionInfoDTO));
        if (StringUtils.isEmpty(reqPromotionInfoDTO.getStoreNo()) && CollectionUtils.isEmpty(reqPromotionInfoDTO.getStoreNoList())) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000624);
        }
        // 用到delete_flag=1、up_down_flag=2、活动时间未结束、5条
        reqPromotionInfoDTO.setUpDownFlag(NumConstant.TWO);
        AtomReqPromotionInfoDTO atomReqPromotionInfoDTO = BeanCopierUtil.copy(reqPromotionInfoDTO, AtomReqPromotionInfoDTO.class);
        ExecuteDTO<List<AtomResPromotionInfoDTO>> executeDTO = atomPromotionInfoAnalysisService.getPromotionTypeList(atomReqPromotionInfoDTO);
        List<ResPromotionInfoDTO> resPromotionInfoDTO = BeanCopierUtil.copyList(executeDTO.getData(), ResPromotionInfoDTO.class);
        log.info("MarketProcess-PromotionInfoAnalysisServiceImpl-getPromotionInfoListLimitFive-end");
        return ExecuteDTO.success(resPromotionInfoDTO);
    }

    @Override
    public ExecuteDTO<List<ResPromotionInfoDTO>> listLotteryOnline(ReqPromotionInfoDTO reqPromotionInfoDTO) {
        log.info("listLotteryOnline--->入参: {}", JSON.toJSONString(reqPromotionInfoDTO));

        // 目前汇享购查询活动详情, 会使用这个字段
        if (WhetherEnum.YES.getCode().equals(reqPromotionInfoDTO.getQueryMerchantUpDownFlag())
                && StringUtils.isNotBlank(reqPromotionInfoDTO.getPromotionNo())) {
            PromotionInfoDomain queryPromotionInfoDomain = new PromotionInfoDomain();
            queryPromotionInfoDomain.setPromotionNo(reqPromotionInfoDTO.getPromotionNo());
            PromotionInfoDomain promotionInfoDetail = promotionInfoDao.selectPromotionInfo(queryPromotionInfoDomain);
            // 如果为商家拆盲盒活动
            if (null != promotionInfoDetail
                    && SourceTypeEnum.SOURCE_TYPE_1002.getCode().equals(promotionInfoDetail.getSourceType())
                    && PromotionTypeEnum.BLINDBOX_DRAW.getCode().equals(promotionInfoDetail.getPromotionType())) {
                AtomLotteryRuleVo atomLotteryRuleVo = BeanCopierUtil.copy(reqPromotionInfoDTO, AtomLotteryRuleVo.class);
                log.info("listLotteryOnline--->selectMerchantLotteryRuleDetail, 查询入参: {}", JSON.toJSONString(atomLotteryRuleVo));
                List<AtomLotteryRuleVo> atomResLotteryRuleVOList = lotteryRuleDao.selectMerchantLotteryRuleDetail(atomLotteryRuleVo);
                log.info("listLotteryOnline--->selectMerchantLotteryRuleDetail, 查询出参: {}", JSON.toJSONString(atomResLotteryRuleVOList));
                return ExecuteDTO.ok(BeanCopierUtil.copyList(atomResLotteryRuleVOList, ResPromotionInfoDTO.class));
            }
        }

        ExecuteDTO<List<AtomResLotteryRuleDTO>> executeDTO = atomLotteryRuleAnalysisService.listLotteryInfo((BeanCopierUtil.copy(reqPromotionInfoDTO, AtomReqLotteryRuleDTO.class)));
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        return ExecuteDTO.success(Lists.newArrayList(BeanCopierUtil.copyList(executeDTO.getData(), ResPromotionInfoDTO.class)));
    }

    @Override
    public ExecuteDTO<ResPromotionInfoDTO> getPromotionInfo(ReqPromotionInfoDTO reqPromotionInfoDTO) {
        // 获取活动详情
        ExecuteDTO<AtomResPromotionInfoDTO> executeDTO = atomLotteryRuleAnalysisService.getLotteryInfo(BeanCopierUtil.copy(reqPromotionInfoDTO, AtomReqLotteryRuleDTO.class));
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        // 活动不存在
        if (null == executeDTO.getData()) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000100);
        }
        return ExecuteDTO.success(BeanCopierUtil.copy(executeDTO.getData(), ResPromotionInfoDTO.class));
    }

    @Override
    public
    ExecuteDTO<ResLotteryDrawTimesDTO> getDrawTimes(ReqLotteryDrawTimesDTO reqLotteryDrawTimesDTO) {
        log.info("getDrawTimes--->入参: {}", JSON.toJSONString(reqLotteryDrawTimesDTO));
        // 参数校验
        promotionInfoAssert.getDrawTimesAssert(reqLotteryDrawTimesDTO);

        // 查询活动信息
        ExecuteDTO<List<AtomResLotteryRuleDTO>> executeDTO = atomLotteryRuleAnalysisService.listLotteryInfo(BeanCopierUtil.copy(reqLotteryDrawTimesDTO, AtomReqLotteryRuleDTO.class));
        if (!executeDTO.successFlag() || CollectionUtils.isEmpty(executeDTO.getData())) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000100);
        }
        return lotteryDrawUtil.getDrawTimes(reqLotteryDrawTimesDTO.getFanNo(), reqLotteryDrawTimesDTO.getOrderNo(), reqLotteryDrawTimesDTO.getStoreNo(), executeDTO.getData().get(0));
    }

    @Override
    public ExecuteDTO<List<ResUsablePromotionInfoDTO>> listUsablePromotionInfo(ReqUsablePromotionInfoDTO reqUsablePromotionInfoDTO) {
        log.info("listUsablePromotionInfo--->请求入参: {}", JSON.toJSONString(reqUsablePromotionInfoDTO));

        AtomReqLotteryRuleDTO atomReqLotteryRuleDTO = BeanCopierUtil.copy(reqUsablePromotionInfoDTO, AtomReqLotteryRuleDTO.class);
        // 获取上架活动
        atomReqLotteryRuleDTO.setUpDownFlag(WhetherEnum.YES.getCode());
        // 只获取hxg链接 排除掉专属链接
        atomReqLotteryRuleDTO.setLotteryShowLocation(LotteryShowLocationEnum.HXG_PROMOTION.getCode());
        // 查询当前有效期内的活动(不含已过期的活动)
        atomReqLotteryRuleDTO.setInvalidTime(LocalDateTime.now());
        ExecuteDTO<List<AtomResLotteryRuleDTO>> executeDTO;

        List<AtomResLotteryRuleDTO> atomResLotteryRuleDTOList;

        // 如果为查询商家创建的活动
        if (SourceTypeEnum.SOURCE_TYPE_1002.getCode().equals(atomReqLotteryRuleDTO.getSourceType())) {
            log.info("listUsablePromotionInfo--->query merchant lottery promotion");
            AtomLotteryRuleVo atomLotteryRuleVo = BeanCopierUtil.copy(atomReqLotteryRuleDTO, AtomLotteryRuleVo.class);
            if (StringUtils.isBlank(atomLotteryRuleVo.getMerchantNo())) {
                MerchantInfoResponseT merchantInfo = userInfoUtil.getMerchantInfo(reqUsablePromotionInfoDTO.getStoreNo());
                atomLotteryRuleVo.setMerchantNo(merchantInfo.getMerchantNo());
            }
            log.info("listUsablePromotionInfo--->selectMerchantLotteryRuleList, 入参: {}", JSON.toJSONString(atomLotteryRuleVo));
            List<AtomLotteryRuleVo> merchantLotteryRuleList = lotteryRuleDao.selectMerchantLotteryRuleList(atomLotteryRuleVo);
            log.info("listUsablePromotionInfo--->selectMerchantLotteryRuleList, 出参: {}", JSON.toJSONString(merchantLotteryRuleList));

            // 如果不存在活动信息 则返回空list
            if (CollectionUtils.isEmpty(merchantLotteryRuleList)) {
                return ExecuteDTO.ok(Collections.emptyList());
            }
            atomResLotteryRuleDTOList = BeanCopierUtil.copyList(merchantLotteryRuleList, AtomResLotteryRuleDTO.class);
        } else {
            executeDTO = atomLotteryRuleAnalysisService.listLotteryInfo(atomReqLotteryRuleDTO);
            if (!executeDTO.successFlag()) {
                return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
            }

            // 如果不存在活动信息 则返回空list
            if (CollectionUtils.isEmpty(executeDTO.getData())) {
                return ExecuteDTO.ok(Collections.emptyList());
            }

            atomResLotteryRuleDTOList = executeDTO.getData();
        }

        List<ResUsablePromotionInfoDTO> resUsablePromotionInfoDTOList = new ArrayList<>();
        atomResLotteryRuleDTOList.forEach(atomResLotteryRuleDTO -> {
            // 出参返回
            ResUsablePromotionInfoDTO resUsablePromotionInfoDTO = BeanCopierUtil.copy(atomResLotteryRuleDTO, ResUsablePromotionInfoDTO.class);
            // 初始化抽奖次数
            ExecuteDTO<ResLotteryDrawTimesDTO> resLotteryDrawTimesDTO = lotteryDrawUtil.getDrawTimes(reqUsablePromotionInfoDTO.getFanNo(), reqUsablePromotionInfoDTO.getOrderNo(), reqUsablePromotionInfoDTO.getStoreNo(), atomResLotteryRuleDTO);

            // 如果是订单抽奖
            if (LotteryTypeEnum.LOTTERY_TYPE_TWO.getCode().equals(reqUsablePromotionInfoDTO.getLotteryType())) {

                // 判断是否限制新老用户
                ExecuteDTO<Boolean> checkNewFansExecuteDTO = preDeterminedCheckService.checkNewFans(reqUsablePromotionInfoDTO.getFanNo(), reqUsablePromotionInfoDTO.getStoreNo(), "", atomResLotteryRuleDTO.getSourceType());
                if (!checkNewFansExecuteDTO.successFlag()) {
                    return;
                }
                if ((checkNewFansExecuteDTO.getData() && JoinUserScopeEnum.OLD_FANS.getCode().equals(atomResLotteryRuleDTO.getUserScope()))
                        || (!checkNewFansExecuteDTO.getData() && JoinUserScopeEnum.NEW_FANS.getCode().equals(atomResLotteryRuleDTO.getUserScope()))) {
                    return;
                }
                // 如果活动未开始的话 订单抽奖也不参与
                if (LocalDateTime.now().isBefore(atomResLotteryRuleDTO.getEffectiveTime())) {
                    return;
                }

                // 查询订单信息
                AtomReqSoDTO atomReqSoDTO = new AtomReqSoDTO();
                atomReqSoDTO.setOrderNo(reqUsablePromotionInfoDTO.getOrderNo());

                // 组装缓存key值
                String redisKey;
                // 如果是平台抽奖key:promotionNo_fanNo
                // 蛋品, 商家抽奖, 目前只有拆盲盒, 也用平台的Key
                if (SourceTypeEnum.SOURCE_TYPE_1001.getCode().equals(atomResLotteryRuleDTO.getSourceType())
                        || SourceTypeEnum.SOURCE_TYPE_1002.getCode().equals(atomResLotteryRuleDTO.getSourceType())) {
                    redisKey = String.format(KeysConstant.FAN_PROMOTION_PLATFORM_PREFIX, atomResLotteryRuleDTO.getPromotionNo(), reqUsablePromotionInfoDTO.getFanNo());
                } else {
                    log.info("listUsablePromotionInfo--->get redisKey, promotionNo: {}, sourceType: {}, storeNo: {}, fanNo: {}",
                                    atomResLotteryRuleDTO.getSourceType(), atomResLotteryRuleDTO.getPromotionNo(),
                                    reqUsablePromotionInfoDTO.getStoreNo(), reqUsablePromotionInfoDTO.getFanNo());
                    redisKey = String.format(KeysConstant.FAN_PROMOTION_PREFIX, atomResLotteryRuleDTO.getPromotionNo(), reqUsablePromotionInfoDTO.getStoreNo(), reqUsablePromotionInfoDTO.getFanNo());
                }
                // 需要累加
                if (WhetherEnum.YES.getCode().equals(reqUsablePromotionInfoDTO.getCalculateFlag())) {
                    // 抽奖规则 单笔订单在线支付金额（不含优惠金额）每满*元可获得*次抽奖机会
                    // int drawTimes = Optional.ofNullable(reqUsablePromotionInfoDTO.getRealAmount()).orElse(BigDecimal.ZERO).divideToIntegralValue(atomResLotteryRuleDTO.getOrderAmount()).intValue() * atomResLotteryRuleDTO.getOrderNum();
                    // 订单满额就可获得一次抽奖
                    int drawTimes = Optional.ofNullable(reqUsablePromotionInfoDTO.getRealAmount()).orElse(BigDecimal.ZERO).compareTo(atomResLotteryRuleDTO.getOrderAmount()) < 0 ? NumConstant.ZERO : NumConstant.ONE;
                    // 判断当前活动每日抽奖次数上限>当日订单抽奖获取的总次数 或者 活动期间剩余总抽奖次数为0 则不允许抽奖
/*                    Integer dailyDrawTimes = Integer.valueOf((String) redisUtil.hget(redisKey, KeysConstant.DAILY_DRAW_GET_TIMES));
                    Integer totalDrawTimes = Integer.valueOf((String) redisUtil.hget(redisKey, KeysConstant.TOTAL_LAST_DRAW_TIMES));
                    // 每日剩余次数 > 剩余总抽奖次数则每日剩余次数为剩余总抽奖次数
                    Integer lastDrawTimes = atomResLotteryRuleDTO.getUserDailyDrawTimes() -
                            dailyDrawTimes > totalDrawTimes ? totalDrawTimes : atomResLotteryRuleDTO.getUserDailyDrawTimes() - dailyDrawTimes;
                    // 如果每日剩余次数小于等于0 或者 总抽奖次数小于等于0 或者总抽奖次数减去每日抽奖次数小于等于0 则均不参加抽奖
                    if (lastDrawTimes <= NumConstant.ZERO || totalDrawTimes <= NumConstant.ZERO || totalDrawTimes - dailyDrawTimes <= NumConstant.ZERO) {
                        return;
                    }
                    // 判断极限值 如果剩余次数>本次发放次数 则直接将本次发放次数累加 否则取当前剩余次数发放
                    int culTimes = atomResLotteryRuleDTO.getUserDailyDrawTimes() - dailyDrawTimes > drawTimes ?
                            drawTimes : atomResLotteryRuleDTO.getUserDailyDrawTimes() - dailyDrawTimes;*/
                    if (drawTimes > NumConstant.ZERO) {
                        // 累加抽奖次数
                        redisUtil.hincr(redisKey, KeysConstant.CURRENT_LAST_DRAW_TIMES, drawTimes);
                        // 每日获取的抽奖次数
                        redisUtil.hincr(redisKey, KeysConstant.DAILY_DRAW_GET_TIMES, drawTimes);
                        // 缓存该订单的中奖次数
                        redisUtil.hset(redisKey, reqUsablePromotionInfoDTO.getOrderNo(), String.valueOf(drawTimes));

                        Object orderNosObject = redisUtil.hget(redisKey, KeysConstant.DAILY_DRAW_ORDER_NOS);
                        if (orderNosObject != null) {
                            // 重新赋值当前orderNos
                            redisUtil.hset(redisKey, KeysConstant.DAILY_DRAW_ORDER_NOS, new StringBuilder().append(orderNosObject).append(CommonConstant.COMMA).append(reqUsablePromotionInfoDTO.getOrderNo()).toString());
                        } else {
                            redisUtil.hset(redisKey, KeysConstant.DAILY_DRAW_ORDER_NOS, reqUsablePromotionInfoDTO.getOrderNo());
                        }
                    }
                }
                // 只返回本次订单获取的抽奖次数
                if (redisUtil.hget(redisKey, reqUsablePromotionInfoDTO.getOrderNo()) == null) {
                    return;
                }
                resUsablePromotionInfoDTO.setDrawTimes(Integer.valueOf((String) redisUtil.hget(redisKey, reqUsablePromotionInfoDTO.getOrderNo())));
            }
            // 如果不需要计算抽奖次数 则直接从缓存中获取抽奖次数
            else {
                resUsablePromotionInfoDTO.setDrawTimes(resLotteryDrawTimesDTO.getData().getLastDrawTimes());
            }
            resUsablePromotionInfoDTOList.add(resUsablePromotionInfoDTO);
        });
        // 如果订单退货审核成功 则不展示
        if (LotteryTypeEnum.LOTTERY_TYPE_TWO.getCode().equals(reqUsablePromotionInfoDTO.getLotteryType()) && StringUtils.isNotBlank(reqUsablePromotionInfoDTO.getOrderNo())) {
            AtomReqSoReturnDTO atomReqSoReturnDTO = new AtomReqSoReturnDTO();
            atomReqSoReturnDTO.setBuyerNo(reqUsablePromotionInfoDTO.getFanNo());
            atomReqSoReturnDTO.setOrderNo(reqUsablePromotionInfoDTO.getOrderNo());
            // 退单表除了审核中、用户取消和商家驳回 都不展示入口
            atomReqSoReturnDTO.setReturnStatus("1000,1001,1007,1008");
            atomReqSoReturnDTO.setRefundmentStatus("");
            ExecuteDTO<Integer> orderByHxgSubscript = legacyOrderCenterService.countReturnOrderByHxgSubscript(atomReqSoReturnDTO);
            if (!orderByHxgSubscript.successFlag()) {
                return ExecuteDTO.error(orderByHxgSubscript.getStatus(), orderByHxgSubscript.getMsg());
            }
            // 如果不存在活动信息 则返回空list
            if (null == orderByHxgSubscript.getData()) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            if (orderByHxgSubscript.getData() > NumConstant.ZERO) {
                return ExecuteDTO.success(Lists.newArrayList());
            }
        }

        return ExecuteDTO.success(resUsablePromotionInfoDTOList);
    }

    /**
     * 根据订单编号查询订单详情中的秒杀活动信息
     *
     * @param orderPromotionInfoDTO
     * @return
     * <AUTHOR>
     * @date 2021-07-13
     */
    @Override
    public ExecuteDTO<ResOrderPromotionInfoDTO> getOrderPromotionInfo(ReqOrderPromotionInfoDTO orderPromotionInfoDTO) {
        log.info("**PromotionInfoAnalysisServiceImpl.getOrderPromotionInfo-入参：{}", orderPromotionInfoDTO);
        // 入参校验
        promotionInfoAssert.getOrderPromotionInfoAssert(orderPromotionInfoDTO);

        // 根据订单编号查询对应的活动编号、商品编号以及购买的商品数量
        ExecuteDTO<ResSoPromotionItemDTO> executeDTO = orderPromotionAnalysisService.getOrderPromotionAndGoodsInfo(orderPromotionInfoDTO.getOrderNo());
        if (null == executeDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }

        // 如果查询的结果中，没有促销活动编号，则直接返回
        if (null == executeDTO.getData() || StringUtils.isBlank(executeDTO.getData().getPromotionNo())) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000612);
        }

        // 根据活动编号商品编码，查询促销活动的基本信息以及商品的秒杀价格
        AtomReqGoodsPromotionGoodsRelationDTO reqDTO = new AtomReqGoodsPromotionGoodsRelationDTO();
        reqDTO.setPromotionNo(executeDTO.getData().getPromotionNo());
        reqDTO.setGoodsNo(executeDTO.getData().getGoodsNo());
        ExecuteDTO<AtomResGoodsPromotionGoodsRelationDTO> promotionAndGoodsInfo = goodsRelationAnalysisService.getPromotionAndGoodsInfo(reqDTO);
        if (null == promotionAndGoodsInfo) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!promotionAndGoodsInfo.successFlag()) {
            return ExecuteDTO.error(promotionAndGoodsInfo.getStatus(), promotionAndGoodsInfo.getMsg());
        }
        if (null == promotionAndGoodsInfo.getData()) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000613);
        }

        // 返回的结果信息
        ResOrderPromotionInfoDTO resOrderPromotionInfoDTO = BeanCopierUtil.copy(promotionAndGoodsInfo.getData(), ResOrderPromotionInfoDTO.class);
        resOrderPromotionInfoDTO.setSerialNum(NumConstant.ONE);

        // 商品数量
        BigDecimal goodsNum = BigDecimal.valueOf(0);
        if (null != executeDTO.getData().getGoodsNum()) {
            goodsNum = executeDTO.getData().getGoodsNum();
        }
        // 活动价格
        BigDecimal promotionPrice = BigDecimal.valueOf(0);
        if (null != promotionAndGoodsInfo.getData().getPromotionPrice()) {
            promotionPrice = promotionAndGoodsInfo.getData().getPromotionPrice();
        }
        // 根据商品编码，查询商品的信息，计算优惠金额
        BigDecimal discountAmount = BigDecimal.valueOf(0);
        // 平台活动，查询云池商品
        if (SourceTypeEnum.SOURCE_TYPE_1001.getCode().equals(promotionAndGoodsInfo.getData().getSourceType())
                && StringUtils.isNotBlank(executeDTO.getData().getGoodsNo())) {
            ReqCloudPoolApplyComPageDTO cloudPoolApplyComPageDTO = new ReqCloudPoolApplyComPageDTO();
            cloudPoolApplyComPageDTO.setNoPage();
            List<String> goodsNoList = new ArrayList<>();
            goodsNoList.add(executeDTO.getData().getGoodsNo());
            cloudPoolApplyComPageDTO.setGoodsNos(goodsNoList);
            // 设置促销活动查询的标识
            cloudPoolApplyComPageDTO.setGoodsPromotionFlag(WhetherEnum.YES.getCode());
            ExecuteDTO<ExecutePageDTO<ResGoodsDTO>> cloudPoolGoodsList = cloudPoolApplyAnalysisService.getCloudPoolGoodsList(cloudPoolApplyComPageDTO);
            if (null == cloudPoolGoodsList) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            if (!cloudPoolGoodsList.successFlag()) {
                return ExecuteDTO.error(cloudPoolGoodsList.getStatus(), cloudPoolGoodsList.getMsg());
            }
            if (null == cloudPoolGoodsList.getData() || CollectionUtils.isEmpty(cloudPoolGoodsList.getData().getRows())) {
                return ExecuteDTO.error(MarketErrorCode.CODE_17000614);
            }
            // 优惠金额 = (云池零售价 - 秒杀价) * 商品数量
            //云池零售价
            BigDecimal retailPrice = BigDecimal.valueOf(0);
            if (null != cloudPoolGoodsList.getData().getRows().get(0).getRetailPrice()) {
                retailPrice = cloudPoolGoodsList.getData().getRows().get(0).getRetailPrice();
            }
            discountAmount = retailPrice.subtract(promotionPrice).multiply(goodsNum);
        } else if (SourceTypeEnum.SOURCE_TYPE_1003.getCode().equals(promotionAndGoodsInfo.getData().getSourceType())
                && StringUtils.isNotBlank(executeDTO.getData().getGoodsNo())) {
            // 店铺活动，店铺自建或者商家分发的商品
            ReqGoodsDTO reqGoodsDTO = BeanCopierUtil.copy(reqDTO, ReqGoodsDTO.class);
            List<String> goodsNoList = new ArrayList<>();
            goodsNoList.add(executeDTO.getData().getGoodsNo());
            // 设置要查询的商品
            reqGoodsDTO.setGoodsNos(goodsNoList);
            // 不分页查询
            reqGoodsDTO.setNoPage();
            ExecuteDTO<ExecutePageDTO<ResGoodsDTO>> goodsPage = goodsAnalysisService.getGoodsPage(reqGoodsDTO);
            if (null == goodsPage) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            if (!goodsPage.successFlag()) {
                return ExecuteDTO.error(goodsPage.getStatus(), goodsPage.getMsg());
            }
            if (null == goodsPage.getData() || CollectionUtils.isEmpty(goodsPage.getData().getRows())) {
                return ExecuteDTO.error(MarketErrorCode.CODE_17000614);
            }
            // 优惠金额 = (零售价 - 秒杀价) * 商品数量
            //零售价
            BigDecimal retailPrice = BigDecimal.valueOf(0);
            if (null != goodsPage.getData().getRows().get(0).getRetailPrice()) {
                retailPrice = goodsPage.getData().getRows().get(0).getRetailPrice();
            }
            discountAmount = retailPrice.subtract(promotionPrice).multiply(goodsNum);
        }
        resOrderPromotionInfoDTO.setDiscountAmount(BigDecimalUtil.setScale(discountAmount));

        // 返回结果
        return ExecuteDTO.success(resOrderPromotionInfoDTO);
    }

    /**
     * @param reqPromotionInfoDTO
     * @return
     */
    @Override
    public ExecuteDTO<Integer> getMerchantOnGoingPromotionCount(ReqPromotionInfoDTO reqPromotionInfoDTO) {
        log.info("PromotionInfoAnalysisServiceImpl.getMerchantOnGoingPromotionCount----param----{}", reqPromotionInfoDTO.getMerchantNo());
        AtomReqPromotionInfoDTO atomReqPromotionInfoDTO = BeanCopierUtil.copy(reqPromotionInfoDTO, AtomReqPromotionInfoDTO.class);
        ExecuteDTO<Integer> promotionCountExecuteDTO = atomPromotionInfoAnalysisService.getMerchantOnGoingPromotionCount(atomReqPromotionInfoDTO);
        if (promotionCountExecuteDTO.successFlag()
                && null != promotionCountExecuteDTO.getData()
                && promotionCountExecuteDTO.getData() > 0) {
            return ExecuteDTO.error(BossLogoffErrorEnum.ONGOING_MARKETING_CAMPAIGN, promotionCountExecuteDTO.getData());
        }
        return promotionCountExecuteDTO;
    }

    @Override
    public ExecuteDTO<ResPromotionInfoDTO> getNewPromotionCount(ReqPromotionInfoDTO reqPromotionInfoDTO) {
        AtomReqPromotionInfoDTO getAtomReqPromotionInfoDTO = BeanCopierUtil.copy(reqPromotionInfoDTO, AtomReqPromotionInfoDTO.class);
        ExecuteDTO<AtomResPromotionInfoDTO> executeDTO = atomPromotionInfoAnalysisService.getNewPromotionCount(getAtomReqPromotionInfoDTO);
        if (executeDTO.successFlag()) {
            return ExecuteDTO.success(BeanCopierUtil.copy(executeDTO.getData(), ResPromotionInfoDTO.class));
        }
        return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
    }

    @Override
    public ExecuteDTO<ResMarketingActivitiesCountDTO> marketingActivitiesCount(ReqPromotionInfoDTO reqPromotionInfoDTO) {
        List<String> promotionTypes = Lists.newArrayList();

        PromotionInfoVO promotionInfoVO = BeanCopierUtil.copy(reqPromotionInfoDTO, PromotionInfoVO.class);
        // 查询数据 需要排除掉代金券和给粉丝发券
        for (String promotionType : reqPromotionInfoDTO.getPromotionTypes()) {
            if (!promotionType.equals(PromotionTypeEnum.VOUCHER.getCode()) && !promotionType.equals(PromotionTypeEnum.SHOP_MANUAL_COUPON.getCode())) {
                promotionTypes.add(promotionType);
            }
        }
        promotionInfoVO.setPromotionTypes(promotionTypes);

        ResMarketingActivitiesCountDTO resMarketingActivitiesCountDTO = new ResMarketingActivitiesCountDTO();
        // 查询数据
        promotionInfoVO.setPromotionTypes(promotionTypes);
        // 排除掉查询当前时间的条件判断
        promotionInfoVO.setInTimeFlag(1);
        promotionInfoVO.setDeleteFlag(null);

        List<PromotionInfoVO> promotionInfos = promotionInfoDao.selectPromotionInfoList(promotionInfoVO);

        // 排除掉草稿状态
        List<PromotionInfoVO> promotionInfoVOS = promotionInfos.stream().filter(promotionInfo -> !promotionInfo.getStatus().equals(PromotionStatusEnum.ONLY_SAVED.getCode())).collect(Collectors.toList());
        // 1.抽奖类活动：小猫钓鱼、大转盘、摇奖机、砸金蛋
        resMarketingActivitiesCountDTO.setLuckDrawMarketCount((int) promotionInfoVOS.stream().filter(promotionInfo -> PromotionTypeEnum.LUCK_DRAW_MARKET.contains(promotionInfo.getPromotionType())).count());
        // 2.网店-商品促销类活动：秒杀、限时购、预售、社群接龙
        resMarketingActivitiesCountDTO.setShopMarketCount((int) promotionInfoVOS.stream().filter(promotionInfo -> PromotionTypeEnum.SHOP_MARKET.contains(promotionInfo.getPromotionType())).count());
        // 3.门店-商品促销类活动：满减满折、特惠促销
        resMarketingActivitiesCountDTO.setStoreMarketCount((int) promotionInfoVOS.stream().filter(promotionInfo -> PromotionTypeEnum.STORE_MARKET.contains(promotionInfo.getPromotionType())).count());
        // 4.券类活动：让粉丝领券、膨胀红包
        resMarketingActivitiesCountDTO.setCouponMarketCount((int) promotionInfoVOS.stream().filter(promotionInfo -> PromotionTypeEnum.COUPON_MARKET.contains(promotionInfo.getPromotionType())).count());
        // 5. 代金券、给粉丝发券
        AtomCouponSettingVo atomCouponSettingVo = new AtomCouponSettingVo();
        if(reqPromotionInfoDTO.getInvalidTime() != null && reqPromotionInfoDTO.getEffectiveTime() != null) {
            atomCouponSettingVo.setInvalidTime(reqPromotionInfoDTO.getInvalidTime());
            atomCouponSettingVo.setEffectiveTime(reqPromotionInfoDTO.getEffectiveTime());
        }
        if(reqPromotionInfoDTO.getStartTimeCount() != null && reqPromotionInfoDTO.getEndTimeCount() != null) {
            atomCouponSettingVo.setStartTime(reqPromotionInfoDTO.getStartTimeCount().atStartOfDay());
            atomCouponSettingVo.setEndTime(reqPromotionInfoDTO.getEndTimeCount().atTime(LocalTime.MAX));
        }
        atomCouponSettingVo.setStoreNo(reqPromotionInfoDTO.getStoreNo());
        atomCouponSettingVo.setMerchantNo(reqPromotionInfoDTO.getMerchantNo());
        atomCouponSettingVo.setPromotionCouponTypeList(Lists.newArrayList(PromotionTypeEnum.VOUCHER.getCode(), PromotionTypeEnum.SHOP_MANUAL_COUPON.getCode()));

        if(null != promotionInfoVO.getStoreNoList() &&  promotionInfoVO.getStoreNoList().size() > 0){
            atomCouponSettingVo.setStoreNoList(promotionInfoVO.getStoreNoList());
            atomCouponSettingVo.setStoreNo(null);
        }


        List<AtomCouponSettingVo> atomCouponSettingVos = couponSettingDao.selectCouponSettingList(atomCouponSettingVo);
        // 需要过滤一下是否还有权限
        List<AtomCouponSettingVo> couponSettingVos = atomCouponSettingVos.stream().filter(atomCouponSetting -> reqPromotionInfoDTO.getPromotionTypes().contains(atomCouponSetting.getPromotionType())).collect(Collectors.toList());
        // 券类活动累加满减券和代金券数据
        resMarketingActivitiesCountDTO.setCouponMarketCount(resMarketingActivitiesCountDTO.getCouponMarketCount() + couponSettingVos.size());
        return ExecuteDTO.success(resMarketingActivitiesCountDTO);
    }

    @Override
    public ExecuteDTO<ResMarketingActivitiesInfoDTO> listMarketingActivities(ReqPromotionInfoDTO reqPromotionInfoDTO) {
        List<String> promotionTypes = Lists.newArrayList();

        PromotionInfoVO promotionInfoVO = BeanCopierUtil.copy(reqPromotionInfoDTO, PromotionInfoVO.class);
        // 查询数据 需要排除掉代金券和给粉丝发券
        for (String promotionType : reqPromotionInfoDTO.getPromotionTypes()) {
            if (!promotionType.equals(PromotionTypeEnum.VOUCHER.getCode()) && !promotionType.equals(PromotionTypeEnum.SHOP_MANUAL_COUPON.getCode())) {
                promotionTypes.add(promotionType);
            }
        }
        promotionInfoVO.setPromotionTypes(promotionTypes);

        // 排除掉查询当前时间的条件判断
        promotionInfoVO.setInTimeFlag(1);
        promotionInfoVO.setDeleteFlag(null);

        //1. 查询活动
        List<PromotionInfoVO> promotionInfos = promotionInfoDao.selectPromotionInfoList(promotionInfoVO);
        //2. 查询代金券、给粉丝发券
        AtomCouponSettingVo atomCouponSettingVo = new AtomCouponSettingVo();
        if (reqPromotionInfoDTO.getInvalidTime() != null && reqPromotionInfoDTO.getEffectiveTime() != null) {
            atomCouponSettingVo.setInvalidTime(reqPromotionInfoDTO.getInvalidTime());
            atomCouponSettingVo.setEffectiveTime(reqPromotionInfoDTO.getEffectiveTime());
        }
        if (reqPromotionInfoDTO.getStartTimeCount() != null && reqPromotionInfoDTO.getEndTimeCount() != null) {
            atomCouponSettingVo.setStartTime(reqPromotionInfoDTO.getStartTimeCount().atStartOfDay());
            atomCouponSettingVo.setEndTime(reqPromotionInfoDTO.getEndTimeCount().atTime(LocalTime.MAX));
        }
        atomCouponSettingVo.setStoreNo(reqPromotionInfoDTO.getStoreNo());

        atomCouponSettingVo.setPromotionCouponTypeList(Lists.newArrayList(PromotionTypeEnum.VOUCHER.getCode(), PromotionTypeEnum.SHOP_MANUAL_COUPON.getCode()));
        List<AtomCouponSettingVo> atomCouponSettingVos = couponSettingDao.selectCouponSettingList(atomCouponSettingVo);
        // 将代金券、给粉丝发券组装到promotion中统一处理
        if (CollectionUtils.isNotEmpty(atomCouponSettingVos)) {
            promotionInfos.addAll(BeanCopierUtil.copyList(atomCouponSettingVos, PromotionInfoVO.class));
        }

        Map<String, List<PromotionInfoVO>> collect = promotionInfos.stream().collect(Collectors.groupingBy(PromotionInfoVO::getPromotionType));
        // 组装返回数据
        List<ResMarketingActivitiesInfoDTO.Marketing> luckDrawMarket = Lists.newArrayList();
        List<ResMarketingActivitiesInfoDTO.Marketing> shopMarket = Lists.newArrayList();
        List<ResMarketingActivitiesInfoDTO.Marketing> storeMarket = Lists.newArrayList();
        List<ResMarketingActivitiesInfoDTO.Marketing> couponMarket = Lists.newArrayList();

        reqPromotionInfoDTO.getPromotionTypes().forEach(promotionType -> {
            ResMarketingActivitiesInfoDTO.Marketing marketing = new ResMarketingActivitiesInfoDTO.Marketing();
            marketing.setPromotionType(promotionType);
            // 默认设置0
            marketing.setPromotionCount(NumConstant.ZERO);
            if (collect.containsKey(promotionType)) {
                marketing.setPromotionCount(collect.get(promotionType).size());
            }
            if (PromotionTypeEnum.LUCK_DRAW_MARKET.contains(marketing.getPromotionType())) {
                luckDrawMarket.add(marketing);
            } else if (PromotionTypeEnum.SHOP_MARKET.contains(marketing.getPromotionType())) {
                shopMarket.add(marketing);
            } else if (PromotionTypeEnum.STORE_MARKET.contains(marketing.getPromotionType())) {
                storeMarket.add(marketing);
            } else if (PromotionTypeEnum.COUPON_MARKET.contains(marketing.getPromotionType())) {
                couponMarket.add(marketing);
            }
        });
        ResMarketingActivitiesInfoDTO resMarketingActivitiesInfoDTO = new ResMarketingActivitiesInfoDTO();
        resMarketingActivitiesInfoDTO.setLuckDrawMarket(luckDrawMarket);
        resMarketingActivitiesInfoDTO.setShopMarket(shopMarket);
        resMarketingActivitiesInfoDTO.setStoreMarket(storeMarket);
        resMarketingActivitiesInfoDTO.setCouponMarket(couponMarket);

        return ExecuteDTO.success(resMarketingActivitiesInfoDTO);
    }


}
