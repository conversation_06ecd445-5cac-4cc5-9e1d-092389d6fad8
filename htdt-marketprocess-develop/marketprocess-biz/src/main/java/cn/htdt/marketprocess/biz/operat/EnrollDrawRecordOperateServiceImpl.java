package cn.htdt.marketprocess.biz.operat;

import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.enums.market.ApplyStatusEnum;
import cn.htdt.common.enums.market.ApplyTypeEnum;
import cn.htdt.common.enums.market.EnrollStoreTypeEnum;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqEnrollApplyDTO;
import cn.htdt.marketcenter.dto.request.AtomReqEnrollImportStoreDTO;
import cn.htdt.marketcenter.dto.request.AtomReqEnrollRecordInfoDTO;
import cn.htdt.marketcenter.dto.request.AtomReqEnrollRuleListDTO;
import cn.htdt.marketcenter.dto.response.AtomResEnrollImportStoreDTO;
import cn.htdt.marketcenter.dto.response.AtomResEnrollRuleListDTO;
import cn.htdt.marketprocess.api.operat.EnrollDrawRecordOperateService;
import cn.htdt.marketprocess.biz.conversion.EnrollPromotionAssert;
import cn.htdt.marketprocess.dto.request.ReqEnrollApplyDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomEnrollImportStoreAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomEnrollRuleAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomEnrollDrawRecordOperateService;
import cn.htdt.userprocess.api.StoreProcessService;
import cn.htdt.userprocess.dto.request.user.ReqStoresDTO;
import cn.htdt.userprocess.dto.response.StoreInfoResponse;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 报名记录操作类
 * <AUTHOR>
 * @date 2022/3/23 14:07
 */
@Slf4j
@DubboService
public class EnrollDrawRecordOperateServiceImpl implements EnrollDrawRecordOperateService {

    @Resource
    private AtomEnrollRuleAnalysisService atomEnrollRuleAnalysisService;
    @Resource
    private AtomEnrollImportStoreAnalysisService atomEnrollImportStoreAnalysisService;
    @DubboReference
    private StoreProcessService storeProcessService;
    @Resource
    private AtomEnrollDrawRecordOperateService atomEnrollDrawRecordOperateService;
    @Resource
    private EnrollPromotionAssert enrollPromotionAssert;

    /**
     * @param recordDTO
     * @Description : 店铺报名接口
     * <AUTHOR> 卜金隆
     * @date : 2022/3/23 14:10
     */
    @Override
    public ExecuteDTO storeEnrollPromotion(ReqEnrollApplyDTO recordDTO) {
        log.info("MarketProcess-EnrollDrawRecordOperateServiceImpl-storeEnrollPromotion-params={}", JSON.toJSONString(recordDTO));
        //断言
        this.enrollPromotionAssert.storeEnrollPromotion(recordDTO);
        // 报名活动规则查询
        AtomReqEnrollRuleListDTO atomReqEnrollRuleListDTO = new AtomReqEnrollRuleListDTO();
        atomReqEnrollRuleListDTO.setPromotionNo(recordDTO.getPromotionNo());
        ExecuteDTO<AtomResEnrollRuleListDTO> executeDTO = atomEnrollRuleAnalysisService.getEnrollPromotionInfo(atomReqEnrollRuleListDTO);
        log.info(String.format("报名活动明细查询结果:%s", JSON.toJSONString(executeDTO)));
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        AtomResEnrollRuleListDTO ruleInfo = executeDTO.getData();
        // 店铺信息

        ReqStoresDTO reqStoresDTO = new ReqStoresDTO();
        reqStoresDTO.setStoreNo(recordDTO.getStoreNo());
        ExecuteDTO<StoreInfoResponse> storeInfoDTO = storeProcessService.getStoreInfoByStoreNo(reqStoresDTO);
        if (!storeInfoDTO.successFlag()) {
            return ExecuteDTO.error(CommonCode.CODE_10000001.getCode(),"店铺信息查询失败");
        }
        StoreInfoResponse storeInfo = storeInfoDTO.getData();
        // 根据报名规则校验店铺是否有报名权限
        ExecuteDTO checkEnrollRoot = checkEnrollRoot(recordDTO,ruleInfo,storeInfo);
        if (!checkEnrollRoot.successFlag()){
            return checkEnrollRoot;
        }
        // 生成报名记录
        AtomReqEnrollRecordInfoDTO recordInfoDTO = BeanCopierUtil.copy(storeInfo, AtomReqEnrollRecordInfoDTO.class);
        recordInfoDTO.setName(recordDTO.getName());
        recordInfoDTO.setPhone(recordDTO.getPhone());
        recordInfoDTO.setDsPhone(recordDTO.getDsPhone());
        recordInfoDTO.setPromotionNo(recordDTO.getPromotionNo());
        recordInfoDTO.setPromotionName(ruleInfo.getPromotionName());
        recordInfoDTO.setApplyStatus(ApplyStatusEnum.APPLY_TYPE_WAIT.getCode());
        recordInfoDTO.setStoreEnrollExplain(recordDTO.getStoreEnrollExplain());
        recordInfoDTO.setDeviceNumber(recordDTO.getDeviceNumber());
        recordInfoDTO.setCreateNo(recordDTO.getCreateNo());
        recordInfoDTO.setCreateName(recordDTO.getCreateName());
        atomEnrollDrawRecordOperateService.addEnrollRecord(recordInfoDTO);

        return ExecuteDTO.success();
    }
    /**
     * @param recordDTO
     * @Description : 平台受理接口
     * <AUTHOR> 卜金隆
     * @date : 2022/3/24 09:20
     */
    @Override
    public ExecuteDTO acceptEnrollPromotion(ReqEnrollApplyDTO recordDTO) {
        log.info("MarketProcess-EnrollDrawRecordOperateServiceImpl-storeEnrollPromotion-params={}", JSON.toJSONString(recordDTO));
        //断言
        this.enrollPromotionAssert.acceptEnrollPromotion(recordDTO);
        // 修改报名状态
        AtomReqEnrollApplyDTO enrollRecordDTO = BeanCopierUtil.copy(recordDTO, AtomReqEnrollApplyDTO.class);
        return atomEnrollDrawRecordOperateService.modifyApplyStatus(enrollRecordDTO);
    }

    /**
     *  根据报名规则校验店铺是否有报名权限
     */
    private ExecuteDTO checkEnrollRoot(ReqEnrollApplyDTO recordDTO, AtomResEnrollRuleListDTO ruleInfo, StoreInfoResponse storeInfo ) {
        if (!EnrollStoreTypeEnum.STORE_TYPE_ONE.getCode().equals(ruleInfo.getStoreType())) {
            return ExecuteDTO.error(CommonCode.CODE_10000002.getCode(),"报名类型错误");
        }
        // 全部店铺参加直接返回
        if (ApplyTypeEnum.APPLY_TYPE_ALL.getCode().equals(ruleInfo.getApplyType())) {
            return ExecuteDTO.success();
        }
        // 区域范围内类型 根据当前店铺地址判断
        if (ApplyTypeEnum.APPLY_TYPE_AREA.getCode().equals(ruleInfo.getApplyType())) {
            if (StringUtils.isEmpty(ruleInfo.getApplyArea())) {
                return ExecuteDTO.error(CommonCode.CODE_10000002.getCode(),"区域范围");
            }
            List<String> areaList =
                    Lists.newArrayList(ruleInfo.getApplyArea().split("\\|"));
            if ((StringUtils.isNotBlank(storeInfo.getProvinceCode()) && areaList.contains(storeInfo.getProvinceCode()))
                    || (StringUtils.isNotBlank(storeInfo.getProvinceCode()) && areaList.contains(storeInfo.getCityCode()))
                    || (StringUtils.isNotBlank(storeInfo.getProvinceCode()) && areaList.contains(storeInfo.getRegionCode()))) {
                return ExecuteDTO.success();
            }
        }
        // 指定店铺类型 根据导入数据判断是否包含当前店铺
        if (ApplyTypeEnum.APPLY_TYPE_IMPORT.getCode().equals(ruleInfo.getApplyType())) {
            AtomReqEnrollImportStoreDTO atomReqEnrollImportStoreDTO = new AtomReqEnrollImportStoreDTO();
            atomReqEnrollImportStoreDTO.setPromotionNo(ruleInfo.getPromotionNo());
            ExecuteDTO<List<AtomResEnrollImportStoreDTO>> listExecuteDTO = atomEnrollImportStoreAnalysisService.getEnrollImportStoreList(atomReqEnrollImportStoreDTO);
            if (!listExecuteDTO.successFlag()) {
                return ExecuteDTO.error(CommonCode.CODE_10000001.getCode(),"店铺信息查询失败");
            }
            if (CollectionUtils.isNotEmpty(listExecuteDTO.getData())) {
                List<String> storeList = listExecuteDTO.getData().stream().map(AtomResEnrollImportStoreDTO::getStoreNo).collect(Collectors.toList());
                if (storeList.contains(recordDTO.getStoreNo())) {
                    return ExecuteDTO.success();
                }
            }
        }
        return ExecuteDTO.error(CommonCode.CODE_10000002.getCode(),"抱歉，当前店铺不在报名活动参与店铺范围内~");
    }
}
