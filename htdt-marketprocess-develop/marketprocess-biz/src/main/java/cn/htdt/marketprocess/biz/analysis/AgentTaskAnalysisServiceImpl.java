package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.market.RewardStatusFlagEnum;
import cn.htdt.common.enums.market.RewardTypeEnum;
import cn.htdt.common.enums.market.TaskTimeStatusEnum;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.*;
import cn.htdt.marketcenter.dto.response.*;
import cn.htdt.marketprocess.api.analysis.AgentTaskAnalysisService;
import cn.htdt.marketprocess.dto.request.ReqAgentTaskDTO;
import cn.htdt.marketprocess.dto.request.ReqStoreAgentTaskDTO;
import cn.htdt.marketprocess.dto.response.ResAgentMarketRewardsetDTO;
import cn.htdt.marketprocess.dto.response.ResAgentTaskDTO;
import cn.htdt.marketprocess.dto.response.ResStoreAgentTaskDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomAgentMarketRewardsetAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomAgentRewardAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomAgentRewardRecordAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomAgentTaskAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomAgentTaskRelStoreOperatService;
import cn.htdt.userprocess.api.UserPublicService;
import cn.htdt.userprocess.dto.publicDto.response.GetStoreListResponse;
import cn.htdt.userprocess.dto.request.MerchantStoreRequest;
import cn.htdt.userprocess.dto.response.MerchantStoreResponse;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021-01-21
 * @Description 代理人任务原子查询服务实现类
 */
@Slf4j
@DubboService
public class AgentTaskAnalysisServiceImpl implements AgentTaskAnalysisService {

    @Resource
    private AtomAgentTaskAnalysisService atomAgentTaskAnalysisService;
    @Resource
    private AtomAgentMarketRewardsetAnalysisService atomAgentMarketRewardsetAnalysisService;
    @Resource
    private AtomAgentRewardAnalysisService atomAgentRewardAnalysisService;

    @Resource
    private AtomAgentTaskRelStoreOperatService atomAgentTaskRelStoreOperatService;

    @DubboReference
    private UserPublicService userPublicService;
    @Resource
    private AtomAgentRewardRecordAnalysisService atomAgentRewardRecordAnalysisService;

    @Override
    public ExecuteDTO<ExecutePageDTO<ResAgentTaskDTO>> getListByPage(ReqAgentTaskDTO reqAgentTaskDTO) {
        AtomReqAgentTaskDTO atomReqAgentTaskDTO = BeanCopierUtil.copy(reqAgentTaskDTO, AtomReqAgentTaskDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResAgentTaskDTO>> executeDTO = atomAgentTaskAnalysisService.getListByPage(atomReqAgentTaskDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ExecutePageDTO<ResAgentTaskDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResAgentTaskDTO> resAgentTaskDTOList = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResAgentTaskDTO.class);
        getTaskTimeStatus(resAgentTaskDTOList);
        executePageDTO.setRows(resAgentTaskDTOList);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    @Override
    public ExecuteDTO<ResAgentTaskDTO> getAgentTaskDetail(ReqAgentTaskDTO reqAgentTaskDTO) {
        AtomReqAgentTaskDTO atomReqAgentTaskDTO = BeanCopierUtil.copy(reqAgentTaskDTO, AtomReqAgentTaskDTO.class);
        ExecuteDTO<AtomResAgentTaskDTO> executeDTO = atomAgentTaskAnalysisService.get(atomReqAgentTaskDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResAgentTaskDTO resAgentTaskDTO = BeanCopierUtil.copy(executeDTO.getData(), ResAgentTaskDTO.class);
        getTaskTimeStatus(Arrays.asList(resAgentTaskDTO));
        getTaskReward(Arrays.asList(resAgentTaskDTO), reqAgentTaskDTO.getAgentNo(), Arrays.asList(RewardStatusFlagEnum.TWO.getCode(), RewardStatusFlagEnum.THREE.getCode()));
        List<ResAgentMarketRewardsetDTO> resAgentMarketRewardsetDTOList = BeanCopierUtil.copyList(executeDTO.getData().getListRewardSetDTO(), ResAgentMarketRewardsetDTO.class);
        resAgentTaskDTO.setListRewardSetDTO(resAgentMarketRewardsetDTOList);
        return ExecuteDTO.success(resAgentTaskDTO);
    }

    @Override
    public ExecuteDTO<ExecutePageDTO<ResAgentTaskDTO>> getStoreAgentTaskByPage(ReqAgentTaskDTO reqAgentTaskDTO) {
        AtomReqAgentTaskDTO atomReqAgentTaskDTO = BeanCopierUtil.copy(reqAgentTaskDTO, AtomReqAgentTaskDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResAgentTaskDTO>> executeDTO = atomAgentTaskAnalysisService.getStoreAgentTaskByPage(atomReqAgentTaskDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ExecutePageDTO<ResAgentTaskDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResAgentTaskDTO> resAgentTaskDTOList = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResAgentTaskDTO.class);
        getTaskTimeStatus(resAgentTaskDTOList);
        executePageDTO.setRows(resAgentTaskDTOList);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    private void getTaskTimeStatus(List<ResAgentTaskDTO> resAgentTaskDTOList) {
        for (ResAgentTaskDTO resAgentTaskDTO: resAgentTaskDTOList) {
            //未开始
            if (resAgentTaskDTO.getTaskStartTime().isAfter(LocalDate.now())) {
                resAgentTaskDTO.setTaskTimeStatus(NumConstant.ONE);
            //进行中
            } else if((resAgentTaskDTO.getTaskStartTime().isBefore(LocalDate.now()) || resAgentTaskDTO.getTaskStartTime().equals(LocalDate.now()))
                    && (resAgentTaskDTO.getTaskEndTime().equals(LocalDateTime.now()) || resAgentTaskDTO.getTaskEndTime().isAfter(LocalDate.now()))) {
                resAgentTaskDTO.setTaskTimeStatus(NumConstant.TWO);
            //已结束
            } else if(resAgentTaskDTO.getTaskEndTime().isBefore(LocalDate.now())) {
                resAgentTaskDTO.setTaskTimeStatus(NumConstant.THREE);
            }
        }
    }

    private ExecuteDTO getTaskReward(List<ResAgentTaskDTO> resAgentTaskDTOList, String agentNo, List<Integer> rewardStatusFlagList) {
        if (CollectionUtils.isEmpty(resAgentTaskDTOList) || StringUtils.isBlank(agentNo)) {
            return ExecuteDTO.success();
        }
        //查询代理人任务获得的酬劳
        List taskOrGoodsNoList = new ArrayList<String>();
        for (ResAgentTaskDTO resAgentTaskDTO : resAgentTaskDTOList) {
            taskOrGoodsNoList.add(resAgentTaskDTO.getTaskNo());
        }
        AtomReqAgentRewardDTO reqAgentRewardDTO = new AtomReqAgentRewardDTO();
        reqAgentRewardDTO.setAgentNo(agentNo);
        reqAgentRewardDTO.setTaskOrGoodsNoList(taskOrGoodsNoList);
        reqAgentRewardDTO.setRewardStatusFlagList(rewardStatusFlagList);
        ExecuteDTO<List<AtomResAgentRewardDTO>> rewardExecuteDTO = atomAgentRewardAnalysisService.selectAgentRewardList(reqAgentRewardDTO);
        if (!rewardExecuteDTO.successFlag()) {
            return ExecuteDTO.error(rewardExecuteDTO.getStatus(), rewardExecuteDTO.getMsg());
        }
        Map<String,String> taskRewardMap = new HashMap();
        //按照任务编号分组
        Map<String, List<AtomResAgentRewardDTO>> taskNoGroupMap = rewardExecuteDTO.getData().stream().collect(Collectors.groupingBy(AtomResAgentRewardDTO::getTaskOrGoodsNo));
        for (Map.Entry<String, List<AtomResAgentRewardDTO>> taskNoMap : taskNoGroupMap.entrySet()) {
            List<AtomResAgentRewardDTO> taskNoAtomResAgentRewardList =  taskNoMap.getValue();
            String taskNo = taskNoMap.getKey();
            StringBuilder reward = new StringBuilder("");
            //按照酬劳类型分组
            Map<Integer, List<AtomResAgentRewardDTO>> rewardGroupMap = taskNoAtomResAgentRewardList.stream().collect(Collectors.groupingBy(AtomResAgentRewardDTO::getRewardType));
            for (Map.Entry<Integer, List<AtomResAgentRewardDTO>> rewardMap : rewardGroupMap.entrySet()) {
                List<AtomResAgentRewardDTO> rewardAtomResAgentRewardList =  rewardMap.getValue();
                Integer rewardType = rewardMap.getKey();
                StringBuilder sb = null;
                if (RewardTypeEnum.YJ.getCode().equals(rewardType)) {
                    sb = new StringBuilder("¥");
                } else if (RewardTypeEnum.HJB.getCode().equals(rewardType)) {
                    sb = new StringBuilder("汇金币");
                } else {
                    continue;
                }
                BigDecimal rewardValue = BigDecimal.ZERO;
                for (AtomResAgentRewardDTO atomResAgentRewardDTO : rewardAtomResAgentRewardList) {
                    rewardValue = rewardValue.add(atomResAgentRewardDTO.getRewardValue());
                }
                sb.append(rewardValue).append("+");
                reward.append(sb.toString());
            }
            taskRewardMap.put(taskNo, reward.toString().substring(0, reward.toString().length() -1));
        }
        for (ResAgentTaskDTO resAgentTaskDTO: resAgentTaskDTOList) {
            resAgentTaskDTO.setEstimateReward(taskRewardMap.get(resAgentTaskDTO.getTaskNo()));
        }
        return ExecuteDTO.success();
    }

    @Override
    public ExecuteDTO<ExecutePageDTO<ResStoreAgentTaskDTO>> getStoreAgentTaskListByParams(ReqStoreAgentTaskDTO reqStoreAgentTaskDTO) {
        log.info("-------AgentTaskAnalysisServiceImpl-->getStoreAgentTankListByParams --start----");
        AtomReqStoreAgentTaskDTO atomStoreAgentTaskDTO = new AtomReqStoreAgentTaskDTO();
        //初始化数据，查询未初始化的任务
        if (NumConstant.TWO == reqStoreAgentTaskDTO.getLoginIdentity()){
            //商家角色
            atomStoreAgentTaskDTO.setMerchantNo(reqStoreAgentTaskDTO.getMerchantNo());
        }else {
            atomStoreAgentTaskDTO.setStoreNo(reqStoreAgentTaskDTO.getStoreNo());
        }
        ExecuteDTO<List<String>>  taskExecuteDto =  atomAgentTaskAnalysisService.getNotInitTaskListByStoreOrMerchant(atomStoreAgentTaskDTO);
        if (!taskExecuteDto.successFlag()) {
            return ExecuteDTO.error(taskExecuteDto.getStatus(), taskExecuteDto.getMsg());
        }
        if (CollectionUtils.isNotEmpty(taskExecuteDto.getData())){
            //未加载的任务-->加载店铺任务关系
            List<String> storeNoList = new ArrayList<>();
            if (NumConstant.TWO == reqStoreAgentTaskDTO.getLoginIdentity()){
                //商家角色，先查询商家下的店铺
                MerchantStoreRequest merchantStoreRequest = new MerchantStoreRequest();
                merchantStoreRequest.setMerchantNo(reqStoreAgentTaskDTO.getMerchantNo());
                log.info(String.format("userPublicService.queryMerchantStore merchantStoreRequest入参:%s", JSON.toJSONString(merchantStoreRequest)));
                ExecuteDTO<List<MerchantStoreResponse>> merchantStoreDto =  userPublicService.queryMerchantStore(merchantStoreRequest);
                log.info(String.format("merchantStoreDto关系结果:%s", JSON.toJSONString(merchantStoreDto)));
                if (merchantStoreDto.successFlag() && CollectionUtils.isNotEmpty(merchantStoreDto.getData())){
                    merchantStoreDto.getData().forEach(agentTaskDTO -> {
                        storeNoList.add(agentTaskDTO.getStoreNo());
                    });
                }
            }else {
                storeNoList.add(reqStoreAgentTaskDTO.getStoreNo());
            }
            AtomReqAgentTaskRelStoreBatchListDTO atomReqAgentTaskRelStoreBatchListDTO = new AtomReqAgentTaskRelStoreBatchListDTO();
            atomReqAgentTaskRelStoreBatchListDTO.setTaskNoList(taskExecuteDto.getData());
            atomReqAgentTaskRelStoreBatchListDTO.setMerchantNo(reqStoreAgentTaskDTO.getMerchantNo());
            atomReqAgentTaskRelStoreBatchListDTO.setMerchantName(reqStoreAgentTaskDTO.getMerchantName());
            atomReqAgentTaskRelStoreBatchListDTO.setCreateNo(reqStoreAgentTaskDTO.getCreateNo());
            atomReqAgentTaskRelStoreBatchListDTO.setCreateName(reqStoreAgentTaskDTO.getCreateName());
            atomReqAgentTaskRelStoreBatchListDTO.setStoreNoList(storeNoList);
            ExecuteDTO insertExecute = atomAgentTaskRelStoreOperatService.saveTaskStoreRel(atomReqAgentTaskRelStoreBatchListDTO);
            log.info(String.format("批量插入店铺任务关系结果:%s", JSON.toJSONString(insertExecute)));
        }

        AtomReqStoreAgentTaskDTO atomReqStoreAgentTaskDTO = BeanCopierUtil.copy(reqStoreAgentTaskDTO, AtomReqStoreAgentTaskDTO.class);
        if (NumConstant.TWO == reqStoreAgentTaskDTO.getLoginIdentity()){
            //商家角色
            atomReqStoreAgentTaskDTO.setStoreNo("");
        }
        ExecuteDTO<ExecutePageDTO<AtomResStoreAgentTaskDTO>> executeDTO = this.atomAgentTaskAnalysisService.getStoreAgentTaskListByParams(atomReqStoreAgentTaskDTO);
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        //参数转换
        ExecutePageDTO<ResStoreAgentTaskDTO> executePageDTO = new ExecutePageDTO<>();
        List<AtomResStoreAgentTaskDTO> atomResAgentTaskDTOList = executeDTO.getData().getRows();
        List<ResStoreAgentTaskDTO> resStoreAgentTaskDTOList = BeanCopierUtil.copyList(atomResAgentTaskDTOList, ResStoreAgentTaskDTO.class);
        List<String> taskNoList = new ArrayList();
        List<String> storeNoList = new ArrayList();
        if (CollectionUtils.isNotEmpty(atomResAgentTaskDTOList)){
            atomResAgentTaskDTOList.forEach(agentTaskDTO -> {
                taskNoList.add(agentTaskDTO.getTaskNo());
                storeNoList.add(agentTaskDTO.getStoreNo());

            });
        }
        //查询酬劳设置
        ExecuteDTO<List<AtomResRewardSetListDto>> rewardSetListDto =  this.atomAgentMarketRewardsetAnalysisService.getRewardSetByTaskNos(taskNoList);
        if(rewardSetListDto.successFlag() && CollectionUtils.isNotEmpty(rewardSetListDto.getData())){
            List<AtomResRewardSetListDto> resRewardSetListDtos = rewardSetListDto.getData();
            resStoreAgentTaskDTOList = resStoreAgentTaskDTOList.stream().map(resStoreAgentTaskDTO->{
                resRewardSetListDtos.stream().filter(resRewardSetListDto->
                        Objects.equals(resStoreAgentTaskDTO.getTaskNo(),resRewardSetListDto.getTaskNo())).forEach(s->
                        {
                            resStoreAgentTaskDTO.setRewardSets(this.queryRewardSetTypeNames(s.getRewardSets()));
                            resStoreAgentTaskDTO.setAgentRewards(this.queryRewardvalueInit(s.getRewardSets()));

                        });
                return resStoreAgentTaskDTO;
            }).collect(Collectors.toList());
        }
        //查询代理人已获得酬劳
        AtomReqStoreAgentRewardDTO atomReqStoreAgentRewardDTO = new AtomReqStoreAgentRewardDTO();
        atomReqStoreAgentRewardDTO.setTaskNoList(taskNoList);
        atomReqStoreAgentRewardDTO.setStoreNoList(storeNoList);
        ExecuteDTO<List<AtomResAgentTaskRewardDTO>> agentRewardListDto =  this.atomAgentRewardAnalysisService.getAgentRewardListByTaskNo(atomReqStoreAgentRewardDTO);
        if(agentRewardListDto.successFlag() && CollectionUtils.isNotEmpty(agentRewardListDto.getData())){
            List<AtomResAgentTaskRewardDTO> agentRewardListDtoData = agentRewardListDto.getData();
            resStoreAgentTaskDTOList = resStoreAgentTaskDTOList.stream().map(resStoreAgentTaskDTO->{
                StringBuilder stringBuilder = new StringBuilder(resStoreAgentTaskDTO.getAgentRewards());
                agentRewardListDtoData.stream().filter(agentTaskRewardDTO->
                        Objects.equals(resStoreAgentTaskDTO.getTaskNo(),agentTaskRewardDTO.getTaskNo()) && Objects.equals(resStoreAgentTaskDTO.getStoreNo(),agentTaskRewardDTO.getStoreNo())).forEach(
                        s-> resStoreAgentTaskDTO.setAgentRewards(this.queryAgentRewardNames(stringBuilder,s)));

                return resStoreAgentTaskDTO;
            }).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(resStoreAgentTaskDTOList)){
            resStoreAgentTaskDTOList.forEach(agentTaskDTO -> {
                //未开始
                if (agentTaskDTO.getTaskStartTime().isAfter(LocalDate.now())) {
                    agentTaskDTO.setTaskStatus(TaskTimeStatusEnum.TASK_NO.getCode());
                }  else if(agentTaskDTO.getTaskEndTime().isBefore(LocalDate.now())) {
                    //已结束
                    agentTaskDTO.setTaskStatus(TaskTimeStatusEnum.TASK_YES.getCode());
                }else{
                    //进行中
                    agentTaskDTO.setTaskStatus(TaskTimeStatusEnum.TASK_IN.getCode());
                }

            });
        }
        //查询店铺名称
        ExecuteDTO<List<GetStoreListResponse>> storeDTO =userPublicService.queryStoreList(storeNoList);
        if(storeDTO.successFlag() && CollectionUtils.isNotEmpty(storeDTO.getData())){
            resStoreAgentTaskDTOList.forEach(resDTO->{
                Optional<GetStoreListResponse> optional = storeDTO.getData().stream().filter(store-> resDTO.getStoreNo().equals(store.getStoreNo())).findFirst();
                if(optional.isPresent()){
                    resDTO.setStoreName(optional.get().getStoreName());
                }
            });
        }

        //查询参与代理人数
        if (CollectionUtils.isNotEmpty(resStoreAgentTaskDTOList)){
            AtomReqStoreAgentRewardDTO reqStoreAgentRewardDTO = new AtomReqStoreAgentRewardDTO();
            reqStoreAgentRewardDTO.setStoreNoList(storeNoList);
            reqStoreAgentRewardDTO.setTaskNoList(taskNoList);
            ExecuteDTO<List<AtomResStoreTaskCollectDTO>>  listExecuteDTO = atomAgentTaskAnalysisService.getStoreTaskJoinAgentNum(reqStoreAgentRewardDTO);
            if (!listExecuteDTO.successFlag()) {
                return ExecuteDTO.error(listExecuteDTO.getStatus(), listExecuteDTO.getMsg());
            }
            //参数转换
            List<AtomResStoreTaskCollectDTO> atomResJoinTaskNumDTOS = listExecuteDTO.getData();
            if (CollectionUtils.isNotEmpty(atomResJoinTaskNumDTOS)){
                resStoreAgentTaskDTOList = resStoreAgentTaskDTOList.stream().map(resStoreAgentTaskDTO -> {
                    atomResJoinTaskNumDTOS.stream().filter(resJoinTaskNumDTO->
                            Objects.equals(resStoreAgentTaskDTO.getStoreNo(),resJoinTaskNumDTO.getStoreNo()) && Objects.equals(resStoreAgentTaskDTO.getTaskNo(),resJoinTaskNumDTO.getTaskNo())).forEach(
                            s-> resStoreAgentTaskDTO.setJoinAgentNum(s.getJoinAgentNum()));
                    return resStoreAgentTaskDTO;
                }).collect(Collectors.toList());
            }
        }

        //查询完成总数
        if (CollectionUtils.isNotEmpty(resStoreAgentTaskDTOList)){
            //完成任务总数
            AtomReqJoinTaskNumDTO reqJoinTaskNumDTO = new AtomReqJoinTaskNumDTO();
            reqJoinTaskNumDTO.setStoreNoList(storeNoList);
            reqJoinTaskNumDTO.setTaskNoList(taskNoList);
            ExecuteDTO<List<AtomResJoinTaskNumDTO>> listExecuteDTO = atomAgentRewardRecordAnalysisService.getStorePullNewTaskFansNum(reqJoinTaskNumDTO);
            if (!listExecuteDTO.successFlag()) {
                return ExecuteDTO.error(listExecuteDTO.getStatus(), listExecuteDTO.getMsg());
            }
            //参数转换
            List<AtomResJoinTaskNumDTO> atomResJoinTaskNumDTOS = listExecuteDTO.getData();
            if (CollectionUtils.isNotEmpty(atomResJoinTaskNumDTOS)){
                resStoreAgentTaskDTOList = resStoreAgentTaskDTOList.stream().map(resStoreAgentTaskDTO -> {
                    atomResJoinTaskNumDTOS.stream().filter(resJoinTaskNumDTO->
                            Objects.equals(resStoreAgentTaskDTO.getStoreNo(),resJoinTaskNumDTO.getStoreNo()) && Objects.equals(resStoreAgentTaskDTO.getTaskNo(),resJoinTaskNumDTO.getTaskNo())).forEach(
                            s-> resStoreAgentTaskDTO.setMemberNum(s.getFansNum()));
                    return resStoreAgentTaskDTO;
                }).collect(Collectors.toList());
            }
        }

        executePageDTO.setRows(resStoreAgentTaskDTOList);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    @Override
    public ExecuteDTO<ExecutePageDTO<ResAgentTaskDTO>> getTaskRewardListByParams(ReqAgentTaskDTO reqAgentTaskDTO) {
        AtomReqAgentTaskDTO atomReqAgentTaskDTO = BeanCopierUtil.copy(reqAgentTaskDTO, AtomReqAgentTaskDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResAgentTaskDTO>> executeDTO = atomAgentTaskAnalysisService.getTaskRewardListByParams(atomReqAgentTaskDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ExecutePageDTO<ResAgentTaskDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResAgentTaskDTO> resAgentTaskDTOList = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResAgentTaskDTO.class);
        getTaskTimeStatus(resAgentTaskDTOList);
        getTaskReward(resAgentTaskDTOList, reqAgentTaskDTO.getAgentNo(), Arrays.asList(RewardStatusFlagEnum.THREE.getCode()));
        executePageDTO.setRows(resAgentTaskDTOList);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    /**
     * 转换酬劳类型文字
     * @param rewardSets 酬劳类型聚合
     * @return
     */
    private String queryRewardSetTypeNames(String rewardSets){

        if (StringUtils.isNotBlank(rewardSets)){
            List<String> rewardSetList = new ArrayList<>(Arrays.asList(rewardSets.split(",")));
            List<String> rewardSetTypeNameList = new ArrayList<>();
            rewardSetList.forEach(s-> rewardSetTypeNameList.add(RewardTypeEnum.getByCode(Integer.parseInt(s)).getType()));
            return  rewardSetTypeNameList.stream().collect(Collectors.joining(","));
        }
        return "";
    }
    /**
      * @param rewardSets 获取初始化的酬劳值
      * @Description : 根据参数查询
      * <AUTHOR> 高繁
      * @date : 2021/3/5 16:27
     */
    private String queryRewardvalueInit(String rewardSets){
        StringBuilder stringBuilder = new StringBuilder();
        if (StringUtils.isNotBlank(rewardSets)){
            List<String> rewardSetList = new ArrayList<>(Arrays.asList(rewardSets.split(",")));
            rewardSetList.forEach(s->
                    {
                        switch (RewardTypeEnum.getByCode(Integer.parseInt(s))){
                            case YJ :
                                stringBuilder.append(NumConstant.ZERO).append(" 元,");
                                break;
                            case HJB:
                                stringBuilder.append(NumConstant.ZERO).append(" 个,");
                                break;
                            default:
                                stringBuilder.append(NumConstant.ZERO).append(" 个,");
                                break;
                        }
                    }
            );
            stringBuilder.deleteCharAt(stringBuilder.length()-1);
            return  stringBuilder.toString();
        }
        return "";
    }

    /**
     * @Description : 酬劳单位转换
     * <AUTHOR> 高繁
     * @date : 2021/1/25 16:28
     */
    private String queryAgentRewardNames(StringBuilder stringBuilder, AtomResAgentTaskRewardDTO agentTaskRewardDTO){

        switch (RewardTypeEnum.getByCode(agentTaskRewardDTO.getRewardType())){
            case YJ :
                stringBuilder.append(agentTaskRewardDTO.getRewardValue()).append(" 元,");
                break;
            case HJB:
                stringBuilder.append(agentTaskRewardDTO.getRewardValue()).append(" 个,");
                break;
            default:
                stringBuilder.append(agentTaskRewardDTO.getRewardValue()).append(" 个,");
                break;
        }
        stringBuilder.deleteCharAt(stringBuilder.length()-1);
        return stringBuilder.toString();
    }
}
