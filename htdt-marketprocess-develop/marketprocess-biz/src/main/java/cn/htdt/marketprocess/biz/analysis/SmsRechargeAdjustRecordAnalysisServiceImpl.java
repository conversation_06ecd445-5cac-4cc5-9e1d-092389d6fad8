package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketprocess.api.analysis.SmsRechargeAdjustRecordAnalysisService;
import cn.htdt.marketprocess.dto.request.ReqSmsRechargeAdjustRecordDTO;
import cn.htdt.marketprocess.dto.response.ResSmsRechargeAdjustRecordDTO;
import cn.htdt.marketprocess.legacycenter.biz.analysis.AtomSmsRechargeAdjustRecordAnalysisServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * <p>
 * 短信条数调整操作记录表 数据查询服务接口实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-19
 */
@Slf4j
@DubboService
public class SmsRechargeAdjustRecordAnalysisServiceImpl implements SmsRechargeAdjustRecordAnalysisService {

    @Resource
    private AtomSmsRechargeAdjustRecordAnalysisServiceImpl atomSmsRechargeAdjustRecordAnalysisService;

    @Override
    public ExecuteDTO<ExecutePageDTO<ResSmsRechargeAdjustRecordDTO>> getSmsRechargeAdjustRecordListByParam(ReqSmsRechargeAdjustRecordDTO reqDTO) {
        log.info("-------SmsRechargeAdjustRecordAnalysisServiceImpl-->getSmsRechargeAdjustRecordListByParam,分页查询短信充值调整记录列表--start----");
        ExecuteDTO<ExecutePageDTO<ResSmsRechargeAdjustRecordDTO>> smsAdjustRecordExecuteDTO = atomSmsRechargeAdjustRecordAnalysisService.getSmsRechargeAdjustRecordListByParam(reqDTO);
        if (!smsAdjustRecordExecuteDTO.successFlag()) {
            return ExecuteDTO.error(smsAdjustRecordExecuteDTO.getStatus(), smsAdjustRecordExecuteDTO.getMsg());
        }

        log.info("-------SmsRechargeAdjustRecordAnalysisServiceImpl-->getSmsRechargeAdjustRecordListByParam,分页查短信充值调整记录列表----");
        return smsAdjustRecordExecuteDTO;
    }
}
