package cn.htdt.marketprocess.biz.operat;

import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.generator.MarketFormGenerator;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.ListUtil;
import cn.htdt.marketcenter.dto.request.AtomReqPromotionInfoDTO;
import cn.htdt.marketprocess.biz.api.GoodsPromotionMultiLevelService;
import cn.htdt.marketprocess.dao.GoodsPromotionMultiLevelDao;
import cn.htdt.marketprocess.domain.GoodsPromotionMultiLevelDomain;
import cn.htdt.marketprocess.dto.request.ReqFullDiscountInfoMultiLevelDTO;
import cn.htdt.marketprocess.dto.response.ResFullDiscountInfoMultiLevelDTO;
import cn.htdt.marketprocess.legacycenter.biz.operat.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 商品促销活动多档位表
 *
 * <AUTHOR>
 * @date 2022-10-26
 */
@Slf4j
@DubboService
public class GoodsPromotionMultiLevelServiceImpl extends BaseServiceImpl<GoodsPromotionMultiLevelDomain> implements GoodsPromotionMultiLevelService {

    @Resource
    private GoodsPromotionMultiLevelDao goodsPromotionMultiLevelDao;

    /**
     * @param multiLevelDTOS List<ReqFullDiscountInfoMultiLevelDTO>
     * @return int
     * @Description 批量保存活根据活动编号查询商品促销活动多档位表
     * <AUTHOR>
     */
    @Override
    public int batchSaveMultiLevels(List<ReqFullDiscountInfoMultiLevelDTO> multiLevelDTOS, AtomReqPromotionInfoDTO reqPromotionInfoDTO) {

        List<GoodsPromotionMultiLevelDomain> promotionMultiLevelDomains = convertToDomain(multiLevelDTOS, reqPromotionInfoDTO);
        if (ListUtil.isNotEmpty(promotionMultiLevelDomains)) {
            // 数据入库
            this.saveBatch(promotionMultiLevelDomains);
        }
        // 返回结果
        return ListUtil.getSize(multiLevelDTOS);
    }

    private List<GoodsPromotionMultiLevelDomain> convertToDomain(List<ReqFullDiscountInfoMultiLevelDTO> multiLevelDTOS, AtomReqPromotionInfoDTO reqPromotionInfoDTO) {
        if (StringUtils.isNotBlank(reqPromotionInfoDTO.getPromotionNo()) && ListUtil.isNotEmpty(multiLevelDTOS)) {
            // 入参转换
            List<GoodsPromotionMultiLevelDomain> multiLevelDomainList = BeanCopierUtil.copyList(multiLevelDTOS, GoodsPromotionMultiLevelDomain.class);
            for (GoodsPromotionMultiLevelDomain multiLevelDomain : multiLevelDomainList) {
                if (StringUtils.isBlank(multiLevelDomain.getMultiLevelNo())) {
                    multiLevelDomain.setMultiLevelNo(MarketFormGenerator.genGoodsPeriodNo());
                }
                multiLevelDomain.setPromotionNo(reqPromotionInfoDTO.getPromotionNo());
                multiLevelDomain.setCreateName(reqPromotionInfoDTO.getCreateName());
                multiLevelDomain.setCreateNo(reqPromotionInfoDTO.getCreateNo());
                multiLevelDomain.setModifyName(reqPromotionInfoDTO.getModifyName());
                multiLevelDomain.setModifyNo(reqPromotionInfoDTO.getModifyNo());
                multiLevelDomain.setStoreNo(reqPromotionInfoDTO.getStoreNo());
                multiLevelDomain.setStoreName(reqPromotionInfoDTO.getStoreName());
                multiLevelDomain.setMerchantNo(reqPromotionInfoDTO.getMerchantNo());
                multiLevelDomain.setMerchantName(reqPromotionInfoDTO.getMerchantName());
                multiLevelDomain.setDeleteFlag(WhetherEnum.NO.getCode());
            }
            return multiLevelDomainList;
        }
        // 返回结果
        return Collections.emptyList();
    }

    /**
     * @param multiLevelDTOS List<ReqFullDiscountInfoMultiLevelDTO>
     * @return int
     * @Description 批量更新
     * <AUTHOR>
     */
    @Override
    public int batchUpdateMultiLevels(List<ReqFullDiscountInfoMultiLevelDTO> multiLevelDTOS, AtomReqPromotionInfoDTO reqPromotionInfoDTO) {
        if (StringUtils.isNotBlank(reqPromotionInfoDTO.getPromotionNo()) && ListUtil.isNotEmpty(multiLevelDTOS)) {
            // 入参转换
            List<GoodsPromotionMultiLevelDomain> promotionMultiLevelDomains = convertToDomain(multiLevelDTOS, reqPromotionInfoDTO);
            // 先删后插
            QueryWrapper<GoodsPromotionMultiLevelDomain> wrapper = new QueryWrapper<>();
            wrapper.eq("promotion_no", reqPromotionInfoDTO.getPromotionNo())
                    .eq("delete_flag", WhetherEnum.NO.getCode());
            goodsPromotionMultiLevelDao.delete(wrapper);
            // 数据入库
            this.saveBatch(promotionMultiLevelDomains);
        }

        return ListUtil.getSize(multiLevelDTOS);
    }

    /**
     * @param promotionNo String
     * @return List<ResFullDiscountInfoMultiLevelDTO> multiLeveldtos
     * @Description 根据活动编号查询商品促销活动多档位表
     * <AUTHOR>
     */
    @Override
    public List<ResFullDiscountInfoMultiLevelDTO> getMultiLevelsByPromotionNo(String promotionNo) {
        QueryWrapper<GoodsPromotionMultiLevelDomain> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("promotion_no", promotionNo)
                .eq("delete_flag", WhetherEnum.NO.getCode());
        List<GoodsPromotionMultiLevelDomain> multiLevelDomains = goodsPromotionMultiLevelDao.selectList(queryWrapper);
        if (ListUtil.isNotEmpty(multiLevelDomains)) {
            return BeanCopierUtil.copyList(multiLevelDomains, ResFullDiscountInfoMultiLevelDTO.class);
        } else {
            return Collections.emptyList();
        }
    }

    /**
     * @param promotionNos String
     * @return List<ResFullDiscountInfoMultiLevelDTO> multiLeveldtos
     * @Description 根据活动编号查询商品促销活动多档位表
     * <AUTHOR>
     */
    @Override
    public List<ResFullDiscountInfoMultiLevelDTO> getMultiLevelsByPromotionNos(List<String> promotionNos) {
        QueryWrapper<GoodsPromotionMultiLevelDomain> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("promotion_no", promotionNos)
                .eq("delete_flag", WhetherEnum.NO.getCode());
        List<GoodsPromotionMultiLevelDomain> multiLevelDomains = goodsPromotionMultiLevelDao.selectList(queryWrapper);
        if (ListUtil.isNotEmpty(multiLevelDomains)) {
            return BeanCopierUtil.copyList(multiLevelDomains, ResFullDiscountInfoMultiLevelDTO.class);
        } else {
            return Collections.emptyList();
        }
    }

}
