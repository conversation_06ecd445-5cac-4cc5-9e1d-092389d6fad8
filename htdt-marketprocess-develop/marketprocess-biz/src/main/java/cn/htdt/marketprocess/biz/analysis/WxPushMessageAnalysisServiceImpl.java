package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.enums.market.PushNoticeTypeEnum;
import cn.htdt.common.enums.market.WxPushMsgCategoryEnum;
import cn.htdt.marketprocess.api.analysis.WxPushMessageAnalysisService;
import cn.htdt.marketprocess.biz.conversion.WxPushMsgAssert;
import cn.htdt.marketprocess.dao.AppletMsgTemplateDao;
import cn.htdt.marketprocess.domain.AppletMsgTemplateDomain;
import cn.htdt.marketprocess.dto.request.ReqWxPushMsgTemplateDTO;
import cn.htdt.marketprocess.vo.AppletMsgTemplateVO;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 微信消息推送 数据查询服务接口实现类
 *
 * <AUTHOR>
 * @date 2021/8/2
 **/
@DubboService
@Slf4j
public class WxPushMessageAnalysisServiceImpl implements WxPushMessageAnalysisService {

//    @Value("${WxTemplate.event-reservation}")
//    private String eventReservationTemplate;
//
//    @Value("${WxTemplate.time-buy}")
//    private String timeBuyTemplate;
//
//    @Value("${WxTemplate.pre-sale}")
//    private String preSaleTemplate;
//
//    @Value("${WxTemplate.unpaid}")
//    private String unpaidTemplate;
//
//    @Value("${WxTemplate.pay-success}")
//    private String paySuccessTemplate;
//
//    @Value("${WxTemplate.order-deliver-goods}")
//    private String orderDeliverGoodsTemplate;
//
//    @Value("${WxTemplate.refund-audit-failed}")
//    private String refundAuditFailedTemplate;
//
//    @Value("${WxTemplate.refund-success}")
//    private String refundSuccessTemplate;
//
//    @Value("${WxTemplate.refund-failed}")
//    private String refundFailedTemplate;
//
//    @Value("${WxTemplate.distribution-commission}")
//    private String distributionCommissionTemplate;

    @Resource
    private WxPushMsgAssert wxPushMsgAssert;

    @Resource
    private AppletMsgTemplateDao appletMsgTemplateDao;

    /**
     * HXG -> 获取微信消息模板集合
     *
     * @param reqDTO 查询参数
     * @return ExecuteDTO<List < String>>
     * <AUTHOR>
     */
    @Override
    public ExecuteDTO<List<String>> getWxMsgTemplateList(ReqWxPushMsgTemplateDTO reqDTO) {
        log.info("-------WxPushMessageAnalysisServiceImpl-->getWxMsgTemplateList,获取微信消息模板集合--start----");

        wxPushMsgAssert.getWxMsgTemplateListAssert(reqDTO);
        List<String> templateList = new ArrayList<>();
        AppletMsgTemplateVO appletMsgTemplate = new AppletMsgTemplateVO();
        appletMsgTemplate.setAppId(reqDTO.getAppId());
        List<String> noticeTypes = new ArrayList<>();
        if (WxPushMsgCategoryEnum.EVENT_RESERVATION.getCode().equals(reqDTO.getMsgCategory())) {
            //秒杀提醒模板
            noticeTypes.add(PushNoticeTypeEnum.EVENT_RESERVATION_SECOND_KILL.getCode());
        } else if (WxPushMsgCategoryEnum.ORDER.getCode().equals(reqDTO.getMsgCategory())) {
            //订单提醒模板
            noticeTypes.add(PushNoticeTypeEnum.UNPAID.getCode());
            noticeTypes.add(PushNoticeTypeEnum.PAY_SUCCESS.getCode());
            noticeTypes.add(PushNoticeTypeEnum.ORDER_DELIVER_GOODS.getCode());
        } else if (WxPushMsgCategoryEnum.REFUND.getCode().equals(reqDTO.getMsgCategory())) {
            //退款提醒模板
            noticeTypes.add(PushNoticeTypeEnum.REFUND_AUDIT_FAILED.getCode());
            noticeTypes.add(PushNoticeTypeEnum.REFUND_SUCCESS.getCode());
            noticeTypes.add(PushNoticeTypeEnum.REFUND_FAILED.getCode());
        } else if (WxPushMsgCategoryEnum.AGENT_COMMISSION.getCode().equals(reqDTO.getMsgCategory())) {
            //代理人佣金
            noticeTypes.add(PushNoticeTypeEnum.DISTRIBUTION_COMMISSION.getCode());
        } else if (WxPushMsgCategoryEnum.TIME_BUY.getCode().equals(reqDTO.getMsgCategory())) {
            //限时购提醒模板
            noticeTypes.add(PushNoticeTypeEnum.EVENT_RESERVATION_LIMIT_TIME.getCode());
        } else if (WxPushMsgCategoryEnum.PRE_SALE.getCode().equals(reqDTO.getMsgCategory())) {
            // 商品预售提醒模板
            noticeTypes.add(PushNoticeTypeEnum.EVENT_RESERVATION_PRE_SALE.getCode());
        }
        appletMsgTemplate.setNoticeTypes(noticeTypes);
        List<AppletMsgTemplateDomain> appletMsgTemplateDomains = appletMsgTemplateDao.selectAppletMsgTemplates(appletMsgTemplate);
        log.info("-------WxPushMessageAnalysisServiceImpl-->getWxMsgTemplateList,获取微信消息模板集合----");
        return ExecuteDTO.success(appletMsgTemplateDomains.stream().map(AppletMsgTemplateDomain::getTemplateId).collect(Collectors.toList()));
    }

    /**
     * 获取模板ID
     *
     * @param template 模板参数
     * @return String
     */
    private String getTemplateId(String template) {
        JSONObject jsonObject = JSONObject.parseObject(template);
        return (String) jsonObject.get("templateId");
    }
}
