## 整合rabbitmq说明

### 加入依赖
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-amqp</artifactId>
</dependency>

### nacos加配置，加在spring：下，以下例子是dev环境
  # MQ
  rabbitmq:
    host: ************
    port: 5672
    username: guest
    password: guest
    virtual-host: /

### 包说明
* config：配置rabbitmq的配置和mq的连接配置
* consumer：消息消费类
* dto：消息类，也可以用自己项目的api的dto
* listenter：配置延迟队列的监听
* send：消息发送类

### 需要自己自定义
* config包
* 非延迟不需要listenter
* 非实体类不需要dto


```