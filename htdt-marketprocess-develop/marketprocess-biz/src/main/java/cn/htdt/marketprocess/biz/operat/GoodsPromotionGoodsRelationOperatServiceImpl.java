package cn.htdt.marketprocess.biz.operat;

import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.enums.GoodsErrorCode;
import cn.htdt.common.dto.enums.MarketErrorCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.constants.PromotionKeyConstant;
import cn.htdt.common.enums.market.PromotionTypeEnum;
import cn.htdt.common.redis.utils.RedisUtil;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqGoodsPromotionGoodsRelationDTO;
import cn.htdt.marketcenter.dto.response.AtomResGoodsPromotionGoodsRelationDTO;
import cn.htdt.marketprocess.api.operat.GoodsPromotionGoodsRelationOperatService;
import cn.htdt.marketprocess.biz.conversion.GoodsPromotionAssert;
import cn.htdt.marketprocess.dao.GoodsPromotionGoodsRelationDao;
import cn.htdt.marketprocess.dto.request.ReqGoodsPromotionCheckDTO;
import cn.htdt.marketprocess.dto.request.ReqGoodsPromotionGoodsRelationDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomGoodsPromotionGoodsRelationAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomGoodsPromotionGoodsRelationOperateService;
import cn.htdt.marketprocess.vo.AtomGoodsPromotionGoodsRelationVo;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/7 10:15
 */
@Slf4j
@DubboService
public class GoodsPromotionGoodsRelationOperatServiceImpl implements GoodsPromotionGoodsRelationOperatService {

    @Resource
    private AtomGoodsPromotionGoodsRelationOperateService atomGoodsPromotionGoodsRelationOperateService;

    @Resource
    private AtomGoodsPromotionGoodsRelationAnalysisService atomGoodsPromotionGoodsRelationAnalysisService;
    @Autowired
    private GoodsPromotionAssert goodsPromotionAssert;
    @Resource
    private GoodsPromotionGoodsRelationDao goodsPromotionGoodsRelationDao;

    @Autowired
    private RedisUtil redisUtil;
    /**
     * 促销商品可用库存 promotion:goods:available:stock:活动编号:商品编号
     */
    private static final String PROMOTION_GOODS_STOCK_PREFIX = PromotionKeyConstant.PROMOTION_GOODS_STOCK_PREFIX;

    /**
     * @param reqGoodsPromotionCheckDTO
     * @Description : 促销活动商品库存检查->扣减
     * <AUTHOR> 高繁
     * @date : 2021/7/19 11:14
     */
    @Override
    public ExecuteDTO promotionRuleCheck(ReqGoodsPromotionCheckDTO reqGoodsPromotionCheckDTO) {
        log.info(String.format("促销活动商品库存检查->扣减入参:%s", JSON.toJSONString(reqGoodsPromotionCheckDTO)));
        goodsPromotionAssert.promotionRuleCheck(reqGoodsPromotionCheckDTO);
     /*   String redisLockKey = PromotionKeyConstant.EDIT_SECOND_SKILL_PROMOTION_LOCK + reqGoodsPromotionCheckDTO.getPromotionNo();
        if (redisUtil.setnx(redisLockKey, "1", RedisKeyTTLConstant.EXPIRE_SECOND_30)){

        }*/
        AtomReqGoodsPromotionGoodsRelationDTO relationDTO = BeanCopierUtil.copy(reqGoodsPromotionCheckDTO, AtomReqGoodsPromotionGoodsRelationDTO.class);
        ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> executeDTO = atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList(relationDTO);
        if (!executeDTO.successFlag() || CollectionUtils.isEmpty(executeDTO.getData())) {
            return ExecuteDTO.error(GoodsErrorCode.CODE_12000001);
        }
        //取一个商品
        AtomResGoodsPromotionGoodsRelationDTO resGoodsPromotionGoodsRelationDTO = executeDTO.getData().get(NumConstant.ZERO);
        //获取促销商品库存
        if (!redisUtil.hasKey(String.format(PROMOTION_GOODS_STOCK_PREFIX,resGoodsPromotionGoodsRelationDTO.getPromotionNo(),resGoodsPromotionGoodsRelationDTO.getPeriodNo(),resGoodsPromotionGoodsRelationDTO.getGoodsNo()))){
            redisUtil.set(String.format(PROMOTION_GOODS_STOCK_PREFIX,resGoodsPromotionGoodsRelationDTO.getPromotionNo(),resGoodsPromotionGoodsRelationDTO.getPeriodNo(),resGoodsPromotionGoodsRelationDTO.getGoodsNo()), String.valueOf(resGoodsPromotionGoodsRelationDTO.getRemainStockNum()));
        }

        if (redisUtil.decr(String.format(PROMOTION_GOODS_STOCK_PREFIX,resGoodsPromotionGoodsRelationDTO.getPromotionNo(),resGoodsPromotionGoodsRelationDTO.getPeriodNo(),resGoodsPromotionGoodsRelationDTO.getGoodsNo()),reqGoodsPromotionCheckDTO.getBuyGoodsNum()) < NumConstant.ZERO){
            log.info("商品可售活动库存不足");
            redisUtil.incr(String.format(PROMOTION_GOODS_STOCK_PREFIX,resGoodsPromotionGoodsRelationDTO.getPromotionNo(),resGoodsPromotionGoodsRelationDTO.getPeriodNo(),resGoodsPromotionGoodsRelationDTO.getGoodsNo()),reqGoodsPromotionCheckDTO.getBuyGoodsNum());
            // 限时购活动
            if (PromotionTypeEnum.LIMIT_TIME.getCode().equals(resGoodsPromotionGoodsRelationDTO.getPromotionType())) {
                // 提示：抱歉，秒杀活动异常火爆，当前库存已售罄~
                return ExecuteDTO.error(MarketErrorCode.CODE_17000625);
            } else if (PromotionTypeEnum.PRE_SALE.getCode().equals(resGoodsPromotionGoodsRelationDTO.getPromotionType())) {
                // 提示：抱歉，秒杀活动异常火爆，当前库存已售罄~
                return ExecuteDTO.error(MarketErrorCode.CODE_17000632);
            } else {
                // 默认按秒杀活动提示：抱歉，秒杀活动异常火爆，当前库存已售罄~
                return ExecuteDTO.error(MarketErrorCode.CODE_17000619);
            }
        }else {
            //数据库库存扣减
            AtomReqGoodsPromotionGoodsRelationDTO atomReqGoodsPromotionGoodsRelationDTO = BeanCopierUtil.copy(reqGoodsPromotionCheckDTO,AtomReqGoodsPromotionGoodsRelationDTO.class);
            ExecuteDTO goodsStockReduce = atomGoodsPromotionGoodsRelationOperateService.promotionGoodsStockReduce(atomReqGoodsPromotionGoodsRelationDTO);
            log.info(String.format("促销商品库存扣减结果%s",JSON.toJSONString(goodsStockReduce)));
            if (!goodsStockReduce.successFlag()){
                redisUtil.incr(String.format(PROMOTION_GOODS_STOCK_PREFIX,resGoodsPromotionGoodsRelationDTO.getPromotionNo(),resGoodsPromotionGoodsRelationDTO.getPeriodNo(),resGoodsPromotionGoodsRelationDTO.getGoodsNo()),reqGoodsPromotionCheckDTO.getBuyGoodsNum());
                return ExecuteDTO.error(CommonCode.CODE_10000003,"活动商品库存扣减异常");
            }
        }
        return ExecuteDTO.success();
    }

    /**
     * @param reqGoodsPromotionGoodsRelationDTO
     * @Description : 促销商品库存扣减
     * <AUTHOR> 高繁
     * @date : 2021/7/6 14:41
     */
    @Override
    public ExecuteDTO promotionGoodsStockReduce(ReqGoodsPromotionGoodsRelationDTO reqGoodsPromotionGoodsRelationDTO) {
        log.info(String.format("促销商品库存扣减入参:%s", JSON.toJSONString(reqGoodsPromotionGoodsRelationDTO)));
        goodsPromotionAssert.promotionGoodsStockReduce(reqGoodsPromotionGoodsRelationDTO);
        AtomReqGoodsPromotionGoodsRelationDTO atomReqGoodsPromotionGoodsRelationDTO = BeanCopierUtil.copy(reqGoodsPromotionGoodsRelationDTO,AtomReqGoodsPromotionGoodsRelationDTO.class);
        ExecuteDTO executeDTO = atomGoodsPromotionGoodsRelationOperateService.promotionGoodsStockReduce(atomReqGoodsPromotionGoodsRelationDTO);
        log.info(String.format("促销商品库存扣减结果%s",JSON.toJSONString(executeDTO)));
        return executeDTO;
    }
    /**
     * @param reqGoodsPromotionGoodsRelationDTO
     * @Description : 促销商品库存增加
     * <AUTHOR> 高繁
     * @date : 2021/8/2 10:35
     */
    @Override
    public ExecuteDTO promotionGoodsStockAdd(ReqGoodsPromotionGoodsRelationDTO reqGoodsPromotionGoodsRelationDTO) {
        log.info("促销商品库存增加入参:{}", JSON.toJSONString(reqGoodsPromotionGoodsRelationDTO));
        goodsPromotionAssert.promotionGoodsStockReduce(reqGoodsPromotionGoodsRelationDTO);
        AtomReqGoodsPromotionGoodsRelationDTO atomReqGoodsPromotionGoodsRelationDTO = BeanCopierUtil.copy(reqGoodsPromotionGoodsRelationDTO,AtomReqGoodsPromotionGoodsRelationDTO.class);
        ExecuteDTO executeDTO = atomGoodsPromotionGoodsRelationOperateService.promotionGoodsStockAdd(atomReqGoodsPromotionGoodsRelationDTO);
        log.info("促销商品库存增加出参:{}",JSON.toJSONString(executeDTO));
        if (executeDTO.successFlag()){
            if (redisUtil.hasKey(String.format(PROMOTION_GOODS_STOCK_PREFIX,reqGoodsPromotionGoodsRelationDTO.getPromotionNo(),reqGoodsPromotionGoodsRelationDTO.getPeriodNo(),reqGoodsPromotionGoodsRelationDTO.getGoodsNo()))){
                redisUtil.incr(String.format(PROMOTION_GOODS_STOCK_PREFIX,reqGoodsPromotionGoodsRelationDTO.getPromotionNo(),reqGoodsPromotionGoodsRelationDTO.getPeriodNo(),reqGoodsPromotionGoodsRelationDTO.getGoodsNo()), reqGoodsPromotionGoodsRelationDTO.getBuyGoodsNum());
            }
        }
        return executeDTO;
    }

    /**
     * 因千橙掌柜收银改价单位换算（斤与公斤），修改特惠促销活动商品价格
     *
     * @param reqGoodsPromotionGoodsRelationDTO
     * @return ExecuteDTO
     * <AUTHOR>
     */
    @Override
    public ExecuteDTO modifyPromotionGoodsPrice(ReqGoodsPromotionGoodsRelationDTO reqGoodsPromotionGoodsRelationDTO) {
        log.info("**modifyPromotionGoodsPrice-入参：{}", reqGoodsPromotionGoodsRelationDTO);
        // 入参校验
        goodsPromotionAssert.modifyPromotionGoodsPriceAssert(reqGoodsPromotionGoodsRelationDTO);

        // 入参转换
        AtomGoodsPromotionGoodsRelationVo vo = BeanCopierUtil.copy(reqGoodsPromotionGoodsRelationDTO, AtomGoodsPromotionGoodsRelationVo.class);
        // 删除活动关联的商品数据
        goodsPromotionGoodsRelationDao.updatePromotionGoodsVipPrice(vo);

        // 返回结果
        return ExecuteDTO.success();
    }
}
