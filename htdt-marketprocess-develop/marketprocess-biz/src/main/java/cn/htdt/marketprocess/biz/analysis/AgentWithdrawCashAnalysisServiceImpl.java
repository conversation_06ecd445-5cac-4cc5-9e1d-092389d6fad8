package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqAgentWithdrawCashDTO;
import cn.htdt.marketcenter.dto.response.AtomResAgentWithdrawCashDTO;
import cn.htdt.marketprocess.api.analysis.AgentWithdrawCashAnalysisService;
import cn.htdt.marketprocess.dto.request.ReqAgentWithdrawCashDTO;
import cn.htdt.marketprocess.dto.response.ResAgentWithdrawCashDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomAgentWithdrawCashAnalysisService;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-03-15
 * @description 提现记录查询
 **/
@DubboService
public class AgentWithdrawCashAnalysisServiceImpl implements AgentWithdrawCashAnalysisService {

    @Resource
    private AtomAgentWithdrawCashAnalysisService atomAgentWithdrawCashAnalysisService;

    @Override
    public ExecuteDTO<List<ResAgentWithdrawCashDTO>> getListByParams(ReqAgentWithdrawCashDTO reqAgentWithdrawCashDTO) {
        AtomReqAgentWithdrawCashDTO atomReqAgentWithdrawCashDTO = BeanCopierUtil.copy(reqAgentWithdrawCashDTO, AtomReqAgentWithdrawCashDTO.class);
        ExecuteDTO<List<AtomResAgentWithdrawCashDTO>> executeDTO = atomAgentWithdrawCashAnalysisService.getListByParams(atomReqAgentWithdrawCashDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        List<ResAgentWithdrawCashDTO> resAgentWithdrawCashDTOList = BeanCopierUtil.copyList(executeDTO.getData(), ResAgentWithdrawCashDTO.class);
        return ExecuteDTO.success(resAgentWithdrawCashDTOList);
    }

    @Override
    public ExecuteDTO<ExecutePageDTO<ResAgentWithdrawCashDTO>> getListByPage(ReqAgentWithdrawCashDTO reqAgentWithdrawCashDTO) {
        AtomReqAgentWithdrawCashDTO atomReqAgentWithdrawCashDTO = BeanCopierUtil.copy(reqAgentWithdrawCashDTO, AtomReqAgentWithdrawCashDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResAgentWithdrawCashDTO>> executeDTO = atomAgentWithdrawCashAnalysisService.getListByPage(atomReqAgentWithdrawCashDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ExecutePageDTO<ResAgentWithdrawCashDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResAgentWithdrawCashDTO> resAgentWithdrawCashDTOList = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResAgentWithdrawCashDTO.class);
        executePageDTO.setRows(resAgentWithdrawCashDTOList);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    @Override
    public ExecuteDTO<ResAgentWithdrawCashDTO> getWithdrawStatistics(ReqAgentWithdrawCashDTO reqAgentWithdrawCashDTO) {
        AtomReqAgentWithdrawCashDTO atomReqAgentWithdrawCashDTO = BeanCopierUtil.copy(reqAgentWithdrawCashDTO, AtomReqAgentWithdrawCashDTO.class);
        ExecuteDTO<AtomResAgentWithdrawCashDTO> executeDTO = atomAgentWithdrawCashAnalysisService.getWithdrawStatistics(atomReqAgentWithdrawCashDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResAgentWithdrawCashDTO resAgentWithdrawCashDTO = BeanCopierUtil.copy(executeDTO.getData(), ResAgentWithdrawCashDTO.class);
        return ExecuteDTO.success(resAgentWithdrawCashDTO);
    }

    @Override
    public ExecuteDTO<List<ResAgentWithdrawCashDTO>> getMouthStatistics(ReqAgentWithdrawCashDTO reqAgentWithdrawCashDTO) {
        AtomReqAgentWithdrawCashDTO atomReqAgentWithdrawCashDTO = BeanCopierUtil.copy(reqAgentWithdrawCashDTO, AtomReqAgentWithdrawCashDTO.class);
        ExecuteDTO<List<AtomResAgentWithdrawCashDTO>> executeDTO = atomAgentWithdrawCashAnalysisService.getMouthStatistics(atomReqAgentWithdrawCashDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        List<ResAgentWithdrawCashDTO> resAgentWithdrawCashDTOList = BeanCopierUtil.copyList(executeDTO.getData(), ResAgentWithdrawCashDTO.class);
        return ExecuteDTO.success(resAgentWithdrawCashDTOList);
    }

}
