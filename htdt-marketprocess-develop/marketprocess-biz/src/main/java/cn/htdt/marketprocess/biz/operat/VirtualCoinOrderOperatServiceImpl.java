package cn.htdt.marketprocess.biz.operat;

import cn.htdt.common.base.exception.BaseException;
import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.enums.MarketErrorCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.enums.*;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.constants.StringNumConstant;
import cn.htdt.common.enums.goods.PaymentMethodEnum;
import cn.htdt.common.enums.market.*;
import cn.htdt.common.enums.user.DeleteFlagEnum;
import cn.htdt.common.enums.yjf.YjfTradeStatusEnum;
import cn.htdt.common.generator.MarketFormGenerator;
import cn.htdt.common.utils.*;
import cn.htdt.marketcenter.dto.request.AtomReqVirtualCoinOrderDTO;
import cn.htdt.marketcenter.dto.request.AtomReqVirtualCoinOrderFlowDTO;
import cn.htdt.marketcenter.dto.request.AtomReqVirtualCoinRuleDTO;
import cn.htdt.marketcenter.dto.response.AtomResVirtualCoinOrderDTO;
import cn.htdt.marketcenter.dto.response.AtomResVirtualCoinOrderFlowDTO;
import cn.htdt.marketcenter.dto.response.AtomResVirtualCoinRuleDTO;
import cn.htdt.marketprocess.api.operat.CouponPromotionOperateService;
import cn.htdt.marketprocess.api.operat.VirtualCoinOperatService;
import cn.htdt.marketprocess.api.operat.VirtualCoinOrderOperatService;
import cn.htdt.marketprocess.biz.conversion.VirtualCoinOrderAssert;
import cn.htdt.marketprocess.biz.utils.VirtualCoinUtil;
import cn.htdt.marketprocess.dao.VirtualCoinOrderFlowDao;
import cn.htdt.marketprocess.dao.VirtualCoinRuleDao;
import cn.htdt.marketprocess.domain.VirtualCoinOrderFlowDomain;
import cn.htdt.marketprocess.dto.request.ReqUserVirtualCoinRechargeDTO;
import cn.htdt.marketprocess.dto.request.ReqVirtualCoinOrderDTO;
import cn.htdt.marketprocess.dto.request.ReqVirtualCoinRuleDistributeCouponDTO;
import cn.htdt.marketprocess.dto.request.ReqvcPaymentAsynCallbackDTO;
import cn.htdt.marketprocess.dto.response.ResVirtualCoinOrderDTO;
import cn.htdt.marketprocess.dto.response.ResVirtualCoinOrderFlowDTO;
import cn.htdt.marketprocess.dto.response.ResVirtualCoinRecordDTO;
import cn.htdt.marketprocess.dto.response.ResVirtualCoinRuleAndCouponInfoDetailDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomVirtualCoinOrderAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomVirtualCoinRuleAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomVirtualCoinOrderOperatService;
import cn.htdt.marketprocess.vo.VirtualCoinRuleAndCouponInfoDetailVo;
import cn.htdt.marketprocess.vo.VirtualCoinRuleAndCouponInfoVo;
import cn.htdt.middlewareprocess.api.htd.SmsChannelSendService;
import cn.htdt.middlewareprocess.dto.request.htd.AtomReqSmsChannelDTO;
import cn.htdt.usercenter.dto.request.ReqFancDTO;
import cn.htdt.usercenter.dto.response.ResFancDTO;
import cn.htdt.usercenter.dto.response.ResFansFollowDTO;
import cn.htdt.userprocess.api.LegacyUserCenterService;
import cn.htdt.userprocess.api.MemberLevelProcessService;
import cn.htdt.userprocess.api.UcFansProcessService;
import cn.htdt.userprocess.dto.request.user.ReqAddMemberLevelDTO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/25
 */
@DubboService
@Slf4j
public class VirtualCoinOrderOperatServiceImpl implements VirtualCoinOrderOperatService {
    @Resource
    private VirtualCoinOrderAssert virtualCoinOrderAssert;

    @Resource
    private VirtualCoinOperatService virtualCoinOperatService;

    @Resource
    private AtomVirtualCoinOrderOperatService orderOperatService;

    @Resource
    private AtomVirtualCoinOrderAnalysisService orderAnalysisService;

    @Resource
    private AtomVirtualCoinRuleAnalysisService coinRuleAnalysisService;

    @DubboReference
    private SmsChannelSendService smsChannelSendService;

    @DubboReference
    private UcFansProcessService ucFansProcessService;

    @DubboReference
    private LegacyUserCenterService legacyUserCenterService;

    @DubboReference
    private MemberLevelProcessService memberLevelProcessService;

    /**
     * 优惠券操作服务
     */
    @Resource
    private CouponPromotionOperateService couponPromotionOperateService;

    @Resource
    private VirtualCoinRuleDao virtualCoinRuleDao;
    @Resource
    private VirtualCoinOrderFlowDao virtualCoinOrderFlowDao;

    @Resource
    private VirtualCoinUtil virtualCoinUtil;

    @Override
    public ExecuteDTO<ResVirtualCoinOrderDTO> orderSubmit(ReqVirtualCoinOrderDTO reqDto) {
        // 参数校验
        this.virtualCoinOrderAssert.orderSubmitAssert(reqDto);

        // 20230928蛋品-赵翔宇-商家橙豆-橙豆规则, 店铺查询橙豆规则, 要区分该店铺是否加入共享店铺
        String virtualCoinRuleType = "";
        if (StringUtils.isNotBlank(reqDto.getStoreNo())) {
            virtualCoinRuleType = virtualCoinUtil.getVirtualCoinRuleType(reqDto.getStoreNo(), reqDto.getMerchantNo(), reqDto.getLoginIdentity());
            if (VirtualCoinRuleTypeEnum.MEMBER_SHARING_RULE.getCode().equals(virtualCoinRuleType)
                    && StringUtils.isBlank(reqDto.getVirtualNo())) {
                // 商家橙豆, 不支持自定义购买
                throw new BaseException(MarketErrorCode.CODE_17000909);
            }
            reqDto.setRuleType(virtualCoinRuleType);
        }
        // 校验橙豆规则编号是否有效
        // 查询橙豆规则
        if (StringUtils.isNotBlank(reqDto.getVirtualNo())) {
            AtomReqVirtualCoinRuleDTO queryRuleDto = new AtomReqVirtualCoinRuleDTO();
            queryRuleDto.setVirtualNo(reqDto.getVirtualNo());
            // 20230928蛋品-赵翔宇-商家橙豆-橙豆规则, 店铺未加入共享店铺, 才需要根据店铺编号查询橙豆规则, 否则根据商家编号查询
            queryRuleDto.setRuleType(virtualCoinRuleType);
            if (VirtualCoinRuleTypeEnum.STORE_RULE.getCode().equals(virtualCoinRuleType)) {
                queryRuleDto.setStoreNo(reqDto.getStoreNo());
            }
            queryRuleDto.setMerchantNo(reqDto.getMerchantNo());
            queryRuleDto.setDeleteFlag(DeleteFlagEnum.NO.getCode());
            ExecuteDTO<AtomResVirtualCoinRuleDTO> coinRule = coinRuleAnalysisService.selectVirtualCoinRule(queryRuleDto);
            if (null == coinRule || null == coinRule.getData()) {
                throw new BaseException(CommonCode.CODE_10000002, "橙豆规则");
            }
            AtomResVirtualCoinRuleDTO coinRuleDto = coinRule.getData();
            // 校验支付金额和可用金额是否一致
            if (BigDecimalUtil.notEqual(coinRuleDto.getPayAmount(), reqDto.getPayAmount())
                    || BigDecimalUtil.notEqual(coinRuleDto.getAvailableAmount(), reqDto.getAvailableAmount())) {
                throw new BaseException(CommonCode.CODE_10000002, "橙豆规则");
            }
        }

        // 订单号
        String orderNo = MarketFormGenerator.virtualCoinOrderNo();
        // 交易流水号
        String orderFlowNo = MarketFormGenerator.virtualCoinOrderNo();

        AtomReqVirtualCoinOrderDTO orderDto = BeanCopierUtil.copy(reqDto, AtomReqVirtualCoinOrderDTO.class);

        orderDto.setVirtualOrderNo(orderNo);
        orderDto.setVirtualRuleNo(reqDto.getVirtualNo());
        orderDto.setPaymentMethod(PaymentMethodEnum.SHOPPAY.getCode()); // 到店支付
        orderDto.setCreateNo(reqDto.getUserNo());
        orderDto.setCreateName(reqDto.getUserName());
        orderDto.setPaymentAmount(reqDto.getPayAmount());
        orderDto.setDisableFlag(WhetherEnum.YES.getCode());

        // 20230928蛋品-赵翔宇-商家橙豆-橙豆订单, 要区分该店铺是否加入共享店铺
        orderDto.setRuleType(virtualCoinRuleType);

        AtomReqVirtualCoinOrderFlowDTO flowDto = BeanCopierUtil.copy(reqDto, AtomReqVirtualCoinOrderFlowDTO.class);
        flowDto.setOrderNo(orderNo);
        flowDto.setOutTradeNo(orderFlowNo);
        flowDto.setCreateNo(reqDto.getUserNo());
        flowDto.setCreateName(reqDto.getUserName());
        flowDto.setPaymentAmount(reqDto.getPayAmount());
        flowDto.setRealAmount(reqDto.getPayAmount());
        flowDto.setPaymentMethod(PaymentMethodEnum.SHOPPAY.getCode());
        flowDto.setPayTime(LocalDateTime.now());
        flowDto.setDisableFlag(WhetherEnum.YES.getCode());
        // 如果是现金支付, 20230928蛋品-赵翔宇-商家储值-橙豆充值, 使用小橙码牌支付, 处理逻辑同现金支付
        if (StoreBillingPayTypeEnum.CASH.getCode().equals(reqDto.getPayType())
                || StoreBillingPayTypeEnum.CODE_PLATE.getCode().equals(reqDto.getPayType())) {
            orderDto.setPaymentStatus(OrderPaymentStatusEnum.PAID.getCode());
            flowDto.setPaymentStatus(OrderPaymentStatusEnum.PAID.getCode());
            // 如果是现金支付, 20230928蛋品-赵翔宇-商家储值-橙豆充值, 使用实际的支付渠道
            flowDto.setPaymentChannel(reqDto.getPayType());
        } else if (StoreBillingPayTypeEnum.ALIPAY_WCHAT.getCode().equals(reqDto.getPayType())) {
            if (BigDecimal.ZERO.compareTo(reqDto.getPayAmount()) == NumConstant.ZERO) {
                // 0元支付 默认现金支付
                flowDto.setPaymentChannel(PaymentChannelEnum.CASH.getCode());
                flowDto.setPaymentStatus(OrderPaymentStatusEnum.PAID.getCode());
                orderDto.setPaymentStatus(OrderPaymentStatusEnum.PAID.getCode());
            } else {
                orderDto.setPaymentStatus(OrderPaymentStatusEnum.UNPAID.getCode());
                flowDto.setPaymentStatus(OrderPaymentStatusEnum.UNPAID.getCode());
                flowDto.setEndPayTime(flowDto.getPayTime().plusDays(NumConstant.ONE).minusMinutes(NumConstant.FIVE));
            }
        } else {
            throw new BaseException(CommonCode.CODE_10000002, "支付方式");
        }

        // 保存充值订单 & 充值流水
        ExecuteDTO<Boolean> booleanExecuteDTO = orderOperatService.orderSubmit(orderDto, flowDto);
        if (null != booleanExecuteDTO && booleanExecuteDTO.getData()) {
            if (OrderPaymentStatusEnum.PAID.getCode().equals(orderDto.getPaymentStatus())) {
                // 交易成功，开始橙豆充值
                this.modifyUserVirtualCoin(orderDto, flowDto);
                // 20230928蛋品-桑伟杰-会员等级-等级升级
                ReqAddMemberLevelDTO reqAddMemberLevelDTO = new ReqAddMemberLevelDTO();
                reqAddMemberLevelDTO.setFanNo(reqDto.getFanNo());
                reqAddMemberLevelDTO.setStoreNo(reqDto.getStoreNo());
                reqAddMemberLevelDTO.setMerchantNo(reqDto.getMerchantNo());
                memberLevelProcessService.addMemberLevel(reqAddMemberLevelDTO);
            }
            // 返回结果
            ResVirtualCoinOrderDTO resDto = BeanCopierUtil.copy(reqDto, ResVirtualCoinOrderDTO.class);
            resDto.setOrderNo(orderNo);
            resDto.setOutTradeNo(orderFlowNo);
            return ExecuteDTO.ok(resDto);
        } else {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
    }


    private void modifyUserVirtualCoin(AtomReqVirtualCoinOrderDTO orderDto, AtomReqVirtualCoinOrderFlowDTO flowDto) {
        ReqUserVirtualCoinRechargeDTO reqUserVirtualCoinDTO = BeanCopierUtil.copy(orderDto, ReqUserVirtualCoinRechargeDTO.class);
        reqUserVirtualCoinDTO.setFanNo(orderDto.getFanNo());
        reqUserVirtualCoinDTO.setVirtualNo(orderDto.getVirtualRuleNo());
        reqUserVirtualCoinDTO.setTradeType(VirtualCoinTradeTypeEnum.RECHARGE.getCode());
        reqUserVirtualCoinDTO.setVirtualCoinChange(orderDto.getAvailableAmount());
        reqUserVirtualCoinDTO.setDeleteFlag(NumConstant.ONE);
        reqUserVirtualCoinDTO.setPaymentChannel(flowDto.getPaymentChannel());
        ExecuteDTO<ResVirtualCoinRecordDTO> executeDTO = virtualCoinOperatService.virtualCoinHandle(reqUserVirtualCoinDTO);
        // 发送短信
        if (null != executeDTO && executeDTO.successFlag()) {
            consumeVirtualCoinNotice(orderDto, executeDTO.getData());
        }
    }

    /**
     * 橙豆充值成功发送短信提醒
     * @param orderDto orderDto
     * @param resVirtualCoinRecordDTO resVirtualCoinRecordDTO
     */
    private void consumeVirtualCoinNotice(AtomReqVirtualCoinOrderDTO orderDto, ResVirtualCoinRecordDTO resVirtualCoinRecordDTO) {
        try {
            String virtualOrderNo = orderDto.getVirtualOrderNo();
            if (null != resVirtualCoinRecordDTO) {
                // 查询粉丝门店信息获取粉丝电话
                ResFansFollowDTO fansFollow = getFansFollow(orderDto.getRuleType(), resVirtualCoinRecordDTO);
                if (null != fansFollow) {
                    // 组装短信发送信息
                    AtomReqSmsChannelDTO atomReqSmsChannelDTO = new AtomReqSmsChannelDTO();
                    atomReqSmsChannelDTO.setBusinessSystemEnum(BusinessSystemEnum.CASH);
                    atomReqSmsChannelDTO.setPhoneNum(fansFollow.getPhone());
                    atomReqSmsChannelDTO.setDsPhoneNum(fansFollow.getDsPhone());
                    StringBuilder smsContent = new StringBuilder(String.format(CommonSmsSceneEnum.RECHARGE_VIRTUAL_COIN.getTemplate(),
                            resVirtualCoinRecordDTO.getStoreName(),
                            DateUtil.format(LocalDateTime.now(), "MM-dd HH:mm"),
                            resVirtualCoinRecordDTO.getVirtualCoinChange(),
                            resVirtualCoinRecordDTO.getTotalCoin()));

                    // 橙豆充值成功, 是否需要给粉丝发券
                    String virtualCoinRuleCouponSmsContent = sendVirtualCoinRuleCouponSetting(orderDto, fansFollow);

                    if (StringUtils.isNotBlank(virtualCoinRuleCouponSmsContent)) {
                        // 充值橙豆送优惠券, 橙豆充值成功, 还需要在短信内容中拼接上"您账户到账X张总价值XX元的优惠券，请及时使用"
                        smsContent.append(virtualCoinRuleCouponSmsContent);
                    }

                    atomReqSmsChannelDTO.setContent(smsContent.toString());
                    atomReqSmsChannelDTO.setSendScene(CommonSmsSceneEnum.RECHARGE_VIRTUAL_COIN.getName());
                    ExecuteDTO executeResult = smsChannelSendService.smsChannelSend(atomReqSmsChannelDTO);
                    log.info("橙豆充值短信发送接口, 订单号: {}, 内容: {}, 发送结果: {}", virtualOrderNo, smsContent, executeResult.successFlag());
                    if (!executeResult.successFlag()){
                        log.error("橙豆充值短信发送失败，入参{}", JSON.toJSONString(atomReqSmsChannelDTO));
                    }
                } else {
                    log.error("橙豆充值短信发送失败，订单号{},查询粉丝门店信息失败!", virtualOrderNo);
                }
            } else {
                log.error("橙豆充值短信发送失败，订单号{}!", virtualOrderNo);
            }
        } catch (Exception e) {
            log.error("橙豆充值短信发送失败:", e);
        }
    }

    /**
     * 根据店铺编号和橙豆规则编号, 查询对应的优惠券信息
     *
     * @param storeNo 店铺编号
     * @param merchantNo 商家编号
     * @param virtualNo 橙豆规则编号
     * @param virtualCoinRuleType 橙豆规则类型, 用于区分是商家橙豆还是店铺橙豆
     * @return 优惠券信息
     */
    private List<ResVirtualCoinRuleAndCouponInfoDetailDTO> getVirtualCoinRuleCouponInfo (String storeNo, String merchantNo, String virtualNo, String virtualCoinRuleType) {
        log.info("-VirtualCoinRuleAnalysisServiceImpl-getVirtualCoinRuleCouponInfo-start, storeNo: {}, virtualNo: {}", storeNo, virtualNo);
        VirtualCoinRuleAndCouponInfoVo virtualCoinRuleAndCouponInfoVo = new VirtualCoinRuleAndCouponInfoVo();

        virtualCoinRuleAndCouponInfoVo.setRuleType(virtualCoinRuleType);

        // 券使用期限类型：自用户获取XX天内可用
        virtualCoinRuleAndCouponInfoVo.setCouponPeriodValidity(CouponPeriodValidityEnum.COUPON_PERIOD_VALIDITY_THREE.getCode());

        // 使用渠道：包含门店开单, 目前有三种情况, '1001, 1002', '1001', '1002', 这里查询语句用的是不等于1001
        virtualCoinRuleAndCouponInfoVo.setCouponUseChannel(CouponUseChannelEnum.COUPON_USE_CHANNEL_ONE.getCode());

        if (VirtualCoinRuleTypeEnum.STORE_RULE.getCode().equals(virtualCoinRuleType)) {
            // 优惠券类型：手动发粉丝券
            virtualCoinRuleAndCouponInfoVo.setStoreNo(storeNo);
            virtualCoinRuleAndCouponInfoVo.setPromotionCouponType(PromotionTypeCouponEnum.SHOP_MANUAL_COUPON.getCode());
        } else {
            // 20230928蛋品-赵翔宇-商家橙豆-橙豆规则, 店铺加入共享店铺, 需要和商家一样, 使用商家编号查询数据
            virtualCoinRuleAndCouponInfoVo.setPromotionCouponType(PromotionTypeCouponEnum.MERCHANT_MANUAL_COUPON.getCode());
            virtualCoinRuleAndCouponInfoVo.setStoreNo("");
            virtualCoinRuleAndCouponInfoVo.setMerchantNo(merchantNo);
        }

        virtualCoinRuleAndCouponInfoVo.setVirtualNo(virtualNo);
        log.info("getVirtualCoinRuleCouponInfo--->查询橙豆优惠券信息, 入参: {}", JSON.toJSONString(virtualCoinRuleAndCouponInfoVo));
        List<VirtualCoinRuleAndCouponInfoDetailVo> coinRuleAndCouponDetailVos = virtualCoinRuleDao.queryVirtualCoinRuleAndCouponInfoDetail(virtualCoinRuleAndCouponInfoVo);
        log.info("getVirtualCoinRuleCouponInfo--->查询橙豆优惠券信息, 出参: {}", JSON.toJSONString(coinRuleAndCouponDetailVos));
        if (CollectionUtils.isEmpty(coinRuleAndCouponDetailVos)) {
            return Collections.emptyList();
        }

        // 返回的结果
        List<ResVirtualCoinRuleAndCouponInfoDetailDTO> resRuleAndCouponDetailList = BeanCopierUtil.copyList(coinRuleAndCouponDetailVos, ResVirtualCoinRuleAndCouponInfoDetailDTO.class);
        log.info("-VirtualCoinRuleAnalysisServiceImpl-getVirtualCoinRuleCouponInfo-param-end,{}", JSON.toJSONString(resRuleAndCouponDetailList));
        return resRuleAndCouponDetailList;
    }

    /**
     * 查询门店粉丝信息
     * @param virtualCoinRuleType 橙豆规则类型 1001-店铺橙豆, 1002-商家橙豆
     * @param resVirtualCoinRecordDTO 橙豆记录
     * @return 粉丝信息
     */
    private ResFansFollowDTO getFansFollow(String virtualCoinRuleType, ResVirtualCoinRecordDTO resVirtualCoinRecordDTO) {
        ReqFancDTO getFanInfoDTO = new ReqFancDTO();
        getFanInfoDTO.setFanNo(resVirtualCoinRecordDTO.getFanNo());

        ExecuteDTO<List<ResFancDTO>> fanInfoExecuteDTO;
        // 20230928蛋品-赵翔宇-商家橙豆-橙豆流水, 店铺为共享店铺, 需要根据商家编号去查询
        if (VirtualCoinRuleTypeEnum.MEMBER_SHARING_RULE.getCode().equals(virtualCoinRuleType)) {
            getFanInfoDTO.setMerchantNo(resVirtualCoinRecordDTO.getMerchantNo());
            fanInfoExecuteDTO = ucFansProcessService.getMerchantFanInfo(getFanInfoDTO);
        } else {
            // 20230928蛋品-赵翔宇-商家橙豆-橙豆流水, 店铺未加入共享店铺
            getFanInfoDTO.setStoreNo(resVirtualCoinRecordDTO.getStoreNo());
            fanInfoExecuteDTO = legacyUserCenterService.getFansInfoByNos(getFanInfoDTO);
        }

        if (null != fanInfoExecuteDTO && CollectionUtils.isNotEmpty(fanInfoExecuteDTO.getData())) {
            ResFancDTO resFancDTO = ListUtil.getFirst(fanInfoExecuteDTO.getData());
            return BeanCopierUtil.copy(resFancDTO, ResFansFollowDTO.class);
        }
        return null;
    }

    @Override
    public void paymentAsynCallback(ReqvcPaymentAsynCallbackDTO reqDto) {
        String merchOrderNo = reqDto.getMerchOrderNo();
        String tradeStatus = TradeStatusEnum.getByYjfTradeStatus(reqDto.getTradeStatus()).getCode();

        ResVirtualCoinOrderFlowDTO flowDTO = this.selectOrderFlow(merchOrderNo);
        if (null == flowDTO) {
            throw new BaseException(MarketErrorCode.CODE_17000904);
        }
        if (OrderPaymentStatusEnum.PAID.getCode().equals(flowDTO.getPaymentStatus())) {
            // 已经是支付完成状态
            log.info("******VirtualCoinOrderOperatService-paymentAsynCallback-OutTradeNo:{}-交易流水已经是成功状态，无需再次回调", flowDTO.getOutTradeNo());
            return;
        }
        String orderNo = flowDTO.getOrderNo();
        // 修改order和flow的支付状态
        flowDTO.setPaymentStatus(tradeStatus);
        flowDTO.setPaymentChannel(PaymentChannelDetailsEnum.getByCode(reqDto.getTradeType()).getPaymentChannel());
        flowDTO.setPaymentChannelDetails(reqDto.getTradeType());
        flowDTO.setModifyNo(reqDto.getModifyNo());
        flowDTO.setModifyName(reqDto.getModifyName());

        AtomResVirtualCoinOrderDTO orderDto;
        ExecuteDTO<AtomResVirtualCoinOrderDTO> orderExecuteDTO = orderAnalysisService.selectOrderByOrderNo(orderNo);
        if (null == orderExecuteDTO || null == orderExecuteDTO.getData()) {
            throw new BaseException(MarketErrorCode.CODE_17000905);
        }
        orderDto = orderExecuteDTO.getData();

        LocalDateTime now = LocalDateTime.now();

        if (YjfTradeStatusEnum.INIT.getCode().equals(reqDto.getTradeStatus()) || YjfTradeStatusEnum.PROCESSING.getCode().equals(reqDto.getTradeStatus())) {
            // 待支付
            flowDTO.setPaymentStatus(OrderPaymentStatusEnum.UNPAID.getCode());
            orderDto.setPaymentStatus(OrderPaymentStatusEnum.UNPAID.getCode());
            // 更新状态
            this.updatePaymentStatus(orderDto, flowDTO);
        } else if (YjfTradeStatusEnum.FAIL.getCode().equals(reqDto.getTradeStatus())
                || YjfTradeStatusEnum.CANCEL.getCode().equals(reqDto.getTradeStatus())
                || YjfTradeStatusEnum.CLOSE.getCode().equals(reqDto.getTradeStatus())
                || YjfTradeStatusEnum.ERROR.getCode().equals(reqDto.getTradeStatus())) {
            // 支付失败
            flowDTO.setPaymentStatus(OrderPaymentStatusEnum.PAID_CLOSED.getCode());
            orderDto.setPaymentStatus(OrderPaymentStatusEnum.PAID_CLOSED.getCode());
            // 更新状态
            this.updatePaymentStatus(orderDto, flowDTO);
        } else if (YjfTradeStatusEnum.SUCCESS.getCode().equals(reqDto.getTradeStatus())) {
            // 支付成功
            flowDTO.setSuccessTime(now);
            flowDTO.setPaymentStatus(OrderPaymentStatusEnum.PAID.getCode());
            orderDto.setPaymentStatus(OrderPaymentStatusEnum.PAID.getCode());
            orderDto.setModifyNo(reqDto.getModifyNo());
            orderDto.setModifyName(reqDto.getModifyName());
            // 更新状态
            this.updatePaymentStatus(orderDto, flowDTO);
            // 交易成功，开始橙豆充值
            this.modifyUserVirtualCoin(BeanCopierUtil.copy(orderDto, AtomReqVirtualCoinOrderDTO.class), BeanCopierUtil.copy(flowDTO, AtomReqVirtualCoinOrderFlowDTO.class));
            // 20230928蛋品-桑伟杰-会员等级-等级升级
            ReqAddMemberLevelDTO reqAddMemberLevelDTO = new ReqAddMemberLevelDTO();
            reqAddMemberLevelDTO.setFanNo(orderDto.getFanNo());
            reqAddMemberLevelDTO.setStoreNo(orderDto.getStoreNo());
            reqAddMemberLevelDTO.setMerchantNo(orderDto.getMerchantNo());
            memberLevelProcessService.addMemberLevel(reqAddMemberLevelDTO);
        }


    }

    @Override
    public ExecuteDTO saveVirtualCoinOrderFlow(AtomReqVirtualCoinOrderFlowDTO atomReqVirtualCoinOrderFlowDTO) {
        VirtualCoinOrderFlowDomain virtualCoinOrderFlowDomain = BeanCopierUtil.copy(atomReqVirtualCoinOrderFlowDTO, VirtualCoinOrderFlowDomain.class);
        return ExecuteDTO.success(virtualCoinOrderFlowDao.insert(virtualCoinOrderFlowDomain));
    }

    @Override
    public ExecuteDTO modifyVirtualCoinOrderFlow(AtomReqVirtualCoinOrderFlowDTO atomReqVirtualCoinOrderFlowDTO) {
        VirtualCoinOrderFlowDomain virtualCoinOrderFlowDomain = BeanCopierUtil.copy(atomReqVirtualCoinOrderFlowDTO, VirtualCoinOrderFlowDomain.class);
        return ExecuteDTO.success(virtualCoinOrderFlowDao.updateFolw(virtualCoinOrderFlowDomain));
    }

    private void updatePaymentStatus(AtomResVirtualCoinOrderDTO orderDto, ResVirtualCoinOrderFlowDTO flowDTO) {
        orderOperatService.updatePaymentStatus(BeanCopierUtil.copy(orderDto, AtomReqVirtualCoinOrderDTO.class), BeanCopierUtil.copy(flowDTO, AtomReqVirtualCoinOrderFlowDTO.class));
    }

    private ResVirtualCoinOrderFlowDTO selectOrderFlow(String outTradeNo) {
        ExecuteDTO<AtomResVirtualCoinOrderFlowDTO> flowDTOExecuteDTO = orderAnalysisService.selectOrderFlowByOutTradeNo(outTradeNo);
        if (null != flowDTOExecuteDTO && flowDTOExecuteDTO.successFlag()) {
            return BeanCopierUtil.copy(flowDTOExecuteDTO.getData(), ResVirtualCoinOrderFlowDTO.class);
        }
        return null;
    }

    /**
     *
     * 充值橙豆送优惠券, 发券
     *
     * @param orderDto 订单信息
     * @param fansFollow 粉丝信息
     * @return 发券成功的短信通知内容
     */
    private String sendVirtualCoinRuleCouponSetting (AtomReqVirtualCoinOrderDTO orderDto, ResFansFollowDTO fansFollow) {

        log.info("-VirtualCoinRuleAnalysisServiceImpl-sendVirtualCoinRuleCouponSetting-param-start, orderDto: {}", JSON.toJSONString(orderDto));

        // 橙豆规则不为空的话需要判断当前橙豆规则是否关联了优惠券, 自定义充值, 没有橙豆规则编号
        if (StringUtils.isNotBlank(orderDto.getVirtualRuleNo())) {
            // 规则对应的优惠券信息
            List<ResVirtualCoinRuleAndCouponInfoDetailDTO> virtualCoinRuleCouponInfos =
                    getVirtualCoinRuleCouponInfo(orderDto.getStoreNo(), orderDto.getMerchantNo(), orderDto.getVirtualRuleNo(), orderDto.getRuleType());

            // 橙豆规则对应的优惠券数据不为空, 则需要生成券发放记录和用户接受券记录, 还需要在短信内容中拼接上"您账户到账X张总价值XX元的优惠券，请及时使用"
            if (CollectionUtils.isNotEmpty(virtualCoinRuleCouponInfos)) {

                log.info("-VirtualCoinRuleAnalysisServiceImpl-sendVirtualCoinRuleCouponSetting--->send coupon start, virtualNo: {}, fanNo: {}", orderDto.getVirtualRuleNo(), fansFollow.getFanNo());

                ReqVirtualCoinRuleDistributeCouponDTO reqVirtualCoinRuleDistributeCouponDTO = BeanCopierUtil.copy(orderDto, ReqVirtualCoinRuleDistributeCouponDTO.class);

                // 设置优惠券数据
                reqVirtualCoinRuleDistributeCouponDTO.setVirtualCoinRuleCouponInfos(virtualCoinRuleCouponInfos);
                // 设置发送规则
                reqVirtualCoinRuleDistributeCouponDTO.setSendCouponRule(SendCouponRuleEnum.COUPON_SOURCE_TYPE_ONE.getCode());
                reqVirtualCoinRuleDistributeCouponDTO.setImportType(NumConstant.ONE);
                reqVirtualCoinRuleDistributeCouponDTO.setFanNo(fansFollow.getFanNo());
                reqVirtualCoinRuleDistributeCouponDTO.setPhone(fansFollow.getPhone());
                reqVirtualCoinRuleDistributeCouponDTO.setDsPhone(fansFollow.getDsPhone());

                // 不需要在方法里发送短信
                reqVirtualCoinRuleDistributeCouponDTO.setSmsNoticeFlag(NumConstant.ONE);

                ExecuteDTO executeDTO = couponPromotionOperateService.batchDistributeVirtualCoinCoupon(reqVirtualCoinRuleDistributeCouponDTO);
                if (!executeDTO.successFlag()) {
                    log.error("橙豆充值保存优惠券记录失败, 入参{}", JSON.toJSONString(reqVirtualCoinRuleDistributeCouponDTO));
                    throw new BaseException(CommonCode.CODE_10000003);
                }

                // 计算橙豆规则对应的优惠券面值之和, 这里从数据库查询出来的结果是精确到小数点后四位, 需要区分是满减券还是折扣券
                BigDecimal couponTotalValue = BigDecimal.ZERO;

                for (ResVirtualCoinRuleAndCouponInfoDetailDTO ruleAndCouponInfoDetailDTO : virtualCoinRuleCouponInfos) {
                    // 如果为满减券, 总价值为所有选中优惠券面值之和
                    if (CouponTypeEnum.COUPON_REDUCE.getCode().equals(ruleAndCouponInfoDetailDTO.getCouponType())) {
                        couponTotalValue = BigDecimalUtil.add(couponTotalValue, ruleAndCouponInfoDetailDTO.getCouponValue());
                    } else if (CouponTypeEnum.COUPON_DISCONUT.getCode().equals(ruleAndCouponInfoDetailDTO.getCouponType())) {
                        // 如果为折扣券, 优惠面额 = 门槛 * 折扣, 8折的话, couponValue为8
                        BigDecimal tempCouponValue = BigDecimalUtil.multiply(ruleAndCouponInfoDetailDTO.getDiscountThreshold(), ruleAndCouponInfoDetailDTO.getCouponValue());
                        BigDecimal realCouponValue = BigDecimalUtil.divide(tempCouponValue, new BigDecimal(StringNumConstant.TEN));
                        couponTotalValue = BigDecimalUtil.add(couponTotalValue, realCouponValue);
                    }
                }

                // 充值橙豆送优惠券, 橙豆充值成功, 还需要在短信内容中拼接上"您账户到账X张总价值XX元的优惠券，请及时使用"
                String virtualCoinRuleCouponSmsContent = String.format(CommonSmsSceneEnum.RECHARGE_VIRTUAL_COIN_SEND_COUPON.getTemplate(),
                        virtualCoinRuleCouponInfos.size(),
                        couponTotalValue);
                return virtualCoinRuleCouponSmsContent;
            }
        }
        return "";
    }

}
