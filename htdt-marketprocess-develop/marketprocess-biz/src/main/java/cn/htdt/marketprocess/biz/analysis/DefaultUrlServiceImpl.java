package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.enums.market.PromotionTypeEnum;
import cn.htdt.marketprocess.api.analysis.DefaultUrlService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021-03-30
 * @Description 获取默认地址接口
 **/
@DubboService
public class DefaultUrlServiceImpl implements DefaultUrlService {

    /**
     * 地址前缀
     */
    @Value("${market.default-url.common}")
    private String common;
    /**
     * 1001:大转盘
     */
    @Value("${market.default-url.big-turntable}")
    private String bigTurntable;
    /** 1002:摇奖机 **/
    @Value("${market.default-url.lottery}")
    private String lottery;
    /** 1003:拆盲盒 **/
    @Value("${market.default-url.blind-box}")
    private String blindBox;
    /** 1004:套圈圈 **/
    @Value("${market.default-url.loop}")
    private String loop;
    /** 1005:小猫钓鱼 **/
    @Value("${market.default-url.kitten-fishing}")
    private String kittenFishing;
    /** 1006:膨胀红包 **/
    @Value("${market.default-url.red-envelope}")
    private String redEnvelope;
    /** 1007:砸金蛋 **/
    @Value("${market.default-url.golden-egg}")
    private String goldenEgg;
    /** 2001:秒杀 **/
    @Value("${market.default-url.second-kill}")
    private String secondKill;
    /** 2002:拼团 **/
    @Value("${market.default-url.team-building}")
    private String teamBuilding;
    /** 2003:预售 **/
    @Value("${market.default-url.pre-sale}")
    private String preSale;
    /** 2004:单品促销 **/
    @Value("${market.default-url.single-goods-promotion}")
    private String singleGoodsPromotion;
    /** 2005:满额促销 **/
    @Value("${market.default-url.full-promotion}")
    private String fullPromotion;
    /** 2006:满量促销 **/
    @Value("${market.default-url.full-volume-promotion}")
    private String fullVolumePromotion;
    /** 2007:满额赠 **/
    @Value("${market.default-url.full-gift}")
    private String fullGift;
    /** 2008:套餐 **/
    @Value("${market.default-url.set-meal}")
    private String setMeal;
    /** 3001:平台-手动发代理人券 **/
    @Value("${market.default-url.platform-manual-agent-coupon}")
    private String platformManualAgentCoupon;
    /** 3002:平台-手动发粉丝券 **/
    @Value("${market.default-url.platform-manual-fans-coupon}")
    private String platformManualFansCoupon;
    /** 3003:平台-网店代理人领券 **/
    @Value("${market.default-url.platform-online-agent-coupon}")
    private String platformOnlineAgentCoupon;
    /** 3004:平台-网店粉丝领券 **/
    @Value("${market.default-url.platform-online-fans-coupon}")
    private String platformOnlineFansCoupon;
    /** 3005:店铺-手动发粉丝券 **/
    @Value("${market.default-url.shop-manual-coupon}")
    private String shopManualCoupon;
    /** 3006:店铺-网店粉丝领券 **/
    @Value("${market.default-url.shop-online-fans-coupon}")
    private String shopOnlineFansCoupon;
    /** 3007:平台-膨胀红包 **/
    @Value("${market.default-url.platform-red-envelope}")
    private String platformRedEnvelope;
    /** 3100:平台-抽奖券 **/
    @Value("${market.default-url.platform-lottery-coupon}")
    private String platformLotteryCoupon;

    /**
     * 默认放url信息-all
     */
    private static Map<String, String> promotionUrls = new HashMap<>();

    @PostConstruct
    private void init() {
        // 促销类型
        // 1001:大转盘
        promotionUrls.put(PromotionTypeEnum.TURNTABLE_DRAW.getCode(), bigTurntable);
        // 1002:摇奖机
        promotionUrls.put(PromotionTypeEnum.LOTTERYWHEEL_DRAW.getCode(), lottery);
        // 1003:拆盲盒
        promotionUrls.put(PromotionTypeEnum.BLINDBOX_DRAW.getCode(), blindBox);
        // 1004:套圈圈
        promotionUrls.put(PromotionTypeEnum.SNARE_DRAW.getCode(), loop);
        // 1005:小猫钓鱼
        promotionUrls.put(PromotionTypeEnum.CATFISHING_DRAW.getCode(), kittenFishing);
        // 1006:膨胀红包
        promotionUrls.put(PromotionTypeEnum.RED_ENVELOPE.getCode(), redEnvelope);
        //1007:砸金蛋
        promotionUrls.put(PromotionTypeEnum.EGG_FRENZY.getCode(),goldenEgg);

        // 2001:秒杀
        promotionUrls.put(PromotionTypeEnum.SENCOND_KILL.getCode(), secondKill);
        // 2002:拼团
        promotionUrls.put(PromotionTypeEnum.TEAM_BUILDING.getCode(), teamBuilding);
        // 2003:预售
        promotionUrls.put(PromotionTypeEnum.PRE_SALE.getCode(), preSale);
        // 2004:单品促销
        promotionUrls.put(PromotionTypeEnum.SINGLE_GOODS_PROMOTION.getCode(), singleGoodsPromotion);
        // 2005:满额促销
        promotionUrls.put(PromotionTypeEnum.FULL_PROMOTION.getCode(), fullPromotion);
        // 2006:满量促销
        promotionUrls.put(PromotionTypeEnum.FULL_VOLUME_PROMOTION.getCode(), fullVolumePromotion);
        // 2007:满额赠
        promotionUrls.put(PromotionTypeEnum.FULL_GIFT.getCode(), fullGift);
        // 2008:套餐
        promotionUrls.put(PromotionTypeEnum.SET_MEAL.getCode(), setMeal);

        // 3001:平台-手动发代理人券
        promotionUrls.put(PromotionTypeEnum.PLATFORM_MANUAL_AGENT_COUPON.getCode(), platformManualAgentCoupon);
        // 3002::平台-手动发粉丝券
        promotionUrls.put(PromotionTypeEnum.PLATFORM_MANUAL_FANS_COUPON.getCode(), platformManualFansCoupon);
        // 3003:平台-网店代理人领券
        promotionUrls.put(PromotionTypeEnum.PLATFORM_ONLINE_AGENT_COUPON.getCode(), platformOnlineAgentCoupon);
        // 3004:平台-网店粉丝领券
        promotionUrls.put(PromotionTypeEnum.PLATFORM_ONLINE_FANS_COUPON.getCode(), platformOnlineFansCoupon);
        // 3005:店铺-手动发粉丝券
        promotionUrls.put(PromotionTypeEnum.SHOP_MANUAL_COUPON.getCode(), shopManualCoupon);
        // 3006:店铺-网店粉丝领券
        promotionUrls.put(PromotionTypeEnum.SHOP_ONLINE_FANS_COUPON.getCode(), shopOnlineFansCoupon);
        // 3007:膨胀红包
        promotionUrls.put(PromotionTypeEnum.PLATFORM_RED_ENVELOPE.getCode(), platformRedEnvelope);
        // 3100:抽奖券
        promotionUrls.put(PromotionTypeEnum.PLATFORM_LOTTERY_COUPON.getCode(), platformLotteryCoupon);
    }

    /**
     * 根据促销类型获取对应的url
     * @return
     */
    @Override
    public ExecuteDTO<String> getDefaultUrl(String promotionType) {
        if(StringUtils.isEmpty(promotionType)) {
            return ExecuteDTO.error(CommonCode.CODE_10000001);
        }
        if(PromotionTypeEnum.getByCode(promotionType) == null) {
            return ExecuteDTO.error(CommonCode.CODE_10000002);
        }
        return ExecuteDTO.success(common + promotionUrls.get(promotionType));
    }

}