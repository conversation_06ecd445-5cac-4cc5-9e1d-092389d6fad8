package cn.htdt.marketprocess.biz.rabbitmq.send;

import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.marketprocess.biz.rabbitmq.config.DirectDeplayConfig;
import cn.htdt.middlewareprocess.dto.request.htd.AtomReqListSmsChannelDTO;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 对象发送者-短信发送
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SmsSender {

    @Resource
    private AmqpTemplate rabbitTemplate;

    public static final String SMS_PREFIX = "【蛋品在线】";

    //短信后缀
    public static final String SMS_SUFFIX = "，拒收请回复R";
    /**
     * 发送消息-营销短信发送
     *
     * @param reqListSmsChannelDTO 消息内容
     */
    public void sendBySmsSend(AtomReqListSmsChannelDTO reqListSmsChannelDTO) {
        log.info("------sendBySmsSend------Sender message：{}", JSON.toJSONString(reqListSmsChannelDTO));
        reqListSmsChannelDTO.setContent(reqListSmsChannelDTO.getContent().replace(SMS_PREFIX, ""));
        // 一次消息最多最多发送500条短信
        if (reqListSmsChannelDTO.getPhoneNumList().size() > NumConstant.FIVE_HUNDRED) {
            List<String> phoneNumList = reqListSmsChannelDTO.getPhoneNumList();
            List<String> dsPhoneNumList = reqListSmsChannelDTO.getDsPhoneNumList();
            for (int i = 0; i < phoneNumList.size(); i += NumConstant.FIVE_HUNDRED) {
                List<String> newPhoneNumList = phoneNumList.subList(i, Math.min(i + NumConstant.FIVE_HUNDRED, phoneNumList.size()));
                if (CollectionUtils.isEmpty(newPhoneNumList)) {
                    reqListSmsChannelDTO.setPhoneNumList(Lists.newArrayList());
                } else {
                    reqListSmsChannelDTO.setPhoneNumList(newPhoneNumList);
                }
                //判断加密文为空
                if (CollectionUtils.isNotEmpty(dsPhoneNumList)) {
                    List<String> newDsPhoneNumList = dsPhoneNumList.subList(i, Math.min(i + NumConstant.FIVE_HUNDRED, phoneNumList.size()));
                    if (CollectionUtils.isEmpty(newDsPhoneNumList) || null == newDsPhoneNumList.get(0)) {
                        reqListSmsChannelDTO.setDsPhoneNumList(Lists.newArrayList());
                    } else {
                        reqListSmsChannelDTO.setDsPhoneNumList(newDsPhoneNumList);
                    }
                }
                this.rabbitTemplate.convertAndSend(DirectDeplayConfig.NORMAL_EXCHANGE_NAME, DirectDeplayConfig.NORMAL_QUEUE_SMS_SEND_ROUTING_KEY, reqListSmsChannelDTO);
            }
        } else {
            this.rabbitTemplate.convertAndSend(DirectDeplayConfig.NORMAL_EXCHANGE_NAME, DirectDeplayConfig.NORMAL_QUEUE_SMS_SEND_ROUTING_KEY, reqListSmsChannelDTO);
        }
    }
}
