package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.constants.StringNumConstant;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.DateUtil;
import cn.htdt.common.utils.ListUtil;
import cn.htdt.marketcenter.dto.request.AtomReqPointsGiftDTO;
import cn.htdt.marketcenter.dto.response.AtomResPointsGiftDTO;
import cn.htdt.marketprocess.api.analysis.PointsGiftAnalysisService;
import cn.htdt.marketprocess.biz.conversion.PointsGiftAssert;
import cn.htdt.marketprocess.biz.utils.PointsUtil;
import cn.htdt.marketprocess.dao.PointsGiftDao;
import cn.htdt.marketprocess.dto.request.ReqPointsGiftDTO;
import cn.htdt.marketprocess.dto.response.ResPointsGiftDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomPointsGiftAnalysisService;
import cn.htdt.marketprocess.vo.PointsGiftVO;
import cn.htdt.userprocess.dto.response.StoreInfoResponse;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 * @see PointsGiftAnalysisService
 */
@DubboService
@Slf4j
public class PointsGiftAnalysisServiceImpl implements PointsGiftAnalysisService {
    @Resource
    PointsGiftAssert pointsGiftAssert;

    @Resource
    AtomPointsGiftAnalysisService atomPointsGiftAnalysisService;

    @Resource
    private PointsGiftDao pointsGiftDao;

    @Resource
    PointsUtil pointsUtil;

    /**
     * @see PointsGiftAnalysisService#getSimpleGift(ReqPointsGiftDTO)
     */
    @Override
    public ExecuteDTO<ResPointsGiftDTO> getSimpleGift(ReqPointsGiftDTO pointsGiftDTO) {
        log.info("PointsGiftAnalysisServiceImpl.getSimpleGift#param----{}", JSON.toJSONString(pointsGiftDTO));
        pointsGiftAssert.getSimpleGift(pointsGiftDTO);
        AtomReqPointsGiftDTO atomReqPointsGiftDTO = BeanCopierUtil.copy(pointsGiftDTO, AtomReqPointsGiftDTO.class);
        ExecuteDTO<AtomResPointsGiftDTO> executeDTO = null;
        //20230928蛋品-wh-查询店铺是否是共享店铺
        StoreInfoResponse storeInfoResponse = pointsUtil.queryStore(pointsGiftDTO.getStoreNo());
        if(storeInfoResponse.getMemberSharing() == NumConstant.ONE){
            //门店的
            atomReqPointsGiftDTO.setRuleType(String.valueOf(NumConstant.ONE));
            executeDTO = atomPointsGiftAnalysisService.getSimpleGift(atomReqPointsGiftDTO);
        }else {
            //商家的
            executeDTO = atomPointsGiftAnalysisService.getSharingSimpleGift(atomReqPointsGiftDTO);
        }
        if(!executeDTO.successFlag()){
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResPointsGiftDTO pointsConfigDTO=BeanCopierUtil.copy(executeDTO.getData(),ResPointsGiftDTO.class);
        return ExecuteDTO.success(pointsConfigDTO);
    }

    /**
     * @see PointsGiftAnalysisService#getMultiGift(ReqPointsGiftDTO)
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResPointsGiftDTO>> getMultiGift(ReqPointsGiftDTO pointsGiftDTO) {
        log.info("PointsGiftAnalysisServiceImpl.getMultiGift#param----{}", JSON.toJSONString(pointsGiftDTO));
        pointsGiftAssert.getMultiGift(pointsGiftDTO);
        AtomReqPointsGiftDTO atomReqPointsGiftDTO = BeanCopierUtil.copy(pointsGiftDTO, AtomReqPointsGiftDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResPointsGiftDTO>> executeDTO = atomPointsGiftAnalysisService.getMultiGift(atomReqPointsGiftDTO);
        if(!executeDTO.successFlag()){
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        //参数转换
        ExecutePageDTO<ResPointsGiftDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResPointsGiftDTO> resPointsGiftDTOS = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResPointsGiftDTO.class);
        resPointsGiftDTOS.forEach(s->s.setModifyTimeStr(DateUtil.format(s.getModifyTime(),DateUtil.YYDDMMHHMMSS)));
        executePageDTO.setRows(resPointsGiftDTOS);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    /**
     * @see PointsGiftAnalysisService#getCanConvertGift(ReqPointsGiftDTO)
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResPointsGiftDTO>> getCanConvertGift(ReqPointsGiftDTO pointsGiftDTO) {
        log.info("PointsGiftAnalysisServiceImpl.getCanConvertGift#param----{}", JSON.toJSONString(pointsGiftDTO));
        pointsGiftAssert.checkStoreNo(pointsGiftDTO);
        //20230928蛋品-wh-查询店铺是否是共享店铺
        StoreInfoResponse storeInfoResponse = pointsUtil.queryStore(pointsGiftDTO.getStoreNo());
        AtomReqPointsGiftDTO atomReqPointsGiftDTO = BeanCopierUtil.copy(pointsGiftDTO, AtomReqPointsGiftDTO.class);
        if(storeInfoResponse.getMemberSharing() == NumConstant.ONE){
            atomReqPointsGiftDTO.setRuleType(String.valueOf(NumConstant.ONE));
        }else {
            atomReqPointsGiftDTO.setRuleType(String.valueOf(NumConstant.TWO));
            atomReqPointsGiftDTO.setMerchantNo(storeInfoResponse.getMerchantNo());
        }
        ExecuteDTO<ExecutePageDTO<AtomResPointsGiftDTO>> executeDTO = atomPointsGiftAnalysisService.getCanConvert(atomReqPointsGiftDTO);
        if(!executeDTO.successFlag()){
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        //参数转换
        ExecutePageDTO<ResPointsGiftDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResPointsGiftDTO> resPointsGiftDTOS = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResPointsGiftDTO.class);
        executePageDTO.setRows(resPointsGiftDTOS);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    @Override
    public ExecuteDTO<ExecutePageDTO<ResPointsGiftDTO>> getMerchantMultiGift(ReqPointsGiftDTO pointsGiftDTO) {
        log.info("PointsGiftAnalysisServiceImpl.getMerchantMultiGift#param----{}", JSON.toJSONString(pointsGiftDTO));
        pointsGiftAssert.getMerchantMultiGift(pointsGiftDTO);
        AtomReqPointsGiftDTO atomReqPointsGiftDTO = BeanCopierUtil.copy(pointsGiftDTO, AtomReqPointsGiftDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResPointsGiftDTO>> executeDTO = atomPointsGiftAnalysisService.getMerchantMultiGift(atomReqPointsGiftDTO);
        if(!executeDTO.successFlag()){
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        //参数转换
        ExecutePageDTO<ResPointsGiftDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResPointsGiftDTO> resPointsGiftDTOS = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResPointsGiftDTO.class);
        resPointsGiftDTOS.forEach(s->s.setModifyTimeStr(DateUtil.format(s.getModifyTime(),DateUtil.YYDDMMHHMMSS)));
        executePageDTO.setRows(resPointsGiftDTOS);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    @Override
    public ExecuteDTO<ExecutePageDTO<ResPointsGiftDTO>> getSharingMultiGift(ReqPointsGiftDTO pointsGiftDTO) {
        log.info("PointsGiftAnalysisServiceImpl.getSharingMultiGift#param----{}", JSON.toJSONString(pointsGiftDTO));
        pointsGiftAssert.getMultiGift(pointsGiftDTO);
        AtomReqPointsGiftDTO atomReqPointsGiftDTO = BeanCopierUtil.copy(pointsGiftDTO, AtomReqPointsGiftDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResPointsGiftDTO>> executeDTO = atomPointsGiftAnalysisService.getSharingMultiGift(atomReqPointsGiftDTO);
        if(!executeDTO.successFlag()){
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        //参数转换
        ExecutePageDTO<ResPointsGiftDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResPointsGiftDTO> resPointsGiftDTOS = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResPointsGiftDTO.class);
        resPointsGiftDTOS.forEach(s->s.setModifyTimeStr(DateUtil.format(s.getModifyTime(),DateUtil.YYDDMMHHMMSS)));
        executePageDTO.setRows(resPointsGiftDTOS);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    /**
     * 20230928蛋品-赵翔宇-切换共享会员的配置, 关闭店铺的共享会员, 若操作的店铺下的商家礼品剩余库存>0, 则不允许关闭
     *
     * @param pointsGiftDTO 查询参数
     * @return 礼品数量
     */
    @Override
    public ExecuteDTO<Integer> getMerchantHasStockGiftCount(ReqPointsGiftDTO pointsGiftDTO) {
        pointsGiftAssert.getMerchantHasStockGiftCountAssert(pointsGiftDTO);

        PointsGiftVO pointsGiftVO = BeanCopierUtil.copy(pointsGiftDTO, PointsGiftVO.class);

        if (NumConstant.ONE == ListUtil.getSize(pointsGiftDTO.getStoreNoList())) {
            pointsGiftVO.setStoreNo(ListUtil.getFirst(pointsGiftDTO.getStoreNoList()));
            pointsGiftVO.setStoreNoList(null);
        }

        pointsGiftVO.setPointsGiftType(StringNumConstant.TWO);
        int merchantHasStockGiftCount = pointsGiftDao.getMerchantHasStockGiftCount(pointsGiftVO);
        return ExecuteDTO.ok(merchantHasStockGiftCount);
    }

    @Override
    public ExecuteDTO<ResPointsGiftDTO> selMerchantPointsGiftDetails(ReqPointsGiftDTO pointsGiftDTO) {
        log.info("PointsGiftAnalysisServiceImpl.selMerchantPointsGiftDetails#param----{}", JSON.toJSONString(pointsGiftDTO));
        pointsGiftAssert.getSimpleGift(pointsGiftDTO);
        AtomReqPointsGiftDTO atomReqPointsGiftDTO = BeanCopierUtil.copy(pointsGiftDTO, AtomReqPointsGiftDTO.class);
        ExecuteDTO<AtomResPointsGiftDTO> executeDTO = atomPointsGiftAnalysisService.selMerchantPointsGiftDetails(atomReqPointsGiftDTO);
        if(!executeDTO.successFlag()){
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResPointsGiftDTO pointsConfigDTO=BeanCopierUtil.copy(executeDTO.getData(),ResPointsGiftDTO.class);
        return ExecuteDTO.success(pointsConfigDTO);
    }
}
