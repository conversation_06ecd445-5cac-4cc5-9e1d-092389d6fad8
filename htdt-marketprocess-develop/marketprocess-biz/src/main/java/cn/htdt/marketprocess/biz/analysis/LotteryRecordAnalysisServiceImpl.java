package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqLotteryRecordDTO;
import cn.htdt.marketprocess.api.analysis.LotteryRecordAnalysisService;
import cn.htdt.marketprocess.biz.conversion.LotteryRecordAssert;
import cn.htdt.marketprocess.dao.LotteryRecordDao;
import cn.htdt.marketprocess.domain.LotteryRecordDomain;
import cn.htdt.marketprocess.dto.request.ReqLotteryRecordDTO;
import cn.htdt.marketprocess.dto.response.ResLotteryRecordDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomLotteryRecordAnalysisService;
import cn.htdt.marketprocess.vo.LotteryRecordVo;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@DubboService
public class LotteryRecordAnalysisServiceImpl implements LotteryRecordAnalysisService {

    @Autowired
    LotteryRecordAssert lotteryRecordAssert;
    @Resource
    LotteryRecordDao lotteryRecordDao;
    @Resource
    AtomLotteryRecordAnalysisService lotteryRecordAnalysisService;

    @Override
    public ExecuteDTO<Integer> getPromotionLotteryCount(ReqLotteryRecordDTO reqLotteryRecordDTO) {
        log.info("LotteryRecordAnalysisServiceImpl#getPromotionLotteryCount----param----{}", JSONObject.toJSONString(reqLotteryRecordDTO));
        lotteryRecordAssert.getPromotionLotteryCountAssert(reqLotteryRecordDTO);
        return lotteryRecordAnalysisService.getPromotionLotteryCount(BeanCopierUtil.copy(reqLotteryRecordDTO,AtomReqLotteryRecordDTO.class));
    }

    @Override
    public ExecuteDTO<Integer> getOrderLotteryCountOfPromotion(ReqLotteryRecordDTO reqLotteryRecordDTO) {
        log.info("LotteryRecordAnalysisServiceImpl#getOrderLotteryCountOfPromotion----param----{}", JSONObject.toJSONString(reqLotteryRecordDTO));
        lotteryRecordAssert.getOrderLotteryCountOfPromotionAssert(reqLotteryRecordDTO);
        return lotteryRecordAnalysisService.getOrderLotteryCountOfPromotion(BeanCopierUtil.copy(reqLotteryRecordDTO, AtomReqLotteryRecordDTO.class));
    }

    @Override
    public ExecuteDTO<List<ResLotteryRecordDTO>> getPromotionLottery(ReqLotteryRecordDTO reqLotteryRecordDTO) {
        LotteryRecordVo lotteryRecordVo = BeanCopierUtil.copy(reqLotteryRecordDTO, LotteryRecordVo.class);
        List<LotteryRecordDomain> list = lotteryRecordDao.selectPromotionLottery(lotteryRecordVo);
        List<ResLotteryRecordDTO> resLotteryRecordDTOS = BeanCopierUtil.copyList(list,ResLotteryRecordDTO.class);
        return ExecuteDTO.success(resLotteryRecordDTOS);
    }
}
