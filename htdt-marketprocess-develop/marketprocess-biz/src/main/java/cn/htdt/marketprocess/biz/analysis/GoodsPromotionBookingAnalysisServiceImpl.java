package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqGoodsPromotionBookingDTO;
import cn.htdt.marketcenter.dto.response.AtomResGoodsPromotionBookingDTO;
import cn.htdt.marketprocess.api.analysis.GoodsPromotionBookingAnalysisService;
import cn.htdt.marketprocess.biz.conversion.GoodsPromotionBookingAssert;
import cn.htdt.marketprocess.dto.request.ReqGoodsPromotionBookingDTO;
import cn.htdt.marketprocess.dto.response.ResGoodsPromotionBookingDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomGoodsPromotionBookingAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomGoodsPromotionGoodsRelationAnalysisService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 * 活动商品预约类
 *
 * <AUTHOR>
 * @date 2021/7/20 17:00
 */
@Slf4j
@DubboService
public class GoodsPromotionBookingAnalysisServiceImpl implements GoodsPromotionBookingAnalysisService {

    @Resource
    private AtomGoodsPromotionBookingAnalysisService atomGoodsPromotionBookingAnalysisService;

    @Resource
    private AtomGoodsPromotionGoodsRelationAnalysisService atomGoodsPromotionGoodsRelationAnalysisService;

    @Resource
    private GoodsPromotionBookingAssert goodsPromotionBookingAssert;

    /**
     * @param
     * @Description :  hxg -> 秒杀商品预约 -> 查询活动商品预约信息
     * <AUTHOR> 卜金隆
     * @date : 2021/7/20 17:30
     */
    @Override
    public ExecuteDTO<List<ResGoodsPromotionBookingDTO>> getGoodsPromotionBookingInfoList(ReqGoodsPromotionBookingDTO reqGoodsPromotionBookingDTO) {
        log.info(String.format("查询活动商品预约信息:%s", JSON.toJSONString(reqGoodsPromotionBookingDTO)));
        this.goodsPromotionBookingAssert.getPromotionAndGoodsInfoAssert(reqGoodsPromotionBookingDTO);
        AtomReqGoodsPromotionBookingDTO goodsPromotionBookingVO = BeanCopierUtil.copy(reqGoodsPromotionBookingDTO, AtomReqGoodsPromotionBookingDTO.class);
        ExecuteDTO<List<AtomResGoodsPromotionBookingDTO>> executeDTO = atomGoodsPromotionBookingAnalysisService.getGoodsPromotionBookingInfoList(goodsPromotionBookingVO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        List<ResGoodsPromotionBookingDTO> bookingDTOS = BeanCopierUtil.copyList(executeDTO.getData(), ResGoodsPromotionBookingDTO.class);
        log.info("-AtomGoodsPromotionBookingAnalysisServiceImpl-getGoodsPromotionBookingInfoList-return={}", JSON.toJSONString(bookingDTOS));
        return ExecuteDTO.success(bookingDTOS);
    }

    /**
     * @param
     * @Description : hxg -> 秒杀商品预约 -> 查询粉丝预约信息 job调用
     * <AUTHOR> 卜金隆
     * @date : 2021/7/20 17:30
     */
    @Override
    public ExecuteDTO<List<ResGoodsPromotionBookingDTO>> getFansBookingInfoList(ReqGoodsPromotionBookingDTO reqGoodsPromotionBookingDTO) {
        log.info(String.format("查询活动商品预约信息:%s", JSON.toJSONString(reqGoodsPromotionBookingDTO)));
        /*this.goodsPromotionBookingAssert.getPromotionAndGoodsInfoAssert(reqGoodsPromotionBookingDTO);*/
        AtomReqGoodsPromotionBookingDTO goodsPromotionBookingVO = BeanCopierUtil.copy(reqGoodsPromotionBookingDTO, AtomReqGoodsPromotionBookingDTO.class);
        ExecuteDTO<List<AtomResGoodsPromotionBookingDTO>> executeDTO = atomGoodsPromotionBookingAnalysisService.getFansBookingInfoList(goodsPromotionBookingVO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        List<ResGoodsPromotionBookingDTO> resGoodsPromotionBookingDTOS = BeanCopierUtil.copyList(executeDTO.getData(), ResGoodsPromotionBookingDTO.class);
        log.info("-AtomGoodsPromotionBookingAnalysisServiceImpl-getGoodsPromotionBookingInfoList-return={}", JSON.toJSONString(resGoodsPromotionBookingDTOS));
        return ExecuteDTO.success(resGoodsPromotionBookingDTOS);
    }

}
