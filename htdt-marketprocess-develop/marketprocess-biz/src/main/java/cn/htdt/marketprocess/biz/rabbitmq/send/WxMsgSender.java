package cn.htdt.marketprocess.biz.rabbitmq.send;

import cn.htdt.marketprocess.biz.rabbitmq.config.DirectDeplayConfig;
import cn.htdt.marketprocess.dto.request.ReqWxMsgUnpaidDTO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 对象发送者-微信消息推送
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WxMsgSender {

    @Resource
    private AmqpTemplate rabbitTemplate;

    /**
     * 发送消息-微信消息（未支付提醒）
     *
     * @param reqWxMsgUnpaidDTO 消息内容
     */
    public void sendByUnpaidMsg(ReqWxMsgUnpaidDTO reqWxMsgUnpaidDTO) {
        log.info("------sendByUnpaidMsg------Sender message：{}", JSON.toJSONString(reqWxMsgUnpaidDTO));
        this.rabbitTemplate.convertAndSend(
                DirectDeplayConfig.NORMAL_EXCHANGE_NAME,
                DirectDeplayConfig.NORMAL_QUEUE_WX_MSG_UNPAID_ROUTING_KEY,
                reqWxMsgUnpaidDTO,
                message -> {
                    message.getMessageProperties().setExpiration(reqWxMsgUnpaidDTO.getReminderTime());
                    return message;
                });
    }
}
