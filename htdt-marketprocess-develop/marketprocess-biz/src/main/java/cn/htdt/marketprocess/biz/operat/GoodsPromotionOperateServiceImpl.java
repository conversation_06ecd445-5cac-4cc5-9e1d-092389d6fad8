package cn.htdt.marketprocess.biz.operat;

import cn.htdt.common.base.exception.BaseException;
import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.enums.GoodsErrorCode;
import cn.htdt.common.dto.enums.MarketErrorCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.utils.ExecuteDTOUtil;
import cn.htdt.common.enums.PlatformTypeEnum;
import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.constants.PromotionKeyConstant;
import cn.htdt.common.enums.constants.RedisKeyTTLConstant;
import cn.htdt.common.enums.goods.SourceTypeEnum;
import cn.htdt.common.enums.market.*;
import cn.htdt.common.generator.MarketFormGenerator;
import cn.htdt.common.redis.utils.RedisUtil;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.BigDecimalUtil;
import cn.htdt.common.utils.DateUtil;
import cn.htdt.common.utils.ListUtil;
import cn.htdt.goodscenter.dto.request.AtomReqGoodsDTO;
import cn.htdt.goodscenter.dto.response.AtomResGoodsDTO;
import cn.htdt.goodsprocess.api.operat.LegacyGoodsCenterService;
import cn.htdt.marketcenter.dto.request.*;
import cn.htdt.marketcenter.dto.response.*;
import cn.htdt.marketprocess.api.analysis.GoodsPromotionRuleAnalysisService;
import cn.htdt.marketprocess.api.operat.GoodsPromotionOperateService;
import cn.htdt.marketprocess.biz.api.GoodsPromotionImageService;
import cn.htdt.marketprocess.biz.api.GoodsPromotionMultiLevelService;
import cn.htdt.marketprocess.biz.conversion.GoodsPromotionAssert;
import cn.htdt.marketprocess.biz.promotionimpls.GoodsPromotionService;
import cn.htdt.marketprocess.dao.GoodsPromotionGoodsRelationDao;
import cn.htdt.marketprocess.dto.request.*;
import cn.htdt.marketprocess.dto.request.communitysolitaire.ReqCommunitySolitaireAddDTO;
import cn.htdt.marketprocess.dto.request.communitysolitaire.ReqCommunitySolitaireGoodsDTO;
import cn.htdt.marketprocess.dto.request.communitysolitaire.ReqCommunitySolitaireStoreDTO;
import cn.htdt.marketprocess.dto.response.ResGoodsPromotionPeriodDTO;
import cn.htdt.marketprocess.dto.response.ResGoodsPromotionRuleInfoDTO;
import cn.htdt.marketprocess.dto.response.ResMemberPriceDetailDTO;
import cn.htdt.marketprocess.dto.response.ResPromotionStoreRuleDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.*;
import cn.htdt.marketprocess.legacycenter.api.operat.*;
import cn.htdt.marketprocess.vo.AtomGoodsPromotionGoodsRelationVo;
import cn.htdt.userprocess.api.StoreProcessService;
import cn.htdt.userprocess.api.UcFansStoreSubscribeService;
import cn.htdt.userprocess.dto.request.MerchantStoreRequest;
import cn.htdt.userprocess.dto.request.user.ReqStoresDTO;
import cn.htdt.userprocess.dto.response.MerchantStoreResponse;
import cn.htdt.userprocess.dto.response.StoreInfoResponse;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品促销操作类
 *
 * <AUTHOR>
 * @date 2021-06-26
 */
@Slf4j
@DubboService
public class GoodsPromotionOperateServiceImpl implements GoodsPromotionOperateService {

    /**
     * 促销商品可用库存 promotion:goods:available:stock:活动编号:商品编号
     */
    private static final String PROMOTION_GOODS_STOCK_PREFIX = PromotionKeyConstant.PROMOTION_GOODS_STOCK_PREFIX;

    @Autowired
    private GoodsPromotionAssert goodsPromotionAssert;

    @Autowired
    private GoodsPromotionRuleAnalysisService goodsPromotionRuleAnalysisService;

    @Autowired
    private RedisUtil redisUtil;

    @Resource
    private AtomPromotionInfoOperatService promotionInfoOperatService;

    @Resource
    private AtomPromotionInfoAnalysisService promotionInfoAnalysisService;

    @Resource
    private AtomGoodsPromotionPeriodOperateService promotionPeriodOperateService;

    @Resource
    private AtomGoodsPromotionRuleOperateService promotionRuleOperateService;

    @Resource
    private AtomPromotionStoreRuleAnalysisService atomPromotionStoreRuleAnalysisService;

    @Resource
    private AtomPromotionStoreRuleOperateService promotionStoreRuleOperateService;

    @Resource
    private AtomPromotionStoreRelationAnalysisService promotionStoreRelationAnalysisService;

    @Resource
    private AtomPromotionStoreRelationOperatService promotionStoreRelationOperatService;

    @Resource
    private AtomGoodsPromotionGoodsRelationAnalysisService atomGoodsPromotionGoodsRelationAnalysisService;

    @Resource
    private AtomGoodsPromotionRuleAnalysisService atomGoodsPromotionRuleAnalysisService;

    @Resource
    private AtomGoodsPromotionPeriodAnalysisService atomGoodsPromotionPeriodAnalysisService;

    @Resource
    private AtomGoodsPromotionGoodsRelationOperateService atomGoodsPromotionGoodsRelationOperateService;

    @Resource
    private AtomGoodsPromotionPeriodOperateService atomGoodsPromotionPeriodOperateService;
    /**
     * 类目
     */
    @Resource
    private AtomGoodsPromotionCategoryRelationOperateService atomGoodsPromotionCategoryRelationOperateService;

    @DubboReference
    private LegacyGoodsCenterService legacyGoodsCenterService;

    @Resource
    private GoodsPromotionImageService goodsPromotionImageService;

    @Resource
    private GoodsPromotionMultiLevelService goodsPromotionMultiLevelService;

    @Resource
    private GoodsPromotionGoodsRelationDao goodsPromotionGoodsRelationDao;

    @Resource
    private List<GoodsPromotionService> goodsPromotionServices;

    @DubboReference
    StoreProcessService storeProcessService;

    @Value("${gzh.template-msg.project-name:您关注的店铺发布了新的接龙}")
    private String defaultProjectName;

    @Value("${gzh.template-msg.goods-name-size:10}")
    private int goodsNameSize;

    @DubboReference
    private UcFansStoreSubscribeService ucFansStoreSubscribeService;

    /**
     * 创建商品促销活动-设置活动规则（设置活动基本信息、活动规则、参与条件）
     *
     * @param goodsPromotionInfoDTO
     * @return
     * <AUTHOR>
     * @date 2021-06-28
     */
    @Override
    public ExecuteDTO<ResGoodsPromotionRuleInfoDTO> saveGoodsPromotionRule(
            ReqGoodsPromotionInfoDTO goodsPromotionInfoDTO) {
        log.info("**GoodsPromotionOperateServiceImpl.saveGoodsPromotionRule创建商品促销活动信息**start**");
        log.info("**GoodsPromotionOperateServiceImpl.saveGoodsPromotionRule入参：{}", goodsPromotionInfoDTO);
        // 入参校验
        goodsPromotionAssert.saveGoodsPromotionRuleAssert(goodsPromotionInfoDTO);

        // 存储返回的结果信息
        ResGoodsPromotionRuleInfoDTO resGoodsPromotionRuleInfoDTO = new ResGoodsPromotionRuleInfoDTO();

        // 如果前端没有传活动编号，表示是新增
        if (StringUtils.isBlank(goodsPromotionInfoDTO.getPromotionNo())) {
            ExecuteDTO executeDTO = this.addSavePromotionRule(goodsPromotionInfoDTO, resGoodsPromotionRuleInfoDTO);
            if (!executeDTO.successFlag()) {
                return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
            }
        } else {
            // 如果前端传了活动信息编号，则表示是修改更新操作
            ExecuteDTO executeDTO = this.editSavePromotionRule(goodsPromotionInfoDTO, resGoodsPromotionRuleInfoDTO);
            if (!executeDTO.successFlag()) {
                return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
            }
        }

        // 创建/更新 活动规则缓存
        this.setPromotionInfoAndRuleToRedis(goodsPromotionInfoDTO.getPromotionNo());
        log.info("**GoodsPromotionOperateServiceImpl.saveGoodsPromotionRule创建商品促销活动信息**end**");

        // 返回结果
        return ExecuteDTO.success(resGoodsPromotionRuleInfoDTO);
    }

    /**
     * 如果商品促销活动处于进行中，对商品促销活动操作之前，对促销活动先上锁，避免由于用户操作影响其他流程
     *
     * @param promotionNo
     * <AUTHOR>
     * @date 2021-07-19
     */
    private boolean lockGoodsPromotion(String promotionNo) {
        String redisLockKey = PromotionKeyConstant.EDIT_SECOND_SKILL_PROMOTION_LOCK + promotionNo;
        return redisUtil.setnx(redisLockKey, "1", RedisKeyTTLConstant.EXPIRE_SECOND_30);
    }

    /**
     * 当编辑完活动信息后，解锁
     *
     * @param promotionNo
     * <AUTHOR>
     * @date 2021-07-19
     */
    private void unLockGoodsPromotion(String promotionNo) {
        String redisLockKey = PromotionKeyConstant.EDIT_SECOND_SKILL_PROMOTION_LOCK + promotionNo;
        redisUtil.del(redisLockKey);
    }

    /**
     * 新增保存活动规则数据
     *
     * @param goodsPromotionInfoDTO
     * @param resGoodsPromotionRuleInfoDTO
     * @return
     * <AUTHOR>
     * @date 2021-09-22
     */
    private ExecuteDTO addSavePromotionRule(ReqGoodsPromotionInfoDTO goodsPromotionInfoDTO,
                                            ResGoodsPromotionRuleInfoDTO resGoodsPromotionRuleInfoDTO) {
        // 生成促销活动的编号，默认是 秒杀的活动编号
        String promotionNo = MarketFormGenerator.genGoodsSKPromotionNo();
        // 限时购活动，生成限时购活动编号
        if (PromotionTypeEnum.LIMIT_TIME.getCode().equals(goodsPromotionInfoDTO.getPromotionType())) {
            promotionNo = MarketFormGenerator.genGoodsLTPromotionNo();
        }
        // 商品预售活动，生成商品预售活动编码
        if (PromotionTypeEnum.PRE_SALE.getCode().equals(goodsPromotionInfoDTO.getPromotionType())) {
            promotionNo = MarketFormGenerator.genGoodsPSPromotionNo();
        }
        goodsPromotionInfoDTO.setPromotionNo(promotionNo);
        resGoodsPromotionRuleInfoDTO.setPromotionNo(promotionNo);
        // 保存活动基本信息
        AtomReqPromotionInfoDTO reqPromotionInfoDTO =
                BeanCopierUtil.copy(goodsPromotionInfoDTO, AtomReqPromotionInfoDTO.class);
        // 取活动场次的最小开始和最大结束时间入到promotion_info表
        LocalTime minStartTime = null;
        LocalTime maxEndTime = null;
        for (ReqGoodsPromotionPeriodDTO periodDTO : goodsPromotionInfoDTO.getGoodsPromotionPeriodDTOList()) {
            LocalTime startTime = periodDTO.getStartTime();
            if (null == minStartTime || (startTime != null && startTime.isBefore(minStartTime))) {
                minStartTime = startTime;
            }
            LocalTime endTime = periodDTO.getEndTime();
            if (null == maxEndTime || (endTime != null && endTime.isAfter(maxEndTime))) {
                maxEndTime = endTime;
            }
        }
        reqPromotionInfoDTO.setDailyStartTime(minStartTime);
        reqPromotionInfoDTO.setDailyEndTime(maxEndTime);
        // 设置指定时间段
        reqPromotionInfoDTO.setEffectivePeriodType(EffectivePeriodTypeEnum.EFFECTIVE_PERIOD_TYPE_TWO.getCode());
        // 创建时，活动的状态设为  1000:草稿状态
        reqPromotionInfoDTO.setStatus(PromotionStatusEnum.ONLY_SAVED.getCode());
        // 活动类型设置为 商品促销
        reqPromotionInfoDTO.setActivityType(ActivityTypeEnum.PROMOTION.getCode());
        ExecuteDTO promotionInfoExecuteDTO = promotionInfoOperatService.saveGoodsPromotionInfo(reqPromotionInfoDTO);
        log.info("保存促销活动基本信息出参：{}", promotionInfoExecuteDTO);
        if (promotionInfoExecuteDTO == null || !promotionInfoExecuteDTO.successFlag()) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }

        // 保存活动规则信息
        AtomReqGoodsPromotionRuleDTO goodsPromotionRuleDTO =
                BeanCopierUtil.copy(goodsPromotionInfoDTO, AtomReqGoodsPromotionRuleDTO.class);
        ExecuteDTO promotionRuleExecuteDTO =
                promotionRuleOperateService.saveGoodsPromotionRule(goodsPromotionRuleDTO);
        log.info("保存促销活动规则信息出参：{}", promotionInfoExecuteDTO);
        if (promotionRuleExecuteDTO == null || !promotionRuleExecuteDTO.successFlag()) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }

        // 保存活动的时间段信息
        List<AtomReqGoodsPromotionPeriodDTO> periodDTOList = BeanCopierUtil
                .copyList(goodsPromotionInfoDTO.getGoodsPromotionPeriodDTOList(),
                        AtomReqGoodsPromotionPeriodDTO.class);
        for (AtomReqGoodsPromotionPeriodDTO periodDTO : periodDTOList) {
            periodDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
            // 生成促销活动时间段编号
            periodDTO.setPeriodNo(MarketFormGenerator.genGoodsPeriodNo());
            // 设置创建用户、店铺以及商家信息
            periodDTO.setCreateNo(goodsPromotionInfoDTO.getCreateNo());
            periodDTO.setCreateName(goodsPromotionInfoDTO.getCreateName());
            periodDTO.setMerchantNo(goodsPromotionInfoDTO.getMerchantNo());
            periodDTO.setMerchantName(goodsPromotionInfoDTO.getMerchantName());
            periodDTO.setStoreNo(goodsPromotionInfoDTO.getStoreNo());
            periodDTO.setStoreName(goodsPromotionInfoDTO.getStoreName());
        }
        ExecuteDTO savePeriodExecuteDTO =
                promotionPeriodOperateService.batchSaveGoodsPromotionPeriod(periodDTOList);
        log.info("保存活动的时间段出参：{}", savePeriodExecuteDTO);
        if (savePeriodExecuteDTO == null || !savePeriodExecuteDTO.successFlag()) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        // 设置返回的时间段和时间段编号的对应关系数据
        List<ResGoodsPromotionPeriodDTO> resPeriodDTOList = BeanCopierUtil.copyList(periodDTOList, ResGoodsPromotionPeriodDTO.class);
        // 封装返回的结果信息
        resGoodsPromotionRuleInfoDTO.setPromotionNo(promotionNo);
        resGoodsPromotionRuleInfoDTO.setGoodsPromotionPeriodDTOList(resPeriodDTOList);

        return ExecuteDTO.success();
    }

    /**
     * 编辑保存促销活动规则数据
     *
     * @param goodsPromotionInfoDTO
     * @param resGoodsPromotionRuleInfoDTO
     * @return
     * <AUTHOR>
     * @date 2021-09-22
     */
    private ExecuteDTO editSavePromotionRule(ReqGoodsPromotionInfoDTO goodsPromotionInfoDTO, ResGoodsPromotionRuleInfoDTO resGoodsPromotionRuleInfoDTO) {
        // 操作正在进行中的活动时需要加锁
        boolean lockFlag = lockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
        if (!lockFlag) {
            return ExecuteDTO.error(CommonCode.CODE_10000003.getCode(), "编辑操作前加锁失败，确定是否编辑时有其他操作");
        }

        // 更新基本信息
        AtomReqPromotionInfoDTO reqPromotionInfoDTO =
                BeanCopierUtil.copy(goodsPromotionInfoDTO, AtomReqPromotionInfoDTO.class);
        // 取活动场次的最小开始和最大结束时间入到promotion_info表
        LocalTime minStartTime = null;
        LocalTime maxEndTime = null;
        List<ReqGoodsPromotionPeriodDTO> updatePeriodList = new ArrayList<>();  //存放要更新的活动场次数据
        List<ReqGoodsPromotionPeriodDTO> addPeriodList = new ArrayList<>();  //存放要新增的活动场次数据
        for (ReqGoodsPromotionPeriodDTO periodDTO : goodsPromotionInfoDTO.getGoodsPromotionPeriodDTOList()) {
            LocalTime startTime = periodDTO.getStartTime();
            if (null == minStartTime || (startTime != null && startTime.isBefore(minStartTime))) {
                minStartTime = startTime;
            }
            LocalTime endTime = periodDTO.getEndTime();
            if (null == maxEndTime || (endTime != null && endTime.isAfter(maxEndTime))) {
                maxEndTime = endTime;
            }
            // 由于编辑操作，可能会删除之前的活动场次，然后新增新的活动场次数据，所以，对于编辑操作传过来的活动场次数据，有的是更新，有的是新增
            if (StringUtils.isNotBlank(periodDTO.getPeriodNo())) {
                updatePeriodList.add(periodDTO);
            } else {
                // 生成促销活动时间段编号
                periodDTO.setPeriodNo(MarketFormGenerator.genGoodsPeriodNo());
                addPeriodList.add(periodDTO);
            }
        }
        reqPromotionInfoDTO.setDailyStartTime(minStartTime);
        reqPromotionInfoDTO.setDailyEndTime(maxEndTime);
        ExecuteDTO updateGoodsPromotionInfo =
                promotionInfoOperatService.updateGoodsPromotionInfo(reqPromotionInfoDTO);
        log.info("更新促销活动信息出参：{}", updateGoodsPromotionInfo);
        if (updateGoodsPromotionInfo == null || !updateGoodsPromotionInfo.successFlag()) {
            // 操作结束后，解锁
            unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }

        // 更新活动规则信息
        AtomReqGoodsPromotionRuleDTO goodsPromotionRuleDTO =
                BeanCopierUtil.copy(goodsPromotionInfoDTO, AtomReqGoodsPromotionRuleDTO.class);
        ExecuteDTO updateGoodsPromotionRule =
                promotionRuleOperateService.updateGoodsPromotionRule(goodsPromotionRuleDTO);
        log.info("更新促销活动规则信息出参：{}", updateGoodsPromotionRule);
        if (updateGoodsPromotionRule == null || !updateGoodsPromotionRule.successFlag()) {
            // 操作结束后，解锁
            unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }

        // 编辑操作更新活动场次数据
        if (CollectionUtils.isNotEmpty(updatePeriodList)) {
            List<AtomReqGoodsPromotionPeriodDTO> periodDTOList = BeanCopierUtil.copyList(updatePeriodList,
                    AtomReqGoodsPromotionPeriodDTO.class);
            ExecuteDTO updateGoodsPromotionPeriod =
                    promotionPeriodOperateService.batchUpdateGoodsPromotionPeriod(periodDTOList);
            log.info("更新活动的时间段出参：{}", updateGoodsPromotionPeriod);
            if (updateGoodsPromotionPeriod == null || !updateGoodsPromotionPeriod.successFlag()) {
                // 操作结束后，解锁
                unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            // 查询根据活动编号活动场次数据，和提交过来的编辑数据对比，确定是否有被删除的活动场次，
            // 有的话，删除数据库，同时删除对应场次下的商品
            AtomReqGoodsPromotionPeriodDTO reqPeriodInfo = new AtomReqGoodsPromotionPeriodDTO();
            reqPeriodInfo.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
            ExecuteDTO<List<AtomResGoodsPromotionPeriodDTO>> databasePeriodList =
                    atomGoodsPromotionPeriodAnalysisService.getGoodsPromotionPeriodByPromotionNo(reqPeriodInfo);
            if (null == databasePeriodList) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            if (!databasePeriodList.successFlag()) {
                return ExecuteDTO.error(databasePeriodList.getStatus(), databasePeriodList.getMsg());
            }
            if (CollectionUtils.isNotEmpty(databasePeriodList.getData())) {
                // 存储待删除的活动场次
                List<String> deletePeriodList = new ArrayList<>();
                databasePeriodList.getData().forEach(databasePeriod -> {
                    boolean deleteFlag = true;  //标识该条数据库存储的场次是否被前端移除
                    for (ReqGoodsPromotionPeriodDTO updatePeriod : updatePeriodList) {
                        if (databasePeriod.getPeriodNo().equals(updatePeriod.getPeriodNo())) {
                            deleteFlag = false;
                        }
                    }
                    if (deleteFlag) {
                        deletePeriodList.add(databasePeriod.getPeriodNo());
                    }
                });
                if (CollectionUtils.isNotEmpty(deletePeriodList)) {
                    // 删除对应场次下的关联商品数据
                    AtomReqGoodsPromotionGoodsRelationDTO relationDTO = new AtomReqGoodsPromotionGoodsRelationDTO();
                    relationDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
                    relationDTO.setPeriodNoList(deletePeriodList);
                    ExecuteDTO deleteGoodsExecuteDTO = atomGoodsPromotionGoodsRelationOperateService.deletePromotionGoodsByNo(relationDTO);
                    if (null == deleteGoodsExecuteDTO) {
                        return ExecuteDTO.error(CommonCode.CODE_10000003);
                    }
                    if (!deleteGoodsExecuteDTO.successFlag()) {
                        return ExecuteDTO.error(deleteGoodsExecuteDTO.getStatus(), deleteGoodsExecuteDTO.getMsg());
                    }
                    // 删除活动场次数据
                    AtomReqGoodsPromotionPeriodDTO reqPeriodDTO = new AtomReqGoodsPromotionPeriodDTO();
                    reqPeriodDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
                    reqPeriodDTO.setPeriodNoList(deletePeriodList);
                    ExecuteDTO deletePeriodExecuteDTO = atomGoodsPromotionPeriodOperateService.deletePeriodByNo(reqPeriodDTO);
                    if (null == deletePeriodExecuteDTO) {
                        return ExecuteDTO.error(CommonCode.CODE_10000003);
                    }
                    if (!deletePeriodExecuteDTO.successFlag()) {
                        return ExecuteDTO.error(deletePeriodExecuteDTO.getStatus(), deletePeriodExecuteDTO.getMsg());
                    }
                }
            }
        }
        // 编辑新增活动场次
        if (CollectionUtils.isNotEmpty(addPeriodList)) {
            List<AtomReqGoodsPromotionPeriodDTO> periodDTOList = BeanCopierUtil.copyList(addPeriodList,
                    AtomReqGoodsPromotionPeriodDTO.class);
            for (AtomReqGoodsPromotionPeriodDTO periodDTO : periodDTOList) {
                periodDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
                // 设置创建用户、店铺以及商家信息
                periodDTO.setCreateNo(goodsPromotionInfoDTO.getCreateNo());
                periodDTO.setCreateName(goodsPromotionInfoDTO.getCreateName());
                periodDTO.setMerchantNo(goodsPromotionInfoDTO.getMerchantNo());
                periodDTO.setMerchantName(goodsPromotionInfoDTO.getMerchantName());
                periodDTO.setStoreNo(goodsPromotionInfoDTO.getStoreNo());
                periodDTO.setStoreName(goodsPromotionInfoDTO.getStoreName());
            }
            ExecuteDTO savePeriodExecuteDTO =
                    promotionPeriodOperateService.batchSaveGoodsPromotionPeriod(periodDTOList);
            log.info("保存活动的时间段出参：{}", savePeriodExecuteDTO);
            if (savePeriodExecuteDTO == null || !savePeriodExecuteDTO.successFlag()) {
                // 操作结束后，解锁
                unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
        }

        // 设置返回的时间段和时间段编号的对应关系数据
        List<ResGoodsPromotionPeriodDTO> resPeriodDTOList = BeanCopierUtil.copyList(goodsPromotionInfoDTO.getGoodsPromotionPeriodDTOList()
                , ResGoodsPromotionPeriodDTO.class);
        // 如果编辑活动，操作结束后，解锁
        unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
        // 封装返回的结果信息
        resGoodsPromotionRuleInfoDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
        resGoodsPromotionRuleInfoDTO.setGoodsPromotionPeriodDTOList(resPeriodDTOList);

        return ExecuteDTO.success();
    }

    /**
     * 创建商品促销活动-设置参与店铺
     *
     * @param reqPromotionRuleDTO
     * @return
     * <AUTHOR>
     * @date 2021-06-28
     */
    @Override
    public ExecuteDTO savePromotionStoreRule(ReqPromotionRuleDTO reqPromotionRuleDTO) {
        log.info("**GoodsPromotionOperateServiceImpl.savePromotionStoreRule创建商品促销活动-设置参与店铺**start**");
        log.info("**GoodsPromotionOperateServiceImpl.savePromotionStoreRule入参：{}", reqPromotionRuleDTO);
        // 入参校验
        goodsPromotionAssert.savePromotionStoreRuleAssert(reqPromotionRuleDTO);

        // 保存活动参与的店铺规则信息，只有平台角色创建促销活动时，才会设置活动店铺
        if (PlatformTypeEnum.PLATFORM_COMPANY.getCode().equals(reqPromotionRuleDTO.getSourceType())) {
            // 先根据活动编号查询促销活动的店铺规则数据
            ExecuteDTO<AtomResPromotionStoreRuleDTO> promotionStoreRule = atomPromotionStoreRuleAnalysisService
                    .selectPromotionStoreRuleByPromotionNo(reqPromotionRuleDTO.getPromotionNo());
            log.info("先根据活动编号查询促销活动的店铺规则数据出参：{}", promotionStoreRule);
            if (promotionStoreRule == null || !promotionStoreRule.successFlag()) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }

            // 编辑活动时需要加锁
            boolean lockFlag = lockGoodsPromotion(reqPromotionRuleDTO.getPromotionNo());
            if (!lockFlag) {
                return ExecuteDTO.error(CommonCode.CODE_10000003.getCode(), "编辑操作前加锁失败，确定是否编辑时有其他操作");
            }

            // 不存在时插入新增，存在时更新
            if (null == promotionStoreRule.getData()) {
                AtomReqPromotionStoreRuleDTO storeRuleDTO =
                        BeanCopierUtil.copy(reqPromotionRuleDTO, AtomReqPromotionStoreRuleDTO.class);
                ExecuteDTO executeDTO = promotionStoreRuleOperateService.savePromotionStoreRule(storeRuleDTO);
                log.info("新增促销活动的店铺规则数据出参：{}", executeDTO);
                if (executeDTO == null || !executeDTO.successFlag()) {
                    // 操作结束后，解锁
                    unLockGoodsPromotion(reqPromotionRuleDTO.getPromotionNo());
                    return ExecuteDTO.error(CommonCode.CODE_10000003);
                }
            } else {
                AtomReqPromotionStoreRuleDTO storeRuleDTO =
                        BeanCopierUtil.copy(reqPromotionRuleDTO, AtomReqPromotionStoreRuleDTO.class);
                ExecuteDTO executeDTO = promotionStoreRuleOperateService.updatePromotionStoreRule(storeRuleDTO);
                log.info("更新促销活动的店铺规则数据出参：{}", executeDTO);
                if (executeDTO == null || !executeDTO.successFlag()) {
                    // 操作结束后，解锁
                    unLockGoodsPromotion(reqPromotionRuleDTO.getPromotionNo());
                    return ExecuteDTO.error(CommonCode.CODE_10000003);
                }
            }

            // 操作结束后，解锁
            unLockGoodsPromotion(reqPromotionRuleDTO.getPromotionNo());
        }

        log.info("**GoodsPromotionOperateServiceImpl.savePromotionStoreRule创建商品促销活动-设置参与店铺**end**");
        return ExecuteDTO.success();
    }

    /**
     * 创建商品促销活动-设置参与商品
     * <p>
     * 秒杀活动和限时购活动设置商品共用
     *
     * @param goodsRelationDTOList
     * @return
     * <AUTHOR>
     * @date 2021-06-28
     */
    @Override
    public ExecuteDTO savePromotionGoodsRelation(List<ReqGoodsPromotionGoodsRelationDTO> goodsRelationDTOList) {
        log.info("**GoodsPromotionOperateServiceImpl.savePromotionGoodsRelation创建商品促销活动-设置参与商品**start**");
        log.info("**GoodsPromotionOperateServiceImpl.savePromotionGoodsRelation入参：{}", goodsRelationDTOList);
        // 入参校验
        goodsPromotionAssert.savePromotionGoodsRelationAssert(goodsRelationDTOList);

        // 加redis缓存锁，避免在进行中时对活动做了处理
        boolean lockFlag = lockGoodsPromotion(goodsRelationDTOList.get(0).getPromotionNo());
        if (!lockFlag) {
            return ExecuteDTO.error(CommonCode.CODE_10000003.getCode(), "编辑操作前加锁失败，确定是否编辑时有其他操作");
        }
        // 根据活动编号、活动场次编号查询出活动下所有设置的商品
        AtomReqGoodsPromotionGoodsRelationDTO reqGoodsPromotionGoodsRelationDTO =
                new AtomReqGoodsPromotionGoodsRelationDTO();
        reqGoodsPromotionGoodsRelationDTO.setPromotionNo(goodsRelationDTOList.get(0).getPromotionNo());
        reqGoodsPromotionGoodsRelationDTO.setPeriodNo(goodsRelationDTOList.get(0).getPeriodNo());
        ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> goodsRelationList =
                atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList(reqGoodsPromotionGoodsRelationDTO);
        log.info("根据活动编号查询原先设置的参与商品数据出参：{}", goodsRelationList);
        if (null == goodsRelationList || !goodsRelationList.successFlag()) {
            // 解锁
            unLockGoodsPromotion(goodsRelationDTOList.get(0).getPromotionNo());
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }

        // 如果活动正在进行中（非草稿状态，避免复制了一个正在进行中的活动），页面只允许做删除商品和新增新的商品操作，
        // 找出数据库中哪些商品在页面被删除了，哪些是新增的新商品，页面删除的，清除数据库中对应的数据，页面新增的新的商品，保存到数据库
        if (CollectionUtils.isNotEmpty(goodsRelationList.getData()) && !PromotionStatusEnum.ONLY_SAVED.getCode()
                .equals(goodsRelationList.getData().get(0).getStatus()) && !DateUtil.getLocalDateTimeNow()
                .isBefore(goodsRelationList.getData().get(0).getEffectiveTime()) && !DateUtil.getLocalDateTimeNow()
                .isAfter(goodsRelationList.getData().get(0).getInvalidTime())) {
            //存储数据库历史的主品数据，避免后面保存新增新的商品数据时，重复存储主品数据
            Set<String> oldParentGoodsSet = new HashSet<>();
            // 抽取页面删除了的活动商品
            List<AtomResGoodsPromotionGoodsRelationDTO> deleteGoodsNoList = new ArrayList<>();
            for (AtomResGoodsPromotionGoodsRelationDTO datum : goodsRelationList.getData()) {
                String parentGoods = datum.getPromotionNo() + "-" + datum.getPeriodNo() + "-" + datum.getParentGoodsNo();
                oldParentGoodsSet.add(parentGoods);
                boolean deleteFlag = true;
                for (ReqGoodsPromotionGoodsRelationDTO goodsRelationDTO : goodsRelationDTOList) {
                    // 数据库查询出来的商品在提交的商品中可以找到，说明没有被删除
                    // 注意：数据库查询出来的包括了主品，避免编辑正在进行中的活动时，移除某个子品，把主品页删除了
                    if (datum.getGoodsNo().equals(goodsRelationDTO.getGoodsNo()) || datum.getGoodsNo().equals(goodsRelationDTO.getParentGoodsNo())) {
                        deleteFlag = false;
                        break;
                    }
                }
                // 记录被删除的商品
                if (deleteFlag) {
                    deleteGoodsNoList.add(datum);
                }
            }
            // 抽取页面新增的新商品
            List<ReqGoodsPromotionGoodsRelationDTO> newAddGoodsList = new ArrayList<>();
            // 需要更新的商品行(修改了活动价格，活动库存，个人限购数的商品行)
            List<ReqGoodsPromotionGoodsRelationDTO> updateGoodsList = new ArrayList<>();
            List<ReqGoodsPromotionGoodsRelationDTO> updateSettingStockNumGoodsList = new ArrayList<>();
            for (ReqGoodsPromotionGoodsRelationDTO goodsRelationDTO : goodsRelationDTOList) {
                boolean newAddFlag = true; //是新增商品的标识
                for (AtomResGoodsPromotionGoodsRelationDTO datum : goodsRelationList.getData()) {
                    // 提交过来的商品，数据库找得到，表示不是新增的新商品
                    if (goodsRelationDTO.getGoodsNo().equals(datum.getGoodsNo())) {
                        if (null != goodsRelationDTO.getSettingStockNum() && !goodsRelationDTO.getSettingStockNum().equals(datum.getSettingStockNum())) {
                            updateSettingStockNumGoodsList.add(goodsRelationDTO);
                        } else if ((null != goodsRelationDTO.getPromotionPrice() && BigDecimalUtil.notEqual(goodsRelationDTO.getPromotionPrice(), datum.getPromotionPrice()))
                                || (null != goodsRelationDTO.getLimitBuyNum() && !goodsRelationDTO.getLimitBuyNum().equals(datum.getLimitBuyNum()))) {
                            updateGoodsList.add(goodsRelationDTO);
                        }
                        newAddFlag = false;
                        break;
                    }
                }
                if (newAddFlag) {
                    newAddGoodsList.add(goodsRelationDTO);
                }
            }
            // 把数据库中多余的活动商品给删除
            if (CollectionUtils.isNotEmpty(deleteGoodsNoList)) {
                List<AtomReqGoodsPromotionGoodsRelationDTO> deleteGoodsList =
                        BeanCopierUtil.copyList(deleteGoodsNoList, AtomReqGoodsPromotionGoodsRelationDTO.class);
                ExecuteDTO deleteNoRelation =
                        atomGoodsPromotionGoodsRelationOperateService.batchDeletePromotionGoodsNoRelation(deleteGoodsList);
                log.info("编辑正在进行中活动，删除数据库多余的活动商品出参：{}", deleteNoRelation);
                if (null == deleteNoRelation || !deleteNoRelation.successFlag()) {
                    // 解锁
                    unLockGoodsPromotion(goodsRelationDTOList.get(0).getPromotionNo());
                    return ExecuteDTO.error(CommonCode.CODE_10000003);
                }
                // 数据库删除了删除的商品后，删除对应商品存在缓存中的活动剩余库存数据
                for (AtomResGoodsPromotionGoodsRelationDTO entity : deleteGoodsNoList) {
                    String redisKey = String.format(PromotionKeyConstant.PROMOTION_GOODS_STOCK_PREFIX
                            , entity.getPromotionNo(), entity.getPeriodNo(), entity.getGoodsNo());
                    redisUtil.del(redisKey);
                }
            }

            // 首先根据活动编号和时间段编号查询活动场次信息
            AtomReqGoodsPromotionPeriodDTO reqPromotionPeriodDTO = new AtomReqGoodsPromotionPeriodDTO();
            reqPromotionPeriodDTO.setPromotionNo(goodsRelationDTOList.get(0).getPromotionNo());
            reqPromotionPeriodDTO.setPeriodNo(goodsRelationDTOList.get(0).getPeriodNo());
            ExecuteDTO<AtomResGoodsPromotionPeriodDTO> promotionPeriodDTOExecuteDTO =
                    atomGoodsPromotionPeriodAnalysisService.selectPromotionPeriodInfo(reqPromotionPeriodDTO);
            log.info("**查询活动场次信息出参：{}", promotionPeriodDTOExecuteDTO);
            if (null == promotionPeriodDTOExecuteDTO || !promotionPeriodDTOExecuteDTO.successFlag()) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            AtomResGoodsPromotionPeriodDTO promotionPeriodDTO = promotionPeriodDTOExecuteDTO.getData();
            if (null == promotionPeriodDTO) {
                return ExecuteDTO.error(CommonCode.CODE_10000002.getCode(), "没有找到活动信息");
            }
            // 保存之前先查询不可选择参与的商品编号（其他活动已经参与的，不可选择）
            /*
            * 去除商品互斥校验，商品在所有活动间均不做互斥校验
            AtomReqGoodsPromotionGoodsRelationDTO reqNoChoiceGoods = new AtomReqGoodsPromotionGoodsRelationDTO();
            reqNoChoiceGoods.setPromotionNo(goodsRelationDTOList.get(0).getPromotionNo());
            reqNoChoiceGoods.setEffectiveTime(promotionPeriodDTO.getEffectiveTime());
            reqNoChoiceGoods.setInvalidTime(promotionPeriodDTO.getInvalidTime());
            reqNoChoiceGoods.setStartTime(promotionPeriodDTO.getStartTime());
            reqNoChoiceGoods.setEndTime(promotionPeriodDTO.getEndTime());
            reqNoChoiceGoods.setSourceType(goodsRelationDTOList.get(0).getSourceType());
            reqNoChoiceGoods.setStoreNo(goodsRelationDTOList.get(0).getStoreNo());
            // 保存时，只排除其他活动中选择过的互斥商品
            reqNoChoiceGoods.setQueryType(NumConstant.TWO);
            ExecuteDTO<List<String>> noChoiceGoodsNoList =
                    atomGoodsPromotionGoodsRelationAnalysisService.getNoChoiceGoodsNoList(reqNoChoiceGoods);
            if (noChoiceGoodsNoList == null || !noChoiceGoodsNoList.successFlag()) {
                return ExecuteDTO.error(CommonCode.CODE_10000003.getCode(), "查询互斥商品时异常");
            }
            */
            // 提取要保存到数据库的活动商品数据
            List<AtomReqGoodsPromotionGoodsRelationDTO> atomGoodsRelationDTOList =
                    this.extractSavePromotionGoodsData(newAddGoodsList, goodsRelationList.getData(),
                            null, oldParentGoodsSet);
            // 保存新增商品
            if (CollectionUtils.isNotEmpty(atomGoodsRelationDTOList)) {
                ExecuteDTO executeDTO = atomGoodsPromotionGoodsRelationOperateService.savePromotionGoodsRelation(atomGoodsRelationDTOList);
                log.info("保存促销活动设置的商品数据出参：{}", executeDTO);
                if (executeDTO == null || !executeDTO.successFlag()) {
                    // 解锁
                    unLockGoodsPromotion(goodsRelationDTOList.get(0).getPromotionNo());
                    return ExecuteDTO.error(CommonCode.CODE_10000003);
                }
            }
            // 已有商品支持修改，可修改活动价格，促销库存，个人限购数，修改促销库存时要注意会修改remain_stock_num，需要计算差值
            if (ListUtil.isNotEmpty(updateGoodsList)) {
                this.updateGoodsPromotionGoodsRelation(updateGoodsList, false);
            }
            // 如果修改营销库存，则需要清空redis缓存中营销库存剩余数量
            if (ListUtil.isNotEmpty(updateSettingStockNumGoodsList)) {
                this.updateGoodsPromotionGoodsRelation(updateSettingStockNumGoodsList, true);
            }

            // 解锁
            unLockGoodsPromotion(goodsRelationDTOList.get(0).getPromotionNo());
        } else {
            // 首次创建时保存活动商品，或者编辑未开始的活动时，需要排除互斥商品以及提取主品数据用于保存，复制操作保存时不做处理
            List<AtomReqGoodsPromotionGoodsRelationDTO> atomGoodsRelationDTOList;
            if (!WhetherEnum.YES.getCode().equals(goodsRelationDTOList.get(0).getCopyFlag())) {
                // 首先根据活动编号和时间段编号查询活动场次信息
                AtomReqGoodsPromotionPeriodDTO reqPromotionPeriodDTO = new AtomReqGoodsPromotionPeriodDTO();
                reqPromotionPeriodDTO.setPromotionNo(goodsRelationDTOList.get(0).getPromotionNo());
                reqPromotionPeriodDTO.setPeriodNo(goodsRelationDTOList.get(0).getPeriodNo());
                ExecuteDTO<AtomResGoodsPromotionPeriodDTO> promotionPeriodDTOExecuteDTO =
                        atomGoodsPromotionPeriodAnalysisService.selectPromotionPeriodInfo(reqPromotionPeriodDTO);
                log.info("**查询活动场次信息出参：{}", promotionPeriodDTOExecuteDTO);
                if (null == promotionPeriodDTOExecuteDTO || !promotionPeriodDTOExecuteDTO.successFlag()) {
                    return ExecuteDTO.error(CommonCode.CODE_10000003);
                }
                AtomResGoodsPromotionPeriodDTO promotionPeriodDTO = promotionPeriodDTOExecuteDTO.getData();
                if (null == promotionPeriodDTO) {
                    return ExecuteDTO.error(CommonCode.CODE_10000002.getCode(), "没有找到活动信息");
                }

                // 保存之前先查询不可选择参与的商品编号（其他活动已经参与的，不可选择）
                /*
                * 去除商品互斥校验，商品在所有活动间均不做互斥校验
                AtomReqGoodsPromotionGoodsRelationDTO reqNoChoiceGoods = new AtomReqGoodsPromotionGoodsRelationDTO();
                reqNoChoiceGoods.setPromotionNo(goodsRelationDTOList.get(0).getPromotionNo());
                reqNoChoiceGoods.setEffectiveTime(promotionPeriodDTO.getEffectiveTime());
                reqNoChoiceGoods.setInvalidTime(promotionPeriodDTO.getInvalidTime());
                reqNoChoiceGoods.setStartTime(promotionPeriodDTO.getStartTime());
                reqNoChoiceGoods.setEndTime(promotionPeriodDTO.getEndTime());
                reqNoChoiceGoods.setSourceType(goodsRelationDTOList.get(0).getSourceType());
                reqNoChoiceGoods.setStoreNo(goodsRelationDTOList.get(0).getStoreNo());
                // 保存时，只排除其他活动中选择过的互斥商品
                reqNoChoiceGoods.setQueryType(NumConstant.TWO);
                ExecuteDTO<List<String>> noChoiceGoodsNoList =
                        atomGoodsPromotionGoodsRelationAnalysisService.getNoChoiceGoodsNoList(reqNoChoiceGoods);
                if (noChoiceGoodsNoList == null || !noChoiceGoodsNoList.successFlag()) {
                    return ExecuteDTO.error(CommonCode.CODE_10000003.getCode(), "查询互斥商品时异常");
                }
                */
                // 提取要保存到数据库的活动商品数据
                atomGoodsRelationDTOList = this.extractSavePromotionGoodsData(goodsRelationDTOList, null,
                        null, null);
            } else {
                atomGoodsRelationDTOList =
                        BeanCopierUtil.copyList(goodsRelationDTOList, AtomReqGoodsPromotionGoodsRelationDTO.class);
            }
            // 直接删除活动场次下全部的历史数据库的商品，重新保存(物理删除)
            AtomReqGoodsPromotionGoodsRelationDTO relationDTO = new AtomReqGoodsPromotionGoodsRelationDTO();
            relationDTO.setPromotionNo(goodsRelationDTOList.get(0).getPromotionNo());
            relationDTO.setPeriodNo(goodsRelationDTOList.get(0).getPeriodNo());
            ExecuteDTO deleteAll = atomGoodsPromotionGoodsRelationOperateService.deletePromotionGoodsByNo(relationDTO);
            log.info("活动未开始保存时，删除数据库历史数据操作出参：{}", deleteAll);
            if (null == deleteAll || !deleteAll.successFlag()) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            // 保存商品
            ExecuteDTO executeDTO = atomGoodsPromotionGoodsRelationOperateService.savePromotionGoodsRelation(atomGoodsRelationDTOList);
            log.info("保存促销活动设置的商品数据出参：{}", executeDTO);
            if (executeDTO == null || !executeDTO.successFlag()) {
                // 解锁
                unLockGoodsPromotion(goodsRelationDTOList.get(0).getPromotionNo());
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }

            // 非复制操作时，活动状态为草稿状态更新为未开始状态
            // 由于限时购活动，各个场次的商品时单独保存的，所以，保存活动场次下的商品后，不更新活动状态，后面由页面点击 “下一步” 直接更新
            if (!WhetherEnum.YES.getCode().equals(goodsRelationDTOList.get(0).getCopyFlag())
                    && !PromotionTypeEnum.LIMIT_TIME.getCode().equals(goodsRelationDTOList.get(0).getPromotionType())) {
                // 根据活动编号查询活动详情
                AtomReqPromotionInfoDTO reqPromotionInfo = new AtomReqPromotionInfoDTO();
                reqPromotionInfo.setPromotionNo(goodsRelationDTOList.get(0).getPromotionNo());
                ExecuteDTO<AtomResPromotionInfoDTO> promotionExecuteDTO =
                        promotionInfoAnalysisService.getPromotionInfo(reqPromotionInfo);
                // 查询结果分析
                if (null == promotionExecuteDTO) {
                    return ExecuteDTO.error(CommonCode.CODE_10000003);
                }
                if (!promotionExecuteDTO.successFlag()) {
                    return ExecuteDTO.error(promotionExecuteDTO.getStatus(), promotionExecuteDTO.getMsg());
                }
                if (promotionExecuteDTO.getData() != null) {
                    AtomResPromotionInfoDTO promotionInfoDTO = promotionExecuteDTO.getData();
                    AtomReqPromotionInfoDTO reqPromotionInfoDTO = new AtomReqPromotionInfoDTO();
                    reqPromotionInfoDTO.setPromotionNo(goodsRelationDTOList.get(0).getPromotionNo());
                    reqPromotionInfoDTO.setOldStatus(PromotionStatusEnum.ONLY_SAVED.getCode());    //待更新的草稿状态
                    if (DateUtil.getLocalDateTimeNow().isBefore(promotionInfoDTO.getEffectiveTime())) {
                        reqPromotionInfoDTO.setStatus(PromotionStatusEnum.NOT_STARTED.getCode());   //要更新的状态
                    } else if (!DateUtil.getLocalDateTimeNow().isAfter(promotionInfoDTO.getInvalidTime())) {
                        reqPromotionInfoDTO.setStatus(PromotionStatusEnum.IN_PROGRESS.getCode());   //要更新的状态
                    } else {
                        reqPromotionInfoDTO.setStatus(PromotionStatusEnum.EXPIRED.getCode());   //要更新的状态
                    }
                    ExecuteDTO updateGoodsPromotionInfo =
                            promotionInfoOperatService.updateGoodsPromotionInfo(reqPromotionInfoDTO);
                    log.info("**保存促销活动商品信息后，更新活动状态为未开始出参：{}", updateGoodsPromotionInfo);
                }
            }
        }

        // 如果编辑正在进行的活动，上锁成功后，操作结束后，解锁
        unLockGoodsPromotion(goodsRelationDTOList.get(0).getPromotionNo());

        log.info("**GoodsPromotionOperateServiceImpl.savePromotionGoodsRelation创建商品促销活动-设置参与商品**end**");

        return ExecuteDTO.success();
    }

    /**
     * @param updateGoodsList updateGoodsList
     * @param clearRedisKey   是否清除redis缓存中剩余营销库存数量，因为修改商品营销库存会修改剩余营销库存数量，需要重新取值，所以在此清楚
     */
    private void updateGoodsPromotionGoodsRelation(List<ReqGoodsPromotionGoodsRelationDTO> updateGoodsList, Boolean clearRedisKey) {
        if (ListUtil.isNotEmpty(updateGoodsList)) {
            for (ReqGoodsPromotionGoodsRelationDTO reqGoodsPromotionGoodsRelationDTO : updateGoodsList) {
                AtomGoodsPromotionGoodsRelationVo vo = BeanCopierUtil.copy(reqGoodsPromotionGoodsRelationDTO, AtomGoodsPromotionGoodsRelationVo.class);
                goodsPromotionGoodsRelationDao.updatePromotionGoodsRelation(vo);
                // 清除redis缓存中剩余营销库存数量
                if (clearRedisKey) {
                    redisUtil.del(String.format(PROMOTION_GOODS_STOCK_PREFIX, reqGoodsPromotionGoodsRelationDTO.getPromotionNo(), reqGoodsPromotionGoodsRelationDTO.getPeriodNo(), reqGoodsPromotionGoodsRelationDTO.getGoodsNo()));
                }
            }
        }
    }

    /**
     * 超级老板app端，设置限时购活动商品
     * <p>
     * 商品预售共用
     * <p>
     * 和PC端不同，app端各个场次的商品一起提交保存，PC端则是各个时间段商品分步保存
     *
     * @param goodsRelationDTOList
     * @return
     * <AUTHOR>
     * @date 2021-11-08
     */
    @Override
    public ExecuteDTO saveLimitTimeGoodsForApp(List<ReqGoodsPromotionGoodsRelationDTO> goodsRelationDTOList) {
        log.info("saveLimitTimeGoodsForApp-入参：{}", goodsRelationDTOList);
        // 入参校验
        goodsPromotionAssert.saveLimitTimeGoodsForAppAssert(goodsRelationDTOList);
        // 加redis缓存锁，避免在进行中时对活动做了处理
        boolean lockFlag = lockGoodsPromotion(goodsRelationDTOList.get(0).getPromotionNo());
        if (!lockFlag) {
            return ExecuteDTO.error(CommonCode.CODE_10000003.getCode(), "编辑操作前加锁失败，确定是否编辑时有其他操作");
        }
        // 根据活动编号查询出活动下设置的商品
        AtomReqGoodsPromotionGoodsRelationDTO reqGoodsPromotionGoodsRelationDTO =
                new AtomReqGoodsPromotionGoodsRelationDTO();
        reqGoodsPromotionGoodsRelationDTO.setPromotionNo(goodsRelationDTOList.get(0).getPromotionNo());
        ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> goodsRelationList =
                atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList(reqGoodsPromotionGoodsRelationDTO);
        log.info("根据活动编号查询原先设置的参与商品数据出参：{}", goodsRelationList);
        if (null == goodsRelationList || !goodsRelationList.successFlag()) {
            // 解锁
            unLockGoodsPromotion(goodsRelationDTOList.get(0).getPromotionNo());
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }

        // 如果活动正在进行中（非草稿状态），页面只允许做删除商品或者新增新的商品操作
        // 找出数据库中哪些商品在页面被删除了，哪些是新增的新商品，页面删除的，清除数据库中对应的数据，页面新增的新的商品，保存到数据库
        if (CollectionUtils.isNotEmpty(goodsRelationList.getData()) && !PromotionStatusEnum.ONLY_SAVED.getCode()
                .equals(goodsRelationList.getData().get(0).getStatus()) && !DateUtil.getLocalDateTimeNow()
                .isBefore(goodsRelationList.getData().get(0).getEffectiveTime()) && !DateUtil.getLocalDateTimeNow()
                .isAfter(goodsRelationList.getData().get(0).getInvalidTime())) {
            //存储数据库历史的主品数据，避免后面保存新增新的商品数据时，重复存储主品数据
            Set<String> oldParentGoodsSet = new HashSet<>();
            // 抽取页面删除了的活动商品
            List<AtomResGoodsPromotionGoodsRelationDTO> deleteGoodsNoList = new ArrayList<>();
            for (AtomResGoodsPromotionGoodsRelationDTO datum : goodsRelationList.getData()) {
                String parentGoods = datum.getPromotionNo() + "-" + datum.getPeriodNo() + "-" + datum.getParentGoodsNo();
                oldParentGoodsSet.add(parentGoods);
                boolean deleteFlag = true;
                for (ReqGoodsPromotionGoodsRelationDTO goodsRelationDTO : goodsRelationDTOList) {
                    // 数据库查询出来的商品在提交的商品中可以找到，说明没有被删除
                    // 注意：数据库查询出来的包括了主品，避免编辑正在进行中的活动时，移除某个子品，把主品页删除了
                    // 加上场次编号比较，避免相同活动的不同场次下设置了同样的商品
                    if (datum.getPeriodNo().equals(goodsRelationDTO.getPeriodNo()) &&
                            (datum.getGoodsNo().equals(goodsRelationDTO.getGoodsNo()) || datum.getGoodsNo()
                                    .equals(goodsRelationDTO.getParentGoodsNo()))) {
                        deleteFlag = false;
                        break;
                    }
                }
                // 记录被删除的商品
                if (deleteFlag) {
                    deleteGoodsNoList.add(datum);
                }
            }
            // 抽取页面新增的商品
            List<ReqGoodsPromotionGoodsRelationDTO> newAddGoodsList = new ArrayList<>();
            // 需要更新的商品行(修改了活动价格，活动库存，个人限购数的商品行)
            List<ReqGoodsPromotionGoodsRelationDTO> updateGoodsList = new ArrayList<>();
            List<ReqGoodsPromotionGoodsRelationDTO> updateSettingStockNumGoodsList = new ArrayList<>();
            for (ReqGoodsPromotionGoodsRelationDTO goodsRelationDTO : goodsRelationDTOList) {
                boolean newAddFlag = true;  //新增的商品标识
                for (AtomResGoodsPromotionGoodsRelationDTO datum : goodsRelationList.getData()) {
                    // 提交过来的商品，数据库找得到，表示不是新增的新商品
                    if (goodsRelationDTO.getPeriodNo().equals(datum.getPeriodNo())
                            && goodsRelationDTO.getGoodsNo().equals(datum.getGoodsNo())) {
                        if (null != goodsRelationDTO.getSettingStockNum() && !goodsRelationDTO.getSettingStockNum().equals(datum.getSettingStockNum())) {
                            updateSettingStockNumGoodsList.add(goodsRelationDTO);
                        } else if ((null != goodsRelationDTO.getPromotionPrice() && BigDecimalUtil.notEqual(goodsRelationDTO.getPromotionPrice(), datum.getPromotionPrice()))
                                || (null != goodsRelationDTO.getLimitBuyNum() && !goodsRelationDTO.getLimitBuyNum().equals(datum.getLimitBuyNum()))) {
                            updateGoodsList.add(goodsRelationDTO);
                        }

                        newAddFlag = false;
                        break;
                    }
                }
                if (newAddFlag) {
                    newAddGoodsList.add(goodsRelationDTO);
                }
            }
            // 把数据库中多余的活动商品给删除
            if (CollectionUtils.isNotEmpty(deleteGoodsNoList)) {
                List<AtomReqGoodsPromotionGoodsRelationDTO> deleteGoodsList =
                        BeanCopierUtil.copyList(deleteGoodsNoList, AtomReqGoodsPromotionGoodsRelationDTO.class);
                ExecuteDTO deleteNoRelation =
                        atomGoodsPromotionGoodsRelationOperateService.batchDeletePromotionGoodsNoRelation(deleteGoodsList);
                log.info("编辑正在进行中活动，删除数据库多余的活动商品出参：{}", deleteNoRelation);
                if (null == deleteNoRelation || !deleteNoRelation.successFlag()) {
                    // 解锁
                    unLockGoodsPromotion(goodsRelationDTOList.get(0).getPromotionNo());
                    return ExecuteDTO.error(CommonCode.CODE_10000003);
                }
                // 数据库删除了删除的商品后，删除对应商品存在缓存中的活动剩余库存数据
                for (AtomResGoodsPromotionGoodsRelationDTO entity : deleteGoodsNoList) {
                    String redisKey = String.format(PromotionKeyConstant.PROMOTION_GOODS_STOCK_PREFIX
                            , entity.getPromotionNo(), entity.getPeriodNo(), entity.getGoodsNo());
                    redisUtil.del(redisKey);
                }
            }

            // 保存新增的新商品
            if (CollectionUtils.isNotEmpty(newAddGoodsList)) {
                // 提取要批量保存到数据库的活动商品数据
                List<AtomReqGoodsPromotionGoodsRelationDTO> atomGoodsRelationDTOList =
                        this.extractBatchSavePromotionGoodsData(newAddGoodsList, oldParentGoodsSet);
                // 保存商品
                ExecuteDTO executeDTO = atomGoodsPromotionGoodsRelationOperateService.savePromotionGoodsRelation(atomGoodsRelationDTOList);
                log.info("保存促销活动设置的商品数据出参：{}", executeDTO);
                if (executeDTO == null || !executeDTO.successFlag()) {
                    // 解锁
                    unLockGoodsPromotion(goodsRelationDTOList.get(0).getPromotionNo());
                    return ExecuteDTO.error(CommonCode.CODE_10000003);
                }
            }

            // 已有商品支持修改，可修改活动价格，促销库存，个人限购数，修改促销库存时要注意会修改remain_stock_num，需要计算差值
            if (ListUtil.isNotEmpty(updateGoodsList)) {
                this.updateGoodsPromotionGoodsRelation(updateGoodsList, false);
            }
            // 如果修改营销库存，则需要清空redis缓存中营销库存剩余数量
            if (ListUtil.isNotEmpty(updateSettingStockNumGoodsList)) {
                this.updateGoodsPromotionGoodsRelation(updateSettingStockNumGoodsList, true);
            }

            // 解锁
            unLockGoodsPromotion(goodsRelationDTOList.get(0).getPromotionNo());
        } else {
            // 提取要批量保存到数据库的活动商品数据
            List<AtomReqGoodsPromotionGoodsRelationDTO> atomGoodsRelationDTOList =
                    this.extractBatchSavePromotionGoodsData(goodsRelationDTOList, null);
            // 直接删除活动下全部的历史数据库的商品，重新保存
            AtomReqGoodsPromotionGoodsRelationDTO relationDTO = new AtomReqGoodsPromotionGoodsRelationDTO();
            relationDTO.setPromotionNo(goodsRelationDTOList.get(0).getPromotionNo());
            ExecuteDTO deleteAll = atomGoodsPromotionGoodsRelationOperateService
                    .deletePromotionGoodsByNo(relationDTO);
            log.info("活动未开始保存时，删除数据库历史数据操作出参：{}", deleteAll);
            if (null == deleteAll || !deleteAll.successFlag()) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            // 保存商品
            ExecuteDTO executeDTO =
                    atomGoodsPromotionGoodsRelationOperateService.savePromotionGoodsRelation(atomGoodsRelationDTOList);
            log.info("保存促销活动设置的商品数据出参：{}", executeDTO);
            if (executeDTO == null || !executeDTO.successFlag()) {
                // 解锁
                unLockGoodsPromotion(goodsRelationDTOList.get(0).getPromotionNo());
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            // 活动状态为草稿状态更新为未开始状态
            AtomReqPromotionInfoDTO reqPromotionInfoDTO = new AtomReqPromotionInfoDTO();
            reqPromotionInfoDTO.setPromotionNo(goodsRelationDTOList.get(0).getPromotionNo());
            reqPromotionInfoDTO.setOldStatus(PromotionStatusEnum.ONLY_SAVED.getCode());    //待更新的草稿状态
            reqPromotionInfoDTO.setStatus(PromotionStatusEnum.NOT_STARTED.getCode());   //要更新的状态
            ExecuteDTO updateGoodsPromotionInfo =
                    promotionInfoOperatService.updateGoodsPromotionInfo(reqPromotionInfoDTO);
            log.info("**保存促销活动商品信息后，更新活动状态为未开始出参：{}", updateGoodsPromotionInfo);
        }

        // 如果编辑正在进行的活动，上锁成功后，操作结束后，解锁
        unLockGoodsPromotion(goodsRelationDTOList.get(0).getPromotionNo());

        log.info("**GoodsPromotionOperateServiceImpl.savePromotionGoodsRelation创建商品促销活动-设置参与商品**end**");

        return ExecuteDTO.success();
    }

    /**
     * 提取要批量保存到数据库的活动商品数据
     *
     * @param goodsRelationDTOList 要保存的商品数据
     * @param oldParentGoodsSet    数据库中已经存储的主品数据
     */
    private List<AtomReqGoodsPromotionGoodsRelationDTO> extractBatchSavePromotionGoodsData(
            List<ReqGoodsPromotionGoodsRelationDTO> goodsRelationDTOList, Set<String> oldParentGoodsSet) {
        // 从提交的商品数据中去重找出系列商品子品对应的主品数据
        List<AtomReqGoodsPromotionGoodsRelationDTO> parentGoodsList = new ArrayList<>();
        Set<String> parentGoodsInfoSet = new HashSet<>(); // 存放要入库的主品信息，用于去重
        if (CollectionUtils.isNotEmpty(oldParentGoodsSet)) {
            parentGoodsInfoSet.addAll(oldParentGoodsSet);
        }
        // 找提交过来要保存的商品是否存在互斥
        Map<String, List<String>> errorGoodsNosMap = new HashMap<>();  //不可设置的互斥商品编号,key为promotionNo-periodNo
        for (ReqGoodsPromotionGoodsRelationDTO goodsRelationDTO : goodsRelationDTOList) {
            // 首次创建活动设置参与商品的新增保存或者编辑未开始活动时，保存的活动剩余库存和设置的活动库存一致
            goodsRelationDTO.setRemainStockNum(goodsRelationDTO.getSettingStockNum());
            // 从提交过来的要保存的非主商品数据中提取出主品数据
            // 非子品商品（普通商品），直接跳过
            if (WhetherEnum.NO.getCode().equals(goodsRelationDTO.getSubGoodsFlag())) {
                continue;
            }
            // 子品商品，根据 活动编号 + 活动时间段编号 + parentGoodsNo 进行去重
            StringBuilder parentGoodsInfo = new StringBuilder();
            parentGoodsInfo.append(goodsRelationDTO.getPromotionNo()).append("-").append(goodsRelationDTO.getPeriodNo())
                    .append("-").append(goodsRelationDTO.getParentGoodsNo());
            if (parentGoodsInfoSet.contains(parentGoodsInfo.toString())) {
                continue;
            }
            // 设置系列商品子品对应的主品要保存的数据
            AtomReqGoodsPromotionGoodsRelationDTO parentGoods = new AtomReqGoodsPromotionGoodsRelationDTO();
            parentGoods.setPromotionNo(goodsRelationDTO.getPromotionNo());
            parentGoods.setGoodsNo(goodsRelationDTO.getParentGoodsNo());
            parentGoods.setPeriodNo(goodsRelationDTO.getPeriodNo());
            parentGoods.setPromotionType(goodsRelationDTO.getPromotionType());
            parentGoodsList.add(parentGoods);
            parentGoodsInfoSet.add(parentGoodsInfo.toString());
        }
        // 保存活动参与的商品信息（主品和非主品）
        List<AtomReqGoodsPromotionGoodsRelationDTO> atomGoodsRelationDTOList =
                BeanCopierUtil.copyList(goodsRelationDTOList, AtomReqGoodsPromotionGoodsRelationDTO.class);
        if (CollectionUtils.isNotEmpty(parentGoodsList)) {
            atomGoodsRelationDTOList.addAll(parentGoodsList);
        }
        return atomGoodsRelationDTOList;
    }

    /**
     * 提取要保存到数据库的活动商品数据
     *
     * @param goodsRelationDTOList 要保存的商品数据
     * @param dbGoodsList          数据库已保存的商品数据
     * @param noChoiceGoodsNoList  不可参与当前活动的商品数据
     * @param oldParentGoodsSet    数据库已经保存了的主品数据
     */
    private List<AtomReqGoodsPromotionGoodsRelationDTO> extractSavePromotionGoodsData(
            List<ReqGoodsPromotionGoodsRelationDTO> goodsRelationDTOList,
            List<AtomResGoodsPromotionGoodsRelationDTO> dbGoodsList,
            List<String> noChoiceGoodsNoList,
            Set<String> oldParentGoodsSet) {
        // 从提交的商品数据中去重找出系列商品子品对应的主品数据
        List<AtomReqGoodsPromotionGoodsRelationDTO> parentGoodsList = new ArrayList<>();
        // 存放要入库的主品信息，用于去重
        Set<String> parentGoodsInfoSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(oldParentGoodsSet)) {
            parentGoodsInfoSet.addAll(oldParentGoodsSet);
        }
        // 找提交过来要保存的商品是否存在互斥
        List<String> errorGoodsNoList = new ArrayList<>();  //不可设置的互斥商品编号
        if (CollectionUtils.isNotEmpty(goodsRelationDTOList)) {
            for (ReqGoodsPromotionGoodsRelationDTO goodsRelationDTO : goodsRelationDTOList) {
                // 找互斥商品
                if (CollectionUtils.isNotEmpty(noChoiceGoodsNoList) && noChoiceGoodsNoList.contains(goodsRelationDTO.getGoodsNo())) {
                    errorGoodsNoList.add(goodsRelationDTO.getGoodsNo());
                }

                // 首次创建活动设置参与商品的新增保存或者编辑未开始活动时，保存的活动剩余库存和设置的活动库存一致
                goodsRelationDTO.setRemainStockNum(goodsRelationDTO.getSettingStockNum());

                // 从提交过来的要保存的非主商品数据中提取出主品数据
                // 非子品商品（普通商品），直接跳过
                if (WhetherEnum.NO.getCode().equals(goodsRelationDTO.getSubGoodsFlag())) {
                    continue;
                }
                // 子品商品，根据 活动编号 + 活动时间段编号 + parentGoodsNo 进行去重
                StringBuilder parentGoodsInfo = new StringBuilder();
                parentGoodsInfo.append(goodsRelationDTO.getPromotionNo()).append("-").append(goodsRelationDTO.getPeriodNo())
                        .append("-").append(goodsRelationDTO.getParentGoodsNo());
                if (parentGoodsInfoSet.contains(parentGoodsInfo.toString())) {
                    continue;
                }
                // 设置系列商品子品对应的主品要保存的数据
                AtomReqGoodsPromotionGoodsRelationDTO parentGoods = new AtomReqGoodsPromotionGoodsRelationDTO();
                parentGoods.setPromotionNo(goodsRelationDTO.getPromotionNo());
                parentGoods.setGoodsNo(goodsRelationDTO.getParentGoodsNo());
                parentGoods.setPeriodNo(goodsRelationDTO.getPeriodNo());
                parentGoods.setPromotionType(goodsRelationDTO.getPromotionType());
                parentGoodsList.add(parentGoods);
                parentGoodsInfoSet.add(parentGoodsInfo.toString());
            }
        }

        // 数据库已存储的历史数据，校验是否存在互斥（针对进行中的活动）
        if (CollectionUtils.isNotEmpty(dbGoodsList) && CollectionUtils.isNotEmpty(noChoiceGoodsNoList)) {
            for (AtomResGoodsPromotionGoodsRelationDTO dbGoods : dbGoodsList) {
                // 找互斥商品，不考虑数据库查询出来的主品数据
                if (StringUtils.isNotBlank(dbGoods.getParentGoodsNo()) && noChoiceGoodsNoList.contains(dbGoods.getGoodsNo())) {
                    errorGoodsNoList.add(dbGoods.getGoodsNo());
                }
            }
        }
        // 如果存在互斥的商品，则直接抛出异常，响应给前端
        if (CollectionUtils.isNotEmpty(errorGoodsNoList)) {
            // 解锁
            String promotionNo = null;
            if (CollectionUtils.isNotEmpty(goodsRelationDTOList)) {
                promotionNo = goodsRelationDTOList.get(0).getPromotionNo();
            } else if (CollectionUtils.isNotEmpty(dbGoodsList)) {
                promotionNo = dbGoodsList.get(0).getPromotionNo();
            }
            unLockGoodsPromotion(promotionNo);
            throw new BaseException(MarketErrorCode.CODE_17000617.getCode(),
                    null, errorGoodsNoList);
        }
        // 保存活动参与的商品信息（主品和非主品）
        List<AtomReqGoodsPromotionGoodsRelationDTO> atomGoodsRelationDTOList =
                BeanCopierUtil.copyList(goodsRelationDTOList, AtomReqGoodsPromotionGoodsRelationDTO.class);
        if (CollectionUtils.isNotEmpty(parentGoodsList)) {
            atomGoodsRelationDTOList.addAll(parentGoodsList);
        }
        return atomGoodsRelationDTOList;
    }

    /**
     * 商品促销活动上下架操作（平台角色上下架平台活动、店铺角色上下架店铺活动）
     *
     * @param upOrDownDTO
     * @return
     * <AUTHOR>
     * @date 2021-07-02
     */
    @Override
    public ExecuteDTO upDownGoodsPromotion(ReqGoodsPromotionUpOrDownDTO upOrDownDTO) {
        log.info("**GoodsPromotionOperateServiceImpl.upDownGoodsPromotion商品促销活动上下架操作**start**");
        log.info("**GoodsPromotionOperateServiceImpl.upDownGoodsPromotion入参：{}", upOrDownDTO);
        // 入参校验
        goodsPromotionAssert.upDownGoodsPromotionAssert(upOrDownDTO);

        // 根据活动编号查询活动详情
        AtomReqPromotionInfoDTO reqPromotionInfoDTO = new AtomReqPromotionInfoDTO();
        reqPromotionInfoDTO.setPromotionNo(upOrDownDTO.getPromotionNo());
        ExecuteDTO<AtomResPromotionInfoDTO> promotionExecuteDTO =
                promotionInfoAnalysisService.getPromotionInfo(reqPromotionInfoDTO);
        // 查询结果分析
        if (null == promotionExecuteDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!promotionExecuteDTO.successFlag()) {
            return ExecuteDTO.error(promotionExecuteDTO.getStatus(), promotionExecuteDTO.getMsg());
        }
        AtomResPromotionInfoDTO promotionInfoDTO = promotionExecuteDTO.getData();
        // 没有找到要上下架的活动时，退出
        if (null == promotionInfoDTO) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000600);
        }
        // 活动状态为 草稿状态时，不能上下架
        if (PromotionStatusEnum.ONLY_SAVED.getCode().equals(promotionInfoDTO.getStatus())) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000601);
        }
        // 活动已过期，不允许上下架
        // 取当前的日期时间，用于判断活动是否过期
        LocalDateTime now = DateUtil.getLocalDateTimeNow();
        if (null != promotionInfoDTO.getInvalidTime() && now.isAfter(promotionInfoDTO.getInvalidTime())) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000602);
        }

        // 更新商品促销活动的上下架状态
        AtomReqPromotionInfoDTO atomReqPromotionInfoDTO =
                BeanCopierUtil.copy(upOrDownDTO, AtomReqPromotionInfoDTO.class);

        if (WhetherEnum.YES.getCode().equals(upOrDownDTO.getUpDownFlag())) {
            // 设置上下架时间为当前时间
            atomReqPromotionInfoDTO.setLatestUpDownTime(DateUtil.getLocalDateTime());
            if (null == promotionInfoDTO.getFirstUpTime()) {
                atomReqPromotionInfoDTO.setFirstUpTime(LocalDateTime.now());
            }
        }


        //上架 && 商家自建 && 首次上架
        if (WhetherEnum.YES.getCode().equals(upOrDownDTO.getUpDownFlag())
                && SourceTypeEnum.SOURCE_TYPE_1002.getCode().equals(promotionInfoDTO.getSourceType())
                && null == promotionInfoDTO.getFirstUpTime()
                && PromotionTypeEnum.MEMBER_PRICE.getCode().equals(promotionInfoDTO.getPromotionType())) {
            //首次上架时间 2023年6月后才有首次上架时间
            atomReqPromotionInfoDTO.setFirstUpTime(LocalDateTime.now());
            //查询商家下所有的共享会员店
            ReqStoresDTO reqStoresDTO = new ReqStoresDTO();
            reqStoresDTO.setMerchantNo(promotionInfoDTO.getMerchantNo());
            reqStoresDTO.setMemberSharing(WhetherEnum.YES.getCode());
            ExecuteDTO<List<StoreInfoResponse>> storeList = storeProcessService.getStoreList(reqStoresDTO);
            if (storeList.successFlag() && CollectionUtils.isNotEmpty(storeList.getData())) {
                //此活动分发给对应共享会员店
                List<ReqPromotionStoreRelationDTO> insertList = new ArrayList<>();

                for (StoreInfoResponse datum : storeList.getData()) {
                    ReqPromotionStoreRelationDTO req;

                    req = new ReqPromotionStoreRelationDTO();
                    req.setRuleNo(MarketFormGenerator.getStoreRuleNo());
                    req.setPromotionNo(promotionInfoDTO.getPromotionNo());
                    req.setStoreSourceType(SourceTypeEnum.SOURCE_TYPE_1002.getCode());
                    req.setMerchantNo(promotionInfoDTO.getMerchantNo());
                    req.setStoreNo(datum.getStoreNo());
                    req.setUpDownFlag(upOrDownDTO.getUpDownFlag());
                    req.setLatestUpDownTime(LocalDateTime.now());
                    insertList.add(req);
                }
                //建立活动与店铺关联关系
                promotionStoreRelationOperatService.batchOperate(insertList, null);
            }
        }

        //商家社群接龙店铺上下架
        if (PromotionTypeEnum.COMMUNITY_SOLITAIRE_SJ.getCode().equals(promotionInfoDTO.getPromotionType())) {

            AtomReqPromotionStoreRelationDTO atomReqPromotionStoreRelationDTO = new AtomReqPromotionStoreRelationDTO();
            atomReqPromotionStoreRelationDTO.setPromotionNo(promotionInfoDTO.getPromotionNo());
            atomReqPromotionStoreRelationDTO.setMerchantNo(promotionInfoDTO.getMerchantNo());
            atomReqPromotionStoreRelationDTO.setDisableFlag(StoreUseEnum.ENABLE.getCode());
            ExecuteDTO<List<AtomResPromotionStoreRelationDTO>> atomResPromotionStoreRelationDTO = promotionStoreRelationAnalysisService.getStoreRelationList(atomReqPromotionStoreRelationDTO);
            if (null == atomResPromotionStoreRelationDTO) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            if (!atomResPromotionStoreRelationDTO.successFlag()) {
                return ExecuteDTO.error(atomResPromotionStoreRelationDTO.getStatus(), atomResPromotionStoreRelationDTO.getMsg());
            }
            List<AtomResPromotionStoreRelationDTO> atomResPromotionStoreRelationList = atomResPromotionStoreRelationDTO.getData();
            if (CollectionUtils.isEmpty(atomResPromotionStoreRelationList)) {
                return ExecuteDTO.error(CommonCode.CODE_10000002, "商家没有指定分发店铺");
            }
            String storeType = null;
            for (AtomResPromotionStoreRelationDTO storeRelation : atomResPromotionStoreRelationList) {
                if (StringUtils.isEmpty(storeRelation.getStoreNo())) {
                    storeType = storeRelation.getStoreType();
                    break;
                }
            }
            List<String> storeNoList = Lists.newArrayList();
            //查询商家下的全部店铺信息
            MerchantStoreRequest merchantStoreRequest = new MerchantStoreRequest();
            merchantStoreRequest.setMerchantNo(promotionInfoDTO.getMerchantNo());
            ExecuteDTO<List<MerchantStoreResponse>> merchantStoreResponseExecuteDTO = storeProcessService.queryMerchantStore(merchantStoreRequest);
            if (null == merchantStoreResponseExecuteDTO) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            if (!merchantStoreResponseExecuteDTO.successFlag()) {
                return ExecuteDTO.error(merchantStoreResponseExecuteDTO.getStatus(), merchantStoreResponseExecuteDTO.getMsg());
            }
            List<MerchantStoreResponse> merchantStoreList = merchantStoreResponseExecuteDTO.getData();
            if (CollectionUtils.isEmpty(merchantStoreList)) {
                return ExecuteDTO.error(MarketErrorCode.CODE_17000636);
            }
            if (StringUtils.isNotEmpty(storeType)) {
                if (StoreTypeEnum.STORE_TYPE_ONE.getCode().equals(storeType)) {
                    //全部店铺
                    storeNoList = merchantStoreList.stream().map(MerchantStoreResponse::getStoreNo).collect(Collectors.toList());
                } else if (StoreTypeEnum.STORE_TYPE_THREE.getCode().equals(storeType)) {
                    //指定店铺
                    storeNoList = atomResPromotionStoreRelationList.stream().filter(storeRelation -> StringUtils.isNotEmpty(storeRelation.getStoreNo()))
                            .map(AtomResPromotionStoreRelationDTO::getStoreNo).collect(Collectors.toList());
                }
            } else {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            //分发店铺上下架
            if (StringUtils.isEmpty(upOrDownDTO.getStoreNo())) {
                //商家操作上下架
                AtomReqPromotionStoreRelationDTO upDownStoreRelationDTO = BeanCopierUtil.copy(upOrDownDTO, AtomReqPromotionStoreRelationDTO.class);
                ;
                upDownStoreRelationDTO.setLatestUpDownTime(LocalDateTime.now());
                ExecuteDTO upDownStoreRelationExecuteDTO = promotionStoreRelationOperatService.updateSjStoreRelation(upDownStoreRelationDTO);
                if (null == upDownStoreRelationExecuteDTO) {
                    return ExecuteDTO.error(CommonCode.CODE_10000003);
                }
                if (!upDownStoreRelationExecuteDTO.successFlag()) {
                    return ExecuteDTO.error(upDownStoreRelationExecuteDTO.getStatus(), upDownStoreRelationExecuteDTO.getMsg());
                }
            }
        }

        //商家可以修改商家活动主表上下架, 店铺可以修改店铺活动主表上下架
        ExecuteDTO upOrDownExecuteDTO = promotionInfoOperatService.updateGoodsPromotionInfo(atomReqPromotionInfoDTO);
        if (null == upOrDownExecuteDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!upOrDownExecuteDTO.successFlag()) {
            return ExecuteDTO.error(upOrDownExecuteDTO.getStatus(), upOrDownExecuteDTO.getMsg());
        }

        // 创建/更新 活动规则缓存
        this.setPromotionInfoAndRuleToRedis(upOrDownDTO.getPromotionNo());
        log.info("**GoodsPromotionOperateServiceImpl.upDownGoodsPromotion商品促销活动上下架操作**end**");

        return ExecuteDTO.success();
    }

    /**
     * 店铺角色-上下架平台促销活动
     *
     * @param upOrDownDTO
     * @return
     * <AUTHOR>
     * @date 2021-07-02
     */
    @Override
    public ExecuteDTO storeUpDownPlatformPromotion(ReqGoodsPromotionUpOrDownDTO upOrDownDTO) {
        log.info("**GoodsPromotionOperateServiceImpl.storeUpDownPlatformPromotion店铺角色-上下架平台促销活动**start**");
        log.info("**GoodsPromotionOperateServiceImpl.storeUpDownPlatformPromotion入参：{}", upOrDownDTO);
        // 入参校验
        goodsPromotionAssert.storeUpDownPlatformPromotionAssert(upOrDownDTO);

        // 根据活动编号查询活动详情
        AtomReqPromotionInfoDTO reqPromotionInfoDTO = new AtomReqPromotionInfoDTO();
        reqPromotionInfoDTO.setPromotionNo(upOrDownDTO.getPromotionNo());
        ExecuteDTO<AtomResPromotionInfoDTO> promotionExecuteDTO =
                promotionInfoAnalysisService.getPromotionInfo(reqPromotionInfoDTO);
        // 查询结果分析
        if (null == promotionExecuteDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!promotionExecuteDTO.successFlag()) {
            return ExecuteDTO.error(promotionExecuteDTO.getStatus(), promotionExecuteDTO.getMsg());
        }
        AtomResPromotionInfoDTO promotionInfoDTO = promotionExecuteDTO.getData();
        // 没有找到要上下架的活动时，退出
        if (null == promotionInfoDTO) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000600);
        }
        // 活动状态为 草稿状态时，不能上下架（店铺角色查询的平台活动列表其实是非草稿状态的，加此逻辑避免列表查询出来了草稿状态的bug）
        if (PromotionStatusEnum.ONLY_SAVED.getCode().equals(promotionInfoDTO.getStatus())) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000601);
        }
        // 活动已过期，不允许上下架
        // 取当前日期时间用于判断活动是否下架
        LocalDateTime now = DateUtil.getLocalDateTimeNow();
        if (null != promotionInfoDTO.getInvalidTime() && now.isAfter(promotionInfoDTO.getInvalidTime())) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000602);
        }
        // 如果平台活动已经被下架了，不允许再执行上下架操作
        if (WhetherEnum.NO.getCode().equals(promotionInfoDTO.getUpDownFlag())) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000603);
        }

        // 查询平台活动设置的店铺参与详情数据
        AtomReqPromotionStoreRuleDTO reqDto = new AtomReqPromotionStoreRuleDTO();
        reqDto.setPromotionNo(upOrDownDTO.getPromotionNo());
        reqDto.setStoreNo(upOrDownDTO.getStoreNo());
        ExecuteDTO<AtomResPromotionStoreRuleDTO> storeRuleAndDetailExecuteDTO =
                atomPromotionStoreRuleAnalysisService.selectPromotionStoreRuleAndDetail(reqDto);
        log.info("查询平台活动设置的店铺参与详情数据出参：{}", storeRuleAndDetailExecuteDTO);
        if (null == storeRuleAndDetailExecuteDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!storeRuleAndDetailExecuteDTO.successFlag()) {
            return ExecuteDTO.error(storeRuleAndDetailExecuteDTO.getStatus(), storeRuleAndDetailExecuteDTO.getMsg());
        }
        AtomResPromotionStoreRuleDTO storeRuleAndDetail = storeRuleAndDetailExecuteDTO.getData();
        // 沒有查到该活动设置的店铺参与规则信息，直接退出
        if (null == storeRuleAndDetail) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000302);
        }
        // 如果平台设置的是店铺报名参与，但该店铺被平台删除了，则不允许执行上下架操作
        if (StoreTypeEnum.STORE_TYPE_TWO.getCode().equals(storeRuleAndDetail.getStoreType()) && StringUtils
                .isBlank(storeRuleAndDetail.getEnrollNo())) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000302);
        }

        // 查询活动店铺关联上下架表中是否存在店铺操作平台活动上下架的记录，没有的话，直接插入，有的话，更新上下架状态
        AtomReqPromotionStoreRelationDTO relationDTO = new AtomReqPromotionStoreRelationDTO();
        relationDTO.setPromotionNo(upOrDownDTO.getPromotionNo());
        relationDTO.setStoreNo(upOrDownDTO.getStoreNo());
        ExecuteDTO<AtomResPromotionStoreRelationDTO> goodsPromotionStoreRelation =
                promotionStoreRelationAnalysisService.getGoodsPromotionStoreRelation(relationDTO);
        if (null == goodsPromotionStoreRelation) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!goodsPromotionStoreRelation.successFlag()) {
            return ExecuteDTO.error(goodsPromotionStoreRelation.getStatus(), goodsPromotionStoreRelation.getMsg());
        }
        // 如果数据库没有记录，则新增
        if (null == goodsPromotionStoreRelation.getData()) {
            AtomReqPromotionStoreRelationDTO saveStoreRelation =
                    BeanCopierUtil.copy(upOrDownDTO, AtomReqPromotionStoreRelationDTO.class);
            saveStoreRelation.setRuleNo(MarketFormGenerator.genStoreRuleNo());
            ExecuteDTO executeDTO = promotionStoreRelationOperatService.saveStoreRelation(saveStoreRelation);
            if (null == executeDTO) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            if (!executeDTO.successFlag()) {
                return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
            }
        } else {
            // 数据库已经存在店铺上下架活动的记录，更新上下架状态
            AtomReqPromotionStoreRelationDTO updateRelation =
                    BeanCopierUtil.copy(upOrDownDTO, AtomReqPromotionStoreRelationDTO.class);
            ExecuteDTO executeDTO = promotionStoreRelationOperatService.modifyStoreShelves(updateRelation);
            if (null == executeDTO) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            if (!executeDTO.successFlag()) {
                return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
            }
        }

        // 创建/更新 活动规则缓存
        this.setPromotionInfoAndRuleToRedis(upOrDownDTO.getPromotionNo());
        log.info("**GoodsPromotionOperateServiceImpl.storeUpDownPlatformPromotion店铺角色-上下架平台促销活动**end**");

        return ExecuteDTO.success();
    }

    /**
     * 删除商品促销活动
     *202503 删除
     * @param goodsPromotionInfoDTO
     * @return
     * <AUTHOR>
     * @date 2021-07-05
     */
    @Override
    public ExecuteDTO deleteGoodsPromotion(ReqGoodsPromotionInfoDTO goodsPromotionInfoDTO) {
        log.info("**GoodsPromotionOperateServiceImpl.deleteGoodsPromotion删除商品促销活动**start**");
        log.info("**GoodsPromotionOperateServiceImpl.deleteGoodsPromotion入参：{}", goodsPromotionInfoDTO);
        // 入参校验
        goodsPromotionAssert.deleteGoodsPromotionAssert(goodsPromotionInfoDTO);

        // 根据活动编号查询出活动下所有设置的商品
        AtomReqGoodsPromotionGoodsRelationDTO reqGoodsPromotionGoodsRelationDTO =
                new AtomReqGoodsPromotionGoodsRelationDTO();
        reqGoodsPromotionGoodsRelationDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
        ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> goodsRelationList =
                atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList(reqGoodsPromotionGoodsRelationDTO);
        log.info("根据活动编号查询原先设置的参与商品数据出参：{}", goodsRelationList);
        if (null == goodsRelationList) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!goodsRelationList.successFlag()) {
            return ExecuteDTO.error(goodsRelationList.getStatus(), goodsRelationList.getMsg());
        }

        //查询活动主表信息
        AtomReqPromotionInfoDTO atomReqPromotionInfoDTO = new AtomReqPromotionInfoDTO();
        atomReqPromotionInfoDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
        ExecuteDTO<AtomResPromotionInfoDTO> atomResPromotionInfoDTO = promotionInfoAnalysisService.getPromotionInfo(atomReqPromotionInfoDTO);
        if (null == atomResPromotionInfoDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!atomResPromotionInfoDTO.successFlag()) {
            return ExecuteDTO.error(atomResPromotionInfoDTO.getStatus(), atomResPromotionInfoDTO.getMsg());
        }
        AtomResPromotionInfoDTO atomResPromotionInfo = atomResPromotionInfoDTO.getData();
        if (atomResPromotionInfo == null) {
            return ExecuteDTO.error(CommonCode.CODE_10000003.getCode(), "活动不存在");
        }
        if (!atomResPromotionInfo.getPromotionType().equals(goodsPromotionInfoDTO.getPromotionType())) {
            return ExecuteDTO.error(CommonCode.CODE_10000002.getCode(), "不可删除其他来源的活动");
        }

        // 加入缓存锁
        boolean lockFlag = lockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
        if (!lockFlag) {
            return ExecuteDTO.error(CommonCode.CODE_10000003.getCode(), "编辑操作前加锁失败，确定是否编辑时有其他操作");
        }

        // 满减满折活动时，删除活动关联的类目信息
        if (PromotionTypeEnum.FULL_PROMOTION.getCode().equals(goodsPromotionInfoDTO.getPromotionType())) {
            ExecuteDTO deletePromotionCategory =
                    atomGoodsPromotionCategoryRelationOperateService.delPromotionCategoryRelation(goodsPromotionInfoDTO.getPromotionNo());
            if (null == deletePromotionCategory) {
                // 解锁
                unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            if (!deletePromotionCategory.successFlag()) {
                // 解锁
                unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
                return ExecuteDTO.error(deletePromotionCategory.getStatus(), deletePromotionCategory.getMsg());
            }
        }

        // 删除商品促销活动规则信息
        ExecuteDTO deletePromotionRule =
                promotionRuleOperateService.deleteGoodsPromotionRule(goodsPromotionInfoDTO.getPromotionNo());
        if (null == deletePromotionRule) {
            // 解锁
            unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!deletePromotionRule.successFlag()) {
            // 解锁
            unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
            return ExecuteDTO.error(deletePromotionRule.getStatus(), deletePromotionRule.getMsg());
        }

        // 删除商品促销活动基本信息
        ExecuteDTO deletePromotionInfo =
                promotionInfoOperatService.deleteGoodsPromotionInfo(goodsPromotionInfoDTO.getPromotionNo());
        if (null == deletePromotionInfo) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!deletePromotionInfo.successFlag()) {
            // 解锁
            unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
            return ExecuteDTO.error(deletePromotionInfo.getStatus(), deletePromotionInfo.getMsg());
        }

        // 删除促销活动时间段信息
        ExecuteDTO deletePeriod =
                promotionPeriodOperateService.deletePeriodByPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
        if (null == deletePeriod) {
            // 解锁
            unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!deletePeriod.successFlag()) {
            // 解锁
            unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
            return ExecuteDTO.error(deletePeriod.getStatus(), deletePeriod.getMsg());
        }

        // 删除商品促销活动设置的参与店铺信息，针对于平台促销活动
        if (PlatformTypeEnum.PLATFORM_COMPANY.getCode().equals(goodsPromotionInfoDTO.getSourceType())) {
            ExecuteDTO deleteStoreRule =
                    promotionStoreRuleOperateService.deletePromotionStoreRule(goodsPromotionInfoDTO.getPromotionNo());
            if (null == deleteStoreRule) {
                // 解锁
                unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            if (!deleteStoreRule.successFlag()) {
                // 解锁
                unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
                return ExecuteDTO.error(deleteStoreRule.getStatus(), deleteStoreRule.getMsg());
            }
        }

        // 删除商品促销活动设置的参与商品信息
        ExecuteDTO deleteGoods = atomGoodsPromotionGoodsRelationOperateService
                .deletePromotionGoodsRelation(goodsPromotionInfoDTO.getPromotionNo());
        if (null == deleteGoods) {
            // 解锁
            unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!deleteGoods.successFlag()) {
            // 解锁
            unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
            return ExecuteDTO.error(deleteGoods.getStatus(), deleteGoods.getMsg());
        }

        //商家社群接龙删除商家分发店铺信息
        if (PromotionTypeEnum.COMMUNITY_SOLITAIRE_SJ.getCode().equals(goodsPromotionInfoDTO.getPromotionType())) {
            ExecuteDTO deleteStoreRelation = promotionStoreRelationOperatService.deletePromotionStoreRelation(goodsPromotionInfoDTO.getPromotionNo());
            if (null == deleteStoreRelation) {
                // 解锁
                unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            if (!deleteStoreRelation.successFlag()) {
                // 解锁
                unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
                return ExecuteDTO.error(deleteStoreRelation.getStatus(), deleteStoreRelation.getMsg());
            }
        }

        // 清空促销互动下所有商品的活动库存缓存
        if (CollectionUtils.isNotEmpty(goodsRelationList.getData())) {
            for (AtomResGoodsPromotionGoodsRelationDTO datum : goodsRelationList.getData()) {
                String redisKey = String.format(PromotionKeyConstant.PROMOTION_GOODS_STOCK_PREFIX
                        , datum.getPromotionNo(), datum.getPeriodNo(), datum.getGoodsNo());
                redisUtil.del(redisKey);
            }
        }

        log.info("**GoodsPromotionOperateServiceImpl.deleteGoodsPromotion删除商品促销活动**end**");

        // 解锁
        unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());

        // 删除该活动规则缓存
        this.delPromotionInfoAndRuleCache(goodsPromotionInfoDTO.getPromotionNo());
        // 成功返回
        return ExecuteDTO.success();
    }

    /**
     * 复制商品促销活动
     * <p>
     * 秒杀活动和限时购活动共用
     *
     * @param goodsPromotionInfoDTO
     * @return
     * <AUTHOR>
     * @date 2021-07-06
     */
    @Override
    public ExecuteDTO copyGoodsPromotionInfo(ReqGoodsPromotionInfoDTO goodsPromotionInfoDTO) {
        log.info("**GoodsPromotionOperateServiceImpl.copyGoodsPromotionInfo-复制商品促销活动-start**");
        log.info("**GoodsPromotionOperateServiceImpl.copyGoodsPromotionInfo-入参：{}", goodsPromotionInfoDTO);
        // 入参校验
        goodsPromotionAssert.copyGoodsPromotionInfoAssert(goodsPromotionInfoDTO);

        // 查询出被复制的活动信息，用于重新保存生成复制的活动
        // 根据活动编号查询活动规则信息
        ReqPromotionInfoDTO promotionInfoDTO = new ReqPromotionInfoDTO();
        promotionInfoDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
        ExecuteDTO<ResGoodsPromotionRuleInfoDTO> goodsPromotionRuleInfo =
                goodsPromotionRuleAnalysisService.getGoodsPromotionRuleInfo(promotionInfoDTO);
        if (!goodsPromotionRuleInfo.successFlag()) {
            return ExecuteDTO.error(goodsPromotionRuleInfo.getStatus(), goodsPromotionRuleInfo.getMsg());
        }
        ResGoodsPromotionRuleInfoDTO promotionRuleInfoDTO = goodsPromotionRuleInfo.getData();
        if (promotionRuleInfoDTO == null) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000600);
        }
        // 如果复制的是平台活动，则查询设置的店铺规则信息
        ResPromotionStoreRuleDTO promotionStoreRuleDTO = null;
        if (PlatformTypeEnum.PLATFORM_COMPANY.getCode().equals(promotionRuleInfoDTO.getSourceType())) {
            ExecuteDTO<AtomResPromotionStoreRuleDTO> storeRuleAndDetail = atomPromotionStoreRuleAnalysisService
                    .selectPromotionStoreRuleByPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
            if (null == storeRuleAndDetail) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            if (!storeRuleAndDetail.successFlag()) {
                return ExecuteDTO.error(storeRuleAndDetail.getStatus(), storeRuleAndDetail.getMsg());
            }
            if (null == storeRuleAndDetail.getData()) {
                return ExecuteDTO.error(MarketErrorCode.CODE_17000604);
            }
            promotionStoreRuleDTO = BeanCopierUtil.copy(storeRuleAndDetail.getData(), ResPromotionStoreRuleDTO.class);
        }
        // 查询设置的参与商品信息
        AtomReqGoodsPromotionGoodsRelationDTO reqPromotionGoods = new AtomReqGoodsPromotionGoodsRelationDTO();
        reqPromotionGoods.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
        ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> goodsRelationListExecuteDTO =
                atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList(reqPromotionGoods);
        if (null == goodsRelationListExecuteDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!goodsRelationListExecuteDTO.successFlag()) {
            return ExecuteDTO.error(goodsRelationListExecuteDTO.getStatus(), goodsRelationListExecuteDTO.getMsg());
        }

        // 将查询出来的被复制的活动信息重新保存，生成复制的活动信息
        // 保存活动规则信息
        ReqGoodsPromotionInfoDTO reqGoodsPromotionInfoDTO =
                BeanCopierUtil.copy(promotionRuleInfoDTO, ReqGoodsPromotionInfoDTO.class);
        // 复制操作，创建活动的来源都来自于PC端
        reqGoodsPromotionInfoDTO.setCreateSource(PromotionCreateSourceEnum.PC.getCode());
        reqGoodsPromotionInfoDTO.setPromotionNo(null);  //清空活动编号，在保存时重新生成新的活动编号
        reqGoodsPromotionInfoDTO.setPromotionName(reqGoodsPromotionInfoDTO.getPromotionName() + "-副本"); //活动名称变更
        reqGoodsPromotionInfoDTO.setGoodsPromotionPeriodDTOList(BeanCopierUtil
                .copyList(promotionRuleInfoDTO.getGoodsPromotionPeriodDTOList(), ReqGoodsPromotionPeriodDTO.class));
        ExecuteDTO<ResGoodsPromotionRuleInfoDTO> savePromotionRule =
                this.saveGoodsPromotionRule(reqGoodsPromotionInfoDTO);
        if (!savePromotionRule.successFlag()) {
            return ExecuteDTO.error(savePromotionRule.getStatus(), savePromotionRule.getMsg());
        }
        // 抽取复制前后的活动场次时间段编号的对应关系
        Map<String, String> oldAndNewPeriodMap = this.getOldPeriodWithNew(promotionRuleInfoDTO.getGoodsPromotionPeriodDTOList()
                , savePromotionRule.getData().getGoodsPromotionPeriodDTOList());
        log.info("匹配复制操作前后的时间段编号出参：{}", oldAndNewPeriodMap);
        if (oldAndNewPeriodMap.size() == 0) {
            return ExecuteDTO.error(CommonCode.CODE_10000002, "复制活动场次");
        }
        String promotionNo = savePromotionRule.getData().getPromotionNo();  //副本的活动编号
        // 保存设置的参与店铺信息
        if (null != promotionStoreRuleDTO) {
            ReqPromotionRuleDTO reqPromotionRuleDTO =
                    BeanCopierUtil.copy(promotionStoreRuleDTO, ReqPromotionRuleDTO.class);
            reqPromotionRuleDTO.setPromotionNo(promotionNo);
            reqPromotionRuleDTO.setSourceType(goodsPromotionInfoDTO.getSourceType());
            ExecuteDTO savePromotionStoreRule = this.savePromotionStoreRule(reqPromotionRuleDTO);
            if (!savePromotionStoreRule.successFlag()) {
                return ExecuteDTO.error(savePromotionStoreRule.getStatus(), savePromotionStoreRule.getMsg());
            }
        }
        // 保存参与的商品信息
        List<ReqGoodsPromotionGoodsRelationDTO> goodsRelationDTOList =
                BeanCopierUtil.copyList(goodsRelationListExecuteDTO.getData(), ReqGoodsPromotionGoodsRelationDTO.class);
        // 保存之前，先用新生成的活动编号和活动时间段编号替换原先的被拷贝的活动商品中的活动编号和时间段编号
        if (CollectionUtils.isNotEmpty(goodsRelationDTOList)) {
            for (ReqGoodsPromotionGoodsRelationDTO goodsRelationDTO : goodsRelationDTOList) {
                goodsRelationDTO.setPromotionNo(promotionNo);
                // 根据被复制数据的时间段编号获取复制后生成的新时间段编号
                goodsRelationDTO.setPeriodNo(oldAndNewPeriodMap.get(goodsRelationDTO.getPeriodNo()));
                // 设置为复制操作
                goodsRelationDTO.setCopyFlag(WhetherEnum.YES.getCode());
            }
            ExecuteDTO savePromotionGoodsRelation = this.savePromotionGoodsRelation(goodsRelationDTOList);
            if (!savePromotionGoodsRelation.successFlag()) {
                return ExecuteDTO.error(savePromotionGoodsRelation.getStatus(), savePromotionGoodsRelation.getMsg());
            }
        }

        log.info("**GoodsPromotionOperateServiceImpl.copyPromotionInfo-复制商品促销活动-end**");

        return ExecuteDTO.success();
    }


    /**
     * 创建满减满折促销活动
     *
     * @param reqDTO
     * @return
     * <AUTHOR>
     * @date 2022-02-17
     */
    @Override
    public ExecuteDTO saveFullDiscountPromotion(ReqFullDiscountInfoAddDTO reqDTO) {
        log.info("**GoodsPromotionOperateServiceImpl.saveFullDiscountPromotion创建满减满折促销活动**start**");
        log.info("**GoodsPromotionOperateServiceImpl.saveFullDiscountPromotion入参：{}", reqDTO);
        // 入参校验
        goodsPromotionAssert.saveFullDiscountPromotionAssert(reqDTO);

        // 如果前端没有传活动编号，表示是新增
        ExecuteDTO executeDTO;
        if (StringUtils.isBlank(reqDTO.getPromotionNo())) {
            executeDTO = this.addFullDiscountPromotionRule(reqDTO);
            if (!executeDTO.successFlag()) {
                return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
            }
        } else {
            // 如果前端传了活动信息编号，则表示是修改更新操作
            executeDTO = this.editFullDiscountPromotionRule(reqDTO);
            if (!executeDTO.successFlag()) {
                return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
            }
        }

        return executeDTO;
    }

    /**
     * 新增保存满减满折活动规则数据
     *202503
     * @param goodsPromotionInfoDTO
     * @return
     * <AUTHOR>
     * @date 2022-02-18
     */
    private ExecuteDTO addFullDiscountPromotionRule(ReqFullDiscountInfoAddDTO goodsPromotionInfoDTO) {
        // 生成促销活动的编号，默认是 秒杀的活动编号
        String promotionNo = MarketFormGenerator.genFullDiscountPromotionNo();

        goodsPromotionInfoDTO.setPromotionNo(promotionNo);
        // 保存活动基本信息
        AtomReqPromotionInfoDTO reqPromotionInfoDTO =
                BeanCopierUtil.copy(goodsPromotionInfoDTO, AtomReqPromotionInfoDTO.class);
        // 取活动场次的最小开始和最大结束时间入到promotion_info表
        LocalTime minStartTime = null;
        LocalTime maxEndTime = null;
        for (ReqGoodsPromotionPeriodDTO periodDTO : goodsPromotionInfoDTO.getGoodsPromotionPeriodDTOList()) {
            LocalTime startTime = periodDTO.getStartTime();
            if (null == minStartTime || (startTime != null && startTime.isBefore(minStartTime))) {
                minStartTime = startTime;
            }
            LocalTime endTime = periodDTO.getEndTime();
            if (null == maxEndTime || (endTime != null && endTime.isAfter(maxEndTime))) {
                maxEndTime = endTime;
            }
        }
        reqPromotionInfoDTO.setDailyStartTime(minStartTime);
        reqPromotionInfoDTO.setDailyEndTime(maxEndTime);
        // 设置指定时间段
        reqPromotionInfoDTO.setEffectivePeriodType(EffectivePeriodTypeEnum.EFFECTIVE_PERIOD_TYPE_TWO.getCode());
        // 上下架字段不为空 为保存并上架
        if (NumConstant.TWO == goodsPromotionInfoDTO.getUpDownFlag()) {
            reqPromotionInfoDTO.setLatestUpDownTime(LocalDateTime.now());
        }
        // 活动类型设置为 商品促销
        reqPromotionInfoDTO.setActivityType(ActivityTypeEnum.PROMOTION.getCode());
        reqPromotionInfoDTO.setStatus(PromotionStatusEnum.ONLY_SAVED.getCode());
        ExecuteDTO promotionInfoExecuteDTO = promotionInfoOperatService.saveGoodsPromotionInfo(reqPromotionInfoDTO);
        log.info("保存促销活动基本信息出参：{}", promotionInfoExecuteDTO);
        if (promotionInfoExecuteDTO == null || !promotionInfoExecuteDTO.successFlag()) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }

        // 保存活动规则信息
        AtomReqGoodsPromotionRuleDTO goodsPromotionRuleDTO =
                BeanCopierUtil.copy(goodsPromotionInfoDTO, AtomReqGoodsPromotionRuleDTO.class);
        ExecuteDTO promotionRuleExecuteDTO =
                promotionRuleOperateService.saveGoodsPromotionRule(goodsPromotionRuleDTO);
        log.info("保存满减满折活动规则信息出参：{}", promotionInfoExecuteDTO);
        if (promotionRuleExecuteDTO == null || !promotionRuleExecuteDTO.successFlag()) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }

        // 保存活动的时间段信息
        List<AtomReqGoodsPromotionPeriodDTO> periodDTOList = BeanCopierUtil
                .copyList(goodsPromotionInfoDTO.getGoodsPromotionPeriodDTOList(),
                        AtomReqGoodsPromotionPeriodDTO.class);

        periodDTOList.forEach(promotionPeriodDTO -> {
            String periodNo = MarketFormGenerator.genGoodsPeriodNo();
            promotionPeriodDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
            // 生成促销活动时间段编号
            promotionPeriodDTO.setPeriodNo(periodNo);
            // 设置创建用户、店铺以及商家信息
            promotionPeriodDTO.setCreateNo(goodsPromotionInfoDTO.getCreateNo());
            promotionPeriodDTO.setCreateName(goodsPromotionInfoDTO.getCreateName());
            promotionPeriodDTO.setMerchantNo(goodsPromotionInfoDTO.getMerchantNo());
            promotionPeriodDTO.setMerchantName(goodsPromotionInfoDTO.getMerchantName());
            promotionPeriodDTO.setStoreNo(goodsPromotionInfoDTO.getStoreNo());
            promotionPeriodDTO.setStoreName(goodsPromotionInfoDTO.getStoreName());
        });
        ExecuteDTO savePeriodExecuteDTO =
                promotionPeriodOperateService.batchSaveGoodsPromotionPeriod(periodDTOList);
        log.info("保存满减满折的时间段出参：{}", savePeriodExecuteDTO);
        if (savePeriodExecuteDTO == null || !savePeriodExecuteDTO.successFlag()) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }

        // 1、商品操作
        List<ReqCouponGoodsRelationDTO> relationDTOS = goodsPromotionInfoDTO.getReqCouponGoodsRelationDTOS();
        if (!CollectionUtils.isEmpty(relationDTOS)) {
            String periodNo = ListUtil.getFirst(periodDTOList).getPeriodNo();
            List<AtomReqGoodsPromotionGoodsRelationDTO> goodsVoList = BeanCopierUtil.copyList(relationDTOS, AtomReqGoodsPromotionGoodsRelationDTO.class);
            this.setGoodsRelationInfo(goodsVoList, goodsPromotionInfoDTO, periodNo);
            this.atomGoodsPromotionGoodsRelationOperateService.savePromotionGoodsRelation(goodsVoList);
        }

        // 2、若为店铺
        if (SourceTypeEnum.SOURCE_TYPE_1003.getCode().equals(goodsPromotionInfoDTO.getSourceType())) {
            // 店铺指定类目
            if (GoodsPromotionScopeEnum.SCOPE_TYPE_CATEGORY.getCode().equals(goodsPromotionInfoDTO.getGoodsPromotionScope())) {
                // 先删除再新增
                List<ReqCouponCategoryRelationDTO> reqCouponCategoryRelationDTOS = goodsPromotionInfoDTO.getReqCouponCategoryRelationDTOS();
                if (!CollectionUtils.isEmpty(reqCouponCategoryRelationDTOS)) {
                    List<AtomReqGoodsPromotionCategoryRelationDTO> addVoList = BeanCopierUtil.copyList(reqCouponCategoryRelationDTOS, AtomReqGoodsPromotionCategoryRelationDTO.class);
                    addVoList.forEach(addVo -> addVo.setPromotionNo(promotionNo));
                    this.atomGoodsPromotionCategoryRelationOperateService.savePromotionCategoryRelation(addVoList);
                }
            }
        }

        // 满减满折支持多档位
        if (ListUtil.isNotEmpty(goodsPromotionInfoDTO.getMultiLevelDTOS())) {
            goodsPromotionMultiLevelService.batchSaveMultiLevels(goodsPromotionInfoDTO.getMultiLevelDTOS(), reqPromotionInfoDTO);
        }

        AtomReqPromotionInfoDTO promotionInfoDTO = new AtomReqPromotionInfoDTO();
        promotionInfoDTO.setPromotionNo(promotionNo);
        promotionInfoDTO.setOldStatus(PromotionStatusEnum.ONLY_SAVED.getCode());    //待更新的草稿状态
        if (DateUtil.getLocalDateTimeNow().isBefore(goodsPromotionInfoDTO.getEffectiveTime())) {
            promotionInfoDTO.setStatus(PromotionStatusEnum.NOT_STARTED.getCode());   //要更新的状态
        } else if (!DateUtil.getLocalDateTimeNow().isAfter(goodsPromotionInfoDTO.getInvalidTime())) {
            promotionInfoDTO.setStatus(PromotionStatusEnum.IN_PROGRESS.getCode());   //要更新的状态
        } else {
            promotionInfoDTO.setStatus(PromotionStatusEnum.EXPIRED.getCode());   //要更新的状态
        }

        ExecuteDTO updateGoodsPromotionInfo =
                promotionInfoOperatService.updateGoodsPromotionInfo(promotionInfoDTO);
        log.info("**保存促销活动商品信息后，更新活动状态为未开始出参：{}", updateGoodsPromotionInfo);

        log.info("-ExpandRuleOperatServiceImpl-setExpandGoods-end");

        // 封装返回的结果信息
        return ExecuteDTO.success();
    }

    /**
     * 编辑保存满减满折活动规则数据
     *
     * @param goodsPromotionInfoDTO
     * @return
     * <AUTHOR>
     * @date 2022-02-18
     */
    private ExecuteDTO editFullDiscountPromotionRule(ReqFullDiscountInfoAddDTO goodsPromotionInfoDTO) {

        // 更新基本信息
        AtomReqPromotionInfoDTO reqPromotionInfoDTO =
                BeanCopierUtil.copy(goodsPromotionInfoDTO, AtomReqPromotionInfoDTO.class);
        // 操作正在进行中的活动时需要加锁
        boolean lockFlag = lockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
        if (!lockFlag) {
            return ExecuteDTO.error(CommonCode.CODE_10000003.getCode(), "编辑操作前加锁失败，确定是否编辑时有其他操作");
        }

        // 取活动场次的最小开始和最大结束时间入到promotion_info表
        LocalTime minStartTime = null;
        LocalTime maxEndTime = null;
        for (ReqGoodsPromotionPeriodDTO periodDTO : goodsPromotionInfoDTO.getGoodsPromotionPeriodDTOList()) {
            LocalTime startTime = periodDTO.getStartTime();
            if (null == minStartTime || (startTime != null && startTime.isBefore(minStartTime))) {
                minStartTime = startTime;
            }
            LocalTime endTime = periodDTO.getEndTime();
            if (null == maxEndTime || (endTime != null && endTime.isAfter(maxEndTime))) {
                maxEndTime = endTime;
            }
        }
        reqPromotionInfoDTO.setDailyStartTime(minStartTime);
        reqPromotionInfoDTO.setDailyEndTime(maxEndTime);
        // 上下架字段不为空 为保存并上架
        if (NumConstant.TWO == goodsPromotionInfoDTO.getUpDownFlag()) {
            reqPromotionInfoDTO.setLatestUpDownTime(LocalDateTime.now());
        }
        ExecuteDTO updateGoodsPromotionInfo =
                promotionInfoOperatService.updateGoodsPromotionInfo(reqPromotionInfoDTO);
        log.info("更新促销活动信息出参：{}", updateGoodsPromotionInfo);
        if (updateGoodsPromotionInfo == null || !updateGoodsPromotionInfo.successFlag()) {
            // 操作结束后，解锁
            unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }

        // 更新活动规则信息
        AtomReqGoodsPromotionRuleDTO goodsPromotionRuleDTO =
                BeanCopierUtil.copy(goodsPromotionInfoDTO, AtomReqGoodsPromotionRuleDTO.class);
        ExecuteDTO updateGoodsPromotionRule =
                promotionRuleOperateService.updateGoodsPromotionRule(goodsPromotionRuleDTO);
        log.info("更新促销活动规则信息出参：{}", updateGoodsPromotionRule);
        if (updateGoodsPromotionRule == null || !updateGoodsPromotionRule.successFlag()) {
            // 操作结束后，解锁
            unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }

        // 保存活动的时间段信息
        List<AtomReqGoodsPromotionPeriodDTO> periodDTOList = BeanCopierUtil
                .copyList(goodsPromotionInfoDTO.getGoodsPromotionPeriodDTOList(),
                        AtomReqGoodsPromotionPeriodDTO.class);
        for (AtomReqGoodsPromotionPeriodDTO promotionPeriodDTO : periodDTOList) {
            String periodNo = MarketFormGenerator.genGoodsPeriodNo();
            promotionPeriodDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
            // 生成促销活动时间段编号
            promotionPeriodDTO.setPeriodNo(periodNo);
            // 设置创建用户、店铺以及商家信息
            promotionPeriodDTO.setCreateNo(goodsPromotionInfoDTO.getCreateNo());
            promotionPeriodDTO.setCreateName(goodsPromotionInfoDTO.getCreateName());
            promotionPeriodDTO.setMerchantNo(goodsPromotionInfoDTO.getMerchantNo());
            promotionPeriodDTO.setMerchantName(goodsPromotionInfoDTO.getMerchantName());
            promotionPeriodDTO.setStoreNo(goodsPromotionInfoDTO.getStoreNo());
            promotionPeriodDTO.setStoreName(goodsPromotionInfoDTO.getStoreName());
        }
        promotionPeriodOperateService.deletePeriodByPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
        ExecuteDTO savePeriodExecuteDTO = promotionPeriodOperateService.batchSaveGoodsPromotionPeriod(periodDTOList);
        log.info("保存满减满折的时间段出参：{}", savePeriodExecuteDTO);
        if (savePeriodExecuteDTO == null || !savePeriodExecuteDTO.successFlag()) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        // 1、商品操作
        // 先删除，再添加
        List<ReqCouponGoodsRelationDTO> relationDTOS = goodsPromotionInfoDTO.getReqCouponGoodsRelationDTOS();
        if (!CollectionUtils.isEmpty(relationDTOS)) {
            String periodNo = ListUtil.getFirst(periodDTOList).getPeriodNo();
            this.atomGoodsPromotionGoodsRelationOperateService.deletePromotionGoodsRelation(goodsPromotionInfoDTO.getPromotionNo());
            List<AtomReqGoodsPromotionGoodsRelationDTO> goodsVoList = BeanCopierUtil.copyList(relationDTOS, AtomReqGoodsPromotionGoodsRelationDTO.class);
            this.setGoodsRelationInfo(goodsVoList, goodsPromotionInfoDTO, periodNo);
            this.atomGoodsPromotionGoodsRelationOperateService.savePromotionGoodsRelation(goodsVoList);
        }

        // 2、若为店铺
        if (SourceTypeEnum.SOURCE_TYPE_1003.getCode().equals(goodsPromotionInfoDTO.getSourceType())) {
            // 店铺指定类目
            if (GoodsPromotionScopeEnum.SCOPE_TYPE_CATEGORY.getCode().equals(goodsPromotionInfoDTO.getGoodsPromotionScope())) {
                // 先删除再新增
                List<ReqCouponCategoryRelationDTO> reqCouponCategoryRelationDTOS = goodsPromotionInfoDTO.getReqCouponCategoryRelationDTOS();
                if (!CollectionUtils.isEmpty(reqCouponCategoryRelationDTOS)) {
                    this.atomGoodsPromotionCategoryRelationOperateService.delPromotionCategoryRelation(goodsPromotionInfoDTO.getPromotionNo());
                    List<AtomReqGoodsPromotionCategoryRelationDTO> addVoList = BeanCopierUtil.copyList(reqCouponCategoryRelationDTOS, AtomReqGoodsPromotionCategoryRelationDTO.class);
                    addVoList.forEach(addVo -> {
                        addVo.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
                    });
                    this.atomGoodsPromotionCategoryRelationOperateService.savePromotionCategoryRelation(addVoList);
                }
            }
        }

        // 满减满折支持多档位，编辑，先删后插
        if (ListUtil.isNotEmpty(goodsPromotionInfoDTO.getMultiLevelDTOS())) {
            goodsPromotionMultiLevelService.batchUpdateMultiLevels(goodsPromotionInfoDTO.getMultiLevelDTOS(), reqPromotionInfoDTO);
        }

        // 操作结束后，解锁
        unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
        return ExecuteDTO.success();
    }

    /**
     * 提取复制保存之后，活动场次的前后对应关系，用于保存活动商品
     *
     * @param oldPeriodDTOList 复制之前，原来的活动场次数据
     * @param newPeriodDTOList 复制之后，新生成的活动场次数据
     * @return
     * <AUTHOR>
     * @date 2021-09-25
     */
    private Map<String, String> getOldPeriodWithNew(List<ResGoodsPromotionPeriodDTO> oldPeriodDTOList
            , List<ResGoodsPromotionPeriodDTO> newPeriodDTOList) {
        Map<String, String> oldAndNewPeriodMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(oldPeriodDTOList) && CollectionUtils.isNotEmpty(newPeriodDTOList)) {
            oldPeriodDTOList.forEach(oldPeriod -> {
                newPeriodDTOList.forEach(newPeriod -> {
                    if (oldPeriod.getStartTime().equals(newPeriod.getStartTime())
                            && oldPeriod.getEndTime().equals(newPeriod.getEndTime())) {
                        oldAndNewPeriodMap.put(oldPeriod.getPeriodNo(), newPeriod.getPeriodNo());
                    }
                });
            });
        }
        return oldAndNewPeriodMap;
    }

    /**
     * @param : promotionNo
     * @Description : 秒杀活动规则放入缓存
     * <AUTHOR> 张涛
     * @date : 2021/8/24 13:47
     */
    private void setPromotionInfoAndRuleToRedis(String promotionNo) {
        String cacheKey = String.format(PromotionKeyConstant.SECOND_SKILL_PROMOTION_RULE, promotionNo);
        // 删除秒杀活动规则缓存
        redisUtil.del(cacheKey);

        // 根据活动编号查询活动规则信息
        AtomReqPromotionInfoDTO atomReqPromotionInfoDTO = new AtomReqPromotionInfoDTO();
        atomReqPromotionInfoDTO.setPromotionNo(promotionNo);
        ExecuteDTO<AtomResGoodsPromotionRuleDTO> ruleInfoExecuteDTO =
                atomGoodsPromotionRuleAnalysisService.getGoodsPromotionRuleByPromotionNo(atomReqPromotionInfoDTO);
        if (null == ruleInfoExecuteDTO || !ruleInfoExecuteDTO.successFlag() || null == ruleInfoExecuteDTO.getData()) {
            return;
        }
        AtomResGoodsPromotionRuleDTO atomResGoodsPromotionRuleDTO = ruleInfoExecuteDTO.getData();

        // 添加活动规则缓存
        redisUtil.set(cacheKey, JSON.toJSONString(atomResGoodsPromotionRuleDTO));
    }

    /**
     * @param : promotionNo
     * @Description : 删除秒杀活动规则缓存
     * <AUTHOR> 张涛
     * @date : 2021/8/24 13:44
     */
    private void delPromotionInfoAndRuleCache(String promotionNo) {
        // 删除秒杀活动规则缓存
        redisUtil.del(String.format(PromotionKeyConstant.SECOND_SKILL_PROMOTION_RULE, promotionNo));
    }

    /**
     * @param : goodsVoList
     * @Description : 塞活动商品关联信息
     * <AUTHOR> 卜金隆
     * @date : 2022/2/21 17:44
     */
    private void setGoodsRelationInfo(List<AtomReqGoodsPromotionGoodsRelationDTO> goodsVoList, ReqFullDiscountInfoAddDTO infoAddDTO, String periodNo) {
        goodsVoList.forEach(goods -> {
            goods.setSubGoodsFlag(NumConstant.ONE);
            goods.setPeriodNo(periodNo);
            goods.setPromotionNo(infoAddDTO.getPromotionNo());
            goods.setStoreNo(infoAddDTO.getStoreNo());
            goods.setStoreName(infoAddDTO.getStoreName());
            goods.setMerchantNo(infoAddDTO.getMerchantNo());
            goods.setMerchantName(infoAddDTO.getMerchantName());
            goods.setCreateNo(infoAddDTO.getCreateNo());
            goods.setCreateName(infoAddDTO.getCreateName());
        });

    }

    /**
     * 创建特惠促销活动
     *
     * @param reqDTO ReqVipPriceAddDTO
     * @return 活动编号
     * <AUTHOR>
     * @date 2022-07-18
     */
    @Override
    public ExecuteDTO<String> saveVipPricePromotion(ReqVipPriceAddDTO reqDTO) {
        log.info("**GoodsPromotionOperateServiceImpl.saveVipPricePromotion,reqDto：{}", reqDTO);
        // 入参校验
        goodsPromotionAssert.saveVipPricePromotionAssert(reqDTO);
        // 设置默认值
        reqDTO.setGoodsPromotionType(GoodsPromotionTypeEnum.VIP_PRICE.getCode());
        reqDTO.setPromotionType(PromotionTypeEnum.VIP_PRICE.getCode());
        reqDTO.setGoodsPromotionScope(GoodsPromotionScopeEnum.SCOPE_TYPE_GOODS.getCode());
        // 活动类型设置为 商品促销
        reqDTO.setActivityType(ActivityTypeEnum.PROMOTION.getCode());

        // 商品校验
        Set<String> goodsNoList = reqDTO.getGoodsList().stream().map(ReqVipPriceGoodsDTO::getGoodsNo).collect(Collectors.toSet());
        AtomReqGoodsDTO goodsDTO = new AtomReqGoodsDTO();
        goodsDTO.setGoodsNos(new ArrayList<>(goodsNoList));
        ExecuteDTO<List<AtomResGoodsDTO>> goodsExecuteDTO = legacyGoodsCenterService.getGoodsByNos(goodsDTO);
        if (null == goodsExecuteDTO || !goodsExecuteDTO.successFlag()) {
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        } else if (ListUtil.isEmpty(goodsExecuteDTO.getData())) {
            return new ExecuteDTO<>(GoodsErrorCode.CODE_12000001.getCode(), GoodsErrorCode.CODE_12000001.getInMsg(), null);
        } else {
            Map<String, AtomResGoodsDTO> goodsDTOMap = ListUtil.toMap(goodsExecuteDTO.getData(), AtomResGoodsDTO::getGoodsNo);
            for (ReqVipPriceGoodsDTO reqVipPriceGoodsDTO : reqDTO.getGoodsList()) {
                AtomResGoodsDTO atomResGoodsDTO = goodsDTOMap.get(reqVipPriceGoodsDTO.getGoodsNo());
                if (null == atomResGoodsDTO) {
                    return new ExecuteDTO<>(GoodsErrorCode.CODE_12000001.getCode(), GoodsErrorCode.CODE_12000001.getInMsg(), null);
                }
                reqVipPriceGoodsDTO.setParentGoodsNo(atomResGoodsDTO.getParentGoodsNo());
            }
        }

        // 如果前端没有传活动编号，表示是新增
        ExecuteDTO<String> executeDTO;
        if (StringUtils.isBlank(reqDTO.getPromotionNo())) {
            executeDTO = this.addVipPricePromotionRule(reqDTO);
            if (!executeDTO.successFlag()) {
                return new ExecuteDTO<>(executeDTO.getStatus(), executeDTO.getMsg(), null);
            }
        } else {
            // 如果前端传了活动信息编号，则表示是修改更新操作
            executeDTO = this.editVipPricePromotionRule(reqDTO);
            if (!executeDTO.successFlag()) {
                return new ExecuteDTO<>(executeDTO.getStatus(), executeDTO.getMsg(), null);
            }
        }

        return executeDTO;
    }

    /**
     * 新增保存特惠促销活动规则数据
     *
     * @param goodsPromotionInfoDTO ReqVipPriceAddDTO
     * @return 活动编号
     * <AUTHOR>
     * @date 2022-07-18
     */
    private ExecuteDTO<String> addVipPricePromotionRule(ReqVipPriceAddDTO goodsPromotionInfoDTO) {
        // 生成促销活动的编号，默认是 秒杀的活动编号
        String promotionNo = MarketFormGenerator.genVipPricePromotionNo();

        goodsPromotionInfoDTO.setPromotionNo(promotionNo);
        // 保存活动基本信息
        AtomReqPromotionInfoDTO reqPromotionInfoDTO = BeanCopierUtil.copy(goodsPromotionInfoDTO, AtomReqPromotionInfoDTO.class);
        // 取活动场次的最小开始和最大结束时间入到promotion_info表
        ReqGoodsPromotionPeriodDTO goodsPromotionPeriodDTO = ListUtil.getFirst(goodsPromotionInfoDTO.getGoodsPromotionPeriodDTOList());
        if (null == goodsPromotionPeriodDTO) {
            return new ExecuteDTO<>(CommonCode.CODE_10000001.getCode(), String.format(CommonCode.CODE_10000001.getInMsg(), "活动时间"), null);
        }
        reqPromotionInfoDTO.setDailyStartTime(goodsPromotionPeriodDTO.getStartTime());
        reqPromotionInfoDTO.setDailyEndTime(goodsPromotionPeriodDTO.getEndTime());
        // 设置指定时间段
        reqPromotionInfoDTO.setEffectivePeriodType(EffectivePeriodTypeEnum.EFFECTIVE_PERIOD_TYPE_TWO.getCode());
        reqPromotionInfoDTO.setStatus(PromotionStatusEnum.ONLY_SAVED.getCode());
        ExecuteDTO promotionInfoExecuteDTO = promotionInfoOperatService.saveGoodsPromotionInfo(reqPromotionInfoDTO);
        log.info("保存特惠促销活动基本信息出参：{}", promotionInfoExecuteDTO);
        if (promotionInfoExecuteDTO == null || !promotionInfoExecuteDTO.successFlag()) {
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        }
        // 保存活动规则信息
        AtomReqGoodsPromotionRuleDTO goodsPromotionRuleDTO = BeanCopierUtil.copy(goodsPromotionInfoDTO, AtomReqGoodsPromotionRuleDTO.class);
        goodsPromotionRuleDTO.setFreeDeliveryFlag(WhetherEnum.YES.getCode());
        goodsPromotionRuleDTO.setDeliveryFlag(WhetherEnum.YES.getCode());
        ExecuteDTO promotionRuleExecuteDTO = promotionRuleOperateService.saveGoodsPromotionRule(goodsPromotionRuleDTO);
        log.info("保存商品特惠促销活动规则信息出参：{}", promotionInfoExecuteDTO);
        if (promotionRuleExecuteDTO == null || !promotionRuleExecuteDTO.successFlag()) {
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        }

        // 保存活动的时间段信息
        AtomReqGoodsPromotionPeriodDTO promotionPeriodDTO = BeanCopierUtil.copy(goodsPromotionPeriodDTO, AtomReqGoodsPromotionPeriodDTO.class);
        promotionPeriodDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
        // 生成促销活动时间段编号
        String periodNo = MarketFormGenerator.genGoodsPeriodNo();
        promotionPeriodDTO.setPeriodNo(periodNo);
        // 设置创建用户、店铺以及商家信息
        promotionPeriodDTO.setCreateNo(goodsPromotionInfoDTO.getCreateNo());
        promotionPeriodDTO.setCreateName(goodsPromotionInfoDTO.getCreateName());
        promotionPeriodDTO.setMerchantNo(goodsPromotionInfoDTO.getMerchantNo());
        promotionPeriodDTO.setMerchantName(goodsPromotionInfoDTO.getMerchantName());
        promotionPeriodDTO.setStoreNo(goodsPromotionInfoDTO.getStoreNo());
        promotionPeriodDTO.setStoreName(goodsPromotionInfoDTO.getStoreName());
        ExecuteDTO savePeriodExecuteDTO = promotionPeriodOperateService.batchSaveGoodsPromotionPeriod(Collections.singletonList(promotionPeriodDTO));
        log.info("保存商品特惠促销活动时间段出参：{}", savePeriodExecuteDTO);
        if (savePeriodExecuteDTO == null || !savePeriodExecuteDTO.successFlag()) {
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        }

        // 关联商品保存
        if (ListUtil.isNotEmpty(goodsPromotionInfoDTO.getGoodsList())) {
            List<AtomReqGoodsPromotionGoodsRelationDTO> goodsVoList = this.convertVipPriceGoods(goodsPromotionInfoDTO, periodNo);
            this.atomGoodsPromotionGoodsRelationOperateService.savePromotionGoodsRelation(goodsVoList);
        } else {
            return new ExecuteDTO<>(CommonCode.CODE_10000001.getCode(), String.format(CommonCode.CODE_10000001.getInMsg(), "活动商品"), null);
        }

        AtomReqPromotionInfoDTO promotionInfoDTO = new AtomReqPromotionInfoDTO();
        promotionInfoDTO.setPromotionNo(promotionNo);
        promotionInfoDTO.setOldStatus(PromotionStatusEnum.ONLY_SAVED.getCode());    //待更新的草稿状态
        if (DateUtil.getLocalDateTimeNow().isBefore(goodsPromotionInfoDTO.getEffectiveTime())) {
            promotionInfoDTO.setStatus(PromotionStatusEnum.NOT_STARTED.getCode());   //要更新的状态
        } else if (!DateUtil.getLocalDateTimeNow().isAfter(goodsPromotionInfoDTO.getInvalidTime())) {
            promotionInfoDTO.setStatus(PromotionStatusEnum.IN_PROGRESS.getCode());   //要更新的状态
        } else {
            promotionInfoDTO.setStatus(PromotionStatusEnum.EXPIRED.getCode());   //要更新的状态
        }

        ExecuteDTO updateGoodsPromotionInfo = promotionInfoOperatService.updateGoodsPromotionInfo(promotionInfoDTO);
        log.info("**保存促销活动商品信息后，更新活动状态为未开始出参：{}", updateGoodsPromotionInfo);

        log.info("-ExpandRuleOperatServiceImpl-setExpandGoods-end");

        // 封装返回活动编码
        return ExecuteDTO.ok(promotionNo);
    }

    private List<AtomReqGoodsPromotionGoodsRelationDTO> convertVipPriceGoods(ReqVipPriceAddDTO goodsPromotionInfoDTO, String periodNo) {
        List<AtomReqGoodsPromotionGoodsRelationDTO> resultList = new ArrayList<>();
        if (ListUtil.isNotEmpty(goodsPromotionInfoDTO.getGoodsList())) {
            for (ReqVipPriceGoodsDTO reqVipPriceGoodsDTO : goodsPromotionInfoDTO.getGoodsList()) {
                AtomReqGoodsPromotionGoodsRelationDTO atomGoodsDTO = new AtomReqGoodsPromotionGoodsRelationDTO();
                atomGoodsDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
                atomGoodsDTO.setPromotionType(goodsPromotionInfoDTO.getPromotionType());
                // 是否子品(1:否 2:是)
                atomGoodsDTO.setSubGoodsFlag(NumConstant.TWO);
                atomGoodsDTO.setPeriodNo(periodNo);
                atomGoodsDTO.setStoreNo(goodsPromotionInfoDTO.getStoreNo());
                atomGoodsDTO.setStoreName(goodsPromotionInfoDTO.getStoreName());
                atomGoodsDTO.setMerchantNo(goodsPromotionInfoDTO.getMerchantNo());
                atomGoodsDTO.setMerchantName(goodsPromotionInfoDTO.getMerchantName());
                atomGoodsDTO.setCreateNo(goodsPromotionInfoDTO.getCreateNo());
                atomGoodsDTO.setCreateName(goodsPromotionInfoDTO.getCreateName());


                // 商品编码
                atomGoodsDTO.setGoodsNo(reqVipPriceGoodsDTO.getGoodsNo());
                atomGoodsDTO.setParentGoodsNo(reqVipPriceGoodsDTO.getParentGoodsNo());
                // 特惠促销
                atomGoodsDTO.setPromotionPrice(reqVipPriceGoodsDTO.getPromotionPrice());

                resultList.add(atomGoodsDTO);
            }
        }

        return resultList;
    }

    /**
     * 编辑保存特惠促销活动规则数据
     *
     * @param goodsPromotionInfoDTO ReqVipPriceAddDTO
     * @return 活动编号
     * <AUTHOR>
     * @date 2022-07-18
     */
    private ExecuteDTO<String> editVipPricePromotionRule(ReqVipPriceAddDTO goodsPromotionInfoDTO) {
        // 操作正在进行中的活动时需要加锁
        boolean lockFlag = lockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
        if (!lockFlag) {
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), "编辑操作前加锁失败，确定是否编辑时有其他操作", null);
        }
        // 更新基本信息
        AtomReqPromotionInfoDTO reqPromotionInfoDTO = BeanCopierUtil.copy(goodsPromotionInfoDTO, AtomReqPromotionInfoDTO.class);
        ReqGoodsPromotionPeriodDTO goodsPromotionPeriodDTO = ListUtil.getFirst(goodsPromotionInfoDTO.getGoodsPromotionPeriodDTOList());
        if (null == goodsPromotionPeriodDTO) {
            return new ExecuteDTO<>(CommonCode.CODE_10000001.getCode(), String.format(CommonCode.CODE_10000001.getInMsg(), "活动时间"), null);
        }
        reqPromotionInfoDTO.setDailyStartTime(goodsPromotionPeriodDTO.getStartTime());
        reqPromotionInfoDTO.setDailyEndTime(goodsPromotionPeriodDTO.getEndTime());
        // 上下架字段不为空 为保存并上架
        if (NumConstant.TWO == goodsPromotionInfoDTO.getUpDownFlag()) {
            reqPromotionInfoDTO.setLatestUpDownTime(LocalDateTime.now());
        }
        ExecuteDTO updateGoodsPromotionInfo = promotionInfoOperatService.updateGoodsPromotionInfo(reqPromotionInfoDTO);
        log.info("更新促销活动信息出参：{}", updateGoodsPromotionInfo);
        if (updateGoodsPromotionInfo == null || !updateGoodsPromotionInfo.successFlag()) {
            // 操作结束后，解锁
            unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        }

        // 更新活动规则信息
        AtomReqGoodsPromotionRuleDTO goodsPromotionRuleDTO = BeanCopierUtil.copy(goodsPromotionInfoDTO, AtomReqGoodsPromotionRuleDTO.class);
        goodsPromotionRuleDTO.setFreeDeliveryFlag(WhetherEnum.YES.getCode());
        goodsPromotionRuleDTO.setDeliveryFlag(WhetherEnum.YES.getCode());
        ExecuteDTO updateGoodsPromotionRule = promotionRuleOperateService.updateGoodsPromotionRule(goodsPromotionRuleDTO);
        log.info("更新促销活动规则信息出参：{}", updateGoodsPromotionRule);
        if (updateGoodsPromotionRule == null || !updateGoodsPromotionRule.successFlag()) {
            // 操作结束后，解锁
            unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        }


        // 保存活动的时间段信息
        List<AtomReqGoodsPromotionPeriodDTO> periodDTOList = BeanCopierUtil
                .copyList(goodsPromotionInfoDTO.getGoodsPromotionPeriodDTOList(),
                        AtomReqGoodsPromotionPeriodDTO.class);

        AtomReqGoodsPromotionPeriodDTO promotionPeriodDTO = periodDTOList.get(NumConstant.ZERO);
        String periodNo = MarketFormGenerator.genGoodsPeriodNo();
        promotionPeriodDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
        // 生成促销活动时间段编号
        promotionPeriodDTO.setPeriodNo(periodNo);
        // 设置创建用户、店铺以及商家信息
        promotionPeriodDTO.setCreateNo(goodsPromotionInfoDTO.getCreateNo());
        promotionPeriodDTO.setCreateName(goodsPromotionInfoDTO.getCreateName());
        promotionPeriodDTO.setMerchantNo(goodsPromotionInfoDTO.getMerchantNo());
        promotionPeriodDTO.setMerchantName(goodsPromotionInfoDTO.getMerchantName());
        promotionPeriodDTO.setStoreNo(goodsPromotionInfoDTO.getStoreNo());
        promotionPeriodDTO.setStoreName(goodsPromotionInfoDTO.getStoreName());
        promotionPeriodOperateService.deletePeriodByPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
        ExecuteDTO savePeriodExecuteDTO = promotionPeriodOperateService.batchSaveGoodsPromotionPeriod(periodDTOList);
        log.info("保存特惠促销的时间段出参：{}", savePeriodExecuteDTO);
        if (savePeriodExecuteDTO == null || !savePeriodExecuteDTO.successFlag()) {
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        }

        // 关联商品保存、先删除，再添加
        if (ListUtil.isNotEmpty(goodsPromotionInfoDTO.getGoodsList())) {
            this.atomGoodsPromotionGoodsRelationOperateService.deletePromotionGoodsRelation(goodsPromotionInfoDTO.getPromotionNo());
            List<AtomReqGoodsPromotionGoodsRelationDTO> goodsVoList = this.convertVipPriceGoods(goodsPromotionInfoDTO, periodNo);
            this.atomGoodsPromotionGoodsRelationOperateService.savePromotionGoodsRelation(goodsVoList);
        } else {
            return new ExecuteDTO<>(CommonCode.CODE_10000001.getCode(), String.format(CommonCode.CODE_10000001.getInMsg(), "活动商品"), null);
        }

        // 操作结束后，解锁
        unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
        return ExecuteDTO.ok(goodsPromotionInfoDTO.getPromotionNo());
    }

    /**
     * 创建社群接龙活动
     *
     * @param reqDTO ReqCommunitySolitaireAddDTO
     * @return 活动编号
     * <AUTHOR>
     * @date 2022-08-08
     */
    @Override
    public ExecuteDTO<String> saveCommunitySolitairePromotion(ReqCommunitySolitaireAddDTO reqDTO) {
        log.info("**GoodsPromotionOperateServiceImpl.saveCommunitySolitairePromotion,reqDto：{}", reqDTO);
        // 入参校验
        goodsPromotionAssert.saveCommunitySolitaireAssert(reqDTO);
        // 设置默认值
        reqDTO.setGoodsPromotionType(GoodsPromotionTypeEnum.COMMUNITY_SOLITAIRE.getCode());
        /**
         * 促销类型设置默认值
         */
        if (StringUtils.isEmpty(reqDTO.getPromotionType())) {
            reqDTO.setPromotionType(PromotionTypeEnum.COMMUNITY_SOLITAIRE.getCode());
        }
        reqDTO.setGoodsPromotionScope(GoodsPromotionScopeEnum.SCOPE_TYPE_GOODS.getCode());
        // 活动类型设置为 商品促销
        reqDTO.setActivityType(ActivityTypeEnum.PROMOTION.getCode());
        // 配送方式 数据格式化
        if (StringUtils.isEmpty(reqDTO.getDeliveryWay())) {
            String goodsPromotionRuleDeliveryWay = Arrays.stream(reqDTO.getDeliveryWay().split(",")).sorted().collect(Collectors.joining(","));
            reqDTO.setDeliveryWay(goodsPromotionRuleDeliveryWay);
        }
        // 商品校验
        if (CollectionUtils.isNotEmpty(reqDTO.getGoodsList())) {
            Set<String> goodsNoList = reqDTO.getGoodsList().stream().map(ReqCommunitySolitaireGoodsDTO::getGoodsNo).collect(Collectors.toSet());
            AtomReqGoodsDTO atomReqGoodsDTO = new AtomReqGoodsDTO();
            atomReqGoodsDTO.setGoodsNos(new ArrayList<>(goodsNoList));
            ExecuteDTO<List<AtomResGoodsDTO>> goodsExecuteDTO = legacyGoodsCenterService.getGoodsByNos(atomReqGoodsDTO);
            if (null == goodsExecuteDTO || !goodsExecuteDTO.successFlag()) {
                return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
            } else if (ListUtil.isEmpty(goodsExecuteDTO.getData())) {
                return new ExecuteDTO<>(MarketErrorCode.CODE_17000634.getCode(), MarketErrorCode.CODE_17000634.getInMsg(), null);
            } else {
                Map<String, AtomResGoodsDTO> goodsDTOMap = ListUtil.toMap(goodsExecuteDTO.getData(), AtomResGoodsDTO::getGoodsNo);
                for (ReqCommunitySolitaireGoodsDTO goodsDTO : reqDTO.getGoodsList()) {
                    AtomResGoodsDTO atomResGoodsDTO = goodsDTOMap.get(goodsDTO.getGoodsNo());
                    if (null == atomResGoodsDTO) {
                        return new ExecuteDTO<>(MarketErrorCode.CODE_17000634.getCode(), MarketErrorCode.CODE_17000634.getInMsg(), null);
                    } else {
                        if (null == atomResGoodsDTO.getRetailPrice() && BigDecimalUtil.gt(goodsDTO.getPromotionPrice(), atomResGoodsDTO.getRetailPrice())) {
                            // 活动价不大于零售价
                            return new ExecuteDTO<>(MarketErrorCode.CODE_17000123.getCode(), MarketErrorCode.CODE_17000123.getInMsg(), null);
                        }
                        goodsDTO.setGoodsName(atomResGoodsDTO.getGoodsName());
                    }
                }
            }
        }

        //店铺校验
        if (PromotionTypeEnum.COMMUNITY_SOLITAIRE_SJ.getCode().equals(reqDTO.getPromotionType())
                && StringUtils.isNotEmpty(reqDTO.getStoreType())) {
            //查询商家店铺信息
            MerchantStoreRequest request = new MerchantStoreRequest();
            request.setMerchantNo(reqDTO.getMerchantNo());
            ExecuteDTO<List<MerchantStoreResponse>> merchantStoreResponse = storeProcessService.queryMerchantStore(request);
            List<MerchantStoreResponse> storeList = merchantStoreResponse.getData();
            if (CollectionUtils.isEmpty(storeList)) {
                return new ExecuteDTO<>(MarketErrorCode.CODE_17000636.getCode(),
                        MarketErrorCode.CODE_17000636.getInMsg(), null);
            }
            //商家指定店铺校验
            if (StoreTypeEnum.STORE_TYPE_THREE.getCode().equals(reqDTO.getStoreType())
                    && CollectionUtils.isEmpty(reqDTO.getStoresList())) {
                return new ExecuteDTO<>(MarketErrorCode.CODE_17000635.getCode(),
                        MarketErrorCode.CODE_17000635.getInMsg(), null);
            }
            if (!CollectionUtils.isEmpty(reqDTO.getStoresList())) {
                Map<String, MerchantStoreResponse> merchantStoreMap = storeList.stream().
                        collect(Collectors.toMap(MerchantStoreResponse::getStoreNo, store -> store));
                for (ReqCommunitySolitaireStoreDTO storeDto : reqDTO.getStoresList()) {
                    if (!merchantStoreMap.containsKey(storeDto.getStoreNo())) {
                        return new ExecuteDTO<>(MarketErrorCode.CODE_17000635.getCode(),
                                "分发店铺" + storeDto.getStoreNo() + "不存在", null);
                    }
                }
            }
        }

        // 如果前端没有传活动编号，表示是新增
        ExecuteDTO<String> executeDTO;
        if (StringUtils.isBlank(reqDTO.getPromotionNo())) {
            executeDTO = this.addCommunitySolitaireRule(reqDTO);
            if (!executeDTO.successFlag()) {
                return new ExecuteDTO<>(executeDTO.getStatus(), executeDTO.getMsg(), null);
            }
        } else {
            // 如果前端传了活动信息编号，则表示是修改更新操作
            executeDTO = this.editCommunitySolitaireRule(reqDTO);
            if (!executeDTO.successFlag()) {
                return new ExecuteDTO<>(executeDTO.getStatus(), executeDTO.getMsg(), null);
            }
        }

        // 清除缓存
        if (StringUtils.isNotBlank(executeDTO.getData())) {
            this.delPromotionInfoAndRuleCache(executeDTO.getData());
        }
        log.info("**GoodsPromotionOperateServiceImpl.saveGoodsPromotionRule创建商品促销活动信息**end**");

        return executeDTO;
    }

    /**
     * 新增保存社群接龙活动规则数据
     *
     * @param goodsPromotionInfoDTO ReqCommunitySolitaireAddDTO
     * @return 活动编号
     * <AUTHOR>
     * @date 2022-08-08
     */
    private ExecuteDTO<String> addCommunitySolitaireRule(ReqCommunitySolitaireAddDTO goodsPromotionInfoDTO) {
        // 生成促销活动的编号，默认是 秒杀的活动编号
        String promotionNo = MarketFormGenerator.genCommunitySolitairePromotionNo();

        goodsPromotionInfoDTO.setPromotionNo(promotionNo);
        // 保存活动基本信息
        AtomReqPromotionInfoDTO reqPromotionInfoDTO = BeanCopierUtil.copy(goodsPromotionInfoDTO, AtomReqPromotionInfoDTO.class);
        // 取活动场次的最小开始和最大结束时间入到promotion_info表
        ReqGoodsPromotionPeriodDTO goodsPromotionPeriodDTO = ListUtil.getFirst(goodsPromotionInfoDTO.getGoodsPromotionPeriodDTOList());
        if (null == goodsPromotionPeriodDTO) {
            return new ExecuteDTO<>(CommonCode.CODE_10000001.getCode(), String.format(CommonCode.CODE_10000001.getInMsg(), "活动时间"), null);
        }
        reqPromotionInfoDTO.setDailyStartTime(goodsPromotionPeriodDTO.getStartTime());
        reqPromotionInfoDTO.setDailyEndTime(goodsPromotionPeriodDTO.getEndTime());
        // 设置指定时间段
        reqPromotionInfoDTO.setEffectivePeriodType(EffectivePeriodTypeEnum.EFFECTIVE_PERIOD_TYPE_TWO.getCode());
        reqPromotionInfoDTO.setStatus(PromotionStatusEnum.ONLY_SAVED.getCode());
        ExecuteDTO promotionInfoExecuteDTO = promotionInfoOperatService.saveGoodsPromotionInfo(reqPromotionInfoDTO);
        log.info("保存社群接龙活动基本信息出参：{}", promotionInfoExecuteDTO);
        if (promotionInfoExecuteDTO == null || !promotionInfoExecuteDTO.successFlag()) {
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        }
        // 保存活动规则信息
        AtomReqGoodsPromotionRuleDTO goodsPromotionRuleDTO = BeanCopierUtil.copy(goodsPromotionInfoDTO, AtomReqGoodsPromotionRuleDTO.class);
        goodsPromotionRuleDTO.setFreeDeliveryFlag(WhetherEnum.YES.getCode());
        goodsPromotionRuleDTO.setDeliveryFlag(WhetherEnum.NO.getCode());
        ExecuteDTO promotionRuleExecuteDTO = promotionRuleOperateService.saveGoodsPromotionRule(goodsPromotionRuleDTO);
        log.info("保存社群接龙活动规则信息出参：{}", promotionInfoExecuteDTO);
        if (promotionRuleExecuteDTO == null || !promotionRuleExecuteDTO.successFlag()) {
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        }

        // 保存活动的时间段信息
        AtomReqGoodsPromotionPeriodDTO promotionPeriodDTO = BeanCopierUtil.copy(goodsPromotionPeriodDTO, AtomReqGoodsPromotionPeriodDTO.class);
        promotionPeriodDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
        // 生成促销活动时间段编号
        String periodNo = MarketFormGenerator.genGoodsPeriodNo();
        promotionPeriodDTO.setPeriodNo(periodNo);
        // 设置创建用户、店铺以及商家信息
        promotionPeriodDTO.setCreateNo(goodsPromotionInfoDTO.getCreateNo());
        promotionPeriodDTO.setCreateName(goodsPromotionInfoDTO.getCreateName());
        promotionPeriodDTO.setMerchantNo(goodsPromotionInfoDTO.getMerchantNo());
        promotionPeriodDTO.setMerchantName(goodsPromotionInfoDTO.getMerchantName());
        promotionPeriodDTO.setStoreNo(goodsPromotionInfoDTO.getStoreNo());
        promotionPeriodDTO.setStoreName(goodsPromotionInfoDTO.getStoreName());
        ExecuteDTO savePeriodExecuteDTO = promotionPeriodOperateService.batchSaveGoodsPromotionPeriod(Collections.singletonList(promotionPeriodDTO));
        log.info("保存社群接龙活动时间段出参：{}", savePeriodExecuteDTO);
        if (savePeriodExecuteDTO == null || !savePeriodExecuteDTO.successFlag()) {
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        }

        //商家社区接龙活动关联店铺信息保存
        if (PromotionTypeEnum.COMMUNITY_SOLITAIRE_SJ.getCode().equals(goodsPromotionInfoDTO.getPromotionType())
                && StringUtils.isNotEmpty(goodsPromotionInfoDTO.getStoreType())) {
            List<AtomReqPromotionStoreRelationDTO> promotionStoreRelationList = this.convertCommunitySolitaireStores(
                    goodsPromotionInfoDTO);
            ExecuteDTO saveStoreExecuteDTO = promotionStoreRelationOperatService.batchSavePromotionStoreRelation(promotionStoreRelationList, NumConstant.ZERO);
            log.info("保存社群接龙活动店铺出参：{}", saveStoreExecuteDTO);
        }

        // 关联商品保存
        if (ListUtil.isNotEmpty(goodsPromotionInfoDTO.getGoodsList())) {
            List<AtomReqGoodsPromotionGoodsRelationDTO> goodsVoList = this.convertCommunitySolitaireGoods(goodsPromotionInfoDTO, periodNo);
            this.atomGoodsPromotionGoodsRelationOperateService.savePromotionGoodsRelation(goodsVoList);
        } else {
            //return new ExecuteDTO<>(CommonCode.CODE_10000001.getCode(), String.format(CommonCode.CODE_10000001.getInMsg(), "活动商品"), null);
        }

        // 活动图片保存
        if (ListUtil.isNotEmpty(goodsPromotionInfoDTO.getPromotionImages())) {
            goodsPromotionImageService.batchSavePromotionImages(goodsPromotionInfoDTO.getPromotionImages(), reqPromotionInfoDTO);
        }

        if (StringUtils.isBlank(goodsPromotionInfoDTO.getRefPromotionNo())) {
            AtomReqPromotionInfoDTO promotionInfoDTO = new AtomReqPromotionInfoDTO();
            promotionInfoDTO.setPromotionNo(promotionNo);
            promotionInfoDTO.setOldStatus(PromotionStatusEnum.ONLY_SAVED.getCode());    //待更新的草稿状态
            promotionInfoDTO.setStatus(this.statusByTime(goodsPromotionInfoDTO));   //要更新的状态
            promotionOnlySaveStatus(goodsPromotionInfoDTO, promotionInfoDTO); //判段是否草稿
            if (WhetherEnum.YES.getCode().equals(goodsPromotionInfoDTO.getUpDownFlag())) {
                promotionInfoDTO.setFirstUpTime(LocalDateTime.now());
            }
            ExecuteDTO updateGoodsPromotionInfo = promotionInfoOperatService.updateGoodsPromotionInfo(promotionInfoDTO);
            log.info("**保存促销活动商品信息后，更新活动状态为未开始出参：{}", updateGoodsPromotionInfo);
        } else {
            log.info("**复制接龙活动,refPromotionNo:{},promotionNo:{}", goodsPromotionInfoDTO.getRefPromotionNo(), promotionNo);
        }
        // 封装返回活动编码
        return ExecuteDTO.ok(promotionNo);
    }

    /**
     * 如果关联商品,商家接龙关联店铺信息为空 等情况，保存为草稿
     * 保存主表数据前设置
     *
     * @param reqDTO
     * @param promotionInfoDTO
     */
    private void promotionOnlySaveStatus(ReqCommunitySolitaireAddDTO reqDTO, AtomReqPromotionInfoDTO promotionInfoDTO) {
        if (CollectionUtils.isEmpty(reqDTO.getGoodsList())
                || (PromotionTypeEnum.COMMUNITY_SOLITAIRE_SJ.getCode().equals(reqDTO.getPromotionType()) && StringUtils.isEmpty(reqDTO.getStoreType()))
        ) {
            promotionInfoDTO.setOldStatus(promotionInfoDTO.getStatus());
            //草稿
            promotionInfoDTO.setStatus(PromotionStatusEnum.ONLY_SAVED.getCode());
            //下架
            promotionInfoDTO.setUpDownFlag(WhetherEnum.NO.getCode());
        }
    }

    /**
     * 店铺信息转换
     *
     * @param goodsPromotionInfoDTO
     * @return
     */
    private List<AtomReqPromotionStoreRelationDTO> convertCommunitySolitaireStores(
            ReqCommunitySolitaireAddDTO goodsPromotionInfoDTO) {
        List<AtomReqPromotionStoreRelationDTO> promotionStoreRelationList = Lists.newArrayList();
        /**
         * 商家信息，记录分派店铺类型 全部或者指定店铺
         */
        AtomReqPromotionStoreRelationDTO storeRelationDTO = new AtomReqPromotionStoreRelationDTO();
        storeRelationDTO.setCreateNo(goodsPromotionInfoDTO.getCreateNo());
        storeRelationDTO.setCreateName(goodsPromotionInfoDTO.getCreateName());
        storeRelationDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
        storeRelationDTO.setMerchantNo(goodsPromotionInfoDTO.getMerchantNo());
        storeRelationDTO.setStoreNo(StringUtils.EMPTY);
        storeRelationDTO.setStoreType(goodsPromotionInfoDTO.getStoreType());
        storeRelationDTO.setDisableFlag(StoreUseEnum.ENABLE.getCode());
        storeRelationDTO.setUpDownFlag(goodsPromotionInfoDTO.getUpDownFlag());
        storeRelationDTO.setRuleNo(MarketFormGenerator.getStoreRuleNo());
        storeRelationDTO.setStoreSourceType(SourceTypeEnum.SOURCE_TYPE_1002.getCode());
        promotionStoreRelationList.add(storeRelationDTO);

        if (StoreTypeEnum.STORE_TYPE_THREE.getCode().equals(goodsPromotionInfoDTO.getStoreType())) {
            //指定店铺
            for (ReqCommunitySolitaireStoreDTO storeDTO : goodsPromotionInfoDTO.getStoresList()) {
                storeRelationDTO = new AtomReqPromotionStoreRelationDTO();
                storeRelationDTO.setCreateNo(goodsPromotionInfoDTO.getCreateNo());
                storeRelationDTO.setCreateName(goodsPromotionInfoDTO.getCreateName());
                storeRelationDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
                storeRelationDTO.setMerchantNo(goodsPromotionInfoDTO.getMerchantNo());
                storeRelationDTO.setStoreNo(storeDTO.getStoreNo());
                storeRelationDTO.setStoreType(goodsPromotionInfoDTO.getStoreType());
                storeRelationDTO.setDisableFlag(StoreUseEnum.ENABLE.getCode());
                storeRelationDTO.setUpDownFlag(StoreUpDownEnum.UP.getCode());
                storeRelationDTO.setRuleNo(MarketFormGenerator.getStoreRuleNo());
                storeRelationDTO.setStoreSourceType(SourceTypeEnum.SOURCE_TYPE_1002.getCode());
                promotionStoreRelationList.add(storeRelationDTO);
            }
        }
        return promotionStoreRelationList;
    }

    private List<AtomReqGoodsPromotionGoodsRelationDTO> convertCommunitySolitaireGoods(ReqCommunitySolitaireAddDTO goodsPromotionInfoDTO, String periodNo) {
        List<AtomReqGoodsPromotionGoodsRelationDTO> resultList = new ArrayList<>();
        if (ListUtil.isNotEmpty(goodsPromotionInfoDTO.getGoodsList())) {
            for (ReqCommunitySolitaireGoodsDTO reqVipPriceGoodsDTO : goodsPromotionInfoDTO.getGoodsList()) {
                AtomReqGoodsPromotionGoodsRelationDTO atomGoodsDTO = new AtomReqGoodsPromotionGoodsRelationDTO();
                atomGoodsDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
                atomGoodsDTO.setPromotionType(goodsPromotionInfoDTO.getPromotionType());
                // 是否子品(1:否 2:是)
                atomGoodsDTO.setSubGoodsFlag(NumConstant.TWO);
                atomGoodsDTO.setPeriodNo(periodNo);
                atomGoodsDTO.setStoreNo(goodsPromotionInfoDTO.getStoreNo());
                atomGoodsDTO.setStoreName(goodsPromotionInfoDTO.getStoreName());
                atomGoodsDTO.setMerchantNo(goodsPromotionInfoDTO.getMerchantNo());
                atomGoodsDTO.setMerchantName(goodsPromotionInfoDTO.getMerchantName());
                atomGoodsDTO.setCreateNo(goodsPromotionInfoDTO.getCreateNo());
                atomGoodsDTO.setCreateName(goodsPromotionInfoDTO.getCreateName());
                // 商品编码
                atomGoodsDTO.setGoodsNo(reqVipPriceGoodsDTO.getGoodsNo());
                // 活动价
                atomGoodsDTO.setPromotionPrice(reqVipPriceGoodsDTO.getPromotionPrice());

                resultList.add(atomGoodsDTO);
            }
        }

        return resultList;
    }

    /**
     * 编辑保存社群接龙活动规则数据
     *
     * @param goodsPromotionInfoDTO ReqCommunitySolitaireAddDTO
     * @return 活动编号
     * <AUTHOR>
     * @date 2022-08-08
     */
    private ExecuteDTO<String> editCommunitySolitaireRule(ReqCommunitySolitaireAddDTO goodsPromotionInfoDTO) {
        // 操作正在进行中的活动时需要加锁
        boolean lockFlag = lockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
        if (!lockFlag) {
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), "编辑操作前加锁失败，确定是否编辑时有其他操作", null);
        }
        //
        AtomReqPromotionInfoDTO atomReqPromotionInfoDTO = new AtomReqPromotionInfoDTO();
        atomReqPromotionInfoDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
        ExecuteDTO<AtomResPromotionInfoDTO> oldPromotionInfoEo = promotionInfoAnalysisService.getPromotionInfo(atomReqPromotionInfoDTO);
        if (null == oldPromotionInfoEo) {
            return ExecuteDTOUtil.error(CommonCode.CODE_10000003);
        } else if (null == oldPromotionInfoEo.getData()) {
            return ExecuteDTOUtil.error(CommonCode.CODE_10000024);
        }
        AtomResPromotionInfoDTO oldPromotionInfo = oldPromotionInfoEo.getData();
        // 更新基本信息
        AtomReqPromotionInfoDTO reqPromotionInfoDTO = BeanCopierUtil.copy(goodsPromotionInfoDTO, AtomReqPromotionInfoDTO.class);
        ReqGoodsPromotionPeriodDTO goodsPromotionPeriodDTO = ListUtil.getFirst(goodsPromotionInfoDTO.getGoodsPromotionPeriodDTOList());
        if (null == goodsPromotionPeriodDTO) {
            return new ExecuteDTO<>(CommonCode.CODE_10000001.getCode(), String.format(CommonCode.CODE_10000001.getInMsg(), "活动时间"), null);
        }
        reqPromotionInfoDTO.setDailyStartTime(goodsPromotionPeriodDTO.getStartTime());
        reqPromotionInfoDTO.setDailyEndTime(goodsPromotionPeriodDTO.getEndTime());
        // 首次上架时间，最新上架时间
        if (NumConstant.TWO == goodsPromotionInfoDTO.getUpDownFlag()) {
            reqPromotionInfoDTO.setLatestUpDownTime(LocalDateTime.now());
            if (null == oldPromotionInfo.getFirstUpTime()) {
                // 首次上架时间
                reqPromotionInfoDTO.setFirstUpTime(LocalDateTime.now());
            }
        }
        // 已发送模板消息，不再修改主表SendMsgFlag状态
        if (oldPromotionInfo.getSendMsgFlag().equals(NumConstant.THREE)) {
            reqPromotionInfoDTO.setSendMsgFlag(NumConstant.THREE);
        }
        String oldStatus = oldPromotionInfo.getStatus();
        String newStatus = statusByTime(goodsPromotionInfoDTO);
        if (!newStatus.equals(oldStatus)) {
            reqPromotionInfoDTO.setOldStatus(oldStatus);
            reqPromotionInfoDTO.setStatus(newStatus);
        }
        //判断是否为草稿
        //商品列表，前端传来的保存数据和数据库数据都为空，为草稿
        if (CollectionUtils.isEmpty(goodsPromotionInfoDTO.getGoodsList())) {
            //查询数据库是否存在关联商品数据
            AtomReqGoodsPromotionGoodsRelationDTO reqPromotionGoods = new AtomReqGoodsPromotionGoodsRelationDTO();
            reqPromotionGoods.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
            ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> executeGoodsDTO = atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList(reqPromotionGoods);
            if (null == executeGoodsDTO) {
                return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
            }
            if (!executeGoodsDTO.successFlag()) {
                return new ExecuteDTO<>(executeGoodsDTO.getStatus(), executeGoodsDTO.getMsg(), null);
            }
            if (ListUtil.isEmpty(executeGoodsDTO.getData())) {
                //草稿
                reqPromotionInfoDTO.setStatus(PromotionStatusEnum.ONLY_SAVED.getCode());
                //下架
                reqPromotionInfoDTO.setUpDownFlag(WhetherEnum.NO.getCode());
            }
        }
        //商家接龙店铺列表，前端传来的保存数据和数据库数据都为空，为草稿
        if (PromotionTypeEnum.COMMUNITY_SOLITAIRE_SJ.getCode().equals(goodsPromotionInfoDTO.getPromotionType())
                && StringUtils.isEmpty(goodsPromotionInfoDTO.getStoreType())) {
            //分发店铺
            AtomReqPromotionStoreRelationDTO promotionStoreRelationDTO = new AtomReqPromotionStoreRelationDTO();
            promotionStoreRelationDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
            promotionStoreRelationDTO.setMerchantNo(goodsPromotionInfoDTO.getMerchantNo());
            promotionStoreRelationDTO.setDisableFlag(StoreUseEnum.ENABLE.getCode());
            ExecuteDTO<List<AtomResPromotionStoreRelationDTO>> resPromotionStoreRelationDTO = promotionStoreRelationAnalysisService.getStoreRelationList(promotionStoreRelationDTO);
            if (null == resPromotionStoreRelationDTO) {
                return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
            }
            if (!resPromotionStoreRelationDTO.successFlag()) {
                return new ExecuteDTO<>(resPromotionStoreRelationDTO.getStatus(), resPromotionStoreRelationDTO.getMsg(), null);
            }
            if (ListUtil.isEmpty(resPromotionStoreRelationDTO.getData())) {
                //草稿
                reqPromotionInfoDTO.setStatus(PromotionStatusEnum.ONLY_SAVED.getCode());
                //下架
                reqPromotionInfoDTO.setUpDownFlag(WhetherEnum.NO.getCode());
            }
        }
        //更新活动主表信息
        ExecuteDTO updateGoodsPromotionInfo = promotionInfoOperatService.updateGoodsPromotionInfo(reqPromotionInfoDTO);
        log.info("更新促销活动信息出参：{}", updateGoodsPromotionInfo);
        if (updateGoodsPromotionInfo == null || !updateGoodsPromotionInfo.successFlag()) {
            // 操作结束后，解锁
            unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        }

        // 更新活动规则信息
        AtomReqGoodsPromotionRuleDTO goodsPromotionRuleDTO = BeanCopierUtil.copy(goodsPromotionInfoDTO, AtomReqGoodsPromotionRuleDTO.class);
        goodsPromotionRuleDTO.setFreeDeliveryFlag(WhetherEnum.YES.getCode());
        goodsPromotionRuleDTO.setDeliveryFlag(WhetherEnum.NO.getCode());
        ExecuteDTO updateGoodsPromotionRule = promotionRuleOperateService.updateGoodsPromotionRule(goodsPromotionRuleDTO);
        log.info("更新促销活动规则信息出参：{}", updateGoodsPromotionRule);
        if (updateGoodsPromotionRule == null || !updateGoodsPromotionRule.successFlag()) {
            // 操作结束后，解锁
            unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        }


        // 关联商品保存、先删除，再添加
        if (ListUtil.isNotEmpty(goodsPromotionInfoDTO.getGoodsList())) {
            // 保存活动的时间段信息
            List<AtomReqGoodsPromotionPeriodDTO> periodDTOList = BeanCopierUtil
                    .copyList(goodsPromotionInfoDTO.getGoodsPromotionPeriodDTOList(), AtomReqGoodsPromotionPeriodDTO.class);

            AtomReqGoodsPromotionPeriodDTO promotionPeriodDTO = periodDTOList.get(NumConstant.ZERO);
            String periodNo = MarketFormGenerator.genGoodsPeriodNo();
            promotionPeriodDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
            // 生成促销活动时间段编号
            promotionPeriodDTO.setPeriodNo(periodNo);
            // 设置创建用户、店铺以及商家信息
            promotionPeriodDTO.setCreateNo(goodsPromotionInfoDTO.getCreateNo());
            promotionPeriodDTO.setCreateName(goodsPromotionInfoDTO.getCreateName());
            promotionPeriodDTO.setMerchantNo(goodsPromotionInfoDTO.getMerchantNo());
            promotionPeriodDTO.setMerchantName(goodsPromotionInfoDTO.getMerchantName());
            promotionPeriodDTO.setStoreNo(goodsPromotionInfoDTO.getStoreNo());
            promotionPeriodDTO.setStoreName(goodsPromotionInfoDTO.getStoreName());
            promotionPeriodOperateService.deletePeriodByPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
            ExecuteDTO savePeriodExecuteDTO = promotionPeriodOperateService.batchSaveGoodsPromotionPeriod(periodDTOList);
            log.info("保存社群接龙的时间段出参：{}", savePeriodExecuteDTO);
            if (savePeriodExecuteDTO == null || !savePeriodExecuteDTO.successFlag()) {
                return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
            }
            this.atomGoodsPromotionGoodsRelationOperateService.deletePromotionGoodsRelation(goodsPromotionInfoDTO.getPromotionNo());
            List<AtomReqGoodsPromotionGoodsRelationDTO> goodsVoList = this.convertCommunitySolitaireGoods(goodsPromotionInfoDTO, periodNo);
            this.atomGoodsPromotionGoodsRelationOperateService.savePromotionGoodsRelation(goodsVoList);
        } else {
            //return new ExecuteDTO<>(CommonCode.CODE_10000001.getCode(), String.format(CommonCode.CODE_10000001.getInMsg(), "活动商品"), null);
        }

        // 商家社群接龙, 分发店铺更新
        if (PromotionTypeEnum.COMMUNITY_SOLITAIRE_SJ.getCode().equals(goodsPromotionInfoDTO.getPromotionType())
                && StringUtils.isNotEmpty(goodsPromotionInfoDTO.getStoreType())) {
            //分发店铺类型1001:全部店铺 1003:指定店铺
            String updateStoreType = goodsPromotionInfoDTO.getStoreType();
            AtomReqPromotionStoreRelationDTO atomReqPromotionStoreRelationDTO = new AtomReqPromotionStoreRelationDTO();
            atomReqPromotionStoreRelationDTO.setMerchantNo(goodsPromotionInfoDTO.getMerchantNo());
            atomReqPromotionStoreRelationDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
            ExecuteDTO<AtomResPromotionStoreRelationDTO> sjStoreRelationExecuteDTO = promotionStoreRelationAnalysisService.getSjPromotionStoreRelation(atomReqPromotionStoreRelationDTO);
            if (sjStoreRelationExecuteDTO == null || !sjStoreRelationExecuteDTO.successFlag()) {
                return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
            }
            AtomResPromotionStoreRelationDTO oldSjStoreRelation = sjStoreRelationExecuteDTO.getData();
            if (oldSjStoreRelation == null) {
                //没有保存关联店铺信息,则新增数据
                List<AtomReqPromotionStoreRelationDTO> promotionStoreRelationList = this.convertCommunitySolitaireStores(
                        goodsPromotionInfoDTO);
                //编辑时关联店铺新增，上下架状态取编辑时设置的上下架状态
                //promotionStoreRelationList.forEach(promotionStoreRelationDTO -> {
                //promotionStoreRelationDTO.setUpDownFlag(goodsPromotionInfoDTO.getUpDownFlag());
                //});
                ExecuteDTO saveStoreExecuteDTO = promotionStoreRelationOperatService.batchSavePromotionStoreRelation(promotionStoreRelationList, NumConstant.ZERO);
                log.info("保存社群接龙活动店铺出参：{}", saveStoreExecuteDTO);
            } else {
                //已经存在关联店铺信息,则更新数据
                if (StoreTypeEnum.STORE_TYPE_ONE.getCode().equals(updateStoreType)) {
                    /**
                     * 更新为全部店铺1001
                     */
                    AtomReqPromotionStoreRelationDTO enableAllStoreRelationDTO = new AtomReqPromotionStoreRelationDTO();
                    enableAllStoreRelationDTO.setMerchantNo(goodsPromotionInfoDTO.getMerchantNo());
                    enableAllStoreRelationDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
                    enableAllStoreRelationDTO.setModifyNo(goodsPromotionInfoDTO.getModifyNo());
                    enableAllStoreRelationDTO.setModifyName(goodsPromotionInfoDTO.getModifyName());
                    enableAllStoreRelationDTO.setStoreType(updateStoreType);
                    //enableAllStoreRelationDTO.setUpDownFlag(goodsPromotionInfoDTO.getUpDownFlag());
                    enableAllStoreRelationDTO.setDisableFlag(StoreUseEnum.ENABLE.getCode());
                    enableAllStoreRelationDTO.setDeleteFlag(NumConstant.ONE);
                    ExecuteDTO enableAllStoreExecuteDTO = promotionStoreRelationOperatService.enableAllStoreRelation(enableAllStoreRelationDTO);
                    if (enableAllStoreExecuteDTO == null || !enableAllStoreExecuteDTO.successFlag()) {
                        return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
                    }
                } else if (StoreTypeEnum.STORE_TYPE_THREE.getCode().equals(updateStoreType)) {
                    /**
                     * 更新为指定店铺1003
                     */
                    ExecuteDTO<List<AtomResPromotionStoreRelationDTO>> oldStoreRelationExecuteDTO = promotionStoreRelationAnalysisService.getStoreRelationList(atomReqPromotionStoreRelationDTO);
                    if (oldStoreRelationExecuteDTO == null || !oldStoreRelationExecuteDTO.successFlag()) {
                        return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
                    }
                    List<String> oldStoreRelationNoList = oldStoreRelationExecuteDTO.getData().stream().map(AtomResPromotionStoreRelationDTO::getStoreNo).collect(Collectors.toList());
                    List<AtomReqPromotionStoreRelationDTO> addStoreRelationList = Lists.newArrayList();
                    //已经存在还将存在的店铺编码
                    List<String> oldEnableStoreList = Lists.newArrayList();
                    for (ReqCommunitySolitaireStoreDTO storeDTO : goodsPromotionInfoDTO.getStoresList()) {
                        if (!oldStoreRelationNoList.contains(storeDTO.getStoreNo())) {
                            //新增数据
                            AtomReqPromotionStoreRelationDTO storeRelationDTO = new AtomReqPromotionStoreRelationDTO();
                            storeRelationDTO = new AtomReqPromotionStoreRelationDTO();
                            storeRelationDTO.setCreateNo(goodsPromotionInfoDTO.getCreateNo());
                            storeRelationDTO.setCreateName(goodsPromotionInfoDTO.getCreateName());
                            storeRelationDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
                            storeRelationDTO.setMerchantNo(goodsPromotionInfoDTO.getMerchantNo());
                            storeRelationDTO.setStoreNo(storeDTO.getStoreNo());
                            storeRelationDTO.setStoreType(updateStoreType);
                            storeRelationDTO.setDisableFlag(StoreUseEnum.ENABLE.getCode());
                            storeRelationDTO.setUpDownFlag(StoreUpDownEnum.UP.getCode());
                            storeRelationDTO.setRuleNo(MarketFormGenerator.getStoreRuleNo());
                            storeRelationDTO.setStoreSourceType(SourceTypeEnum.SOURCE_TYPE_1002.getCode());
                            addStoreRelationList.add(storeRelationDTO);
                        } else {
                            //已经存在的店铺
                            oldEnableStoreList.add(storeDTO.getStoreNo());
                        }
                    }
                    //已经存在即将不存在的店铺编码, 即移除的店铺
                    if (ListUtil.isNotEmpty(oldEnableStoreList) && ListUtil.isNotEmpty(oldStoreRelationNoList)) {
                        oldStoreRelationNoList.removeAll(oldEnableStoreList);
                    }
                    if (ListUtil.isNotEmpty(oldStoreRelationNoList)) {
                        List<String> oldDisableStoreList = oldStoreRelationNoList.stream().filter(storeNo -> StringUtils.isNotEmpty(storeNo)).collect(Collectors.toList());
                        if (ListUtil.isNotEmpty(oldDisableStoreList)) {
                            //已经存在即将不存在的店铺,即移除的店铺 设置disable
                            AtomReqPromotionStoreRelationDTO disableStoreRelationDTO = new AtomReqPromotionStoreRelationDTO();
                            disableStoreRelationDTO.setMerchantNo(goodsPromotionInfoDTO.getMerchantNo());
                            disableStoreRelationDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
                            disableStoreRelationDTO.setModifyNo(goodsPromotionInfoDTO.getModifyNo());
                            disableStoreRelationDTO.setModifyName(goodsPromotionInfoDTO.getModifyName());
                            ExecuteDTO disableStoreExecuteDTO = promotionStoreRelationOperatService.disableExistStoreRelation(disableStoreRelationDTO, oldDisableStoreList);
                            if (disableStoreExecuteDTO == null || !disableStoreExecuteDTO.successFlag()) {
                                return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
                            }
                        }
                    }
                    //商家关联，店铺编码为空字符串
                    if (!oldEnableStoreList.contains(StringUtils.EMPTY)) {
                        oldEnableStoreList.add(StringUtils.EMPTY);
                    }
                    //商家社群接龙已经存在，继续关联的店铺
                    if (ListUtil.isNotEmpty(oldEnableStoreList)) {
                        AtomReqPromotionStoreRelationDTO enableStoreRelationDTO = new AtomReqPromotionStoreRelationDTO();
                        enableStoreRelationDTO.setMerchantNo(goodsPromotionInfoDTO.getMerchantNo());
                        enableStoreRelationDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
                        enableStoreRelationDTO.setModifyNo(goodsPromotionInfoDTO.getModifyNo());
                        enableStoreRelationDTO.setModifyName(goodsPromotionInfoDTO.getModifyName());
                        enableStoreRelationDTO.setStoreType(updateStoreType);
                        //enableStoreRelationDTO.setUpDownFlag(goodsPromotionInfoDTO.getUpDownFlag());
                        ExecuteDTO enableStoreExecuteDTO = promotionStoreRelationOperatService.enableExistStoreRelation(enableStoreRelationDTO, oldEnableStoreList);
                        if (enableStoreExecuteDTO == null || !enableStoreExecuteDTO.successFlag()) {
                            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
                        }
                    }
                    //存在新增数据
                    if (ListUtil.isNotEmpty(addStoreRelationList)) {
                        ExecuteDTO saveStoreExecuteDTO = promotionStoreRelationOperatService.batchSavePromotionStoreRelation(addStoreRelationList, NumConstant.ONE);
                        if (saveStoreExecuteDTO == null || !saveStoreExecuteDTO.successFlag()) {
                            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
                        }
                    }
                }
                //更新商家关联店铺的分发店铺方式以及上下架状态等信息
                AtomReqPromotionStoreRelationDTO sjStoreRelationDTO = new AtomReqPromotionStoreRelationDTO();
                sjStoreRelationDTO.setMerchantNo(goodsPromotionInfoDTO.getMerchantNo());
                sjStoreRelationDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
                sjStoreRelationDTO.setModifyNo(goodsPromotionInfoDTO.getModifyNo());
                sjStoreRelationDTO.setModifyName(goodsPromotionInfoDTO.getModifyName());
                sjStoreRelationDTO.setStoreType(updateStoreType);
                sjStoreRelationDTO.setUpDownFlag(goodsPromotionInfoDTO.getUpDownFlag());
                ExecuteDTO sjStoreExecuteDTO = promotionStoreRelationOperatService.updateSjStoreRelation(sjStoreRelationDTO);
                if (sjStoreExecuteDTO == null || !sjStoreExecuteDTO.successFlag()) {
                    return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
                }
            }
        }

        // 活动图片保存
        if (ListUtil.isNotEmpty(goodsPromotionInfoDTO.getPromotionImages())) {
            goodsPromotionImageService.batchUpdatePromotionImages(goodsPromotionInfoDTO.getPromotionImages(), reqPromotionInfoDTO);
        }
        // 操作结束后，解锁
        unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
        return ExecuteDTO.ok(goodsPromotionInfoDTO.getPromotionNo());
    }

    /**
     * 初始化商家接龙发送模板消息信息
     *
     * @param goodsPromotionInfoDTO
     * @param storeNoList
     * @param merchantStoreList
     * @return
     */
    private ExecuteDTO initSjPromotionTemplateMsg(ReqCommunitySolitaireAddDTO goodsPromotionInfoDTO, List<String> storeNoList, List<MerchantStoreResponse> merchantStoreList) {
        MerchantStoreRequest merchantStoreRequest = new MerchantStoreRequest();
        merchantStoreRequest.setMerchantNo(goodsPromotionInfoDTO.getMerchantNo());
        ExecuteDTO<List<MerchantStoreResponse>> merchantStoreResponseExecuteDTO = storeProcessService.queryMerchantStore(merchantStoreRequest);
        if (null == merchantStoreResponseExecuteDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!merchantStoreResponseExecuteDTO.successFlag()) {
            return ExecuteDTO.error(merchantStoreResponseExecuteDTO.getStatus(), merchantStoreResponseExecuteDTO.getMsg());
        }
        merchantStoreList.addAll(merchantStoreResponseExecuteDTO.getData());
        if (CollectionUtils.isEmpty(merchantStoreList)) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000636);
        }
        //分发店铺
        if (StoreTypeEnum.STORE_TYPE_ONE.getCode().equals(goodsPromotionInfoDTO.getStoreType())) {
            //全部店铺
            storeNoList.addAll(merchantStoreList.stream().map(MerchantStoreResponse::getStoreNo).collect(Collectors.toList()));
        } else if (StoreTypeEnum.STORE_TYPE_THREE.getCode().equals(goodsPromotionInfoDTO.getStoreType())) {
            //指定店铺
            AtomReqPromotionStoreRelationDTO atomReqPromotionStoreRelationDTO = new AtomReqPromotionStoreRelationDTO();
            atomReqPromotionStoreRelationDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
            atomReqPromotionStoreRelationDTO.setMerchantNo(goodsPromotionInfoDTO.getMerchantNo());
            atomReqPromotionStoreRelationDTO.setDisableFlag(StoreUseEnum.ENABLE.getCode());
            ExecuteDTO<List<AtomResPromotionStoreRelationDTO>> storeRelationExecuteDTO = promotionStoreRelationAnalysisService.getStoreRelationList(atomReqPromotionStoreRelationDTO);
            if (storeRelationExecuteDTO == null || !storeRelationExecuteDTO.successFlag()) {
                return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
            }
            if (ListUtil.isNotEmpty(storeRelationExecuteDTO.getData())) {
                storeNoList.addAll(storeRelationExecuteDTO.getData().stream()
                        .map(AtomResPromotionStoreRelationDTO::getStoreNo)
                        .filter(StringUtils::isNotEmpty).collect(Collectors.toList()));
            }
        }
        return ExecuteDTO.ok();
    }

    private String statusByTime(ReqCommunitySolitaireAddDTO goodsPromotionInfoDTO) {
        if (DateUtil.getLocalDateTimeNow().isBefore(goodsPromotionInfoDTO.getEffectiveTime())) {
            return PromotionStatusEnum.NOT_STARTED.getCode();
        } else if (!DateUtil.getLocalDateTimeNow().isAfter(goodsPromotionInfoDTO.getInvalidTime())) {
            return PromotionStatusEnum.IN_PROGRESS.getCode();
        } else {
            return PromotionStatusEnum.EXPIRED.getCode();
        }
    }

//202503
    @Override
    public ExecuteDTO<String> saveMemberPricePromotion(ReqMemberPriceAddDTO reqDTO) {
        log.info("**GoodsPromotionOperateServiceImpl.saveMemberPricePromotion,reqDto：{}", reqDTO);
        // 入参校验
        goodsPromotionAssert.saveMemberPricePromotionAssert(reqDTO);
        // 设置默认值
        if (StringUtils.isBlank(reqDTO.getGoodsPromotionScope())) {
            reqDTO.setGoodsPromotionScope(GoodsPromotionScopeEnum.SCOPE_TYPE_GOODS.getCode());
        }
        // 活动类型设置为 商品促销
        reqDTO.setActivityType(ActivityTypeEnum.PROMOTION.getCode());

        // 商品校验
        if (ListUtil.isNotEmpty(reqDTO.getGoodsList())) {
            Set<String> goodsNoList = reqDTO.getGoodsList().stream().map(ReqVipPriceGoodsDTO::getGoodsNo).collect(Collectors.toSet());
            AtomReqGoodsDTO goodsDTO = new AtomReqGoodsDTO();
            goodsDTO.setGoodsNos(new ArrayList<>(goodsNoList));
            ExecuteDTO<List<AtomResGoodsDTO>> goodsExecuteDTO = legacyGoodsCenterService.getGoodsByNos(goodsDTO);
            if (null == goodsExecuteDTO || !goodsExecuteDTO.successFlag()) {
                return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
            } else if (ListUtil.isEmpty(goodsExecuteDTO.getData())) {
                return new ExecuteDTO<>(GoodsErrorCode.CODE_12000001.getCode(), GoodsErrorCode.CODE_12000001.getInMsg(), null);
            } else {
                Map<String, AtomResGoodsDTO> goodsDTOMap = ListUtil.toMap(goodsExecuteDTO.getData(), AtomResGoodsDTO::getGoodsNo);
                for (ReqVipPriceGoodsDTO reqVipPriceGoodsDTO : reqDTO.getGoodsList()) {
                    AtomResGoodsDTO atomResGoodsDTO = goodsDTOMap.get(reqVipPriceGoodsDTO.getGoodsNo());
                    if (null == atomResGoodsDTO) {
                        return new ExecuteDTO<>(GoodsErrorCode.CODE_12000001.getCode(), GoodsErrorCode.CODE_12000001.getInMsg(), null);
                    }
                    reqVipPriceGoodsDTO.setParentGoodsNo(atomResGoodsDTO.getParentGoodsNo());
                }
            }
        }

        // 如果前端没有传活动编号，表示是新增
        ExecuteDTO<String> executeDTO;
        if (StringUtils.isBlank(reqDTO.getPromotionNo())) {
            executeDTO = this.addMemberPricePromotionRule(reqDTO);// 新增活动 202503
            if (!executeDTO.successFlag()) {
                return new ExecuteDTO<>(executeDTO.getStatus(), executeDTO.getMsg(), null);
            }
        } else {
            // 如果前端传了活动信息编号，则表示是修改更新操作
            executeDTO = this.editMemberPricePromotionRule(reqDTO);
            if (!executeDTO.successFlag()) {
                return new ExecuteDTO<>(executeDTO.getStatus(), executeDTO.getMsg(), null);
            }
        }

        return executeDTO;
    }

    @Override
    public ExecuteDTO<String> copyPromotionInfo(ReqPromotionQueryDTO reqDTO) {

        if (StringUtils.isBlank(reqDTO.getPromotionNo())) {
            return ExecuteDTOUtil.error(CommonCode.CODE_10000001, "活动编号");
        }

        ExecuteDTO<ResMemberPriceDetailDTO> executeDTO = goodsPromotionRuleAnalysisService.getMemberPriceDetailByPromotionNo(reqDTO);

        if (null == executeDTO) {
            return ExecuteDTOUtil.error(CommonCode.CODE_10000002, "被复制的会员价活动");
        }

        if (!executeDTO.successFlag()) {
            return ExecuteDTOUtil.error(executeDTO.getStatus(), executeDTO.getMsg());
        }


        ReqMemberPriceAddDTO reqMemberPriceAddDTO = JSON.parseObject(JSON.toJSONString(executeDTO.getData()), ReqMemberPriceAddDTO.class);

        if (StringUtils.isNotBlank(reqMemberPriceAddDTO.getPromotionName())) {
            reqMemberPriceAddDTO.setPromotionName(reqMemberPriceAddDTO.getPromotionName() + "-副本");
        }

        //草稿
        reqMemberPriceAddDTO.setStatus(PromotionStatusEnum.ONLY_SAVED.getCode());
        //创建来源PC
        reqMemberPriceAddDTO.setCreateSource(PromotionCreateSourceEnum.PC.getCode());
        //商品促销
        reqMemberPriceAddDTO.setActivityType(ActivityTypeEnum.PROMOTION.getCode());
        //来源
        reqMemberPriceAddDTO.setSourceType(SourceTypeEnum.SOURCE_TYPE_1002.getCode());
        //新增复制的活动
        reqMemberPriceAddDTO.setPromotionNo(null);

        ExecuteDTO<String> stringExecuteDTO = this.saveMemberPricePromotion(reqMemberPriceAddDTO);
        return stringExecuteDTO;
    }

    /**
     * 新增保存特惠促销活动规则数据 会员价
     * 202503
     *
     * @param goodsPromotionInfoDTO ReqVipPriceAddDTO
     * @return 活动编号
     * <AUTHOR>
     * @date 2022-07-18
     */
    private ExecuteDTO<String> addMemberPricePromotionRule(ReqMemberPriceAddDTO goodsPromotionInfoDTO) {
        // 生成促销活动的编号，默认是 秒杀的活动编号
        String promotionType = PromotionTypeEnum.MEMBER_PRICE.getCode();
        String promotionNo = MarketFormGenerator.genMemberPricePromotionNo();
        goodsPromotionInfoDTO.setPromotionNo(promotionNo);

        LocalDateTime effectiveTime = LocalDateTime.now();
        LocalDateTime invalidTime = LocalDateTime.of(9999, 12, 31, 23, 59, 59, 0);
        // 保存活动基本信息
        AtomReqPromotionInfoDTO reqPromotionInfoDTO = BeanCopierUtil.copy(goodsPromotionInfoDTO, AtomReqPromotionInfoDTO.class);
        reqPromotionInfoDTO.setPromotionType(promotionType);
        reqPromotionInfoDTO.setPeriodValidity(CouponPeriodValidityEnum.COUPON_PERIOD_VALIDITY_ONE.getCode());
        reqPromotionInfoDTO.setEffectiveTime(effectiveTime);
        reqPromotionInfoDTO.setInvalidTime(invalidTime);
        // 取活动场次的最小开始和最大结束时间入到promotion_info表
        reqPromotionInfoDTO.setDailyStartTime(goodsPromotionInfoDTO.getStartTime());
        reqPromotionInfoDTO.setDailyEndTime(goodsPromotionInfoDTO.getEndTime());
        // 设置指定时间段
        reqPromotionInfoDTO.setEffectivePeriodType(EffectivePeriodTypeEnum.EFFECTIVE_PERIOD_TYPE_TWO.getCode());
        reqPromotionInfoDTO.setOldStatus(PromotionStatusEnum.ONLY_SAVED.getCode());    //待更新的草稿状态
        if (StringUtils.isNotBlank(goodsPromotionInfoDTO.getStatus())) {
            //复制场景下创建的活动,为草稿
            reqPromotionInfoDTO.setStatus(PromotionStatusEnum.ONLY_SAVED.getCode());
            reqPromotionInfoDTO.setUpDownFlag(WhetherEnum.NO.getCode());
        } else {
            reqPromotionInfoDTO.setStatus(PromotionStatusEnum.IN_PROGRESS.getCode());   //要更新的状态
        }
        ExecuteDTO promotionInfoExecuteDTO = promotionInfoOperatService.saveGoodsPromotionInfo(reqPromotionInfoDTO);// 新增活动
        log.info("保存会员价活动基本信息出参：{}", promotionInfoExecuteDTO);
        if (promotionInfoExecuteDTO == null || !promotionInfoExecuteDTO.successFlag()) {
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        }

        // 保存活动规则信息
        AtomReqGoodsPromotionRuleDTO goodsPromotionRuleDTO = BeanCopierUtil.copy(goodsPromotionInfoDTO, AtomReqGoodsPromotionRuleDTO.class);
        goodsPromotionRuleDTO.setFreeDeliveryFlag(WhetherEnum.YES.getCode());
        goodsPromotionRuleDTO.setDeliveryFlag(WhetherEnum.YES.getCode());
        goodsPromotionRuleDTO.setPromotionType(promotionType);
        goodsPromotionRuleDTO.setGoodsPromotionType(GoodsPromotionTypeEnum.MEMBER_PRICE.getCode());
        goodsPromotionRuleDTO.setPeriodValidity(CouponPeriodValidityEnum.COUPON_PERIOD_VALIDITY_ONE.getCode());
        goodsPromotionRuleDTO.setEffectiveTime(effectiveTime);
        goodsPromotionRuleDTO.setInvalidTime(invalidTime);
        if(StringUtils.isNotEmpty(goodsPromotionInfoDTO.getGoodsPromotionUserType())){
            goodsPromotionRuleDTO.setGoodsPromotionUserType(goodsPromotionInfoDTO.getGoodsPromotionUserType());
        }
        if (GoodsPromotionScopeEnum.SCOPE_TYPE_ALL.getCode().equals(goodsPromotionInfoDTO.getGoodsPromotionScope())) {
            goodsPromotionRuleDTO.setAllGoodsFlag(WhetherEnum.YES.getCode());
        }
        ExecuteDTO promotionRuleExecuteDTO = promotionRuleOperateService.saveGoodsPromotionRule(goodsPromotionRuleDTO);// 新增活动规则信息 202503 goodsPromotionUserType  1001 一口价  1002 折扣率 旧数据一律一口价计算
        log.info("保存商品会员价活动规则信息出参：{}", promotionInfoExecuteDTO);
        if (promotionRuleExecuteDTO == null || !promotionRuleExecuteDTO.successFlag()) {
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        }

        // 2、若为店铺
        if (SourceTypeEnum.SOURCE_TYPE_1003.getCode().equals(goodsPromotionInfoDTO.getSourceType())) {
            // 店铺指定类目
            if (GoodsPromotionScopeEnum.SCOPE_TYPE_CATEGORY.getCode().equals(goodsPromotionInfoDTO.getGoodsPromotionScope())) {
                // 先删除再新增
                List<ReqCouponCategoryRelationDTO> reqCouponCategoryRelationDTOS = goodsPromotionInfoDTO.getReqCouponCategoryRelationDTOS();
                if (!CollectionUtils.isEmpty(reqCouponCategoryRelationDTOS)) {
                    List<AtomReqGoodsPromotionCategoryRelationDTO> addVoList = BeanCopierUtil.copyList(reqCouponCategoryRelationDTOS, AtomReqGoodsPromotionCategoryRelationDTO.class);
                    addVoList.forEach(addVo -> addVo.setPromotionNo(promotionNo));
                    this.atomGoodsPromotionCategoryRelationOperateService.savePromotionCategoryRelation(addVoList);
                }
            }
        }



        // 保存活动的时间段信息
        AtomReqGoodsPromotionPeriodDTO promotionPeriodDTO = new AtomReqGoodsPromotionPeriodDTO();
        promotionPeriodDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
        promotionPeriodDTO.setRepectType(goodsPromotionInfoDTO.getRepectType());
        promotionPeriodDTO.setRepectVal(goodsPromotionInfoDTO.getRepectVal());
        promotionPeriodDTO.setStartTime(goodsPromotionInfoDTO.getStartTime());
        promotionPeriodDTO.setEndTime(goodsPromotionInfoDTO.getEndTime());
        // 生成促销活动时间段编号
        String periodNo = MarketFormGenerator.genGoodsPeriodNo();
        promotionPeriodDTO.setPeriodNo(periodNo);
        // 设置创建用户、店铺以及商家信息
        promotionPeriodDTO.setCreateNo(goodsPromotionInfoDTO.getCreateNo());
        promotionPeriodDTO.setCreateName(goodsPromotionInfoDTO.getCreateName());
        promotionPeriodDTO.setMerchantNo(goodsPromotionInfoDTO.getMerchantNo());
        promotionPeriodDTO.setMerchantName(goodsPromotionInfoDTO.getMerchantName());
        promotionPeriodDTO.setStoreNo(goodsPromotionInfoDTO.getStoreNo());
        promotionPeriodDTO.setStoreName(goodsPromotionInfoDTO.getStoreName());
        ExecuteDTO savePeriodExecuteDTO = promotionPeriodOperateService.batchSaveGoodsPromotionPeriod(Collections.singletonList(promotionPeriodDTO));//商品促销活动时间段表
        log.info("保存商品会员价活动时间段出参：{}", savePeriodExecuteDTO);
        if (savePeriodExecuteDTO == null || !savePeriodExecuteDTO.successFlag()) {
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        }

        // 关联商品保存
        if (ListUtil.isNotEmpty(goodsPromotionInfoDTO.getGoodsList())) {
            List<AtomReqGoodsPromotionGoodsRelationDTO> goodsVoList = this.convertVipPriceGoods(goodsPromotionInfoDTO, periodNo);
            this.atomGoodsPromotionGoodsRelationOperateService.savePromotionGoodsRelation(goodsVoList);
        } else if (GoodsPromotionScopeEnum.SCOPE_TYPE_GOODS.getCode().equals(goodsPromotionInfoDTO.getGoodsPromotionScope())) {
            return new ExecuteDTO<>(CommonCode.CODE_10000001.getCode(), String.format(CommonCode.CODE_10000001.getInMsg(), "活动商品"), null);
        }
        // 封装返回活动编码
        return ExecuteDTO.ok(promotionNo);
    }

    private List<AtomReqGoodsPromotionGoodsRelationDTO> convertVipPriceGoods(ReqMemberPriceAddDTO goodsPromotionInfoDTO, String periodNo) {
        List<AtomReqGoodsPromotionGoodsRelationDTO> resultList = new ArrayList<>();
        if (ListUtil.isNotEmpty(goodsPromotionInfoDTO.getGoodsList())) {
            for (ReqVipPriceGoodsDTO reqVipPriceGoodsDTO : goodsPromotionInfoDTO.getGoodsList()) {
                AtomReqGoodsPromotionGoodsRelationDTO atomGoodsDTO = new AtomReqGoodsPromotionGoodsRelationDTO();
                atomGoodsDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
                atomGoodsDTO.setPromotionType(PromotionTypeEnum.MEMBER_PRICE.getCode());
                // 是否子品(1:否 2:是)
                atomGoodsDTO.setSubGoodsFlag(NumConstant.TWO);
                atomGoodsDTO.setPeriodNo(periodNo);
                atomGoodsDTO.setStoreNo(goodsPromotionInfoDTO.getStoreNo());
                atomGoodsDTO.setStoreName(goodsPromotionInfoDTO.getStoreName());
                atomGoodsDTO.setMerchantNo(goodsPromotionInfoDTO.getMerchantNo());
                atomGoodsDTO.setMerchantName(goodsPromotionInfoDTO.getMerchantName());
                atomGoodsDTO.setCreateNo(goodsPromotionInfoDTO.getCreateNo());
                atomGoodsDTO.setCreateName(goodsPromotionInfoDTO.getCreateName());
                if(StringUtils.isNotEmpty(reqVipPriceGoodsDTO.getDiscountRate())){
                    atomGoodsDTO.setDiscountRate(reqVipPriceGoodsDTO.getDiscountRate());
                }
                // 商品编码
                atomGoodsDTO.setGoodsNo(reqVipPriceGoodsDTO.getGoodsNo());
                atomGoodsDTO.setParentGoodsNo(reqVipPriceGoodsDTO.getParentGoodsNo());
                // 特惠促销
                atomGoodsDTO.setPromotionPrice(reqVipPriceGoodsDTO.getPromotionPrice());

                resultList.add(atomGoodsDTO);
            }
        }

        return resultList;
    }

    /**
     * 编辑保存特惠促销活动规则数据
     *
     * @param goodsPromotionInfoDTO ReqVipPriceAddDTO
     * @return 活动编号
     * <AUTHOR>
     * @date 2022-07-18
     */
    private ExecuteDTO<String> editMemberPricePromotionRule(ReqMemberPriceAddDTO goodsPromotionInfoDTO) {
        //会员价编辑后只有进行中的状态
        goodsPromotionInfoDTO.setStatus(PromotionStatusEnum.IN_PROGRESS.getCode());
        // 操作正在进行中的活动时需要加锁
        boolean lockFlag = lockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
        if (!lockFlag) {
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), "编辑操作前加锁失败，确定是否编辑时有其他操作", null);
        }
        // 更新基本信息
        AtomReqPromotionInfoDTO reqPromotionInfoDTO = BeanCopierUtil.copy(goodsPromotionInfoDTO, AtomReqPromotionInfoDTO.class);
        reqPromotionInfoDTO.setPromotionType(PromotionTypeEnum.MEMBER_PRICE.getCode());
        reqPromotionInfoDTO.setDailyStartTime(goodsPromotionInfoDTO.getStartTime());
        reqPromotionInfoDTO.setDailyEndTime(goodsPromotionInfoDTO.getEndTime());
        // 上下架字段不为空 为保存并上架
        if (NumConstant.TWO == goodsPromotionInfoDTO.getUpDownFlag()) {
            reqPromotionInfoDTO.setLatestUpDownTime(LocalDateTime.now());
        }
        ExecuteDTO updateGoodsPromotionInfo = promotionInfoOperatService.updateGoodsPromotionInfo(reqPromotionInfoDTO);
        log.info("更新促销活动信息出参：{}", updateGoodsPromotionInfo);
        if (updateGoodsPromotionInfo == null || !updateGoodsPromotionInfo.successFlag()) {
            // 操作结束后，解锁
            unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        }

        // 更新活动规则信息
        AtomReqGoodsPromotionRuleDTO goodsPromotionRuleDTO = BeanCopierUtil.copy(goodsPromotionInfoDTO, AtomReqGoodsPromotionRuleDTO.class);
        goodsPromotionRuleDTO.setFreeDeliveryFlag(WhetherEnum.YES.getCode());
        goodsPromotionRuleDTO.setDeliveryFlag(WhetherEnum.YES.getCode());
        goodsPromotionRuleDTO.setPromotionType(PromotionTypeEnum.MEMBER_PRICE.getCode());
        goodsPromotionRuleDTO.setGoodsPromotionType(GoodsPromotionTypeEnum.MEMBER_PRICE.getCode());
        if (GoodsPromotionScopeEnum.SCOPE_TYPE_ALL.getCode().equals(goodsPromotionInfoDTO.getGoodsPromotionScope())) {
            goodsPromotionRuleDTO.setAllGoodsFlag(WhetherEnum.YES.getCode());
        }
        if(StringUtils.isNotEmpty(goodsPromotionInfoDTO.getGoodsPromotionUserType())){
            goodsPromotionRuleDTO.setGoodsPromotionUserType(goodsPromotionInfoDTO.getGoodsPromotionUserType());
        }

        ExecuteDTO updateGoodsPromotionRule = promotionRuleOperateService.updateGoodsPromotionRule(goodsPromotionRuleDTO);
        log.info("更新促销活动规则信息出参：{}", updateGoodsPromotionRule);
        if (updateGoodsPromotionRule == null || !updateGoodsPromotionRule.successFlag()) {
            // 操作结束后，解锁
            unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        }


        // 保存活动的时间段信息
        AtomReqGoodsPromotionPeriodDTO promotionPeriodDTO = new AtomReqGoodsPromotionPeriodDTO();
        String periodNo = MarketFormGenerator.genGoodsPeriodNo();
        promotionPeriodDTO.setPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
        promotionPeriodDTO.setRepectType(goodsPromotionInfoDTO.getRepectType());
        promotionPeriodDTO.setRepectVal(goodsPromotionInfoDTO.getRepectVal());
        promotionPeriodDTO.setStartTime(goodsPromotionInfoDTO.getStartTime());
        promotionPeriodDTO.setEndTime(goodsPromotionInfoDTO.getEndTime());
        // 生成促销活动时间段编号
        promotionPeriodDTO.setPeriodNo(periodNo);
        // 设置创建用户、店铺以及商家信息
        promotionPeriodDTO.setCreateNo(goodsPromotionInfoDTO.getCreateNo());
        promotionPeriodDTO.setCreateName(goodsPromotionInfoDTO.getCreateName());
        promotionPeriodDTO.setMerchantNo(goodsPromotionInfoDTO.getMerchantNo());
        promotionPeriodDTO.setMerchantName(goodsPromotionInfoDTO.getMerchantName());
        promotionPeriodDTO.setStoreNo(goodsPromotionInfoDTO.getStoreNo());
        promotionPeriodDTO.setStoreName(goodsPromotionInfoDTO.getStoreName());
        promotionPeriodOperateService.deletePeriodByPromotionNo(goodsPromotionInfoDTO.getPromotionNo());
        ExecuteDTO savePeriodExecuteDTO = promotionPeriodOperateService.batchSaveGoodsPromotionPeriod(Collections.singletonList(promotionPeriodDTO));
        log.info("保存特惠促销的时间段出参：{}", savePeriodExecuteDTO);
        if (savePeriodExecuteDTO == null || !savePeriodExecuteDTO.successFlag()) {
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        }

        // 关联商品保存、先删除，再添加
        if (ListUtil.isNotEmpty(goodsPromotionInfoDTO.getGoodsList())) {
            this.atomGoodsPromotionGoodsRelationOperateService.deletePromotionGoodsRelation(goodsPromotionInfoDTO.getPromotionNo());
            List<AtomReqGoodsPromotionGoodsRelationDTO> goodsVoList = this.convertVipPriceGoods(goodsPromotionInfoDTO, periodNo);
            this.atomGoodsPromotionGoodsRelationOperateService.savePromotionGoodsRelation(goodsVoList);
        } else if (GoodsPromotionScopeEnum.SCOPE_TYPE_ALL.getCode().equals(goodsPromotionInfoDTO.getGoodsPromotionScope())) {
            // 全场通用，可能修改前是指定商品，因此此处也删除关联商品
            this.atomGoodsPromotionGoodsRelationOperateService.deletePromotionGoodsRelation(goodsPromotionInfoDTO.getPromotionNo());
        } else if (GoodsPromotionScopeEnum.SCOPE_TYPE_GOODS.getCode().equals(goodsPromotionInfoDTO.getGoodsPromotionScope())) {
            return new ExecuteDTO<>(CommonCode.CODE_10000001.getCode(), String.format(CommonCode.CODE_10000001.getInMsg(), "活动商品"), null);
        }
        // 操作结束后，解锁
        unLockGoodsPromotion(goodsPromotionInfoDTO.getPromotionNo());
        return ExecuteDTO.ok(goodsPromotionInfoDTO.getPromotionNo());
    }

    @Override
    public ExecuteDTO<String> saveGoodsPromotion(ReqGoodsPromotionInfoDTO goodsPromotionInfoDTO, List<ReqGoodsPromotionGoodsRelationDTO> goodsRelationDTOList) {
        for (GoodsPromotionService goodsPromotionService : goodsPromotionServices) {
            if (goodsPromotionService.checkByPromotionType(goodsPromotionInfoDTO)) {
                if (StringUtils.isNotBlank(goodsPromotionInfoDTO.getPromotionExplain())) {
                    goodsPromotionInfoDTO.setPromotionExplain(goodsPromotionInfoDTO.getPromotionExplain().trim());
                }
                return goodsPromotionService.saveGoodsPromotion(goodsPromotionInfoDTO, goodsRelationDTOList);
            }
        }
        return ExecuteDTOUtil.error(CommonCode.CODE_10000003);
    }

    /**
     * 店铺上下级商家创建的活动
     *
     * @param upOrDownDTO
     * @return
     */
    @Override
    public ExecuteDTO storeUpDownSjPromotion(ReqGoodsPromotionUpOrDownDTO upOrDownDTO) {
        log.info("**GoodsPromotionOperateServiceImpl.storeUpDownSjPromotion店铺上下级商家创建的活动**start**");
        log.info("**GoodsPromotionOperateServiceImpl.storeUpDownSjPromotion入参：{}", upOrDownDTO);
        // 入参校验
        goodsPromotionAssert.upDownGoodsPromotionAssert(upOrDownDTO);

        // 根据活动编号查询活动详情
        AtomReqPromotionInfoDTO reqPromotionInfoDTO = new AtomReqPromotionInfoDTO();
        reqPromotionInfoDTO.setPromotionNo(upOrDownDTO.getPromotionNo());
        ExecuteDTO<AtomResPromotionInfoDTO> promotionExecuteDTO =
                promotionInfoAnalysisService.getPromotionInfo(reqPromotionInfoDTO);
        // 查询结果分析
        if (null == promotionExecuteDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!promotionExecuteDTO.successFlag()) {
            return ExecuteDTO.error(promotionExecuteDTO.getStatus(), promotionExecuteDTO.getMsg());
        }
        AtomResPromotionInfoDTO promotionInfoDTO = promotionExecuteDTO.getData();
        // 没有找到要上下架的活动时，退出
        if (null == promotionInfoDTO) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000600);
        }
        // 活动状态为 草稿状态时，不能上下架
        if (PromotionStatusEnum.ONLY_SAVED.getCode().equals(promotionInfoDTO.getStatus())) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000601);
        }
        // 活动已过期，不允许上下架
        // 取当前的日期时间，用于判断活动是否过期
        LocalDateTime now = DateUtil.getLocalDateTimeNow();
        if (null != promotionInfoDTO.getInvalidTime() && now.isAfter(promotionInfoDTO.getInvalidTime())) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000602);
        }
        if (PromotionTypeEnum.COMMUNITY_SOLITAIRE_SJ.getCode().equals(promotionInfoDTO.getPromotionType())) {
            //分发店铺上下架
            if (StringUtils.isNotEmpty(upOrDownDTO.getStoreNo())) {
                //查询商家下的全部店铺信息, 校验店铺信息
                MerchantStoreRequest merchantStoreRequest = new MerchantStoreRequest();
                merchantStoreRequest.setMerchantNo(promotionInfoDTO.getMerchantNo());
                ExecuteDTO<List<MerchantStoreResponse>> merchantStoreResponseExecuteDTO = storeProcessService.queryMerchantStore(merchantStoreRequest);
                if (null == merchantStoreResponseExecuteDTO) {
                    return ExecuteDTO.error(CommonCode.CODE_10000003);
                }
                if (!merchantStoreResponseExecuteDTO.successFlag()) {
                    return ExecuteDTO.error(merchantStoreResponseExecuteDTO.getStatus(), merchantStoreResponseExecuteDTO.getMsg());
                }
                List<MerchantStoreResponse> merchantStoreList = merchantStoreResponseExecuteDTO.getData();
                if (CollectionUtils.isEmpty(merchantStoreList)) {
                    return ExecuteDTO.error(MarketErrorCode.CODE_17000636);
                } else {
                    List<String> storeNoList = merchantStoreList.stream().map(MerchantStoreResponse::getStoreNo).collect(Collectors.toList());
                    if (!storeNoList.contains(upOrDownDTO.getStoreNo())) {
                        return ExecuteDTO.error(CommonCode.CODE_10000002, "商家没有该店铺");
                    }
                }
                //店铺操作上下架
                AtomReqPromotionStoreRelationDTO atomReqPromotionStoreRelationDTO = new AtomReqPromotionStoreRelationDTO();
                atomReqPromotionStoreRelationDTO.setPromotionNo(promotionInfoDTO.getPromotionNo());
                atomReqPromotionStoreRelationDTO.setMerchantNo(promotionInfoDTO.getMerchantNo());
                ExecuteDTO<List<AtomResPromotionStoreRelationDTO>> atomResPromotionStoreRelationDTO = promotionStoreRelationAnalysisService.getStoreRelationList(atomReqPromotionStoreRelationDTO);
                if (null == atomResPromotionStoreRelationDTO) {
                    return ExecuteDTO.error(CommonCode.CODE_10000003);
                }
                if (!atomResPromotionStoreRelationDTO.successFlag()) {
                    return ExecuteDTO.error(atomResPromotionStoreRelationDTO.getStatus(), atomResPromotionStoreRelationDTO.getMsg());
                }
                List<AtomResPromotionStoreRelationDTO> atomResPromotionStoreRelationList = atomResPromotionStoreRelationDTO.getData();
                String storeType = null;
                for (AtomResPromotionStoreRelationDTO storeRelation : atomResPromotionStoreRelationList) {
                    if (StringUtils.isEmpty(storeRelation.getStoreNo())) {
                        storeType = storeRelation.getStoreType();
                        break;
                    }
                }
                List<AtomResPromotionStoreRelationDTO> exsistStoreRelationList = atomResPromotionStoreRelationList.stream()
                        .filter(storeRelation -> upOrDownDTO.getStoreNo().equals(storeRelation.getStoreNo()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(exsistStoreRelationList)) {
                    //不存在记录，商家关联全部店铺, 新增一条记录
                    List<AtomReqPromotionStoreRelationDTO> addList = Lists.newArrayList();
                    AtomReqPromotionStoreRelationDTO addDto = BeanCopierUtil.copy(upOrDownDTO, AtomReqPromotionStoreRelationDTO.class);
                    addDto.setCreateNo(upOrDownDTO.getModifyNo());
                    addDto.setCreateName(upOrDownDTO.getModifyName());
                    addDto.setPromotionNo(promotionInfoDTO.getPromotionNo());
                    addDto.setMerchantNo(promotionInfoDTO.getMerchantNo());
                    addDto.setStoreNo(upOrDownDTO.getStoreNo());
                    addDto.setStoreType(storeType);
                    addDto.setDisableFlag(StoreUseEnum.ENABLE.getCode());
                    addDto.setUpDownFlag(upOrDownDTO.getUpDownFlag());
                    addDto.setRuleNo(MarketFormGenerator.getStoreRuleNo());
                    addDto.setStoreSourceType(SourceTypeEnum.SOURCE_TYPE_1002.getCode());
                    addList.add(addDto);
                    ExecuteDTO storeRlationAddExecuteDTO = promotionStoreRelationOperatService.batchSavePromotionStoreRelation(addList, NumConstant.ZERO);
                    if (null == storeRlationAddExecuteDTO) {
                        return ExecuteDTO.error(CommonCode.CODE_10000003);
                    }
                    if (!storeRlationAddExecuteDTO.successFlag()) {
                        return ExecuteDTO.error(storeRlationAddExecuteDTO.getStatus(), storeRlationAddExecuteDTO.getMsg());
                    }
                } else {
                    //存在记录,修改已存在记录
                    AtomReqPromotionStoreRelationDTO upDownStoreRelationDTO = BeanCopierUtil.copy(upOrDownDTO, AtomReqPromotionStoreRelationDTO.class);
                    upDownStoreRelationDTO.setLatestUpDownTime(LocalDateTime.now());
                    ExecuteDTO upDownStoreRelationExecuteDTO = promotionStoreRelationOperatService.upDownStoreRelation(upDownStoreRelationDTO);
                    if (null == upDownStoreRelationExecuteDTO) {
                        return ExecuteDTO.error(CommonCode.CODE_10000003);
                    }
                    if (!upDownStoreRelationExecuteDTO.successFlag()) {
                        return ExecuteDTO.error(upDownStoreRelationExecuteDTO.getStatus(), upDownStoreRelationExecuteDTO.getMsg());
                    }
                }
            }
        }
        log.info("**GoodsPromotionOperateServiceImpl.storeUpDownSjPromotion店铺上下级商家创建的活动**end**");
        return ExecuteDTO.ok();
    }

}
