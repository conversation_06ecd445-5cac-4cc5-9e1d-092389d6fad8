package cn.htdt.marketprocess.biz.reactor;

import cn.htdt.common.base.exception.BaseException;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketprocess.api.operat.WxPushMessageOperateService;
import cn.htdt.marketprocess.biz.constant.ConstantConstant;
import cn.htdt.marketprocess.biz.reactor.inft.EventProcess;
import cn.htdt.marketprocess.dto.request.ReqWxPushMsgDTO;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * 微信推送消息
 *
 * <AUTHOR>
 * @Date 2021-07-29
 **/
@Slf4j
public class WxPushMsg implements EventProcess {

    @Resource
    private WxPushMessageOperateService wxPushMessageOperateService;

    private WxPushMsg(ReactorBus reactorBus) {
        reactorBus.addon(ConstantConstant.ReactorProcess.WxPushMsg, this);
    }

    @Override
    public <T> void process(T t) {
        try {
            ReqWxPushMsgDTO reqWxPushMsgDTO = (ReqWxPushMsgDTO) t;
            log.info("--wxPushMessageOperateService.wxPushMessage--入参--{}", reqWxPushMsgDTO);
            ExecuteDTO executeDTO = wxPushMessageOperateService.wxPushMessage(reqWxPushMsgDTO);
            log.info("--wxPushMessageOperateService.wxPushMessage--出参--{}", executeDTO);
        } catch (BaseException e) {
            log.error("******WxPushMsg-process-", e);
        }
    }
}
