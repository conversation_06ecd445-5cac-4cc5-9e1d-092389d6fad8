package cn.htdt.marketprocess.biz.operat;

import cn.htdt.common.base.exception.BaseException;
import cn.htdt.common.dto.enums.MarketErrorCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.market.CardPeriodValidityEnum;
import cn.htdt.common.enums.market.CardStatusEnum;
import cn.htdt.common.generator.MarketFormGenerator;
import cn.htdt.common.redis.utils.RedisUtil;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.CollectionUtils;
import cn.htdt.marketprocess.api.operat.CardCountingFansOperatService;
import cn.htdt.marketprocess.biz.conversion.CardCountingFansAssert;
import cn.htdt.marketprocess.dao.CardCountingConfigDao;
import cn.htdt.marketprocess.dao.CardCountingFansDao;
import cn.htdt.marketprocess.dao.CardCountingFansUseRecordDao;
import cn.htdt.marketprocess.domain.CardCountingFansDomain;
import cn.htdt.marketprocess.domain.CardCountingFansUseRecordDomain;
import cn.htdt.marketprocess.dto.request.ReqCardCountingFansDTO;
import cn.htdt.marketprocess.dto.request.ReqCardCountingFansListDTO;
import cn.htdt.marketprocess.dto.request.ReqCardCountingFansUseRecordDTO;
import cn.htdt.marketprocess.vo.CardCountingConfigVO;
import cn.htdt.marketprocess.vo.CardCountingFansVO;
import cn.htdt.usercenter.dto.response.ResFancDTO;
import cn.htdt.userprocess.api.UcFansProcessService;
import cn.htdt.userprocess.dto.request.GetFancInfoRequest;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 计次卡粉丝表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-13
 */
@Slf4j
@DubboService
public class CardCountingFansOperatServiceImpl implements CardCountingFansOperatService {

    @DubboReference
    private UcFansProcessService ucFansProcessService;

    @Resource
    private CardCountingConfigDao cardCountingConfigDao;

    @Resource
    private CardCountingFansDao cardCountingFansDao;

    @Resource
    private CardCountingFansUseRecordDao cardCountingFansUseRecordDao;

    @Autowired
    private CardCountingFansAssert cardCountingFansAssert;

    @Autowired
    private RedisUtil redisUtil;

    public static final String CARD_COUNTING_WRITE_OFF_PREFIX = "cardCounting:writeOff:store_%s:%s";

    @Override
    public ExecuteDTO<String> sendFansCardCounting(ReqCardCountingFansDTO reqCardCountingFansDTO) throws BaseException {
        log.info("-CardCountingFansOperatServiceImpl-sendFansCardCounting-param={}", JSON.toJSONString(reqCardCountingFansDTO));
        // 参数校验
        cardCountingFansAssert.sendFansCardCountingAssert(reqCardCountingFansDTO);
        // 判断活动是否存在
        CardCountingConfigVO cardCountingConfigVO = new CardCountingConfigVO();
        cardCountingConfigVO.setPromotionNo(reqCardCountingFansDTO.getPromotionNo());
        CardCountingConfigVO cardCountingConfig = this.cardCountingConfigDao.selectCardCountingConfig(cardCountingConfigVO);
        log.info("-CardCountingFansOperatServiceImpl-sendFansCardCounting-活动信息={}", JSON.toJSONString(cardCountingConfig));
        if (cardCountingConfig == null) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000100);
        }
        // @TODO 判断发放和配置是否同一个店
//        String cardFansNo = StringUtils.isEmpty(reqCardCountingFansDTO.getCardFansNo()) ? MarketFormGenerator.genCardCountingFansNo() : reqCardCountingFansDTO.getCardFansNo();
//        String key = String.format(KeysConstant.PROMOTION_CARD_FANS_PREFIX, reqCardCountingFansDTO.getStoreNo(), reqCardCountingFansDTO.getFanNo(), cardFansNo);
//        if (redisUtil.hasKey(key)) {
//            return ExecuteDTO.error(MarketErrorCode.CODE_17000710);
//        }
        // 判断是否已发过，已发过无需再发卡
        CardCountingFansVO cardCountingFansVO = new CardCountingFansVO();
        cardCountingFansVO.setOrderNo(reqCardCountingFansDTO.getOrderNo());
        cardCountingFansVO.setFanNo(reqCardCountingFansDTO.getFanNo());
        cardCountingFansVO.setMerchantNo(reqCardCountingFansDTO.getMerchantNo());
        cardCountingFansVO.setStoreNo(reqCardCountingFansDTO.getStoreNo());
        Integer count = this.cardCountingFansDao.selectCardCountingFansCount(cardCountingFansVO);
        log.info("-CardCountingFansOperatServiceImpl-sendFansCardCounting-selectCardCountingFansCount-count={}", count);
        // 不存在，发放卡
        if (count != null && count <= NumConstant.ZERO) {
            CardCountingFansDomain cardCountingFansDomain = BeanCopierUtil.copy(reqCardCountingFansDTO, CardCountingFansDomain.class);
            // 获取粉丝信息
            GetFancInfoRequest getFancInfoRequest = new GetFancInfoRequest();
            getFancInfoRequest.setFanNo(reqCardCountingFansDTO.getFanNo());
            ExecuteDTO<ResFancDTO> executeDTO = this.ucFansProcessService.getFansByFanNo(getFancInfoRequest);
            log.info("-CardCountingFansOperatServiceImpl-sendFansCardCounting-getFansByFanNo-获取粉丝信息={}", JSON.toJSONString(executeDTO));
            if (executeDTO.successFlag()) {
                if (executeDTO.getData() != null) {
                    cardCountingFansDomain.setPhone(executeDTO.getData().getPhone());
                    cardCountingFansDomain.setDsPhone(executeDTO.getData().getDsPhone());
                    if (StringUtils.isNotBlank(executeDTO.getData().getStoreFanName())) {
                        cardCountingFansDomain.setFanName(executeDTO.getData().getStoreFanName());
                    } else if (StringUtils.isNotBlank(executeDTO.getData().getName())) {
                        cardCountingFansDomain.setFanName(executeDTO.getData().getName());
                    } else if (StringUtils.isNotBlank(executeDTO.getData().getNickName())) {
                        cardCountingFansDomain.setFanName(executeDTO.getData().getNickName());
                    }
                }
            } else {
                log.info("-CardCountingFansOperatServiceImpl-sendFansCardCounting-getFansByFanNo获取粉丝信息异常");
            }
            cardCountingFansDomain.setCardFansNo(MarketFormGenerator.genCardCountingFansNo());
            cardCountingFansDomain.setCardNo(MarketFormGenerator.getFansCardNo(NumConstant.TWELVE));
            cardCountingFansDomain.setCardPeriodValidity(cardCountingConfig.getCardPeriodValidity());
            if (StringUtils.isBlank(cardCountingFansDomain.getCardCountingName())) {
                cardCountingFansDomain.setCardCountingName(cardCountingConfig.getCardCountingName());
            }
            if (StringUtils.isBlank(cardCountingFansDomain.getStoreName())) {
                cardCountingFansDomain.setStoreName(cardCountingConfig.getStoreName());
            }
            if (StringUtils.isBlank(cardCountingFansDomain.getMerchantName())) {
                cardCountingFansDomain.setMerchantName(cardCountingConfig.getMerchantName());
            }
            if (CardPeriodValidityEnum.CARD_PERIOD_VALIDITY_ONE.getCode().equals(cardCountingConfig.getCardPeriodValidity())) {
                // 长期有效
                cardCountingFansDomain.setCardEffectiveTime(LocalDateTime.now());
                cardCountingFansDomain.setCardInvalidTime(LocalDateTime.of(9999, 12, 31, 23, 59, 59, 0));
            } else if (CardPeriodValidityEnum.CARD_PERIOD_VALIDITY_TWO.getCode().equals(cardCountingConfig.getCardPeriodValidity())) {
                // 指定日期
                cardCountingFansDomain.setCardEffectiveTime(cardCountingConfig.getCardEffectiveTime());
                cardCountingFansDomain.setCardInvalidTime(cardCountingConfig.getCardInvalidTime());
            } else if (CardPeriodValidityEnum.CARD_PERIOD_VALIDITY_THREE.getCode().equals(cardCountingConfig.getCardPeriodValidity())) {
                // 自领取后天数有效
                int cardEffectiveDay = cardCountingConfig.getCardEffectiveDay() == null ? NumConstant.ONE : cardCountingConfig.getCardEffectiveDay();
                LocalDateTime ldt = LocalDateTime.now();
                LocalDateTime endDateTime = LocalDateTime.of(ldt.getYear(), ldt.getMonth(), ldt.getDayOfMonth(), 23, 59, 59, 0);
                cardCountingFansDomain.setCardEffectiveTime(ldt);
                cardCountingFansDomain.setCardInvalidTime(endDateTime.plusDays((long) (cardEffectiveDay - 1)));
            }
            cardCountingFansDomain.setCardCounts(cardCountingConfig.getCounts());
            cardCountingFansDomain.setCardRemainCounts(cardCountingConfig.getCounts());
            cardCountingFansDomain.setCardStatus(CardStatusEnum.CARD_STATUS_ZERO.getCode());
            cardCountingFansDomain.setUseExplain(cardCountingConfig.getUseExplain());
            // 发放计次卡信息
            log.info("-CardCountingFansOperatServiceImpl-sendFansCardCounting-发放计次卡信息={}", JSON.toJSONString(cardCountingFansDomain));
            this.cardCountingFansDao.insert(cardCountingFansDomain);
        }
        log.info("-CardCountingFansOperatServiceImpl-sendFansCardCounting-end");
        return ExecuteDTO.success();
    }

    @Override
    public ExecuteDTO<String> sendFansCardCountings(ReqCardCountingFansListDTO reqCardCountingFansListDTO) {
        log.info("-CardCountingFansOperatServiceImpl-sendFansCardCountings-param={}", JSON.toJSONString(reqCardCountingFansListDTO));
        // 参数校验
        cardCountingFansAssert.sendFansCardCountingsAssert(reqCardCountingFansListDTO);
        // 判断活动是否存在
        CardCountingConfigVO cardCountingConfigVO = new CardCountingConfigVO();
        cardCountingConfigVO.setPromotionNo(reqCardCountingFansListDTO.getReqCardCountingFansDTOList().get(NumConstant.ZERO).getPromotionNo());
        CardCountingConfigVO cardCountingConfig = this.cardCountingConfigDao.selectCardCountingConfig(cardCountingConfigVO);
        log.info("-CardCountingFansOperatServiceImpl-sendFansCardCountings-活动信息={}", JSON.toJSONString(cardCountingConfig));
        if (cardCountingConfig == null) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000100);
        }
        // @TODO 判断发放和配置是否同一个店

        // 判断是否已发过，已发过无需再发卡
        CardCountingFansVO cardCountingFansVO = new CardCountingFansVO();
        cardCountingFansVO.setOrderNo(reqCardCountingFansListDTO.getReqCardCountingFansDTOList().get(NumConstant.ZERO).getOrderNo());
        cardCountingFansVO.setFanNo(reqCardCountingFansListDTO.getReqCardCountingFansDTOList().get(NumConstant.ZERO).getFanNo());
        cardCountingFansVO.setMerchantNo(reqCardCountingFansListDTO.getReqCardCountingFansDTOList().get(NumConstant.ZERO).getMerchantNo());
        cardCountingFansVO.setStoreNo(reqCardCountingFansListDTO.getReqCardCountingFansDTOList().get(NumConstant.ZERO).getStoreNo());
        Integer count = this.cardCountingFansDao.selectCardCountingFansCount(cardCountingFansVO);
        log.info("-CardCountingFansOperatServiceImpl-sendFansCardCountings-selectCardCountingFansCount-count={}", count);
        // 不存在，发放卡
        if (count != null && count <= NumConstant.ZERO) {
            for (ReqCardCountingFansDTO reqCardCountingFansDTO : reqCardCountingFansListDTO.getReqCardCountingFansDTOList()) {
                CardCountingFansDomain cardCountingFansDomain = BeanCopierUtil.copy(reqCardCountingFansDTO, CardCountingFansDomain.class);
                cardCountingFansDomain.setCardFansNo(MarketFormGenerator.genCardCountingFansNo());
                cardCountingFansDomain.setCardNo(MarketFormGenerator.getFansCardNo(NumConstant.SIXTEEN));
                cardCountingFansDomain.setCardPeriodValidity(cardCountingConfig.getCardPeriodValidity());

                if (StringUtils.isBlank(cardCountingFansDomain.getCardCountingName())) {
                    cardCountingFansDomain.setCardCountingName(cardCountingConfig.getCardCountingName());
                }
                if (StringUtils.isBlank(cardCountingFansDomain.getStoreName())) {
                    cardCountingFansDomain.setStoreName(cardCountingConfig.getStoreName());
                }
                if (StringUtils.isBlank(cardCountingFansDomain.getMerchantName())) {
                    cardCountingFansDomain.setMerchantName(cardCountingConfig.getMerchantName());
                }
                if (CardPeriodValidityEnum.CARD_PERIOD_VALIDITY_ONE.getCode().equals(cardCountingConfig.getCardPeriodValidity())) {
                    // 长期有效
                    cardCountingFansDomain.setCardEffectiveTime(LocalDateTime.now());
                    cardCountingFansDomain.setCardInvalidTime(LocalDateTime.of(9999, 12, 31, 23, 59, 59, 0));
                } else if (CardPeriodValidityEnum.CARD_PERIOD_VALIDITY_TWO.getCode().equals(cardCountingConfig.getCardPeriodValidity())) {
                    // 指定日期
                    cardCountingFansDomain.setCardEffectiveTime(cardCountingConfig.getCardEffectiveTime());
                    cardCountingFansDomain.setCardInvalidTime(cardCountingConfig.getCardInvalidTime());
                } else if (CardPeriodValidityEnum.CARD_PERIOD_VALIDITY_THREE.getCode().equals(cardCountingConfig.getCardPeriodValidity())) {
                    // 自领取后天数有效
                    int cardEffectiveDay = cardCountingConfig.getCardEffectiveDay() == null ? NumConstant.ONE : cardCountingConfig.getCardEffectiveDay();
                    LocalDateTime ldt = LocalDateTime.now();
                    LocalDateTime endDateTime = LocalDateTime.of(ldt.getYear(), ldt.getMonth(), ldt.getDayOfMonth(), 23, 59, 59, 0);
                    cardCountingFansDomain.setCardEffectiveTime(ldt);
                    cardCountingFansDomain.setCardInvalidTime(endDateTime.plusDays((long) (cardEffectiveDay - 1)));
                }
                cardCountingFansDomain.setUseExplain(cardCountingConfig.getUseExplain());
                // 发放计次卡信息
                log.info("-CardCountingFansOperatServiceImpl-sendFansCardCountings-发放计次卡信息={}", JSON.toJSONString(cardCountingFansDomain));
                this.cardCountingFansDao.insert(cardCountingFansDomain);
            }
        }
        log.info("-CardCountingFansOperatServiceImpl-sendFansCardCountings-end");
        return ExecuteDTO.success();
    }

    @Override
    public ExecuteDTO dealFansCardCountingsReturn(ReqCardCountingFansDTO reqCardCountingFansDTO) throws BaseException {
        log.info("-CardCountingFansOperatServiceImpl-dealFansCardCountingsReturn-param={}", JSON.toJSONString(reqCardCountingFansDTO));
        // 参数校验
        cardCountingFansAssert.dealFansCardCountingsReturnAssert(reqCardCountingFansDTO);
        CardCountingFansVO cardCountingFansVO = new CardCountingFansVO();
        cardCountingFansVO.setOrderNo(reqCardCountingFansDTO.getOrderNo());
        List<CardCountingFansVO> cardCountingFansVOS = this.cardCountingFansDao.selectCardCountingFansListByOrder(cardCountingFansVO);
        // 处理退资源时，计数卡的处理逻辑
        if (CollectionUtils.isNotEmpty(cardCountingFansVOS)) {
            // 逻辑删除计数卡
            this.cardCountingFansDao.deleteCardCountingFansListByOrder(cardCountingFansVO);
        }
        log.info("-CardCountingFansOperatServiceImpl-dealFansCardCountingsReturn-end");
        return ExecuteDTO.success();
    }

    /**
     * 计次卡核销
     *
     * @param reqDTO 请求参数
     * @return ExecuteDTO
     * <AUTHOR>
     */
    @Override
    public ExecuteDTO writeOffCardCounting(ReqCardCountingFansUseRecordDTO reqDTO) {
        log.info("-CardCountingFansOperatServiceImpl-writeOffCardCounting-param={}", JSON.toJSONString(reqDTO));

        // 计次卡核销抢锁
        int i = NumConstant.ZERO;
        String lockKey = String.format(CARD_COUNTING_WRITE_OFF_PREFIX, reqDTO.getStoreNo(), reqDTO.getCardNo());
        // 抢锁状态
        boolean lockStatus = false;
        while (i < NumConstant.SIX) {
            i++;
            lockStatus = redisUtil.setnx(lockKey, reqDTO.getCardNo(), NumConstant.FIVE);
            //抢锁，超时时间5秒
            if (lockStatus) {
                log.info("获取了redis锁的当前线程是:{}", Thread.currentThread().getName());
                break;
            }

            try {
                log.info("线程:{}第{}占用锁失败，自旋等待结果", Thread.currentThread().getName(), i);
                //每次抢锁间隔1秒
                Thread.sleep(NumConstant.ONE_THOUSAND);
            } catch (InterruptedException e) {
                log.error("writeOffCardCounting接口报错msg：", e);
                Thread.currentThread().interrupt();
            }
        }

        if (lockStatus) {
            try {
                // 抢锁成功，核销计次卡
                // 获取计次卡信息
                CardCountingFansVO domain = new CardCountingFansVO();
                domain.setCardNo(reqDTO.getCardNo());
                domain.setStoreNo(reqDTO.getStoreNo());
                log.info("---writeOffCardCounting---cardCountingFansDao.selectByParam-入参：{}---", JSON.toJSONString(domain));
                List<CardCountingFansVO> cardCountingFansList = cardCountingFansDao.selectByParam(domain);
                log.info("---writeOffCardCounting---cardCountingFansDao.selectByParam-出参：{}---", JSON.toJSONString(cardCountingFansList));
                if (CollectionUtils.isEmpty(cardCountingFansList)) {
                    // 未找到计次卡
                    log.info("---writeOffCardCounting---未找到计次卡---");
                    return ExecuteDTO.error(MarketErrorCode.CODE_17001203);
                }
                CardCountingFansVO cardCountingFans = cardCountingFansList.get(NumConstant.ZERO);
                if (CardStatusEnum.CARD_STATUS_TWO.getCode().equals(cardCountingFans.getCardStatus())
                        || CardStatusEnum.CARD_STATUS_THREE.getCode().equals(cardCountingFans.getCardStatus())) {
                    // 计次卡已过期/已失效
                    log.info("---writeOffCardCounting---计次卡已过期/已失效---");
                    return ExecuteDTO.error(MarketErrorCode.CODE_17001204);
                }
                if (CardPeriodValidityEnum.CARD_PERIOD_VALIDITY_TWO.getCode().equals(cardCountingFans.getCardPeriodValidity())
                        || CardPeriodValidityEnum.CARD_PERIOD_VALIDITY_THREE.getCode().equals(cardCountingFans.getCardPeriodValidity())) {
                    //1002已过期
                    if (cardCountingFans.getCardInvalidTime().isBefore(LocalDateTime.now())) {
                        // 计次卡已过期
                        log.info("---writeOffCardCounting---计次卡已过期---");
                        return ExecuteDTO.error(MarketErrorCode.CODE_17001204);
                    }
                }
                if (cardCountingFans.getCardRemainCounts() < reqDTO.getConsumeCounts()) {
                    // 剩余可用次数不足，请重新输入
                    log.info("---writeOffCardCounting---剩余可用次数不足，请重新输入---");
                    return ExecuteDTO.error(MarketErrorCode.CODE_17001205);
                }
                // 修改计次卡余额
                CardCountingFansVO cardCountingFansVO = new CardCountingFansVO();
                cardCountingFansVO.setCardNo(reqDTO.getCardNo());
                cardCountingFansVO.setStoreNo(reqDTO.getStoreNo());
                cardCountingFansVO.setConsumeCounts(reqDTO.getConsumeCounts());
                if (reqDTO.getConsumeCounts().equals(cardCountingFans.getCardRemainCounts())) {
                    cardCountingFansVO.setCardStatus(CardStatusEnum.CARD_STATUS_ONE.getCode());
                }
                log.info("---writeOffCardCounting---cardCountingFansDao.updateCardCountingCardRemainCounts-入参：{}---", JSON.toJSONString(cardCountingFansVO));
                cardCountingFansDao.updateCardCountingCardRemainCounts(cardCountingFansVO);

                // 新增计次卡使用记录
                CardCountingFansUseRecordDomain useRecordDomain = new CardCountingFansUseRecordDomain();
                useRecordDomain.setCardFansRecordNo(MarketFormGenerator.genCardFansRecordNo());
                useRecordDomain.setCardFansNo(cardCountingFans.getCardFansNo());
                useRecordDomain.setPromotionNo(cardCountingFans.getPromotionNo());
                useRecordDomain.setCardCountingNo(cardCountingFans.getCardCountingNo());
                useRecordDomain.setCardCountingName(cardCountingFans.getCardCountingName());
                useRecordDomain.setFanNo(cardCountingFans.getFanNo());
                useRecordDomain.setFanName(cardCountingFans.getFanName());
                useRecordDomain.setCardNo(cardCountingFans.getCardNo());
                useRecordDomain.setCardCounts(cardCountingFans.getCardCounts());
                useRecordDomain.setConsumeCounts(reqDTO.getConsumeCounts());
                useRecordDomain.setCardRemainCounts(cardCountingFans.getCardRemainCounts() - useRecordDomain.getConsumeCounts());
                useRecordDomain.setUseSource(reqDTO.getUseSource());
                useRecordDomain.setMerchantNo(reqDTO.getMerchantNo());
                useRecordDomain.setMerchantName(reqDTO.getMerchantName());
                useRecordDomain.setStoreNo(reqDTO.getStoreNo());
                useRecordDomain.setStoreName(reqDTO.getStoreName());
                log.info("---writeOffCardCounting---cardCountingFansUseRecordDao.insert-入参：{}---", JSON.toJSONString(useRecordDomain));
                cardCountingFansUseRecordDao.insert(useRecordDomain);

                return ExecuteDTO.success();
            } finally {
                log.error("释放锁，ket：{}", lockKey);
                redisUtil.del(lockKey);
            }
        }

        return ExecuteDTO.error(MarketErrorCode.CODE_17001206);
    }
}
