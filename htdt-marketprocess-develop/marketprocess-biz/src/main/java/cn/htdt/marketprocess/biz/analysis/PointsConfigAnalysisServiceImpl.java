package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.constants.StringNumConstant;
import cn.htdt.common.enums.market.PointConfigTypeEnum;
import cn.htdt.common.enums.market.PointLimitEnum;
import cn.htdt.common.generator.IdGenerator;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqPointsConfigDTO;
import cn.htdt.marketcenter.dto.request.AtomReqPointsConfigLogDTO;
import cn.htdt.marketcenter.dto.response.AtomResPointsConfigDTO;
import cn.htdt.marketcenter.dto.response.AtomResPointsConfigLogDTO;
import cn.htdt.marketprocess.api.analysis.PointsConfigAnalysisService;
import cn.htdt.marketprocess.api.operat.PointsConfigOperateService;
import cn.htdt.marketprocess.biz.conversion.PointsConfigAssert;
import cn.htdt.marketprocess.biz.utils.PointsUtil;
import cn.htdt.marketprocess.biz.utils.UserInfoUtil;
import cn.htdt.marketprocess.dto.request.ReqPointsConfigDTO;
import cn.htdt.marketprocess.dto.response.ResPointsConfigDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomPointsConfigAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomPointsConfigLogAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomPointsConfigOperateService;
import cn.htdt.userprocess.dto.response.MerchantInfoResponseT;
import cn.htdt.userprocess.dto.response.StoreInfoResponse;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


@DubboService
@Slf4j
public class PointsConfigAnalysisServiceImpl implements PointsConfigAnalysisService {
    @Resource
    PointsConfigAssert configAssert;

    @Resource
    PointsConfigOperateService pointsConfigOperateService;

    @Resource
    AtomPointsConfigLogAnalysisService atomPointsConfigLogAnalysisService;

    @Resource
    AtomPointsConfigAnalysisService atomPointsConfigAnalysisService;
    @Resource
    private AtomPointsConfigOperateService atomPointsConfigOperateService;

    @Resource
    PointsUtil pointsUtil;

    @Resource
    private UserInfoUtil userInfoUtil;

    /**
     * @see PointsConfigAnalysisService#getStorePointsConfig(ReqPointsConfigDTO)
     */
    @Override
    public ExecuteDTO<List<ResPointsConfigDTO>> getStorePointsConfig(ReqPointsConfigDTO reqPointsConfigDTO) {
        log.info("PointsConfigAnalysisServiceImpl.selectStorePointsConfig---param----{}", JSON.toJSONString(reqPointsConfigDTO));
        AtomReqPointsConfigDTO atomReqPointsConfigDTO = null;
        ExecuteDTO<List<AtomResPointsConfigDTO>> executeDTO = null;
        //20230928蛋品-wh-商家积分-查询商家和店铺积分配置信息
        if(null == reqPointsConfigDTO.getLoginIdentity() || NumConstant.FOUR == reqPointsConfigDTO.getLoginIdentity()){
            //如果是门店根据门店查询积分配置
            configAssert.getStorePointsConfig(reqPointsConfigDTO);
            //20230928蛋品-wh-查询店铺是否是共享店铺
            StoreInfoResponse storeInfoResponse = pointsUtil.queryStore(reqPointsConfigDTO.getStoreNo());
            if(storeInfoResponse.getMemberSharing() == NumConstant.ONE){
                //查询门店的
                atomReqPointsConfigDTO = BeanCopierUtil.copy(reqPointsConfigDTO, AtomReqPointsConfigDTO.class);
                reqPointsConfigDTO.setPointsType(String.valueOf(NumConstant.ONE));
                atomReqPointsConfigDTO.setPointsType(String.valueOf(NumConstant.ONE));
                executeDTO = atomPointsConfigAnalysisService.getStorePointsConfigs(atomReqPointsConfigDTO);
            }else {
                //查询商家的
                //如果是商家就按商家查询，商家积分配置
                atomReqPointsConfigDTO = BeanCopierUtil.copy(reqPointsConfigDTO, AtomReqPointsConfigDTO.class);
                atomReqPointsConfigDTO.setMerchantNo(reqPointsConfigDTO.getMerchantNo());
                reqPointsConfigDTO.setPointsType(String.valueOf(NumConstant.TWO));
                executeDTO = atomPointsConfigAnalysisService.getMerchantPointsConfigs(atomReqPointsConfigDTO);
            }
        }else if(NumConstant.TWO == reqPointsConfigDTO.getLoginIdentity()){
            //如果是商家就按商家查询，商家积分配置
            atomReqPointsConfigDTO = BeanCopierUtil.copy(reqPointsConfigDTO, AtomReqPointsConfigDTO.class);
            atomReqPointsConfigDTO.setMerchantNo(reqPointsConfigDTO.getMerchantNo());
            reqPointsConfigDTO.setPointsType(String.valueOf(NumConstant.TWO));
            executeDTO = atomPointsConfigAnalysisService.getMerchantPointsConfigs(atomReqPointsConfigDTO);
        }

        //如果数据为空 需要初始化配置信息
        if (executeDTO.successFlag() && executeDTO.getData().size() ==0) {
            pointsConfigOperateService.initStorePointsConfig(reqPointsConfigDTO);
        }else {
            //如果数据不为空 初始化后加的积分抵扣配置信息
            AtomReqPointsConfigDTO atomReqPointsConfig = BeanCopierUtil.copy(reqPointsConfigDTO, AtomReqPointsConfigDTO.class);
            atomReqPointsConfig.setConfigType(PointConfigTypeEnum.POINT_PAY.getCode());
            if(executeDTO.getData().stream().filter(s-> s.getConfigType().equals(PointConfigTypeEnum.POINT_PAY.getCode())).findAny().isPresent() == false){
                List<AtomReqPointsConfigDTO> atomReqPointsConfigDTOS = new ArrayList<>();
                atomReqPointsConfig.setConfigNo(IdGenerator.getDid());
                atomReqPointsConfig.setDisableFlag(2);
                atomReqPointsConfig.setConfigType(PointConfigTypeEnum.POINT_PAY.getCode());
                atomReqPointsConfig.setDisableFlag(NumConstant.TWO);
                atomReqPointsConfig.setDeductionScale(NumConstant.ONE_HUNDRED);
                atomReqPointsConfig.setDeductionAmountLimit(PointLimitEnum.NO_LIMIT.getCode());
                atomReqPointsConfig.setOrderAmountLimit(PointLimitEnum.NO_LIMIT.getCode());
                StringBuffer stringBuffer = new StringBuffer();
                stringBuffer.append("1001").append(",").append("1002").append(",").append("1003");
                atomReqPointsConfig.setUseChannel(stringBuffer.toString());
                atomReqPointsConfigDTOS.add(atomReqPointsConfig);
                atomPointsConfigOperateService.batchAddPointsConfig(atomReqPointsConfigDTOS);
            }
        }

        if(null == reqPointsConfigDTO.getLoginIdentity() || NumConstant.FOUR == reqPointsConfigDTO.getLoginIdentity()){
            //20230928蛋品-wh-查询店铺是否是共享店铺
            StoreInfoResponse storeInfoResponse = pointsUtil.queryStore(reqPointsConfigDTO.getStoreNo());
            if(storeInfoResponse.getMemberSharing() == NumConstant.ONE){
                //查询门店的
                atomReqPointsConfigDTO = BeanCopierUtil.copy(reqPointsConfigDTO, AtomReqPointsConfigDTO.class);
                reqPointsConfigDTO.setPointsType(String.valueOf(NumConstant.ONE));
                atomReqPointsConfigDTO.setPointsType(String.valueOf(NumConstant.ONE));
                executeDTO = atomPointsConfigAnalysisService.getStorePointsConfigs(atomReqPointsConfigDTO);
            }else {
                //查询商家的
                //如果是商家就按商家查询，商家积分配置
                atomReqPointsConfigDTO = BeanCopierUtil.copy(reqPointsConfigDTO, AtomReqPointsConfigDTO.class);
                atomReqPointsConfigDTO.setMerchantNo(reqPointsConfigDTO.getMerchantNo());
                reqPointsConfigDTO.setPointsType(String.valueOf(NumConstant.TWO));
                executeDTO = atomPointsConfigAnalysisService.getMerchantPointsConfigs(atomReqPointsConfigDTO);
            }
        }else if(NumConstant.TWO == reqPointsConfigDTO.getLoginIdentity()){
            //如果是商家就按商家查询，商家积分配置
            executeDTO = atomPointsConfigAnalysisService.getMerchantPointsConfigs(atomReqPointsConfigDTO);
        }

        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        List<ResPointsConfigDTO> resPointsConfigDTOList = BeanCopierUtil.copyList(executeDTO.getData(), ResPointsConfigDTO.class);
        hasOperateLog(resPointsConfigDTOList);
        log.info("PointsConfigAnalysisServiceImpl.selectStorePointsConfig----end----{}");
        return ExecuteDTO.success(resPointsConfigDTOList);
    }

    /**
     * 判断店铺积分配置是否有操作记录
     *
     * @param resPointsConfigDTOList 店铺积分配置
     */
    private void hasOperateLog(List<ResPointsConfigDTO> resPointsConfigDTOList) {
        for (ResPointsConfigDTO temp : resPointsConfigDTOList) {
            if (PointConfigTypeEnum.POINT_ORDER.getCode().equals(temp.getConfigType()) || PointConfigTypeEnum.POINT_STORE.getCode().equals(temp.getConfigType())) {
                AtomReqPointsConfigLogDTO atomReqPointsConfigLogDTO = new AtomReqPointsConfigLogDTO();
                atomReqPointsConfigLogDTO.setConfigNo(temp.getConfigNo());
                ExecuteDTO<ExecutePageDTO<AtomResPointsConfigLogDTO>> executeDTO = atomPointsConfigLogAnalysisService.getPointsConfigLog(atomReqPointsConfigLogDTO);
                if (executeDTO.getData().getRows().size() == 0) {
                    temp.setInitialization(true);
                }
            }
        }
    }
    /**
     * 20230928蛋品-赵翔宇-商家积分, orderprocess有不少地方调用这里, 下单后送积分, 查询积分配置等
     *
     * @see PointsConfigAnalysisService#getOpenStorePointsConfig(ReqPointsConfigDTO)
     */
    @Override
    public ExecuteDTO<List<ResPointsConfigDTO>> getOpenStorePointsConfig(ReqPointsConfigDTO reqPointsConfigDTO) {
        log.info("PointsConfigAnalysisServiceImpl.getOpenStorePointsConfig---param----{}", JSON.toJSONString(reqPointsConfigDTO));
        configAssert.getStorePointsConfig(reqPointsConfigDTO);

        // 20230928蛋品-赵翔宇-商家积分
        String pointsType = reqPointsConfigDTO.getPointsType();
        StoreInfoResponse storeInfoResponse = null;
        if (StringUtils.isBlank(pointsType)) {
            // 20230928蛋品-赵翔宇-商家积分, 积分类型为空的话, 需要根据店铺编号去查询, 店铺为共享会员
            storeInfoResponse = pointsUtil.queryStore(reqPointsConfigDTO.getStoreNo());
            if(WhetherEnum.YES.getCode().equals(storeInfoResponse.getMemberSharing())){
                pointsType = StringNumConstant.TWO;
            } else {
                // 店铺不为共享会员
                pointsType = StringNumConstant.ONE;
            }
        }

        AtomReqPointsConfigDTO atomReqPointsConfigDTO = BeanCopierUtil.copy(reqPointsConfigDTO, AtomReqPointsConfigDTO.class);

        atomReqPointsConfigDTO.setPointsType(pointsType);

        ExecuteDTO<List<AtomResPointsConfigDTO>> executeDTO;
        // 不为共享店铺
        if (StringNumConstant.ONE.equals(pointsType)) {
            log.info("getOpenStorePointsConfig---查询店铺积分配置----{}", JSON.toJSONString(atomReqPointsConfigDTO));
            executeDTO = atomPointsConfigAnalysisService.getStorePointsConfigs(atomReqPointsConfigDTO);
        } else {
            if (StringUtils.isBlank(atomReqPointsConfigDTO.getMerchantNo())) {
                if (null != storeInfoResponse && StringUtils.isNotBlank(storeInfoResponse.getMerchantNo())) {
                    atomReqPointsConfigDTO.setMerchantNo(storeInfoResponse.getMerchantNo());
                } else {
                    // 单独查询商家编号
                    MerchantInfoResponseT merchantInfo = userInfoUtil.getMerchantInfo(atomReqPointsConfigDTO.getStoreNo());
                    atomReqPointsConfigDTO.setMerchantNo(merchantInfo.getMerchantNo());
                }
            }
            atomReqPointsConfigDTO.setStoreNo("");
            //如果是共享店铺, 则根据商家编号查询，商家积分配置
            log.info("getOpenStorePointsConfig---查询共享会员积分配置----{}", JSON.toJSONString(atomReqPointsConfigDTO));
            executeDTO = atomPointsConfigAnalysisService.getMerchantPointsConfigs(atomReqPointsConfigDTO);
        }

        log.info("PointsConfigAnalysisServiceImpl.getOpenStorePointsConfig---查询结果----{}", JSON.toJSONString(executeDTO));
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        List<AtomResPointsConfigDTO> configs = executeDTO.getData();
        if (null != configs) {
            configs = configs.stream().filter(s -> s.getDisableFlag() == 1).collect(Collectors.toList());
        }
        executeDTO.setData(configs);
        return ExecuteDTO.success(BeanCopierUtil.copyList(configs, ResPointsConfigDTO.class));
    }


    /**
     * @param reqPointsConfigDTO
     * @Description : 根据配置类型获取积分配置信息
     * <AUTHOR> 高繁
     * @date : 2021/9/28 10:30
     */
    @Override
    public ExecuteDTO<ResPointsConfigDTO> getStorePointConfigByType(ReqPointsConfigDTO reqPointsConfigDTO) {
        log.info("PointsConfigAnalysisServiceImpl.getStorePointConfigByType#param----{}", JSON.toJSONString(reqPointsConfigDTO));
        configAssert.getStorePointConfigByType(reqPointsConfigDTO);

        return null;
    }

    /**
     * @see PointsConfigAnalysisService#getPointsConfigByConfigNo(ReqPointsConfigDTO)
     */
    @Override
    public ExecuteDTO<ReqPointsConfigDTO> getPointsConfigByConfigNo(ReqPointsConfigDTO reqPointsConfigDTO) {
        log.info("PointsConfigAnalysisServiceImpl.getPointsConfigByConfigNo----param----{}", JSON.toJSONString(reqPointsConfigDTO));
        configAssert.getPointsConfigByConfigNoAssert(reqPointsConfigDTO);
        AtomReqPointsConfigDTO atomReqPointsConfigDTO = BeanCopierUtil.copy(reqPointsConfigDTO, AtomReqPointsConfigDTO.class);
        //20230928蛋品-wh-查询店铺是否是共享店铺
        if(StringUtils.isNotBlank(reqPointsConfigDTO.getStoreNo())){
            //店铺编号为空就查询门店的
            atomReqPointsConfigDTO.setPointsType(String.valueOf(NumConstant.ONE));
        }else {
            atomReqPointsConfigDTO.setPointsType(String.valueOf(NumConstant.TWO));
        }
        ExecuteDTO<AtomResPointsConfigDTO> executeDTO = atomPointsConfigAnalysisService.getPointsConfigByConfigNo(atomReqPointsConfigDTO);
        if (null == executeDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResPointsConfigDTO resPointsConfig = BeanCopierUtil.copy(executeDTO.getData(), ResPointsConfigDTO.class);
        return ExecuteDTO.success(resPointsConfig);
    }


}
