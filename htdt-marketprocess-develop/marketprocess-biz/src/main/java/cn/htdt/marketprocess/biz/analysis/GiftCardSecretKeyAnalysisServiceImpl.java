package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketprocess.api.analysis.GiftCardSecretKeyAnalysisService;
import cn.htdt.marketprocess.dao.GiftCardSecretKeyDao;
import cn.htdt.marketprocess.domain.GiftCardSecretKeyDomain;
import cn.htdt.marketprocess.dto.request.ReqGiftCardSecretKeyDTO;
import cn.htdt.marketprocess.dto.response.ResGiftCardSecretKeyDTO;
import cn.htdt.marketprocess.legacycenter.biz.operat.BaseServiceImpl;
import cn.htdt.marketprocess.vo.GiftCardSecretKeyVo;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/11 16:24
 */
@Slf4j
@DubboService
public class GiftCardSecretKeyAnalysisServiceImpl extends BaseServiceImpl implements GiftCardSecretKeyAnalysisService {

    @Resource
    private GiftCardSecretKeyDao giftCardSecretKeyDao;

    @Override
    public ExecuteDTO<ExecutePageDTO<ResGiftCardSecretKeyDTO>> getGiftCardSecretKeyPage(ReqGiftCardSecretKeyDTO reqGiftCardSecretKeyDTO) {
        log.info(String.format("礼品卡卡密分页查询入参:%s", JSON.toJSONString(reqGiftCardSecretKeyDTO)));
        Page<Object> pages = PageHelper.startPage(reqGiftCardSecretKeyDTO);
        pages.setReasonable(false);
        GiftCardSecretKeyVo giftCardSecretKeyVo = BeanCopierUtil.copy(reqGiftCardSecretKeyDTO, GiftCardSecretKeyVo.class);
        giftCardSecretKeyVo.setCardStatus(null);
        if (StringUtils.isNotBlank(reqGiftCardSecretKeyDTO.getCardStatus())) {
            // cardStatus支持逗号分隔，转成list
            List<String> cardStatusList = Arrays.stream(reqGiftCardSecretKeyDTO.getCardStatus().split(",")).distinct().collect(Collectors.toList());
            giftCardSecretKeyVo.setCardStatusList(cardStatusList);
        }
        List<GiftCardSecretKeyDomain> giftCardSecretKeyDomainList = giftCardSecretKeyDao.selectByParam(giftCardSecretKeyVo);
        log.info("礼品卡卡密列表查询结束");
        ExecutePageDTO<ResGiftCardSecretKeyDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResGiftCardSecretKeyDTO> giftCardSecretKeyDTOList = BeanCopierUtil.copyList(giftCardSecretKeyDomainList, ResGiftCardSecretKeyDTO.class);
        executePageDTO.setTotal(pages.getTotal());
        executePageDTO.setRows(giftCardSecretKeyDTOList);
        return ExecuteDTO.ok(executePageDTO);
    }

    @Override
    public ExecuteDTO<ExecutePageDTO<ResGiftCardSecretKeyDTO>> getGiftCardSendPage(ReqGiftCardSecretKeyDTO reqGiftCardSecretKeyDTO) {
        log.info(String.format("礼品卡发送分页查询入参:%s", JSON.toJSONString(reqGiftCardSecretKeyDTO)));
        Page<Object> pages = PageHelper.startPage(reqGiftCardSecretKeyDTO);
        GiftCardSecretKeyDomain giftCardSecretKeyDomain = BeanCopierUtil.copy(reqGiftCardSecretKeyDTO, GiftCardSecretKeyDomain.class);
        List<GiftCardSecretKeyDomain> giftCardSecretKeyDomainList = giftCardSecretKeyDao.selectSendListByParam(giftCardSecretKeyDomain);
        log.info("礼品卡发送列表查询结束");
        ExecutePageDTO<ResGiftCardSecretKeyDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResGiftCardSecretKeyDTO> giftCardSecretKeyDTOList = BeanCopierUtil.copyList(giftCardSecretKeyDomainList, ResGiftCardSecretKeyDTO.class);
        executePageDTO.setTotal(pages.getTotal());
        executePageDTO.setRows(giftCardSecretKeyDTOList);
        return ExecuteDTO.ok(executePageDTO);
    }

    @Override
    public ExecuteDTO<List<ResGiftCardSecretKeyDTO>> getGiftCardSecretKeyList(ReqGiftCardSecretKeyDTO reqGiftCardSecretKeyDTO) {
        log.info(String.format("礼品卡卡密列表查询入参:%s", JSON.toJSONString(reqGiftCardSecretKeyDTO)));
        GiftCardSecretKeyVo giftCardSecretKeyVo = BeanCopierUtil.copy(reqGiftCardSecretKeyDTO, GiftCardSecretKeyVo.class);
        giftCardSecretKeyVo.setCardStatus(null);
        if (StringUtils.isNotBlank(reqGiftCardSecretKeyDTO.getCardStatus())) {
            // cardStatus支持逗号分隔，转成list
            List<String> cardStatusList = Arrays.stream(reqGiftCardSecretKeyDTO.getCardStatus().split(",")).distinct().collect(Collectors.toList());
            giftCardSecretKeyVo.setCardStatusList(cardStatusList);
        }
        List<GiftCardSecretKeyDomain> giftCardSecretKeyDomainList = giftCardSecretKeyDao.selectByParam(giftCardSecretKeyVo);
        log.info("礼品卡卡密列表查询结束");
        List<ResGiftCardSecretKeyDTO> giftCardSecretKeyDTOList = BeanCopierUtil.copyList(giftCardSecretKeyDomainList, ResGiftCardSecretKeyDTO.class);
        return ExecuteDTO.ok(giftCardSecretKeyDTOList);
    }

    @Override
    public ExecuteDTO<Integer> getFansCardCount(ReqGiftCardSecretKeyDTO reqGiftCardSecretKeyDTO) {
        log.info("getFansCardCount入参:{}", JSON.toJSONString(reqGiftCardSecretKeyDTO));
        GiftCardSecretKeyVo queryVo = new GiftCardSecretKeyVo();
        queryVo.setDsPhone(reqGiftCardSecretKeyDTO.getDsPhone());
        queryVo.setCardStatusList(Arrays.asList(reqGiftCardSecretKeyDTO.getCardStatus().split(",")));
        queryVo.setMerchantNo(reqGiftCardSecretKeyDTO.getMerchantNo());
        queryVo.setStoreNo(reqGiftCardSecretKeyDTO.getStoreNo());
        Integer integer = giftCardSecretKeyDao.getFansCardCount(queryVo);
        log.info("getFansCardCount入参:{},resp:{}", JSON.toJSONString(reqGiftCardSecretKeyDTO), integer);
        return ExecuteDTO.ok(integer);
    }

    @Override
    public ExecuteDTO<Integer> getCardCountByPromotionNo(ReqGiftCardSecretKeyDTO reqGiftCardSecretKeyDTO) {
        log.info("getCardCountByPromotionNo:{}", JSON.toJSONString(reqGiftCardSecretKeyDTO));
        QueryWrapper<GiftCardSecretKeyDomain> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("delete_flag", WhetherEnum.NO.getCode());
        queryWrapper.eq("promotion_no", reqGiftCardSecretKeyDTO.getPromotionNo());
        // cardStatus支持逗号分隔,如果是!开头则取反
        if (StringUtils.isNotBlank(reqGiftCardSecretKeyDTO.getCardStatus()) && reqGiftCardSecretKeyDTO.getCardStatus().startsWith("!")) {
            queryWrapper.notIn("card_status", Arrays.asList(reqGiftCardSecretKeyDTO.getCardStatus().substring(1).split(",")));
        } else if (StringUtils.isNotBlank(reqGiftCardSecretKeyDTO.getCardStatus())) {
            queryWrapper.in("card_status", Arrays.asList(reqGiftCardSecretKeyDTO.getCardStatus().split(",")));
        }
        Integer integer = giftCardSecretKeyDao.selectCount(queryWrapper);
        log.info("getCardCountByPromotionNo:{},resp:{}", JSON.toJSONString(reqGiftCardSecretKeyDTO), integer);
        return ExecuteDTO.ok(integer);
    }

    @Override
    public ExecuteDTO<Integer> getCardUsedCountByPromotionNo(ReqGiftCardSecretKeyDTO reqGiftCardSecretKeyDTO) {
        log.info("getCardUsedCountByPromotionNo:{}", JSON.toJSONString(reqGiftCardSecretKeyDTO));
        QueryWrapper<GiftCardSecretKeyDomain> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("delete_flag", WhetherEnum.NO.getCode());
        queryWrapper.eq("promotion_no", reqGiftCardSecretKeyDTO.getPromotionNo());
        queryWrapper.isNotNull("order_no");
        queryWrapper.ne("order_no", "");
        Integer integer = giftCardSecretKeyDao.selectCount(queryWrapper);
        log.info("getCardUsedCountByPromotionNo:{},resp:{}", JSON.toJSONString(reqGiftCardSecretKeyDTO), integer);
        return ExecuteDTO.ok(integer);
    }

}
