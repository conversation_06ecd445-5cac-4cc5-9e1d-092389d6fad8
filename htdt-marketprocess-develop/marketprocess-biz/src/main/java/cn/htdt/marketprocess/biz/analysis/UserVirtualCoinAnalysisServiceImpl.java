package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.base.exception.BaseException;
import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.enums.HxgLogoffErrorEnum;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.market.VirtualCoinRuleTypeEnum;
import cn.htdt.common.enums.market.VirtualCoinTradeTypeEnum;
import cn.htdt.common.enums.user.DeleteFlagEnum;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.CollectionUtils;
import cn.htdt.marketcenter.dto.request.AtomReqUserVirtualCoinDTO;
import cn.htdt.marketcenter.dto.response.AtomResUserVirtualCoinDTO;
import cn.htdt.marketprocess.api.analysis.UserVirtualCoinAnalysisService;
import cn.htdt.marketprocess.biz.conversion.VirtualCoinRecordAssert;
import cn.htdt.marketprocess.biz.utils.VirtualCoinUtil;
import cn.htdt.marketprocess.dao.UserVirtualCoinDao;
import cn.htdt.marketprocess.dao.VirtualCoinRecordDao;
import cn.htdt.marketprocess.domain.UserVirtualCoinDomain;
import cn.htdt.marketprocess.dto.request.ReqUserVirtualCoinAndRecordDTO;
import cn.htdt.marketprocess.dto.request.ReqUserVirtualCoinDTO;
import cn.htdt.marketprocess.dto.response.ResUserVirtualCoinAndLatestRecordDTO;
import cn.htdt.marketprocess.dto.response.ResUserVirtualCoinDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomUserVirtualCoinAnalysisService;
import cn.htdt.marketprocess.vo.ReqVirtualCoinAndLatestRecordVo;
import cn.htdt.marketprocess.vo.ResVirtualCoinLatestRecordVo;
import cn.htdt.marketprocess.vo.UserVirtualCoinVo;
import cn.htdt.usercenter.dto.request.ReqFancDTO;
import cn.htdt.usercenter.dto.response.ResFancDTO;
import cn.htdt.userprocess.api.LegacyUserCenterService;
import cn.htdt.userprocess.api.UcFansProcessService;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/1/26
 */
@Slf4j
@DubboService
public class UserVirtualCoinAnalysisServiceImpl implements UserVirtualCoinAnalysisService {
//    @DubboReference
//    private UcFansProcessService ucFansService;
    @Resource
    private AtomUserVirtualCoinAnalysisService userVirtualCoinAnalysisService;

    @DubboReference
    private LegacyUserCenterService legacyUserCenterService;

    @DubboReference
    private UcFansProcessService ucFansProcessService;

    @Resource
    private UserVirtualCoinDao userVirtualCoinDao;

    @Resource
    private VirtualCoinRecordDao virtualCoinRecordDao;

    @Resource
    private VirtualCoinUtil virtualCoinUtil;

    @Resource
    private VirtualCoinRecordAssert virtualCoinRecordAssert;

    /**
     * 查询用户橙豆列表
     *
     * @param reqUserVirtualCoinDTO
     * @return
     */
    @Override
    public ExecuteDTO<ResUserVirtualCoinDTO> selectUserVirtualCoinList(ReqUserVirtualCoinDTO reqUserVirtualCoinDTO) {
        log.info("-UserVirtualCoinAnalysisServiceImpl-selectUserVirtualCoinList---start--param={}", JSON.toJSONString(reqUserVirtualCoinDTO));
//        ReqFancDTO reqFancDTO = new ReqFancDTO();
//        reqFancDTO.setPhone(reqUserVirtualCoinDTO.getMobile());
//        reqFancDTO.setFanNo(reqUserVirtualCoinDTO.getFanNo());
//        reqFancDTO.setStoreNo(reqUserVirtualCoinDTO.getStoreNo());
//        ExecuteDTO<ResFancDTO> fansInfoByPhone = ucFansService.getFansInfoByPhone(reqFancDTO);
//        if (!fansInfoByPhone.successFlag() || StringUtils.isEmpty(fansInfoByPhone.getData())){
//            throw new BaseException(CommonCode.CODE_10000002,"粉丝信息");
//        }
        // 根据粉丝编号查询
        ReqFancDTO reqFancDTO = new ReqFancDTO();
        reqFancDTO.setFanNo(reqUserVirtualCoinDTO.getFanNo());
        ExecuteDTO<ResFancDTO> fancInfoByFanNo = legacyUserCenterService.getFancInfoByFanNo(reqFancDTO);
        if (!fancInfoByFanNo.successFlag() || null == fancInfoByFanNo.getData()) {
            throw new BaseException(CommonCode.CODE_10000002, "粉丝信息");
        }

        AtomReqUserVirtualCoinDTO virtualCoinDTO = new AtomReqUserVirtualCoinDTO();
        virtualCoinDTO.setFanNo(fancInfoByFanNo.getData().getFanNo());

        String virtualCoinRuleType = virtualCoinUtil.getVirtualCoinRuleType(reqUserVirtualCoinDTO.getStoreNo(), reqUserVirtualCoinDTO.getMerchantNo(), null);
        log.info("selectUserVirtualCoinList--->virtualCoinRuleType: {}", virtualCoinRuleType);

        // ********蛋品-赵翔宇-商家橙豆-粉丝橙豆, 店铺未加入共享店铺
        if (VirtualCoinRuleTypeEnum.STORE_RULE.getCode().equals(virtualCoinRuleType)) {
            virtualCoinDTO.setStoreNo(reqUserVirtualCoinDTO.getStoreNo());
        } else {
            // ********蛋品-赵翔宇-商家橙豆-粉丝橙豆
            virtualCoinDTO.setMerchantNo(reqUserVirtualCoinDTO.getMerchantNo());
        }
        virtualCoinDTO.setRuleType(virtualCoinRuleType);

        ExecuteDTO<List<AtomResUserVirtualCoinDTO>> listExecuteDTO =
                userVirtualCoinAnalysisService.selectUserVirtualCoinList(virtualCoinDTO);
        if (!listExecuteDTO.successFlag()){
            throw new BaseException(CommonCode.CODE_10000002,"粉丝橙豆");
        }
        ResUserVirtualCoinDTO resUserVirtualCoinDTO = new ResUserVirtualCoinDTO();
        if (CollectionUtils.isEmpty(listExecuteDTO.getData())){
            return ExecuteDTO.success(resUserVirtualCoinDTO);
        }
        AtomResUserVirtualCoinDTO atomResUserVirtualCoinDTO = listExecuteDTO.getData().get(NumConstant.ZERO);
        resUserVirtualCoinDTO = BeanCopierUtil.copy(atomResUserVirtualCoinDTO, ResUserVirtualCoinDTO.class);
        resUserVirtualCoinDTO.setFansName(fancInfoByFanNo.getData().getStoreFanName());
        log.info("-UserVirtualCoinAnalysisServiceImpl-selectUserVirtualCoinList---end--param={}", JSON.toJSONString(reqUserVirtualCoinDTO));
        return ExecuteDTO.success(resUserVirtualCoinDTO);
    }

    /**
     * ********蛋品-赵翔宇-商家橙豆, 查询指定粉丝的粉丝橙豆信息
     *
     * @param reqUserVirtualCoinDTO 查询参数
     * @return 粉丝橙豆信息
     */
    @Override
    public ExecuteDTO<ResUserVirtualCoinDTO> selectUserVirtualCoin(ReqUserVirtualCoinDTO reqUserVirtualCoinDTO) {
        log.info("selectUserVirtualCoin--->查询粉丝橙豆余额: {}", JSON.toJSONString(reqUserVirtualCoinDTO));
        virtualCoinRecordAssert.selectUserVirtualCoinAssert(reqUserVirtualCoinDTO);

        String virtualCoinRuleType = virtualCoinUtil.getVirtualCoinRuleType(reqUserVirtualCoinDTO.getStoreNo(), reqUserVirtualCoinDTO.getMerchantNo(), null);
        log.info("selectUserVirtualCoin--->查询粉丝橙豆余额, virtualCoinRuleType: {}", virtualCoinRuleType);

        // ********蛋品-赵翔宇-商家橙豆, 查询粉丝橙豆, 需要判断该店铺是否加入共享店铺
        UserVirtualCoinDomain queryVirtualCoinDomain = BeanCopierUtil.copy(reqUserVirtualCoinDTO, UserVirtualCoinDomain.class);
        // 店铺未加入共享店铺
        if (VirtualCoinRuleTypeEnum.STORE_RULE.getCode().equals(virtualCoinRuleType)) {
            queryVirtualCoinDomain.setStoreNo(reqUserVirtualCoinDTO.getStoreNo());
        } else {
            // 店铺未加入共享店铺, 需要根据商家编号去查询
            queryVirtualCoinDomain.setMerchantNo(reqUserVirtualCoinDTO.getMerchantNo());
        }
        queryVirtualCoinDomain.setRuleType(virtualCoinRuleType);
        queryVirtualCoinDomain.setDeleteFlag(WhetherEnum.NO.getCode());
        // 粉丝剩余橙豆数量和粉丝编号
        log.info("selectUserVirtualCoin--->查询粉丝橙豆余额, 查询入参: {}", JSON.toJSONString(queryVirtualCoinDomain));
        UserVirtualCoinDomain userVirtualCoinResult = userVirtualCoinDao.getUserVirtualCoin(queryVirtualCoinDomain);
        if (null == userVirtualCoinResult) {
            return ExecuteDTO.ok();
        }

        ResUserVirtualCoinDTO resUserVirtualCoinDTO = BeanCopierUtil.copy(userVirtualCoinResult, ResUserVirtualCoinDTO.class);

        return ExecuteDTO.ok(resUserVirtualCoinDTO);
    }

    /**
     * 粉丝注销校验账户存在未使用的橙豆
     *
     * @param fanNo 粉丝编号
     * @return ExecuteDTO
     */
    @Override
    public ExecuteDTO<Integer> checkVirtualCoin(String fanNo) {
        AtomReqUserVirtualCoinDTO reqUserVirtualCoinDTO = new AtomReqUserVirtualCoinDTO();
        reqUserVirtualCoinDTO.setFanNo(fanNo);
        reqUserVirtualCoinDTO.setDeleteFlag(DeleteFlagEnum.NO.getCode());
        ExecuteDTO<List<AtomResUserVirtualCoinDTO>> listExecuteDTO = userVirtualCoinAnalysisService.selectUserVirtualCoinList(reqUserVirtualCoinDTO);
        if (null != listExecuteDTO && CollectionUtils.isNotEmpty(listExecuteDTO.getData())) {
            List<AtomResUserVirtualCoinDTO> rows = listExecuteDTO.getData().stream()
                    .filter(p -> null != p.getAccountRemainCoins() && p.getAccountRemainCoins().compareTo(BigDecimal.ZERO) > NumConstant.ZERO)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(rows)) {
                Collections.sort(rows, Comparator.comparing(AtomResUserVirtualCoinDTO::getAccountRemainCoins).reversed());
                AtomResUserVirtualCoinDTO virtualCoinDTO = rows.get(NumConstant.ZERO);
                String msg = String.format(HxgLogoffErrorEnum.UNUSED_VIRTUAL_COIN.getMsg(), virtualCoinDTO.getStoreName(), virtualCoinDTO.getAccountRemainCoins());
                return ExecuteDTO.error(HxgLogoffErrorEnum.UNUSED_VIRTUAL_COIN.getCode(), msg);
            }
        }

        return ExecuteDTO.ok(NumConstant.ZERO);
    }

    /**
     * 千橙掌柜智能收银 -> 全部功能 -> 橙豆管理-> 橙豆列表
     * @param reqUserVirtualCoinAndRecordDTO 请求参数
     * @return 响应参数
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResUserVirtualCoinAndLatestRecordDTO>> getStoreFansVirtualCoinAndLatestRecord(ReqUserVirtualCoinAndRecordDTO reqUserVirtualCoinAndRecordDTO) {
        log.info("-UserVirtualCoinAnalysisServiceImpl---->getStoreFansVirtualCoinAndLatestRecordData---start--param={}", JSON.toJSONString(reqUserVirtualCoinAndRecordDTO));
        virtualCoinRecordAssert.virtualCoinAndRecordAssert(reqUserVirtualCoinAndRecordDTO);

        List<String> fanNoList = Collections.emptyList();
        List<ResFancDTO> fansDTOList;

        // ********蛋品-赵翔宇-商家橙豆-橙豆流水, 商家可以查看共享店铺的橙豆流水, 若会员店A为共享会员的店铺，之后商家将其从共享会员店铺删除后，商家平台还是可以看到此店铺发生的历史商家橙豆流水数据
        String virtualCoinRuleType = virtualCoinUtil.getVirtualCoinRuleType(reqUserVirtualCoinAndRecordDTO.getStoreNo(),
                                                                                reqUserVirtualCoinAndRecordDTO.getMerchantNo(),
                                                                                reqUserVirtualCoinAndRecordDTO.getLoginIdentity());

        if (StringUtils.isNotBlank(reqUserVirtualCoinAndRecordDTO.getMobile()) || StringUtils.isNotBlank(reqUserVirtualCoinAndRecordDTO.getStoreFanName())){
            log.info("-UserVirtualCoinAnalysisServiceImpl-getStoreFansVirtualCoinAndLatestRecordData---get fanInfo by mobile or storeFanName");
            // 查询手机号或者店铺粉丝备注名, 查询粉丝信息
            ReqFancDTO reqFanDTO = new ReqFancDTO();
            reqFanDTO.setPhone(reqUserVirtualCoinAndRecordDTO.getMobile());
            reqFanDTO.setMerchantNo(reqUserVirtualCoinAndRecordDTO.getMerchantNo());
            ExecuteDTO<List<ResFancDTO>> executeDTO;

            // ********蛋品-赵翔宇-商家橙豆-粉丝橙豆, 该店铺不为共享店铺
            if (VirtualCoinRuleTypeEnum.STORE_RULE.getCode().equals(virtualCoinRuleType)) {
                reqFanDTO.setStoreFanName(reqUserVirtualCoinAndRecordDTO.getStoreFanName());
                reqFanDTO.setStoreNo(reqUserVirtualCoinAndRecordDTO.getStoreNo());
                executeDTO = legacyUserCenterService.getFanBySelect(reqFanDTO);
            } else {
                // ********蛋品-赵翔宇-商家橙豆-粉丝橙豆, 店铺加入共享店铺等同于商家身份查询粉丝信息
                executeDTO = ucFansProcessService.getMerchantFanInfo(reqFanDTO);
            }

            if (null == executeDTO) {
                log.info("-UserVirtualCoinAnalysisServiceImpl---->getStoreFansVirtualCoinAndLatestRecordData---getFanBySelect response data is null");
                return ExecuteDTO.error(CommonCode.CODE_10000001, "查询粉丝信息异常");
            }

            if (!executeDTO.successFlag()){
                log.info("-UserVirtualCoinAnalysisServiceImpl---->getStoreFansVirtualCoinAndLatestRecordData---getFanBySelect fail");
                return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
            }
            fansDTOList = executeDTO.getData();
            if (CollectionUtils.isNotEmpty(fansDTOList)) {
                fanNoList = fansDTOList.stream().map(ResFancDTO::getFanNo).distinct().collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(fanNoList)){
                log.info("-UserVirtualCoinAnalysisServiceImpl---->getStoreFansVirtualCoinAndLatestRecordData---getFanBySelect, result is null");
                ExecutePageDTO<ResUserVirtualCoinAndLatestRecordDTO> executePageDTO = new ExecutePageDTO<>();
                executePageDTO.setTotal(NumConstant.ZERO);
                executePageDTO.setRows(Collections.emptyList());
                return ExecuteDTO.ok(executePageDTO);
            }

        }

        // 先查询店铺下的粉丝编号和橙豆余额
        Page<Object> page = PageHelper.startPage(reqUserVirtualCoinAndRecordDTO);
        ReqVirtualCoinAndLatestRecordVo reqVirtualCoinVo = BeanCopierUtil.copy(reqUserVirtualCoinAndRecordDTO, ReqVirtualCoinAndLatestRecordVo.class);

        // ********蛋品-赵翔宇-商家橙豆-粉丝橙豆, 店铺未加入共享店铺
        if (VirtualCoinRuleTypeEnum.STORE_RULE.getCode().equals(virtualCoinRuleType)) {
            reqVirtualCoinVo.setStoreNo(reqUserVirtualCoinAndRecordDTO.getStoreNo());
            reqVirtualCoinVo.setMerchantNo(reqUserVirtualCoinAndRecordDTO.getMerchantNo());
        } else {
            // ********蛋品-赵翔宇-商家橙豆-粉丝橙豆
            reqVirtualCoinVo.setMerchantNo(reqUserVirtualCoinAndRecordDTO.getMerchantNo());
            reqVirtualCoinVo.setStoreNo("");
        }
        reqVirtualCoinVo.setRuleType(virtualCoinRuleType);

        reqVirtualCoinVo.setDeleteFlag(WhetherEnum.NO.getCode());
        if (CollectionUtils.isNotEmpty(fanNoList)) {
            reqVirtualCoinVo.setFanNoList(fanNoList);
        }
        // 粉丝剩余橙豆数量和粉丝编号
        List<UserVirtualCoinDomain> userVirtualCoinResult = userVirtualCoinDao.getStoreFansVirtualCoinList(reqVirtualCoinVo);
        if (CollectionUtils.isEmpty(userVirtualCoinResult)) {
            log.error("-UserVirtualCoinAnalysisServiceImpl-getStoreFansVirtualCoinAndLatestRecordData, userVirtualCoinResult is empty, storeNo: {}", reqUserVirtualCoinAndRecordDTO.getStoreNo());
            ExecutePageDTO<ResUserVirtualCoinAndLatestRecordDTO> executePageDTO = new ExecutePageDTO<>();
            executePageDTO.setTotal(NumConstant.ZERO);
            executePageDTO.setRows(Collections.emptyList());
            return ExecuteDTO.ok(executePageDTO);
        }

        log.info("-UserVirtualCoinAnalysisServiceImpl-getStoreFansVirtualCoinAndLatestRecordData, userVirtualCoinResult: {}", userVirtualCoinResult);

        // 粉丝编号集合, 以用户橙豆表的粉丝集合为准
        fanNoList = userVirtualCoinResult.stream().map(UserVirtualCoinDomain::getFanNo).collect(Collectors.toList());

        // 返回的数据
        List<ResUserVirtualCoinAndLatestRecordDTO> userVirtualCoinAndLatestRecord = BeanCopierUtil.copyList(userVirtualCoinResult, ResUserVirtualCoinAndLatestRecordDTO.class);
        log.info("-UserVirtualCoinAnalysisServiceImpl-getStoreFansVirtualCoinAndLatestRecordData, userVirtualCoinAndLatestRecord: {}", userVirtualCoinAndLatestRecord);

        // 粉丝编号集合
        ReqVirtualCoinAndLatestRecordVo reqVirtualCoinAndLatestRecordVo = new ReqVirtualCoinAndLatestRecordVo();
        reqVirtualCoinAndLatestRecordVo.setRuleType(virtualCoinRuleType);
        if (CollectionUtils.isNotEmpty(fanNoList)) {
            reqVirtualCoinAndLatestRecordVo.setFanNoList(fanNoList);
        }

        // 查询交易类型为充值和消费的记录
        List<String> tradeTypeList = new ArrayList<>();
        tradeTypeList.add(VirtualCoinTradeTypeEnum.RECHARGE.getCode());
        tradeTypeList.add(VirtualCoinTradeTypeEnum.ORDER_CONSUME.getCode());
        reqVirtualCoinAndLatestRecordVo.setTradeTypeList(tradeTypeList);

        // 粉丝在店铺或者共享店铺下, 最近充值和消费橙豆的信息
        List<ResVirtualCoinLatestRecordVo> latestVirtualCoinRecordData;
        // ********蛋品-赵翔宇-商家橙豆-粉丝橙豆, 店铺未加入共享店铺
        if (VirtualCoinRuleTypeEnum.STORE_RULE.getCode().equals(virtualCoinRuleType)) {
            reqVirtualCoinAndLatestRecordVo.setStoreNo(reqUserVirtualCoinAndRecordDTO.getStoreNo());
            // 查询店铺粉丝上次充值时间, 上次充值时间, 上次订单支付时间, 上次消费橙豆数额
            latestVirtualCoinRecordData = virtualCoinRecordDao.getLatestVirtualCoinRecordData(reqVirtualCoinAndLatestRecordVo);
        } else {
            // ********蛋品-赵翔宇-商家橙豆-粉丝橙豆, 店铺加入共享店铺后, 或者商家查询粉丝橙豆充值和消费信息
            reqVirtualCoinAndLatestRecordVo.setMerchantNo(reqUserVirtualCoinAndRecordDTO.getMerchantNo());
            latestVirtualCoinRecordData = virtualCoinRecordDao.getMerchantLatestVirtualCoinRecordData(reqVirtualCoinAndLatestRecordVo);
        }

        log.info("-UserVirtualCoinAnalysisServiceImpl-getStoreFansVirtualCoinAndLatestRecordData, latestVirtualCoinRecordData: {}", latestVirtualCoinRecordData);
        List<ResFancDTO> fansInfoList = Collections.emptyList();

        ReqFancDTO reqFancDTO = new ReqFancDTO();
        reqFancDTO.setFansNoList(fanNoList);
        ExecuteDTO<List<ResFancDTO>> fansInfoExecuteDto;

        // ********蛋品-赵翔宇-商家橙豆-粉丝橙豆, 只有店铺未加入共享店铺, 使用的是店铺自己的橙豆规则, 完善粉丝手机号和粉丝备注, 店铺加入共享店铺后, 需要以商家的维度去查询粉丝信息
        if (VirtualCoinRuleTypeEnum.STORE_RULE.getCode().equals(virtualCoinRuleType)) {
            reqFancDTO.setStoreNo(reqUserVirtualCoinAndRecordDTO.getStoreNo());
            fansInfoExecuteDto = legacyUserCenterService.getFansInfoByNos(reqFancDTO);
        } else {
            reqFancDTO.setMerchantNo(reqUserVirtualCoinAndRecordDTO.getMerchantNo());
            fansInfoExecuteDto = ucFansProcessService.getMerchantFanInfo(reqFancDTO);
        }

        if (fansInfoExecuteDto.successFlag() && CollectionUtils.isNotEmpty(fansInfoExecuteDto.getData())) {
            fansInfoList = fansInfoExecuteDto.getData();
        }

        for (ResUserVirtualCoinAndLatestRecordDTO userVirtualCoinDTO : userVirtualCoinAndLatestRecord) {
            Optional<ResVirtualCoinLatestRecordVo> latestRecordVoOp = latestVirtualCoinRecordData.stream().
                    filter(resVirtualCoinAndLatestRecordVo -> userVirtualCoinDTO.getFanNo()
                            .equals(resVirtualCoinAndLatestRecordVo.getFanNo())).findFirst();

            // 设置充值、消费橙豆数额，以及充值消费时间
            if (latestRecordVoOp.isPresent()) {
                ResVirtualCoinLatestRecordVo latestRecordVo = latestRecordVoOp.get();
                BeanUtils.copyProperties(latestRecordVo, userVirtualCoinDTO);
            }

            // 设置店铺名以及手机号
            Optional<ResFancDTO> fanInfoOptional = fansInfoList.stream().filter(resFanDTO -> resFanDTO.getFanNo().equals(userVirtualCoinDTO.getFanNo())).findFirst();
            if (fanInfoOptional.isPresent()) {
                ResFancDTO resFancDTO = fanInfoOptional.get();
                userVirtualCoinDTO.setStoreFanName(resFancDTO.getStoreFanName());
                userVirtualCoinDTO.setPhone(resFancDTO.getPhone());
                userVirtualCoinDTO.setDsPhone(resFancDTO.getDsPhone());
            }
        }

        if (CollectionUtils.isNotEmpty(userVirtualCoinAndLatestRecord)) {
            // 手机号为空的数据
            List<ResUserVirtualCoinAndLatestRecordDTO> blankPhoneFanInfoList =
                        userVirtualCoinAndLatestRecord.stream()
                                .filter(virtualCoinAndLatestRecord -> StringUtils.isBlank(virtualCoinAndLatestRecord.getPhone()))
                                .collect(Collectors.toList());

            // ********单品-商家储值-粉丝橙豆, 店铺退出共享店铺, 结果里粉丝手机号会为空, 根据粉丝编号再去查询一边
            if (CollectionUtils.isNotEmpty(blankPhoneFanInfoList)) {
                List<String> blankPhoneFanNoList = blankPhoneFanInfoList
                                                        .stream()
                                                        .map(ResUserVirtualCoinAndLatestRecordDTO::getFanNo)
                                                        .distinct()
                                                        .collect(Collectors.toList());

                reqFancDTO = new ReqFancDTO();
                reqFancDTO.setFansNoList(blankPhoneFanNoList);
                log.info("getStoreFansVirtualCoinAndLatestRecordData--->get fanPhone info, reqFancDTO: {}", JSON.toJSONString(reqFancDTO));
                ExecuteDTO<List<ResFancDTO>> fanPhoneInfoExecute = ucFansProcessService.selectFanPhoneInfo(reqFancDTO);
                log.info("getStoreFansVirtualCoinAndLatestRecordData--->get fanPhone info, fanPhoneInfoExecute: {}", JSON.toJSONString(fanPhoneInfoExecute));
                if (null != fanPhoneInfoExecute && fanPhoneInfoExecute.successFlag() && CollectionUtils.isNotEmpty(fanPhoneInfoExecute.getData())) {
                    List<ResFancDTO> phoneInfoList = fanPhoneInfoExecute.getData();
                    for (ResUserVirtualCoinAndLatestRecordDTO resUserVirtualCoinAndLatestRecordDTO : blankPhoneFanInfoList) {
                        Optional<ResFancDTO> fanPhoneInfoOp = phoneInfoList.stream().
                                filter(fanPhoneInfo -> fanPhoneInfo.getFanNo()
                                        .equals(resUserVirtualCoinAndLatestRecordDTO.getFanNo())).findFirst();

                        // 设置手机号
                        if (fanPhoneInfoOp.isPresent()) {
                            ResFancDTO fanPhoneInfo = fanPhoneInfoOp.get();
                            resUserVirtualCoinAndLatestRecordDTO.setPhone(fanPhoneInfo.getPhone());
                            resUserVirtualCoinAndLatestRecordDTO.setDsPhone(fanPhoneInfo.getDsPhone());
                        }
                    }

                }
            }
        }

        ExecutePageDTO<ResUserVirtualCoinAndLatestRecordDTO> executePageDTO = new ExecutePageDTO<>();
        executePageDTO.setTotal(page.getTotal());
        executePageDTO.setRows(userVirtualCoinAndLatestRecord);
        return ExecuteDTO.ok(executePageDTO);
    }

    @Override
    public ExecuteDTO<List<String>> getHasVirtualCoinStoreNoList(ReqUserVirtualCoinDTO reqUserVirtualCoinDTO) {
        virtualCoinRecordAssert.getHasVirtualCoinStoreNoListAssert(reqUserVirtualCoinDTO);

        UserVirtualCoinVo userVirtualCoinVo = new UserVirtualCoinVo();
        userVirtualCoinVo.setFanNoList(reqUserVirtualCoinDTO.getFanNoList());
        userVirtualCoinVo.setStoreNoList(reqUserVirtualCoinDTO.getStoreNoList());

        log.info("getHasVirtualCoinStoreNoList--->查询入参: {}", JSON.toJSONString(userVirtualCoinVo));
        List<String> list = userVirtualCoinDao.selectHasVirtualCoinStoreNoList(userVirtualCoinVo);
        return ExecuteDTO.ok(list);
    }

    /**
     * ********蛋品-赵翔宇-商家橙豆, 查询商家下商家橙豆大于0的粉丝数量
     *
     * @param reqUserVirtualCoinDTO 查询参数
     * @return 粉丝数量
     */
    @Override
    public ExecuteDTO<Integer> getMerchantHasVirtualCoinFanCount(ReqUserVirtualCoinDTO reqUserVirtualCoinDTO) {
        virtualCoinRecordAssert.getMerchantHasVirtualCoinFanCountAssert(reqUserVirtualCoinDTO);

        UserVirtualCoinVo userVirtualCoinVo = BeanCopierUtil.copy(reqUserVirtualCoinDTO, UserVirtualCoinVo.class);

        userVirtualCoinVo.setRuleType(VirtualCoinRuleTypeEnum.MEMBER_SHARING_RULE.getCode());
        int merchantHasVirtualCoinFanCount = userVirtualCoinDao.getMerchantHasVirtualCoinFanCount(userVirtualCoinVo);
        return ExecuteDTO.ok(merchantHasVirtualCoinFanCount);
    }


}
