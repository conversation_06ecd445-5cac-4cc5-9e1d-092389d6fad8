package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.market.StoreTypeEnum;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.response.AtomResPromotionStoreRuleDTO;
import cn.htdt.marketprocess.api.analysis.EnrollRuleAnalysisService;
import cn.htdt.marketprocess.api.analysis.PromotionStoreRuleAnalysisService;
import cn.htdt.marketprocess.biz.conversion.PromotionStoreRuleAssert;
import cn.htdt.marketprocess.dto.request.ReqEnrollRuleListDTO;
import cn.htdt.marketprocess.dto.request.ReqPromotionInfoDTO;
import cn.htdt.marketprocess.dto.response.ResEnrollRuleListDTO;
import cn.htdt.marketprocess.dto.response.ResPromotionStoreRuleDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomPromotionStoreRuleAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;


/**
 * 活动店铺规则查询服务
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
@Slf4j
@DubboService
public class PromotionStoreRuleAnalysisServiceImpl implements PromotionStoreRuleAnalysisService {

    @Resource
    private AtomPromotionStoreRuleAnalysisService promotionStoreRuleAnalysisService;

    @Autowired
    private PromotionStoreRuleAssert promotionStoreRuleAssert;

    @Autowired
    private EnrollRuleAnalysisService enrollRuleAnalysisService;

    /**
     * 查询促销活动设置的参与店铺信息
     *
     * @param reqPromotionInfoDTO
     * @return
     * <AUTHOR>
     * @date 2021-07-05
     */
    @Override
    public ExecuteDTO<ResPromotionStoreRuleDTO> getPromotionStoreInfo(ReqPromotionInfoDTO reqPromotionInfoDTO) {
        log.info("**PromotionStoreRuleAnalysisServiceImpl.getPromotionStoreInfo-查询促销活动设置的参与店铺信息-start**");
        log.info("**PromotionStoreRuleAnalysisServiceImpl.getPromotionStoreInfo-入参：{}", reqPromotionInfoDTO);
        // 入参校验
        promotionStoreRuleAssert.getPromotionStoreInfoAssert(reqPromotionInfoDTO);

        // 查询促销活动设置的参与店铺的规则信息
        ExecuteDTO<AtomResPromotionStoreRuleDTO> storeRuleAndDetail = promotionStoreRuleAnalysisService.selectPromotionStoreRuleByPromotionNo(reqPromotionInfoDTO.getPromotionNo());
        if (null == storeRuleAndDetail) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!storeRuleAndDetail.successFlag()) {
            return ExecuteDTO.error(storeRuleAndDetail.getStatus(), storeRuleAndDetail.getMsg());
        }
        if (null == storeRuleAndDetail.getData()) {
            return ExecuteDTO.success();
        }

        // 出参转换
        ResPromotionStoreRuleDTO resPromotionStoreRuleDTO = BeanCopierUtil.copy(storeRuleAndDetail.getData(), ResPromotionStoreRuleDTO.class);

        // 如果设置的店铺规则是报名参与，则查询出报名的详细记录信息
        if (StoreTypeEnum.STORE_TYPE_TWO.getCode().equals(resPromotionStoreRuleDTO.getStoreType())
                && StringUtils.isNotBlank(resPromotionStoreRuleDTO.getEnrollNo())) {
            ReqEnrollRuleListDTO reqEnrollRuleListDTO = new ReqEnrollRuleListDTO();
            reqEnrollRuleListDTO.setPromotionNo(resPromotionStoreRuleDTO.getEnrollNo());
            ExecuteDTO<ExecutePageDTO<ResEnrollRuleListDTO>> enrollPromotionList = enrollRuleAnalysisService.getEnrollPromotionList(reqEnrollRuleListDTO);
            if (null == enrollPromotionList) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            if (!enrollPromotionList.successFlag()) {
                return ExecuteDTO.error(enrollPromotionList.getStatus(), enrollPromotionList.getMsg());
            }
            if (null != enrollPromotionList.getData() && CollectionUtils.isNotEmpty(enrollPromotionList.getData().getRows())) {
                // 一个促销活动只能设置一个店铺报名活动
                resPromotionStoreRuleDTO.setEnrollRuleListDTO(BeanCopierUtil.copy(enrollPromotionList.getData().getRows().get(0), ResEnrollRuleListDTO.class));
            }
        }

        log.info("**PromotionStoreRuleAnalysisServiceImpl.getPromotionStoreInfo-查询促销活动设置的参与店铺信息-end**");

        // 返回结果
        return ExecuteDTO.success(resPromotionStoreRuleDTO);
    }

}
