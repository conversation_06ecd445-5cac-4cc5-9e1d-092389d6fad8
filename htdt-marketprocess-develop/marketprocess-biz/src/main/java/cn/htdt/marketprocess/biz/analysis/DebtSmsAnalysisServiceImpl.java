package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.base.exception.BaseException;
import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.enums.DecipherScene;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.market.SmsTypeEnum;
import cn.htdt.common.utils.encry.EncryptUtil;
import cn.htdt.common.utils.encry.dto.EncryptDecryptRecordDTO;
import cn.htdt.marketprocess.dto.request.ReqDebtSmsDTO;
import cn.htdt.marketcenter.dto.response.ResDebtSmsDTO;
import cn.htdt.marketprocess.api.analysis.DebtSmsAnalysisService;
import cn.htdt.marketprocess.dao.SmsSendConfigDao;
import cn.htdt.marketprocess.domain.SmsSendConfigDomain;
import cn.htdt.orderprocess.api.OrderAnalysisService;
import cn.htdt.orderprocess.dto.request.ReqOrderDTO;
import cn.htdt.orderprocess.dto.request.ResArrearsOrderCountDTO;
import cn.htdt.userprocess.api.StoreProcessService;
import cn.htdt.userprocess.dto.request.user.ReqStoresDTO;
import cn.htdt.userprocess.dto.response.StoreInfoResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * 详细说明.
 * <p>
 * Copyright: Copyright (c) 2023-05-29 19:16
 * <p>
 * Company: htd
 * <p>
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@DubboService
@RefreshScope
public class DebtSmsAnalysisServiceImpl implements DebtSmsAnalysisService {


    @DubboReference
    private OrderAnalysisService orderAnalysisService;

    @Resource
    private SmsSendConfigDao smsSendConfigDao ;

    @Resource
    private EncryptUtil encryptUtil;

    @DubboReference
    private StoreProcessService storeProcessService;

    @Value("${debt-remind.amount:5000}")
    private String debtRemindAmount;

    @Value("${debt-remind.sms-content-amount:截止目前，您在%s的欠款金额有%s元，请及时到店还款或联系店铺说明情况（联系电话：%s），如已还款请忽略}")
    private String smsContentAmount;

    @Value("${debt-remind.sms-content-time:截止目前，您在%s的欠款金额有%s元，已欠款%s天，请及时到店还款或联系店铺说明情况（联系电话：%s），如已还款请忽略}")
    private String smsContentTime;

    /**
     * @Description 查询是否弹出欠款催收提醒
     * <AUTHOR>
     * @param reqDebtSmsDTO
     * @return ResDebtSmsDTO
     */
    @Override
    public ExecuteDTO<ResDebtSmsDTO> getDebtPopup(ReqDebtSmsDTO reqDebtSmsDTO){
        ResDebtSmsDTO resDebtSmsDTO = new ResDebtSmsDTO();
        //默认不弹出
        resDebtSmsDTO.setPopupFlag(WhetherEnum.NO.getCode());
        SmsSendConfigDomain domain = new SmsSendConfigDomain();
        domain.setStoreNo(reqDebtSmsDTO.getStoreNo());
        domain.setSmsType(SmsTypeEnum.DEBT_FANS.getCode());
        List<SmsSendConfigDomain> domains = smsSendConfigDao.selectByParams(domain);
        //查询欠款提醒配置是否关闭弹窗
        if(CollectionUtils.isNotEmpty(domains)){
            SmsSendConfigDomain smsSendConfigDomain = domains.get(NumConstant.ZERO);
            //不弹出
            if(smsSendConfigDomain.getNoRemind()!=NumConstant.ONE){
                return ExecuteDTO.success(resDebtSmsDTO);
            }
        }
        //未查询到配置则弹框，并查询欠款金额，如果大于配置金额则弹出提醒
        ReqOrderDTO reqOrderDTO = new ReqOrderDTO();
        reqOrderDTO.setStoreNo(reqDebtSmsDTO.getStoreNo());
        reqOrderDTO.setHeadFlag(NumConstant.TWO);
        reqOrderDTO.setArrearsFlag(NumConstant.TWO);
        ExecuteDTO<ResArrearsOrderCountDTO> executeDTO = orderAnalysisService.getOrderArrearsCount(reqOrderDTO);
        if(executeDTO.successFlag() && executeDTO.getData()!=null){
            ResArrearsOrderCountDTO orderCountDTO = executeDTO.getData();
            //如果欠款金额大于设置金额则弹出警告提醒
            if(orderCountDTO.getArrearsAmountCount()!=null){
                if(orderCountDTO.getArrearsAmountCount().compareTo(new BigDecimal(debtRemindAmount))>= NumConstant.ZERO){
                    resDebtSmsDTO.setPopupFlag(WhetherEnum.YES.getCode());
                    resDebtSmsDTO.setOrderArrearsAmount(orderCountDTO.getArrearsAmountCount());
                }
            }
        }
        return ExecuteDTO.success(resDebtSmsDTO);
    }

    /**
     * @Description 查询短信发送内容
     * <AUTHOR>
     * @param reqDebtSmsDTO
     * @return ResDebtSmsDTO
     */
    @Override
    public ExecuteDTO<ResDebtSmsDTO> getDebtSmsContent(ReqDebtSmsDTO reqDebtSmsDTO){
        this.doAssert(reqDebtSmsDTO);
        String storeName = "";
        String tel = "";
        ReqStoresDTO storesDTO = new ReqStoresDTO();
        storesDTO.setStoreNo(reqDebtSmsDTO.getStoreNo());
        ExecuteDTO<List<StoreInfoResponse>> executeDTO = storeProcessService.getStoreList(storesDTO);
        if(executeDTO.successFlag() && CollectionUtils.isNotEmpty(executeDTO.getData())){
            storeName = executeDTO.getData().get(NumConstant.ZERO).getStoreName();
            //解密
            tel = encryptUtil.decryptSingle(executeDTO.getData().get(NumConstant.ZERO).getDsContactPhone(),new EncryptDecryptRecordDTO("bossapp", "bossapp", DecipherScene.COMMON.getSceneCode()));

        }
        ResDebtSmsDTO resDebtSmsDTO = new ResDebtSmsDTO();
        //动态取短信变量
        if(NumConstant.ONE == reqDebtSmsDTO.getSendTarget()){
            resDebtSmsDTO.setSmsContent(String.format(smsContentAmount,storeName,"xx",tel));
        }
        if(NumConstant.TWO == reqDebtSmsDTO.getSendTarget()){
            resDebtSmsDTO.setSmsContent(String.format(smsContentTime,storeName,"xx",reqDebtSmsDTO.getSendTargetValue(),tel));
        }
        resDebtSmsDTO.setSmsPreviewContent("【蛋品在线】"+resDebtSmsDTO.getSmsContent());
        return ExecuteDTO.success(resDebtSmsDTO);
    }

    private void doAssert(ReqDebtSmsDTO reqDebtSmsDTO) {
        if(reqDebtSmsDTO==null){
            throw new BaseException(CommonCode.CODE_10000001, "参数");
        }else if(reqDebtSmsDTO.getSendTarget()==null){
            throw new BaseException(CommonCode.CODE_10000001, "sendTarget");
        }else if(WhetherEnum.getByCode(reqDebtSmsDTO.getSendTarget())==null){
            throw new BaseException(CommonCode.CODE_10000002, "sendTarget");
        }else if(reqDebtSmsDTO.getSendTargetValue()==null){
            throw new BaseException(CommonCode.CODE_10000001, "sendTargetValue");
        }else if(StringUtils.isBlank(reqDebtSmsDTO.getStoreNo())){
            throw new BaseException(CommonCode.CODE_10000001, "storeNo");
        }
    }
}
