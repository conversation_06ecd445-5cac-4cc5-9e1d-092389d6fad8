package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.base.exception.BaseException;
import cn.htdt.common.dto.enums.BossLogoffErrorEnum;
import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.enums.MarketErrorCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.OrderStatusEnum;
import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.market.MarketTypeEnum;
import cn.htdt.common.enums.market.RewardStatusFlagEnum;
import cn.htdt.common.enums.market.RewardTypeEnum;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.BigDecimalUtil;
import cn.htdt.marketcenter.dto.request.AgentRewardStoreNoDTO;
import cn.htdt.marketcenter.dto.request.AtomReqAgentRewardDTO;
import cn.htdt.marketcenter.dto.response.*;
import cn.htdt.marketprocess.api.analysis.AgentRewardAnalysisService;
import cn.htdt.marketprocess.dao.AgentRewardDao;
import cn.htdt.marketprocess.dto.request.ReqAgentRewardDTO;
import cn.htdt.marketprocess.dto.request.ReqAgentRewardStoreNoDTO;
import cn.htdt.marketprocess.dto.response.*;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomAgentRewardAnalysisService;
import cn.htdt.ordercenter.dto.request.AtomReqSoDTO;
import cn.htdt.ordercenter.dto.response.AtomResSoDTO;
import cn.htdt.orderprocess.api.LegacyOrderCenterService;
import cn.htdt.usercenter.dto.response.ResUUcStoreDTO;
import cn.htdt.userprocess.api.LegacyUserCenterService;
import cn.htdt.userprocess.api.UserPublicService;
import cn.htdt.userprocess.dto.publicDto.response.GetStoreListResponse;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021-01-14
 * @Description 代理人酬劳信息原子查询服务
 */
@DubboService
@Slf4j
public class AgentRewardAnalysisServiceImpl implements AgentRewardAnalysisService {

    @Resource
    private AtomAgentRewardAnalysisService atomAgentRewardAnalysisService;

    @DubboReference
    private UserPublicService userPublicService;

    @DubboReference
    private LegacyOrderCenterService legacyOrderCenterService;

    @DubboReference
    private LegacyUserCenterService legacyUserCenterService;

    @Resource
    private AgentRewardDao agentRewardDao;

    @Override
    public ExecuteDTO<ExecutePageDTO<ResAgentRewardDTO>> getAgentRewardListByPage(ReqAgentRewardDTO reqAgentRewardDTO) {
        Map<String,String> storeNameMap = new HashMap<>();
        Map<String,String> merchantNameMap = new HashMap<>();
        Map<String, LocalDateTime> orderCreateTimeMap = new HashMap<>();
        Map<String, LocalDateTime> orderPaymentConfirmDateMap = new HashMap<>();
        List<String> storeNoList = new ArrayList<>();
        List<String> orderNoList = new ArrayList<>();
        //跨库查询店铺名称
        if (StringUtils.isNotBlank(reqAgentRewardDTO.getStoreName())) {
            ExecuteDTO<List<GetStoreListResponse>> storeExecuteDTO = userPublicService.queryStoreByName(reqAgentRewardDTO.getStoreName());
            if (!storeExecuteDTO.successFlag()) {
                return ExecuteDTO.error(storeExecuteDTO.getStatus(), storeExecuteDTO.getMsg());
            }
            for (GetStoreListResponse getStoreListResponse : storeExecuteDTO.getData()) {
                storeNoList.add(getStoreListResponse.getStoreNo());
            }
        }
        if (StringUtils.isNotBlank(reqAgentRewardDTO.getStoreNo())){
            storeNoList.add(reqAgentRewardDTO.getStoreNo());
        }
        //设置查询参数
        AtomReqAgentRewardDTO atomReqPageAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        if (CollectionUtils.isNotEmpty(storeNoList)) {
            atomReqPageAgentRewardDTO.setStoreNoList(storeNoList);
        }
        //查询代理人酬劳信息列表
        ExecuteDTO<ExecutePageDTO<AtomResAgentRewardDTO>> executeDTO =  atomAgentRewardAnalysisService.getAgentRewardListByPage(atomReqPageAgentRewardDTO);
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        storeNoList.clear();
        for (AtomResAgentRewardDTO atomResAgentRewardDTO : executeDTO.getData().getRows()) {
            storeNoList.add(atomResAgentRewardDTO.getStoreNo());
            if (MarketTypeEnum.CloudDistribution.getCode().equals(atomResAgentRewardDTO.getMarketType()) || MarketTypeEnum.StoreDistribution.getCode().equals(atomResAgentRewardDTO.getMarketType())) {
                orderNoList.add(atomResAgentRewardDTO.getOrderNo());
            }
        }
        if (CollectionUtils.isNotEmpty(storeNoList)) {
            ExecuteDTO<List<GetStoreListResponse>> storeExecuteDTO = userPublicService.queryStoreList(storeNoList);
            if (!storeExecuteDTO.successFlag()) {
                return storeExecuteDTO.error(executeDTO.getStatus(), storeExecuteDTO.getMsg());
            }
            for (GetStoreListResponse store : storeExecuteDTO.getData()) {
                storeNameMap.put(store.getStoreNo(), store.getStoreName());
                merchantNameMap.put(store.getStoreNo(), store.getMerchantName());
            }
        }
        if (CollectionUtils.isNotEmpty(orderNoList)) {
            AtomReqSoDTO atomReqSoDTO = new AtomReqSoDTO();
            atomReqSoDTO.setOrderNos(orderNoList);
            ExecuteDTO<List<AtomResSoDTO>> soExecuteDTO = legacyOrderCenterService.getSoList(atomReqSoDTO);
            if (!soExecuteDTO.successFlag()) {
                return soExecuteDTO.error(soExecuteDTO.getStatus(), soExecuteDTO.getMsg());
            }
            for (AtomResSoDTO atomResSoDTO : soExecuteDTO.getData()) {
                orderCreateTimeMap.put(atomResSoDTO.getOrderNo(), atomResSoDTO.getOrderCreateTime());
                orderPaymentConfirmDateMap.put(atomResSoDTO.getOrderNo(), atomResSoDTO.getOrderPaymentConfirmDate());
            }
        }
        //参数转换
        List<ResAgentRewardDTO> resAgentRewardDTOList = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResAgentRewardDTO.class);
        for (ResAgentRewardDTO resAgentRewardDTO : resAgentRewardDTOList) {
            //归属店铺
            resAgentRewardDTO.setStoreName(storeNameMap.get(resAgentRewardDTO.getStoreNo()));
            //归属商家
            resAgentRewardDTO.setMerchantName(merchantNameMap.get(resAgentRewardDTO.getStoreNo()));
            //下单时间
            if (MarketTypeEnum.CloudDistribution.getCode().equals(resAgentRewardDTO.getMarketType()) || MarketTypeEnum.StoreDistribution.getCode().equals(resAgentRewardDTO.getMarketType())) {
                resAgentRewardDTO.setOrderCreateTime(orderCreateTimeMap.get(resAgentRewardDTO.getOrderNo()));
                resAgentRewardDTO.setPayTime(orderPaymentConfirmDateMap.get(resAgentRewardDTO.getOrderNo()));
            }
        }
        getOrderReward(resAgentRewardDTOList, reqAgentRewardDTO.getAgentNo(), null);
        ExecutePageDTO<ResAgentRewardDTO> executePageDTO = new ExecutePageDTO<>();
        executePageDTO.setRows(resAgentRewardDTOList);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    private ExecuteDTO getOrderReward(List<ResAgentRewardDTO> resAgentRewardDTOList, String agentNo, List<Integer> rewardStatusFlagList) {
        if (CollectionUtils.isEmpty(resAgentRewardDTOList) || StringUtils.isBlank(agentNo)) {
            return ExecuteDTO.success();
        }
        //查询代理人订单获得的酬劳
        List orderNoList = new ArrayList<String>();
        for (ResAgentRewardDTO resAgentRewardDTO : resAgentRewardDTOList) {
            orderNoList.add(resAgentRewardDTO.getOrderNo());
        }
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = new AtomReqAgentRewardDTO();
        atomReqAgentRewardDTO.setAgentNo(agentNo);
        atomReqAgentRewardDTO.setOrderNoList(orderNoList);
        atomReqAgentRewardDTO.setRewardStatusFlagList(rewardStatusFlagList);
        ExecuteDTO<List<AtomResAgentRewardDTO>> rewardExecuteDTO = atomAgentRewardAnalysisService.selectAgentRewardList(atomReqAgentRewardDTO);
        if (!rewardExecuteDTO.successFlag()) {
            return ExecuteDTO.error(rewardExecuteDTO.getStatus(), rewardExecuteDTO.getMsg());
        }
        Map<String,String> orderRewardMap = new HashMap();
        String orderNo = "";
        String goodsNo = "";
        //按照订单编号分组
        Map<String, List<AtomResAgentRewardDTO>> orderNoGroupMap = rewardExecuteDTO.getData().stream().collect(Collectors.groupingBy(AtomResAgentRewardDTO::getOrderNo));
        for (Map.Entry<String, List<AtomResAgentRewardDTO>> orderNoMap : orderNoGroupMap.entrySet()) {
            List<AtomResAgentRewardDTO> orderNoAtomResAgentRewardList =  orderNoMap.getValue();
            orderNo = orderNoMap.getKey();
            //按照商品编号分组
            Map<String, List<AtomResAgentRewardDTO>> goodsNoGroupMap = orderNoAtomResAgentRewardList.stream().collect(Collectors.groupingBy(AtomResAgentRewardDTO::getTaskOrGoodsNo));
            for (Map.Entry<String, List<AtomResAgentRewardDTO>> goodsNoMap : goodsNoGroupMap.entrySet()) {
                StringBuilder reward = new StringBuilder("");
                List<AtomResAgentRewardDTO> goodsNoAtomResAgentRewardList =  goodsNoMap.getValue();
                goodsNo = goodsNoMap.getKey();
                //按照酬劳类型分组
                Map<Integer, List<AtomResAgentRewardDTO>> RewardTypeGroupMap = goodsNoAtomResAgentRewardList.stream().collect(Collectors.groupingBy(AtomResAgentRewardDTO::getRewardType));
                for (Map.Entry<Integer, List<AtomResAgentRewardDTO>> rewardTypeMap : RewardTypeGroupMap.entrySet()) {
                    List<AtomResAgentRewardDTO> rewardTypeAtomResAgentRewardList =  rewardTypeMap.getValue();
                    Integer rewardType = rewardTypeMap.getKey();
                    StringBuilder sb = null;
                    BigDecimal rewardValue = BigDecimal.ZERO;
                    for (AtomResAgentRewardDTO atomResAgentRewardDTO : rewardTypeAtomResAgentRewardList) {
                        rewardValue = rewardValue.add(atomResAgentRewardDTO.getRewardValue());
                    }
                    if (RewardTypeEnum.YJ.getCode().equals(rewardType)) {
                        sb = new StringBuilder("¥");
                        sb.append(rewardValue).append("+");
                    } else if (RewardTypeEnum.LP.getCode().equals(rewardType)) {
                        sb = new StringBuilder("礼品");
                        sb.append(rewardValue.intValue()).append("个").append("+");
                    } else if (RewardTypeEnum.XJQ.getCode().equals(rewardType)) {
                        sb = new StringBuilder("现金券");
                        sb.append(rewardValue.intValue()).append("张").append("+");
                    } else if (RewardTypeEnum.FWQ.getCode().equals(rewardType)) {
                        sb = new StringBuilder("服务券");
                        sb.append(rewardValue.intValue()).append("张").append("+");
                    }else {
                        continue;
                    }
                    reward.append(sb.toString());
                }
                if (reward.toString().length() >= NumConstant.ONE) {
                    orderRewardMap.put(orderNo + ":" +goodsNo, reward.toString().substring(NumConstant.ZERO, reward.toString().length() - NumConstant.ONE));
                }
            }
        }
        for (ResAgentRewardDTO resAgentRewardDTO : resAgentRewardDTOList) {
            resAgentRewardDTO.setEstimateReward(orderRewardMap.get(resAgentRewardDTO.getOrderNo() + ":" + resAgentRewardDTO.getTaskOrGoodsNo()));
        }
        return ExecuteDTO.success();
    }

    @Override
    public ExecuteDTO<ResAgentRewardDTO> getNewAgentReward(ReqAgentRewardDTO reqAgentRewardDTO) {
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        ExecuteDTO<AtomResAgentRewardDTO> executeDTO =  atomAgentRewardAnalysisService.getNewAgentReward(atomReqAgentRewardDTO);
        if(!executeDTO.successFlag()){
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResAgentRewardDTO resAgentRewardDTO = BeanCopierUtil.copy(executeDTO.getData(), ResAgentRewardDTO.class);
        return ExecuteDTO.success(resAgentRewardDTO);
    }

    @Override
    public ExecuteDTO<ResAgentRewardDTO> getAgentRewardStatistics(ReqAgentRewardDTO reqAgentRewardDTO) {
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        ExecuteDTO<AtomResAgentRewardDTO> executeDTO =  atomAgentRewardAnalysisService.getAgentRewardStatistics(atomReqAgentRewardDTO);
        if(!executeDTO.successFlag()){
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResAgentRewardDTO resAgentRewardDTO = BeanCopierUtil.copy(executeDTO.getData(), ResAgentRewardDTO.class);
        return ExecuteDTO.success(resAgentRewardDTO);
    }

    @Override
    public ExecuteDTO<List<ResAgentRewardDTO>> getAgentRewardMouthStatistics(ReqAgentRewardDTO reqAgentRewardDTO) {
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        ExecuteDTO<List<AtomResAgentRewardDTO>> executeDTO = atomAgentRewardAnalysisService.getAgentRewardMouthStatistics(atomReqAgentRewardDTO);
        if(!executeDTO.successFlag()){
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        List<ResAgentRewardDTO> resAgentRewardDTOList = BeanCopierUtil.copyList(executeDTO.getData(), ResAgentRewardDTO.class);
        return ExecuteDTO.success(resAgentRewardDTOList);
    }

    @Override
    public ExecuteDTO<ResAgentRewardDTO> getGoodsOrTaskDayStatistics(ReqAgentRewardDTO reqAgentRewardDTO) {
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        ExecuteDTO<AtomResAgentRewardDTO> executeDTO = atomAgentRewardAnalysisService.getGoodsOrTaskDayStatistics(atomReqAgentRewardDTO);
        if(!executeDTO.successFlag()){
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResAgentRewardDTO resAgentRewardDTO = BeanCopierUtil.copy(executeDTO.getData(), ResAgentRewardDTO.class);
        return ExecuteDTO.success(resAgentRewardDTO);
    }

    @Override
    public ExecuteDTO<ResAgentRewardDTO> getDistributeOrderCount(ReqAgentRewardDTO reqAgentRewardDTO) {
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        ExecuteDTO<AtomResAgentRewardDTO> executeDTO = atomAgentRewardAnalysisService.getDistributeOrderCount(atomReqAgentRewardDTO);
        if(!executeDTO.successFlag()){
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResAgentRewardDTO resAgentRewardDTO = BeanCopierUtil.copy(executeDTO.getData(), ResAgentRewardDTO.class);
        return ExecuteDTO.success(resAgentRewardDTO);
    }

    @Override
    public ExecuteDTO<ResAgentRewardDTO> getAgentOrderStatistics(ReqAgentRewardDTO reqAgentRewardDTO) {
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        ExecuteDTO<AtomResAgentRewardDTO> executeDTO = atomAgentRewardAnalysisService.getAgentOrderStatistics(atomReqAgentRewardDTO);
        if(!executeDTO.successFlag()){
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResAgentRewardDTO resAgentRewardDTO = BeanCopierUtil.copy(executeDTO.getData(), ResAgentRewardDTO.class);
        return ExecuteDTO.success(resAgentRewardDTO);
    }

    @Override
    public ExecuteDTO<ResAgentRewardDTO> getAgentTaskStatistics(ReqAgentRewardDTO reqAgentRewardDTO) {
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        ExecuteDTO<AtomResAgentRewardDTO> executeDTO = atomAgentRewardAnalysisService.getAgentTaskStatistics(atomReqAgentRewardDTO);
        if(!executeDTO.successFlag()){
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResAgentRewardDTO resAgentRewardDTO = BeanCopierUtil.copy(executeDTO.getData(), ResAgentRewardDTO.class);
        return ExecuteDTO.success(resAgentRewardDTO);
    }

    @Override
    public ExecuteDTO<ResAgentRewardDTO> getAgentYjStatistics(ReqAgentRewardDTO reqAgentRewardDTO) {
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        ExecuteDTO<AtomResAgentRewardDTO> executeDTO = atomAgentRewardAnalysisService.getAgentYjStatistics(atomReqAgentRewardDTO);
        if(!executeDTO.successFlag()){
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResAgentRewardDTO resAgentRewardDTO = BeanCopierUtil.copy(executeDTO.getData(), ResAgentRewardDTO.class);
        return ExecuteDTO.success(resAgentRewardDTO);
    }

    @Override
    public ExecuteDTO<ResAgentRewardDTO> getAgentOtherStatistics(ReqAgentRewardDTO reqAgentRewardDTO) {
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        ExecuteDTO<AtomResAgentRewardDTO> executeDTO = atomAgentRewardAnalysisService.getAgentOtherStatistics(atomReqAgentRewardDTO);
        if(!executeDTO.successFlag()){
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResAgentRewardDTO resAgentRewardDTO = BeanCopierUtil.copy(executeDTO.getData(), ResAgentRewardDTO.class);
        return ExecuteDTO.success(resAgentRewardDTO);
    }

    @Override
    public ExecuteDTO<ResAgentRewardDTO> getAgentTotalStatistics(ReqAgentRewardDTO reqAgentRewardDTO) {
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        ExecuteDTO<AtomResAgentRewardDTO> executeDTO = atomAgentRewardAnalysisService.getAgentTotalStatistics(atomReqAgentRewardDTO);
        if(!executeDTO.successFlag()){
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResAgentRewardDTO resAgentRewardDTO = BeanCopierUtil.copy(executeDTO.getData(), ResAgentRewardDTO.class);
        return ExecuteDTO.success(resAgentRewardDTO);
    }

    @Override
    public ExecuteDTO<ExecutePageDTO<ResAgentRewardDTO>> selectWriteOffGoodsList(ReqAgentRewardDTO reqAgentRewardDTO) {
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        //查询代理人酬劳信息
        ExecuteDTO<ExecutePageDTO<AtomResAgentRewardDTO>> executePageDTOExecuteDTO = atomAgentRewardAnalysisService.getWriteOffGoodsList(atomReqAgentRewardDTO);
        List<AtomResAgentRewardDTO> rewardDTOS = executePageDTOExecuteDTO.getData().getRows();
        AtomReqSoDTO atomReqSoDTO = new AtomReqSoDTO();

        List<ResAgentRewardDTO> resAgentRewardDTOS = BeanCopierUtil.copyList(rewardDTOS, ResAgentRewardDTO.class);
        if(CollectionUtils.isNotEmpty(resAgentRewardDTOS)){
            List<String> orderNos = new ArrayList<>();
            resAgentRewardDTOS.forEach(reward ->{
                orderNos.add(reward.getOrderNo());
            });
            atomReqSoDTO.setOrderNos(orderNos);
            //查询订单状态
            ExecuteDTO<List<AtomResSoDTO>> soList = legacyOrderCenterService.getSoList(atomReqSoDTO);
            resAgentRewardDTOS.forEach(reward ->{
                Optional<AtomResSoDTO> optional = soList.getData().stream().filter(so->so.getOrderNo().equals(reward.getOrderNo())).findFirst();
                if(optional.isPresent()){
                    reward.setOrderStatus(optional.get().getOrderStatus());
                    reward.setOrderStatusValue(OrderStatusEnum.getByCode(optional.get().getOrderStatus()).getName());
                }
            });
        }
        //分页
        ExecutePageDTO<ResAgentRewardDTO> executePageDTO = new ExecutePageDTO<>();
        executePageDTO.setRows(resAgentRewardDTOS);
        executePageDTO.setTotal(executePageDTOExecuteDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    @Override
    public ExecuteDTO<Integer> selectWriteOffGoodsCount(ReqAgentRewardDTO reqAgentRewardDTO) {
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        //查询代理人酬劳核销统计信息
        ExecuteDTO<Integer> executeDTO = atomAgentRewardAnalysisService.getWriteOffGoodsCount(atomReqAgentRewardDTO);
        return ExecuteDTO.success(executeDTO.getData());
    }

    @Override
    public ExecuteDTO<ResAgentRewardDTO> getWriteOffCode(ReqAgentRewardDTO reqAgentRewardDTO) {
        if (StringUtils.isBlank(reqAgentRewardDTO.getWriteOffCode())) {
            return ExecuteDTO.error(CommonCode.CODE_10000001, "writeOffCode");
        }
        //查询核销码是否存在
        AtomReqAgentRewardDTO agentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        ExecuteDTO<AtomResAgentRewardDTO> executeDTO = atomAgentRewardAnalysisService.getWriteOffCode(agentRewardDTO);
        if (executeDTO.getData() == null) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000110);
        }
        //判断是否已核销
        if (WhetherEnum.YES.getCode().equals(executeDTO.getData().getWriteOffStatus())) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000111);
        }
        return ExecuteDTO.success(BeanCopierUtil.copy(executeDTO.getData(),ResAgentRewardDTO.class));

    }

    @Override
    public ExecuteDTO<ExecutePageDTO<ResAgentRewardDTO>> getAgentRewardWriteOff(ReqAgentRewardDTO reqAgentRewardDTO) {
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        atomReqAgentRewardDTO.setWriteOffStatus(WhetherEnum.NO.getCode());
        //查询代理人酬劳信息
        ExecuteDTO<ExecutePageDTO<AtomResAgentRewardDTO>> executePageDTOExecuteDTO = atomAgentRewardAnalysisService.getWriteOffGoodsList(atomReqAgentRewardDTO);
        List<AtomResAgentRewardDTO> rewardDTOS = executePageDTOExecuteDTO.getData().getRows();
        List<ResAgentRewardDTO> resAgentRewardDTOS = BeanCopierUtil.copyList(rewardDTOS, ResAgentRewardDTO.class);

        //查询店铺名称
        List<String> orderNos = new ArrayList<>();
        rewardDTOS.forEach(reward ->{
            orderNos.add(reward.getStoreNo());
        });
		ExecuteDTO<List<GetStoreListResponse>> storeDTO = userPublicService.queryStoreList(orderNos);
        resAgentRewardDTOS.forEach(reward ->{
            Optional<GetStoreListResponse> optional = storeDTO.getData().stream().filter(so->so.getStoreNo().equals(reward.getStoreNo())).findFirst();
            if(optional.isPresent()){
                reward.setStoreName(optional.get().getStoreName());
               /* reward.setAdminPhone(optional.get().getAdminPhone());
                reward.setAdminName(optional.get().getAdminName());
                reward.setAdminNo(optional.get().getAdminNo());
                reward.setProvinceName(optional.get().getProvinceName());
                reward.setCityName(optional.get().getCityName());
                reward.setRegionName(optional.get().getRegionName());
                reward.setStreetName(optional.get().getStreetName());
                reward.setDetailAddress(optional.get().getDetailAddress());*/
                reward.setCancelName(optional.get().getCancelName());
                reward.setCancelPhone(optional.get().getCancelPhone());
                reward.setCancelAddress(optional.get().getCancelAddress());
            }
        });
        ExecutePageDTO<ResAgentRewardDTO> executePageDTO = new ExecutePageDTO<>();
        executePageDTO.setRows(resAgentRewardDTOS);
        executePageDTO.setTotal(executePageDTOExecuteDTO.getData().getTotal());

        return ExecuteDTO.success(executePageDTO);
    }

    @Override
    public ExecuteDTO<ResAgentRewardDTO> getTotalAgentReward(ReqAgentRewardDTO reqAgentRewardDTO) {
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        ExecuteDTO<AtomResAgentRewardDTO> executeDTO = atomAgentRewardAnalysisService.getTotalAgentReward(atomReqAgentRewardDTO);
        if(!executeDTO.successFlag()){
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResAgentRewardDTO resAgentRewardDTO = BeanCopierUtil.copy(executeDTO.getData(), ResAgentRewardDTO.class);
        return ExecuteDTO.success(resAgentRewardDTO);
    }

    @Override
    public ExecuteDTO<List<ResAgentRewardDTO>> getByOrderNo(ReqAgentRewardDTO reqAgentRewardDTO) {
        if(reqAgentRewardDTO == null){
            throw new BaseException(CommonCode.CODE_10000001, "入参");
        }else if(StringUtils.isBlank(reqAgentRewardDTO.getOrderNo())
                && StringUtils.isBlank(reqAgentRewardDTO.getParentOrderNo())){
            throw new BaseException(CommonCode.CODE_10000009, "orderNo|parentOrderNo");
        }
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        ExecuteDTO<List<AtomResAgentRewardDTO>> executeDTO = this.atomAgentRewardAnalysisService.getByOrderNo(atomReqAgentRewardDTO);
        if(!executeDTO.successFlag()){
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        return ExecuteDTO.success(BeanCopierUtil.copyList(executeDTO.getData(), ResAgentRewardDTO.class));
    }

    @Override
    public ExecuteDTO<ResPlanAsAWholeRewardDTO> getPlanAsAWholeReward(ReqAgentRewardDTO reqAgentRewardDTO) {
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        ExecuteDTO<AtomResPlanAsAWholeRewardDTO> executeDTO = this.atomAgentRewardAnalysisService.getPlanAsAWholeReward(atomReqAgentRewardDTO);
        if(!executeDTO.successFlag()){
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        return ExecuteDTO.success(BeanCopierUtil.copy(executeDTO.getData(), ResPlanAsAWholeRewardDTO.class));
    }

    //暂不适用，但请勿删
    /*@Override
    public ExecuteDTO<BigDecimal> getAgentRewardValueSum(ReqAgentRewardDTO reqAgentRewardDTO) {
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        ExecuteDTO<BigDecimal> executeDTO = this.atomAgentRewardAnalysisService.getAgentRewardValueSum(atomReqAgentRewardDTO);
        return executeDTO;
    }

    @Override
    public ExecuteDTO<BigDecimal> getStoreToBeReturnRewardValueSum(ReqAgentRewardDTO reqAgentRewardDTO) {
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        ExecuteDTO<BigDecimal> executeDTO = this.atomAgentRewardAnalysisService.getStoreToBeReturnRewardValueSum(atomReqAgentRewardDTO);
        return executeDTO;
    }

    @Override
    public ExecuteDTO<BigDecimal> getStoreRewardValueSum(ReqAgentRewardDTO reqAgentRewardDTO) {
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        ExecuteDTO<BigDecimal> executeDTO = this.atomAgentRewardAnalysisService.getStoreRewardValueSum(atomReqAgentRewardDTO);
        return executeDTO;
    }

    @Override
    public ExecuteDTO<BigDecimal> getDisStoreRewardValueSum(ReqAgentRewardDTO reqAgentRewardDTO) {
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        ExecuteDTO<BigDecimal> executeDTO = this.atomAgentRewardAnalysisService.getDisStoreRewardValueSum(atomReqAgentRewardDTO);
        return executeDTO;
    }*/

    @Override
    public ExecuteDTO<List<ResAgentRewardDTO>> getAgentRewardList(ReqAgentRewardDTO reqAgentRewardDTO) {
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        ExecuteDTO<List<AtomResAgentRewardDTO>> executeDTO = atomAgentRewardAnalysisService.selectAgentRewardList(atomReqAgentRewardDTO);
        if(!executeDTO.successFlag()){
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        List<ResAgentRewardDTO> resAgentRewardDTOList = BeanCopierUtil.copyList(executeDTO.getData(), ResAgentRewardDTO.class);
        getOrderReward(resAgentRewardDTOList, reqAgentRewardDTO.getAgentNo(), null);
        return ExecuteDTO.success(resAgentRewardDTOList);
    }

    @Override
    public ExecuteDTO<List<ResAgentRewardProfitDTO>> getProfitByOrderNo(ReqAgentRewardDTO reqAgentRewardDTO) {
        if(reqAgentRewardDTO == null){
            throw new BaseException(CommonCode.CODE_10000001, "参数");
        }else if(StringUtils.isBlank(reqAgentRewardDTO.getParentOrderNo()) && StringUtils.isBlank(reqAgentRewardDTO.getOrderNo())){
            throw new BaseException(CommonCode.CODE_10000009, "parentOrderNo和orderNo");
        }
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        ExecuteDTO<List<AtomResAgentRewardProfitDTO>> executeDTO = this.atomAgentRewardAnalysisService.getProfitByOrderNo(atomReqAgentRewardDTO);
        if(!executeDTO.successFlag()){
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        return ExecuteDTO.success(BeanCopierUtil.copyList(executeDTO.getData(), ResAgentRewardProfitDTO.class));
    }

	@Override
    public ExecuteDTO<ResAgentRewardOrderMoneyDTO> selectAgentRewardOrderMoney(ReqAgentRewardStoreNoDTO reqAgentRewardStoreNoDTO) {
        AgentRewardStoreNoDTO agentRewardStoreNoDTO = BeanCopierUtil.copy(reqAgentRewardStoreNoDTO, AgentRewardStoreNoDTO.class);
        ExecuteDTO<AgentRewardOrderMoneyDTO> executeDTO = atomAgentRewardAnalysisService.selectAgentRewardOrderMoney(agentRewardStoreNoDTO);
        if(!executeDTO.successFlag()){
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        return ExecuteDTO.success(BeanCopierUtil.copy(executeDTO.getData(), ResAgentRewardOrderMoneyDTO.class));
    }

    @Override
	public ExecuteDTO<ExecutePageDTO<ResAgentRewardDTO>> getRewardListByPage(ReqAgentRewardDTO reqAgentRewardDTO) {
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResAgentRewardDTO>> executeDTO = atomAgentRewardAnalysisService.getRewardListByPage(atomReqAgentRewardDTO);
        List<ResAgentRewardDTO> resAgentRewardDTOList = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResAgentRewardDTO.class);
        for (ResAgentRewardDTO resAgentRewardDTO : resAgentRewardDTOList) {
            ExecuteDTO<ResUUcStoreDTO> storeExecuteDTO = legacyUserCenterService.getStoreInfoByStoreNo(resAgentRewardDTO.getDistributionStoreNo());
            if (storeExecuteDTO.successFlag() && storeExecuteDTO.getData() != null) {
                resAgentRewardDTO.setStoreName(storeExecuteDTO.getData().getStoreName());
            }
            resAgentRewardDTO.setGoodsName(resAgentRewardDTO.getGoodsNameConcat());
            resAgentRewardDTO.setRewardValue(resAgentRewardDTO.getRewardValue());
            if (resAgentRewardDTO.getRewardStatusFlag() != null) {
                resAgentRewardDTO.setRewardStatusFlagName(RewardStatusFlagEnum.getByCode(resAgentRewardDTO.getRewardStatusFlag()).getType());
            }
        }
        ExecutePageDTO<ResAgentRewardDTO> executePageDTO = new ExecutePageDTO<>();
        executePageDTO.setRows(resAgentRewardDTOList);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    @Override
    public ExecuteDTO<List<ResAgentRewardDTO>> getRewardMonthCount(ReqAgentRewardDTO reqAgentRewardDTO) {
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        ExecuteDTO<List<AtomResAgentRewardDTO>> executeDTO = atomAgentRewardAnalysisService.getRewardMonthCount(atomReqAgentRewardDTO);
        List<ResAgentRewardDTO> resAgentRewardDTOList = BeanCopierUtil.copyList(executeDTO.getData(), ResAgentRewardDTO.class);
        return ExecuteDTO.success(resAgentRewardDTOList);
    }

    /**
     * 汇赚钱-首页-代理人获得酬劳滚动信息
     *
     * @param reqAgentRewardDTO
     * @return
     * @auth hxj
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResAgentRewardCircularDisplayDTO>> selectAgentRewardCircularDisplay(
            ReqAgentRewardDTO reqAgentRewardDTO) {
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResAgentRewardDTO>> executePageDTOExecuteDTO =
                atomAgentRewardAnalysisService.selectAgentRewardCircularDisplay(atomReqAgentRewardDTO);
        List<ResAgentRewardCircularDisplayDTO> resAgentRewardCircularDisplayDTOS =
                BeanCopierUtil.copyList(executePageDTOExecuteDTO.getData().getRows(), ResAgentRewardCircularDisplayDTO.class);
        resAgentRewardCircularDisplayDTOS.stream().forEach(agentReward ->
                {
                    buildAgentRewardMsg(agentReward);
                });

        return ExecuteDTO.success(new ExecutePageDTO<>(executePageDTOExecuteDTO.getData().getTotal(),
                resAgentRewardCircularDisplayDTOS));
    }

    public void buildAgentRewardMsg(ResAgentRewardCircularDisplayDTO agentReward){
        StringBuilder sb = new StringBuilder();
        sb.append("恭喜");
        sb.append(agentReward.getAgentName()==null?"**":agentReward.getAgentName().substring(0,1)+"**");
        if (Objects.equals(agentReward.getMarketType(),MarketTypeEnum.PullNew.getCode())){
            sb.append("完成推广任务，获得");
        }else{
            sb.append("完成分销任务，获得");
        }
        String rewardValue ;
        if (agentReward.getRewardType().equals(RewardTypeEnum.YJ.getCode())){
            rewardValue = agentReward.getRewardValue().toString();
        }else{
            rewardValue = BigDecimalUtil.setScale(agentReward.getRewardValue(),0).toString();
        }
        Map<Integer,String> msgResult = new HashMap(16);
        msgResult.put(RewardTypeEnum.YJ.getCode(), rewardValue + "元" + RewardTypeEnum.YJ.getType());
        msgResult.put(RewardTypeEnum.HJB.getCode(),rewardValue + "个" + RewardTypeEnum.HJB.getType());
        msgResult.put(RewardTypeEnum.LP.getCode(),rewardValue + "件" + RewardTypeEnum.LP.getType());
        msgResult.put(RewardTypeEnum.XJQ.getCode(),rewardValue + "张" + RewardTypeEnum.XJQ.getType());
        msgResult.put(RewardTypeEnum.FWQ.getCode(),rewardValue + "张" + RewardTypeEnum.FWQ.getType());
        msgResult.put(RewardTypeEnum.HFQ.getCode(),rewardValue + "张" + RewardTypeEnum.HFQ.getType());

        if (msgResult.containsKey(agentReward.getRewardType())){
            sb.append(msgResult.get(agentReward.getRewardType()));
        }
        agentReward.setShowMsg(sb.toString());
    }

    @Override
    public ExecuteDTO<Integer> getOrderCount(ReqAgentRewardDTO reqAgentRewardDTO) {
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        ExecuteDTO<Integer> executeDTO = atomAgentRewardAnalysisService.getOrderCount(atomReqAgentRewardDTO);
        return ExecuteDTO.success(executeDTO.getData());
    }

    @Override
    public ExecuteDTO<BigDecimal> getYjCount(ReqAgentRewardDTO reqAgentRewardDTO) {
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        ExecuteDTO<BigDecimal> executeDTO = atomAgentRewardAnalysisService.getYjCount(atomReqAgentRewardDTO);
        return ExecuteDTO.success(executeDTO.getData());
    }

    @Override
    public ExecuteDTO<BigDecimal> getYjCountByOrderNo(ReqAgentRewardDTO reqAgentRewardDTO) {
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        ExecuteDTO<BigDecimal> executeDTO = atomAgentRewardAnalysisService.getYjCountByOrderNo(atomReqAgentRewardDTO);
        return ExecuteDTO.success(executeDTO.getData());
    }

    @Override
    public ExecuteDTO<List<ResYjCountDTO>> selectYjCountByOrderNos(Set<String> orderNos) {
        ExecuteDTO<List<AtomYjCountDTO>> listExecuteDTO = atomAgentRewardAnalysisService.selectYjCountByOrderNos(orderNos);
        if (!listExecuteDTO.successFlag()){
            return ExecuteDTO.error(listExecuteDTO.getStatus(),listExecuteDTO.getMsg());
        }
        List<ResYjCountDTO> resYjCountDTOS = BeanCopierUtil.copyList(listExecuteDTO.getData(), ResYjCountDTO.class);
        return ExecuteDTO.success(resYjCountDTOS);
    }

    @Override
    public ExecuteDTO<List<ResAgentRewardDTO>> getBestRewardAgentList(ReqAgentRewardDTO reqAgentRewardDTO) {
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        ExecuteDTO<List<AtomResAgentRewardDTO>> executeDTO = atomAgentRewardAnalysisService.getBestRewardAgentList(atomReqAgentRewardDTO);
        List<ResAgentRewardDTO> resAgentRewardDTOList = BeanCopierUtil.copyList(executeDTO.getData(), ResAgentRewardDTO.class);
        for (ResAgentRewardDTO resAgentRewardDTO : resAgentRewardDTOList) {
            StringBuilder sb = new StringBuilder();
            sb.append("完成任务，获得");
            if (Objects.equals(resAgentRewardDTO.getRewardType(),RewardTypeEnum.YJ.getCode())){
                sb.append(resAgentRewardDTO.getRewardValue() + "元" + RewardTypeEnum.YJ.getType());
            }
            if (Objects.equals(resAgentRewardDTO.getRewardType(),RewardTypeEnum.HJB.getCode())){
                sb.append(resAgentRewardDTO.getRewardValue().toBigInteger() + "个" + RewardTypeEnum.HJB.getType());
            }
            if (Objects.equals(resAgentRewardDTO.getRewardType(),RewardTypeEnum.LP.getCode())){
                sb.append(resAgentRewardDTO.getRewardValue().toBigInteger() + "件" + RewardTypeEnum.LP.getType());
            }
            if (Objects.equals(resAgentRewardDTO.getRewardType(),RewardTypeEnum.XJQ.getCode())){
                sb.append(resAgentRewardDTO.getRewardValue().toBigInteger() + "张" + RewardTypeEnum.XJQ.getType());
            }
            if (Objects.equals(resAgentRewardDTO.getRewardType(),RewardTypeEnum.FWQ.getCode())){
                sb.append(resAgentRewardDTO.getRewardValue().toBigInteger() + "张" + RewardTypeEnum.FWQ.getType());
            }
            if (Objects.equals(resAgentRewardDTO.getRewardType(),RewardTypeEnum.HFQ.getCode())){
                sb.append(resAgentRewardDTO.getRewardValue().toBigInteger() + "张" + RewardTypeEnum.HFQ.getType());
            }
            resAgentRewardDTO.setTaskMessage(sb.toString());
        }
        return ExecuteDTO.success(resAgentRewardDTOList);
    }

    @Override
    public ExecuteDTO<Integer> getUnpaidRewardCount(ReqAgentRewardDTO reqAgentRewardDTO) {
        log.info("----AgentRewardAnalysisServiceImpl---getUnpaidRewardCount---param----{}", JSON.toJSONString(reqAgentRewardDTO));
        AtomReqAgentRewardDTO atomReqAgentRewardDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardDTO.class);
        ExecuteDTO<Integer> unpaidCountExecuteDTO = atomAgentRewardAnalysisService.getUnpaidRewardCount(atomReqAgentRewardDTO);
        if (unpaidCountExecuteDTO.successFlag()
                && null != unpaidCountExecuteDTO.getData()
                && unpaidCountExecuteDTO.getData() > 0) {
            return ExecuteDTO.error(BossLogoffErrorEnum.UNPAID_REMUNERATION,unpaidCountExecuteDTO.getData());
        }
        return ExecuteDTO.success();
    }

    /**
     * 查询可提现佣金（预估+冻结+解冻）
     * @param reqAgentRewardDTO reqAgentRewardDTO.getAgentNo代理人编号
     * @return 可提现佣金（预估+冻结+解冻）
     */
    @Override
    public ExecuteDTO<BigDecimal> selectTotalUnpaidYjReward(ReqAgentRewardDTO reqAgentRewardDTO) {
        return atomAgentRewardAnalysisService.getYjrewardTotal(reqAgentRewardDTO.getAgentNo());
    }

    /**
     * 查询未使用的服务券
     * @param agentNo 代理人编号
     * @return 未使用的服务券
     */
    @Override
    public ExecuteDTO<BigDecimal> getUnusedAgentServiceCoupon(String agentNo) {
        return atomAgentRewardAnalysisService.getUnusedAgentServiceCoupon(agentNo);
    }

    /**
     * 查询未使用的现金券
     * @param agentNo 代理人编号
     * @return 未使用的现金券
     */
    @Override
    public ExecuteDTO<BigDecimal> getUnusedAgentExclusiveCoupon(String agentNo) {
        return atomAgentRewardAnalysisService.getUnusedAgentExclusiveCoupon(agentNo);
    }

    /**
     * 查询未使用的代理人酬劳（排除佣金，现金券，服务券）
     * @param agentNo 代理人编号
     * @return 未使用的代理人酬劳（排除佣金，现金券，服务券）
     */
    @Override
    public ExecuteDTO<BigDecimal> getUnusedAgentOtherReward(String agentNo) {
        return atomAgentRewardAnalysisService.getUnusedAgentOtherReward(agentNo);
    }

    @Override
    public ExecuteDTO<BigDecimal> selectYjCountSumByOrderNos(Set<String> orderNos) {
        return ExecuteDTO.ok(agentRewardDao.selectYjCountSumByOrderNos(orderNos));
    }
}