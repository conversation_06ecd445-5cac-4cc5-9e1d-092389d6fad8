package cn.htdt.marketprocess.biz.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.market.MarketTypeEnum;
import cn.htdt.common.enums.market.RewardRelatedDateTypeEnum;
import cn.htdt.common.enums.market.RewardTypeEnum;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqAgentMarketRewardsetDTO;
import cn.htdt.marketcenter.dto.request.AtomReqAgentMarketRewardsetRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomResAgentMarketRewardsetDTO;
import cn.htdt.marketcenter.dto.response.GoodsNoByCommissionDTO;
import cn.htdt.marketprocess.api.operat.AgentMarketRewardsetOperatService;
import cn.htdt.marketprocess.biz.conversion.AgentMarketRewardsetAssert;
import cn.htdt.marketprocess.dto.request.ReqAgentMarketRewardsetDTO;
import cn.htdt.marketprocess.dto.response.ResAgentMarketRewardsetDTO;
import cn.htdt.marketprocess.dto.response.ResGoodsNoByCommissionDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomAgentMarketRewardsetAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomAgentMarketRewardsetOperatService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-01-20
 * @Description 分销商品|云池商品酬劳设置
 **/
@Slf4j
@DubboService
public class AgentMarketRewardsetOperatServiceImpl implements AgentMarketRewardsetOperatService {

    @Autowired
    private AgentMarketRewardsetAssert agentMarketRewardsetAssert;

    @Resource
    private AtomAgentMarketRewardsetOperatService atomAgentMarketRewardsetOperatService;

    @Resource
    private AtomAgentMarketRewardsetAnalysisService atomAgentMarketRewardsetAnalysisService;

    @Override
    public ExecuteDTO saveMarketRewardset(ReqAgentMarketRewardsetDTO reqAgentMarketRewardsetDTO) {
        this.agentMarketRewardsetAssert.saveMarketRewardset(reqAgentMarketRewardsetDTO);
        AtomReqAgentMarketRewardsetDTO atomReqAgentMarketRewardsetDTO = BeanCopierUtil.copy(reqAgentMarketRewardsetDTO, AtomReqAgentMarketRewardsetDTO.class);
        if (MarketTypeEnum.StoreDistribution.getCode().equals(reqAgentMarketRewardsetDTO.getMarketType())) {
            AtomReqAgentMarketRewardsetDTO searchAgentMarketRewardsetDTO = new AtomReqAgentMarketRewardsetDTO();
            searchAgentMarketRewardsetDTO.setTaskOrGoodsNo(reqAgentMarketRewardsetDTO.getTaskOrGoodsNo());
            searchAgentMarketRewardsetDTO.setMarketType(reqAgentMarketRewardsetDTO.getMarketType());
            ExecuteDTO<List<AtomResAgentMarketRewardsetDTO>> executeRewardsetDTO = this.atomAgentMarketRewardsetAnalysisService.getList(searchAgentMarketRewardsetDTO);
            if (!executeRewardsetDTO.successFlag()) {
                log.info(String.format("*AgentMarketRewardsetOperatServiceImpl-saveMarketRewardset-查询酬劳设置异常-出参:{%s}", JSON.toJSONString(executeRewardsetDTO)));
                return ExecuteDTO.error(executeRewardsetDTO.getStatus(), executeRewardsetDTO.getMsg());
            }
            if (CollectionUtils.isNotEmpty(executeRewardsetDTO.getData())) {
                String msgA = null;
                RewardTypeEnum rewardTypeEnumA = RewardTypeEnum.getByCode(reqAgentMarketRewardsetDTO.getRewardType());
                //更改后
                if (RewardTypeEnum.YJ == rewardTypeEnumA) {
                    msgA = String.format("%s比率%s", RewardTypeEnum.YJ.getType(), reqAgentMarketRewardsetDTO.getYjOrHjb().toString() + "%");
                } else if (RewardTypeEnum.XJQ == rewardTypeEnumA) {
                    String name = String.format("%s满%s减%s", RewardTypeEnum.XJQ.getType(), reqAgentMarketRewardsetDTO.getRelatedUp().intValue(), reqAgentMarketRewardsetDTO.getRelatedLess().intValue());
                    if (RewardRelatedDateTypeEnum.LONG_TIME.getCode().equals(reqAgentMarketRewardsetDTO.getRelatedDateType())) {
                        msgA = String.format("%s，长期有效", name);
                    } else {
                        msgA = String.format("%s，有效周期%s天", name, reqAgentMarketRewardsetDTO.getRelatedEffDays());
                    }
                } else if ((RewardTypeEnum.LP == rewardTypeEnumA)
                        || (RewardTypeEnum.FWQ == rewardTypeEnumA)) {
                    String name = rewardTypeEnumA.getType();
                    if (RewardRelatedDateTypeEnum.LONG_TIME.getCode().equals(reqAgentMarketRewardsetDTO.getRelatedDateType())) {
                        msgA = String.format("%s(%s)，长期有效", name, reqAgentMarketRewardsetDTO.getRelatedName());
                    } else {
                        msgA = String.format("%s(%s)，有效周期%s天", name, reqAgentMarketRewardsetDTO.getRelatedName(), reqAgentMarketRewardsetDTO.getRelatedEffDays());
                    }
                }
                String msgB = null;
                AtomResAgentMarketRewardsetDTO rewardsetDTOA = executeRewardsetDTO.getData().get(NumConstant.ZERO);
                RewardTypeEnum rewardTypeEnumB = RewardTypeEnum.getByCode(rewardsetDTOA.getRewardType());
                //更改前
                if (RewardTypeEnum.YJ == rewardTypeEnumB) {
                    msgB = String.format("%s比率%s", RewardTypeEnum.YJ.getType(), rewardsetDTOA.getYjOrHjb().toString() + "%");
                } else if (RewardTypeEnum.XJQ == rewardTypeEnumB) {
                    String name = String.format("%s满%s减%s", RewardTypeEnum.XJQ.getType(), rewardsetDTOA.getRelatedUp().intValue(), rewardsetDTOA.getRelatedLess().intValue());
                    if (RewardRelatedDateTypeEnum.LONG_TIME.getCode().equals(rewardsetDTOA.getRelatedDateType())) {
                        msgB = String.format("%s，长期有效", name);
                    } else {
                        msgB = String.format("%s，有效周期%s天", name, rewardsetDTOA.getRelatedEffDays());
                    }
                } else if ((RewardTypeEnum.LP == rewardTypeEnumB)
                        || (RewardTypeEnum.FWQ == rewardTypeEnumB)) {
                    String name = rewardTypeEnumB.getType();
                    if (RewardRelatedDateTypeEnum.LONG_TIME.getCode().equals(rewardsetDTOA.getRelatedDateType())) {
                        msgB = String.format("%s(%s)，长期有效", name, rewardsetDTOA.getRelatedName());
                    } else {
                        msgB = String.format("%s(%s)，长期周期%s天", name, rewardsetDTOA.getRelatedName(), rewardsetDTOA.getRelatedEffDays());
                    }
                }

                AtomReqAgentMarketRewardsetRecordDTO rewardSetRecordDTO = new AtomReqAgentMarketRewardsetRecordDTO();
                rewardSetRecordDTO.setTaskOrGoodsNo(reqAgentMarketRewardsetDTO.getTaskOrGoodsNo());
                rewardSetRecordDTO.setMarketType(reqAgentMarketRewardsetDTO.getMarketType());
                rewardSetRecordDTO.setRewardType(reqAgentMarketRewardsetDTO.getRewardType());
                rewardSetRecordDTO.setSetType(reqAgentMarketRewardsetDTO.getSetType());
                rewardSetRecordDTO.setMerchantNo(reqAgentMarketRewardsetDTO.getMerchantNo());
                rewardSetRecordDTO.setStoreNo(reqAgentMarketRewardsetDTO.getStoreNo());
                rewardSetRecordDTO.setModifyBeforeRecord(msgB);
                rewardSetRecordDTO.setModifyAfterRecord(msgA);
                atomReqAgentMarketRewardsetDTO.setRewardSetRecordDTO(rewardSetRecordDTO);
            }
        }
        ExecuteDTO executeDTO = this.atomAgentMarketRewardsetOperatService.save(atomReqAgentMarketRewardsetDTO);
        return executeDTO;
    }

    @Override
    public ExecuteDTO saveListMarketRewardset() {
        return null;
    }

    @Override
    public ExecuteDTO<ResAgentMarketRewardsetDTO> selectGoodsNoByGoodsNo(ReqAgentMarketRewardsetDTO reqAgentMarketRewardsetDTO) {
        AtomReqAgentMarketRewardsetDTO atomReqAgentMarketRewardsetDTO = BeanCopierUtil.copy(reqAgentMarketRewardsetDTO, AtomReqAgentMarketRewardsetDTO.class);
        ExecuteDTO<AtomResAgentMarketRewardsetDTO> executeDTO = atomAgentMarketRewardsetOperatService.selectGoodsNoByGoodsNo(atomReqAgentMarketRewardsetDTO);
        if (!executeDTO.successFlag()){
            ExecuteDTO.error(executeDTO.getStatus(),executeDTO.getMsg());
        }
        return ExecuteDTO.success(BeanCopierUtil.copy(executeDTO.getData(),ResAgentMarketRewardsetDTO.class));
    }

    @Override
    public ExecuteDTO<List<ResGoodsNoByCommissionDTO>> selectGoodsNoByCommission(ReqAgentMarketRewardsetDTO reqAgentMarketRewardsetDTO) {
        AtomReqAgentMarketRewardsetDTO atomReqAgentMarketRewardsetDTO = BeanCopierUtil.copy(reqAgentMarketRewardsetDTO, AtomReqAgentMarketRewardsetDTO.class);
        ExecuteDTO<List<GoodsNoByCommissionDTO>> executeDTO = atomAgentMarketRewardsetOperatService.selectGoodsNoByCommission(atomReqAgentMarketRewardsetDTO);
        if (!executeDTO.successFlag()){
            ExecuteDTO.error(executeDTO.getStatus(),executeDTO.getMsg());
        }
        return ExecuteDTO.success(BeanCopierUtil.copyList(executeDTO.getData(),ResGoodsNoByCommissionDTO.class));
    }
}
