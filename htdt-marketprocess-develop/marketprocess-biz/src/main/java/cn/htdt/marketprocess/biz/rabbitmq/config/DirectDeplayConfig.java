package cn.htdt.marketprocess.biz.rabbitmq.config;

import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * direct模式配置-延迟队列配置
 *
 * <AUTHOR>
 */
@Configuration
public class DirectDeplayConfig {

    public static final String env = System.getProperty("spring.profiles.active");

    /**
     * 正常业务交换机
     **/
    public static final String NORMAL_EXCHANGE_NAME = "marketprocess_normal_exchange";

    //正常业务队列
    /**
     * 短信发送消息队列
     */
    public final static String NORMAL_QUEUE_SMS_SEND_NAME = "marketprocess_normal_queue_sms_send";
    public final static String NORMAL_QUEUE_SMS_SEND_ROUTING_KEY = "marketprocess_normal_queue_sms_send_routingkey";
    /**
     * 微信消息（未支付提醒）消息队列
     */
    public final static String NORMAL_QUEUE_WX_MSG_UNPAID_NAME = "marketprocess_normal_queue_wx_msg_unpaid";
    public final static String NORMAL_QUEUE_WX_MSG_UNPAID_ROUTING_KEY = "marketprocess_normal_queue_wx_msg_unpaid_routingkey";

    /**
     * 短信接受消息队列
     */
    public final static String NORMAL_QUEUE_SMS_ACCEPT_NAME = "marketprocess_normal_queue_sms_accept";
    public final static String NORMAL_QUEUE_SMS_ACCEPT_ROUTING_KEY = "marketprocess_normal_queue_sms_accept_routingkey";

    /**
     * DLX（死信交换机）
     **/
    public static final String DEAD_LETTER_EXCHANGE_NAME = "marketprocess_dead_letter_exchange";

    //DLX queue（死信队列）
    /**
     * 短信发送死信队列
     **/
    public final static String DEAD_LETTER_QUEUE_SMS_SEND_NAME = "marketprocess_dead_letter_queue_sms_send";
    public final static String DEAD_LETTER_QUEUE_SMS_SEND_ROUTING_KEY = "marketprocess_dead_letter_queue_sms_send_routingkey";

    /**
     * 短信发送死信队列
     **/
    public final static String DEAD_LETTER_QUEUE_SMS_ACCEPT_NAME = "marketprocess_dead_letter_queue_sms_accept";
    public final static String DEAD_LETTER_QUEUE_SMS_ACCEPT_ROUTING_KEY = "marketprocess_dead_letter_queue_sms_accept_routingkey";
    /**
     * 微信消息（未支付提醒）死信队列
     **/
    public final static String DEAD_LETTER_QUEUE_WX_MSG_UNPAID_NAME = "marketprocess_dead_letter_queue_wx_msg_unpaid";
    public final static String DEAD_LETTER_QUEUE_WX_MSG_UNPAID_ROUTING_KEY = "marketprocess_dead_letter_queue_wx_msg_unpaid_routingkey";

    //*************normal**************

    /**
     * 创建normal_exchange
     *
     * @return DirectExchange 交换机
     */
    @Bean
    DirectExchange normalExchange() {
        return new DirectExchange(NORMAL_EXCHANGE_NAME);
    }

    /**
     * 创建业务队列（短信发送）
     *
     * @return Queue 队列
     */
    @Bean
    Queue normalQueueSmsSend() {
        return QueueBuilder.durable(NORMAL_QUEUE_SMS_SEND_NAME + env)
                // x-dead-letter-exchange    这里声明当前队列绑定的死信交换机
                .withArgument("x-dead-letter-exchange", DEAD_LETTER_EXCHANGE_NAME)
                // x-dead-letter-routing-key  这里声明当前队列的死信路由key
                .withArgument("x-dead-letter-routing-key", DEAD_LETTER_QUEUE_SMS_SEND_ROUTING_KEY)
                .build();
    }

    /**
     * 创建业务队列（短信接受）
     *
     * @return Queue 队列
     */
    @Bean
    Queue normalQueueSmsAccept() {
        return QueueBuilder.durable(NORMAL_QUEUE_SMS_ACCEPT_NAME)
                // x-dead-letter-exchange    这里声明当前队列绑定的死信交换机
                .withArgument("x-dead-letter-exchange", DEAD_LETTER_EXCHANGE_NAME)
                // x-dead-letter-routing-key  这里声明当前队列的死信路由key
                .withArgument("x-dead-letter-routing-key", DEAD_LETTER_QUEUE_SMS_ACCEPT_ROUTING_KEY)
                .build();
    }

    /**
     * 创建业务队列（微信消息（未支付提醒））
     *
     * @return Queue 队列
     */
    @Bean
    Queue normalQueueWxMsgUnpaid() {
        return QueueBuilder.durable(NORMAL_QUEUE_WX_MSG_UNPAID_NAME)
                // x-dead-letter-exchange    这里声明当前队列绑定的死信交换机
                .withArgument("x-dead-letter-exchange", DEAD_LETTER_EXCHANGE_NAME)
                // x-dead-letter-routing-key  这里声明当前队列的死信路由key
                .withArgument("x-dead-letter-routing-key", DEAD_LETTER_QUEUE_WX_MSG_UNPAID_ROUTING_KEY)
                .build();
    }

    /**
     * 说明：绑定  将队列和交换机绑定, 并设置用于匹配键：NORMAL_QUEUE_SMS_SEND_ROUTING_KEY + env
     *
     * @param normalQueueSmsSend 业务队列（短信发送）
     * @param normalExchange     交换机
     * @return Binding
     */
    @Bean
    Binding bindingQueueSmsSend(Queue normalQueueSmsSend, DirectExchange normalExchange) {
        return BindingBuilder.bind(normalQueueSmsSend)
                .to(normalExchange)
                .with(NORMAL_QUEUE_SMS_SEND_ROUTING_KEY);
    }

    @Bean
    Binding bindingQueueSmsAccept(Queue normalQueueSmsAccept, DirectExchange normalExchange) {
        return BindingBuilder.bind(normalQueueSmsAccept)
                .to(normalExchange)
                .with(NORMAL_QUEUE_SMS_ACCEPT_ROUTING_KEY);
    }

    /**
     * 说明：绑定  将队列和交换机绑定, 并设置用于匹配键：NORMAL_QUEUE_WX_MSG_UNPAID_ROUTING_KEY
     *
     * @param normalQueueWxMsgUnpaid 业务队列（微信消息（未支付提醒））
     * @param normalExchange         交换机
     * @return Binding
     */
    @Bean
    Binding bindingQueueWxMsgUnpaid(Queue normalQueueWxMsgUnpaid, DirectExchange normalExchange) {
        return BindingBuilder.bind(normalQueueWxMsgUnpaid)
                .to(normalExchange)
                .with(NORMAL_QUEUE_WX_MSG_UNPAID_ROUTING_KEY);
    }

    //*************DLX**************

    /**
     * 创建DLX(死信交换机)
     *
     * @return DirectExchange 死信交换机
     */
    @Bean
    DirectExchange deadLetterExchange() {
        return new DirectExchange(DEAD_LETTER_EXCHANGE_NAME);
    }

    /**
     * 声明死信队列：短信发送（DEAD_LETTER_QUEUE_SMS_SEND_NAME）
     *
     * @return Queue
     */
    @Bean
    Queue deadLetterQueueSmsSend() {
        return QueueBuilder.durable(DEAD_LETTER_QUEUE_SMS_SEND_NAME).build();
    }

    /**
     * 声明死信队列：短信接受（DEAD_LETTER_QUEUE_SMS_ACCEPT_NAME）
     *
     * @return Queue
     */
    @Bean
    Queue deadLetterQueueSmsAccept() {
        return QueueBuilder.durable(DEAD_LETTER_QUEUE_SMS_ACCEPT_NAME).build();
    }

    /**
     * 声明死信队列：微信消息（未支付提醒）（DEAD_LETTER_QUEUE_WX_MSG_UNPAID_NAME）
     *
     * @return Queue
     */
    @Bean
    Queue deadLetterQueueWxMsgUnpaid() {
        return QueueBuilder.durable(DEAD_LETTER_QUEUE_WX_MSG_UNPAID_NAME).build();
    }

    /**
     * 说明：绑定  将死信队列和死信交换机绑定, 并设置用于匹配键：DEAD_LETTER_QUEUE_SMS_SEND_ROUTING_KEY
     *
     * @param deadLetterQueueSmsSend 死信队列（短信发送）
     * @param deadLetterExchange     死信交换机
     * @return Binding
     */
    @Bean
    Binding bindingDeadLetterQueueSmsSend(Queue deadLetterQueueSmsSend, DirectExchange deadLetterExchange) {
        return BindingBuilder.bind(deadLetterQueueSmsSend)
                .to(deadLetterExchange)
                .with(DEAD_LETTER_QUEUE_SMS_SEND_ROUTING_KEY);
    }

    /**
     * 说明：绑定  将死信队列和死信交换机绑定, 并设置用于匹配键：DEAD_LETTER_QUEUE_SMS_SEND_ROUTING_KEY
     *
     * @param deadLetterQueueWxMsgUnpaid 死信队列（微信消息（未支付提醒））
     * @param deadLetterExchange         死信交换机
     * @return Binding
     */
    @Bean
    Binding bindingDeadLetterQueueWxMsgUnpaid(Queue deadLetterQueueWxMsgUnpaid, DirectExchange deadLetterExchange) {
        return BindingBuilder.bind(deadLetterQueueWxMsgUnpaid)
                .to(deadLetterExchange)
                .with(DEAD_LETTER_QUEUE_WX_MSG_UNPAID_ROUTING_KEY);
    }
}
