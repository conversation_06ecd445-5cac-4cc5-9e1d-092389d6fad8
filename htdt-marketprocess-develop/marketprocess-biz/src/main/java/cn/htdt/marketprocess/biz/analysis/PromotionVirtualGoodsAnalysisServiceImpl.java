package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.utils.ExecuteDTOUtil;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.ListUtil;
import cn.htdt.marketprocess.api.analysis.PromotionVirtualGoodsAnalysisService;
import cn.htdt.marketprocess.dao.PromotionVirtualGoodsRelationDao;
import cn.htdt.marketprocess.domain.PromotionVirtualGoodsRelationDomain;
import cn.htdt.marketprocess.dto.request.ReqPromotionVirtualGoodsRelationDTO;
import cn.htdt.marketprocess.dto.response.ResPromotionVirtualGoodsRelationDTO;
import cn.htdt.marketprocess.vo.PromotionVirtualGoodsRelationVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-10-10
 */
@DubboService
@Slf4j
public class PromotionVirtualGoodsAnalysisServiceImpl implements PromotionVirtualGoodsAnalysisService {

    @Resource
    private PromotionVirtualGoodsRelationDao promotionVirtualGoodsRelationDao;

    @Override
    public ExecuteDTO<ResPromotionVirtualGoodsRelationDTO> selectOne(ReqPromotionVirtualGoodsRelationDTO queryDto) {
        PromotionVirtualGoodsRelationVo queryVo = BeanCopierUtil.copy(queryDto, PromotionVirtualGoodsRelationVo.class);
        List<PromotionVirtualGoodsRelationDomain> relationDomains = promotionVirtualGoodsRelationDao.selectByParams(queryVo);
        if (ListUtil.isNotEmpty(relationDomains)) {
            return ExecuteDTO.ok(BeanCopierUtil.copy(ListUtil.getFirst(relationDomains), ResPromotionVirtualGoodsRelationDTO.class));
        }
        return ExecuteDTOUtil.error(CommonCode.CODE_10000024);
    }

    @Override
    public ExecuteDTO<List<ResPromotionVirtualGoodsRelationDTO>> selectList(ReqPromotionVirtualGoodsRelationDTO queryDto) {
        PromotionVirtualGoodsRelationVo queryVo = BeanCopierUtil.copy(queryDto, PromotionVirtualGoodsRelationVo.class);
        List<PromotionVirtualGoodsRelationDomain> relationDomains = promotionVirtualGoodsRelationDao.selectByParams(queryVo);
        if (ListUtil.isNotEmpty(relationDomains)) {
            return ExecuteDTO.ok(BeanCopierUtil.copyList(relationDomains, ResPromotionVirtualGoodsRelationDTO.class));
        }
        return ExecuteDTOUtil.error(CommonCode.CODE_10000024);
    }

}
