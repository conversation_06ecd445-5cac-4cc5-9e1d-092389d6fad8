package cn.htdt.marketprocess.biz.constant;

/**
 * Redis 命名空间常量
 */
public class KeysConstant {
    private KeysConstant() {
    }

    public static final String UNDERLINE = "_";
    /**
     * 有效期30天
     */
    public static final Integer EXPIRE_TIME_THIRTY_DAY = 2592000;

    /**
     * 有效期七天
     */
    public static final Integer EXPIRE_TIME_SEVEN_DAY = 604800;

    /**
     * 有效期三天
     */
    public static final Integer EXPIRE_TIME_THREE_DAY = 259200;

    /**
     * 有效期六小时
     */
    public static final Integer EXPIRE_TIME_SIX_HOURS = 21600;

    /**
     * 一小时
     */
    public static final Integer EXPIRE_TIME_ONE_HOURS = 3600;

    /**
     * 半小时
     */
    public static final Integer EXPIRE_TIME_HALF_HOURS = 1800;

    /**
     * 10秒
     */
    public static final Integer TTL_SECOND_10 = 10;

    /**
     * 10秒
     */
    public static final String TTL_SECOND_STRING_10 = "10";

    /**
     * 3秒
     */
    public static final Integer TTL_SECOND_3 = 3;

    /**
     * 活动缓存前缀
     */
    public static final String PROMOTION_PREFIX = "promotion:%s";

    /**
     * 店铺抽奖活动缓存前缀
     */
    public static final String STORE_PROMOTION_PREFIX = "promotion:store:%s_%s";

    /**
     * 用户抽奖活动缓存前缀
     */
    public static final String FAN_PROMOTION_PREFIX = "promotion:store:fan:%s_%s_%s";

    /**
     * 用户抽奖活动缓存前缀(平台)
     */
    public static final String FAN_PROMOTION_PLATFORM_PREFIX = "promotion:store:fan:%s_%s";

    /**
     * 抽奖redis锁
     */
    public static final String LOTTERY_DRAW_LOCK_KEY = "%s_%s";

    /**
     * 分享获取额外次数
     */
    public static final String DAILY_SHARE_TIMES = "DAILY_SHARE_TIMES";

    /**
     * 金币兑换额外次数
     */
    public static final String DAILY_GOLD_EXCHANGE_TIMES = "DAILY_GOLD_EXCHANGE_TIMES";

    /**
     * 积分兑换额外次数
     */
    public static final String DAILY_SCORE_EXCHANGE_TIMES = "DAILY_SCORE_EXCHANGE_TIMES";

    /**
     * 每日免费抽奖剩余次数
     * 抽奖优先消耗
     */
    public static final String DAILY_FREE_DRAW_TIMES = "DAILY_FREE_DRAW_TIMES";

    /**
     * 剩余额外获取的抽奖次数
     */
    public static final String CURRENT_LAST_EXTRA_DRAW_TIMES = "CURRENT_LAST_EXTRA_DRAW_TIMES";

    /**
     * 单日最后一次抽奖时间
     * 用于判断是否需要清空当日抽奖及中奖次数使用
     */
    public static final String DAILY_LAST_DRAW_TIME = "DAILY_LAST_DRAW_TIME";

    /**
     * 当前剩余总抽奖次数
     * 包含之前通过分享及金币兑换未抽奖的次数累加及每日免费抽奖次数
     * 如果是订单抽奖 则为订单抽奖次数
     */
    public static final String CURRENT_LAST_DRAW_TIMES = "CURRENT_LAST_DRAW_TIMES";

    /**
     * 剩余总抽奖次数
     * 配置粉丝总抽奖次数
     * 只与用户每日免费抽奖次数有关联
     */
    public static final String TOTAL_LAST_DRAW_TIMES = "TOTAL_LAST_DRAW_TIMES";

    /**
     * 单店活动期间每日中奖次数
     * 已抽中奖次数
     */
    public static final String CURRENT_STORE_DAILY_WINNING_TIMES = "CURRENT_STORE_DAILY_WINNING_TIMES";

    /**
     * 单店活动期间总计中奖次数
     * 已抽中奖次数
     */
    public static final String CURRENT_STORE_TOTAL_WINNING_TIMES = "CURRENT_STORE_TOTAL_WINNING_TIMES";

    /**
     * 单日最后一次中奖时间
     * 用于判断是否需要清空当日抽奖及中奖次数使用
     */
    public static final String DAILY_LAST_WINNING_TIME = "DAILY_LAST_WINNING_TIME";

    /**
     * 活动来源
     */
    public static final String PROMOTION_SOURCE_TYPE = "PROMOTION_SOURCE_TYPE";

    /**
     * 用户总中奖次数
     */
    public static final String CURRENT_USER_TOTAL_WINNING_TIMES = "CURRENT_USER_TOTAL_WINNING_TIMES";

    /**
     * 用户每日中奖次数
     */
    public static final String CURRENT_USER_DAILY_WINNING_TIMES = "CURRENT_USER_DAILY_WINNING_TIMES";

    /**
     * 每日抽奖订单编号(记录用于第二天清空昨日抽奖次数)
     */
    public static final String DAILY_DRAW_ORDER_NOS = "DAILY_DRAW_ORDER_NOS";

    /**
     * 每日获取的抽奖次数
     * 用于判断订单当日是否还能获取抽奖次数
     */
    public static final String DAILY_DRAW_GET_TIMES = "DAILY_DRAW_GET_TIMES";

    /**
     * 店铺膨胀红包活动缓存前缀（storeNo_fanNo_expandNo）store:fan:expand
     */
    public static final String PROMOTION_EXPAND_FAN_PREFIX = "promotion:expand:%s_%s_%s";


    /**
     * 短信营销-生日节日短信消费key
     */
    public static final String SMS_SEND_CONFIG_NO = "sms:send_configNo:%s";
}