package cn.htdt.marketprocess.biz.operat;

import cn.htdt.common.base.exception.BaseException;
import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.enums.MarketErrorCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.market.VirtualCoinRuleTypeEnum;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqVirtualCoinRuleDTO;
import cn.htdt.marketcenter.dto.response.AtomResVirtualCoinRuleDTO;
import cn.htdt.marketprocess.api.operat.VirtualCoinRuleOperatService;
import cn.htdt.marketprocess.biz.api.VirtualCoinRuleCouponRelationService;
import cn.htdt.marketprocess.biz.utils.VirtualCoinUtil;
import cn.htdt.marketprocess.dao.VirtualCoinRuleCouponRelationDao;
import cn.htdt.marketprocess.domain.VirtualCoinRuleCouponRelationDomain;
import cn.htdt.marketprocess.dto.request.ReqVirtualCoinRuleAndCouponDTO;
import cn.htdt.marketprocess.dto.request.ReqVirtualCoinRuleDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomVirtualCoinRuleAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomVirtualCoinRuleOperatService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/25
 */
@Slf4j
@DubboService
public class VirtualCoinRuleOperatServiceImpl implements VirtualCoinRuleOperatService {
    @Resource
    private AtomVirtualCoinRuleOperatService virtualCoinRuleOperatService;

    @Resource
    private AtomVirtualCoinRuleAnalysisService analysisService;

    @Resource
    private VirtualCoinRuleCouponRelationService virtualCoinRuleCouponRelationService;

    @Resource
    private VirtualCoinRuleCouponRelationDao virtualCoinRuleCouponRelationDao;

    @Resource
    private VirtualCoinUtil virtualCoinUtil;

    /**
     * 橙豆规则插入
     * 20230928 蛋品-赵翔宇-商家橙豆-橙豆规则
     *
     * @param reqVirtualCoinRuleDTO
     * @return
     */
    @Override
    public ExecuteDTO insertVirtualCoinRule(ReqVirtualCoinRuleDTO reqVirtualCoinRuleDTO) {
        log.info("-VirtualCoinRuleOperatServiceImpl-insertVirtualCoinRule-param-start,{}", JSON.toJSONString(reqVirtualCoinRuleDTO));
        AtomReqVirtualCoinRuleDTO atomReqVirtualCoinRuleDTO = BeanCopierUtil.copy(reqVirtualCoinRuleDTO, AtomReqVirtualCoinRuleDTO.class);
        AtomReqVirtualCoinRuleDTO reqVirtualCoinRule = BeanCopierUtil.copy(reqVirtualCoinRuleDTO,AtomReqVirtualCoinRuleDTO.class);

        // 20230928 蛋品-赵翔宇-商家橙豆-橙豆规则
        String virtualCoinRuleType = virtualCoinUtil.getVirtualCoinRuleType(reqVirtualCoinRuleDTO.getStoreNo(), reqVirtualCoinRuleDTO.getMerchantNo(), reqVirtualCoinRuleDTO.getLoginIdentity());
        reqVirtualCoinRule.setRuleType(virtualCoinRuleType);
        // 设置橙豆规则类型
        atomReqVirtualCoinRuleDTO.setRuleType(virtualCoinRuleType);

        // 判断店铺或者商家, 是否存在支付金额或者可用橙豆一样的橙豆规则
        ExecuteDTO<List<AtomResVirtualCoinRuleDTO>> atomResVirtualCoinRuleDTOExecuteDTO =
                analysisService.selectVirtualCoinRules(reqVirtualCoinRule);
        if (atomResVirtualCoinRuleDTOExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(atomResVirtualCoinRuleDTOExecuteDTO.getData())){
            return ExecuteDTO.error(MarketErrorCode.CODE_17000906);
        }
        ExecuteDTO<String> executeDTO = virtualCoinRuleOperatService.insertVirtualCoinRule(atomReqVirtualCoinRuleDTO);
        // 设置生成的唯一标识
        reqVirtualCoinRuleDTO.setVirtualNo(executeDTO.getData());

        // 保存橙豆规则优惠券关联表的数据
        saveVirtualCoinRuleAndCouponRelation(reqVirtualCoinRuleDTO, false);
        log.info("-VirtualCoinRuleOperatServiceImpl-insertVirtualCoinRule-param-end,{}", JSON.toJSONString(reqVirtualCoinRuleDTO));
        return ExecuteDTO.success();
    }

    /**
     * 删除橙豆规则
     *
     * @param reqVirtualCoinRuleDTO
     * @return
     */
    @Override
    public ExecuteDTO deleteVirtualCoinRule(ReqVirtualCoinRuleDTO reqVirtualCoinRuleDTO) {
        log.info("-VirtualCoinRuleOperatServiceImpl-deleteVirtualCoinRule-param-start,{}", JSON.toJSONString(reqVirtualCoinRuleDTO));
        AtomReqVirtualCoinRuleDTO atomReqVirtualCoinRuleDTO = BeanCopierUtil.copy(reqVirtualCoinRuleDTO, AtomReqVirtualCoinRuleDTO.class);
        ExecuteDTO executeDTO = virtualCoinRuleOperatService.deleteVirtualCoinRule(atomReqVirtualCoinRuleDTO);

        removeVirtualCoinRuleAndCouponRelationData(reqVirtualCoinRuleDTO);

        log.info("-VirtualCoinRuleOperatServiceImpl-deleteVirtualCoinRule-param-end,{}", JSON.toJSONString(reqVirtualCoinRuleDTO));
        return ExecuteDTO.success();
    }

    /**
     * 修改橙豆规则
     *
     * 20230928 蛋品-赵翔宇-商家橙豆-橙豆规则
     * 只有商家可以删除商家橙豆规则, 共享会员下的店铺无法删除商家橙豆规则
     * 店铺未加入共享店铺, 可以删除自己的橙豆规则
     *
     * @param reqVirtualCoinRuleDTO
     * @return
     */
    @Override
    public ExecuteDTO updateVirtualCoinRule(ReqVirtualCoinRuleDTO reqVirtualCoinRuleDTO) {
        log.info("-VirtualCoinRuleOperatServiceImpl-updateVirtualCoinRule-param-start,{}", JSON.toJSONString(reqVirtualCoinRuleDTO));
        AtomReqVirtualCoinRuleDTO atomReqVirtualCoinRuleDTO = BeanCopierUtil.copy(reqVirtualCoinRuleDTO, AtomReqVirtualCoinRuleDTO.class);
        AtomReqVirtualCoinRuleDTO reqVirtualCoinRule = BeanCopierUtil.copy(reqVirtualCoinRuleDTO,AtomReqVirtualCoinRuleDTO.class);

        // 20230928 蛋品-赵翔宇-商家橙豆-橙豆规则
        String virtualCoinRuleType = virtualCoinUtil.getVirtualCoinRuleType(reqVirtualCoinRuleDTO.getStoreNo(), reqVirtualCoinRuleDTO.getMerchantNo(), reqVirtualCoinRuleDTO.getLoginIdentity());
        reqVirtualCoinRule.setRuleType(virtualCoinRuleType);

        ExecuteDTO<List<AtomResVirtualCoinRuleDTO>> atomResVirtualCoinRuleDTOExecuteDTO =
                analysisService.selectVirtualCoinRuleWithOutSelfs(reqVirtualCoinRule);
        if (atomResVirtualCoinRuleDTOExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(atomResVirtualCoinRuleDTOExecuteDTO.getData())){
            return ExecuteDTO.error(MarketErrorCode.CODE_17000906);
        }
        // 20230928 蛋品-赵翔宇-商家橙豆-橙豆规则, 设置橙豆规则类型
        atomReqVirtualCoinRuleDTO.setRuleType(virtualCoinRuleType);
        // 注意这里其实是把之前的数据逻辑删除, 重新保存了一条数据, 所以也重新生成了规则编号
        ExecuteDTO<String> updateExecuteDTO = virtualCoinRuleOperatService.updateVirtualCoinRule(atomReqVirtualCoinRuleDTO);
        if (!updateExecuteDTO.successFlag()) {
            return ExecuteDTO.error(updateExecuteDTO.getStatus(), updateExecuteDTO.getMsg());
        }

        // 删除原来的橙豆规则优惠券关联关系
        if (VirtualCoinRuleTypeEnum.MEMBER_SHARING_RULE.getCode().equals(virtualCoinRuleType)) {
            reqVirtualCoinRuleDTO.setStoreNo("");
        }
        removeVirtualCoinRuleAndCouponRelationData(reqVirtualCoinRuleDTO);

        // 重新设置规则编号
        String newVirtualNo = updateExecuteDTO.getData();
        reqVirtualCoinRuleDTO.setVirtualNo(newVirtualNo);

        // 保存新的橙豆规则优惠券关联关系
        saveVirtualCoinRuleAndCouponRelation(reqVirtualCoinRuleDTO, true);

        log.info("-VirtualCoinRuleOperatServiceImpl-updateVirtualCoinRule-param-end,{}", JSON.toJSONString(reqVirtualCoinRuleDTO));
        return ExecuteDTO.success();
    }

    /**
     * 保存橙豆规则优惠券关联表的数据
     *
     * @param reqVirtualCoinRuleDTO 请求参数
     * @param setCreateNoFlag 是否要设置创建人
     */
    private void saveVirtualCoinRuleAndCouponRelation(ReqVirtualCoinRuleDTO reqVirtualCoinRuleDTO, boolean setCreateNoFlag) {
        log.info("-VirtualCoinRuleOperatServiceImpl-saveVirtualCoinRuleAndCouponRelation, reqVirtualCoinRuleDTO: {}", reqVirtualCoinRuleDTO);
        // 保存橙豆规则和优惠券关联数据
        List<ReqVirtualCoinRuleAndCouponDTO> coinRuleAndCouponList = reqVirtualCoinRuleDTO.getVirtualCoinRuleAndCouponList();
        if (CollectionUtils.isNotEmpty(coinRuleAndCouponList)) {
            log.info("-VirtualCoinRuleOperatServiceImpl-saveVirtualCoinRuleAndCouponRelation-batchSaveCoinRuleCouponRelation, coinRuleAndCouponList: {}", coinRuleAndCouponList);
            List<VirtualCoinRuleCouponRelationDomain> saveList = new ArrayList<>();
            VirtualCoinRuleCouponRelationDomain coinRuleCouponRelationDomain;
            for (ReqVirtualCoinRuleAndCouponDTO coinRuleAndCouponDTO : coinRuleAndCouponList) {
                coinRuleCouponRelationDomain = new VirtualCoinRuleCouponRelationDomain();
                BeanUtils.copyProperties(reqVirtualCoinRuleDTO, coinRuleCouponRelationDomain);
                // 橙豆规则和保存都用到了此方法, 手动更新时,保存橙豆优惠券关联表时,需要把修改人设置为创建人
                if (setCreateNoFlag) {
                    coinRuleCouponRelationDomain.setCreateNo(reqVirtualCoinRuleDTO.getModifyNo());
                    coinRuleCouponRelationDomain.setCreateName(reqVirtualCoinRuleDTO.getModifyName());
                }
                coinRuleCouponRelationDomain.setCouponNo(coinRuleAndCouponDTO.getCouponNo());
                coinRuleCouponRelationDomain.setPromotionNo(coinRuleAndCouponDTO.getPromotionNo());
                saveList.add(coinRuleCouponRelationDomain);
            }

            // 批量保存数据
            boolean saveBatch = virtualCoinRuleCouponRelationService.saveBatch(saveList);
            if (!saveBatch) {
                log.error("-VirtualCoinRuleOperatServiceImpl-saveVirtualCoinRuleAndCouponRelation, batchSaveCoinRuleCouponRelation data error, storeNo: {}, virtualNo: {}", reqVirtualCoinRuleDTO.getStoreNo(), reqVirtualCoinRuleDTO.getVirtualNo());
                throw new BaseException(CommonCode.CODE_10000003);
            }
        }
    }

    /**
     * 逻辑删除橙豆规则优惠券关联表的数据
     *
     * @param reqVirtualCoinRuleDTO 请求参数
     */
    private void removeVirtualCoinRuleAndCouponRelationData(ReqVirtualCoinRuleDTO reqVirtualCoinRuleDTO) {
        log.info("-VirtualCoinRuleOperatServiceImpl-removeVirtualRuleAndCouponRelationData, storeNo: {}, virtualNo: {}", reqVirtualCoinRuleDTO.getStoreNo(), reqVirtualCoinRuleDTO.getVirtualNo());

        // 逻辑删除, 方便查询之前的橙豆规则配置的优惠券
        VirtualCoinRuleCouponRelationDomain virtualCoinRuleCouponRelationDomain = new VirtualCoinRuleCouponRelationDomain();
        virtualCoinRuleCouponRelationDomain.setDeleteFlag(NumConstant.TWO);
        virtualCoinRuleCouponRelationDomain.setVirtualNo(reqVirtualCoinRuleDTO.getVirtualNo());
        virtualCoinRuleCouponRelationDomain.setStoreNo(reqVirtualCoinRuleDTO.getStoreNo());
        virtualCoinRuleCouponRelationDomain.setMerchantNo(reqVirtualCoinRuleDTO.getMerchantNo());
        virtualCoinRuleCouponRelationDomain.setModifyNo(reqVirtualCoinRuleDTO.getModifyNo());
        virtualCoinRuleCouponRelationDomain.setModifyName(reqVirtualCoinRuleDTO.getModifyName());
        int updVirtualCoinRuleRelation = virtualCoinRuleCouponRelationDao.updVirtualCoinRuleCouponRelation(virtualCoinRuleCouponRelationDomain);

        log.info("-VirtualCoinRuleOperatServiceImpl-removeVirtualRuleAndCouponRelationData, updVirtualCoinRuleRelation: {}", updVirtualCoinRuleRelation);
    }
}
