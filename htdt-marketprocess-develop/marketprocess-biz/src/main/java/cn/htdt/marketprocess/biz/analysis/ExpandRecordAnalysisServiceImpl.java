package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqExpandRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomResExpandRecordDTO;
import cn.htdt.marketprocess.api.analysis.ExpandRecordAnalysisService;
import cn.htdt.marketprocess.dto.request.ReqExpandRecordDTO;
import cn.htdt.marketprocess.dto.request.ReqExpandRuleDetailDTO;
import cn.htdt.marketprocess.dto.response.ResExpandRecordCountDTO;
import cn.htdt.marketprocess.dto.response.ResExpandRecordDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomExpandRecordAnalysisService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 膨胀红包发起记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Slf4j
@DubboService
public class ExpandRecordAnalysisServiceImpl implements ExpandRecordAnalysisService {

    /**
     * 膨胀红包发起记录
     */
    @Resource
    private AtomExpandRecordAnalysisService atomExpandRecordAnalysisService;

    @Override
    public ExecuteDTO<ResExpandRecordDTO> getOneExpandRecord(ReqExpandRecordDTO reqExpandRecordDTO) {
        log.info("-AtomExpandRecordAnalysisServiceImpl-getOneExpandRecord-param={}", JSON.toJSONString(reqExpandRecordDTO));
        AtomReqExpandRecordDTO atomReqExpandRecordDTO = BeanCopierUtil.copy(reqExpandRecordDTO, AtomReqExpandRecordDTO.class);
        ExecuteDTO<AtomResExpandRecordDTO> executeDTO = atomExpandRecordAnalysisService.getOneExpandRecord(atomReqExpandRecordDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResExpandRecordDTO resExpandRecordDTO = BeanCopierUtil.copy(executeDTO.getData(), ResExpandRecordDTO.class);
        log.info("-AtomExpandRecordAnalysisServiceImpl-getOneExpandRecord-return={}", JSON.toJSONString(resExpandRecordDTO));
        return ExecuteDTO.success(resExpandRecordDTO);
    }

    @Override
    public ExecuteDTO<ExecutePageDTO<ResExpandRecordDTO>> getExpandRecordPage(ReqExpandRecordDTO reqExpandRecordDTO) {
        log.info("-AtomExpandRecordAnalysisServiceImpl-getExpandRecordPage-param={}", JSON.toJSONString(reqExpandRecordDTO));
        ExecutePageDTO<ResExpandRecordDTO> executePageDTO = new ExecutePageDTO<>();
        AtomReqExpandRecordDTO atomReqExpandRecordDTO = BeanCopierUtil.copy(reqExpandRecordDTO, AtomReqExpandRecordDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResExpandRecordDTO>> executeDTO = atomExpandRecordAnalysisService.getExpandRecordPage(atomReqExpandRecordDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        List<ResExpandRecordDTO> resList = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResExpandRecordDTO.class);
        log.info("-AtomExpandRecordAnalysisServiceImpl-getExpandRecordPage-return={}", JSON.toJSONString(resList));
        executePageDTO.setRows(resList);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    @Override
    public ExecuteDTO<Integer> getCurrentExpandRecordCount(ReqExpandRecordDTO reqExpandRecordDTO) {
        log.info("-AtomExpandRecordAnalysisServiceImpl-getCurrentExpandRecordCount-param={}", JSON.toJSONString(reqExpandRecordDTO));
        AtomReqExpandRecordDTO atomReqExpandRecordDTO = BeanCopierUtil.copy(reqExpandRecordDTO, AtomReqExpandRecordDTO.class);
        ExecuteDTO<Integer> executeDTO = atomExpandRecordAnalysisService.getCurrentExpandRecordCount(atomReqExpandRecordDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        log.info("-AtomExpandRecordAnalysisServiceImpl-getCurrentExpandRecordCount-return={}", JSON.toJSONString(executeDTO));
        return ExecuteDTO.success(executeDTO.getData());
    }

    @Override
    public ExecuteDTO<Integer> getExpandRecordTotalCount(ReqExpandRecordDTO reqExpandRecordDTO) {
        log.info("-AtomExpandRecordAnalysisServiceImpl-getExpandRecordTotalCount-param={}", JSON.toJSONString(reqExpandRecordDTO));
        AtomReqExpandRecordDTO atomReqExpandRecordDTO = BeanCopierUtil.copy(reqExpandRecordDTO, AtomReqExpandRecordDTO.class);
        ExecuteDTO<Integer> executeDTO = atomExpandRecordAnalysisService.getExpandRecordTotalCount(atomReqExpandRecordDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        log.info("-AtomExpandRecordAnalysisServiceImpl-getExpandRecordTotalCount-return={}", JSON.toJSONString(executeDTO));
        return ExecuteDTO.success(executeDTO.getData());
    }

    @Override
    public ExecuteDTO<ResExpandRecordCountDTO> getUserExpandRecordCount(ReqExpandRecordDTO reqExpandRecordDTO) {
        log.info("-AtomExpandRecordAnalysisServiceImpl-getUserExpandRecordCount-param={}", JSON.toJSONString(reqExpandRecordDTO));
        AtomReqExpandRecordDTO atomReqExpandRecordDTO = BeanCopierUtil.copy(reqExpandRecordDTO, AtomReqExpandRecordDTO.class);
        ExecuteDTO<Integer> executeDTO = atomExpandRecordAnalysisService.getCurrentExpandRecordCount(atomReqExpandRecordDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        Integer userDailyTimes = executeDTO.getData() == null ? NumConstant.ZERO : executeDTO.getData();
        log.info("-AtomExpandRecordAnalysisServiceImpl-getUserExpandRecordCount-getCurrentExpandRecordCount-return={}", JSON.toJSONString(executeDTO));
        ExecuteDTO<Integer> totalExecuteDTO = atomExpandRecordAnalysisService.getExpandRecordTotalCount(atomReqExpandRecordDTO);
        if (!totalExecuteDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        Integer userTotalTimes = totalExecuteDTO.getData() == null ? NumConstant.ZERO : totalExecuteDTO.getData();
        log.info("-AtomExpandRecordAnalysisServiceImpl-getUserExpandRecordCount-getExpandRecordTotalCount-return={}", JSON.toJSONString(executeDTO));
        ResExpandRecordCountDTO dto = new ResExpandRecordCountDTO();
        dto.setPromotionNo(reqExpandRecordDTO.getPromotionNo());
        dto.setFanNo(reqExpandRecordDTO.getFanNo());
        dto.setUserDailyTimes(userDailyTimes);
        dto.setUserTotalTimes(userTotalTimes);
        return ExecuteDTO.success(dto);
    }

    /**
     * 膨胀红包兑换记录
     *
     * @param reqDTO
     * @return
     * @auther 卜金隆
     */
    @Override
    public ExecuteDTO<List<ResExpandRecordDTO>> getExchangeRecord(ReqExpandRuleDetailDTO reqDTO) {
        log.info("-AtomExpandRecordAnalysisServiceImpl-getExchangeRecord-param={}", JSON.toJSONString(reqDTO));
        AtomReqExpandRecordDTO atomReqExpandRecordDTO = BeanCopierUtil.copy(reqDTO, AtomReqExpandRecordDTO.class);
        atomReqExpandRecordDTO.setNoPage();
        atomReqExpandRecordDTO.setExchangeStatus(WhetherEnum.YES.getCode());
        ExecuteDTO<ExecutePageDTO<AtomResExpandRecordDTO>> executeDTO = atomExpandRecordAnalysisService.getExpandRecordPage(atomReqExpandRecordDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        List<ResExpandRecordDTO> resList = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResExpandRecordDTO.class);
        log.info("-AtomExpandRecordAnalysisServiceImpl-getExchangeRecord-return={}", JSON.toJSONString(resList));
        return ExecuteDTO.success(resList);
    }
}
