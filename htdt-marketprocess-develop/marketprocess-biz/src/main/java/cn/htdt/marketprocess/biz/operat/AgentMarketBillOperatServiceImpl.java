package cn.htdt.marketprocess.biz.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqAgentMarketBillDTO;
import cn.htdt.marketprocess.api.operat.AgentMarketBillOperatService;
import cn.htdt.marketprocess.dto.request.ReqAgentMarketBillDTO;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomAgentMarketBillOperatService;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2021-01-15
 * @Description 任务和营销清单原子操作服务
 */
@DubboService
public class AgentMarketBillOperatServiceImpl implements AgentMarketBillOperatService {

    @Resource
    private AtomAgentMarketBillOperatService atomAgentMarketBillOperatService;

    @Override
    public ExecuteDTO saveAgentMarketBill(ReqAgentMarketBillDTO reqAgentMarketBillDTO) {
        AtomReqAgentMarketBillDTO atomReqAgentMarketBillDTO = BeanCopierUtil.copy(reqAgentMarketBillDTO, AtomReqAgentMarketBillDTO.class);
        ExecuteDTO executeDTO = atomAgentMarketBillOperatService.saveAgentMarketBill(atomReqAgentMarketBillDTO);
        return executeDTO;
    }

}
