package cn.htdt.marketprocess.biz.operat;

import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.enums.market.PromotionStatusEnum;
import cn.htdt.marketcenter.dto.request.AtomReqPromotionInfoDTO;
import cn.htdt.marketprocess.api.operat.LimitTimePromotionOperateService;
import cn.htdt.marketprocess.biz.conversion.LimitTimePromotionAssert;
import cn.htdt.marketprocess.dto.request.ReqPromotionInfoDTO;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomPromotionInfoOperatService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

/**
 * 限时购活动操作服务
 *
 * <AUTHOR>
 * @date 2021-10-28
 */
@Slf4j
@DubboService
public class LimitTimePromotionOperateServiceImpl implements LimitTimePromotionOperateService {

    @Autowired
    private LimitTimePromotionAssert limitTimePromotionAssert;

    @Resource
    private AtomPromotionInfoOperatService atomPromotionInfoOperatService;

    /**
     * 限时购活动状态更新
     *
     * @param reqDTO 请求参数
     * @return
     * <AUTHOR>
     * @date 2021-10-28
     */
    @Override
    public ExecuteDTO updateLimitTimeStatus(ReqPromotionInfoDTO reqDTO) {
        log.info("updateLimitTimeStatus-入参：{}", reqDTO);
        // 入参校验
        limitTimePromotionAssert.updateLimitTimeStatusAssert(reqDTO);

        // 更新活动状态：更新成非草稿状态
        AtomReqPromotionInfoDTO atomReqDTO = new AtomReqPromotionInfoDTO();
        atomReqDTO.setPromotionNo(reqDTO.getPromotionNo());
        atomReqDTO.setStatus(reqDTO.getStatus());
        atomReqDTO.setOldStatus(PromotionStatusEnum.ONLY_SAVED.getCode());
        ExecuteDTO executeDTO = atomPromotionInfoOperatService.updateGoodsPromotionInfo(atomReqDTO);
        if (null == executeDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }

        // 成功响应
        return ExecuteDTO.success();
    }
}
