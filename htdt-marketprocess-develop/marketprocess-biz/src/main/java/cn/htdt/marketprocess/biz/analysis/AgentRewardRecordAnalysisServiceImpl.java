package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.OrderStatusEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.market.MarketTypeEnum;
import cn.htdt.common.enums.market.RewardStatusFlagEnum;
import cn.htdt.common.enums.market.RewardTypeEnum;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.goodsprocess.api.analysis.GoodsAnalysisService;
import cn.htdt.goodsprocess.dto.request.goodsmedia.ReqGoodsMediaDTO;
import cn.htdt.marketcenter.dto.request.*;
import cn.htdt.marketcenter.dto.response.*;
import cn.htdt.marketprocess.api.analysis.AgentRewardRecordAnalysisService;
import cn.htdt.marketprocess.dto.request.ReqAgentRewardDetailsDTO;
import cn.htdt.marketprocess.dto.request.ReqAgentRewardManageDTO;
import cn.htdt.marketprocess.dto.request.ReqAgentRewardRecordDTO;
import cn.htdt.marketprocess.dto.request.ReqStoreAgentRewardDetailDTO;
import cn.htdt.marketprocess.dto.response.*;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomAgentRewardAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomAgentRewardRecordAnalysisService;
import cn.htdt.ordercenter.dto.request.AtomReqSoDTO;
import cn.htdt.ordercenter.dto.response.AtomResSoDTO;
import cn.htdt.orderprocess.api.LegacyOrderCenterService;
import cn.htdt.userprocess.api.UserPublicService;
import cn.htdt.userprocess.dto.request.UnEffectiveAgentDTO;
import cn.htdt.userprocess.dto.response.UnEffectiveAgentResponse;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021-01-15
 * @Description 代理人酬劳过程信息原子查询服务实现类
 */
@Slf4j
@DubboService
public class AgentRewardRecordAnalysisServiceImpl implements AgentRewardRecordAnalysisService {

    /**
     * 代理人酬劳过程信息查询原子服务
     */
    @Resource
    private AtomAgentRewardRecordAnalysisService atomAgentRewardRecordAnalysisService;
    @Resource
    private AtomAgentRewardAnalysisService atomAgentRewardAnalysisService;

    @DubboReference
    private UserPublicService userPublicService;
    @DubboReference
    private LegacyOrderCenterService legacyOrderCenterService;

    @DubboReference
    private GoodsAnalysisService goodsAnalysisService;

    @Override
    public ExecuteDTO<ExecutePageDTO<ResAgentRewardRecordDTO>> getAgentRewardRecordListByPage(ReqAgentRewardRecordDTO reqAgentRewardRecordDTO) {
        AtomReqAgentRewardRecordDTO atomReqPageAgentRewardRecordDTO = BeanCopierUtil.copy(reqAgentRewardRecordDTO, AtomReqAgentRewardRecordDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResAgentRewardRecordDTO>> executeDTO =  atomAgentRewardRecordAnalysisService.getAgentRewardRecordListByPage(atomReqPageAgentRewardRecordDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ExecutePageDTO<ResAgentRewardRecordDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResAgentRewardRecordDTO> resAgentRewardRecordDTOList = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResAgentRewardRecordDTO.class);
        executePageDTO.setRows(resAgentRewardRecordDTOList);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }
    /**
     * @param reqAgentRewardManageDTO
     * @Description : 代理人酬劳管理列表查询
     * <AUTHOR> 高繁
     * @date : 2021/1/28 11:08
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResAgentRewardManageDTO>> getAgentRewardManageByParams(ReqAgentRewardManageDTO reqAgentRewardManageDTO) {
        //代理人酬劳信息列表
        UnEffectiveAgentDTO unEffectiveAgentDTO = BeanCopierUtil.copy(reqAgentRewardManageDTO, UnEffectiveAgentDTO.class);
        unEffectiveAgentDTO.setAgentStatus(String.valueOf(NumConstant.ONE));
        unEffectiveAgentDTO.setFanName(reqAgentRewardManageDTO.getAgentName());
        unEffectiveAgentDTO.setPhone(reqAgentRewardManageDTO.getAgentPhone());
        ExecuteDTO<ExecutePageDTO<UnEffectiveAgentResponse>> executeDTO = userPublicService.queryUnEffectiveAgentList(unEffectiveAgentDTO);
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        //参数转换
        ExecutePageDTO<ResAgentRewardManageDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResAgentRewardManageDTO> resAgentRewardRecordDTOList = this.agentInfoDtoTransfer(executeDTO.getData().getRows(),reqAgentRewardManageDTO,ResAgentRewardManageDTO.class);
        executePageDTO.setRows(resAgentRewardRecordDTOList);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }
    /**
     * @param reqAgentRewardDTO
     * @Description : 根据代理人编号、店铺查询代理人酬劳明细
     * <AUTHOR> 高繁
     * @date : 2021/2/2 11:34
     */
    @Override
    public ExecuteDTO<ResAgentRewardDetailsDTO> getAgentRewardDetailsByStoreNoAgentNo(ReqAgentRewardDetailsDTO reqAgentRewardDTO) {
        //代理人酬劳分销商品数量查询
        ResAgentRewardDetailsDTO resAgentRewardDetailsDTO = new ResAgentRewardDetailsDTO();
        AtomReqAgentRewardGoodsNumDTO reqAgentRewardGoodsNumDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardGoodsNumDTO.class);
        List<String> agentNoList = new ArrayList<>();
        agentNoList.add(reqAgentRewardDTO.getAgentNo());
        List<String> storeNoList = new ArrayList<>();
        storeNoList.add(reqAgentRewardDTO.getStoreNo());
        reqAgentRewardGoodsNumDTO.setStoreNoList(storeNoList);
        reqAgentRewardGoodsNumDTO.setAgentNoList(agentNoList);
        log.info("查询分销数量接口入参 {}",JSON.toJSONString(reqAgentRewardGoodsNumDTO));
        ExecuteDTO<List<AtomResAgentRewardGoodsNumDTO>> executeDTO =  atomAgentRewardAnalysisService.getAgentRewardGoodsNum(reqAgentRewardGoodsNumDTO);
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        //参数转换
        List<AtomResAgentRewardGoodsNumDTO> atomResAgentRewardGoodsNumDTOList = executeDTO.getData();
        if (CollectionUtils.isNotEmpty(atomResAgentRewardGoodsNumDTOList)){
            atomResAgentRewardGoodsNumDTOList.forEach(atomResAgentRewardGoodsNumDTO -> {
                if (MarketTypeEnum.CloudDistribution.getCode().equals(atomResAgentRewardGoodsNumDTO.getMarketType())){
                    resAgentRewardDetailsDTO.setCloudGoodsNum(atomResAgentRewardGoodsNumDTO.getGoodsNum());
                } else if (MarketTypeEnum.StoreDistribution.getCode().equals(atomResAgentRewardGoodsNumDTO.getMarketType())){
                    resAgentRewardDetailsDTO.setDistributeGoodsNum(atomResAgentRewardGoodsNumDTO.getGoodsNum());
                }
            });
        }
        //查询代理人预计酬劳
        AtomReqStoreAgentRewardSumDTO atomReqStoreAgentRewardSumDTO = new AtomReqStoreAgentRewardSumDTO();
        atomReqStoreAgentRewardSumDTO.setStoreNoList(storeNoList);
        atomReqStoreAgentRewardSumDTO.setAgentNoList(agentNoList);
        atomReqStoreAgentRewardSumDTO.setRewardStatus(NumConstant.ONE);
        log.info("查询代理人预计酬劳接口入参 {}",JSON.toJSONString(atomReqStoreAgentRewardSumDTO));
        ExecuteDTO<List<AtomResStoreAgentRewardSumDTO>> sumExecuteDto = atomAgentRewardAnalysisService.getStoreAgentRewardSum(atomReqStoreAgentRewardSumDTO);
        log.info("查询代理人预计酬劳接口结果 {}",JSON.toJSONString(sumExecuteDto));
        //判断状态
        if (!sumExecuteDto.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        //参数转换
        List<AtomResStoreAgentRewardSumDTO> atomResStoreAgentRewardSumDTOS = sumExecuteDto.getData();
        if (CollectionUtils.isNotEmpty(atomResStoreAgentRewardSumDTOS)){
            atomResStoreAgentRewardSumDTOS.forEach(atomResAgentRewardGoodsNumDTO -> {
                if (RewardTypeEnum.YJ.getCode().equals(atomResAgentRewardGoodsNumDTO.getRewardType())){
                    resAgentRewardDetailsDTO.setPredictCommission(atomResAgentRewardGoodsNumDTO.getRewardValue());
                }else if (RewardTypeEnum.FWQ.getCode().equals(atomResAgentRewardGoodsNumDTO.getRewardType())){
                    resAgentRewardDetailsDTO.setPredictServiceCoupon(atomResAgentRewardGoodsNumDTO.getRewardName());
                    resAgentRewardDetailsDTO.setPredictServiceCouponNum(atomResAgentRewardGoodsNumDTO.getRewardValue().intValue());
                }  else if (RewardTypeEnum.HJB.getCode().equals(atomResAgentRewardGoodsNumDTO.getRewardType())){
                    resAgentRewardDetailsDTO.setPredictGoldCoins(atomResAgentRewardGoodsNumDTO.getRewardValue().intValue());
                }  else if (RewardTypeEnum.LP.getCode().equals(atomResAgentRewardGoodsNumDTO.getRewardType())){
                    resAgentRewardDetailsDTO.setPredictGift(atomResAgentRewardGoodsNumDTO.getRewardName());
                    resAgentRewardDetailsDTO.setPredictGiftNum(atomResAgentRewardGoodsNumDTO.getRewardValue().intValue());
                }  else if (RewardTypeEnum.XJQ.getCode().equals(atomResAgentRewardGoodsNumDTO.getRewardType())){
                    resAgentRewardDetailsDTO.setPredictMoneyCoupon(atomResAgentRewardGoodsNumDTO.getRewardName());
                    resAgentRewardDetailsDTO.setPredictMoneyCouponNum(atomResAgentRewardGoodsNumDTO.getRewardValue().intValue());
                }
            });
        }
        //已获得酬劳
        atomReqStoreAgentRewardSumDTO.setRewardStatus(NumConstant.TWO);
        log.info("查询代理人已获得酬劳接口入参 {}",JSON.toJSONString(atomReqStoreAgentRewardSumDTO));
        sumExecuteDto = atomAgentRewardAnalysisService.getStoreAgentRewardSum(atomReqStoreAgentRewardSumDTO);
        log.info("查询代理人已获得酬劳接口结果 {}",JSON.toJSONString(sumExecuteDto));
        //判断状态
        if (!sumExecuteDto.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        //参数转换
        atomResStoreAgentRewardSumDTOS = sumExecuteDto.getData();
        if (CollectionUtils.isNotEmpty(atomResStoreAgentRewardSumDTOS)){
            atomResStoreAgentRewardSumDTOS.forEach(atomResAgentRewardGoodsNumDTO -> {
                if (RewardTypeEnum.YJ.getCode().equals(atomResAgentRewardGoodsNumDTO.getRewardType())){
                    resAgentRewardDetailsDTO.setCommission(atomResAgentRewardGoodsNumDTO.getRewardValue());
                }else if (RewardTypeEnum.FWQ.getCode().equals(atomResAgentRewardGoodsNumDTO.getRewardType())){
                    resAgentRewardDetailsDTO.setServiceCoupon(atomResAgentRewardGoodsNumDTO.getRewardName());
                    resAgentRewardDetailsDTO.setServiceCouponNum(atomResAgentRewardGoodsNumDTO.getRewardValue().intValue());
                }  else if (RewardTypeEnum.HJB.getCode().equals(atomResAgentRewardGoodsNumDTO.getRewardType())){
                    resAgentRewardDetailsDTO.setGoldCoins(atomResAgentRewardGoodsNumDTO.getRewardValue().intValue());
                }  else if (RewardTypeEnum.LP.getCode().equals(atomResAgentRewardGoodsNumDTO.getRewardType())){
                    resAgentRewardDetailsDTO.setGift(atomResAgentRewardGoodsNumDTO.getRewardName());
                    resAgentRewardDetailsDTO.setGiftNum(atomResAgentRewardGoodsNumDTO.getRewardValue().intValue());
                }  else if (RewardTypeEnum.XJQ.getCode().equals(atomResAgentRewardGoodsNumDTO.getRewardType())){
                    resAgentRewardDetailsDTO.setMoneyCoupon(atomResAgentRewardGoodsNumDTO.getRewardName());
                    resAgentRewardDetailsDTO.setMoneyCouponNum(atomResAgentRewardGoodsNumDTO.getRewardValue().intValue());
                }
            });
        }
        return ExecuteDTO.success(resAgentRewardDetailsDTO);
    }
    /**
     * @param reqStoreAgentRewardDetailDTO
     * @Description : 查询代理人在此店铺下的酬劳明细列表
     * <AUTHOR> 高繁
     * @date : 2021/2/17 15:57
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResStoreAgentRewardDetailListDTO>> getAgentRewardDetailList(ReqStoreAgentRewardDetailDTO reqStoreAgentRewardDetailDTO) {
        //代理人酬劳信息列表
        AtomReqStoreAgentRewardDetailDTO atomReqStoreAgentRewardDetailDTO= BeanCopierUtil.copy(reqStoreAgentRewardDetailDTO, AtomReqStoreAgentRewardDetailDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResStoreAgentRewardDetailListDTO>> executeDTO =  atomAgentRewardAnalysisService.getAgentRewardDetailList(atomReqStoreAgentRewardDetailDTO);
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        //参数转换
        ExecutePageDTO<ResStoreAgentRewardDetailListDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResStoreAgentRewardDetailListDTO> resAgentRewardRecordDTOList = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResStoreAgentRewardDetailListDTO.class);
        List<String> orderNoList = new ArrayList<>();
        resAgentRewardRecordDTOList.forEach(resStoreAgentRewardDetailListDTO ->{
            if (StringUtils.isNotBlank(resStoreAgentRewardDetailListDTO.getOrderNo())) {
                orderNoList.add(resStoreAgentRewardDetailListDTO.getOrderNo());
            }
            if (MarketTypeEnum.PullNew.getCode().equals(resStoreAgentRewardDetailListDTO.getMarketType())){
                resStoreAgentRewardDetailListDTO.setTaskOrGoodsName(MarketTypeEnum.PullNew.getType());
            }
            resStoreAgentRewardDetailListDTO.setRewardStatusStr(RewardStatusFlagEnum.getByCode(resStoreAgentRewardDetailListDTO.getRewardStatusFlag()).getType());

        });
        //查询订单状态
        if (CollectionUtils.isNotEmpty(orderNoList)){
            AtomReqSoDTO reqSoDTO = new AtomReqSoDTO();
            reqSoDTO.setOrderNos(orderNoList);
            ExecuteDTO<List<AtomResSoDTO>> listExecuteDTO = legacyOrderCenterService.getSoList(reqSoDTO);
            if (listExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(listExecuteDTO.getData())) {
                List<AtomResSoDTO> orderlist = listExecuteDTO.getData();
                resAgentRewardRecordDTOList = resAgentRewardRecordDTOList.stream().map(rewardDetailListDTO -> {
                    orderlist.stream().filter(order->
                            Objects.equals(rewardDetailListDTO.getOrderNo(),order.getOrderNo())).forEach(
                            s-> rewardDetailListDTO.setOrderStatus(OrderStatusEnum.getByCode(s.getOrderStatus()).getName()));
                    return rewardDetailListDTO;
                }).collect(Collectors.toList());
            }
        }
        //查询粉丝手机号和粉丝操作时间
        for (ResStoreAgentRewardDetailListDTO resStoreAgentRewardDetailListDTO : resAgentRewardRecordDTOList) {
            AtomReqAgentRewardRecordDTO reqAgentRewardRecordDTO = new AtomReqAgentRewardRecordDTO();
            reqAgentRewardRecordDTO.setRewardRecordNo(resStoreAgentRewardDetailListDTO.getRewardRecordNo());
            ExecuteDTO<ExecutePageDTO<AtomResAgentRewardRecordDTO>> recordExecuteDTO = atomAgentRewardRecordAnalysisService.getAgentRewardRecordListByPage(reqAgentRewardRecordDTO);
            if (recordExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(recordExecuteDTO.getData().getRows())) {
                AtomResAgentRewardRecordDTO atomResAgentRewardRecordDTO = recordExecuteDTO.getData().getRows().get(NumConstant.ZERO);
                resStoreAgentRewardDetailListDTO.setFanName(atomResAgentRewardRecordDTO.getFanName());
                resStoreAgentRewardDetailListDTO.setFanMobile(atomResAgentRewardRecordDTO.getFanMobile());
                resStoreAgentRewardDetailListDTO.setFansOperateDate(atomResAgentRewardRecordDTO.getFansOperateDate());
            }
        }
        //查询商品主图
        for (ResStoreAgentRewardDetailListDTO resStoreAgentRewardDetailListDTO : resAgentRewardRecordDTOList) {
            ReqGoodsMediaDTO reqGoodsMediaDTO = new ReqGoodsMediaDTO();
            reqGoodsMediaDTO.setGoodsNo(resStoreAgentRewardDetailListDTO.getTaskOrGoodsNo());
            ExecuteDTO<String> goodsExecuteDTO = goodsAnalysisService.selectMainPictureByGoodsNoList(reqGoodsMediaDTO);
            if (goodsExecuteDTO.successFlag() && goodsExecuteDTO.getData() != null) {
                resStoreAgentRewardDetailListDTO.setMainPictureUrl(goodsExecuteDTO.getData());
            }
        }
        executePageDTO.setRows(resAgentRewardRecordDTOList);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }
    /**
     * @param reqAgentRewardManageDTO
     * @Description : 查询失效代理人列表
     * <AUTHOR> 高繁
     * @date : 2021/2/18 13:36
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResAgentRewardDisableDTO>> getAgentRewardDisableByParams(ReqAgentRewardManageDTO reqAgentRewardManageDTO) {
        UnEffectiveAgentDTO unEffectiveAgentDTO = BeanCopierUtil.copy(reqAgentRewardManageDTO, UnEffectiveAgentDTO.class);
        unEffectiveAgentDTO.setAgentStatus(String.valueOf(NumConstant.TWO));
        unEffectiveAgentDTO.setFanName(reqAgentRewardManageDTO.getAgentName());
        unEffectiveAgentDTO.setPhone(reqAgentRewardManageDTO.getAgentPhone());
        ExecuteDTO<ExecutePageDTO<UnEffectiveAgentResponse>> executeDTO = userPublicService.queryUnEffectiveAgentList(unEffectiveAgentDTO);
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ExecutePageDTO<ResAgentRewardDisableDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResAgentRewardDisableDTO> resAgentRewardDisableDTOS = this.agentInfoDtoTransfer(executeDTO.getData().getRows(),reqAgentRewardManageDTO,ResAgentRewardDisableDTO.class);
        //查询
        executePageDTO.setRows(resAgentRewardDisableDTOS);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }
    /**
     * @param reqAgentRewardDTO
     * @Description : 根据代理人编号+店铺编号查询酬劳扩展信息
     * <AUTHOR> 高繁
     * @date : 2021/2/25 16:42
     */
    @Override
    public ExecuteDTO<ResAgentRewardDetailExpandDto> getAgentRewardDetailExpand(ReqAgentRewardDetailsDTO reqAgentRewardDTO) {
        //代理人酬劳扩展信息查询
        //预估佣金
        ResAgentRewardDetailExpandDto resAgentRewardDetailsDTO = new ResAgentRewardDetailExpandDto();

        //已获得佣金
        List<ResAgentRewardCommissionDto> agentRewardCommissionDtos = new ArrayList<>();
        //预计获得服务券
        List<ResAgentRewardGiftDto> preServiceCouponDtos = new ArrayList<>();
        List<ResAgentRewardGiftDto> preGiftDtos = new ArrayList<>();
        List<ResAgentRewardGiftDto> preMoneyCouponDtos = new ArrayList<>();
        //已获得服务券
        List<ResAgentRewardGiftDto> serviceCouponDtos = new ArrayList<>();
        List<ResAgentRewardGiftDto> giftDtos = new ArrayList<>();
        List<ResAgentRewardGiftDto> moneyCouponDtos = new ArrayList<>();
        AtomReqAgentRewardGoodsNumDTO reqAgentRewardGoodsNumDTO = BeanCopierUtil.copy(reqAgentRewardDTO, AtomReqAgentRewardGoodsNumDTO.class);
        reqAgentRewardGoodsNumDTO.setRewardStatus(NumConstant.ONE);
        ExecuteDTO<List<AtomResAgentRewardCommissionDto>> sumExecuteDto = atomAgentRewardAnalysisService.getAgentRewardCommissionList(reqAgentRewardGoodsNumDTO);
        //判断状态
        if (!sumExecuteDto.successFlag()) {
            return ExecuteDTO.error(sumExecuteDto.getStatus(), sumExecuteDto.getMsg());
        }
        //参数转换
        List<AtomResAgentRewardCommissionDto> atomResAgentRewardCommissionDtos = sumExecuteDto.getData();
        List<ResAgentRewardCommissionDto> preAgentRewardCommissionDtos = BeanCopierUtil.copyList(this.commissionDtoTransfer(atomResAgentRewardCommissionDtos),ResAgentRewardCommissionDto.class);
        resAgentRewardDetailsDTO.setPredictCommissionList(preAgentRewardCommissionDtos);
        //已获得酬劳
        reqAgentRewardGoodsNumDTO.setRewardStatus(NumConstant.TWO);
        sumExecuteDto = atomAgentRewardAnalysisService.getAgentRewardCommissionList(reqAgentRewardGoodsNumDTO);
        //判断状态
        if (!sumExecuteDto.successFlag()) {
            return ExecuteDTO.error(sumExecuteDto.getStatus(), sumExecuteDto.getMsg());
        }
        atomResAgentRewardCommissionDtos = sumExecuteDto.getData();
        agentRewardCommissionDtos = BeanCopierUtil.copyList(this.commissionDtoTransfer(atomResAgentRewardCommissionDtos),ResAgentRewardCommissionDto.class);
        resAgentRewardDetailsDTO.setCommissionList(agentRewardCommissionDtos);
        //预计获得礼品+券
        reqAgentRewardGoodsNumDTO.setRewardStatus(NumConstant.ONE);
        ExecuteDTO<List<AtomResAgentRewardGiftDto>>  preGiftExecuteDto = atomAgentRewardAnalysisService.getAgentRewardGiftList(reqAgentRewardGoodsNumDTO);
        if (!preGiftExecuteDto.successFlag()) {
            return ExecuteDTO.error(preGiftExecuteDto.getStatus(), preGiftExecuteDto.getMsg());
        }
        //参数转换
        List<AtomResAgentRewardGiftDto> atomResAgentRewardGiftDtos = preGiftExecuteDto.getData();
        if (CollectionUtils.isNotEmpty(atomResAgentRewardGiftDtos)){
            atomResAgentRewardGiftDtos.forEach(atomResAgentRewardGiftDto -> {
                ResAgentRewardGiftDto resAgentRewardGiftDto = new ResAgentRewardGiftDto();
                resAgentRewardGiftDto.setRewardName(atomResAgentRewardGiftDto.getRewardName());
                resAgentRewardGiftDto.setRewardValue(atomResAgentRewardGiftDto.getRewardValue().intValue());
                 if (RewardTypeEnum.FWQ.getCode().equals(atomResAgentRewardGiftDto.getRewardType())){
                     preServiceCouponDtos.add(resAgentRewardGiftDto);
                }  else if (RewardTypeEnum.LP.getCode().equals(atomResAgentRewardGiftDto.getRewardType())){
                     preGiftDtos.add(resAgentRewardGiftDto);
                }  else if (RewardTypeEnum.XJQ.getCode().equals(atomResAgentRewardGiftDto.getRewardType())){
                     preMoneyCouponDtos.add(resAgentRewardGiftDto);
                }
            });
        }
        resAgentRewardDetailsDTO.setPredictServiceCouponList(preServiceCouponDtos);
        resAgentRewardDetailsDTO.setPredictGiftList(preGiftDtos);
        resAgentRewardDetailsDTO.setPredictMoneyCouponList(preMoneyCouponDtos);
        reqAgentRewardGoodsNumDTO.setRewardStatus(NumConstant.TWO);
        preGiftExecuteDto = atomAgentRewardAnalysisService.getAgentRewardGiftList(reqAgentRewardGoodsNumDTO);
        if (!preGiftExecuteDto.successFlag()) {
            return ExecuteDTO.error(preGiftExecuteDto.getStatus(), preGiftExecuteDto.getMsg());
        }
        //参数转换
        atomResAgentRewardGiftDtos = preGiftExecuteDto.getData();
        if (CollectionUtils.isNotEmpty(atomResAgentRewardGiftDtos)){
            atomResAgentRewardGiftDtos.forEach(atomResAgentRewardGiftDto -> {
                ResAgentRewardGiftDto resAgentRewardGiftDto = new ResAgentRewardGiftDto();
                resAgentRewardGiftDto.setRewardName(atomResAgentRewardGiftDto.getRewardName());
                resAgentRewardGiftDto.setRewardValue(atomResAgentRewardGiftDto.getRewardValue().intValue());
                if (RewardTypeEnum.FWQ.getCode().equals(atomResAgentRewardGiftDto.getRewardType())){
                    serviceCouponDtos.add(resAgentRewardGiftDto);
                }  else if (RewardTypeEnum.LP.getCode().equals(atomResAgentRewardGiftDto.getRewardType())){
                    giftDtos.add(resAgentRewardGiftDto);
                }  else if (RewardTypeEnum.XJQ.getCode().equals(atomResAgentRewardGiftDto.getRewardType())){
                    moneyCouponDtos.add(resAgentRewardGiftDto);
                }
            });
        }
        resAgentRewardDetailsDTO.setServiceCouponList(serviceCouponDtos);
        resAgentRewardDetailsDTO.setGiftList(giftDtos);
        resAgentRewardDetailsDTO.setMoneyCouponList(moneyCouponDtos);
        return ExecuteDTO.success(resAgentRewardDetailsDTO);
    }

    @Override
    public ExecuteDTO<Integer> getFanTotalStatistics(ReqAgentRewardRecordDTO reqAgentRewardRecordDTO) {
        AtomReqAgentRewardRecordDTO atomReqAgentRewardRecordDTO = BeanCopierUtil.copy(reqAgentRewardRecordDTO, AtomReqAgentRewardRecordDTO.class);
        ExecuteDTO<Integer> executeDTO = atomAgentRewardRecordAnalysisService.getFanTotalStatistics(atomReqAgentRewardRecordDTO);
        return executeDTO;
    }

    private List<AtomResAgentRewardCommissionDto> commissionDtoTransfer(List<AtomResAgentRewardCommissionDto> atomResAgentRewardCommissionDtos){
        if (CollectionUtils.isNotEmpty(atomResAgentRewardCommissionDtos)){
            atomResAgentRewardCommissionDtos.forEach(atomResAgentRewardGoodsNumDTO -> {
                if (MarketTypeEnum.PullNew.getCode().equals(atomResAgentRewardGoodsNumDTO.getMarketType()) ||
                        MarketTypeEnum.CloudDistribution.getCode().equals(atomResAgentRewardGoodsNumDTO.getMarketType())){
                    atomResAgentRewardGoodsNumDTO.setMarketType(MarketTypeEnum.PullNew.getCode());
                    atomResAgentRewardGoodsNumDTO.setRewardValue(atomResAgentRewardGoodsNumDTO.getRewardValue());
                }else if (MarketTypeEnum.StoreDistribution.getCode().equals(atomResAgentRewardGoodsNumDTO.getMarketType())){
                    atomResAgentRewardGoodsNumDTO.setMarketType(MarketTypeEnum.StoreDistribution.getCode());
                    atomResAgentRewardGoodsNumDTO.setRewardValue(atomResAgentRewardGoodsNumDTO.getRewardValue());
                }
            });
            atomResAgentRewardCommissionDtos = atomResAgentRewardCommissionDtos.stream()
                    .collect(Collectors.toMap(AtomResAgentRewardCommissionDto::getMarketType, a -> a, (o1,o2)-> {
                        o1.setRewardValue(o1.getRewardValue().add(o2.getRewardValue()));
                        return o1;
                    })).values().stream().collect(Collectors.toList());
        }

        return atomResAgentRewardCommissionDtos;
    }

    private <T> List<T> agentInfoDtoTransfer(List<UnEffectiveAgentResponse> unEffectiveAgentResponses,ReqAgentRewardManageDTO reqAgentRewardManageDTO,Class<T> target){
        log.info(String.format("代理人信息结果 :%s", JSON.toJSONString(unEffectiveAgentResponses)));
        if (CollectionUtils.isNotEmpty(unEffectiveAgentResponses)){
            List<ResAgentRewardDisableDTO>  list = new ArrayList<>();

            for (UnEffectiveAgentResponse unEffectiveAgentResponse : unEffectiveAgentResponses) {
                ResAgentRewardDisableDTO resAgentRewardDisableDTO = new ResAgentRewardDisableDTO();
                resAgentRewardDisableDTO.setAgentNo(unEffectiveAgentResponse.getAgentNo());
                resAgentRewardDisableDTO.setAgentName(unEffectiveAgentResponse.getName());
                resAgentRewardDisableDTO.setAgentPhone(unEffectiveAgentResponse.getPhone());
                resAgentRewardDisableDTO.setStoreName(unEffectiveAgentResponse.getStoreName());
                resAgentRewardDisableDTO.setInvalidTime(unEffectiveAgentResponse.getTimelist());
                resAgentRewardDisableDTO.setStoreNo(unEffectiveAgentResponse.getStoreNo());
                list.add(resAgentRewardDisableDTO);
            }

            //查询分销云池商品数
            AtomReqAgentRewardGoodsNumDTO reqAgentRewardGoodsNumDTO = BeanCopierUtil.copy(reqAgentRewardManageDTO, AtomReqAgentRewardGoodsNumDTO.class);
            List<String> agentNoList = new LinkedList<>();
            List<String> storeNoList = new LinkedList<>();
            list.forEach(resAgentRewardManageDTO -> {
                storeNoList.add(resAgentRewardManageDTO.getStoreNo());
                agentNoList.add(resAgentRewardManageDTO.getAgentNo());
            });
            reqAgentRewardGoodsNumDTO.setAgentNoList(agentNoList);
            reqAgentRewardGoodsNumDTO.setStoreNoList(storeNoList);
            ExecuteDTO<List<AtomResAgentRewardGoodsNumDTO>> sumExecuteDTO =  atomAgentRewardAnalysisService.getAgentRewardGoodsNum(reqAgentRewardGoodsNumDTO);
            //参数转换
            List<AtomResAgentRewardGoodsNumDTO> atomResAgentRewardGoodsNumDTOList = sumExecuteDTO.getData();
            if (sumExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(atomResAgentRewardGoodsNumDTOList)){
                list = list.stream().map(resAgentRewardManageDTO -> {
                    atomResAgentRewardGoodsNumDTOList.stream().filter(resAgentRewardGoodsNumDTO->
                            Objects.equals(resAgentRewardManageDTO.getStoreNo(),resAgentRewardGoodsNumDTO.getStoreNo()) && Objects.equals(resAgentRewardManageDTO.getAgentNo(),resAgentRewardGoodsNumDTO.getAgentNo())).forEach(
                            s-> {
                                if (MarketTypeEnum.CloudDistribution.getCode().equals(s.getMarketType())){
                                    resAgentRewardManageDTO.setCloudGoodsNum(s.getGoodsNum());
                                }else if (MarketTypeEnum.StoreDistribution.getCode().equals(s.getMarketType())){
                                    resAgentRewardManageDTO.setDistributeNum(s.getGoodsNum());
                                }
                            });

                    return resAgentRewardManageDTO;
                }).collect(Collectors.toList());
            }
            //完成任务总数
            AtomReqJoinTaskNumDTO reqJoinTaskNumDTO = new AtomReqJoinTaskNumDTO();
            reqJoinTaskNumDTO.setAgentNoList(agentNoList);
            reqJoinTaskNumDTO.setStoreNoList(storeNoList);
            ExecuteDTO<List<AtomResJoinTaskNumDTO>> listExecuteDTO = atomAgentRewardRecordAnalysisService.getStorePullNewTaskFansNum(reqJoinTaskNumDTO);
            //参数转换
            List<AtomResJoinTaskNumDTO> atomResJoinTaskNumDTOS = listExecuteDTO.getData();
            if (listExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(atomResJoinTaskNumDTOS)){
                list = list.stream().map(resAgentRewardManageDTO -> {
                    atomResJoinTaskNumDTOS.stream().filter(resJoinTaskNumDTO->
                            Objects.equals(resAgentRewardManageDTO.getStoreNo(),resJoinTaskNumDTO.getStoreNo()) && Objects.equals(resAgentRewardManageDTO.getAgentNo(),resJoinTaskNumDTO.getAgentNo())).forEach(
                            s-> resAgentRewardManageDTO.setTaskNum(s.getFansNum()));
                    return resAgentRewardManageDTO;
                }).collect(Collectors.toList());
            }
            //查询酬劳信息
            AtomReqStoreAgentRewardSumDTO reqStoreAgentRewardSumDTO = new AtomReqStoreAgentRewardSumDTO();
            reqStoreAgentRewardSumDTO.setStoreNoList(storeNoList);
            reqStoreAgentRewardSumDTO.setAgentNoList(agentNoList);
            log.info(String.format("代理人酬劳信息入参 :%s", JSON.toJSONString(reqStoreAgentRewardSumDTO)));
            ExecuteDTO<List<AtomResStoreAgentRewardSumDTO>> sumExecuteDto = atomAgentRewardAnalysisService.getStoreAgentRewardValueSum(reqStoreAgentRewardSumDTO);
            log.info(String.format("代理人酬劳信息结果 :%s", JSON.toJSONString(sumExecuteDto)));
            if (sumExecuteDto.successFlag() && CollectionUtils.isNotEmpty(sumExecuteDto.getData())){
                List<AtomResStoreAgentRewardSumDTO> atomResStoreAgentRewardSumDTOList = sumExecuteDto.getData();
                list = list.stream().map(resAgentRewardManageDTO -> {
                    atomResStoreAgentRewardSumDTOList.stream().filter(resJoinTaskNumDTO->
                            Objects.equals(resAgentRewardManageDTO.getStoreNo(),resJoinTaskNumDTO.getStoreNo()) && Objects.equals(resAgentRewardManageDTO.getAgentNo(),resJoinTaskNumDTO.getAgentNo())).forEach(
                            s-> {
                                if (RewardTypeEnum.YJ.getCode().equals(s.getRewardType())){
                                    resAgentRewardManageDTO.setCommission(s.getRewardValue());
                                }else if (RewardTypeEnum.FWQ.getCode().equals(s.getRewardType())){
                                    resAgentRewardManageDTO.setServiceCouponNum(s.getRewardValue().intValue());
                                }  else if (RewardTypeEnum.HJB.getCode().equals(s.getRewardType())){
                                    resAgentRewardManageDTO.setGoldCoinNum(s.getRewardValue().intValue());
                                }  else if (RewardTypeEnum.LP.getCode().equals(s.getRewardType())){
                                    resAgentRewardManageDTO.setGiftNum(s.getRewardValue().intValue());
                                }  else if (RewardTypeEnum.XJQ.getCode().equals(s.getRewardType())){
                                    resAgentRewardManageDTO.setMoneyCouponNum(s.getRewardValue().intValue());
                                }
                            });
                    return resAgentRewardManageDTO;
                }).collect(Collectors.toList());
            }

            return BeanCopierUtil.copyList(list,target);
        }
        return new ArrayList<>();
    }
}
