package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.DateUtil;
import cn.htdt.marketcenter.dto.request.AtomReqPointsConfigLogDTO;
import cn.htdt.marketcenter.dto.response.AtomResPointsConfigLogDTO;
import cn.htdt.marketprocess.api.analysis.PointsConfigLogAnalysisService;
import cn.htdt.marketprocess.biz.conversion.PointsConfigLogAssert;
import cn.htdt.marketprocess.dto.request.ReqPointsConfigLogDTO;
import cn.htdt.marketprocess.dto.response.ResPointsConfigLogDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomPointsConfigLogAnalysisService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@DubboService
public class PointsConfigLogAnalysisServiceImpl implements PointsConfigLogAnalysisService {
    @Resource
    PointsConfigLogAssert logAssert;

    @Resource
    AtomPointsConfigLogAnalysisService atomPointsConfigLogAnalysisService;

    /**
     * @param logDTO
     * @return
     * @see PointsConfigLogAnalysisService#getPointsConfigOperateLogs(ReqPointsConfigLogDTO)
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResPointsConfigLogDTO>> getPointsConfigOperateLogs(ReqPointsConfigLogDTO logDTO) {
        log.info("PointsConfigLogAnalysisServiceImpl.getPointsConfigOperateLogs#param----{}", JSON.toJSONString(logDTO));
        logAssert.getPointsConfigOperateLogs(logDTO);
        AtomReqPointsConfigLogDTO atomReqPointsConfigLogDTO = BeanCopierUtil.copy(logDTO, AtomReqPointsConfigLogDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResPointsConfigLogDTO>> executeDTO = atomPointsConfigLogAnalysisService.getPointsConfigLog(atomReqPointsConfigLogDTO);

        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        //参数转换
        List<ResPointsConfigLogDTO> logDTOS = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResPointsConfigLogDTO.class);
        logDTOS.forEach(s->s.setCreateTimeStr(DateUtil.format(s.getCreateTime(),DateUtil.YYDDMMHHMMSS)));
        return ExecuteDTO.success(new ExecutePageDTO<>(executeDTO.getData().getTotal(),logDTOS));
    }
}
