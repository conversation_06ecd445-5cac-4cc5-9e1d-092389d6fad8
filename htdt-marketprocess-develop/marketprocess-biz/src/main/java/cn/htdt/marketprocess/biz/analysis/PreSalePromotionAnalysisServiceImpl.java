package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.base.exception.BaseException;
import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.enums.GoodsErrorCode;
import cn.htdt.common.dto.enums.MarketErrorCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.goods.GoodsSourceTypeEnum;
import cn.htdt.common.enums.goods.GoodsStatusEnum;
import cn.htdt.common.enums.goods.MultiUnitTypeEnum;
import cn.htdt.common.enums.goods.SeriesTypeEnum;
import cn.htdt.common.enums.market.*;
import cn.htdt.common.redis.utils.RedisBaseUtil;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.BigDecimalUtil;
import cn.htdt.common.utils.DateUtil;
import cn.htdt.goodsprocess.api.analysis.GoodsAnalysisService;
import cn.htdt.goodsprocess.dto.request.goods.ReqGoodsDTO;
import cn.htdt.goodsprocess.dto.response.goods.ResGoodsDTO;
import cn.htdt.goodsprocess.dto.response.goodstag.ResGoodsTagDTO;
import cn.htdt.marketcenter.dto.request.AtomReqAgentMarketRewardsetDTO;
import cn.htdt.marketcenter.dto.request.AtomReqGoodsPromotionGoodsRelationDTO;
import cn.htdt.marketcenter.dto.request.AtomReqPromotionInfoDTO;
import cn.htdt.marketcenter.dto.response.AtomResAgentMarketRewardsetDTO;
import cn.htdt.marketcenter.dto.response.AtomResGoodsPromotionGoodsRelationDTO;
import cn.htdt.marketcenter.dto.response.AtomResGoodsPromotionRuleDTO;
import cn.htdt.marketprocess.api.analysis.PreDeterminedCheckService;
import cn.htdt.marketprocess.api.analysis.PreSalePromotionAnalysisService;
import cn.htdt.marketprocess.biz.conversion.PreSalePromotionAssert;
import cn.htdt.marketprocess.dto.request.ReqPromotionGoodsOrderCheckDTO;
import cn.htdt.marketprocess.dto.request.ReqPromotionInfoDTO;
import cn.htdt.marketprocess.dto.response.*;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomAgentMarketRewardsetAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomGoodsPromotionGoodsRelationAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomGoodsPromotionRuleAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 商品预售活动查询服务
 *
 * <AUTHOR>
 * @date 2022-01-05
 */
@DubboService
@Slf4j
public class PreSalePromotionAnalysisServiceImpl implements PreSalePromotionAnalysisService {

    @Autowired
    private PreSalePromotionAssert preSalePromotionAssert;

    @Autowired
    private GoodsPromotionGoodsRelationAnalysisServiceImpl goodsPromotionGoodsRelationAnalysisServiceImpl;

    @Resource
    private AtomGoodsPromotionRuleAnalysisService atomGoodsPromotionRuleAnalysisService;

    @Resource
    private AtomGoodsPromotionGoodsRelationAnalysisService atomGoodsPromotionGoodsRelationAnalysisService;

    @DubboReference
    private GoodsAnalysisService goodsAnalysisService;

    @Resource
    private AtomAgentMarketRewardsetAnalysisService atomAgentMarketRewardsetAnalysisService;

    @Autowired
    private PreDeterminedCheckService preDeterminedCheckService;

    @Autowired
    private RedisBaseUtil redisBaseUtil;

    @Autowired
    private LimitTimePromotionAnalysisServiceImpl limitTimePromotionAnalysisService;

    /**
     * 根据店铺编号查询有效商品预售活动列表-汇享购
     *
     * @param reqDTO 查询参数
     * @return 商品预售活动列表
     * <AUTHOR>
     * @date 2022-01-05
     */
    @Override
    public ExecuteDTO<List<ResGoodsPromotionRuleDTO>> getPreSalePromotionForHxg(ReqPromotionInfoDTO reqDTO) {
        log.info("getPreSalePromotionForHxg-入参：{}", reqDTO);
        // 入参校验
        preSalePromotionAssert.getPreSalePromotionForHxgAssert(reqDTO);

        // 查询商品预售活动信息
        AtomReqPromotionInfoDTO infoDTO = BeanCopierUtil.copy(reqDTO, AtomReqPromotionInfoDTO.class);
        ExecuteDTO<List<AtomResGoodsPromotionRuleDTO>> promotionExecuteDTO =
                atomGoodsPromotionRuleAnalysisService.getLimitTimePromotionForHxg(infoDTO);
        if (null == promotionExecuteDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!promotionExecuteDTO.successFlag()) {
            return ExecuteDTO.error(promotionExecuteDTO.getStatus(), promotionExecuteDTO.getMsg());
        }

        // 出参转换
        List<ResGoodsPromotionRuleDTO> goodsPromotionRuleDTOList =
                BeanCopierUtil.copyList(promotionExecuteDTO.getData(), ResGoodsPromotionRuleDTO.class);

        // 汇赚钱模块，校验是否查询出来的活动列表数据，是否包含分销商品，如果没有分销商品，则去掉活动
        if (SecKillLocationEnum.H_Z_Q_SECKILL.getCode().equals(reqDTO.getPreSaleLocation())
                && CollectionUtils.isNotEmpty(goodsPromotionRuleDTOList)) {
            this.limitTimePromotionAnalysisService.removeNoDistributeGoodsPromotion(goodsPromotionRuleDTOList);
        }

        //返回结果
        return ExecuteDTO.success(goodsPromotionRuleDTOList);
    }

    /**
     * 首页商品预售活动商品列表
     *
     * @param reqDTO 查询参数
     * @return 商品预售活动以及商品信息
     * <AUTHOR>
     * @date 2022-01-04
     */
    @Override
    public ExecuteDTO<ResIndexPreSalePromotionDTO> getIndexPreSaleGoodsList(ReqPromotionInfoDTO reqDTO) {
        log.info("getIndexPreSaleGoodsList-入参：{}", reqDTO);
        // 入参校验
        preSalePromotionAssert.getIndexPreSaleGoodsListAssert(reqDTO);

        // 查询商品预售活动信息
        reqDTO.setSearchType(SecKillLocationEnum.NEW_FANS_SECKILL.getCode());   //首页调用，按活动有效期开始时间排序
        ExecuteDTO<List<ResGoodsPromotionRuleDTO>> promotionExecuteDTO = this.getPreSalePromotionForHxg(reqDTO);
        if (!promotionExecuteDTO.successFlag()) {
            return ExecuteDTO.error(promotionExecuteDTO.getStatus(), promotionExecuteDTO.getMsg());
        }

        // 封装返回的结果数据
        ResIndexPreSalePromotionDTO resIndexPreSalePromotionDTO = new ResIndexPreSalePromotionDTO();
        List<ResGoodsPromotionRuleDTO> promotionList = promotionExecuteDTO.getData();
        // 根据活动编码查询活动下的商品信息，追加到响应的结果集中
        this.getPreSalePromotionGoodsInfo(reqDTO, resIndexPreSalePromotionDTO, promotionList);

        // 查询活动标签和设置商品按钮状态
        if (CollectionUtils.isNotEmpty(resIndexPreSalePromotionDTO.getGoodsList())) {
            // 查询出所有的促销活动标签
            List<ResGoodsTagDTO> goodsPromotionTagList = this.goodsPromotionGoodsRelationAnalysisServiceImpl.getGoodsPromotionTagName();
            // 根据商品对应的参与的促销活动，找到对应的商品预售活动标签
            List<String> tagNameList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(goodsPromotionTagList)) {
                for (ResGoodsTagDTO resGoodsTagDTO : goodsPromotionTagList) {
                    if (StringUtils.isNotBlank(resGoodsTagDTO.getTagPictureUrl())
                            && PromotionTypeEnum.PRE_SALE.getCode().equals(resGoodsTagDTO.getPromotionType())) {
                        tagNameList.add(resGoodsTagDTO.getTagPictureUrl());
                    }
                }
            }
            for (ResPreSaleGoodsDTO preSaleGoodsDTO : resIndexPreSalePromotionDTO.getGoodsList()) {
                // 设置活动标签
                if (CollectionUtils.isNotEmpty(tagNameList)) {
                    preSaleGoodsDTO.setActiveTagNames(tagNameList);
                }
                // 计算首页商品预售活动倒计时和按钮状态
                this.getPreSaleGoodsButton(preSaleGoodsDTO);
            }
        }

        // 返回结果
        return ExecuteDTO.success(resIndexPreSalePromotionDTO);
    }

    /**
     * 设置商品预售活动商品的按钮状态
     *
     * @param preSaleGoodsDTO 商品预售活动商品数据
     * <AUTHOR>
     */
    private void getPreSaleGoodsButton(ResPreSaleGoodsDTO preSaleGoodsDTO) {
        // 先根据活动的开始和结束时间，计算按钮的未开始、进行中、已结束
        // 活动的开始时间和结束时间
        LocalDateTime startTime = preSaleGoodsDTO.getEffectiveTime();
        LocalDateTime endTime = preSaleGoodsDTO.getInvalidTime();
        // 当前时间
        LocalDateTime nowTime = DateUtil.getLocalDateTime();
        // 当前时间在活动开始之前
        if (nowTime.isBefore(startTime)) {
            // 按钮展示未开始
            preSaleGoodsDTO.setButton(SecKillGoodsStatusEnum.NOT_START.getCode());
        } else if (!nowTime.isAfter(endTime)) {
            // 如果当前时间不大于活动的结束时间，说明活动处于进行中
            // 设置成进行中
            preSaleGoodsDTO.setButton(SecKillGoodsStatusEnum.STARTING.getCode());
        } else {
            // 当前时间大于活动的结束时间，说明当前场次的活动已经结束
            // 按钮设置成已结束
            preSaleGoodsDTO.setButton(SecKillGoodsStatusEnum.END.getCode());
        }

        // 如果活动已结束，则直接退出
        if (SecKillGoodsStatusEnum.END.getCode().equals(preSaleGoodsDTO.getButton())) {
            return;
        }
        // 超过预售商品促销库存，按钮设置成已抢光，直接退出
        if (preSaleGoodsDTO.getRemainStockNum() != null && preSaleGoodsDTO.getRemainStockNum() <= NumConstant.ZERO) {
            preSaleGoodsDTO.setButton(SecKillGoodsStatusEnum.NOT_HAVING.getCode());
            return;
        }
    }

    /**
     * 查询商品预售活动下的商品信息，追加到响应的结果集中
     *
     * @param reqDTO                      页面请求参数
     * @param resIndexPreSalePromotionDTO 待响应的结果数据
     * @param promotionList               活动数据
     */
    private void getPreSalePromotionGoodsInfo(ReqPromotionInfoDTO reqDTO,
                                              ResIndexPreSalePromotionDTO resIndexPreSalePromotionDTO,
                                              List<ResGoodsPromotionRuleDTO> promotionList) {
        if (CollectionUtils.isNotEmpty(promotionList)) {
            int promotionSize = promotionList.size();
            resIndexPreSalePromotionDTO.setPromotionSize(promotionSize);
            if (promotionSize == NumConstant.ONE) {
                resIndexPreSalePromotionDTO.setPromotionNo(promotionList.get(NumConstant.ZERO).getPromotionNo());
            }
            // 存放返回的取到的商品预售活动下的商品数据
            List<ResPreSaleGoodsDTO> preSaleGoodsDTOList = new ArrayList<>();
            for (ResGoodsPromotionRuleDTO promotionDTO : promotionList) {
                // 找到商品符合数量的商品后，退出循环查询
                if (preSaleGoodsDTOList.size() > NumConstant.FIVE) {
                    break;
                }
                // 查询活动关联的商品
                AtomReqGoodsPromotionGoodsRelationDTO goodsRelationDTO = new AtomReqGoodsPromotionGoodsRelationDTO();
                goodsRelationDTO.setPromotionNo(promotionDTO.getPromotionNo());
                ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> goodsExecuteDTO =
                        atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList(goodsRelationDTO);
                if (null == goodsExecuteDTO) {
                    throw new BaseException(CommonCode.CODE_10000003);
                }
                if (!goodsExecuteDTO.successFlag()) {
                    throw new BaseException(goodsExecuteDTO.getStatus(), goodsExecuteDTO.getMsg());
                }
                if (CollectionUtils.isNotEmpty(goodsExecuteDTO.getData())) {
                    List<ResGoodsPromotionGoodsRelationDTO> relationDTOList = BeanCopierUtil.copyList(goodsExecuteDTO.getData()
                            , ResGoodsPromotionGoodsRelationDTO.class);
                    relationDTOList.forEach(relationDTO -> {
                        // 取设置的商品预售活动规则数据
                        relationDTO.setEffectiveTime(promotionDTO.getEffectiveTime());  //活动开始时间
                        relationDTO.setInvalidTime(promotionDTO.getInvalidTime());  //活动结束时间
                        relationDTO.setPromotionName(promotionDTO.getPromotionName());  //活动名称
                        relationDTO.setSourceType(promotionDTO.getSourceType());    //活动来源
                        relationDTO.setPromotionType(promotionDTO.getPromotionType());  //活动类型
                        relationDTO.setUserScope(promotionDTO.getUserScope());  //活动对象
                        relationDTO.setPromotionDate(promotionDTO.getEffectiveTime().toLocalDate());  //活动日期
                    });
                    // 根据商品预售活动关联的商品查询商品详细信息
                    this.getIndexPreSaleGoodsDetailList(reqDTO, preSaleGoodsDTOList, relationDTOList);
                }
            }
            resIndexPreSalePromotionDTO.setGoodsList(preSaleGoodsDTOList);
        }
    }

    /**
     * 获取汇享购首页商品预售活动商品详情列表
     *
     * @param reqDTO              前端页面传过来的查询参数
     * @param preSaleGoodsDTOList 要返回的商品预售活动场次下的商品数据
     * @param relationDTOList     商品预售活动设置的商品数据
     */
    private void getIndexPreSaleGoodsDetailList(ReqPromotionInfoDTO reqDTO,
                                                List<ResPreSaleGoodsDTO> preSaleGoodsDTOList,
                                                List<ResGoodsPromotionGoodsRelationDTO> relationDTOList) {
        //筛选出非子品的商品，包括主品与普通商品
        List<ResGoodsPromotionGoodsRelationDTO> notSubGoodsList = relationDTOList.stream().filter(goodsDTO ->
                WhetherEnum.NO.getCode().equals(goodsDTO.getSubGoodsFlag())).collect(Collectors.toList());
        //筛选出子品的商品
        List<ResGoodsPromotionGoodsRelationDTO> subGoodsList = relationDTOList.stream().filter(goodsDTO ->
                WhetherEnum.YES.getCode().equals(goodsDTO.getSubGoodsFlag())).collect(Collectors.toList());
        // 没有主品 + 普通商品的话，直接退出
        if (CollectionUtils.isEmpty(notSubGoodsList)) {
            return;
        }
        ReqGoodsDTO reqGoodsDTO = new ReqGoodsDTO();
        reqGoodsDTO.setStoreNo(reqDTO.getStoreNo());
        reqGoodsDTO.setNoPage();
        // 汇赚钱进入时，只查询分销商品
        if (SecKillLocationEnum.H_Z_Q_SECKILL.getCode().equals(reqDTO.getPreSaleLocation())) {
            reqGoodsDTO.setDistributeGoodsFlag(WhetherEnum.YES.getCode());
        }
        // 可用的商品
        reqGoodsDTO.setDisableFlag(NumConstant.TWO);
        reqGoodsDTO.setGoodsNos(notSubGoodsList.stream().map(ResGoodsPromotionGoodsRelationDTO::getGoodsNo).collect(Collectors.toList()));
        //查询商品信息主品+普通商品
        log.info("--goodsAnalysisService.getGoodsPage-start-reqGoodsDTO-{}", reqGoodsDTO);
        ExecuteDTO<ExecutePageDTO<ResGoodsDTO>> goodsExecute = goodsAnalysisService.getGoodsPage(reqGoodsDTO);
        if (goodsExecute != null && goodsExecute.successFlag() && goodsExecute.getData() != null
                && CollectionUtils.isNotEmpty(goodsExecute.getData().getRows())) {
            // 汇赚钱页面，需要查询出所有子品的商品数据，用于计算佣金
            if (CollectionUtils.isNotEmpty(subGoodsList)
                    && SecKillLocationEnum.H_Z_Q_SECKILL.getCode().equals(reqDTO.getLimitTimeLocation())) {
                this.getSubGoodsInfo(subGoodsList, reqGoodsDTO);
            }

            List<ResGoodsDTO> resGoodsDTOList = goodsExecute.getData().getRows();
            //将商品信息遍历赋值到商品预售活动商品列表下
            for (ResGoodsPromotionGoodsRelationDTO notSubGoods : notSubGoodsList) {
                Optional<ResGoodsDTO> optional = resGoodsDTOList.stream().
                        filter(goods -> notSubGoods.getGoodsNo().equals(goods.getGoodsNo()))
                        .findFirst();
                if (optional.isPresent()) {
                    ResGoodsDTO resGoodsDTO = optional.get();
                    ResPreSaleGoodsDTO preSaleGoodsDTO = BeanCopierUtil.copy(resGoodsDTO, ResPreSaleGoodsDTO.class);
                    // 20230928 蛋品 多单位商品
                    if (MultiUnitTypeEnum.MAIN_UNIT.getCode().equals(resGoodsDTO.getMultiUnitType()) && StringUtils.isNotBlank(resGoodsDTO.getCalculationUnitName())) {
                        preSaleGoodsDTO.setGoodsName(resGoodsDTO.getGoodsName() + "(" + resGoodsDTO.getCalculationUnitName() + ")");
                    }
                    // 取出活动商品配置的信息
                    preSaleGoodsDTO.setPromotionPrice(notSubGoods.getPromotionPrice()); //活动价格
                    preSaleGoodsDTO.setPromotionNo(notSubGoods.getPromotionNo());   //活动编号
                    preSaleGoodsDTO.setPromotionName(notSubGoods.getPromotionName());   //活动名称
                    preSaleGoodsDTO.setEffectiveTime(notSubGoods.getEffectiveTime());   //活动开始时间
                    preSaleGoodsDTO.setInvalidTime(notSubGoods.getInvalidTime());   //活动结束时间
                    preSaleGoodsDTO.setPeriodNo(notSubGoods.getPeriodNo());   //活动场次时间段编号
                    preSaleGoodsDTO.setRemainStockNum(notSubGoods.getRemainStockNum()); //商品促销活动剩余库存
                    preSaleGoodsDTO.setPromotionType(notSubGoods.getPromotionType()); //活动类型
                    preSaleGoodsDTO.setUserScope(notSubGoods.getUserScope()); //互动对象
                    preSaleGoodsDTO.setPromotionDate(notSubGoods.getPromotionDate()); //活动日期
                    //如果是主品，则需要查询出子品的最小商品预售价格
                    if (SeriesTypeEnum.PARENT_GOODS.getCode().equals(resGoodsDTO.getSeriesType())) {
                        if (CollectionUtils.isNotEmpty(subGoodsList)) {
                            // 找出对应主品以及对应活动下的子品集合
                            List<ResGoodsPromotionGoodsRelationDTO> currentParentSubList = subGoodsList.stream()
                                    .filter(subGoods -> subGoods.getParentGoodsNo().equals(notSubGoods.getGoodsNo())
                                            && subGoods.getPromotionNo().equals(notSubGoods.getPromotionNo())).collect(Collectors.toList());
                            // 计算对应主品下的所有子品中最大和最小的活动价格格
                            if (CollectionUtils.isNotEmpty(currentParentSubList)) {
                                // 最小商品预售价格
                                Optional<ResGoodsPromotionGoodsRelationDTO> min = currentParentSubList.stream()
                                        .min(Comparator.comparing(ResGoodsPromotionGoodsRelationDTO::getPromotionPrice));
                                if (min.isPresent()) {
                                    ResGoodsPromotionGoodsRelationDTO goodsRelationDTO = min.get();
                                    preSaleGoodsDTO.setMinPromotionPrice(goodsRelationDTO.getPromotionPrice());
                                }
                                // 最大商品预售价格
                                Optional<ResGoodsPromotionGoodsRelationDTO> max = currentParentSubList.stream()
                                        .max(Comparator.comparing(ResGoodsPromotionGoodsRelationDTO::getPromotionPrice));
                                max.ifPresent(maxGoodsRelationDTO -> preSaleGoodsDTO.setMaxPromotionPrice(maxGoodsRelationDTO.getPromotionPrice()));
                                // 所有子品中的活动剩余库存值汇总，用于页面展示主品的状态是不是已抢光
                                Integer remainStockTotalNum = 0;
                                for (ResGoodsPromotionGoodsRelationDTO goodsRelationDTO : currentParentSubList) {
                                    if (null != goodsRelationDTO && null != goodsRelationDTO.getRemainStockNum()) {
                                        remainStockTotalNum = remainStockTotalNum + goodsRelationDTO.getRemainStockNum();
                                    }
                                }
                                preSaleGoodsDTO.setRemainStockNum(remainStockTotalNum); //商品促销活动剩余库存
                                // 计算酬劳
                                this.calculateReward(preSaleGoodsDTO, currentParentSubList);
                            }
                        }
                    } else {
                        // 非系列商品
                        List<ResGoodsPromotionGoodsRelationDTO> goodsList = new ArrayList<>();
                        notSubGoods.setCloudPoolSupplyPrice(resGoodsDTO.getCloudPoolSupplyPrice());
                        goodsList.add(notSubGoods);
                        // 计算酬劳
                        this.calculateReward(preSaleGoodsDTO, goodsList);
                    }

                    preSaleGoodsDTOList.add(preSaleGoodsDTO);
                    // 商品商品预售商品大于5时 ，结束逻辑判断
                    if (preSaleGoodsDTOList.size() > NumConstant.FIVE) {
                        return;
                    }
                }
            }
        }
    }

    /**
     * 计算酬劳
     *
     * @param preSaleGoodsDTO 响应的商品预售活动商品数据
     * @param goodsList       商品预售活动关联的商品
     */
    private void calculateReward(ResPreSaleGoodsDTO preSaleGoodsDTO, List<ResGoodsPromotionGoodsRelationDTO> goodsList) {
        // 分销商品
        if (NumConstant.TWO == preSaleGoodsDTO.getDistributeGoodsFlag()) {
            // 返回的酬劳信息
            StringBuilder result = new StringBuilder();
            // 分销酬劳设置
            AtomReqAgentMarketRewardsetDTO atomReqAgentMarketRewardsetDTO = new AtomReqAgentMarketRewardsetDTO();
            // 非子品，直接取goodsNo
            atomReqAgentMarketRewardsetDTO.setTaskOrGoodsNo(preSaleGoodsDTO.getGoodsNo());
            atomReqAgentMarketRewardsetDTO.setMarketType(MarketTypeEnum.StoreDistribution.getCode());
            ExecuteDTO<List<AtomResAgentMarketRewardsetDTO>> listExecuteDTO =
                    atomAgentMarketRewardsetAnalysisService.getList(atomReqAgentMarketRewardsetDTO);
            if (null == listExecuteDTO || !listExecuteDTO.successFlag()) {
                throw new BaseException(CommonCode.CODE_10000003);
            }
            // 取最大的活动价格
            BigDecimal maxPromotionPrice = null;
            Optional<ResGoodsPromotionGoodsRelationDTO> realSalePriceOptional = goodsList.stream()
                    .max(Comparator.comparing(ResGoodsPromotionGoodsRelationDTO::getPromotionPrice));
            if (realSalePriceOptional.isPresent()) {
                maxPromotionPrice = realSalePriceOptional.get().getPromotionPrice();
            }
            List<AtomResAgentMarketRewardsetDTO> rewardLists = listExecuteDTO.getData();
            if (CollectionUtils.isNotEmpty(rewardLists)) {
                AtomResAgentMarketRewardsetDTO atomResAgentMarketRewardsetDTO = rewardLists.get(0);
                if (null != atomResAgentMarketRewardsetDTO) {
                    // 佣金类型
                    Integer rewardType = atomResAgentMarketRewardsetDTO.getRewardType();
                    preSaleGoodsDTO.setRewardType(atomResAgentMarketRewardsetDTO.getRewardType());
                    if (RewardTypeEnum.getByCode(rewardType) != null) {
                        preSaleGoodsDTO.setRewardTypeValue(RewardTypeEnum.getByCode(rewardType).getType());
                    }
                    switch (rewardType) {
                        case NumConstant.ONE:
                            // 佣金
                            BigDecimal oneHundred = new BigDecimal(NumConstant.ONE_HUNDRED);
                            if (null != atomResAgentMarketRewardsetDTO.getYjOrHjb() && maxPromotionPrice != null) {
                                // 按比例计算佣金
                                BigDecimal calculateResult = BigDecimalUtil.setScale(atomResAgentMarketRewardsetDTO.getYjOrHjb().divide(oneHundred)
                                        .multiply(maxPromotionPrice));
                                // 四舍五入保留2位小数
                                calculateResult = BigDecimalUtil.setScale(calculateResult);
                                result.append("￥").append(calculateResult);
                            } else {
                                log.info("*********佣金接口返回空值，请后台查看问题*********************");
                            }
                            break;
                        case NumConstant.TWO:
                            if (null != atomResAgentMarketRewardsetDTO.getYjOrHjb()) {
                                // 汇金币
                                result.append("汇金币").append(atomResAgentMarketRewardsetDTO.getYjOrHjb()).append("个");
                            } else {
                                log.info("*********汇金币接口返回空值，请后台查看问题*********************");
                            }
                            break;
                        case NumConstant.THREE:
                            // 礼品
                            result.append(atomResAgentMarketRewardsetDTO.getRelatedName());
                            break;
                        case NumConstant.FOUR:
                            // 现金券
                            result.append("现金券(满￥").append(atomResAgentMarketRewardsetDTO.getRelatedUp()).append("减")
                                    .append(atomResAgentMarketRewardsetDTO.getRelatedLess()).append(")");
                            break;
                        case NumConstant.FIVE:
                            // 服务劵
                            result.append(atomResAgentMarketRewardsetDTO.getRelatedName());
                            break;
                        default:
                            log.info("*********酬劳设置未匹配到类型，请后台查看问题*********************");
                    }
                }
            }
            if (StringUtils.isNotBlank(result.toString())) {
                preSaleGoodsDTO.setReward(result.toString());
            }
        }
    }

    /**
     * 查询子品信息，用于计算酬劳
     *
     * @param subGoodsList 商品预售活动关联的子品数据
     * @param reqGoodsDTO  查询的请求参数
     * <AUTHOR>
     * @date 2021-09-28
     */
    private void getSubGoodsInfo(List<ResGoodsPromotionGoodsRelationDTO> subGoodsList, ReqGoodsDTO reqGoodsDTO) {
        reqGoodsDTO.setGoodsNos(subGoodsList.stream().map(ResGoodsPromotionGoodsRelationDTO::getGoodsNo).collect(Collectors.toList()));
        ExecuteDTO<ExecutePageDTO<ResGoodsDTO>> subGoodsExecuteDTO = goodsAnalysisService.getGoodsPage(reqGoodsDTO);
        log.info("查询子品的商品信息结果出参：{}", subGoodsExecuteDTO);
        if (subGoodsExecuteDTO == null || !subGoodsExecuteDTO.successFlag()) {
            throw new BaseException(CommonCode.CODE_10000003);
        }
        // 取出子品相关信息
        if (subGoodsExecuteDTO.getData() != null && CollectionUtils.isNotEmpty(subGoodsExecuteDTO.getData().getRows())) {
            subGoodsList.forEach(subGoods -> {
                Optional<ResGoodsDTO> optional = subGoodsExecuteDTO.getData().getRows().stream()
                        .filter(goods -> subGoods.getGoodsNo().equals(goods.getGoodsNo()))
                        .findFirst();
                if (optional.isPresent()) {
                    ResGoodsDTO goodsDTO = optional.get();
                    // 取零售价格，用于计算佣金
                    subGoods.setRetailPrice(goodsDTO.getRetailPrice()); //子品的零售价
                }
            });
        }
    }

    /**
     * 商品预售活动详情商品聚合页
     *
     * @param reqDTO 查询参数
     * @return 商品预售活动详情商品
     * <AUTHOR>
     * @date 2022-01-04
     */
    @Override
    public ExecuteDTO<ResPreSaleGoodsPageDTO> getPreSaleGoodsPage(ReqPromotionInfoDTO reqDTO) {
        log.info("getPreSaleGoodsPage-入参：{}", reqDTO);
        // 入参校验
        this.preSalePromotionAssert.getPreSaleGoodsPageAssert(reqDTO);

        // 根据活动编号查询活动信息
        AtomReqPromotionInfoDTO promotionInfoDTO = BeanCopierUtil.copy(reqDTO, AtomReqPromotionInfoDTO.class);
        ExecuteDTO<AtomResGoodsPromotionRuleDTO> executeDTO =
                this.atomGoodsPromotionRuleAnalysisService.getGoodsPromotionRuleByPromotionNo(promotionInfoDTO);
        if (null == executeDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        if (executeDTO.getData() == null) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000100);
        }
        ResGoodsPromotionRuleDTO promotionRuleDTO = BeanCopierUtil.copy(executeDTO.getData(), ResGoodsPromotionRuleDTO.class);

        // 待返回的结果数据
        ResPreSaleGoodsPageDTO preSaleGoodsPageDTO = new ResPreSaleGoodsPageDTO();
        preSaleGoodsPageDTO.setPreSalePromotion(promotionRuleDTO);

        // 查询活动下的商品数据
        if (promotionRuleDTO != null) {
            this.getPreSaleGoodsList(preSaleGoodsPageDTO, promotionRuleDTO, reqDTO);
        }

        // 返回结果
        return ExecuteDTO.success(preSaleGoodsPageDTO);
    }

    /**
     * 查询商品预售活动下设置的商品数据
     *
     * @param preSaleGoodsPageDTO 待返回的商品数据
     * @param promotionRuleDTO    商品预售活动
     * @param reqDTO              页面请求查询的参数
     * <AUTHOR>
     * @date 2022-01-10
     */
    private void getPreSaleGoodsList(ResPreSaleGoodsPageDTO preSaleGoodsPageDTO, ResGoodsPromotionRuleDTO promotionRuleDTO,
                                     ReqPromotionInfoDTO reqDTO) {
        List<ResPreSaleGoodsDTO> preSaleGoodsDTOList = new ArrayList<>();
        //查询活动下的商品列表
        AtomReqGoodsPromotionGoodsRelationDTO goodsRelationDTO = BeanCopierUtil.copy(reqDTO, AtomReqGoodsPromotionGoodsRelationDTO.class);
        log.info("--atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList-start-goodsRelationDTO-{}", goodsRelationDTO);
        ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> goodsExecuteDTO =
                this.atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList(goodsRelationDTO);
        log.info("--atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList-end");
        if (goodsExecuteDTO != null && goodsExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(goodsExecuteDTO.getData())) {
            List<ResGoodsPromotionGoodsRelationDTO> relationDTOList =
                    BeanCopierUtil.copyList(goodsExecuteDTO.getData(), ResGoodsPromotionGoodsRelationDTO.class);
            //筛选出非子品的商品，包括主品与普通商品
            List<ResGoodsPromotionGoodsRelationDTO> notSubGoodsList = relationDTOList.stream().filter(goodsDTO ->
                    WhetherEnum.NO.getCode().equals(goodsDTO.getSubGoodsFlag())).collect(Collectors.toList());
            //筛选出子品的商品
            List<ResGoodsPromotionGoodsRelationDTO> subGoodsList = relationDTOList.stream().filter(goodsDTO ->
                    WhetherEnum.YES.getCode().equals(goodsDTO.getSubGoodsFlag())).collect(Collectors.toList());
            //列表查询排除子品，商品预售活动详情页调用
            if (!WhetherEnum.YES.getCode().equals(reqDTO.getQuerySubGoodsFlag())) {
                this.getPromotionGoodsInfo(reqDTO, promotionRuleDTO, preSaleGoodsDTOList, notSubGoodsList, subGoodsList);
            } else {
                //指定商品编号查询不排除子品，商品预售活动商品详情页调用
                this.getPromotionGoodsInfo(reqDTO, promotionRuleDTO, preSaleGoodsDTOList, relationDTOList, subGoodsList);
            }
        }

        // 设置活动标签和活动商品的按钮状态
        if (CollectionUtils.isNotEmpty(preSaleGoodsDTOList)) {
            // 查询出所有的促销活动标签
            List<ResGoodsTagDTO> goodsPromotionTagList = this.goodsPromotionGoodsRelationAnalysisServiceImpl.getGoodsPromotionTagName();
            // 根据商品对应的参与的促销活动，找到对应的商品预售活动标签
            List<String> tagNameList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(goodsPromotionTagList)) {
                for (ResGoodsTagDTO resGoodsTagDTO : goodsPromotionTagList) {
                    if (StringUtils.isNotBlank(resGoodsTagDTO.getTagPictureUrl())
                            && PromotionTypeEnum.PRE_SALE.getCode().equals(resGoodsTagDTO.getPromotionType())) {
                        tagNameList.add(resGoodsTagDTO.getTagPictureUrl());
                    }
                }
            }
            for (ResPreSaleGoodsDTO preSaleGoodsDTO : preSaleGoodsDTOList) {
                // 设置活动标签
                if (CollectionUtils.isNotEmpty(tagNameList)) {
                    preSaleGoodsDTO.setActiveTagNames(tagNameList);
                }
                // 计算首页商品预售活动倒计时和按钮状态
                this.getPreSaleGoodsButton(preSaleGoodsDTO);
            }
        }
        preSaleGoodsPageDTO.setGoodsList(preSaleGoodsDTOList);
    }

    /**
     * 查询活动下的商品详情数据
     *
     * @param reqDTO              页面请求的查询参数
     * @param promotionRuleDTO    活动信息
     * @param preSaleGoodsDTOList 待返回的活动商品信息
     * @param goodsList           活动设置的商品品数据
     * @param subGoodsList        活动设置的子品数据
     */
    private void getPromotionGoodsInfo(ReqPromotionInfoDTO reqDTO, ResGoodsPromotionRuleDTO promotionRuleDTO,
                                       List<ResPreSaleGoodsDTO> preSaleGoodsDTOList,
                                       List<ResGoodsPromotionGoodsRelationDTO> goodsList,
                                       List<ResGoodsPromotionGoodsRelationDTO> subGoodsList) {
        ReqGoodsDTO reqGoodsDTO = new ReqGoodsDTO();
        //店铺活动则根据goodsNo查询
        reqGoodsDTO.setGoodsNos(goodsList.stream()
                .map(ResGoodsPromotionGoodsRelationDTO::getGoodsNo).collect(Collectors.toList()));
        reqGoodsDTO.setNoPage();
        reqGoodsDTO.setStoreNo(reqDTO.getStoreNo());
        // 可用的商品
        reqGoodsDTO.setDisableFlag(NumConstant.TWO);
        // 如果是汇赚钱进入活动详情页，只查询分销商品
        if (SecKillLocationEnum.H_Z_Q_SECKILL.getCode().equals(reqDTO.getPreSaleLocation())) {
            reqGoodsDTO.setDistributeGoodsFlag(WhetherEnum.YES.getCode());
        }
        //查询商品信息
        log.info("--goodsAnalysisService.getGoodsPage-start-reqGoodsDTO-{}", reqGoodsDTO);
        ExecuteDTO<ExecutePageDTO<ResGoodsDTO>> goodsExecute = goodsAnalysisService.getGoodsPage(reqGoodsDTO);
        log.info("--goodsAnalysisService.getGoodsPage-end");
        if (goodsExecute != null && goodsExecute.successFlag() && goodsExecute.getData() != null
                && CollectionUtils.isNotEmpty(goodsExecute.getData().getRows())) {
            List<ResGoodsDTO> resGoodsDTOList = goodsExecute.getData().getRows();
            // 查询出所有子品的商品数据，用于计算佣金
            if (CollectionUtils.isNotEmpty(subGoodsList)) {
                this.getSubGoodsInfo(subGoodsList, reqGoodsDTO);
            }
            //将商品信息遍历赋值到或活动详情页的商品列表下
            goodsList.forEach(goodsDTO -> {
                //如果是云池商品则拿查询出来的cloudPoolGoodsNo与goodsNo比较，否则直接用goodsno
                Optional<ResGoodsDTO> optional = resGoodsDTOList.stream()
                        .filter(goods -> goodsDTO.getGoodsNo().equals(goods.getGoodsNo())
                                || goodsDTO.getGoodsNo().equals(goods.getCloudPoolGoodsNo())).findFirst();
                if (optional.isPresent()) {
                    ResGoodsDTO resGoodsDTO = optional.get();
                    ResPreSaleGoodsDTO preSaleGoodsDTO = BeanCopierUtil.copy(resGoodsDTO, ResPreSaleGoodsDTO.class);
                    // 20230928 蛋品 多单位商品
                    if (MultiUnitTypeEnum.MAIN_UNIT.getCode().equals(resGoodsDTO.getMultiUnitType()) && StringUtils.isNotBlank(resGoodsDTO.getCalculationUnitName())) {
                        preSaleGoodsDTO.setGoodsName(resGoodsDTO.getGoodsName() + "(" + resGoodsDTO.getCalculationUnitName() + ")");
                    }
                    preSaleGoodsDTO.setPromotionNo(promotionRuleDTO.getPromotionNo());
                    preSaleGoodsDTO.setPromotionName(promotionRuleDTO.getPromotionName());
                    preSaleGoodsDTO.setEffectiveTime(promotionRuleDTO.getEffectiveTime());
                    preSaleGoodsDTO.setInvalidTime(promotionRuleDTO.getInvalidTime());
                    preSaleGoodsDTO.setSourceType(promotionRuleDTO.getSourceType());
                    preSaleGoodsDTO.setPromotionType(promotionRuleDTO.getPromotionType());
                    preSaleGoodsDTO.setUserScope(promotionRuleDTO.getUserScope());
                    preSaleGoodsDTO.setUpDownFlag(promotionRuleDTO.getUpDownFlag());    //活动上下架状态
                    preSaleGoodsDTO.setDeliveryFlag(promotionRuleDTO.getDeliveryFlag());    //配送方式是否以商品自身配置为主
                    preSaleGoodsDTO.setDeliveryWay(promotionRuleDTO.getDeliveryWay());  //活动设置的配送方式
                    preSaleGoodsDTO.setPromotionDate(promotionRuleDTO.getEffectiveTime().toLocalDate());    //活动日期
                    preSaleGoodsDTO.setPromotionPrice(goodsDTO.getPromotionPrice());    //活动价格
                    preSaleGoodsDTO.setPeriodNo(goodsDTO.getPeriodNo());    //活动场次时间段编号
                    preSaleGoodsDTO.setRemainStockNum(goodsDTO.getRemainStockNum());    //活动商品剩余促销库存
                    preSaleGoodsDTO.setSettingStockNum(goodsDTO.getSettingStockNum());
                    // 对于商品预售活动商品详情页调用时，相关取数据的逻辑统一放到了 hxg 应用层做处理
                    if (!WhetherEnum.YES.getCode().equals(reqDTO.getQuerySubGoodsFlag())) {
                        //如果是主品，则需要查询出子品的最小活动价格
                        if (SeriesTypeEnum.PARENT_GOODS.getCode().equals(resGoodsDTO.getSeriesType()) && CollectionUtils.isNotEmpty(subGoodsList)) {
                            // 找出对应主品下的子品商品
                            List<ResGoodsPromotionGoodsRelationDTO> currentParentSubList = subGoodsList.stream()
                                    .filter(subGoods -> subGoods.getParentGoodsNo().equals(goodsDTO.getGoodsNo())).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(currentParentSubList)) {
                                // 最小活动价格格
                                Optional<ResGoodsPromotionGoodsRelationDTO> minOptional = subGoodsList.stream().filter(
                                        subGoods -> subGoods.getParentGoodsNo().equals(goodsDTO.getGoodsNo()))
                                        .min(Comparator.comparing(ResGoodsPromotionGoodsRelationDTO::getPromotionPrice));
                                //设置最小活动价格
                                if (minOptional.isPresent()) {
                                    ResGoodsPromotionGoodsRelationDTO goodsRelationDTO = minOptional.get();
                                    preSaleGoodsDTO.setMinPromotionPrice(goodsRelationDTO.getPromotionPrice());
                                }
                                // 取最大活动价格
                                Optional<ResGoodsPromotionGoodsRelationDTO> maxOptional = subGoodsList.stream().filter(
                                        subGoods -> subGoods.getParentGoodsNo().equals(goodsDTO.getGoodsNo()))
                                        .max(Comparator.comparing(ResGoodsPromotionGoodsRelationDTO::getPromotionPrice));
                                maxOptional.ifPresent(resGoodsPromotionGoodsRelationDTO ->
                                        preSaleGoodsDTO.setMaxPromotionPrice(resGoodsPromotionGoodsRelationDTO.getPromotionPrice()));

                                // 汇总子品中所有的活动剩余库存以及设置的活动库存
                                Integer remainStockTotalNum = 0;
                                Integer settingStockTotalNum = 0;
                                for (ResGoodsPromotionGoodsRelationDTO goodsRelationDTO : currentParentSubList) {
                                    if (null != goodsRelationDTO && null != goodsRelationDTO.getRemainStockNum()) {
                                        remainStockTotalNum = remainStockTotalNum + goodsRelationDTO.getRemainStockNum();
                                    }
                                    if (null != goodsRelationDTO && null != goodsRelationDTO.getSettingStockNum()) {
                                        settingStockTotalNum = settingStockTotalNum + goodsRelationDTO.getSettingStockNum();
                                    }
                                }
                                preSaleGoodsDTO.setRemainStockNum(remainStockTotalNum);    //所有子品的活动商品剩余促销库存
                                preSaleGoodsDTO.setSettingStockNum(settingStockTotalNum);   //所有子品的设置活动库存
                                // 计算酬劳
                                this.calculateReward(preSaleGoodsDTO, currentParentSubList);
                            }
                        } else {
                            // 非系列商品
                            List<ResGoodsPromotionGoodsRelationDTO> goods = new ArrayList<>();
                            goodsDTO.setCloudPoolSupplyPrice(resGoodsDTO.getCloudPoolSupplyPrice());
                            goods.add(goodsDTO);
                            // 计算酬劳
                            this.calculateReward(preSaleGoodsDTO, goods);
                        }
                    }
                    preSaleGoodsDTOList.add(preSaleGoodsDTO);
                }
            });
        }
    }

    /**
     * 商品预售活动商品下单校验
     *
     * @param reqDTO 查询参数
     * @return
     * <AUTHOR>
     * @date 2022-01-12
     */
    @Override
    public ExecuteDTO preSaleGoodsOrderCheck(ReqPromotionGoodsOrderCheckDTO reqDTO) {
        log.info("preSaleGoodsOrderCheck-入参：{}", reqDTO);
        // 入参校验
        this.preSalePromotionAssert.preSaleGoodsOrderCheck(reqDTO);

        // 根据商品编号查询商品信息
        ReqGoodsDTO goodsDto = new ReqGoodsDTO();
        goodsDto.setDisableFlag(NumConstant.TWO);   // 查询有效的商品
        goodsDto.setGoodsNo(reqDTO.getGoodsNo());
        ExecuteDTO<ResGoodsDTO> goodsInfoExecuteDTO = goodsAnalysisService.getGoods(goodsDto);
        if (null == goodsInfoExecuteDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!goodsInfoExecuteDTO.successFlag()) {
            return ExecuteDTO.error(goodsInfoExecuteDTO.getStatus(), goodsInfoExecuteDTO.getMsg());
        }
        if (null == goodsInfoExecuteDTO.getData()) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000616);
        }
        ResGoodsDTO goodsInfo = goodsInfoExecuteDTO.getData();
        // 校验商品下架
        if (GoodsStatusEnum.OFF_SHELF.getCode().equals(goodsInfo.getGoodsStatus())) {
            return ExecuteDTO.error(GoodsErrorCode.CODE_12000226);
        }
        // 云池商品时，取cloudPoolGoodsNo，否则取 goodsNo
        String goodsNo;
        if (GoodsSourceTypeEnum.CLOUD_POOL_DISTRIBUTE.getCode().equals(goodsInfo.getGoodsSourceType())) {
            goodsNo = goodsInfo.getCloudPoolGoodsNo();
        } else {
            goodsNo = goodsInfo.getGoodsNo();
        }

        // 查询商品预售活动商品配置的活动信息
        AtomReqGoodsPromotionGoodsRelationDTO goodsRelationDTO = new AtomReqGoodsPromotionGoodsRelationDTO();
        goodsRelationDTO.setPromotionNo(reqDTO.getPromotionNo());
        goodsRelationDTO.setPeriodNo(reqDTO.getPeriodNo());
        goodsRelationDTO.setGoodsNo(goodsNo);
        ExecuteDTO<AtomResGoodsPromotionGoodsRelationDTO> goodsAndRuleExecuteDTO =
                atomGoodsPromotionGoodsRelationAnalysisService.getGoodsAndRule(goodsRelationDTO);
        if (null == goodsAndRuleExecuteDTO || !goodsAndRuleExecuteDTO.successFlag()) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (null == goodsAndRuleExecuteDTO.getData()) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000616);
        }
        AtomResGoodsPromotionGoodsRelationDTO goodsAndRule = goodsAndRuleExecuteDTO.getData();

        // 如果活动配置限制新用户参与时，校验用户是否为新用户
        if (UserScopeEnum.NEW_FANS.getCode().equals(goodsAndRule.getUserScope())) {
            // 判断是否限制新老用户，如果是云池商品，表示平台，store传 null
            ExecuteDTO<Boolean> checkNewFansExecuteDTO = preDeterminedCheckService.checkNewFans(reqDTO.getFanNo(),
                    GoodsSourceTypeEnum.CLOUD_POOL_DISTRIBUTE.getCode().equals(goodsInfo.getGoodsSourceType()) ? null : reqDTO.getStoreNo());
            if (!checkNewFansExecuteDTO.successFlag()) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            // 活动设置了限制新用户，但该用户已经下过单不是新用户了，提示信息给前端
            if (!checkNewFansExecuteDTO.getData()) {
                return ExecuteDTO.error(MarketErrorCode.CODE_17000630);
            }
        } else if (UserScopeEnum.OLD_FANS.getCode().equals(goodsAndRule.getUserScope())) {
            // 如果活动配置限制老用户参与时，校验用户是否为老用户
            // 判断是否限制新老用户，如果是云池商品，表示平台，store传 null
            ExecuteDTO<Boolean> checkNewFansExecuteDTO = preDeterminedCheckService.checkNewFans(reqDTO.getFanNo(),
                    GoodsSourceTypeEnum.CLOUD_POOL_DISTRIBUTE.getCode().equals(goodsInfo.getGoodsSourceType()) ? null : reqDTO.getStoreNo());
            if (!checkNewFansExecuteDTO.successFlag()) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            // 活动设置了限制老用户，但该用户没有下过单，是新用户，提示信息给前端
            if (checkNewFansExecuteDTO.getData()) {
                return ExecuteDTO.error(MarketErrorCode.CODE_17000631);
            }
        }

        return ExecuteDTO.success();
    }
}