package cn.htdt.marketprocess.biz.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqAgentWithdrawCashDTO;
import cn.htdt.marketprocess.api.operat.AgentWithdrawCashOperatService;
import cn.htdt.marketprocess.dto.request.ReqAgentWithdrawCashDTO;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomAgentWithdrawCashOperatService;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

@DubboService
public class AgentWithdrawCashOperatServiceImpl implements AgentWithdrawCashOperatService {

    @Resource
    private AtomAgentWithdrawCashOperatService atomAgentWithdrawCashOperatService;

    @Override
    public ExecuteDTO save(ReqAgentWithdrawCashDTO reqAgentWithdrawCashDTO) {
        AtomReqAgentWithdrawCashDTO atomReqAgentWithdrawCashDTO = BeanCopierUtil.copy(reqAgentWithdrawCashDTO, AtomReqAgentWithdrawCashDTO.class);
        ExecuteDTO executeDTO = atomAgentWithdrawCashOperatService.save(atomReqAgentWithdrawCashDTO);
        return executeDTO;
    }

}
