package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.generator.IdGenerator;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqSmsRechargePackageDTO;
import cn.htdt.marketcenter.dto.response.AtomResSmsRechargePackageDTO;
import cn.htdt.marketprocess.api.analysis.SmsRechargePackageAnalysisService;
import cn.htdt.marketprocess.dao.SmsRechargePackageDao;
import cn.htdt.marketprocess.domain.SmsRechargePackageDomain;
import cn.htdt.marketprocess.dto.request.ReqSmsRechargePackageDTO;
import cn.htdt.marketprocess.dto.response.ResSmsRechargePackageDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomSmsRechargePackageAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * 短信充值套餐 数据查询服务接口实现类
 *
 * <AUTHOR>
 * @date 2021/9/26
 **/
@Slf4j
@DubboService
public class SmsRechargePackageAnalysisServiceImpl implements SmsRechargePackageAnalysisService {

    @Resource
    private AtomSmsRechargePackageAnalysisService atomSmsRechargePackageAnalysisService;
    @Resource
    private SmsRechargePackageDao smsRechargePackageDao;

    /**
     * 综合服务平台 -> 营销管理 -> 短信营销活动 -> 短信充值 -> 获取短信充值套餐列表
     * 超级老板APP -> 会员 -> 短信营销 -> 短信充值 -> 获取短信充值套餐列表
     *
     * @param reqDTO 查询参数
     * @return ExecuteDTO<List < ResSmsRechargePackageDTO>>
     * <AUTHOR>
     */
    @Override
    public ExecuteDTO<List<ResSmsRechargePackageDTO>> getSmsRechargePackageList(ReqSmsRechargePackageDTO reqDTO) {
        log.info("-------SmsRechargePackageAnalysisServiceImpl-->getSmsRechargePackageList,获取短信充值套餐列表--start----");
        log.info("-----getSmsRechargePackageList---->,查询参数:{}", reqDTO);

        AtomReqSmsRechargePackageDTO atomReqDTO = BeanCopierUtil.copy(reqDTO, AtomReqSmsRechargePackageDTO.class);

        //获取短信充值套餐列表
        ExecuteDTO<List<AtomResSmsRechargePackageDTO>> executeDTO = atomSmsRechargePackageAnalysisService.getSmsRechargePackageList(atomReqDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }

        log.info("-------SmsRechargePackageAnalysisServiceImpl-->getSmsRechargePackageList,获取短信充值套餐列表----");
        return ExecuteDTO.success(BeanCopierUtil.copyList(executeDTO.getData(), ResSmsRechargePackageDTO.class));
    }

    @Override
    public ExecuteDTO addSmsRechargePackage(ReqSmsRechargePackageDTO reqDTO) {
        SmsRechargePackageDomain smsRechargePackageDomain = BeanCopierUtil.copy(reqDTO, SmsRechargePackageDomain.class);
        smsRechargePackageDomain.setPackageNo(IdGenerator.getDid());
        Integer sort = smsRechargePackageDao.selectMaxSort();
        smsRechargePackageDomain.setSortValue(sort+1);
        smsRechargePackageDomain.setOriginalPrice(new BigDecimal(smsRechargePackageDomain.getRechargeNum()*0.06));
        smsRechargePackageDao.insert(smsRechargePackageDomain);
        return ExecuteDTO.success();
    }

    @Override
    public ExecuteDTO modifySmsRechargePackage(ReqSmsRechargePackageDTO reqDTO) {
        SmsRechargePackageDomain smsRechargePackageDomain = BeanCopierUtil.copy(reqDTO, SmsRechargePackageDomain.class);
        smsRechargePackageDomain.setOriginalPrice(new BigDecimal(smsRechargePackageDomain.getRechargeNum()*0.06));
        smsRechargePackageDao.updateParam(smsRechargePackageDomain);
        return ExecuteDTO.success();
    }

    @Override
    public ExecuteDTO deleteSmsRechargePackage(String packageNo) {
        smsRechargePackageDao.deleteByPackageNo(packageNo);
        return ExecuteDTO.success();
    }


}
