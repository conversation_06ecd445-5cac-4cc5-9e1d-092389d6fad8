package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.base.exception.BaseException;
import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.enums.GoodsErrorCode;
import cn.htdt.common.dto.enums.MarketErrorCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.goods.GoodsSourceTypeEnum;
import cn.htdt.common.enums.goods.SourceTypeEnum;
import cn.htdt.common.enums.market.*;
import cn.htdt.common.enums.user.BelongCompanyEnum;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.LazyEval;
import cn.htdt.common.utils.ListUtil;
import cn.htdt.goodscenter.dto.request.AtomReqGoodsDTO;
import cn.htdt.goodscenter.dto.response.AtomResGoodsDTO;
import cn.htdt.goodsprocess.api.operat.LegacyGoodsCenterService;
import cn.htdt.marketcenter.dto.request.AtomReqCouponSettingDTO;
import cn.htdt.marketcenter.dto.request.AtomReqCouponUserRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomResCouponRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomResCouponSettingDTO;
import cn.htdt.marketprocess.api.analysis.CouponPromotionAnalysisService;
import cn.htdt.marketprocess.api.analysis.CouponUserRecordAnalysisService;
import cn.htdt.marketprocess.api.analysis.PreDeterminedCheckService;
import cn.htdt.marketprocess.biz.conversion.CouponPromotionSettingAssert;
import cn.htdt.marketprocess.biz.utils.CouponInfoUtil;
import cn.htdt.marketprocess.biz.utils.UserInfoUtil;
import cn.htdt.marketprocess.dao.CouponStoreCategoryRelationDao;
import cn.htdt.marketprocess.dao.CouponUserRecordDao;
import cn.htdt.marketprocess.dao.PromotionStoreRelationDao;
import cn.htdt.marketprocess.dao.PromotionVirtualGoodsRelationDao;
import cn.htdt.marketprocess.domain.CouponStoreCategoryRelationDomain;
import cn.htdt.marketprocess.domain.PromotionVirtualGoodsRelationDomain;
import cn.htdt.marketprocess.dto.request.ReqCouponCanUseRecordDTO;
import cn.htdt.marketprocess.dto.request.ReqCouponGoodsAmountDTO;
import cn.htdt.marketprocess.dto.request.ReqCouponInfoDTO;
import cn.htdt.marketprocess.dto.request.ReqStoreCouponListDTO;
import cn.htdt.marketprocess.dto.response.*;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomCouponSettingAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomCouponUserRecordAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomPromotionStoreRelationAnalysisService;
import cn.htdt.marketprocess.vo.CouponCategoryRelationVO;
import cn.htdt.marketprocess.vo.CouponUserRecordVO;
import cn.htdt.marketprocess.vo.PromotionStoreRelationVo;
import cn.htdt.marketprocess.vo.PromotionVirtualGoodsRelationVo;
import cn.htdt.orderprocess.api.OrderAnalysisService;
import cn.htdt.orderprocess.dto.request.ReqOrderItemDTO;
import cn.htdt.userprocess.api.AgentProcessService;
import cn.htdt.userprocess.api.UserPublicService;
import cn.htdt.userprocess.dto.publicDto.response.GetStoreListResponse;
import cn.htdt.userprocess.dto.request.AgentInfoRequest;
import cn.htdt.userprocess.dto.request.MerchantStoreRequest;
import cn.htdt.userprocess.dto.response.AgentInfoResponse;
import cn.htdt.userprocess.dto.response.MerchantInfoResponseT;
import cn.htdt.userprocess.dto.response.MerchantStoreResponse;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 优惠券查询服务
 *
 * <AUTHOR>
 */
@Slf4j
@DubboService
public class CouponPromotionAnalysisServiceImpl implements CouponPromotionAnalysisService {

    @Resource
    private AtomCouponSettingAnalysisService atomCouponSettingAnalysisService;

    @Resource
    private AtomPromotionStoreRelationAnalysisService atomPromotionStoreRelationAnalysisService;

    @Resource
    private AtomCouponUserRecordAnalysisService atomCouponUserRecordAnalysisService;

    @DubboReference
    private LegacyGoodsCenterService legacyGoodsCenterService;

    @DubboReference
    private AgentProcessService agentProcessService;

    @Autowired
    PreDeterminedCheckService preDeterminedCheckService;

    @Autowired
    CouponUserRecordAnalysisService couponUserRecordAnalysisService;

    @Autowired
    private CouponPromotionSettingAssert couponPromotionSettingAssert;

    @Resource
    private CouponStoreCategoryRelationDao couponStoreCategoryRelationDao;

    @DubboReference
    private OrderAnalysisService orderAnalysisService;

    @DubboReference
    private UserPublicService userPublicService;

    @Resource
    private CouponUserRecordDao couponUserRecordDao;

    @Resource
    private PromotionStoreRelationDao promotionStoreRelationDao;

    @Resource
    PromotionVirtualGoodsRelationDao promotionVirtualGoodsRelationDao;


    @Resource
    private UserInfoUtil userInfoUtil;

    @Resource
    private CouponInfoUtil couponInfoUtil;

    /**
     * @Description 获取优惠券活动详情数据
     * <AUTHOR>
     * @Date 2021/4/15 14:22
     * @Param
     * @Return
     */
    @Override
    public ExecuteDTO<ResCouponPromotionSettingDTO> getCouponPromotionSettingInfo(ReqCouponInfoDTO reqCouponInfoDTO) {
        log.info("**CouponPromotionAnalysisServiceImpl**getCouponPromotionSettingInfo-入参：{}", JSON.toJSONString(reqCouponInfoDTO));
        // 入参校验
        couponPromotionSettingAssert.getCouponPromotionSettingInfo(reqCouponInfoDTO);
        AtomReqCouponSettingDTO atomReqCouponSettingDTO = BeanCopierUtil.copy(reqCouponInfoDTO, AtomReqCouponSettingDTO.class);
        ExecuteDTO<AtomResCouponSettingDTO> couponSetting = atomCouponSettingAnalysisService.getCouponSetting(atomReqCouponSettingDTO);
        log.info("**CouponPromotionAnalysisServiceImpl**getCouponPromotionSettingInfo-出参：{}", JSON.toJSONString(couponSetting));
        if (!couponSetting.successFlag()) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        ResCouponPromotionSettingDTO resCouponPromotionSettingDTO = BeanCopierUtil.copy(couponSetting.getData(), ResCouponPromotionSettingDTO.class);
        return ExecuteDTO.success(resCouponPromotionSettingDTO);
    }

    @Override
    public ExecuteDTO<Boolean> checkStoreCouponExist(ReqCouponInfoDTO reqCouponInfoDTO) {

        AtomReqCouponSettingDTO atomReqCouponSettingDTO = new AtomReqCouponSettingDTO();
        atomReqCouponSettingDTO.setStoreNo(reqCouponInfoDTO.getStoreNo());
        atomReqCouponSettingDTO.setCarQueryFlag(reqCouponInfoDTO.getCarQueryFlag());
        // 查询上架优惠券活动
        atomReqCouponSettingDTO.setUpDownFlag(WhetherEnum.YES.getCode());
        ExecuteDTO<List<AtomResCouponSettingDTO>> listExecuteDTO = atomCouponSettingAnalysisService.listCouponSetting4Store(atomReqCouponSettingDTO);
        // 如果调用失败或者不存在优惠券 均返回不存在优惠券
        if (!listExecuteDTO.successFlag() || CollectionUtils.isEmpty(listExecuteDTO.getData())) {
            return ExecuteDTO.success(Boolean.FALSE);
        }
        return ExecuteDTO.success(Boolean.TRUE);
    }

    /**
     * hxg-购物车根据店铺获取优惠券列表
     * 1.获取店铺可用活动
     * 2.通过活动查询优惠券
     *
     * @param
     * @return
     * <AUTHOR>
     */
    @Override
    public ExecuteDTO<List<ResCouponApplyListDTO>> getHxgShoppingStoreCouponList(ReqStoreCouponListDTO storeCouponListDTO) {
        log.info("**CouponPromotionAnalysisServiceImpl**getHxgStoreCouponListByParam-入参：{}", JSON.toJSONString(storeCouponListDTO));
        // 入参校验
        couponPromotionSettingAssert.getPromotionStoreRelationByStoreNo(storeCouponListDTO);
        AtomReqCouponSettingDTO atomReqCouponSettingDTO = new AtomReqCouponSettingDTO();
        atomReqCouponSettingDTO.setStoreNo(storeCouponListDTO.getStoreNo());
        if (StringUtils.isNotBlank(storeCouponListDTO.getMerchantNo())) {
            atomReqCouponSettingDTO.setMerchantNo(storeCouponListDTO.getMerchantNo());
        } else {
            MerchantInfoResponseT merchantInfo = userInfoUtil.getMerchantInfo(storeCouponListDTO.getStoreNo());
            atomReqCouponSettingDTO.setMerchantNo(merchantInfo.getMerchantNo());
        }
        // 查询上架优惠券活动
        atomReqCouponSettingDTO.setUpDownFlag(WhetherEnum.YES.getCode());
        atomReqCouponSettingDTO.setNoPage();

        // 店铺券类型
        List<String> promotionCouponTypes = new ArrayList<>();
        // 粉丝平台+粉丝店铺
        promotionCouponTypes.add(PromotionTypeCouponEnum.SHOP_ONLINE_FANS_COUPON.getCode());
        promotionCouponTypes.add(PromotionTypeCouponEnum.PLATFORM_ONLINE_FANS_COUPON.getCode());
        promotionCouponTypes.add(PromotionTypeCouponEnum.MERCHANT_ONLINE_FANS_COUPON.getCode());
        atomReqCouponSettingDTO.setPromotionCouponTypeList(promotionCouponTypes);
        ExecuteDTO<ExecutePageDTO<AtomResCouponSettingDTO>> homePageCouponsList = atomCouponSettingAnalysisService.getHomePageCouponsList(atomReqCouponSettingDTO);

        if (!homePageCouponsList.successFlag()) {
            return ExecuteDTO.error(homePageCouponsList.getStatus(), homePageCouponsList.getMsg());
        }
        // 校验剔除用户不符合的 和 券不符合的
        List<ResCouponApplyListDTO> couponApplyListDTOS = this.goodsDetailCheckCoupon(storeCouponListDTO, homePageCouponsList.getData().getRows());

        log.info("**CouponPromotionAnalysisServiceImpl**getHxgStoreCouponListByParam-step1:查询店铺下可用活动return：{}", JSON.toJSONString(storeCouponListDTO));
        return ExecuteDTO.success(couponApplyListDTOS);
    }

    /**
     * hxg-通用根据店铺获取优惠券列表（不符合的展示原因）
     * 1.获取店铺可用活动
     * 2.通过活动查询优惠券
     *
     * @param
     * @return
     * <AUTHOR>
     */
    @Override
    public ExecuteDTO<List<ResCouponApplyListDTO>> getHxgStoreCouponList(ReqStoreCouponListDTO storeCouponListDTO) {
        log.info("**CouponPromotionAnalysisServiceImpl**getHxgStoreCouponListByParam-入参：{}", JSON.toJSONString(storeCouponListDTO));
        // 入参校验
        couponPromotionSettingAssert.getPromotionStoreRelationByStoreNo(storeCouponListDTO);
        AtomReqCouponSettingDTO atomReqCouponSettingDTO = new AtomReqCouponSettingDTO();
        atomReqCouponSettingDTO.setStoreNo(storeCouponListDTO.getStoreNo());
        //判断活动创建来源
        atomReqCouponSettingDTO.setSourceType(storeCouponListDTO.getSourceType());
        // 查询上架优惠券活动
        atomReqCouponSettingDTO.setUpDownFlag(WhetherEnum.YES.getCode());
        atomReqCouponSettingDTO.setNoPage();
        // 店铺券类型
        List<String> promotionCouponTypes = new ArrayList<>();
        // 粉丝平台+粉丝店铺
        promotionCouponTypes.add(PromotionTypeCouponEnum.SHOP_ONLINE_FANS_COUPON.getCode());
        promotionCouponTypes.add(PromotionTypeCouponEnum.PLATFORM_ONLINE_FANS_COUPON.getCode());
        atomReqCouponSettingDTO.setPromotionCouponTypeList(promotionCouponTypes);
        ExecuteDTO<ExecutePageDTO<AtomResCouponSettingDTO>> homePageCouponsList = atomCouponSettingAnalysisService.getHomePageCouponsList(atomReqCouponSettingDTO);

        if (!homePageCouponsList.successFlag()) {
            return ExecuteDTO.error(homePageCouponsList.getStatus(), homePageCouponsList.getMsg());
        }
        List<AtomResCouponSettingDTO> couponSettingList = homePageCouponsList.getData().getRows();
        List<ResCouponApplyListDTO> couponApplyListDTOS = BeanCopierUtil.copyList(couponSettingList, ResCouponApplyListDTO.class);
        // 校验剔除用户不符合的 和 券不符合的
        this.checkCoupon(storeCouponListDTO, couponApplyListDTOS);

        log.info("**CouponPromotionAnalysisServiceImpl**getHxgStoreCouponListByParam-step1:查询店铺下可用活动return：{}", JSON.toJSONString(storeCouponListDTO));
        return ExecuteDTO.success(couponApplyListDTOS);
    }

    /**
     * hxg-领券中心券信息
     *
     * @param storeCouponListDTO
     * @return
     * @auther 卜金隆
     */
    @Override
    public ExecuteDTO<ResCouponCentreInfoDTO> getCouponCentreInfo(ReqStoreCouponListDTO storeCouponListDTO) {
        log.info("**CouponPromotionAnalysisServiceImpl**getCouponCentreInfo-入参：{}", JSON.toJSONString(storeCouponListDTO));
        // 入参校验
        couponPromotionSettingAssert.getPromotionStoreRelationByStoreNo(storeCouponListDTO);
        AtomReqCouponSettingDTO atomReqCouponSettingDTO = new AtomReqCouponSettingDTO();
        atomReqCouponSettingDTO.setStoreNo(storeCouponListDTO.getStoreNo());
        // 查询上架优惠券活动
        atomReqCouponSettingDTO.setUpDownFlag(WhetherEnum.YES.getCode());
        atomReqCouponSettingDTO.setNoPage();
        // 店铺券类型
        List<String> promotionCouponTypes = new ArrayList<>();
        //判断是否是代理人
        if (WhetherEnum.YES.getCode().equals(storeCouponListDTO.getAgentFlag())) {
            if (StringUtils.isBlank(storeCouponListDTO.getFanNo())) {
                throw new BaseException(CommonCode.CODE_10000001, "代理人");
            }
            AgentInfoRequest agentInfoRequest = new AgentInfoRequest();
            agentInfoRequest.setFanNo(storeCouponListDTO.getFanNo());
            ExecuteDTO<AgentInfoResponse> agentExecuteDTO = agentProcessService.getAgentInfo(agentInfoRequest);
            if (!agentExecuteDTO.successFlag()) {
                return ExecuteDTO.error(agentExecuteDTO.getStatus(), agentExecuteDTO.getMsg());
            }
            AgentInfoResponse resAgentDTO = agentExecuteDTO.getData();
            if (resAgentDTO == null) {
                return ExecuteDTO.error(MarketErrorCode.CODE_17000001, "该代理人不存在");
            }
            //代理人平台
            promotionCouponTypes.add(PromotionTypeCouponEnum.PLATFORM_ONLINE_AGENT_COUPON.getCode());
            //代理人店铺
            atomReqCouponSettingDTO.setStoreNo(resAgentDTO.getStoreNo());
        } else {
            // 粉丝平台+粉丝店铺
            promotionCouponTypes.add(PromotionTypeCouponEnum.SHOP_ONLINE_FANS_COUPON.getCode());
            promotionCouponTypes.add(PromotionTypeCouponEnum.PLATFORM_ONLINE_FANS_COUPON.getCode());
            promotionCouponTypes.add(PromotionTypeCouponEnum.MERCHANT_ONLINE_FANS_COUPON.getCode());
        }

        // todo xh 2023-06-08 目标二：营销管理 代理人领券 汇通数科体系不取平台营销活动
        MerchantInfoResponseT merchantInfoResponse = this.userInfoUtil.getMerchantInfo(storeCouponListDTO.getStoreNo());
        if (BelongCompanyEnum.getByCode(merchantInfoResponse.getBelongCompany()) == BelongCompanyEnum.HTSK) {
            atomReqCouponSettingDTO.setListSourceType(Arrays.asList(PromotionSourceTypeEnum.PROMOTION_SOURCE_TYPE_TWO.getCode(),
                    PromotionSourceTypeEnum.PROMOTION_SOURCE_TYPE_THREE.getCode()));
        }
        atomReqCouponSettingDTO.setPromotionCouponTypeList(promotionCouponTypes);
        atomReqCouponSettingDTO.setMerchantNo(merchantInfoResponse.getMerchantNo());
        ExecuteDTO<ExecutePageDTO<AtomResCouponSettingDTO>> homePageCouponsList = atomCouponSettingAnalysisService.getHomePageCouponsList(atomReqCouponSettingDTO);

        if (!homePageCouponsList.successFlag()) {
            return ExecuteDTO.error(homePageCouponsList.getStatus(), homePageCouponsList.getMsg());
        }
        List<AtomResCouponSettingDTO> couponSettingList = homePageCouponsList.getData().getRows();
        List<ResCouponApplyListDTO> couponApplyListDTOS = BeanCopierUtil.copyList(couponSettingList, ResCouponApplyListDTO.class);
        // 校验剔除用户不符合的 和 券不符合的
        this.checkCoupon(storeCouponListDTO, couponApplyListDTOS);
        // 封装返回格式
        ResCouponCentreInfoDTO resCouponCentreInfoDTO = new ResCouponCentreInfoDTO();
        List<ResCouponApplyListDTO> storeCouponInfo = new ArrayList<>();
        List<ResCouponApplyListDTO> cloudPoolCouponInfo = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(couponApplyListDTOS)) {
            couponApplyListDTOS.forEach(couponSetting -> {
                if (WhetherEnum.YES.getCode().equals(storeCouponListDTO.getAgentFlag())) {
                    if (PromotionTypeCouponEnum.PLATFORM_ONLINE_AGENT_COUPON.getCode().equals(couponSetting.getPromotionCouponType())) {
                        cloudPoolCouponInfo.add(couponSetting);
                    }
                } else {
                    if (PromotionTypeCouponEnum.PLATFORM_ONLINE_FANS_COUPON.getCode().equals(couponSetting.getPromotionCouponType())) {
                        cloudPoolCouponInfo.add(couponSetting);
                    } else {
                        storeCouponInfo.add(couponSetting);
                    }
                }
            });
        }
        resCouponCentreInfoDTO.setStoreCouponInfo(storeCouponInfo);
        resCouponCentreInfoDTO.setCloudPoolCouponInfo(cloudPoolCouponInfo);
        log.info("**CouponPromotionAnalysisServiceImpl**getCouponCentreInfo-step1:领券中心券信息return：{}", JSON.toJSONString(resCouponCentreInfoDTO));
        return ExecuteDTO.success(resCouponCentreInfoDTO);
    }

    /**
     * hxg-店铺首页-优惠券3-10条
     *
     * @param
     * @return
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResCouponApplyListDTO>> getHomePageCouponsList(ReqStoreCouponListDTO storeCouponListDTO) {
        log.info("**CouponPromotionAnalysisServiceImpl**getHomePageCouponsList-入参：{}", JSON.toJSONString(storeCouponListDTO));
        // 入参校验
        couponPromotionSettingAssert.getPromotionStoreRelationByStoreNo(storeCouponListDTO);
        AtomReqCouponSettingDTO atomReqCouponSettingDTO = new AtomReqCouponSettingDTO();
        atomReqCouponSettingDTO.setStoreNo(storeCouponListDTO.getStoreNo());
        atomReqCouponSettingDTO.setPageNum(storeCouponListDTO.getPageNum());
        atomReqCouponSettingDTO.setPageSize(storeCouponListDTO.getPageSize());
        //判断活动创建来源
        atomReqCouponSettingDTO.setSourceType(storeCouponListDTO.getSourceType());
        //判断活动状态，装修最新优惠券使用
        if (StringUtils.isNotBlank(storeCouponListDTO.getStatus())) {
            atomReqCouponSettingDTO.setStatus(storeCouponListDTO.getStatus());
        }
        // 查询上架优惠券活动
        atomReqCouponSettingDTO.setUpDownFlag(WhetherEnum.YES.getCode());
        // 店铺券类型
        List<String> promotionCouponTypes = new ArrayList<>();
        if (ListUtil.isNotEmpty(storeCouponListDTO.getPromotionCouponTypeList())) {
            promotionCouponTypes.addAll(storeCouponListDTO.getPromotionCouponTypeList());
        } else {
            // 粉丝平台+粉丝店铺
            promotionCouponTypes.add(PromotionTypeCouponEnum.SHOP_ONLINE_FANS_COUPON.getCode());
            promotionCouponTypes.add(PromotionTypeCouponEnum.PLATFORM_ONLINE_FANS_COUPON.getCode());
        }
        atomReqCouponSettingDTO.setPromotionCouponTypeList(promotionCouponTypes);
        ExecuteDTO<ExecutePageDTO<AtomResCouponSettingDTO>> executeDTO = atomCouponSettingAnalysisService.getHomePageCouponsList(atomReqCouponSettingDTO);

        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        List<AtomResCouponSettingDTO> couponSettingList = executeDTO.getData().getRows();
        List<ResCouponApplyListDTO> couponApplyListDTOS = BeanCopierUtil.copyList(couponSettingList, ResCouponApplyListDTO.class);
        // 校验剔除用户不符合的 和 券不符合的
        storeCouponListDTO.setMerchantNo(userInfoUtil.getMerchantInfo(storeCouponListDTO.getStoreNo()).getMerchantNo());
        this.checkCoupon(storeCouponListDTO, couponApplyListDTOS);

        log.info("**CouponPromotionAnalysisServiceImpl**getHomePageCouponsList-step1:店铺首页-优惠券3-10条return：{}", JSON.toJSONString(storeCouponListDTO));
        ExecutePageDTO<ResCouponApplyListDTO> executePageDTO = new ExecutePageDTO<>();
        executePageDTO.setRows(couponApplyListDTOS);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    /**
     * hxg-商品详情页-当前商品可参加的优惠券列表
     *
     * @param
     * @return
     */
    @Override
    public ExecuteDTO<ResCouponDetailListDTO> getGoodsDetailCouponsList(ReqStoreCouponListDTO storeCouponListDTO) {
        log.info("**CouponPromotionAnalysisServiceImpl**getGoodsDetailCouponsList-入参：{}", JSON.toJSONString(storeCouponListDTO));
        // 入参校验
        couponPromotionSettingAssert.getGoodsDetailCouponsList(storeCouponListDTO);
        //查询商品主信息
        AtomReqGoodsDTO reqGoodsDTO = new AtomReqGoodsDTO();
        reqGoodsDTO.setNoPage();
        List<String> goodsNoList = new ArrayList<>();
        goodsNoList.add(storeCouponListDTO.getGoodsNo());
        reqGoodsDTO.setGoodsNos(goodsNoList);
        ExecuteDTO<ExecutePageDTO<AtomResGoodsDTO>> executePageDTOExecuteDTO = legacyGoodsCenterService.getGoodsPage(reqGoodsDTO);
        if (!executePageDTOExecuteDTO.successFlag() || executePageDTOExecuteDTO.getData() == null
                || CollectionUtils.isEmpty(executePageDTOExecuteDTO.getData().getRows())) {
            throw new BaseException(CommonCode.CODE_10000003);
        }
        //过滤优惠券门槛信息
        List<ResGoodsRecordDTO> goodsList = BeanCopierUtil.copyList(executePageDTOExecuteDTO.getData().getRows(), ResGoodsRecordDTO.class);
        ResGoodsRecordDTO resGoodsRecordDTO = goodsList.get(0);

        AtomReqCouponSettingDTO atomReqCouponSettingDTO = new AtomReqCouponSettingDTO();
        atomReqCouponSettingDTO.setStoreNo(resGoodsRecordDTO.getStoreNo());
        if (StringUtils.isNotBlank(resGoodsRecordDTO.getMerchantNo())) {
            atomReqCouponSettingDTO.setMerchantNo(resGoodsRecordDTO.getMerchantNo());
        }
        // 查询上架优惠券活动
        atomReqCouponSettingDTO.setUpDownFlag(WhetherEnum.YES.getCode());
        // 云池标识
        if (GoodsSourceTypeEnum.CLOUD_POOL_DISTRIBUTE.getCode().equals(resGoodsRecordDTO.getGoodsSourceType())) {
            storeCouponListDTO.setIsCloudGoods(NumConstant.ONE);
        } else {
            storeCouponListDTO.setIsCloudGoods(NumConstant.TWO);
        }
        List<String> promotionCouponTypes = new ArrayList<>();
        // 云池
        if (storeCouponListDTO.getIsCloudGoods() != null && NumConstant.ONE == storeCouponListDTO.getIsCloudGoods()) {
            promotionCouponTypes.add(PromotionTypeCouponEnum.PLATFORM_ONLINE_FANS_COUPON.getCode());
        } else {
            // 普通
            promotionCouponTypes.add(PromotionTypeCouponEnum.SHOP_ONLINE_FANS_COUPON.getCode());
            // 因为平台可以创店铺通用券 这里查询出平台类型券 然后要过滤掉云池券 只保留 couponUseScope=1001
            promotionCouponTypes.add(PromotionTypeCouponEnum.PLATFORM_ONLINE_FANS_COUPON.getCode());
            // 商家-网店粉丝领券
            promotionCouponTypes.add(PromotionTypeCouponEnum.MERCHANT_ONLINE_FANS_COUPON.getCode());
        }
        atomReqCouponSettingDTO.setPromotionCouponTypeList(promotionCouponTypes);
        atomReqCouponSettingDTO.setNoPage();
        // 店铺下所有可用
        ExecuteDTO<ExecutePageDTO<AtomResCouponSettingDTO>> couponsList = atomCouponSettingAnalysisService.getHomePageCouponsList(atomReqCouponSettingDTO);

        if (!couponsList.successFlag()) {
            return ExecuteDTO.error(couponsList.getStatus(), couponsList.getMsg());
        }
        List<AtomResCouponSettingDTO> couponSettingList = couponsList.getData().getRows();
        // 校验剔除 不可领取和店铺内所有券已抢光 的优惠券
        List<ResCouponApplyListDTO> couponApplyListDTOS = this.goodsDetailCheckCoupon(storeCouponListDTO, couponSettingList);
        // 如果指定商品可用的券，剔除品牌品类不符的 剔除指定商品券
        List<ResCouponApplyListDTO> couponApplyDTO = this.checkCouponByParam(resGoodsRecordDTO, couponApplyListDTOS);

        // 添加用户已领取可用的优惠券，和累加领取数量
        // 用户已领取的并且该商品可用的优惠券
        List<ResCouponCanUserRecordDTO> couponCanUseListData = null;
        if (StringUtils.isNotBlank(storeCouponListDTO.getFanNo())) {
            ReqCouponCanUseRecordDTO reqCouponCanUseRecordDTO = BeanCopierUtil.copy(storeCouponListDTO, ReqCouponCanUseRecordDTO.class);
            reqCouponCanUseRecordDTO.setStoreNo(resGoodsRecordDTO.getStoreNo());
            ReqCouponGoodsAmountDTO couponGoodsAmountDTO = new ReqCouponGoodsAmountDTO();
            couponGoodsAmountDTO.setGoodsNo(storeCouponListDTO.getGoodsNo());
            List<ReqCouponGoodsAmountDTO> couponGoodsAmountList = new ArrayList<>();
            couponGoodsAmountList.add(couponGoodsAmountDTO);
            reqCouponCanUseRecordDTO.setGoodsInfo(couponGoodsAmountList);
            reqCouponCanUseRecordDTO.setCouponUseChannel(CouponUseChannelEnum.COUPON_USE_CHANNEL_ONE.getCode());
            reqCouponCanUseRecordDTO.setGoodsDetailQuery(NumConstant.TWO);  //商品详情页指定按领券时间倒序排
            ExecuteDTO<List<ResCouponCanUserRecordDTO>> couponCanUseList = couponUserRecordAnalysisService.getCouponCanUseList(reqCouponCanUseRecordDTO);
            if (!couponCanUseList.successFlag()) {
                return ExecuteDTO.error(couponCanUseList.getStatus(), couponCanUseList.getMsg());
            }
            // 用户已领取的并且该商品可用的优惠券
            couponCanUseListData = couponCanUseList.getData();
        }
        ResCouponDetailListDTO resCouponDetailListDTO = new ResCouponDetailListDTO();
        resCouponDetailListDTO.setCouponApplyList(couponApplyDTO);
        resCouponDetailListDTO.setCouponAlreadyList(BeanCopierUtil.copyList(couponCanUseListData, ResCouponApplyListDTO.class));
        log.info("**CouponPromotionAnalysisServiceImpl**getGoodsDetailCouponsList-step1:当前商品可参加的优惠券列表return：{}", JSON.toJSONString(storeCouponListDTO));
        return ExecuteDTO.success(resCouponDetailListDTO);
    }

    /**
     * hxg-商品详情页 3条券
     *
     * @param
     * @return
     * @auther 卜金隆
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResCouponApplyListDTO>> getGoodsDetailAndListCoupons(ReqStoreCouponListDTO storeCouponListDTO) {
        log.info("**CouponPromotionAnalysisServiceImpl**getGoodsDetailCouponsList-入参：{}", JSON.toJSONString(storeCouponListDTO));
        // 入参校验
        couponPromotionSettingAssert.getGoodsDetailCouponsList(storeCouponListDTO);

        //查询商品主信息
        AtomReqGoodsDTO reqGoodsDTO = new AtomReqGoodsDTO();
        reqGoodsDTO.setNoPage();
        List<String> goodsNoList = new ArrayList<>();
        goodsNoList.add(storeCouponListDTO.getGoodsNo());
        reqGoodsDTO.setGoodsNos(goodsNoList);
        ExecuteDTO<ExecutePageDTO<AtomResGoodsDTO>> executePageDTOExecuteDTO = legacyGoodsCenterService.getGoodsPage(reqGoodsDTO);
        if (!executePageDTOExecuteDTO.successFlag() || executePageDTOExecuteDTO.getData() == null
                || CollectionUtils.isEmpty(executePageDTOExecuteDTO.getData().getRows())) {
            throw new BaseException(CommonCode.CODE_10000003);
        }
        //过滤优惠券门槛信息
        List<ResGoodsRecordDTO> goodsList = BeanCopierUtil.copyList(executePageDTOExecuteDTO.getData().getRows(), ResGoodsRecordDTO.class);
        ResGoodsRecordDTO resGoodsRecordDTO = goodsList.get(0);

        AtomReqCouponSettingDTO atomReqCouponSettingDTO = new AtomReqCouponSettingDTO();
        atomReqCouponSettingDTO.setStoreNo(storeCouponListDTO.getStoreNo());
        if (StringUtils.isNotBlank(storeCouponListDTO.getMerchantNo())) {
            atomReqCouponSettingDTO.setMerchantNo(storeCouponListDTO.getMerchantNo());
        } else {
            MerchantInfoResponseT merchantInfo = userInfoUtil.getMerchantInfo(storeCouponListDTO.getStoreNo());
            atomReqCouponSettingDTO.setMerchantNo(merchantInfo.getMerchantNo());
        }
        // 查询上架优惠券活动
        atomReqCouponSettingDTO.setUpDownFlag(WhetherEnum.YES.getCode());
        // 云池标识
        if (GoodsSourceTypeEnum.CLOUD_POOL_DISTRIBUTE.getCode().equals(resGoodsRecordDTO.getGoodsSourceType())) {
            storeCouponListDTO.setIsCloudGoods(NumConstant.ONE);
        } else {
            storeCouponListDTO.setIsCloudGoods(NumConstant.TWO);
        }
        List<String> promotionCouponTypes = new ArrayList<>();
        // 云池
        if (storeCouponListDTO.getIsCloudGoods() != null && NumConstant.ONE == storeCouponListDTO.getIsCloudGoods()) {
            promotionCouponTypes.add(PromotionTypeCouponEnum.PLATFORM_ONLINE_FANS_COUPON.getCode());
        } else {
            // 普通
            promotionCouponTypes.add(PromotionTypeCouponEnum.SHOP_ONLINE_FANS_COUPON.getCode());
            // 因为平台可以创店铺通用券 这里查询出平台类型券 然后要过滤掉云池券 只保留 couponUseScope=1001
            promotionCouponTypes.add(PromotionTypeCouponEnum.PLATFORM_ONLINE_FANS_COUPON.getCode());
            // 商家-网店粉丝领券
            promotionCouponTypes.add(PromotionTypeCouponEnum.MERCHANT_ONLINE_FANS_COUPON.getCode());
        }
        atomReqCouponSettingDTO.setPromotionCouponTypeList(promotionCouponTypes);
        atomReqCouponSettingDTO.setNoPage();
        // 店铺下所有可用
        ExecuteDTO<ExecutePageDTO<AtomResCouponSettingDTO>> couponsList = atomCouponSettingAnalysisService.getHomePageCouponsList(atomReqCouponSettingDTO);

        if (!couponsList.successFlag()) {
            return ExecuteDTO.error(couponsList.getStatus(), couponsList.getMsg());
        }
        List<AtomResCouponSettingDTO> couponSettingList = couponsList.getData().getRows();

        // 校验剔除 不可领取和店铺内所有券已抢光 的优惠券
        List<ResCouponApplyListDTO> couponApplyListDTOS = this.goodsDetailCheckCoupon(storeCouponListDTO, couponSettingList);
        // 如果指定商品可用的券，剔除品牌品类不符的 剔除指定商品券
        List<ResCouponApplyListDTO> couponApplyDTO = this.checkCouponByParam(resGoodsRecordDTO, couponApplyListDTOS);

        // 获取商详页展示的优惠券数
        long couponNum = 0;
        if (CollectionUtils.isNotEmpty(couponApplyDTO)) {
            couponNum = couponApplyDTO.size();
        }
        // 如果没有可领券数据记录，则查询可用的券数据放商详页展示
        if (StringUtils.isNotBlank(storeCouponListDTO.getFanNo()) && CollectionUtils.isEmpty(couponApplyDTO)) {
            ReqCouponCanUseRecordDTO reqCouponCanUseRecordDTO = BeanCopierUtil.copy(storeCouponListDTO, ReqCouponCanUseRecordDTO.class);
            ReqCouponGoodsAmountDTO couponGoodsAmountDTO = new ReqCouponGoodsAmountDTO();
            couponGoodsAmountDTO.setGoodsNo(storeCouponListDTO.getGoodsNo());
            List<ReqCouponGoodsAmountDTO> couponGoodsAmountList = new ArrayList<>();
            couponGoodsAmountList.add(couponGoodsAmountDTO);
            reqCouponCanUseRecordDTO.setGoodsInfo(couponGoodsAmountList);
            reqCouponCanUseRecordDTO.setGoodsDetailQuery(NumConstant.TWO);  //商品详情页指定按领券时间倒序排
            ExecuteDTO<List<ResCouponCanUserRecordDTO>> couponCanUseList = couponUserRecordAnalysisService.getCouponCanUseList(reqCouponCanUseRecordDTO);
            if (!couponCanUseList.successFlag()) {
                return ExecuteDTO.error(couponCanUseList.getStatus(), couponCanUseList.getMsg());
            }
            // 用户已领取的并且该商品可用的优惠券
            couponApplyDTO = BeanCopierUtil.copyList(couponCanUseList.getData(), ResCouponApplyListDTO.class);
            // 设置优惠券面值额
            if (CollectionUtils.isNotEmpty(couponApplyDTO)) {
                couponApplyDTO.forEach(couponDTO -> {
                    couponCanUseList.getData().forEach(couponCanUse -> {
                        if (couponDTO.getCouponNo().equals(couponCanUse.getCouponNo())) {
                            couponDTO.setCouponValue(couponCanUse.getUserCouponValue());
                        }
                    });
                });
            }
            couponNum = CollectionUtils.isEmpty(couponApplyDTO) ? 0 : couponApplyDTO.size();
        }
        // 截取3条
        List<ResCouponApplyListDTO> resCouponApply = this.getResCouponApplyList(couponApplyDTO);
        ExecutePageDTO<ResCouponApplyListDTO> executePageDTO = new ExecutePageDTO<>();
        executePageDTO.setRows(resCouponApply);
        executePageDTO.setTotal(couponNum);
        return ExecuteDTO.success(executePageDTO);
    }

    /**
     * 商品详情页专用-校验剔除 不可领取和店铺内所有券已抢光 的优惠券
     *
     * <AUTHOR>
     * @date 2021-12-28
     */
    private List<ResCouponApplyListDTO> goodsDetailCheckCoupon(ReqStoreCouponListDTO storeCouponListDTO,
                                                               List<AtomResCouponSettingDTO> couponSettingList) {
        if (CollectionUtils.isEmpty(couponSettingList)) {
            return null;
        }
        // 反参
        List<ResCouponApplyListDTO> couponApplyListDTOS = new ArrayList<>();

        LazyEval<UserScopeEnum> platformNewFans = this.checkNewFans(storeCouponListDTO.getFanNo(), null, null);
        LazyEval<UserScopeEnum> merchantNewFans = this.checkNewFans(storeCouponListDTO.getFanNo(), null, storeCouponListDTO.getMerchantNo());
        LazyEval<UserScopeEnum> storeNewFans = this.checkNewFans(storeCouponListDTO.getFanNo(), storeCouponListDTO.getStoreNo(), null);

        for (AtomResCouponSettingDTO couponSettingDTO : couponSettingList) {
            // 新老用户限制不符合，丢弃该优惠券数据
            if (UserScopeEnum.NONE.getCode().equals(couponSettingDTO.getUserScope())) {
                // 不限制粉丝范围，无需校验是否新用户
            } else if (UserScopeEnum.MEMBER.getCode().equals(couponSettingDTO.getUserScope())) {
                // 优惠券暂无会员专享
                continue;
            } else {
                // 云池优惠券
                if (PromotionTypeCouponEnum.PLATFORM_ONLINE_AGENT_COUPON.getCode().equals(couponSettingDTO.getPromotionCouponType())
                        || PromotionTypeCouponEnum.PLATFORM_ONLINE_FANS_COUPON.getCode().equals(couponSettingDTO.getPromotionCouponType())) {
                    if (!platformNewFans.get().getCode().equals(couponSettingDTO.getUserScope())) {
                        continue;
                    }
                } else if (PromotionTypeCouponEnum.MERCHANT_ONLINE_FANS_COUPON.getCode().equals(couponSettingDTO.getPromotionCouponType())) {
                    // 商家优惠券
                    if (!merchantNewFans.get().getCode().equals(couponSettingDTO.getUserScope())) {
                        continue;
                    }
                } else {
                    // 店铺优惠券
                    if (!storeNewFans.get().getCode().equals(couponSettingDTO.getUserScope())) {
                        continue;
                    }
                }
            }

            // 券活动发行量超过配置数，排除
            if (WhetherEnum.YES.getCode().equals(couponSettingDTO.getCirculationLimitFlag()) && couponSettingDTO.getRemainNum() <= NumConstant.ZERO) {
                continue;
            }

            // 优惠券可用校验
            AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO = BeanCopierUtil.copy(storeCouponListDTO, AtomReqCouponUserRecordDTO.class);
            atomReqCouponUserRecordDTO.setPromotionNo(couponSettingDTO.getPromotionNo());
            atomReqCouponUserRecordDTO.setCouponNo(couponSettingDTO.getCouponNo());
            // 优惠券可获得次数配置
            ExecuteDTO<AtomResCouponRecordDTO> couponUserRecordCounts = atomCouponUserRecordAnalysisService.getCouponUserRecordCounts(atomReqCouponUserRecordDTO);
            if (!couponUserRecordCounts.successFlag() || couponUserRecordCounts.getData() == null) {
                throw new BaseException(CommonCode.CODE_10000003);
            }
            AtomResCouponRecordDTO couponUserRecordCountsData = couponUserRecordCounts.getData();
            // 用户单日领券数  || 单个用户总共可获得张数 达到上限 , 排除
            if (couponSettingDTO.getUserDailyNum() <= couponUserRecordCountsData.getUserDailyNum() ||
                    couponSettingDTO.getUserTotalNum() <= couponUserRecordCountsData.getUserTotalNum()) {
                // 用户已领取
                // couponSettingDTO.setCouponStatus(CouponGetStatusEnum.COUPON_GET_ALREADY.getCode());
                continue;
            }
            // 平台券在店铺已抢光，排除（店铺用户单日领券数 || 店铺用户总共可获得张数）,商家券不限制店铺总获得数
            if (PromotionTypeCouponEnum.PLATFORM_ONLINE_AGENT_COUPON.getCode().equals(couponSettingDTO.getPromotionCouponType())
                    || PromotionTypeCouponEnum.PLATFORM_ONLINE_FANS_COUPON.getCode().equals(couponSettingDTO.getPromotionCouponType())) {
                if (couponSettingDTO.getStoreDailyNum() <= couponUserRecordCountsData.getStoreDailyNum() ||
                        couponSettingDTO.getStoreTotalNum() <= couponUserRecordCountsData.getStoreTotalNum()) {
                    continue;
                }
            }

            ResCouponApplyListDTO couponApplyDTO = BeanCopierUtil.copy(couponSettingDTO, ResCouponApplyListDTO.class);
            // 默认可领取
            couponApplyDTO.setCouponStatus(CouponGetStatusEnum.COUPON_GET_CAN.getCode());
            couponApplyDTO.setUseAlreadyCount(couponUserRecordCountsData.getStoreUserTotalNum());
            couponApplyListDTOS.add(couponApplyDTO);
        }
        return couponApplyListDTOS;
    }

    private LazyEval<UserScopeEnum> checkNewFans(String fanNo, String storeNo, String merchantNo) {
        return new LazyEval<>(() -> {
            if (StringUtils.isNotBlank(fanNo)) {
                // 查询是否是该店铺新用户
                ExecuteDTO<Boolean> booleanExecuteDTO = preDeterminedCheckService.checkNewFans(fanNo, storeNo, merchantNo);
                if (booleanExecuteDTO == null || !booleanExecuteDTO.successFlag()) {
                    throw new BaseException(CommonCode.CODE_10000003);
                }
                return booleanExecuteDTO.getData() ? UserScopeEnum.NEW_FANS : UserScopeEnum.OLD_FANS;
            }
            return UserScopeEnum.NEW_FANS;
        });
    }

    /**
     * hxg-商品列表页 3条券
     *
     * @param
     * @return
     * @auther 卜金隆
     */
    @Override
    public ExecuteDTO<Map<String, List<ResCouponApplyListDTO>>> getGoodsListCoupons(ReqStoreCouponListDTO storeCouponListDTO) {
        log.info("**CouponPromotionAnalysisServiceImpl**getGoodsListCoupons-入参：{}", JSON.toJSONString(storeCouponListDTO));
        // 入参校验
        this.couponPromotionSettingAssert.getGoodsListCouponsList(storeCouponListDTO);
        //筛选出店铺商品
        List<ResGoodsRecordDTO> storeGoodsRecoreDTOList = storeCouponListDTO.getResGoodsInfo().stream()
                .filter(resGoodsRecordDTO -> (resGoodsRecordDTO.getGoodsSourceType().equals(GoodsSourceTypeEnum.STORE.getCode()) ||
                        resGoodsRecordDTO.getGoodsSourceType().equals(GoodsSourceTypeEnum.MERCHANT_DISTRIBUTE.getCode()))).collect(Collectors.toList());
        //筛选出云池商品
        List<ResGoodsRecordDTO> cloudPoolGoodsRecoreDTOList = storeCouponListDTO.getResGoodsInfo().stream()
                .filter(resGoodsRecordDTO -> resGoodsRecordDTO.getGoodsSourceType().equals(GoodsSourceTypeEnum.CLOUD_POOL_DISTRIBUTE.getCode())).collect(Collectors.toList());
        // 封装返回参数
        Map<String, List<ResCouponApplyListDTO>> couponSettingInfo = new HashMap<>();
        //店铺商品
        if (CollectionUtils.isNotEmpty(storeGoodsRecoreDTOList)) {
            //查询店铺优惠券
            List<AtomResCouponSettingDTO> storeCouponList = this.getStoreCoupon(storeCouponListDTO);
            //将满足商品要求三张优惠券赋值到商品列表
            this.getThreeCouponOnGoodsList(storeGoodsRecoreDTOList, couponSettingInfo, storeCouponList);
        }
        //云池商品
        if (CollectionUtils.isNotEmpty(cloudPoolGoodsRecoreDTOList)) {
            //查询云池优惠券
            List<AtomResCouponSettingDTO> coolPoolCouponList = this.getCoolPoolCoupon(storeCouponListDTO);
            //将满足商品要求三张优惠券赋值到商品列表
            this.getThreeCouponOnGoodsList(cloudPoolGoodsRecoreDTOList, couponSettingInfo, coolPoolCouponList);
        }
        return ExecuteDTO.success(couponSettingInfo);
    }


    @Override
    public ExecuteDTO<ResCouponApplyListDTO> getCouponDetailByCenter(ReqCouponInfoDTO couponInfoDTO) {
        log.info("MarketProcess-CouponPromotionAnalysisServiceImpl-getCouponDetailByCenter-params-start");
        log.info("MarketProcess-CouponPromotionAnalysisServiceImpl-getCouponDetailByCenter-params={}", JSON.toJSONString(couponInfoDTO));
        AtomReqCouponSettingDTO atomReqCouponSettingDTO = BeanCopierUtil.copy(couponInfoDTO, AtomReqCouponSettingDTO.class);
        // 前端传了店铺编码，用于校验是否新人（平台新人，店铺新人），置空避免查询平台优惠券指定店铺时查不到数据
        atomReqCouponSettingDTO.setStoreNo(null);
        ExecuteDTO<AtomResCouponSettingDTO> executeDTO = atomCouponSettingAnalysisService.getCouponSetting(atomReqCouponSettingDTO);
        log.info("MarketProcess-CouponPromotionAnalysisServiceImpl-getCouponDetailByCenter-return={}", JSON.toJSONString(executeDTO));
        //判断状态
        if (!executeDTO.successFlag() || executeDTO.getData() == null) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResCouponSettingDTO resCouponSettingDTO = BeanCopierUtil.copy(executeDTO.getData(), ResCouponSettingDTO.class);
        // 设置活动状态
        this.setOneCouponStatus(resCouponSettingDTO);
        ResCouponApplyListDTO resCouponApplyListDTO = BeanCopierUtil.copy(resCouponSettingDTO, ResCouponApplyListDTO.class);
        ArrayList<ResCouponApplyListDTO> resCouponApplyList = new ArrayList<>();
        resCouponApplyList.add(resCouponApplyListDTO);
        // 校验剔除用户不符合的 和 券不符合的
        this.checkCoupon(BeanCopierUtil.copy(couponInfoDTO, ReqStoreCouponListDTO.class), resCouponApplyList);

        // 当前用户剩余可获得张数
        if (PromotionTypeCouponEnum.VOUCHER.getCode().equals(resCouponApplyListDTO.getPromotionCouponType())) {
            // 获取当前粉丝已购买数量
            CouponUserRecordVO couponUserRecordVO = new CouponUserRecordVO();
            couponUserRecordVO.setFanNo(couponInfoDTO.getFanNo());
            couponUserRecordVO.setCouponNo(couponInfoDTO.getCouponNo());
            couponUserRecordVO.setStoreNo(couponInfoDTO.getStoreNo());
            int count = couponUserRecordDao.selectCouponCountByTerm(couponUserRecordVO);
            // 获取商品编号
            PromotionVirtualGoodsRelationVo queryVo = new PromotionVirtualGoodsRelationVo();
            queryVo.setPromotionNo(couponInfoDTO.getCouponNo());
            PromotionVirtualGoodsRelationDomain goodsRelationDomain = this.promotionVirtualGoodsRelationDao.selectOneByParams(queryVo);
            if (goodsRelationDomain == null) {
                return ExecuteDTO.error(GoodsErrorCode.CODE_12000001.getCode(), GoodsErrorCode.CODE_12000001.getInMsg());
            }
            // 获取当前粉丝购买但未支付的数量
            ReqOrderItemDTO reqOrderItemDTO = new ReqOrderItemDTO();
            reqOrderItemDTO.setGoodsNo(goodsRelationDomain.getGoodsNo());
            reqOrderItemDTO.setBuyerNo(couponInfoDTO.getFanNo());
            ExecuteDTO<Integer> countExecuteDTO = this.orderAnalysisService.getVirtualGoodsSaleNum(reqOrderItemDTO);
            if (!countExecuteDTO.successFlag()) {
                return ExecuteDTO.error(GoodsErrorCode.CODE_12000001.getCode(), "订单" + GoodsErrorCode.CODE_12000001.getInMsg());
            }
            int orderCount = countExecuteDTO.getData() == null ? NumConstant.ZERO : countExecuteDTO.getData();
            log.info("MarketProcess-CouponPromotionAnalysisServiceImpl-getCouponDetailByCenter-count={}", count);
            log.info("MarketProcess-CouponPromotionAnalysisServiceImpl-getCouponDetailByCenter-orderCount={}", orderCount);
            // 若购买数量>剩余限购数，则提示：“已超限购数量，你最多还可买X个”（X取值当前粉丝账户剩余可购数量）
            int userTotalNum = resCouponApplyListDTO.getUserTotalNum() == null ? NumConstant.ZERO : resCouponApplyListDTO.getUserTotalNum();
            int maxBuyNum = userTotalNum - count - orderCount;
            resCouponApplyListDTO.setUserRemainNum(Math.max(maxBuyNum, NumConstant.ZERO));
        } else if (null != resCouponApplyListDTO.getUseAlreadyCount()) {
            resCouponApplyListDTO.setUserRemainNum(resCouponApplyListDTO.getUserTotalNum() - resCouponApplyListDTO.getUseAlreadyCount());
        } else {
            resCouponApplyListDTO.setUserRemainNum(resCouponApplyListDTO.getUserTotalNum());
        }
        // 适用店铺列表
        List<ResPromotionStoreRelationDTO> suitStoreList = new ArrayList<>();
        if (PromotionTypeCouponEnum.SHOP_MANUAL_COUPON.getCode().equals(resCouponSettingDTO.getPromotionCouponType())
                || PromotionTypeCouponEnum.SHOP_ONLINE_FANS_COUPON.getCode().equals(resCouponSettingDTO.getPromotionCouponType())) {
            ResPromotionStoreRelationDTO storeRelationDTO = new ResPromotionStoreRelationDTO();
            storeRelationDTO.setStoreNo(resCouponSettingDTO.getStoreNo());
            storeRelationDTO.setStoreName(resCouponSettingDTO.getStoreName());
            suitStoreList.add(storeRelationDTO);
        } else if (PromotionTypeCouponEnum.MERCHANT_ONLINE_FANS_COUPON.getCode().equals(resCouponSettingDTO.getPromotionCouponType())) {
            PromotionStoreRelationVo promotionStoreRelationVo = new PromotionStoreRelationVo();
            promotionStoreRelationVo.setPromotionNo(couponInfoDTO.getCouponNo());
            promotionStoreRelationVo.setStoreSourceType(SourceTypeEnum.SOURCE_TYPE_1002.getCode());
            promotionStoreRelationVo.setDisableFlag(WhetherEnum.NO.getCode());
            List<PromotionStoreRelationVo> promotionStoreRelationVos = promotionStoreRelationDao.selectPromotionStoreRelationList(promotionStoreRelationVo);
            if (ListUtil.isNotEmpty(promotionStoreRelationVos)) {
                Map<String, List<PromotionStoreRelationVo>> storeListMap = ListUtil.groupBy(promotionStoreRelationVos, PromotionStoreRelationVo::getStoreType);
                List<PromotionStoreRelationVo> merhcantConfig = storeListMap.get(StoreTypeEnum.STORE_TYPE_ONE.getCode());
                if (ListUtil.isNotEmpty(merhcantConfig)) {
                    PromotionStoreRelationVo merhcantLine = ListUtil.getFirst(merhcantConfig);
                    // 如果商家上架，则过滤店铺下架的数据，商家下架，则所有店铺都下架
                    if (StoreUpDownEnum.UP.getCode().equals(merhcantLine.getUpDownFlag())) {
                        MerchantStoreRequest merchantStoreRequest = new MerchantStoreRequest();
                        merchantStoreRequest.setMerchantNo(resCouponSettingDTO.getMerchantNo());
                        ExecuteDTO<List<MerchantStoreResponse>> merchantStoreDto =  userPublicService.queryMerchantStore(merchantStoreRequest);
                        if (null != merchantStoreDto && ListUtil.isNotEmpty(merchantStoreDto.getData())) {
                            Set<String> downStoreNoSet = storeListMap.getOrDefault(StoreTypeEnum.STORE_TYPE_THREE.getCode(), new ArrayList<>()).stream()
                                    .filter(p -> StoreUpDownEnum.DOWN.getCode().equals(p.getUpDownFlag()) && StringUtils.isNotBlank(p.getStoreNo()))
                                    .map(PromotionStoreRelationVo::getStoreNo).collect(Collectors.toSet());
                            for (MerchantStoreResponse storeResponse : merchantStoreDto.getData()) {
                                if (!downStoreNoSet.contains(storeResponse.getStoreNo())) {
                                    ResPromotionStoreRelationDTO storeRelationDTO = new ResPromotionStoreRelationDTO();
                                    storeRelationDTO.setStoreNo(storeResponse.getStoreNo());
                                    storeRelationDTO.setStoreName(storeResponse.getStoreName());
                                    suitStoreList.add(storeRelationDTO);
                                }
                            }
                        }
                    }
                } else {
                    List<String> storeNos = promotionStoreRelationVos.stream().map(PromotionStoreRelationVo::getStoreNo).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                    ExecuteDTO<List<GetStoreListResponse>> resStoreListEo = userPublicService.queryStoreList(storeNos);
                    if (null != resStoreListEo && ListUtil.isNotEmpty(resStoreListEo.getData())) {
                        suitStoreList.addAll(BeanCopierUtil.copyList(resStoreListEo.getData(), ResPromotionStoreRelationDTO.class));
                    }
                }
            }
        }
        resCouponApplyListDTO.setSuitStoreList(suitStoreList);
        return ExecuteDTO.success(resCouponApplyList.get(NumConstant.ZERO));
    }

    /**
     * 将满足商品要求三张优惠券赋值到商品列表
     *
     * @param cloudPoolGoodsRecoreDTOList
     * @param couponSettingInfo
     * @param coolPoolCouponList
     */
    private void getThreeCouponOnGoodsList(List<ResGoodsRecordDTO> cloudPoolGoodsRecoreDTOList, Map<String, List<ResCouponApplyListDTO>> couponSettingInfo, List<AtomResCouponSettingDTO> coolPoolCouponList) {
        // 如果指定商品可用的券，剔除品牌品类不符的 剔除指定商品券
        List<ResCouponApplyListDTO> couponApplyListDTOS = BeanCopierUtil.copyList(coolPoolCouponList, ResCouponApplyListDTO.class);
        //获取优惠券过滤条件
        ResGoodsCouponConditionDTO couponConditionDTO = this.couponInfoUtil.getResGoodsCouponConditionDTO(couponApplyListDTOS);
        cloudPoolGoodsRecoreDTOList.forEach(resGoodsRecordDTO -> {
            if (CollectionUtils.isNotEmpty(couponApplyListDTOS)) {
                log.info("**CouponPromotionAnalysisServiceImpl**checkCoupon-step3:剔除品牌品类不符的 剔除指定商品券：{}", JSON.toJSONString(couponApplyListDTOS));
                //如果指定商品可用的券，剔除品牌品类不符的 剔除指定商品券(给优惠券集合使用)
                List<ResCouponApplyListDTO> couponApplyDTO = this.getCouponApplyList(couponConditionDTO, resGoodsRecordDTO, couponApplyListDTOS);
                // 截取3条
                List<ResCouponApplyListDTO> resCouponApply = this.getResCouponApplyList(couponApplyDTO);
                couponSettingInfo.put(resGoodsRecordDTO.getGoodsNo(), resCouponApply);
            }
        });
    }

    /**
     * 获取店铺优惠券
     */
    private List<AtomResCouponSettingDTO> getStoreCoupon(ReqStoreCouponListDTO storeCouponListDTO) {
        // 店铺优惠券入参
        AtomReqCouponSettingDTO atomReqCouponSettingDTO = new AtomReqCouponSettingDTO();
        atomReqCouponSettingDTO.setNoPage();
        atomReqCouponSettingDTO.setStoreNo(storeCouponListDTO.getStoreNo());
        // 查询上架优惠券活动
        atomReqCouponSettingDTO.setUpDownFlag(WhetherEnum.YES.getCode());
        // 店铺券类型
        List<String> promotionCouponTypes = new ArrayList<>();
        // 店铺
        promotionCouponTypes.add(PromotionTypeCouponEnum.SHOP_ONLINE_FANS_COUPON.getCode());
        promotionCouponTypes.add(PromotionTypeCouponEnum.PLATFORM_ONLINE_FANS_COUPON.getCode());
        atomReqCouponSettingDTO.setPromotionCouponTypeList(promotionCouponTypes);
        // 店铺下所有可用
        ExecuteDTO<ExecutePageDTO<AtomResCouponSettingDTO>> couponsList = atomCouponSettingAnalysisService.getHomePageCouponsList(atomReqCouponSettingDTO);
        log.info("**CouponPromotionAnalysisServiceImpl**getStoreCoupon-店铺优惠券出参：{}", JSON.toJSONString(couponsList));
        if (!couponsList.successFlag()) {
            throw new BaseException(CommonCode.CODE_10000003);
        }
        List<AtomResCouponSettingDTO> returnDto = new ArrayList<>();
        // 因为平台可以创店铺通用券 这里查询出平台类型券 然后要过滤掉云池券 只保留 couponUseScope=1001
        if (CollectionUtils.isNotEmpty(couponsList.getData().getRows())) {
            couponsList.getData().getRows().forEach(coupons -> {
                if (CouponUseScopeEnum.COUPON_USE_SCOPE_ONE.getCode().equals(coupons.getCouponUseScope())) {
                    if (PromotionTypeCouponEnum.SHOP_ONLINE_FANS_COUPON.getCode().equals(coupons.getPromotionCouponType())
                            || PromotionTypeCouponEnum.PLATFORM_ONLINE_FANS_COUPON.getCode().equals(coupons.getPromotionCouponType())) {
                        returnDto.add(coupons);
                    }
                } else if (CouponUseScopeEnum.COUPON_USE_SCOPE_FIVE.getCode().equals(coupons.getCouponUseScope())) {
                    returnDto.add(coupons);
                } else if (CouponUseScopeEnum.COUPON_USE_SCOPE_FOUR.getCode().equals(coupons.getCouponUseScope())) {
                    returnDto.add(coupons);
                } else if (CouponUseScopeEnum.COUPON_USE_SCOPE_SIX.getCode().equals(coupons.getCouponUseScope())) {
                    returnDto.add(coupons);
                }
            });
        }
        return returnDto;
    }

    /**
     * 获取云池优惠券
     */
    private List<AtomResCouponSettingDTO> getCoolPoolCoupon(ReqStoreCouponListDTO storeCouponListDTO) {
        // 店铺优惠券入参
        AtomReqCouponSettingDTO atomReqCouponSettingDTO = new AtomReqCouponSettingDTO();
        atomReqCouponSettingDTO.setNoPage();
        atomReqCouponSettingDTO.setStoreNo(storeCouponListDTO.getStoreNo());
        // 查询上架优惠券活动
        atomReqCouponSettingDTO.setUpDownFlag(WhetherEnum.YES.getCode());
        // 云池券类型
        List<String> coolCouponTypes = new ArrayList<>();
        // 云池
        coolCouponTypes.add(PromotionTypeCouponEnum.PLATFORM_ONLINE_FANS_COUPON.getCode());
        atomReqCouponSettingDTO.setPromotionCouponTypeList(coolCouponTypes);
        // 店铺下所有可用
        ExecuteDTO<ExecutePageDTO<AtomResCouponSettingDTO>> couponsList = atomCouponSettingAnalysisService.getHomePageCouponsList(atomReqCouponSettingDTO);
        log.info("**CouponPromotionAnalysisServiceImpl**getStoreCoupon-云池优惠券出参：{}", JSON.toJSONString(couponsList));
        if (!couponsList.successFlag()) {
            throw new BaseException(CommonCode.CODE_10000003);
        }
        return couponsList.getData().getRows();
    }

    /**
     * 通用-用户不符合的 和 券不符合的添加状态
     */
    public void checkCoupon(ReqStoreCouponListDTO storeCouponListDTO, List<ResCouponApplyListDTO> couponSettingList) {
        log.info("**CouponPromotionAnalysisServiceImpl**checkCoupon-step2:校验剔除用户不符合的 和 券不符合的：{}", JSON.toJSONString(couponSettingList));
        if (CollectionUtils.isEmpty(couponSettingList)) {
            return;
        }
        couponSettingList.forEach(couponSettingDTO -> {
            // 默认可领取
            couponSettingDTO.setCouponStatus(CouponGetStatusEnum.COUPON_GET_CAN.getCode());
        });
        if (StringUtils.isNotBlank(storeCouponListDTO.getFanNo())) {
            // 查询是否是平台新用户
            ExecuteDTO<Boolean> booleanExecuteDTO = preDeterminedCheckService.checkNewFans(storeCouponListDTO.getFanNo(), null);
            if (!booleanExecuteDTO.successFlag()) {
                throw new BaseException(CommonCode.CODE_10000003);
            }
            //查询是否商家新用户
            ExecuteDTO<Boolean> merchatNewFans = preDeterminedCheckService.checkNewFans(storeCouponListDTO.getFanNo(), storeCouponListDTO.getStoreNo(), storeCouponListDTO.getMerchantNo());
            if (!merchatNewFans.successFlag()) {
                throw new BaseException(CommonCode.CODE_10000003);
            }
            // 查询是否是该店铺新用户
            ExecuteDTO<Boolean> storeNewFans = preDeterminedCheckService.checkNewFans(storeCouponListDTO.getFanNo(), storeCouponListDTO.getStoreNo());
            if (!storeNewFans.successFlag()) {
                throw new BaseException(CommonCode.CODE_10000003);
            }
            couponSettingList.stream().forEach(couponSettingDTO -> {
                // 云池优惠券
                if (PromotionTypeCouponEnum.PLATFORM_ONLINE_AGENT_COUPON.getCode().equals(couponSettingDTO.getPromotionCouponType()) || PromotionTypeCouponEnum.PLATFORM_ONLINE_FANS_COUPON.getCode().equals(couponSettingDTO.getPromotionCouponType())) {
                    // 新用户
                    if (booleanExecuteDTO.getData()) {
                        if (JoinUserScopeEnum.OLD_FANS.getCode().equals(couponSettingDTO.getUserScope())) {
                            couponSettingDTO.setCouponStatus(CouponGetStatusEnum.COUPON_GET_NOT_CAN.getCode());
                        }
                    } else {
                        // 老用户
                        if (JoinUserScopeEnum.NEW_FANS.getCode().equals(couponSettingDTO.getUserScope())) {
                            couponSettingDTO.setCouponStatus(CouponGetStatusEnum.COUPON_GET_NOT_CAN.getCode());
                        }
                    }
                } else if (PromotionTypeCouponEnum.MERCHANT_ONLINE_FANS_COUPON.getCode().equals(couponSettingDTO.getPromotionCouponType())) {
                    //商家优惠券
                    // 新用户
                    if (merchatNewFans.getData()) {
                        if (JoinUserScopeEnum.OLD_FANS.getCode().equals(couponSettingDTO.getUserScope())) {
                            couponSettingDTO.setCouponStatus(CouponGetStatusEnum.COUPON_GET_NOT_CAN.getCode());
                        }
                    } else {
                        // 老用户
                        if (JoinUserScopeEnum.NEW_FANS.getCode().equals(couponSettingDTO.getUserScope())) {
                            couponSettingDTO.setCouponStatus(CouponGetStatusEnum.COUPON_GET_NOT_CAN.getCode());
                        }
                    }
                } else {
                    // 店铺优惠券
                    if (storeNewFans.getData()) {
                        if (JoinUserScopeEnum.OLD_FANS.getCode().equals(couponSettingDTO.getUserScope())) {
                            couponSettingDTO.setCouponStatus(CouponGetStatusEnum.COUPON_GET_NOT_CAN.getCode());
                        }
                    } else {
                        // 老用户
                        if (JoinUserScopeEnum.NEW_FANS.getCode().equals(couponSettingDTO.getUserScope())) {
                            couponSettingDTO.setCouponStatus(CouponGetStatusEnum.COUPON_GET_NOT_CAN.getCode());
                        }
                    }
                }
            });
        }
        couponSettingList.stream().filter(dto -> CouponGetStatusEnum.COUPON_GET_CAN.getCode().equals(dto.getCouponStatus())).forEach(couponSettingDTO -> {
            // 优惠券可用校验
            AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO = BeanCopierUtil.copy(storeCouponListDTO, AtomReqCouponUserRecordDTO.class);
            atomReqCouponUserRecordDTO.setPromotionNo(couponSettingDTO.getPromotionNo());
            atomReqCouponUserRecordDTO.setCouponNo(couponSettingDTO.getCouponNo());
            // 优惠券可获得次数配置
            ExecuteDTO<AtomResCouponRecordDTO> couponUserRecordCounts =
                    atomCouponUserRecordAnalysisService.getCouponUserRecordCounts(atomReqCouponUserRecordDTO);
            if (couponUserRecordCounts == null || !couponUserRecordCounts.successFlag() || couponUserRecordCounts.getData() == null) {
                throw new BaseException(CommonCode.CODE_10000003);
            }
            if (StringUtils.isNotBlank(storeCouponListDTO.getFanNo())) {
                // 用户已领取数量
                couponSettingDTO.setUseAlreadyCount(couponUserRecordCounts.getData().getStoreUserTotalNum());
            } else {
                // 前端要不为null
                couponSettingDTO.setUseAlreadyCount(NumConstant.ZERO);
            }
            AtomResCouponRecordDTO couponUserRecordCountsData = couponUserRecordCounts.getData();
            // 优惠券对用户限制
            if (StringUtils.isNotBlank(storeCouponListDTO.getFanNo())) {
                // 用户单日领券数  || 单个用户总共可获得张数
                if (couponSettingDTO.getUserDailyNum() <= couponUserRecordCountsData.getUserDailyNum() ||
                        couponSettingDTO.getUserTotalNum() <= couponUserRecordCountsData.getUserTotalNum()) {
                    couponSettingDTO.setCouponStatus(CouponGetStatusEnum.COUPON_GET_ALREADY.getCode());
                }
            }
            // 平台券对应店铺限制：店铺用户单日领券数 || 店铺用户总共可获得张数
            if (PromotionTypeCouponEnum.PLATFORM_ONLINE_AGENT_COUPON.getCode().equals(couponSettingDTO.getPromotionCouponType())
                    || PromotionTypeCouponEnum.PLATFORM_ONLINE_FANS_COUPON.getCode().equals(couponSettingDTO.getPromotionCouponType())) {
                // 平台券剩余张数
                if (NumConstant.TWO == couponSettingDTO.getCirculationLimitFlag()) {
                    // 店铺每日剩余领取张数
                    int storeDaily = couponSettingDTO.getStoreDailyNum() - couponUserRecordCountsData.getStoreDailyNum();
                    // 店铺总共剩余张数
                    int storeTotalNum = couponSettingDTO.getStoreTotalNum() - couponUserRecordCountsData.getStoreTotalNum();
                    // 总剩余张数
                    int remainNum = couponSettingDTO.getRemainNum();
                    // 取最小
                    couponSettingDTO.setRemainNum(Math.min((Math.min(storeDaily, storeTotalNum)), remainNum));
                }
                if (couponSettingDTO.getStoreDailyNum() <= couponUserRecordCountsData.getStoreDailyNum() ||
                        couponSettingDTO.getStoreTotalNum() <= couponUserRecordCountsData.getStoreTotalNum()) {
                    couponSettingDTO.setCouponStatus(CouponGetStatusEnum.COUPON_GET_OUT.getCode());
                }
            }
            // 判断总张数是否发完
            if (WhetherEnum.YES.getCode().equals(couponSettingDTO.getCirculationLimitFlag())) {
                if (couponSettingDTO.getRemainNum() <= NumConstant.ZERO) {
                    couponSettingDTO.setCouponStatus(CouponGetStatusEnum.COUPON_GET_OUT.getCode());
                }
            }
        });
        log.info("**CouponPromotionAnalysisServiceImpl**checkCoupon-step2:校验剔除用户不符合的 和 券不符合的：return{}", JSON.toJSONString(couponSettingList));
    }

    /**
     * 如果指定商品可用的券，剔除品牌品类不符的 剔除指定商品券(给优惠券集合使用)
     *
     * @param resGoodsRecordDTO
     * @param couponApplyListDTOS
     */
    private List<ResCouponApplyListDTO> getCouponApplyList(ResGoodsCouponConditionDTO couponConditionDTO, ResGoodsRecordDTO resGoodsRecordDTO, List<ResCouponApplyListDTO> couponApplyListDTOS) {
        //店铺指定品牌
        Map<String, List<String>> couponBrandList = couponConditionDTO.getCouponBrandList();
        //店铺指定类目
        Map<String, List<String>> couponCategoryList = couponConditionDTO.getCouponCategoryList();
        //券指定商品
        Map<String, List<String>> couponGoodsList = couponConditionDTO.getCouponGoodsList();
        List<ResCouponApplyListDTO> returnDto = new ArrayList<>();
        couponApplyListDTOS.forEach(couponApply -> {
            if (GoodsSourceTypeEnum.CLOUD_POOL_DISTRIBUTE.getCode().equals(resGoodsRecordDTO.getGoodsSourceType())) {
                log.info(String.format("云池商品基本信息:%s", JSON.toJSONString(resGoodsRecordDTO)));
                if (GoodsSourceTypeEnum.CLOUD_POOL_DISTRIBUTE.getCode().equals(resGoodsRecordDTO.getGoodsSourceType())) {
                    //云池券
                    if (CouponUseScopeEnum.COUPON_USE_SCOPE_TWO.getCode().equals(couponApply.getCouponUseScope())) {
                        returnDto.add(couponApply);
                    } else if (CouponUseScopeEnum.COUPON_USE_SCOPE_THREE.getCode().equals(couponApply.getCouponUseScope())) {
                        //指定云池商品,查询券设置的云池商品编号
                        List<String> goodsNo = couponGoodsList.get(couponApply.getCouponNo());
                        if (CollectionUtils.isNotEmpty(goodsNo) && goodsNo.contains(resGoodsRecordDTO.getCloudPoolGoodsNo())) {
                            returnDto.add(couponApply);
                        }
                    }
                }
            } else if (GoodsSourceTypeEnum.STORE.getCode().equals(resGoodsRecordDTO.getGoodsSourceType()) && StringUtils.isNotBlank(resGoodsRecordDTO.getMerchantGoodsNo())) {
                // 商家商品通用
                if (CouponUseScopeEnum.COUPON_USE_SCOPE_ONE.getCode().equals(couponApply.getCouponUseScope())
                        || CouponUseScopeEnum.COUPON_USE_SCOPE_EIGHT.getCode().equals(couponApply.getCouponUseScope())) {
                    returnDto.add(couponApply);
                } else if (CouponUseScopeEnum.COUPON_USE_SCOPE_NINE.getCode().equals(couponApply.getCouponUseScope())) {
                    // 指定商家商品
                    List<String> goodsNos = couponGoodsList.get(couponApply.getCouponNo());
                    if (CollectionUtils.isNotEmpty(goodsNos) && goodsNos.contains(resGoodsRecordDTO.getMerchantGoodsNo())) {
                        returnDto.add(couponApply);
                    }
                } else if (CouponUseScopeEnum.COUPON_USE_SCOPE_SIX.getCode().equals(couponApply.getCouponUseScope())) {
                    //店铺指定商品
                    List<String> goodsNo = couponGoodsList.get(couponApply.getCouponNo());
                    if (CollectionUtils.isNotEmpty(goodsNo) && goodsNo.contains(resGoodsRecordDTO.getGoodsNo())) {
                        returnDto.add(couponApply);
                    }
                }
            } else {
                if (CouponUseScopeEnum.COUPON_USE_SCOPE_ONE.getCode().equals(couponApply.getCouponUseScope())) {
                    if (PromotionTypeCouponEnum.SHOP_ONLINE_FANS_COUPON.getCode().equals(couponApply.getPromotionCouponType())
                            || PromotionTypeCouponEnum.PLATFORM_ONLINE_FANS_COUPON.getCode().equals(couponApply.getPromotionCouponType())
                            || PromotionTypeCouponEnum.MERCHANT_ONLINE_FANS_COUPON.getCode().equals(couponApply.getPromotionCouponType())) {
                        returnDto.add(couponApply);
                    }
                } else if (CouponUseScopeEnum.COUPON_USE_SCOPE_FIVE.getCode().equals(couponApply.getCouponUseScope())) {
                    List<String> brands = couponBrandList.get(couponApply.getCouponNo());
                    if (CollectionUtils.isNotEmpty(brands) && brands.contains(resGoodsRecordDTO.getBrandNo())) {
                        returnDto.add(couponApply);
                    }
                } else if (CouponUseScopeEnum.COUPON_USE_SCOPE_FOUR.getCode().equals(couponApply.getCouponUseScope())) {
                    //店铺指定类目
                    List<String> category = couponCategoryList.get(couponApply.getCouponNo());
                    if (CollectionUtils.isNotEmpty(category) && category.contains(resGoodsRecordDTO.getCategoryNo())) {
                        returnDto.add(couponApply);
                    }
                } else if (CouponUseScopeEnum.COUPON_USE_SCOPE_SIX.getCode().equals(couponApply.getCouponUseScope())) {
                    //店铺指定商品
                    List<String> goodsNo = couponGoodsList.get(couponApply.getCouponNo());
                    if (CollectionUtils.isNotEmpty(goodsNo) && goodsNo.contains(resGoodsRecordDTO.getGoodsNo())) {
                        returnDto.add(couponApply);
                    }
                }
            }
        });
        return returnDto;
    }


    /**
     * 如果指定商品可用的券，剔除品牌品类不符的 剔除指定商品券
     */
    private List<ResCouponApplyListDTO> checkCouponByParam(ResGoodsRecordDTO resGoodsRecordDTO, List<ResCouponApplyListDTO> couponApplyListDTOS) {
        log.info("**CouponPromotionAnalysisServiceImpl**checkCoupon-step3:剔除品牌品类不符的 剔除指定商品券：{}", JSON.toJSONString(couponApplyListDTOS));
        if (CollectionUtils.isEmpty(couponApplyListDTOS)) {
            return couponApplyListDTOS;
        }
        //获取优惠券过滤条件
        ResGoodsCouponConditionDTO couponConditionDTO = this.couponInfoUtil.getResGoodsCouponConditionDTO(couponApplyListDTOS);
        return this.getCouponApplyList(couponConditionDTO, resGoodsRecordDTO, couponApplyListDTOS);
    }

    /**
     * 查询券指定店铺类目编码
     */
    @Override
    public ExecuteDTO<List<String>> getCouponStoreCategoryListByCouponNo(AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO) {
        CouponCategoryRelationVO couponCategoryRelationVO = new CouponCategoryRelationVO();
        couponCategoryRelationVO.setCouponNo(atomReqCouponUserRecordDTO.getCouponNo());
        List<CouponStoreCategoryRelationDomain> couponStoreCategoryRelationDomains = couponStoreCategoryRelationDao.selectCouponStoreCategoryRelation(couponCategoryRelationVO);
        if (ListUtil.isNotEmpty(couponStoreCategoryRelationDomains)) {
            List<String> categoryNos = couponStoreCategoryRelationDomains.stream().map(CouponStoreCategoryRelationDomain::getCategoryNo).collect(Collectors.toList());
            log.info("**CouponPromotionAnalysisServiceImpl**getCouponStoreCategoryListByCouponNo-指定店铺类目:,CouponNo:{},list:{}", atomReqCouponUserRecordDTO.getCouponNo(), JSON.toJSONString(categoryNos));
            return ExecuteDTO.ok(categoryNos);
        } else {
            return ExecuteDTO.ok(Collections.emptyList());
        }
    }

    private List<ResCouponApplyListDTO> getResCouponApplyList(List<ResCouponApplyListDTO> couponApplyListDTOS) {
        List<ResCouponApplyListDTO> resCouponApply = null;
        // 前端要截取3条
        if (CollectionUtils.isNotEmpty(couponApplyListDTOS)) {
            if (couponApplyListDTOS.size() > NumConstant.THREE) {
                resCouponApply = couponApplyListDTOS.subList(NumConstant.ZERO, NumConstant.THREE);
            } else {
                resCouponApply = couponApplyListDTOS;
            }
        }
        return resCouponApply;
    }


    private void setOneCouponStatus(ResCouponSettingDTO dto) {
        if (dto != null) {
            // 草稿状态
            if (StringUtils.isNotBlank(dto.getStatus()) && PromotionStatusEnum.ONLY_SAVED.getCode().equals(dto.getStatus())) {
                dto.setStatus(PromotionStatusEnum.ONLY_SAVED.getCode());
                // 活动有效期（1001：长期有效 1002：指定日期）
            } else if (EffectivePeriodTypeEnum.EFFECTIVE_PERIOD_TYPE_ONE.getCode().equals(dto.getPeriodValidity())) {
                dto.setStatus(PromotionStatusEnum.IN_PROGRESS.getCode());
                // 其他状态
            } else if (EffectivePeriodTypeEnum.EFFECTIVE_PERIOD_TYPE_TWO.getCode().equals(dto.getPeriodValidity())
                    && dto.getEffectiveTime() != null && dto.getInvalidTime() != null) {
                //未开始
                if (dto.getEffectiveTime().isAfter(LocalDateTime.now())) {
                    dto.setStatus(PromotionStatusEnum.NOT_STARTED.getCode());
                    //进行中
                } else if ((dto.getEffectiveTime().isBefore(LocalDateTime.now()) || dto.getEffectiveTime().equals(LocalDateTime.now()))
                        && (dto.getInvalidTime().equals(LocalDateTime.now()) || dto.getInvalidTime().isAfter(LocalDateTime.now()))) {
                    dto.setStatus(PromotionStatusEnum.IN_PROGRESS.getCode());
                    //已结束
                } else if (dto.getInvalidTime().isBefore(LocalDateTime.now())) {
                    dto.setStatus(PromotionStatusEnum.EXPIRED.getCode());
                }
            }
        }
    }
}
