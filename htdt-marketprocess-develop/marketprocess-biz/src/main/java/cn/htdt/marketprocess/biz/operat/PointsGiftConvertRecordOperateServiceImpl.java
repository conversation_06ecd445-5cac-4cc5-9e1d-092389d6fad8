package cn.htdt.marketprocess.biz.operat;

import cn.htdt.common.base.exception.BaseException;
import cn.htdt.common.dto.enums.MarketErrorCode;
import cn.htdt.common.dto.enums.UserErrorCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.market.PointChangeEventEnum;
import cn.htdt.common.enums.user.FanTypeEnum;
import cn.htdt.common.generator.IdGenerator;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqPointsGiftConvertRecordDTO;
import cn.htdt.marketcenter.dto.request.AtomReqPointsGiftDTO;
import cn.htdt.marketcenter.dto.request.AtomReqUserPointsDTO;
import cn.htdt.marketcenter.dto.response.AtomResPointsGiftDTO;
import cn.htdt.marketcenter.dto.response.AtomResUserPointsDTO;
import cn.htdt.marketprocess.api.operat.PointsGiftConvertRecordOperateService;
import cn.htdt.marketprocess.api.operat.UserPointsRecordOperateService;
import cn.htdt.marketprocess.biz.conversion.PointsGiftAssert;
import cn.htdt.marketprocess.biz.utils.PointsUtil;
import cn.htdt.marketprocess.dto.request.ReqPointsGiftConvertRecordDTO;
import cn.htdt.marketprocess.dto.request.ReqUserPointsRecordDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomPointsGiftAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomUserPointsAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomPointsGiftConvertRecordOperateService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomPointsGiftOperateService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomUserPointsOperateService;
import cn.htdt.usercenter.dto.request.ReqFancDTO;
import cn.htdt.usercenter.dto.request.ReqFansFollowDTO;
import cn.htdt.usercenter.dto.response.ResFancDTO;
import cn.htdt.usercenter.dto.response.ResFansFollowDTO;
import cn.htdt.userprocess.api.UcFansProcessService;
import cn.htdt.userprocess.dto.request.GetFancInfoRequest;
import cn.htdt.userprocess.dto.response.StoreInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

@DubboService
@Slf4j
public class PointsGiftConvertRecordOperateServiceImpl implements PointsGiftConvertRecordOperateService {

    @Resource
    PointsGiftAssert pointsGiftAssert;

    @Resource
    AtomPointsGiftAnalysisService atomPointsGiftAnalysisService;

    @Resource
    AtomUserPointsAnalysisService atomUserPointsAnalysisService;

    @Resource
    AtomPointsGiftOperateService atomPointsGiftOperateService;

    @Resource
    AtomUserPointsOperateService atomUserPointsOperateService;

    @Resource
    AtomPointsGiftConvertRecordOperateService atomPointsGiftConvertRecordOperateService;

    @Resource
    UserPointsRecordOperateService userPointsRecordOperateService;

    @Resource
    PointsUtil pointsUtil;

    @DubboReference
    private UcFansProcessService ucFansProcessService;


    /**
     * @see PointsGiftConvertRecordOperateService#convertGift(ReqPointsGiftConvertRecordDTO)
     */
    @Override
    public ExecuteDTO convertGift(ReqPointsGiftConvertRecordDTO recordDTO) {
        log.info("PointsGiftConvertRecordOperateServiceImpl.convertGift----param----", recordDTO);
        pointsGiftAssert.convertGift(recordDTO);
        //20230928蛋品-wh-礼品-礼品兑换查询用户的积分是，门店的还是商家的
        StoreInfoResponse storeInfoResponse = pointsUtil.queryStore(recordDTO.getStoreNo());
        if(storeInfoResponse.getMemberSharing() == NumConstant.ONE){
            recordDTO.setRuleType(String.valueOf(NumConstant.ONE));
        }else {
            recordDTO.setRuleType(String.valueOf(NumConstant.TWO));
        }

        AtomResPointsGiftDTO giftInfo = getGiftInfo(recordDTO);
        AtomResUserPointsDTO userPointsInfo = getUserPointsInfo(recordDTO, giftInfo);
        //减库存
        reduceStock(recordDTO, giftInfo, userPointsInfo);
        //减积分
        updateUserPoints(recordDTO, giftInfo);
        //兑换后如果库存为0，更新状态为下架
        updateStatus(recordDTO);
        if(storeInfoResponse.getMemberSharing() == NumConstant.TWO){
            //如果是共享店铺查询这个粉丝和这个店铺是否是有关注关系，没有新建关注关系
            bindingRelationship(recordDTO,storeInfoResponse);
        }
        return ExecuteDTO.success();
    }

    /**
     * 查询要兑换的礼品详情
     *
     * @param recordDTO 礼品id
     * @return 礼品详情
     */
    private AtomResPointsGiftDTO getGiftInfo(ReqPointsGiftConvertRecordDTO recordDTO) {
        //查商品最新的数据以获取礼品最新的数据
        AtomReqPointsGiftDTO giftDTO = BeanCopierUtil.copy(recordDTO, AtomReqPointsGiftDTO.class);
        ExecuteDTO<AtomResPointsGiftDTO> giftInfoExecuteDTO = null;
        //20230928蛋品-wh-礼品-根据条件查询单条商家共享商城礼品的信息
        if(String.valueOf(NumConstant.ONE).equals(recordDTO.getRuleType())){
            giftDTO.setRuleType(String.valueOf(NumConstant.ONE));
            giftInfoExecuteDTO = atomPointsGiftAnalysisService.getSimpleGift(giftDTO);
        }else{
            giftInfoExecuteDTO = atomPointsGiftAnalysisService.getSharingSimpleGift(giftDTO);
        }

        if (!giftInfoExecuteDTO.successFlag()) {
            throw new BaseException(giftInfoExecuteDTO.getStatus(), giftInfoExecuteDTO.getMsg());
        }
        if (giftInfoExecuteDTO.getData() == null) {
            throw new BaseException(MarketErrorCode.CODE_17000807);
        }
        AtomResPointsGiftDTO giftInfo = giftInfoExecuteDTO.getData();
        //如果剩余库存小于兑换的数量 提示库存不足
        if (giftInfo.getGiftStockNum() < recordDTO.getConvertNum()) {
            throw new BaseException(MarketErrorCode.CODE_17000805);
        }
        return giftInfo;
    }

    /**
     * 查询用户积分信息 如果积分信息不够兑换 直接抛出异常返回
     *
     * @param recordDTO 手机号
     * @param giftInfo  需要兑换的礼品信息
     * @return 用户的积分信息
     */
    private AtomResUserPointsDTO getUserPointsInfo(ReqPointsGiftConvertRecordDTO recordDTO, AtomResPointsGiftDTO giftInfo) {
        AtomReqUserPointsDTO atomReqUserPointsDTO = BeanCopierUtil.copy(recordDTO, AtomReqUserPointsDTO.class);
        //20230928蛋品-wh-礼品-礼品兑换查询用户的积分是，门店的还是商家的
        StoreInfoResponse storeInfoResponse = pointsUtil.queryStore(recordDTO.getStoreNo());
        if(String.valueOf(NumConstant.ONE).equals(recordDTO.getRuleType())){
            atomReqUserPointsDTO.setRuleType(String.valueOf(NumConstant.ONE));
        }else {
            atomReqUserPointsDTO.setRuleType(String.valueOf(NumConstant.TWO));
        }
        // 20230928蛋品-商家积分, 商家编号为空, 需要设置商家编号
        if (StringUtils.isBlank(recordDTO.getMerchantNo())) {
            recordDTO.setMerchantNo(storeInfoResponse.getMerchantNo());
            atomReqUserPointsDTO.setMerchantNo(storeInfoResponse.getMerchantNo());
        }
        ExecuteDTO<AtomResUserPointsDTO> userPointsInfoExecuteDTO = atomUserPointsAnalysisService.getSimpleUserPoints(atomReqUserPointsDTO);
        if (!userPointsInfoExecuteDTO.successFlag()) {
            throw new BaseException(userPointsInfoExecuteDTO.getStatus(), userPointsInfoExecuteDTO.getMsg());
        }
        if (userPointsInfoExecuteDTO.getData() == null) {
            throw new BaseException(UserErrorCode.CODE_13000001);
        }
        AtomResUserPointsDTO userPointsInfo = userPointsInfoExecuteDTO.getData();
        int remainPoints = userPointsInfo.getAccountRemainPoints();
        int points = giftInfo.getPoints();
        int convertNum = recordDTO.getConvertNum();
        int reducePoints = points * convertNum;
        if (remainPoints < reducePoints) {
            throw new BaseException(MarketErrorCode.CODE_17000806);
        }
        return userPointsInfo;
    }

    /**
     * 礼品库存扣减
     *
     * @param recordDTO      礼品id
     * @param giftInfo       礼品所需积分
     * @param userPointsInfo 粉丝信息
     */
    private void reduceStock(ReqPointsGiftConvertRecordDTO recordDTO, AtomResPointsGiftDTO giftInfo, AtomResUserPointsDTO userPointsInfo) {
        AtomReqPointsGiftConvertRecordDTO convertRecordDTO = BeanCopierUtil.copy(recordDTO, AtomReqPointsGiftConvertRecordDTO.class);
        ExecuteDTO reduceStockDTO = atomPointsGiftOperateService.reduceStockNum(convertRecordDTO);
        if (!reduceStockDTO.successFlag()) {
            throw new BaseException(reduceStockDTO.getStatus(), reduceStockDTO.getMsg());
        }
        //减库存成功后新增兑换记录
        addConvertRecord(recordDTO, giftInfo, userPointsInfo);
    }

    /**
     * 新增礼品兑换流水 异常不影响主流程
     *
     * @param recordDTO      基本信息
     * @param giftInfo       要兑换的礼品信息
     * @param userPointsInfo 粉丝信息
     */
    private void addConvertRecord(ReqPointsGiftConvertRecordDTO recordDTO, AtomResPointsGiftDTO giftInfo, AtomResUserPointsDTO userPointsInfo) {
        recordDTO.setFansNo(userPointsInfo.getFansNo());
        recordDTO.setName(userPointsInfo.getName());
        recordDTO.setReducePoints(giftInfo.getPoints() * recordDTO.getConvertNum());
        recordDTO.setGiftName(giftInfo.getGiftName());
        String convertNo = IdGenerator.getDid();
        recordDTO.setConvertNo(convertNo);
        AtomReqPointsGiftConvertRecordDTO convertRecordDTO = BeanCopierUtil.copy(recordDTO, AtomReqPointsGiftConvertRecordDTO.class);
        atomPointsGiftConvertRecordOperateService.addConvertRecord(convertRecordDTO);
    }

    /**
     * 更新粉丝账户积分
     *
     * @param recordDTO 请求参数
     * @param giftInfo  礼品信息
     */
    private void updateUserPoints(ReqPointsGiftConvertRecordDTO recordDTO, AtomResPointsGiftDTO giftInfo) {
        AtomReqUserPointsDTO atomReqUserPointsDTO = BeanCopierUtil.copy(recordDTO, AtomReqUserPointsDTO.class);
        atomReqUserPointsDTO.setChangeNum(0 - recordDTO.getConvertNum() * giftInfo.getPoints());
        //20230928蛋品-wh-礼品-共享店铺下扣减积分是针对，门店下粉丝编号全部扣减（粉丝积分是公用的）
        //更新积分成功后 添加积分记录表数据
        ReqUserPointsRecordDTO reqUserPointsRecordDTO = BeanCopierUtil.copy(atomReqUserPointsDTO, ReqUserPointsRecordDTO.class);
        //20230928蛋品-wh-查询店铺是否是共享店铺
        if(String.valueOf(NumConstant.ONE).equals(recordDTO.getRuleType())){
            //门店的
            atomReqUserPointsDTO.setRuleType(String.valueOf(NumConstant.ONE));
            reqUserPointsRecordDTO.setType(String.valueOf(NumConstant.ONE));
        }else {
            //商家的
            atomReqUserPointsDTO.setRuleType(String.valueOf(NumConstant.TWO));
            reqUserPointsRecordDTO.setType(String.valueOf(NumConstant.TWO));
        }
        ExecuteDTO updateUserPointsDTO = atomUserPointsOperateService.updateUserPoints(atomReqUserPointsDTO);

        if (!updateUserPointsDTO.successFlag()) {
            throw new BaseException(updateUserPointsDTO.getStatus(), updateUserPointsDTO.getMsg());
        }
        reqUserPointsRecordDTO.setAdjustReason("积分兑换礼品");
        reqUserPointsRecordDTO.setPointChangeEvent(PointChangeEventEnum.POINTS_EXCHANGE_GIFT.getCode());
        userPointsRecordOperateService.addUserPointsRecord(reqUserPointsRecordDTO);
    }

    /**
     * 如果兑换礼品后剩余库存为0 更新礼品状态为下架
     *
     * @param recordDTO 礼品编号
     */
    private void updateStatus(ReqPointsGiftConvertRecordDTO recordDTO) {
        AtomReqPointsGiftDTO atomReqPointsGiftDTO = BeanCopierUtil.copy(recordDTO, AtomReqPointsGiftDTO.class);
        ExecuteDTO<Integer> executeDTO = atomPointsGiftOperateService.undercarriageNullstock(atomReqPointsGiftDTO);
        if (!executeDTO.successFlag()) {
            throw new BaseException(executeDTO.getStatus(), executeDTO.getMsg());
        }
    }


    private void bindingRelationship(ReqPointsGiftConvertRecordDTO recordDTO,StoreInfoResponse storeInfoResponse){
        ReqFancDTO reqFancDTO = new ReqFancDTO();
        reqFancDTO.setStoreNo(recordDTO.getStoreNo());
        reqFancDTO.setFanNo(recordDTO.getFansNo());
        ExecuteDTO<ResFansFollowDTO> followDTOExecuteDTO = ucFansProcessService.selectFansFollow(reqFancDTO);
        log.info("PointsGiftConvertRecordOperateServiceImpl.bindingRelationship----param----", recordDTO);
        if (!followDTOExecuteDTO.successFlag()) {
            throw new BaseException(followDTOExecuteDTO.getStatus(), followDTOExecuteDTO.getMsg());
        }
        if(null == followDTOExecuteDTO.getData()){
            //如果是空就添加绑定关系
            // 完善粉丝手机号
            GetFancInfoRequest getFancInfoRequest = new GetFancInfoRequest();
            getFancInfoRequest.setFanNo(recordDTO.getFansNo());
            ExecuteDTO fansByFanNoExecute = ucFansProcessService.getFansByFanNo(getFancInfoRequest);
            if (null != fansByFanNoExecute && fansByFanNoExecute.successFlag() && null != fansByFanNoExecute.getData()) {
                ResFancDTO resFancDTO = (ResFancDTO) fansByFanNoExecute.getData();
                ReqFansFollowDTO reqFanFollowDTO = new ReqFansFollowDTO();
                reqFanFollowDTO.setPhone(resFancDTO.getPhone());
                reqFanFollowDTO.setDsPhone(resFancDTO.getDsPhone());
                reqFanFollowDTO.setName(resFancDTO.getName());
                reqFanFollowDTO.setFanNo(recordDTO.getFansNo());
                reqFanFollowDTO.setStoreNumber(storeInfoResponse.getStoreNo());
                reqFanFollowDTO.setStoreName(storeInfoResponse.getStoreName());
                reqFanFollowDTO.setFollowSource(FanTypeEnum.JFLY.getCode());
                reqFanFollowDTO.setProvinceCode(resFancDTO.getProvinceCode());
                reqFanFollowDTO.setProvinceName(resFancDTO.getProvinceName());
                reqFanFollowDTO.setCityCode(resFancDTO.getCityCode());
                reqFanFollowDTO.setCityName(reqFancDTO.getCityName());
                reqFanFollowDTO.setRegionCode(reqFancDTO.getRegionCode());
                reqFanFollowDTO.setRegionName(reqFancDTO.getRegionName());
                reqFanFollowDTO.setStreetCode(reqFancDTO.getStreetCode());
                reqFanFollowDTO.setStreetName(reqFancDTO.getStreetName());
                ucFansProcessService.saveFansFollow(reqFanFollowDTO);
            }
        }
    }
}
