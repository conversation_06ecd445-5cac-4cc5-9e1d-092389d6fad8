package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.base.exception.BaseException;
import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.goods.SeriesTypeEnum;
import cn.htdt.common.enums.market.*;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.BigDecimalUtil;
import cn.htdt.goodscenter.dto.request.AtomReqGoodsDTO;
import cn.htdt.goodscenter.dto.response.AtomResGoodsDTO;
import cn.htdt.goodsprocess.api.operat.LegacyGoodsCenterService;
import cn.htdt.goodsprocess.dto.response.goods.ResGoodsDTO;
import cn.htdt.marketcenter.dto.request.AtomReqCouponGoodsDTO;
import cn.htdt.marketcenter.dto.request.AtomReqExpandHelpRecordDTO;
import cn.htdt.marketcenter.dto.request.AtomReqExpandRecordDTO;
import cn.htdt.marketcenter.dto.request.AtomReqExpandRuleDTO;
import cn.htdt.marketcenter.dto.response.*;
import cn.htdt.marketprocess.api.analysis.ExpandRecordAnalysisService;
import cn.htdt.marketprocess.api.analysis.ExpandRuleAnalysisService;
import cn.htdt.marketprocess.dto.request.ReqCouponGoodsDTO;
import cn.htdt.marketprocess.dto.request.ReqExpandRecordDTO;
import cn.htdt.marketprocess.dto.request.ReqExpandRuleDTO;
import cn.htdt.marketprocess.dto.request.ReqExpandRuleDetailDTO;
import cn.htdt.marketprocess.dto.response.*;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomAgentMarketRewardsetAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomExpandHelpRecordAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomExpandRecordAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomExpandRuleAnalysisService;
import cn.htdt.userprocess.api.UserPublicService;
import cn.htdt.userprocess.dto.publicDto.response.GetStoreListResponse;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 膨胀红包规则 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Slf4j
@DubboService
public class ExpandRuleAnalysisServiceImpl extends CommonAnalysisServiceImpl implements ExpandRuleAnalysisService {

    /**
     * 膨胀红包活动基本信息
     */
    @Resource
    private AtomExpandRuleAnalysisService atomExpandRuleAnalysisService;
    /**
     * 酬劳
     */
    @Resource
    private AtomAgentMarketRewardsetAnalysisService atomAgentMarketRewardsetAnalysisService;

    @DubboReference
    private LegacyGoodsCenterService legacyGoodsCenterService;

    @Resource
    private AtomExpandRecordAnalysisService atomExpandRecordAnalysisService;

    @Resource
    private AtomExpandHelpRecordAnalysisService atomExpandHelpRecordAnalysisService;
    /**
     * 获取所有店铺用
     */
    @DubboReference
    private UserPublicService userPublicService;

    @Resource
    private ExpandRecordAnalysisService expandRecordAnalysisService;

    @Override
    public ExecuteDTO<ExecutePageDTO<ResExpandRuleDTO>> getExpandPage(ReqExpandRuleDTO reqExpandRuleDTO) {
        log.info("-ExpandRuleAnalysisServiceImpl-getExpandPage-param={}", JSON.toJSONString(reqExpandRuleDTO));
        ExecutePageDTO<ResExpandRuleDTO> executePageDTO = new ExecutePageDTO<>();
        // 查询活动信息
        AtomReqExpandRuleDTO atomReqExpandRuleDTO = BeanCopierUtil.copy(reqExpandRuleDTO, AtomReqExpandRuleDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResExpandRuleDTO>> executeDTO = this.atomExpandRuleAnalysisService.getExpandPage(atomReqExpandRuleDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        List<ResExpandRuleDTO> resExpandRuleDTOs = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResExpandRuleDTO.class);
        // 设置活动状态
        this.setExpandStatus(resExpandRuleDTOs);
        // 设置店铺名称
        this.getStoreNames(resExpandRuleDTOs);
        executePageDTO.setRows(resExpandRuleDTOs);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        log.info("-ExpandRuleAnalysisServiceImpl-getExpandPage-end");
        return ExecuteDTO.success(executePageDTO);
    }

    @Override
    public ExecuteDTO<ResExpandRuleDTO> getExpandRule(ReqExpandRuleDTO reqExpandRuleDTO) {
        log.info("-ExpandRuleAnalysisServiceImpl-getExpandRule-param={}", JSON.toJSONString(reqExpandRuleDTO));
        // 查询活动信息
        AtomReqExpandRuleDTO atomReqExpandRuleDTO = BeanCopierUtil.copy(reqExpandRuleDTO, AtomReqExpandRuleDTO.class);
        ExecuteDTO<AtomResExpandRuleDTO> executeDTO = this.atomExpandRuleAnalysisService.getExpandRule(atomReqExpandRuleDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResExpandRuleDTO resExpandRuleDTO = BeanCopierUtil.copy(executeDTO.getData(), ResExpandRuleDTO.class);
        // 设置活动状态
        this.setOneExpandStatus(resExpandRuleDTO);
        // 添加领取量和使用量
        ExecuteDTO<AtomResExpandRecordDTO> recordDTOExecuteDTO = this.setUseCount(resExpandRuleDTO);
        if (!recordDTOExecuteDTO.successFlag()) {
            return ExecuteDTO.error(recordDTOExecuteDTO.getStatus(), recordDTOExecuteDTO.getMsg());
        }
        resExpandRuleDTO.setUseCount(recordDTOExecuteDTO.getData() != null ? recordDTOExecuteDTO.getData().getUseCount() : NumConstant.ZERO);
        resExpandRuleDTO.setExchangeCount(recordDTOExecuteDTO.getData() != null ? recordDTOExecuteDTO.getData().getExchangeCount() : NumConstant.ZERO);
        log.info("-ExpandRuleAnalysisServiceImpl-getExpandRule-end");
        return ExecuteDTO.success(resExpandRuleDTO);
    }

    @Override
    public ExecuteDTO<ResCouponGoodsDTO> getExpandGoodsRelation(ReqCouponGoodsDTO reqExpandRuleDTO) {
        log.info("-ExpandRuleAnalysisServiceImpl-getExpandGoodsRelation-param={}", JSON.toJSONString(reqExpandRuleDTO));
        // 查询活动信息
        AtomReqCouponGoodsDTO atomReqCouponGoodsDTO = BeanCopierUtil.copy(reqExpandRuleDTO, AtomReqCouponGoodsDTO.class);
        ExecuteDTO<AtomResCouponGoodsDTO> executeDTO = this.atomExpandRuleAnalysisService.getExpandGoodsRelation(atomReqCouponGoodsDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResCouponGoodsDTO resExpandRuleDTO = BeanCopierUtil.copy(executeDTO.getData(), ResCouponGoodsDTO.class);
        if (resExpandRuleDTO != null && CollectionUtils.isNotEmpty(resExpandRuleDTO.getResCouponGoodsRelationDTOS())) {
            List<String> goodsNoList = resExpandRuleDTO.getResCouponGoodsRelationDTOS().stream().map(ResCouponGoodsRelationDTO::getGoodsNo).collect(Collectors.toList());
            AtomReqGoodsDTO goodsDTO = new AtomReqGoodsDTO();
            goodsDTO.setGoodsNos(goodsNoList);
            ExecuteDTO<List<AtomResGoodsDTO>> goodsExecuteDTO = legacyGoodsCenterService.getGoodsByNos(goodsDTO);
            if (goodsExecuteDTO.successFlag()) {
                List<AtomResGoodsDTO> goodsList = goodsExecuteDTO.getData();
                if (CollectionUtils.isNotEmpty(goodsList)) {
                    List<ResGoodsDTO> list = BeanCopierUtil.copyList(goodsList, ResGoodsDTO.class);
                    //获取主品集合
                    List<ResGoodsDTO> parentGoodsList = list.stream().filter(goods -> SeriesTypeEnum.PARENT_GOODS.getCode().equals(goods.getSeriesType())).collect(Collectors.toList());
                    //获取主品商品库存集合（取子品库存的区间）
                    this.getSeriesGoodsStockList(parentGoodsList, false);
                    // 商品map
                    Map<String, ResGoodsDTO> goodsDTOMap = list.stream().collect(Collectors.toMap(ResGoodsDTO::getGoodsNo, ResGoodsDTO -> ResGoodsDTO));
                    // 指定商品
                    if (CouponUseScopeEnum.COUPON_USE_SCOPE_SIX.getCode().equals(resExpandRuleDTO.getCouponUseScope())) {
                        ExecuteDTO<List<AtomResAgentMarketRewardsetDTO>> rewardExecuteDTO = atomAgentMarketRewardsetAnalysisService.batchGetRewardSetByTaskNos(goodsNoList);
                        if (rewardExecuteDTO.successFlag()) {
                            List<AtomResAgentMarketRewardsetDTO> rewardsetDTOS = rewardExecuteDTO.getData();
                            if (CollectionUtils.isNotEmpty(rewardsetDTOS)) {
                                // 酬劳
                                Map<String, AtomResAgentMarketRewardsetDTO> rewardsetDTOMap = rewardsetDTOS.stream().collect(Collectors.toMap(AtomResAgentMarketRewardsetDTO::getTaskOrGoodsNo, AtomResAgentMarketRewardsetDTO -> AtomResAgentMarketRewardsetDTO));
                                for (ResCouponGoodsRelationDTO dto : resExpandRuleDTO.getResCouponGoodsRelationDTOS()) {
                                    AtomResAgentMarketRewardsetDTO rewardsetDTO = rewardsetDTOMap.get(dto.getGoodsNo());
                                    if (rewardsetDTO != null) {
                                        dto.setRewardType(rewardsetDTO.getRewardType());
                                        dto.setYjOrHjb(rewardsetDTO.getYjOrHjb());
                                    }
                                }
                            }
                        }
                        for (ResCouponGoodsRelationDTO dto : resExpandRuleDTO.getResCouponGoodsRelationDTOS()) {
                            ResGoodsDTO resGoodsDTO = goodsDTOMap.get(dto.getGoodsNo());
                            if (resGoodsDTO != null) {
                                dto.setGoodsForm(resGoodsDTO.getGoodsForm());
                                dto.setRetailPrice(resGoodsDTO.getRetailPrice());
                                dto.setMinRetailPrice(resGoodsDTO.getMinRetailPrice());
                                dto.setMaxRetailPrice(resGoodsDTO.getMaxRetailPrice());
                                // 20230928蛋品 lixiang  商品管理 多单位商品
                                dto.setMultiUnitType(resGoodsDTO.getMultiUnitType());
                                dto.setMultiUnitGoodsNo(resGoodsDTO.getMultiUnitGoodsNo());
                            }
                        }
                    }
                }
            }
        }
        log.info("-ExpandRuleAnalysisServiceImpl-getExpandGoodsRelation-end");
        return ExecuteDTO.success(resExpandRuleDTO);
    }

    @Override
    public ExecuteDTO<ResExpandRuleDetailDTO> getExpandPromotionDetail(ReqExpandRuleDetailDTO reqDTO) {
        // 免登陆判断
        // 1.如果没有带expandNo说明是新发起页面或者自己发起过的页面   首先通过fansNo和promotionNo查发起记录表 有记录则查发起信息
        // 2.带有expandNo 可能是自己进入也可能是粉丝助力页
        log.info("-ExpandRuleAnalysisServiceImpl-getExpandPromotionDetail-param={}", JSON.toJSONString(reqDTO));
        if (StringUtils.isEmpty(reqDTO.getPromotionNo())) {
            throw new BaseException(CommonCode.CODE_10000002, "PromotionNo");
        }
        // 查询活动信息
        AtomReqExpandRuleDTO atomReqExpandRuleDTO = BeanCopierUtil.copy(reqDTO, AtomReqExpandRuleDTO.class);
        ExecuteDTO<AtomResExpandRuleDTO> executeDTO = this.atomExpandRuleAnalysisService.getExpandRule(atomReqExpandRuleDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResExpandRuleDTO resExpandRuleDTO = BeanCopierUtil.copy(executeDTO.getData(), ResExpandRuleDTO.class);
        // 设置活动状态
        this.setOneExpandStatus(resExpandRuleDTO);
        // 具体身份按钮逻辑
        return this.setButton(reqDTO, resExpandRuleDTO);

    }

    /**
     * 根据活动不同状态封装出参
     * 1.如果没有带expandNo说明是新发起页面或者自己发起过的页面   首先通过fansNo和promotionNo查发起记录表 有记录则查发起信息
     * 2.带有expandNo 可能是自己进入也可能是粉丝助力页
     *
     * @param reqDTO           接口入参
     * @param resExpandRuleDTO 出参信息
     */
    private ExecuteDTO<ResExpandRuleDetailDTO> setButton(ReqExpandRuleDetailDTO reqDTO, ResExpandRuleDTO resExpandRuleDTO) {
        ResExpandRuleDetailDTO detailDTO = new ResExpandRuleDetailDTO();
        // 20230818蛋品-盛守武-膨胀红包改动（start）
        detailDTO.setHelperCouponRuleMsg(resExpandRuleDTO.getHelperCouponRuleMsg());
        // 20230818蛋品-盛守武-膨胀红包改动（end）
        ResExpandRuleInfoDTO ruleInfoDTO = BeanCopierUtil.copy(resExpandRuleDTO, ResExpandRuleInfoDTO.class);
        // 已过期直接返回
        if (PromotionStatusEnum.EXPIRED.getCode().equals(resExpandRuleDTO.getStatus()) || WhetherEnum.NO.getCode().equals(resExpandRuleDTO.getStoreUpDownFlag())) {
            detailDTO.setStatus(ExpandStatusEnum.EXPAND_STATUS_THREE.getCode());
            detailDTO.setShowMsg(ExpandStatusEnum.EXPAND_STATUS_THREE.getShowMsg());
            return ExecuteDTO.success(detailDTO);
        } else if (PromotionStatusEnum.NOT_STARTED.getCode().equals(resExpandRuleDTO.getStatus())) {
            // 活动未开始
            detailDTO.setExpandRuleInfo(ruleInfoDTO);
            detailDTO.setStatus(ExpandStatusEnum.EXPAND_STATUS_ONE.getCode());
            detailDTO.setShowMsg(ExpandStatusEnum.EXPAND_STATUS_ONE.getShowMsg());
            return ExecuteDTO.success(detailDTO);
        } else if (StringUtils.isBlank(reqDTO.getFanNo()) && StringUtils.isBlank(reqDTO.getExpandNo()) && PromotionStatusEnum.IN_PROGRESS.getCode().equals(resExpandRuleDTO.getStatus())) {
            // 待发起状态
            detailDTO.setExpandRuleInfo(ruleInfoDTO);
            detailDTO.setStatus(ExpandStatusEnum.EXPAND_STATUS_TWO.getCode());
            detailDTO.setShowMsg(ExpandStatusEnum.EXPAND_STATUS_TWO.getShowMsg());
            return ExecuteDTO.success(detailDTO);
        }
        // 发起记录
        AtomResExpandRecordDTO resExpandRecordDTO = null;
        // 说明活动已经发起 通过连接分享进来会携带expandNo
        if (PromotionStatusEnum.IN_PROGRESS.getCode().equals(resExpandRuleDTO.getStatus()) && StringUtils.isNotBlank(reqDTO.getExpandNo())) {
            AtomReqExpandRecordDTO recordDTO = new AtomReqExpandRecordDTO();
            recordDTO.setExpandNo(reqDTO.getExpandNo());
            log.info("-ExpandRuleAnalysisServiceImpl-getOneExpandRecord-param={}", JSON.toJSONString(recordDTO));
            ExecuteDTO<AtomResExpandRecordDTO> executeDTO = atomExpandRecordAnalysisService.getOneExpandRecord(recordDTO);
            log.info("-ExpandRuleAnalysisServiceImpl-getOneExpandRecord-return={}", JSON.toJSONString(executeDTO));
            if (!executeDTO.successFlag()) {
                return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
            }
            resExpandRecordDTO = executeDTO.getData();
            if (resExpandRecordDTO != null) {
                ruleInfoDTO.setHeadImg(resExpandRecordDTO.getHeadImg());
                ruleInfoDTO.setNickName(resExpandRecordDTO.getNickName());
            }
        }

        // 是否登录
        if (StringUtils.isNotEmpty(reqDTO.getFanNo())) {
            // 带有expandNo 可能是自己进入也可能是粉丝助力页
            // 通过expandNo 查询发起信息 判断是发起人或是粉丝
            // 本人访问
            if (reqDTO.getFanNo().equals(reqDTO.getShareUser()) || StringUtils.isBlank(reqDTO.getExpandNo())) {
                AtomReqExpandRecordDTO recordDTO = new AtomReqExpandRecordDTO();
                recordDTO.setFanNo(reqDTO.getFanNo());
                recordDTO.setPromotionNo(reqDTO.getPromotionNo());
                recordDTO.setSortType(SortTypeEnum.SORT_CREATE_TIME.getCode());
                log.info("-ExpandRuleAnalysisServiceImpl-getExpandRecordPage-param={}", JSON.toJSONString(recordDTO));
                // 是否有未兑换的发起活动
                ExecuteDTO<ExecutePageDTO<AtomResExpandRecordDTO>> executeDTO = atomExpandRecordAnalysisService.getExpandRecordPage(recordDTO);
                log.info("-ExpandRuleAnalysisServiceImpl-getExpandRecordPage-return={}", JSON.toJSONString(executeDTO));
                if (!executeDTO.successFlag()) {
                    return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
                }
                List<AtomResExpandRecordDTO> rowsData = executeDTO.getData().getRows();
                // 没有记录待发起
                if (CollectionUtils.isEmpty(rowsData)) {
                    // 待发起状态
                    detailDTO.setExpandRuleInfo(ruleInfoDTO);
                    detailDTO.setStatus(ExpandStatusEnum.EXPAND_STATUS_TWO.getCode());
                    detailDTO.setShowMsg(ExpandStatusEnum.EXPAND_STATUS_TWO.getShowMsg());
                    return ExecuteDTO.success(detailDTO);
                }

                resExpandRecordDTO = rowsData.get(NumConstant.ZERO);
                ruleInfoDTO.setHeadImg(resExpandRecordDTO.getHeadImg());
                ruleInfoDTO.setNickName(resExpandRecordDTO.getNickName());

                // 助力数量
                ExecuteDTO<Integer> helpRecordCount = getHelpRecordCount(resExpandRecordDTO.getExpandNo());
                if (!helpRecordCount.successFlag()) {
                    return ExecuteDTO.error(helpRecordCount.getStatus(), helpRecordCount.getMsg());
                }
                // 已兑换
                if (resExpandRecordDTO.getExchangeStatus().equals(WhetherEnum.YES.getCode())) {
                    detailDTO.setExpandRuleInfo(ruleInfoDTO);
                    detailDTO.setStatus(ExpandStatusEnum.EXPAND_STATUS_SIX.getCode());
                    detailDTO.setExchangeStatus(resExpandRecordDTO.getExchangeStatus());
                    detailDTO.setHelpRecordCount(helpRecordCount.getData());
                    detailDTO.setShowMsg(ExpandStatusEnum.EXPAND_STATUS_SIX.getShowMsg());
                    detailDTO.setExpandNo(resExpandRecordDTO.getExpandNo());
                    detailDTO.setExpandMoney(BigDecimalUtil.setScale(resExpandRecordDTO.getExpandMoney()));
                    // 设置再次发起按钮标识
                    this.setStartFlag(detailDTO,reqDTO,resExpandRuleDTO);
                    return ExecuteDTO.success(detailDTO);
                }
                // 达到领取阈值 未兑换的
                if (resExpandRecordDTO.getExpandMoney().compareTo(resExpandRecordDTO.getExchangeThreshold()) >= NumConstant.ZERO) {
                    detailDTO.setExpandRuleInfo(ruleInfoDTO);
                    detailDTO.setStatus(ExpandStatusEnum.EXPAND_STATUS_FIVE.getCode());
                    detailDTO.setHelpRecordCount(helpRecordCount.getData());
                    detailDTO.setExpandNo(resExpandRecordDTO.getExpandNo());
                    detailDTO.setExpandMoney(BigDecimalUtil.setScale(resExpandRecordDTO.getExpandMoney()));
                    detailDTO.setShowMsg(ExpandStatusEnum.EXPAND_STATUS_FIVE.getShowMsg());
                    return ExecuteDTO.success(detailDTO);
                } else {
                    // 已发起状态
                    detailDTO.setExpandRuleInfo(ruleInfoDTO);
                    detailDTO.setStatus(ExpandStatusEnum.EXPAND_STATUS_FOUR.getCode());
                    detailDTO.setHelpRecordCount(helpRecordCount.getData());
                    detailDTO.setExpandNo(resExpandRecordDTO.getExpandNo());
                    detailDTO.setExpandMoney(BigDecimalUtil.setScale(resExpandRecordDTO.getExpandMoney()));
                    detailDTO.setShowMsg(ExpandStatusEnum.EXPAND_STATUS_FOUR.getShowMsg());
                    return ExecuteDTO.success(detailDTO);
                }
            } else {
                if (resExpandRecordDTO == null) {
                    return ExecuteDTO.error(CommonCode.CODE_10000001, "data");
                }
                // 粉丝访问
                // 助力数量
                ExecuteDTO<Integer> helpRecordCount = getHelpRecordCount(reqDTO.getExpandNo());
                if (!helpRecordCount.successFlag()) {
                    return ExecuteDTO.error(helpRecordCount.getStatus(), helpRecordCount.getMsg());
                }

                AtomReqExpandHelpRecordDTO helpRecordDTO = BeanCopierUtil.copy(reqDTO, AtomReqExpandHelpRecordDTO.class);
                helpRecordDTO.setFanNo(null);
                // 校验是否助力过
                ExecuteDTO<List<AtomResExpandHelpRecordDTO>> helpRecordList = atomExpandHelpRecordAnalysisService.getExpandHelpRecordList(helpRecordDTO);
                if (!helpRecordList.successFlag()) {
                    return ExecuteDTO.error(helpRecordList.getStatus(), helpRecordList.getMsg());
                }
                // 没人助力过
                if (CollectionUtils.isEmpty(helpRecordList.getData())) {
                    detailDTO.setExpandRuleInfo(ruleInfoDTO);
                    detailDTO.setStatus(ExpandStatusEnum.EXPAND_STATUS_SEVEN.getCode());
                    detailDTO.setHelpRecordCount(helpRecordCount.getData());
                    detailDTO.setIsFans(WhetherEnum.YES.getCode());
                    detailDTO.setShowMsg(ExpandStatusEnum.EXPAND_STATUS_SEVEN.getShowMsg());
                    detailDTO.setExpandMoney(BigDecimalUtil.setScale(resExpandRecordDTO.getExpandMoney()));
                    return ExecuteDTO.success(detailDTO);
                } else {
                    List<AtomResExpandHelpRecordDTO> helpRecordListData = helpRecordList.getData();
                    // 已有助力记录  1：校验是否已经助力满 2：查询当前粉丝是否助力过
                    Optional<AtomResExpandHelpRecordDTO> helpRecordInfo = helpRecordListData.stream().filter(helpRecord -> helpRecord.getFanNo().equals(reqDTO.getFanNo())).findFirst();
                    if (helpRecordInfo.isPresent()) {
                        // 已助力且已兑换状态
                        if (resExpandRecordDTO.getExchangeStatus().equals(WhetherEnum.YES.getCode())) {
                            detailDTO.setExpandRuleInfo(ruleInfoDTO);
                            detailDTO.setStatus(ExpandStatusEnum.EXPAND_STATUS_TEN.getCode());
                            detailDTO.setExchangeStatus(resExpandRecordDTO.getExchangeStatus());
                            detailDTO.setHelpRecordCount(helpRecordCount.getData());
                            detailDTO.setIsFans(WhetherEnum.YES.getCode());
                            detailDTO.setShowMsg(String.format(ExpandStatusEnum.EXPAND_STATUS_TEN.getShowMsg(), BigDecimalUtil.setScale(resExpandRecordDTO.getExpandMoney()).toString()));
                            detailDTO.setExpandMoney(BigDecimalUtil.setScale(resExpandRecordDTO.getExpandMoney()));
                            // 设置再次发起按钮标识
                            this.setStartFlag(detailDTO,reqDTO,resExpandRuleDTO);
                            return ExecuteDTO.success(detailDTO);
                        } else {
                            AtomResExpandHelpRecordDTO recordDTO = helpRecordInfo.get();
                            detailDTO.setExpandRuleInfo(ruleInfoDTO);
                            detailDTO.setStatus(ExpandStatusEnum.EXPAND_STATUS_EIGHT.getCode());
                            detailDTO.setHelpRecordCount(helpRecordCount.getData());
                            detailDTO.setIsFans(WhetherEnum.YES.getCode());
                            detailDTO.setFansHelpMoney(recordDTO.getHelpMoney());
                            detailDTO.setShowMsg(String.format(ExpandStatusEnum.EXPAND_STATUS_EIGHT.getShowMsg(), BigDecimalUtil.setScale(resExpandRecordDTO.getExpandMoney()).toString()));
                            detailDTO.setExpandMoney(BigDecimalUtil.setScale(resExpandRecordDTO.getExpandMoney()));
                            // 设置再次发起按钮标识
                            this.setStartFlag(detailDTO,reqDTO,resExpandRuleDTO);
                            return ExecuteDTO.success(detailDTO);
                        }
                    }
                    // 校验是否已助力结束
                    else if (resExpandRecordDTO.getExchangeStatus().equals(WhetherEnum.YES.getCode()) || resExpandRuleDTO.getInviteHelpTimes().equals(helpRecordCount.getData())) {
                        detailDTO.setExpandRuleInfo(ruleInfoDTO);
                        detailDTO.setStatus(ExpandStatusEnum.EXPAND_STATUS_NINE.getCode());
                        detailDTO.setHelpRecordCount(helpRecordCount.getData());
                        detailDTO.setExchangeStatus(resExpandRecordDTO.getExchangeStatus());
                        detailDTO.setIsFans(WhetherEnum.YES.getCode());
                        detailDTO.setShowMsg(String.format(ExpandStatusEnum.EXPAND_STATUS_NINE.getShowMsg(), BigDecimalUtil.setScale(resExpandRecordDTO.getExpandMoney()).toString()));
                        detailDTO.setExpandMoney(BigDecimalUtil.setScale(resExpandRecordDTO.getExpandMoney()));
                        return ExecuteDTO.success(detailDTO);
                    } else {
                        detailDTO.setExpandRuleInfo(ruleInfoDTO);
                        detailDTO.setStatus(ExpandStatusEnum.EXPAND_STATUS_SEVEN.getCode());
                        detailDTO.setExchangeStatus(resExpandRecordDTO.getExchangeStatus());
                        detailDTO.setHelpRecordCount(helpRecordCount.getData());
                        detailDTO.setIsFans(WhetherEnum.YES.getCode());
                        detailDTO.setShowMsg(ExpandStatusEnum.EXPAND_STATUS_SEVEN.getShowMsg());
                        detailDTO.setExpandMoney(BigDecimalUtil.setScale(resExpandRecordDTO.getExpandMoney()));
                        return ExecuteDTO.success(detailDTO);
                    }

                }
            }
        } else {
            if (resExpandRecordDTO == null) {
                return ExecuteDTO.error(CommonCode.CODE_10000001, "data");
            }
            // 说明活动已经发起
            if (PromotionStatusEnum.IN_PROGRESS.getCode().equals(resExpandRuleDTO.getStatus())) {
                // 助力数量
                ExecuteDTO<Integer> helpRecordCount = getHelpRecordCount(reqDTO.getExpandNo());
                if (!helpRecordCount.successFlag()) {
                    return ExecuteDTO.error(helpRecordCount.getStatus(), helpRecordCount.getMsg());
                }
                if (resExpandRecordDTO.getExchangeStatus().equals(WhetherEnum.YES.getCode()) || resExpandRuleDTO.getInviteHelpTimes().equals(helpRecordCount.getData())) {
                    detailDTO.setExpandRuleInfo(ruleInfoDTO);
                    detailDTO.setStatus(ExpandStatusEnum.EXPAND_STATUS_NINE.getCode());
                    detailDTO.setHelpRecordCount(helpRecordCount.getData());
                    detailDTO.setIsFans(WhetherEnum.YES.getCode());
                    detailDTO.setShowMsg(String.format(ExpandStatusEnum.EXPAND_STATUS_NINE.getShowMsg(), BigDecimalUtil.setScale(resExpandRecordDTO.getExpandMoney()).toString()));
                    detailDTO.setExpandMoney(BigDecimalUtil.setScale(resExpandRecordDTO.getExpandMoney()));
                    return ExecuteDTO.success(detailDTO);
                }
                // 带有expandNo 是粉丝助力页
                detailDTO.setExpandRuleInfo(ruleInfoDTO);
                detailDTO.setStatus(ExpandStatusEnum.EXPAND_STATUS_SEVEN.getCode());
                detailDTO.setHelpRecordCount(helpRecordCount.getData());
                detailDTO.setIsFans(WhetherEnum.YES.getCode());
                detailDTO.setShowMsg(ExpandStatusEnum.EXPAND_STATUS_SEVEN.getShowMsg());
                detailDTO.setExpandMoney(BigDecimalUtil.setScale(resExpandRecordDTO.getExpandMoney()));
                return ExecuteDTO.success(detailDTO);
            }
        }
        return ExecuteDTO.error(CommonCode.CODE_10000003);
    }
    /**
     * 是否还有发起次数
     */

    private void setStartFlag(ResExpandRuleDetailDTO detailDTO, ReqExpandRuleDetailDTO reqDTO,ResExpandRuleDTO resExpandRuleDTO) {
        // 获取用户发起的次数
        ReqExpandRecordDTO reqExpandRecordDTO = new ReqExpandRecordDTO();
        reqExpandRecordDTO.setPromotionNo(reqDTO.getPromotionNo());
        reqExpandRecordDTO.setFanNo(reqDTO.getFanNo());
        reqExpandRecordDTO.setStoreNo(reqDTO.getStoreNo());
        ExecuteDTO<ResExpandRecordCountDTO> countExecuteDTO = this.expandRecordAnalysisService.getUserExpandRecordCount(reqExpandRecordDTO);
        if(!countExecuteDTO.successFlag()) {
            throw new BaseException(CommonCode.CODE_10000003);
        }
        ResExpandRecordCountDTO resExpandRecordCountDTO = countExecuteDTO.getData();
        Integer userDailTimes = resExpandRuleDTO.getUserDailyTimes() == null ? NumConstant.ZERO : resExpandRuleDTO.getUserDailyTimes();
        Integer userTotalTimes = resExpandRuleDTO.getUserTotalTimes() == null ? NumConstant.ZERO : resExpandRuleDTO.getUserTotalTimes();
        // 活动期间累计可发起数量
        // 获取某活动累计发起的数量
        if(resExpandRuleDTO.getUserTotalTimes() != null && resExpandRecordCountDTO.getUserTotalTimes() > userTotalTimes) {
            detailDTO.setStartFlag(WhetherEnum.NO.getCode());
        }
        // 每日可发起数量
        // 获取当日发起的数量
        if(resExpandRuleDTO.getUserDailyTimes() != null && resExpandRecordCountDTO.getUserDailyTimes() > userDailTimes) {
            detailDTO.setStartFlag(WhetherEnum.NO.getCode());
        }
    }

    /**
     * 助力数量
     */
    private ExecuteDTO<Integer> getHelpRecordCount(String expandNo) {
        // 已助力数量
        AtomReqExpandHelpRecordDTO helpRecordDTO = new AtomReqExpandHelpRecordDTO();
        helpRecordDTO.setExpandNo(expandNo);
        ExecuteDTO<Integer> recordCountDto = atomExpandHelpRecordAnalysisService.getExpandHelpRecordCount(helpRecordDTO);
        if (!recordCountDto.successFlag()) {
            return ExecuteDTO.error(recordCountDto.getStatus(), recordCountDto.getMsg());
        }
        return recordCountDto;
    }

    protected void getStoreNames(List<ResExpandRuleDTO> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            //获取有店铺编码商品
            List<ResExpandRuleDTO> storeGoodsDTOList = list.stream().filter(goodsDTO -> StringUtils.isNotBlank(goodsDTO.getStoreNo())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(storeGoodsDTOList)) {
                List<String> storeNoList = storeGoodsDTOList.stream().map(ResExpandRuleDTO::getStoreNo).collect(Collectors.toList());
                //获取店铺集合
                ExecuteDTO<List<GetStoreListResponse>> storeDTO = userPublicService.queryStoreList(storeNoList);
                if (storeDTO.successFlag() && CollectionUtils.isNotEmpty(storeDTO.getData())) {
                    storeGoodsDTOList.forEach(resDTO -> {
                        Optional<GetStoreListResponse> optional = storeDTO.getData().stream().filter(store -> resDTO.getStoreNo().equals(store.getStoreNo())).findFirst();
                        optional.ifPresent(getStoreListResponse -> resDTO.setStoreName(getStoreListResponse.getStoreName()));
                    });
                }
            }
        }
    }

    private void setExpandStatus(List<ResExpandRuleDTO> resExpandRuleDTOS) {
        if (CollectionUtils.isNotEmpty(resExpandRuleDTOS)) {
            resExpandRuleDTOS.forEach(ruleDto -> {
                // 草稿状态
                if (StringUtils.isNotEmpty(ruleDto.getStatus()) && PromotionStatusEnum.ONLY_SAVED.getCode().equals(ruleDto.getStatus())) {
                    ruleDto.setStatus(PromotionStatusEnum.ONLY_SAVED.getCode());
                    // 活动有效期（1001：长期有效 1002：指定日期）
                } else if (EffectivePeriodTypeEnum.EFFECTIVE_PERIOD_TYPE_ONE.getCode().equals(ruleDto.getPeriodValidity())) {
                    ruleDto.setStatus(PromotionStatusEnum.IN_PROGRESS.getCode());
                    // 其他状态
                } else if (EffectivePeriodTypeEnum.EFFECTIVE_PERIOD_TYPE_TWO.getCode().equals(ruleDto.getPeriodValidity())
                        && ruleDto.getEffectiveTime() != null && ruleDto.getInvalidTime() != null) {
                    //未开始
                    if (ruleDto.getEffectiveTime().isAfter(LocalDateTime.now())) {
                        ruleDto.setStatus(PromotionStatusEnum.NOT_STARTED.getCode());
                        //进行中
                    } else if ((ruleDto.getEffectiveTime().isBefore(LocalDateTime.now()) || ruleDto.getEffectiveTime().equals(LocalDateTime.now()))
                            && (ruleDto.getInvalidTime().equals(LocalDateTime.now()) || ruleDto.getInvalidTime().isAfter(LocalDateTime.now()))) {
                        ruleDto.setStatus(PromotionStatusEnum.IN_PROGRESS.getCode());
                        //已结束
                    } else if (ruleDto.getInvalidTime().isBefore(LocalDateTime.now())) {
                        ruleDto.setStatus(PromotionStatusEnum.EXPIRED.getCode());
                    }
                }
            });
        }
    }

    private void setOneExpandStatus(ResExpandRuleDTO dto) {
        if (dto != null) {
            // 草稿状态
            if (StringUtils.isNotEmpty(dto.getStatus()) && PromotionStatusEnum.ONLY_SAVED.getCode().equals(dto.getStatus())) {
                dto.setStatus(PromotionStatusEnum.ONLY_SAVED.getCode());
                // 活动有效期（1001：长期有效 1002：指定日期）
            } else if (EffectivePeriodTypeEnum.EFFECTIVE_PERIOD_TYPE_ONE.getCode().equals(dto.getPeriodValidity())) {
                dto.setStatus(PromotionStatusEnum.IN_PROGRESS.getCode());
                // 其他状态
            } else if (EffectivePeriodTypeEnum.EFFECTIVE_PERIOD_TYPE_TWO.getCode().equals(dto.getPeriodValidity())
                    && dto.getEffectiveTime() != null && dto.getInvalidTime() != null) {
                //未开始
                if (dto.getEffectiveTime().isAfter(LocalDateTime.now())) {
                    dto.setStatus(PromotionStatusEnum.NOT_STARTED.getCode());
                    //进行中
                } else if ((dto.getEffectiveTime().isBefore(LocalDateTime.now()) || dto.getEffectiveTime().equals(LocalDateTime.now()))
                        && (dto.getInvalidTime().equals(LocalDateTime.now()) || dto.getInvalidTime().isAfter(LocalDateTime.now()))) {
                    dto.setStatus(PromotionStatusEnum.IN_PROGRESS.getCode());
                    //已结束
                } else if (dto.getInvalidTime().isBefore(LocalDateTime.now())) {
                    dto.setStatus(PromotionStatusEnum.EXPIRED.getCode());
                }
            }
        }
    }


    private ExecuteDTO<AtomResExpandRecordDTO> setUseCount(ResExpandRuleDTO resExpandRuleDTO) {
        AtomReqExpandRecordDTO atomReqExpandRecordDTO = new AtomReqExpandRecordDTO();
        atomReqExpandRecordDTO.setPromotionNo(resExpandRuleDTO.getPromotionNo());
        ExecuteDTO<AtomResExpandRecordDTO> expandRecordUseCount = this.atomExpandRecordAnalysisService.getExpandRecordUseCount(atomReqExpandRecordDTO);
        if (!expandRecordUseCount.successFlag()) {
            return ExecuteDTO.error(expandRecordUseCount.getStatus(), expandRecordUseCount.getMsg());
        }
        return expandRecordUseCount;
    }

}
