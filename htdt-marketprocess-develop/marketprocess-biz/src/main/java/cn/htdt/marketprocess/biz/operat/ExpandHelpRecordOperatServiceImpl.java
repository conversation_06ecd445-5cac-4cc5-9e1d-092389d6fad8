package cn.htdt.marketprocess.biz.operat;

import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.enums.MarketErrorCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.market.*;
import cn.htdt.common.generator.MarketFormGenerator;
import cn.htdt.common.redis.utils.RedisUtil;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.*;
import cn.htdt.marketcenter.dto.response.AtomResExpandRuleDTO;
import cn.htdt.marketprocess.api.operat.ExpandHelpRecordOperatService;
import cn.htdt.marketprocess.api.operat.ExpandRecordOperatService;
import cn.htdt.marketprocess.biz.constant.KeysConstant;
import cn.htdt.marketprocess.biz.conversion.ExpandHelpRecorAssert;
import cn.htdt.marketprocess.dao.ExpandHelpRecordDao;
import cn.htdt.marketprocess.domain.ExpandHelpRecordDomain;
import cn.htdt.marketprocess.dto.request.ReqExpandHelpRecordDTO;
import cn.htdt.marketprocess.dto.request.ReqExpandRecordDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomExpandRuleAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomCouponSettingOperatService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomCouponUserRecordOperatService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomExpandHelpRecordOperatService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomExpandRecordOperatService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 膨胀红包助力记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Slf4j
@DubboService
public class ExpandHelpRecordOperatServiceImpl implements ExpandHelpRecordOperatService {

    /**
     * 膨胀红包活动基本信息
     */
    @Resource
    private AtomExpandRuleAnalysisService atomExpandRuleAnalysisService;
    /**
     * 膨胀红包记录信息
     */
    @Resource
    private AtomExpandRecordOperatService atomExpandRecordOperatService;
    /**
     * 膨胀红包发起记录
     */
    @Resource
    private AtomExpandHelpRecordOperatService atomExpandHelpRecordOperatService;

    @Autowired
    private ExpandRecordOperatService expandRecordOperatService;

    @Autowired
    private ExpandHelpRecorAssert expandHelpRecorAssert;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private AtomCouponUserRecordOperatService atomCouponUserRecordOperatService;

    @Autowired
    private ExpandHelpRecordDao expandHelpRecordDao;
    //20230818蛋品-盛守武-膨胀红包改动（start）
    @Resource
    private AtomCouponSettingOperatService atomCouponSettingOperatService;

    private static final String HELP_SUFFIX = "_help";
    //20230818蛋品-盛守武-膨胀红包改动（end）
    @Override
    public ExecuteDTO helpExpandHelpRecord(ReqExpandHelpRecordDTO reqExpandHelpRecordDTO) {
        log.info("-ExpandHelpRecordOperatServiceImpl-helpExpandHelpRecord-param={}", JSON.toJSONString(reqExpandHelpRecordDTO));
        log.info("-ExpandHelpRecordOperatServiceImpl-helpExpandHelpRecord-start");
        expandHelpRecorAssert.helpExpandHelpRecordAssert(reqExpandHelpRecordDTO);
        String key = String.format(KeysConstant.PROMOTION_EXPAND_FAN_PREFIX, reqExpandHelpRecordDTO.getStoreNo(), reqExpandHelpRecordDTO.getShareFanNo(), reqExpandHelpRecordDTO.getExpandNo());
        if (!redisUtil.hasKey(key)) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000711);
        }
        String value = redisUtil.lpop(key);
        if (StringUtils.isEmpty(value)) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000711);
        }
        AtomReqExpandRecordDTO atomReqExpandRecordDTO = new AtomReqExpandRecordDTO();
        atomReqExpandRecordDTO.setExpandNo(reqExpandHelpRecordDTO.getExpandNo());
        atomReqExpandRecordDTO.setExpandMoney(new BigDecimal(value));
        ExecuteDTO executeDTO = this.atomExpandRecordOperatService.moneyExpandRecord(atomReqExpandRecordDTO);
        if (!executeDTO.successFlag()) {
            AtomReqExpandRuleDTO atomReqExpandRuleDTO = new AtomReqExpandRuleDTO();
            atomReqExpandRuleDTO.setPromotionNo(reqExpandHelpRecordDTO.getPromotionNo());
            ExecuteDTO<AtomResExpandRuleDTO> ruleExecuteDTO = this.atomExpandRuleAnalysisService.getExpandRule(atomReqExpandRuleDTO);
            log.info("-ExpandHelpRecordOperatServiceImpl-helpExpandHelpRecord-活动信息={}", JSON.toJSONString(ruleExecuteDTO));
            AtomResExpandRuleDTO atomResExpandRuleDTO = ruleExecuteDTO.getData();
            if (ruleExecuteDTO.successFlag() && atomResExpandRuleDTO == null) {
                return ExecuteDTO.error(MarketErrorCode.CODE_17000100);
            }
            LocalDateTime couponInvalidTime = atomResExpandRuleDTO.getCouponInvalidTime();
            log.info("-ExpandHelpRecordOperatServiceImpl-helpExpandHelpRecord-生成发起红包-key={}-value={}-couponInvalidTime={}", key, value, couponInvalidTime);
            redisUtil.lPush(key, value, couponInvalidTime);
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        AtomReqExpandHelpRecordDTO atomReqExpandHelpRecordDTO = BeanCopierUtil.copy(reqExpandHelpRecordDTO, AtomReqExpandHelpRecordDTO.class);
        String expandHelpNo = MarketFormGenerator.genExpandHelpNo();
        atomReqExpandHelpRecordDTO.setHelpNo(expandHelpNo);
        atomReqExpandHelpRecordDTO.setExpandNo(reqExpandHelpRecordDTO.getExpandNo());
        atomReqExpandHelpRecordDTO.setHelpMoney(new BigDecimal(value));
        executeDTO = this.atomExpandHelpRecordOperatService.insertExpandHelpRecord(atomReqExpandHelpRecordDTO);
        if (!executeDTO.successFlag()) {
            AtomReqExpandRuleDTO atomReqExpandRuleDTO = new AtomReqExpandRuleDTO();
            atomReqExpandRuleDTO.setPromotionNo(reqExpandHelpRecordDTO.getPromotionNo());
            ExecuteDTO<AtomResExpandRuleDTO> ruleExecuteDTO = this.atomExpandRuleAnalysisService.getExpandRule(atomReqExpandRuleDTO);
            log.info("-ExpandHelpRecordOperatServiceImpl-helpExpandHelpRecord-活动信息={}", JSON.toJSONString(ruleExecuteDTO));
            AtomResExpandRuleDTO atomResExpandRuleDTO = ruleExecuteDTO.getData();
            if (ruleExecuteDTO.successFlag() && atomResExpandRuleDTO == null) {
                return ExecuteDTO.error(MarketErrorCode.CODE_17000100);
            }
            LocalDateTime couponInvalidTime = atomResExpandRuleDTO.getCouponInvalidTime();
            log.info("-ExpandHelpRecordOperatServiceImpl-helpExpandHelpRecord-生成发起红包-key={}-value={}-couponInvalidTime={}", key, value, couponInvalidTime);
            redisUtil.lPush(key, value, couponInvalidTime);
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        // 最后一个助力的人完成后 自动兑换红包
        long listSize = redisUtil.lGetListSize(key);
        if (listSize == 0L) {
            ReqExpandRecordDTO recordDTO = BeanCopierUtil.copy(reqExpandHelpRecordDTO, ReqExpandRecordDTO.class);
            recordDTO.setExchangeStatus(WhetherEnum.YES.getCode());
            ExecuteDTO expandRecord = expandRecordOperatService.exchangeExpandRecord(recordDTO);
            if (!expandRecord.successFlag()) {
                return ExecuteDTO.error(expandRecord.getStatus(),expandRecord.getMsg());
            }
        }
        //20230818蛋品-盛守武-膨胀红包改动（start）
        AtomReqExpandRuleDTO atomReqExpandRuleDTO = new AtomReqExpandRuleDTO();
        atomReqExpandRuleDTO.setPromotionNo(reqExpandHelpRecordDTO.getPromotionNo());
        ExecuteDTO<AtomResExpandRuleDTO> ruleExecuteDTO = this.atomExpandRuleAnalysisService.getExpandRule(atomReqExpandRuleDTO);
        log.info("-ExpandHelpRecordOperatServiceImpl-helpExpandHelpRecord-活动信息={}", JSON.toJSONString(ruleExecuteDTO));
        AtomResExpandRuleDTO resExpandRuleDTO = ruleExecuteDTO.getData();
        if (resExpandRuleDTO != null && resExpandRuleDTO.getHelperCouponIssueFlag()) {
            // 助力成功，给助力者发放优惠券
            // 查询助力者券是否发放，userCouponNo字段为空表示未发券
            LambdaQueryWrapper<ExpandHelpRecordDomain> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(ExpandHelpRecordDomain::getFanNo, reqExpandHelpRecordDTO.getFanNo());
            lambdaQueryWrapper.eq(ExpandHelpRecordDomain::getPromotionNo, reqExpandHelpRecordDTO.getPromotionNo());
            lambdaQueryWrapper.orderByDesc(ExpandHelpRecordDomain::getCreateTime);
            List<ExpandHelpRecordDomain> helpRecordList = expandHelpRecordDao.selectList(lambdaQueryWrapper);
            if (CollectionUtils.isNotEmpty(helpRecordList)) {
                ExpandHelpRecordDomain latestHelpRecord = helpRecordList.get(0);
                if (StringUtils.isBlank(latestHelpRecord.getUserCouponNo())) {
                    // 保存助力者优惠券配置信息
                    AtomReqCouponSettingDTO couponSettingDTO = getAtomReqCouponSettingDTO(resExpandRuleDTO, helpRecordList.get(0));
                    atomCouponSettingOperatService.changeExpandCoupon(couponSettingDTO);
                    AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO = buildCouponUserRecordParam(reqExpandHelpRecordDTO, resExpandRuleDTO, expandHelpNo);
                    log.info("-ExpandHelpRecordOperatServiceImpl-buildCouponUserRecordParam-param={}", JSON.toJSONString(atomReqCouponUserRecordDTO));
                    this.atomCouponUserRecordOperatService.batchInsertCouponUserRecord(Collections.singletonList(atomReqCouponUserRecordDTO));
                    // 发券成功，更新膨胀红包助力记录表的券编号
                    ExpandHelpRecordDomain helpRecordDomain = new ExpandHelpRecordDomain();
                    LambdaUpdateWrapper<ExpandHelpRecordDomain> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(StringUtils.isNotBlank(expandHelpNo), ExpandHelpRecordDomain::getHelpNo, expandHelpNo);
                    updateWrapper.set(StringUtils.isNotBlank(atomReqCouponUserRecordDTO.getUserCouponNo()), ExpandHelpRecordDomain::getUserCouponNo, atomReqCouponUserRecordDTO.getUserCouponNo());
                    expandHelpRecordDao.update(helpRecordDomain, updateWrapper);
                }
            }
        }
        //20230818蛋品-盛守武-膨胀红包改动（end）
        log.info("-ExpandHelpRecordOperatServiceImpl-helpExpandHelpRecord-end");
        return ExecuteDTO.success();
    }

    /**
     * 构造助力者优惠券配置入参
     * //20230818蛋品-盛守武-膨胀红包改动
     *
     * @param resExpandRuleDTO
     * @param helpRecord
     * @return
     */
    private AtomReqCouponSettingDTO getAtomReqCouponSettingDTO(AtomResExpandRuleDTO resExpandRuleDTO, ExpandHelpRecordDomain helpRecord) {
        AtomReqCouponSettingDTO couponSettingDTO = new AtomReqCouponSettingDTO();
        String couponNo = resExpandRuleDTO.getPromotionNo();
        couponSettingDTO.setPromotionNo(couponNo);
        couponSettingDTO.setCouponNo(helpRecord.getHelpNo());
        couponSettingDTO.setPromotionName(resExpandRuleDTO.getPromotionName());
        couponSettingDTO.setCouponName(resExpandRuleDTO.getPromotionName());
        // 促销类型-膨胀红包
        couponSettingDTO.setPromotionCouponType(PromotionTypeCouponEnum.RED_PACKET_COUPON.getCode());
        // 使用门槛阈值（满减时表示满多少元）
        couponSettingDTO.setDiscountThreshold(resExpandRuleDTO.getHelperCouponThreshold());
        // 券面额
        couponSettingDTO.setCouponValue(resExpandRuleDTO.getHelperCouponValue());
        // 券标识
        couponSettingDTO.setCouponIdentity(resExpandRuleDTO.getCouponIdentity());
        // 券有效期（1001：长期有效 1002：指定日期 1003:自领取后天数有效），默认1002
        couponSettingDTO.setCouponPeriodValidity(resExpandRuleDTO.getCouponPeriodValidity());
        // 券开始时间
        couponSettingDTO.setCouponEffectiveTime(resExpandRuleDTO.getCouponEffectiveTime());
        // 券结束时间
        couponSettingDTO.setCouponInvalidTime(resExpandRuleDTO.getCouponInvalidTime());
        // 券使用渠道 (1001:汇享购下单 1002:门店下单)
        couponSettingDTO.setCouponUseChannel(CouponUseChannelEnum.COUPON_USE_CHANNEL_ONE.getCode());
        // 券使用范围(1001:店铺全场通用 1002:云池商品通用 1003:指定云池商品)
        couponSettingDTO.setCouponUseScope(resExpandRuleDTO.getCouponUseScope());
        couponSettingDTO.setCreateNo(resExpandRuleDTO.getCreateNo());
        couponSettingDTO.setCreateName(resExpandRuleDTO.getCreateName());
        couponSettingDTO.setModifyNo(resExpandRuleDTO.getModifyNo());
        couponSettingDTO.setModifyName(resExpandRuleDTO.getModifyName());
        couponSettingDTO.setMerchantNo(resExpandRuleDTO.getMerchantNo());
        couponSettingDTO.setMerchantName(resExpandRuleDTO.getMerchantName());
        couponSettingDTO.setStoreNo(resExpandRuleDTO.getStoreNo());
        couponSettingDTO.setStoreName(resExpandRuleDTO.getStoreName());
        // 基础信息
        // 活动对象 1001: 不限制 1002: 限制老用户 1003: 限制新用户
        couponSettingDTO.setUserScope(UserScopeEnum.NONE.getCode());
        // 活动类型 1000:抽奖  2000:商品促销 3000:优惠券 4000:报名活动
        couponSettingDTO.setActivityType(ActivityTypeEnum.COUPON.getCode());
        // 促销类型 3100:抽奖券
        couponSettingDTO.setPromotionType(PromotionTypeCouponEnum.RED_PACKET_COUPON.getCode());
        couponSettingDTO.setStatus(PromotionStatusEnum.IN_PROGRESS.getCode());
        couponSettingDTO.setUpDownFlag(WhetherEnum.YES.getCode());
        return couponSettingDTO;
    }

    /**
     * 构造用户券参数
     * //20230818蛋品-盛守武-膨胀红包改动
     *
     * @param reqExpandHelpRecordDTO
     * @param resExpandRuleDTO
     * @param expandHelpNo
     * @return
     */
    private AtomReqCouponUserRecordDTO buildCouponUserRecordParam(ReqExpandHelpRecordDTO reqExpandHelpRecordDTO, AtomResExpandRuleDTO resExpandRuleDTO, String expandHelpNo) {
        AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO = new AtomReqCouponUserRecordDTO();
        atomReqCouponUserRecordDTO.setPromotionNo(reqExpandHelpRecordDTO.getPromotionNo());
        atomReqCouponUserRecordDTO.setCouponNo(expandHelpNo);
        atomReqCouponUserRecordDTO.setDiscountThreshold(resExpandRuleDTO.getHelperCouponThreshold());
        atomReqCouponUserRecordDTO.setUserCouponValue(resExpandRuleDTO.getHelperCouponValue());
        atomReqCouponUserRecordDTO.setCouponEffectiveTime(resExpandRuleDTO.getCouponEffectiveTime());
        atomReqCouponUserRecordDTO.setCouponInvalidTime(resExpandRuleDTO.getCouponInvalidTime());
        atomReqCouponUserRecordDTO.setUserCouponNo(reqExpandHelpRecordDTO.getExpandNo() + HELP_SUFFIX);
        atomReqCouponUserRecordDTO.setSendBatchNo(MarketFormGenerator.genSendBatchNo());
        atomReqCouponUserRecordDTO.setFanNo(reqExpandHelpRecordDTO.getFanNo());
        atomReqCouponUserRecordDTO.setPhone(reqExpandHelpRecordDTO.getPhone());
        atomReqCouponUserRecordDTO.setDsPhone(reqExpandHelpRecordDTO.getDsPhone());
        atomReqCouponUserRecordDTO.setSourceType(CouponSourceTypeEnum.EXPAND_HELP_OBTAIN.getCode());
        atomReqCouponUserRecordDTO.setCouponPeriodValidity(resExpandRuleDTO.getCouponPeriodValidity());
        atomReqCouponUserRecordDTO.setCouponEffectiveDay(resExpandRuleDTO.getCouponEffectiveDay());
        atomReqCouponUserRecordDTO.setCouponType(CouponTypeEnum.COUPON_REDUCE.getCode());
        atomReqCouponUserRecordDTO.setMerchantNo(resExpandRuleDTO.getMerchantNo());
        atomReqCouponUserRecordDTO.setMerchantName(resExpandRuleDTO.getMerchantName());
        atomReqCouponUserRecordDTO.setStoreNo(resExpandRuleDTO.getStoreNo());
        atomReqCouponUserRecordDTO.setStoreName(resExpandRuleDTO.getStoreName());
        atomReqCouponUserRecordDTO.setCreateNo(reqExpandHelpRecordDTO.getCreateNo());
        atomReqCouponUserRecordDTO.setCreateName(reqExpandHelpRecordDTO.getCreateName());
        return atomReqCouponUserRecordDTO;
    }

    private void operatError() {

    }

}
