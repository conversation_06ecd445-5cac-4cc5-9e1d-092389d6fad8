package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.market.VirtualCoinRuleTypeEnum;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.CollectionUtils;
import cn.htdt.marketcenter.dto.request.AtomReqVirtualCoinOrderDTO;
import cn.htdt.marketcenter.dto.request.AtomReqVirtualCoinOrderFlowDTO;
import cn.htdt.marketcenter.dto.response.AtomResVirtualCoinOrderDTO;
import cn.htdt.marketcenter.dto.response.AtomResVirtualCoinOrderFlowDTO;
import cn.htdt.marketprocess.api.analysis.VirtualCoinOrderAnalysisService;
import cn.htdt.marketprocess.biz.conversion.VirtualCoinOrderAssert;
import cn.htdt.marketprocess.biz.utils.VirtualCoinUtil;
import cn.htdt.marketprocess.dto.request.ReqVirtualCoinOrderDTO;
import cn.htdt.marketprocess.dto.request.ReqVirtualCoinOrderFlowDTO;
import cn.htdt.marketprocess.dto.response.ResUserVirtualCoinAndLatestRecordDTO;
import cn.htdt.marketprocess.dto.response.ResVirtualCoinOrderFlowDTO;
import cn.htdt.marketprocess.dto.response.ResVirtualCoinOrderPageDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomVirtualCoinOrderAnalysisService;
import cn.htdt.usercenter.dto.request.ReqFancDTO;
import cn.htdt.usercenter.dto.response.ResFancDTO;
import cn.htdt.userprocess.api.LegacyUserCenterService;
import cn.htdt.userprocess.api.UcFansProcessService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/1/25
 */
@DubboService
@Slf4j
public class VirtualCoinOrderAnalysisServiceImpl implements VirtualCoinOrderAnalysisService {

    @Resource
    private AtomVirtualCoinOrderAnalysisService orderAnalysisService;

    @Resource
    private VirtualCoinUtil virtualCoinUtil;

    @Resource
    private VirtualCoinOrderAssert virtualCoinOrderAssert;

    @DubboReference
    private LegacyUserCenterService legacyUserCenterService;

    @DubboReference
    private UcFansProcessService ucFansProcessService;

    @Override
    public ExecuteDTO<ResVirtualCoinOrderFlowDTO> selectOrderFlow(ReqVirtualCoinOrderFlowDTO flowDto) {
        AtomReqVirtualCoinOrderFlowDTO queryFlowDTO = BeanCopierUtil.copy(flowDto, AtomReqVirtualCoinOrderFlowDTO.class);
        ExecuteDTO<AtomResVirtualCoinOrderFlowDTO> flowDTOExecuteDTO = orderAnalysisService.selectOrderFlow(queryFlowDTO);
        if (null != flowDTOExecuteDTO && flowDTOExecuteDTO.successFlag()) {
            return ExecuteDTO.ok(BeanCopierUtil.copy(flowDTOExecuteDTO.getData(), ResVirtualCoinOrderFlowDTO.class));
        }
        return ExecuteDTO.ok(null);
    }

    private ResVirtualCoinOrderFlowDTO selectOrderFlow(String outTradeNo) {
        ExecuteDTO<AtomResVirtualCoinOrderFlowDTO> flowDTOExecuteDTO = orderAnalysisService.selectOrderFlowByOutTradeNo(outTradeNo);
        if (null != flowDTOExecuteDTO && flowDTOExecuteDTO.successFlag()) {
            return BeanCopierUtil.copy(flowDTOExecuteDTO.getData(), ResVirtualCoinOrderFlowDTO.class);
        }
        return null;
    }

    @Override
    public ExecuteDTO<ExecutePageDTO<ResVirtualCoinOrderPageDTO>> selectOrderByStoreNoByPage(ReqVirtualCoinOrderDTO queryDto) {
        log.info("selectOrderByStoreNoByPage--->请求入参: {}", JSON.toJSONString(queryDto));
        virtualCoinOrderAssert.selectOrderByStoreNoByPageAssert(queryDto);

        ExecutePageDTO<ResVirtualCoinOrderPageDTO> executePageDTO = new ExecutePageDTO<>();
        executePageDTO.setTotal(NumConstant.ZERO);
        executePageDTO.setRows(new ArrayList<>());

        AtomReqVirtualCoinOrderDTO queryOrderDto = BeanCopierUtil.copy(queryDto, AtomReqVirtualCoinOrderDTO.class);

        // 20230928蛋品-赵翔宇-商家橙豆-财务报表-橙豆收益, 店铺退出共享店铺, 商家依然可以看到橙豆收益
        String virtualCoinRuleType = virtualCoinUtil.getVirtualCoinRuleType(queryDto.getStoreNo(), queryDto.getMerchantNo(), queryDto.getLoginIdentity());
        queryOrderDto.setRuleType(virtualCoinRuleType);

        ExecuteDTO<ExecutePageDTO<AtomResVirtualCoinOrderDTO>> ordersPage = orderAnalysisService.selectOrderByStoreNoByPage(queryOrderDto);
        if (null != ordersPage && ordersPage.successFlag() && null != ordersPage.getData() && CollectionUtils.isNotEmpty(ordersPage.getData().getRows())) {
            ExecutePageDTO<AtomResVirtualCoinOrderDTO> ordersPageData = ordersPage.getData();
            executePageDTO.setTotal(ordersPageData.getTotal());
            executePageDTO.setRows(BeanCopierUtil.copyList(ordersPageData.getRows(), ResVirtualCoinOrderPageDTO.class));

            // 获取粉丝编号集合
            List<String> fansNoList = executePageDTO.getRows().stream().map(ResVirtualCoinOrderPageDTO::getFanNo).distinct().collect(Collectors.toList());

            // 粉丝信息
            List<ResFancDTO> fansInfoList = Collections.emptyList();

            ReqFancDTO reqFancDTO = new ReqFancDTO();
            reqFancDTO.setFansNoList(fansNoList);
            ExecuteDTO<List<ResFancDTO>> fansInfoExecuteDto;

            // 20230928蛋品-赵翔宇-商家橙豆-财务报表-橙豆收益
            if (VirtualCoinRuleTypeEnum.STORE_RULE.getCode().equals(virtualCoinRuleType)) {
                reqFancDTO.setStoreNo(queryDto.getStoreNo());
                fansInfoExecuteDto = legacyUserCenterService.getFansInfoByNos(reqFancDTO);
            } else {
                reqFancDTO.setMerchantNo(queryDto.getMerchantNo());
                fansInfoExecuteDto = ucFansProcessService.getMerchantFanInfo(reqFancDTO);
            }

            if (fansInfoExecuteDto.successFlag() && CollectionUtils.isNotEmpty(fansInfoExecuteDto.getData())) {
                fansInfoList = fansInfoExecuteDto.getData();
            }

            for (ResVirtualCoinOrderPageDTO row : executePageDTO.getRows()) {
                // 设置店铺名以及手机号
                Optional<ResFancDTO> fanInfoOptional = fansInfoList.stream().filter(resFanDTO -> resFanDTO.getFanNo().equals(row.getFanNo())).findFirst();
                if (fanInfoOptional.isPresent()) {
                    ResFancDTO resFancDTO = fanInfoOptional.get();
                    row.setStoreFanName(resFancDTO.getStoreFanName());
                    row.setMobile(resFancDTO.getPhone());
                }
            }

            if (CollectionUtils.isNotEmpty(executePageDTO.getRows())) {
                List<ResVirtualCoinOrderPageDTO> resVirtualCoinOrderList = executePageDTO.getRows();
                // 20230928单品-商家储值-财务报表-橙豆收益, 店铺退出共享店铺, 结果里粉丝手机号会为空, 根据粉丝编号再去查询一边
                List<ResVirtualCoinOrderPageDTO> blankPhoneOrderInfoList = resVirtualCoinOrderList
                                                                            .stream()
                                                                            .filter(resVirtualCoinOrder -> StringUtils.isBlank(resVirtualCoinOrder.getMobile()))
                                                                            .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(blankPhoneOrderInfoList)) {
                    List<String> blankPhoneFanNoList = blankPhoneOrderInfoList
                                                        .stream()
                                                        .map(ResVirtualCoinOrderPageDTO::getFanNo)
                                                        .distinct()
                                                        .collect(Collectors.toList());

                    reqFancDTO = new ReqFancDTO();
                    reqFancDTO.setFansNoList(blankPhoneFanNoList);
                    log.info("selectOrderByStoreNoByPage--->get fanPhone info, reqFancDTO: {}", JSON.toJSONString(reqFancDTO));
                    ExecuteDTO<List<ResFancDTO>> fanPhoneInfoExecute = ucFansProcessService.selectFanPhoneInfo(reqFancDTO);
                    log.info("selectOrderByStoreNoByPage--->get fanPhone info, fanPhoneInfoExecute: {}", JSON.toJSONString(fanPhoneInfoExecute));
                    if (null != fanPhoneInfoExecute && fanPhoneInfoExecute.successFlag() && CollectionUtils.isNotEmpty(fanPhoneInfoExecute.getData())) {
                        List<ResFancDTO> phoneInfoList = fanPhoneInfoExecute.getData();
                        for (ResVirtualCoinOrderPageDTO resVirtualCoinOrderPageDTO : resVirtualCoinOrderList) {
                            Optional<ResFancDTO> fanPhoneInfoOp = phoneInfoList.stream().
                                    filter(fanPhoneInfo -> fanPhoneInfo.getFanNo()
                                            .equals(resVirtualCoinOrderPageDTO.getFanNo())).findFirst();

                            // 设置手机号
                            if (fanPhoneInfoOp.isPresent()) {
                                ResFancDTO fanPhoneInfo = fanPhoneInfoOp.get();
                                resVirtualCoinOrderPageDTO.setMobile(fanPhoneInfo.getPhone());
                            }
                        }

                    }
                }
            }

            // 下面为原有的逻辑
            //            Map<String, ResFansFollowDTO> map = new HashMap<>();
//            for (ResVirtualCoinOrderPageDTO row : executePageDTO.getRows()) {
//                ResFansFollowDTO resFansFollowDTO = map.get(row.getFanNo());
//                if (null == resFansFollowDTO) {
//                    ReqFansFollowDTO reqFansFollowDTO = new ReqFansFollowDTO();
//                    reqFansFollowDTO.setFanNo(row.getFanNo());
//                    reqFansFollowDTO.setStoreNumber(row.getStoreNo());
//                    ExecuteDTO<List<ResFansFollowDTO>> fanFollow = legacyUserCenterService.getFanFollow(reqFansFollowDTO);
//                    if (null != fanFollow && CollectionUtils.isNotEmpty(fanFollow.getData())) {
//                        resFansFollowDTO = fanFollow.getData().get(0);
//                        map.put(row.getFanNo(), resFansFollowDTO);
//                    }
//                }
//                if (null != resFansFollowDTO) {
//                    if (StringUtils.isNotEmpty(resFansFollowDTO.getStoreFanName())) {
//                        row.setStoreFanName(resFansFollowDTO.getStoreFanName());
//                    }
//                    row.setMobile(resFansFollowDTO.getPhone());
//                }
//            }
        }
        return ExecuteDTO.ok(executePageDTO);
    }

    @Override
    public ExecuteDTO<BigDecimal> getTotalPayment(ReqVirtualCoinOrderDTO queryDto) {

        AtomReqVirtualCoinOrderDTO queryOrderDto = BeanCopierUtil.copy(queryDto, AtomReqVirtualCoinOrderDTO.class);

        // 20230928蛋品-赵翔宇-商家橙豆-橙豆流水, 财务报表-橙豆收益, 店铺退出共享店铺, 依然可以看到橙豆收益
        String virtualCoinRuleType = virtualCoinUtil.getVirtualCoinRuleType(queryDto.getStoreNo(), queryDto.getMerchantNo(), queryDto.getLoginIdentity());
        queryOrderDto.setRuleType(virtualCoinRuleType);

        ExecuteDTO<BigDecimal> totalPayment = orderAnalysisService.getTotalPayment(queryOrderDto);
        if (null != totalPayment) {
            return totalPayment;
        }
        return ExecuteDTO.ok(BigDecimal.ZERO);
    }

    @Override
    public ExecuteDTO<ResVirtualCoinOrderFlowDTO> selectOrderFlowByOutTradeNo(String outTradeNo) {
        ExecuteDTO<AtomResVirtualCoinOrderFlowDTO> flowDTOExecuteDTO = orderAnalysisService.selectOrderFlowByOutTradeNo(outTradeNo);
        if (null != flowDTOExecuteDTO && flowDTOExecuteDTO.successFlag()) {
            return ExecuteDTO.ok(BeanCopierUtil.copy(flowDTOExecuteDTO.getData(), ResVirtualCoinOrderFlowDTO.class));
        }
        return ExecuteDTO.ok(null);
    }
}
