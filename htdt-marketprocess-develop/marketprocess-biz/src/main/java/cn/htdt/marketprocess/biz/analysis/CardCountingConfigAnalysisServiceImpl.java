package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.base.exception.BaseException;
import cn.htdt.common.dto.enums.MarketErrorCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.dto.utils.ExecuteDTOUtil;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.market.CardPeriodValidityEnum;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.ListUtil;
import cn.htdt.goodsprocess.api.analysis.GoodsAnalysisService;
import cn.htdt.goodsprocess.dto.request.goods.ReqGoodsDTO;
import cn.htdt.goodsprocess.dto.response.goodsrealstock.ResGoodsRealStockDTO;
import cn.htdt.marketprocess.api.analysis.CardCountingConfigAnalysisService;
import cn.htdt.marketprocess.biz.conversion.CardCountingConfigAssert;
import cn.htdt.marketprocess.dao.CardCountingConfigDao;
import cn.htdt.marketprocess.dto.request.ReqCardCountingConfigDTO;
import cn.htdt.marketprocess.dto.request.ReqPromotionQueryDTO;
import cn.htdt.marketprocess.dto.response.ResCardCountingConfigDTO;
import cn.htdt.marketprocess.dto.response.ResVoucherSaleAndUsedCountDTO;
import cn.htdt.marketprocess.vo.CardCountingConfigVO;
import cn.htdt.marketprocess.vo.VoucherSaleAndUsedCountVO;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 计次卡设置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-11
 */
@Slf4j
@DubboService
public class CardCountingConfigAnalysisServiceImpl implements CardCountingConfigAnalysisService {

    @Resource
    private CardCountingConfigDao cardCountingConfigDao;

    @Resource
    private GoodsAnalysisService goodsAnalysisService;

    @Autowired
    private CardCountingConfigAssert cardCountingConfigAssert;

    @Override
    public ExecuteDTO<ResCardCountingConfigDTO> checkCardCountingConfig(ReqCardCountingConfigDTO reqCardCountingConfigDTO) throws BaseException {
        log.info("-CardCountingConfigAnalysisServiceImpl-checkCardCountingConfig-param={}", JSON.toJSONString(reqCardCountingConfigDTO));
        // 参数校验
        cardCountingConfigAssert.checkCardCountingConfigAssert(reqCardCountingConfigDTO);
        // 判断活动是否存在
        CardCountingConfigVO cardCountingConfigVO = new CardCountingConfigVO();
        cardCountingConfigVO.setPromotionNo(reqCardCountingConfigDTO.getPromotionNo());
        CardCountingConfigVO cardCountingConfig = this.cardCountingConfigDao.selectCardCountingConfigInfo(cardCountingConfigVO);
        log.info("-CardCountingConfigAnalysisServiceImpl-checkCardCountingConfig-活动信息={}", JSON.toJSONString(cardCountingConfig));
        if (cardCountingConfig == null) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000100);
        }
        // 计次卡已删除
        if(cardCountingConfig.getDeleteFlag() != null && cardCountingConfig.getDeleteFlag() == NumConstant.TWO) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17001202);
        }
        // 计次卡已下架
        if(cardCountingConfig.getUpDownFlag() != null && cardCountingConfig.getUpDownFlag() != NumConstant.TWO) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17001201);
        }
        // 计次卡已过期，不支持购买
        if(cardCountingConfig.getCardPeriodValidity() != null && CardPeriodValidityEnum.CARD_PERIOD_VALIDITY_TWO.getCode().equals(cardCountingConfig.getCardPeriodValidity())) {
            if(LocalDateTime.now().isAfter(cardCountingConfig.getCardInvalidTime())) {
                return ExecuteDTO.error(MarketErrorCode.CODE_17001200);
            }
        }
        ResCardCountingConfigDTO resCardCountingConfigDTO = BeanCopierUtil.copy(cardCountingConfig, ResCardCountingConfigDTO.class);
        return ExecuteDTO.success(resCardCountingConfigDTO);
    }

    @Override
    public ExecuteDTO<ResCardCountingConfigDTO> getCardCountingConfigStock(ReqCardCountingConfigDTO reqCardCountingConfigDTO) {
        log.info("-CardCountingConfigAnalysisServiceImpl-getCardCountingConfig-param={}", JSON.toJSONString(reqCardCountingConfigDTO));
        // 参数校验
        cardCountingConfigAssert.getCardCountingConfigAssert(reqCardCountingConfigDTO);
        // 判断活动是否存在
        CardCountingConfigVO cardCountingConfigVO = new CardCountingConfigVO();
        cardCountingConfigVO.setPromotionNo(reqCardCountingConfigDTO.getPromotionNo());
        CardCountingConfigVO cardCountingConfig = this.cardCountingConfigDao.selectCardCountingConfigInfo(cardCountingConfigVO);
        log.info("-CardCountingConfigAnalysisServiceImpl-getCardCountingConfig-活动信息={}", JSON.toJSONString(cardCountingConfig));
        if (cardCountingConfig == null) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000100);
        }
        ResCardCountingConfigDTO resCardCountingConfigDTO = BeanCopierUtil.copy(cardCountingConfig, ResCardCountingConfigDTO.class);
        // 根据计次卡编号获取商品
        if(resCardCountingConfigDTO != null && StringUtils.isNotBlank(resCardCountingConfigDTO.getCardCountingNo())) {
            ReqGoodsDTO reqGoodsDTO = new ReqGoodsDTO();
            reqGoodsDTO.setThirdGoodsNo(resCardCountingConfigDTO.getCardCountingNo());
            ExecuteDTO<ResGoodsRealStockDTO> stockExecuteDTO = this.goodsAnalysisService.getGoodsStockByThirdGoodsNo(reqGoodsDTO);
            log.info("-CardCountingConfigAnalysisServiceImpl-getCardCountingConfig-stockExecuteDTO={}", JSON.toJSONString(stockExecuteDTO));
            if(!stockExecuteDTO.successFlag()){
                return ExecuteDTO.error(stockExecuteDTO.getStatus(), stockExecuteDTO.getMsg());
            }
            BigDecimal availableStockNum = BigDecimal.ZERO;
            if(stockExecuteDTO != null && stockExecuteDTO.getData() != null && stockExecuteDTO.getData().getAvailableStockNum() != null
                && stockExecuteDTO.getData().getAvailableStockNum().compareTo(BigDecimal.ZERO) > NumConstant.ZERO) {
                availableStockNum = stockExecuteDTO.getData().getAvailableStockNum();
            }
            resCardCountingConfigDTO.setAvailableStockNum(availableStockNum);
        }
        return ExecuteDTO.success(resCardCountingConfigDTO);
    }

    @Override
    public ExecuteDTO<ExecutePageDTO<ResCardCountingConfigDTO>> getCardCountingPage(ReqCardCountingConfigDTO reqCardCountingConfigDTO) throws BaseException {
        log.info("-CardCountingConfigAnalysisServiceImpl-getCardCountingPage-param={}", JSON.toJSONString(reqCardCountingConfigDTO));
        // 判断活动是否存在
        CardCountingConfigVO cardCountingConfigVO = BeanCopierUtil.copy(reqCardCountingConfigDTO, CardCountingConfigVO.class);
        Page<Object> pages = PageHelper.startPage(reqCardCountingConfigDTO);
        List<CardCountingConfigVO> cardCountingConfigList = this.cardCountingConfigDao.getCardCountingPage(cardCountingConfigVO);
        log.info("-CardCountingConfigAnalysisServiceImpl-getCardCountingPage-活动信息={}", JSON.toJSONString(cardCountingConfigList));

        List<ResCardCountingConfigDTO> resCardCountingConfigDTOs = BeanCopierUtil.copyList(cardCountingConfigList, ResCardCountingConfigDTO.class);

        return ExecuteDTO.ok(new ExecutePageDTO<>(pages.getTotal(), resCardCountingConfigDTOs));
    }

    @Override
    public ExecuteDTO<ResCardCountingConfigDTO> getCardCountingConfig(ReqCardCountingConfigDTO reqCardCountingConfigDTO) throws BaseException {
        log.info("-CardCountingConfigAnalysisServiceImpl-getCardCountingConfig-param={}", JSON.toJSONString(reqCardCountingConfigDTO));
        // 判断活动是否存在
        CardCountingConfigVO cardCountingConfigVO = BeanCopierUtil.copy(reqCardCountingConfigDTO, CardCountingConfigVO.class);
        CardCountingConfigVO cardCountingConfig = this.cardCountingConfigDao.selectCardCountingConfigInfo(cardCountingConfigVO);
        log.info("-CardCountingConfigAnalysisServiceImpl-getCardCountingConfig-活动信息={}", JSON.toJSONString(cardCountingConfig));

        if (cardCountingConfig == null) {
            return ExecuteDTOUtil.error(MarketErrorCode.CODE_17000100.getCode(), MarketErrorCode.CODE_17000100.getShowMsg());
        } else {
            return ExecuteDTO.ok(BeanCopierUtil.copy(cardCountingConfig, ResCardCountingConfigDTO.class));
        }
    }

    @Override
    public ExecuteDTO<List<ResVoucherSaleAndUsedCountDTO>> getPromotionSaleAndUsedCount(ReqPromotionQueryDTO queryDTO) throws BaseException {
        log.info("-CardCountingConfigAnalysisServiceImpl-getPromotionSaleAndUsedCount-param={}", JSON.toJSONString(queryDTO));
        if (ListUtil.isNotEmpty(queryDTO.getPromotionNos())) {
            List<VoucherSaleAndUsedCountVO> saleAndUsedCountVOS = cardCountingConfigDao.selectSaleAndUsedCount(queryDTO.getPromotionNos(), queryDTO.getIngoreDeleteFlag());
            List<ResVoucherSaleAndUsedCountDTO> resultList = BeanCopierUtil.copyList(saleAndUsedCountVOS, ResVoucherSaleAndUsedCountDTO.class);
            log.info("-CardCountingConfigAnalysisServiceImpl-getPromotionSaleAndUsedCount-{}", JSON.toJSONString(resultList));
            return ExecuteDTO.ok(resultList);
        }
        log.info("-CardCountingConfigAnalysisServiceImpl-getPromotionSaleAndUsedCount-emptyList");
        return ExecuteDTO.ok(Collections.emptyList());
    }

}
