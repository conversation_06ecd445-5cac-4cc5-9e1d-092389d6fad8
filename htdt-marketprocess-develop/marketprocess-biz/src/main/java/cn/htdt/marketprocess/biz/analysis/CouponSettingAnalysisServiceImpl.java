package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.base.exception.BaseException;
import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.enums.GoodsErrorCode;
import cn.htdt.common.dto.enums.MarketErrorCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.goods.GoodsFormEnum;
import cn.htdt.common.enums.goods.GoodsQueryTypeEnum;
import cn.htdt.common.enums.goods.SeriesTypeEnum;
import cn.htdt.common.enums.market.*;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.CloudPoolCommissionCalculationUtil;
import cn.htdt.common.utils.ListUtil;
import cn.htdt.goodscenter.dto.request.AtomReqGoodsDTO;
import cn.htdt.goodscenter.dto.request.AtomReqGoodsRealStockDTO;
import cn.htdt.goodscenter.dto.response.AtomResGoodsDTO;
import cn.htdt.goodscenter.dto.response.AtomResGoodsRealStockDTO;
import cn.htdt.goodsprocess.api.analysis.CloudPoolApplyAnalysisService;
import cn.htdt.goodsprocess.api.analysis.GoodsAnalysisService;
import cn.htdt.goodsprocess.api.operat.LegacyGoodsCenterService;
import cn.htdt.goodsprocess.dto.request.goods.ReqGoodsDTO;
import cn.htdt.goodsprocess.dto.response.goods.ResGoodsDTO;
import cn.htdt.goodsprocess.dto.response.goods.ResSeriesGoodsDTO;
import cn.htdt.goodsprocess.dto.response.goods.ResSubGoodsDTO;
import cn.htdt.marketcenter.dto.request.AtomReqCloudPoolGoodsCommissionConfigDTO;
import cn.htdt.marketcenter.dto.request.AtomReqCouponGoodsDTO;
import cn.htdt.marketcenter.dto.request.AtomReqCouponSettingDTO;
import cn.htdt.marketcenter.dto.request.AtomReqPromotionStoreRelationDTO;
import cn.htdt.marketcenter.dto.response.*;
import cn.htdt.marketprocess.api.analysis.CouponPromotionAnalysisService;
import cn.htdt.marketprocess.api.analysis.CouponSettingAnalysisService;
import cn.htdt.marketprocess.biz.conversion.CouponSettingAssert;
import cn.htdt.marketprocess.biz.utils.StoreRelationUtil;
import cn.htdt.marketprocess.biz.utils.VirtualCoinUtil;
import cn.htdt.marketprocess.dao.CouponSettingDao;
import cn.htdt.marketprocess.dao.CouponUserRecordDao;
import cn.htdt.marketprocess.dao.PromotionVirtualGoodsRelationDao;
import cn.htdt.marketprocess.domain.PromotionVirtualGoodsRelationDomain;
import cn.htdt.marketprocess.dto.request.*;
import cn.htdt.marketprocess.dto.response.*;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomAgentMarketRewardsetAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomCloudPoolGoodsCommissionConfigAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomCouponSettingAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomPromotionStoreRelationAnalysisService;
import cn.htdt.marketprocess.vo.*;
import cn.htdt.orderprocess.api.OrderAnalysisService;
import cn.htdt.orderprocess.dto.request.ReqOrderItemDTO;
import cn.htdt.userprocess.api.MemberLevelProcessService;
import cn.htdt.userprocess.api.StoreProcessService;
import cn.htdt.userprocess.api.UserPublicService;
import cn.htdt.userprocess.dto.publicDto.response.GetStoreListResponse;
import cn.htdt.userprocess.dto.request.MerchantStoreRequest;
import cn.htdt.userprocess.dto.request.user.ReqMemberLevelDTO;
import cn.htdt.userprocess.dto.response.MerchantStoreResponse;
import cn.htdt.userprocess.dto.response.user.ResMemberLevelDTO;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
@DubboService
public class CouponSettingAnalysisServiceImpl implements CouponSettingAnalysisService {

    /**
     * 优惠券活动
     */
    @Resource
    private AtomCouponSettingAnalysisService atomCouponSettingAnalysisService;

    @DubboReference
    private LegacyGoodsCenterService legacyGoodsCenterService;

    /**
     * 酬劳
     */
    @Resource
    private AtomAgentMarketRewardsetAnalysisService atomAgentMarketRewardsetAnalysisService;

    @DubboReference
    private CloudPoolApplyAnalysisService cloudPoolApplyAnalysisService;

    @DubboReference
    private GoodsAnalysisService goodsAnalysisService;

    @DubboReference
    private OrderAnalysisService orderAnalysisService;

    @DubboReference
    private UserPublicService userPublicService;

    @Resource
    private AtomCloudPoolGoodsCommissionConfigAnalysisService atomCloudPoolGoodsCommissionConfigAnalysisService;

    @Autowired
    private CouponPromotionAnalysisService couponPromotionAnalysisService;

    /**
     * 优惠券使用记录
     */
    @Autowired
    CouponUserRecordDao couponUserRecordDao;

    @Autowired
    PromotionVirtualGoodsRelationDao promotionVirtualGoodsRelationDao;

    @Autowired
    private CouponSettingAssert couponSettingAssert;

    @Resource
    private CouponSettingDao couponSettingDao;

    @Resource
    private AtomPromotionStoreRelationAnalysisService promotionStoreRelationAnalysisService;

    @DubboReference
    private StoreProcessService storeProcessService;

    @DubboReference
    private MemberLevelProcessService memberLevelProcessService;

    @Resource
    private StoreRelationUtil storeRelationUtil;

    @Resource
    private VirtualCoinUtil virtualCoinUtil;

    @Override
    public ExecuteDTO<ExecutePageDTO<ResCouponSettingDTO>> getCouponSettingPage(ReqCouponSettingDTO reqCouponSettingDTO) {
        log.info("MarketProcess-CouponSettingAnalysisServiceImpl-getCouponSettingPage-params-start");
        log.info("MarketProcess-CouponSettingAnalysisServiceImpl-getCouponSettingPage-params={}", JSON.toJSONString(reqCouponSettingDTO));
        AtomReqCouponSettingDTO atomReqCouponSettingDTO = BeanCopierUtil.copy(reqCouponSettingDTO, AtomReqCouponSettingDTO.class);
        //是否店铺角色查询
        Integer loginIdentity = reqCouponSettingDTO.getLoginIdentity();
        boolean isStore = loginIdentity != null && (loginIdentity.equals(NumConstant.FOUR) || loginIdentity.equals(NumConstant.EIGHT));
        if(StringUtils.isNotEmpty(reqCouponSettingDTO.getStoreNo())){
            //分发店铺查询商家已上过架的领取活动
            atomReqCouponSettingDTO.setHasUpFlag(WhetherEnum.YES.getCode());
        }
        ExecuteDTO<ExecutePageDTO<AtomResCouponSettingDTO>> executeDTO = atomCouponSettingAnalysisService.getCouponSettingPage(atomReqCouponSettingDTO);
        log.info("MarketProcess-CouponSettingAnalysisServiceImpl-getCouponSettingPage-return={}", JSON.toJSONString(executeDTO));
        log.info("MarketProcess-CouponSettingAnalysisServiceImpl-getCouponSettingPage-end");
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        //参数转换
        ExecutePageDTO<ResCouponSettingDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResCouponSettingDTO> resCouponSettingDTOS = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResCouponSettingDTO.class);
        // 设置活动状态
        this.setCouponStatus(resCouponSettingDTOS);
        // 设置上下架状态
        this.setUpDown(resCouponSettingDTOS, isStore);
        // 商家查询设置分发店铺信息
        this.setStoreInfo(resCouponSettingDTOS, loginIdentity, reqCouponSettingDTO.getMerchantNo(), reqCouponSettingDTO.getPromotionCouponType());
        executePageDTO.setRows(resCouponSettingDTOS);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    /**
     * 商家查询设置分发店铺信息
     * @param resCouponSettingDTOs
     * @param loginIdentity
     * @param merchantNo
     */
    public ExecuteDTO setStoreInfo(List<ResCouponSettingDTO> resCouponSettingDTOs, Integer loginIdentity, String merchantNo, String promotionCouponType){
        if (CollectionUtils.isNotEmpty(resCouponSettingDTOs) && loginIdentity != null && loginIdentity.equals(NumConstant.TWO)
                && StringUtils.isNotEmpty(merchantNo) && PromotionTypeCouponEnum.MERCHANT_ONLINE_FANS_COUPON.getCode().equals(promotionCouponType)){
            List<String> promotionNoList = resCouponSettingDTOs.stream().map(ResCouponSettingDTO::getPromotionNo).collect(Collectors.toList());
            AtomReqPromotionStoreRelationDTO atomReqPromotionStoreRelationDTO = new AtomReqPromotionStoreRelationDTO();
            atomReqPromotionStoreRelationDTO.setMerchantNo(merchantNo);
            atomReqPromotionStoreRelationDTO.setPromotionNoList(promotionNoList);
            atomReqPromotionStoreRelationDTO.setDisableFlag(StoreUseEnum.ENABLE.getCode());
            ExecuteDTO<List<AtomResPromotionStoreRelationDTO>> resStoreRelationlistExecuteDTO = promotionStoreRelationAnalysisService.getStoreRelationList(atomReqPromotionStoreRelationDTO);
            if (!resStoreRelationlistExecuteDTO.successFlag()) {
                return ExecuteDTO.error(resStoreRelationlistExecuteDTO.getStatus(), resStoreRelationlistExecuteDTO.getMsg());
            }
            List<AtomResPromotionStoreRelationDTO> resStoreRelationlist = resStoreRelationlistExecuteDTO.getData();
            if (CollectionUtils.isNotEmpty(resStoreRelationlist)){
                ExecuteDTO<List<MerchantStoreResponse>> merchantStoreResponseExecuteDTO = storeRelationUtil.merchantStore(merchantNo);
                if (null == merchantStoreResponseExecuteDTO) {
                    return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
                }
                if (!merchantStoreResponseExecuteDTO.successFlag()) {
                    return new ExecuteDTO<>(merchantStoreResponseExecuteDTO.getStatus(), merchantStoreResponseExecuteDTO.getMsg(), null);
                }
                List<MerchantStoreResponse> merchantStoreResponseList = merchantStoreResponseExecuteDTO.getData();
                //商家没有店铺
                if (CollectionUtils.isEmpty(merchantStoreResponseList)) {
                    return new ExecuteDTO<>(MarketErrorCode.CODE_17000636.getCode(), MarketErrorCode.CODE_17000636.getInMsg(), null);
                }
                Map<String, MerchantStoreResponse> merchantStoreMap = merchantStoreResponseList.stream().collect(Collectors.toMap(MerchantStoreResponse::getStoreNo, store -> store));
                List<String> allStoreNameList = merchantStoreResponseList.stream().map(MerchantStoreResponse::getStoreName).collect(Collectors.toList());
                resCouponSettingDTOs.forEach(resCouponSettingDTO -> {
                    List<AtomResPromotionStoreRelationDTO> couponStoreRelationlist = resStoreRelationlist.stream().filter(relation -> resCouponSettingDTO.getPromotionNo().equals(relation.getPromotionNo())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(couponStoreRelationlist)){
                        AtomicReference<String> storeType = new AtomicReference<String>();
                        couponStoreRelationlist.forEach(relation -> {
                            if(StringUtils.isEmpty(relation.getStoreNo())){
                                storeType.set(relation.getStoreType());
                            }
                        });
                        if(StoreTypeEnum.STORE_TYPE_ONE.getCode().equals(storeType.get())){
                            //全部店铺
                            resCouponSettingDTO.setStoreNameList(allStoreNameList);
                            resCouponSettingDTO.setStoreNum(allStoreNameList.size());
                        } else if(StoreTypeEnum.STORE_TYPE_THREE.getCode().equals(storeType.get())){
                            //指定店铺
                            List<String> storeNameList = Lists.newArrayList();
                            couponStoreRelationlist.forEach(relation -> {
                                if(StringUtils.isNotEmpty(relation.getStoreNo())){
                                    storeNameList.add(merchantStoreMap.get(relation.getStoreNo()).getStoreName());
                                }
                            });
                            resCouponSettingDTO.setStoreNameList(storeNameList);
                            resCouponSettingDTO.setStoreNum(storeNameList.size());
                        }
                    }
                });
            }
        }
        return ExecuteDTO.success();
    }

    /**
     * 设置上下架状态
     * @param resCouponSettingDTOs
     * @param isStore
     */
    public void setUpDown(List<ResCouponSettingDTO> resCouponSettingDTOs, boolean isStore) {
        if (CollectionUtils.isNotEmpty(resCouponSettingDTOs)){
            resCouponSettingDTOs.forEach(resCouponSettingDTO -> {
                resCouponSettingDTO.setOriginUpDownFlag(resCouponSettingDTO.getUpDownFlag());
                if(isStore && resCouponSettingDTO.getStoreRelationUpDownFlag() != null){
                    //上架
                    Integer upDownCode = resCouponSettingDTO.getUpDownFlag();
                    if(StoreUpDownEnum.UP.getCode().equals(resCouponSettingDTO.getUpDownFlag())
                            && ((StoreUpDownEnum.UP.getCode().equals(resCouponSettingDTO.getStoreRelationUpDownFlag())
                                    && StoreUseEnum.ENABLE.getCode().equals(resCouponSettingDTO.getStoreRelationDisableFlag())
                                )
                            )
                    ){
                        upDownCode = StoreUpDownEnum.UP.getCode();
                    }
                    //下架
                    if(StoreUpDownEnum.DOWN.getCode().equals(resCouponSettingDTO.getUpDownFlag())
                            || StoreUpDownEnum.DOWN.getCode().equals(resCouponSettingDTO.getStoreRelationUpDownFlag())
                            || StoreUseEnum.DISABLE.getCode().equals(resCouponSettingDTO.getStoreRelationDisableFlag())
                    ){
                        upDownCode = StoreUpDownEnum.DOWN.getCode();
                    }
                    //移除店铺默认下架
                    if(StoreUseEnum.DISABLE.getCode().equals(resCouponSettingDTO.getStoreRelationDisableFlag())){
                        resCouponSettingDTO.setOriginUpDownFlag(StoreUpDownEnum.DOWN.getCode());
                    }
                    resCouponSettingDTO.setUpDownFlag(upDownCode);
                }
            });
        }
    }

    @Override
    public ExecuteDTO<ExecutePageDTO<ResCouponSettingDTO>> getCouponSettingMerchantStorePage(ReqCouponSettingDTO reqCouponSettingDTO) {
        // 商家、店铺优惠券查询平台优惠券
        ExecuteDTO<ExecutePageDTO<AtomResCouponSettingDTO>> couponSetting4StoreByPage = atomCouponSettingAnalysisService.getCouponSetting4StoreByPage(BeanCopierUtil.copy(reqCouponSettingDTO, AtomReqCouponSettingDTO.class));
        if (!couponSetting4StoreByPage.successFlag()) {
            return ExecuteDTO.error(couponSetting4StoreByPage.getStatus(), couponSetting4StoreByPage.getMsg());
        }
        ExecutePageDTO<ResCouponSettingDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResCouponSettingDTO> resCouponSettingDTOS = BeanCopierUtil.copyList(couponSetting4StoreByPage.getData().getRows(), ResCouponSettingDTO.class);
        // 设置活动状态
        this.setCouponStatus(resCouponSettingDTOS);
        executePageDTO.setRows(resCouponSettingDTOS);
        executePageDTO.setTotal(couponSetting4StoreByPage.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    @Override
    public ExecuteDTO<ResCouponSettingDTO> getCouponSetting(ReqCouponSettingDTO reqCouponSettingDTO) {
        log.info("MarketProcess-CouponSettingAnalysisServiceImpl-getCouponSetting-params-start");
        log.info("MarketProcess-CouponSettingAnalysisServiceImpl-getCouponSetting-params={}", JSON.toJSONString(reqCouponSettingDTO));
        AtomReqCouponSettingDTO atomReqCouponSettingDTO = BeanCopierUtil.copy(reqCouponSettingDTO, AtomReqCouponSettingDTO.class);
        ExecuteDTO<AtomResCouponSettingDTO> executeDTO = atomCouponSettingAnalysisService.getCouponSetting(atomReqCouponSettingDTO);
        log.info("MarketProcess-CouponSettingAnalysisServiceImpl-getCouponSetting-return={}", JSON.toJSONString(executeDTO));
        log.info("MarketProcess-CouponSettingAnalysisServiceImpl-getCouponSetting-end");
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResCouponSettingDTO resCouponSettingDTO = BeanCopierUtil.copy(executeDTO.getData(), ResCouponSettingDTO.class);
        // 设置活动状态
        this.setOneCouponStatus(resCouponSettingDTO);
        //20230928蛋品-吴鑫鑫-优惠券增加折扣-优惠券活动详情查询优惠券折扣返回
        if(CouponTypeEnum.COUPON_DISCONUT.getCode().equals(resCouponSettingDTO.getCouponType())){
            resCouponSettingDTO.setCouponDiscount(resCouponSettingDTO.getCouponValue());
        }
        //20230928蛋品-吴鑫鑫-营销管理-优惠券活动详情返回查询商品类型
        if(CouponUseScopeEnum.COUPON_USE_SCOPE_NINE.getCode().equals(resCouponSettingDTO.getCouponUseScope())){
            resCouponSettingDTO.setGoodsQueryType(GoodsQueryTypeEnum.MERCHANT_MERCHANT.getCode());
        }
        return ExecuteDTO.success(resCouponSettingDTO);
    }

    @Override
    public ExecuteDTO<ResCouponGoodsDTO> getCouponGoodsRelation(ReqCouponGoodsDTO reqCouponGoodsDTO) {
        log.info("MarketProcess-CouponSettingAnalysisServiceImpl-getCouponGoodsRelation-params={}", JSON.toJSONString(reqCouponGoodsDTO));
        AtomReqCouponGoodsDTO atomReqCouponGoodsDTO = BeanCopierUtil.copy(reqCouponGoodsDTO, AtomReqCouponGoodsDTO.class);
        ExecuteDTO<AtomResCouponGoodsDTO> executeDTO = atomCouponSettingAnalysisService.getCouponGoodsRelation(atomReqCouponGoodsDTO);
        log.info("MarketProcess-CouponSettingAnalysisServiceImpl-getCouponGoodsRelation-return={}", JSON.toJSONString(executeDTO));
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResCouponGoodsDTO resCouponGoodsDTO = BeanCopierUtil.copy(executeDTO.getData(), ResCouponGoodsDTO.class);
        if (resCouponGoodsDTO != null && CollectionUtils.isNotEmpty(resCouponGoodsDTO.getResCouponGoodsRelationDTOS())) {
            List<String> goodsNoList = resCouponGoodsDTO.getResCouponGoodsRelationDTOS().stream().map(ResCouponGoodsRelationDTO -> ResCouponGoodsRelationDTO.getGoodsNo()).collect(Collectors.toList());
            ReqGoodsDTO goodsDTO = new ReqGoodsDTO();
            goodsDTO.setNoPage();
            goodsDTO.setGoodsNos(goodsNoList);
            goodsDTO.setIsAuxiliaryUnit(WhetherEnum.YES.getCode());
            ExecuteDTO<ExecutePageDTO<ResGoodsDTO>> goodsExecuteDTO = goodsAnalysisService.getGoodsPage(goodsDTO);
            if (goodsExecuteDTO.successFlag()) {
                List<ResGoodsDTO> goodsList = goodsExecuteDTO.getData().getRows();
                if (CollectionUtils.isNotEmpty(goodsList)) {
                    List<ResGoodsDTO> list = BeanCopierUtil.copyList(goodsList, ResGoodsDTO.class);
                    // 指定云池商品
                    if (CouponUseScopeEnum.COUPON_USE_SCOPE_THREE.getCode().equals(resCouponGoodsDTO.getCouponUseScope())) {
                        this.getStoreNameList(list);
                        //获取非主品集合
                        List<ResGoodsDTO> notParentGoodsNoList = list.stream().filter(goods -> !SeriesTypeEnum.PARENT_GOODS.getCode().equals(goods.getSeriesType())).collect(Collectors.toList());
                        //获取非主品商品库存集合（包括子品和普通）
                        this.getNotSeriesGoodsStockList(notParentGoodsNoList, false);
                        try {
                            this.getCloudPoolPriceAndCommissionList(list);
                        } catch (Exception e) {
                            log.error("查询商品云池价格与佣金列表报错-入参--{}--", list, e);
                        }
                    }//获取主品集合
                    List<ResGoodsDTO> parentGoodsList = list.stream().filter(goods -> SeriesTypeEnum.PARENT_GOODS.getCode().equals(goods.getSeriesType())).collect(Collectors.toList());
                    //获取主品商品库存集合（取子品库存的区间）
                    this.getSeriesGoodsStockList(parentGoodsList, false);
                    // 商品map
                    Map<String, ResGoodsDTO> goodsDTOMap = list.stream().collect(Collectors.toMap(ResGoodsDTO::getGoodsNo, ResGoodsDTO -> ResGoodsDTO));
                    // 店铺指定商品
                    if (CouponUseScopeEnum.COUPON_USE_SCOPE_SIX.getCode().equals(resCouponGoodsDTO.getCouponUseScope())) {
                        ExecuteDTO<List<AtomResAgentMarketRewardsetDTO>> rewardExecuteDTO = atomAgentMarketRewardsetAnalysisService.batchGetRewardSetByTaskNos(goodsNoList);
                        if (rewardExecuteDTO.successFlag()) {
                            List<AtomResAgentMarketRewardsetDTO> rewardsetDTOS = rewardExecuteDTO.getData();
                            if (CollectionUtils.isNotEmpty(rewardsetDTOS)) {
                                // 酬劳
                                Map<String, AtomResAgentMarketRewardsetDTO> rewardsetDTOMap = rewardsetDTOS.stream().collect(Collectors.toMap(AtomResAgentMarketRewardsetDTO::getTaskOrGoodsNo, AtomResAgentMarketRewardsetDTO -> AtomResAgentMarketRewardsetDTO));
                                for (ResCouponGoodsRelationDTO dto : resCouponGoodsDTO.getResCouponGoodsRelationDTOS()) {
                                    AtomResAgentMarketRewardsetDTO rewardsetDTO = rewardsetDTOMap.get(dto.getGoodsNo());
                                    if (rewardsetDTO != null) {
                                        dto.setRewardType(rewardsetDTO.getRewardType());
                                        dto.setYjOrHjb(rewardsetDTO.getYjOrHjb());
                                    }
                                }
                            }
                        }
                    }
                    //指定商品（商家或者店铺）
                    if (CouponUseScopeEnum.COUPON_USE_SCOPE_SIX.getCode().equals(resCouponGoodsDTO.getCouponUseScope()) || CouponUseScopeEnum.COUPON_USE_SCOPE_NINE.getCode().equals(resCouponGoodsDTO.getCouponUseScope())) {
                        for (ResCouponGoodsRelationDTO dto : resCouponGoodsDTO.getResCouponGoodsRelationDTOS()) {
                            ResGoodsDTO resGoodsDTO = goodsDTOMap.get(dto.getGoodsNo());
                            setResCouponGoodsRelation(dto, resGoodsDTO);
                        }
                    }
                    // 赋值
                    // 指定云池商品
                    if (CouponUseScopeEnum.COUPON_USE_SCOPE_THREE.getCode().equals(resCouponGoodsDTO.getCouponUseScope())) {
                        for (ResCouponGoodsRelationDTO dto : resCouponGoodsDTO.getResCouponGoodsRelationDTOS()) {
                            ResGoodsDTO resGoodsDTO = goodsDTOMap.get(dto.getGoodsNo());
                            if (resGoodsDTO != null) {
                                // 商品形式 1001-普通商品;1002-系列商品;1003-称重商品
                                dto.setGoodsForm(resGoodsDTO.getGoodsForm());
                                // 系列商品类型 1001:主品 1002:子品
                                dto.setSeriesType(resGoodsDTO.getSeriesType());
                                // 云池供货价
                                dto.setCloudPoolSupplyPrice(resGoodsDTO.getCloudPoolSupplyPrice());
                                dto.setMinCloudPoolSupplyPrice(resGoodsDTO.getMinCloudPoolSupplyPrice());
                                dto.setMaxCloudPoolSupplyPrice(resGoodsDTO.getMaxCloudPoolSupplyPrice());
                                // 代理人佣金
                                dto.setAgentCommission(resGoodsDTO.getAgentCommission());
                                dto.setMinAgentCommission(resGoodsDTO.getMinAgentCommission());
                                dto.setMaxAgentCommission(resGoodsDTO.getMaxAgentCommission());
                                // 分销店佣金
                                dto.setDistributionStoreCommission(resGoodsDTO.getDistributionStoreCommission());
                                dto.setMinDistributionStoreCommission(resGoodsDTO.getMinDistributionStoreCommission());
                                dto.setMaxDistributionStoreCommission(resGoodsDTO.getMaxDistributionStoreCommission());
                                // 平台服务费
                                dto.setPlatformServiceCommission(resGoodsDTO.getPlatformServiceCommission());
                                dto.setMinPlatformServiceCommission(resGoodsDTO.getMinPlatformServiceCommission());
                                dto.setMaxPlatformServiceCommission(resGoodsDTO.getMaxPlatformServiceCommission());
                                // 云池零售价
                                dto.setCloudPoolRetailPrice(resGoodsDTO.getCloudPoolRetailPrice());
                                dto.setMinCloudPoolRetailPrice(resGoodsDTO.getMinCloudPoolRetailPrice());
                                dto.setMaxCloudPoolRetailPrice(resGoodsDTO.getMaxCloudPoolRetailPrice());
                                // 云池市场价
                                dto.setCloudPoolMarketPrice(resGoodsDTO.getCloudPoolMarketPrice());
                                dto.setMinCloudPoolMarketPrice(resGoodsDTO.getMinCloudPoolMarketPrice());
                                dto.setMaxCloudPoolMarketPrice(resGoodsDTO.getMaxCloudPoolMarketPrice());
                                // 店铺
                                dto.setStoreNo(resGoodsDTO.getStoreNo());
                                dto.setStoreName(resGoodsDTO.getStoreName());
                                // 主图 库存
                                dto.setMainPictureUrl(resGoodsDTO.getMainPictureUrl());
                                dto.setCanSaleStockNum(resGoodsDTO.getCanSaleStockNum());
                                dto.setSalesVolume(resGoodsDTO.getSalesVolume());
                                dto.setGoodsForm(resGoodsDTO.getGoodsForm());
                            }
                        }
                    }
                }
            }
        }
        return ExecuteDTO.success(resCouponGoodsDTO);
    }

    /**
     * 组装商品信息
     * @param dto
     * @param resGoodsDTO
     */
    private void setResCouponGoodsRelation(ResCouponGoodsRelationDTO dto, ResGoodsDTO resGoodsDTO){
        if (resGoodsDTO != null) {
            dto.setRetailPrice(resGoodsDTO.getRetailPrice());
            dto.setMinRetailPrice(resGoodsDTO.getMinRetailPrice());
            dto.setMaxRetailPrice(resGoodsDTO.getMaxRetailPrice());
            dto.setMainPictureUrl(resGoodsDTO.getMainPictureUrl());
            dto.setCanSaleStockNum(resGoodsDTO.getCanSaleStockNum());
            dto.setSalesVolume(resGoodsDTO.getSalesVolume());
            dto.setGoodsForm(resGoodsDTO.getGoodsForm());
            dto.setGoodsFormValue(resGoodsDTO.getGoodsFormValue());
            dto.setFirstAttributeName(resGoodsDTO.getFirstAttributeName());
            dto.setFirstAttributeValueName(resGoodsDTO.getFirstAttributeValueName());
            dto.setSecondAttributeName(resGoodsDTO.getSecondAttributeName());
            dto.setSecondAttributeValueName(resGoodsDTO.getSecondAttributeValueName());
            dto.setThirdAttributeName(resGoodsDTO.getThirdAttributeName());
            dto.setThirdAttributeValueName(resGoodsDTO.getThirdAttributeValueName());
            String attributeNames = (StringUtils.isNotEmpty(resGoodsDTO.getFirstAttributeValueName()) ? resGoodsDTO.getFirstAttributeValueName() + "," : StringUtils.EMPTY)
                    + (StringUtils.isNotEmpty(resGoodsDTO.getSecondAttributeValueName()) ? resGoodsDTO.getSecondAttributeValueName() + "," : StringUtils.EMPTY)
                    + (StringUtils.isNotEmpty(resGoodsDTO.getThirdAttributeValueName()) ? resGoodsDTO.getThirdAttributeValueName() + "," : StringUtils.EMPTY);
            if(StringUtils.isNotEmpty(attributeNames)){
                dto.setAttributeNames(attributeNames.substring(NumConstant.ZERO, attributeNames.lastIndexOf(",")));
            }
            dto.setMultiUnitType(resGoodsDTO.getMultiUnitType());
            dto.setMultiUnitGoodsNo(resGoodsDTO.getMultiUnitGoodsNo());
        }
    }

    @Override
    public ExecuteDTO<List<ResCouponApplyListDTO>> getCouponSettingList(ReqCouponSettingDTO reqCouponSettingDTO) {
        log.info("-CouponSettingAnalysisServiceImpl-getCouponSettingList-param={}", JSON.toJSONString(reqCouponSettingDTO));
        AtomReqCouponSettingDTO atomReqCouponSettingDTO = BeanCopierUtil.copy(reqCouponSettingDTO, AtomReqCouponSettingDTO.class);
        ExecuteDTO<List<AtomResCouponSettingDTO>> executeDTO = atomCouponSettingAnalysisService.getCouponSettingList(atomReqCouponSettingDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        List<AtomResCouponSettingDTO> couponSettingList = executeDTO.getData();
        List<ResCouponApplyListDTO> couponApplyListDTOS = BeanCopierUtil.copyList(couponSettingList, ResCouponApplyListDTO.class);
        log.info("-CouponSettingAnalysisServiceImpl-getCouponSettingList-checkCoupon处理前返回-return={}", JSON.toJSONString(couponApplyListDTOS));
        // 校验剔除用户不符合的 和 券不符合的
        ReqStoreCouponListDTO storeCouponListDTO = BeanCopierUtil.copy(reqCouponSettingDTO, ReqStoreCouponListDTO.class);
        this.couponPromotionAnalysisService.checkCoupon(storeCouponListDTO, couponApplyListDTOS);
        log.info("-CouponSettingAnalysisServiceImpl-getCouponSettingList-checkCoupon处理完返回-return={}", JSON.toJSONString(couponApplyListDTOS));
        return ExecuteDTO.success(couponApplyListDTOS);
    }

    @Override
    public ExecuteDTO checkCouponSettingCanSale(ReqCouponSettingDTO reqCouponSettingDTO) {
        log.info("MarketProcess-CouponSettingAnalysisServiceImpl-checkCouponSettingCanSale-params-start");
        log.info("MarketProcess-CouponSettingAnalysisServiceImpl-checkCouponSettingCanSale-params={}", JSON.toJSONString(reqCouponSettingDTO));
        ExecuteDTO checkExecuteDTO = couponSettingAssert.checkCouponSettingCanSaleAssert(reqCouponSettingDTO);
        if(!checkExecuteDTO.successFlag()) {
            return checkExecuteDTO;
        }
        AtomReqCouponSettingDTO atomReqCouponSettingDTO = BeanCopierUtil.copy(reqCouponSettingDTO, AtomReqCouponSettingDTO.class);
        ExecuteDTO<AtomResCouponSettingDTO> executeDTO = atomCouponSettingAnalysisService.getCouponSetting(atomReqCouponSettingDTO);
        log.info("MarketProcess-CouponSettingAnalysisServiceImpl-checkCouponSettingCanSale-return={}", JSON.toJSONString(executeDTO));
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        AtomResCouponSettingDTO couponSettingDTO = executeDTO.getData();
        // 活动不存在
        if(couponSettingDTO == null) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000600);
        }
        // 抱歉，活动已下架
        if(couponSettingDTO.getUpDownFlag() != null && couponSettingDTO.getUpDownFlag() == NumConstant.ONE) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000700);
        }
        ResCouponSettingDTO resCouponSettingDTO = BeanCopierUtil.copy(couponSettingDTO, ResCouponSettingDTO.class);
        // 设置活动状态
        this.setOneCouponStatus(resCouponSettingDTO);
        // 券已过期，不支持购买
        if(PromotionStatusEnum.EXPIRED.getCode().equals(resCouponSettingDTO.getStatus())) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17001003);
        }
        Integer totalNum = resCouponSettingDTO.getTotalNum() == null ? NumConstant.ZERO : resCouponSettingDTO.getTotalNum();
        Integer remainNum = resCouponSettingDTO.getRemainNum() == null ? NumConstant.ZERO : resCouponSettingDTO.getRemainNum();
        // 代金券已售罄
        if(remainNum <= NumConstant.ZERO) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17001000);
        }
        // 每人限购数校验
        // 若购买数量>代金券可售数量，则提示：“可售数量不足，请重新选购数量”
        if(reqCouponSettingDTO.getGoodsNum() > remainNum) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17001001);
        }
        // 获取当前粉丝已购买数量
        CouponUserRecordVO couponUserRecordVO = new CouponUserRecordVO();
        couponUserRecordVO.setFanNo(reqCouponSettingDTO.getFanNo());
        couponUserRecordVO.setCouponNo(reqCouponSettingDTO.getCouponNo());
        couponUserRecordVO.setStoreNo(reqCouponSettingDTO.getStoreNo());
        int count = couponUserRecordDao.selectCouponCountByTerm(couponUserRecordVO);
        // 获取商品编号
        PromotionVirtualGoodsRelationVo queryVo = new PromotionVirtualGoodsRelationVo();
        queryVo.setPromotionNo(reqCouponSettingDTO.getCouponNo());
        PromotionVirtualGoodsRelationDomain goodsRelationDomain = this.promotionVirtualGoodsRelationDao.selectOneByParams(queryVo);
        if(goodsRelationDomain == null) {
            return ExecuteDTO.error(GoodsErrorCode.CODE_12000001.getCode(), GoodsErrorCode.CODE_12000001.getInMsg());
        }
        // 获取当前粉丝购买但未支付的数量
        ReqOrderItemDTO reqOrderItemDTO = new ReqOrderItemDTO();
        reqOrderItemDTO.setGoodsNo(goodsRelationDomain.getGoodsNo());
        reqOrderItemDTO.setBuyerNo(reqCouponSettingDTO.getFanNo());
        ExecuteDTO<Integer> countExecuteDTO = this.orderAnalysisService.getVirtualGoodsSaleNum(reqOrderItemDTO);
        if(!countExecuteDTO.successFlag()) {
            return ExecuteDTO.error(GoodsErrorCode.CODE_12000001.getCode(), "订单"+GoodsErrorCode.CODE_12000001.getInMsg());
        }
        Integer orderCount = countExecuteDTO.getData()==null ? NumConstant.ZERO : countExecuteDTO.getData();
        log.info("MarketProcess-CouponSettingAnalysisServiceImpl-checkCouponSettingCanSale-count={}", count);
        log.info("MarketProcess-CouponSettingAnalysisServiceImpl-checkCouponSettingCanSale-orderCount={}", orderCount);
        // 若购买数量>剩余限购数，则提示：“已超限购数量，你最多还可买X个”（X取值当前粉丝账户剩余可购数量）
        int userTotalNum = resCouponSettingDTO.getUserTotalNum() == null ? NumConstant.ZERO : resCouponSettingDTO.getUserTotalNum();
        int maxBuyNum = userTotalNum - count - orderCount;
        if(reqCouponSettingDTO.getGoodsNum() > maxBuyNum) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17001002.getCode(), String.format(MarketErrorCode.CODE_17001002.getInMsg(), (maxBuyNum<NumConstant.ZERO ? NumConstant.ZERO : maxBuyNum)));
        }
        log.info("MarketProcess-CouponSettingAnalysisServiceImpl-checkCouponSettingCanSale-end");
        return ExecuteDTO.success(resCouponSettingDTO);
    }

    @Override
    public ExecuteDTO checkCouponSettingValid(ReqCouponSettingDTO reqCouponSettingDTO) {
        log.info("MarketProcess-CouponSettingAnalysisServiceImpl-checkCouponSettingCanSale-params-start");
        log.info("MarketProcess-CouponSettingAnalysisServiceImpl-checkCouponSettingCanSale-params={}", JSON.toJSONString(reqCouponSettingDTO));
        AtomReqCouponSettingDTO atomReqCouponSettingDTO = BeanCopierUtil.copy(reqCouponSettingDTO, AtomReqCouponSettingDTO.class);
        ExecuteDTO<AtomResCouponSettingDTO> executeDTO = atomCouponSettingAnalysisService.getCouponSetting(atomReqCouponSettingDTO);
        log.info("MarketProcess-CouponSettingAnalysisServiceImpl-checkCouponSettingCanSale-return={}", JSON.toJSONString(executeDTO));
        log.info("MarketProcess-CouponSettingAnalysisServiceImpl-checkCouponSettingCanSale-end");
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        AtomResCouponSettingDTO couponSettingDTO = executeDTO.getData();
        // 活动不存在
        if(couponSettingDTO == null) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000600);
        }
        ResCouponSettingDTO resCouponSettingDTO = BeanCopierUtil.copy(couponSettingDTO, ResCouponSettingDTO.class);
        // 设置活动状态
        this.setOneCouponStatus(resCouponSettingDTO);
        // 券已过期，不支持购买
        if(PromotionStatusEnum.EXPIRED.getCode().equals(resCouponSettingDTO.getStatus())) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17001003);
        }
        return ExecuteDTO.success(resCouponSettingDTO);
    }

    private void getStoreNameList(List<ResGoodsDTO> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            //获取有店铺编码商品
            List<ResGoodsDTO> storeGoodsDTOList = list.stream().filter(goodsDTO -> StringUtils.isNotBlank(goodsDTO.getStoreNo())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(storeGoodsDTOList)) {
                List<String> storeNoList = storeGoodsDTOList.stream().map(ResGoodsDTO::getStoreNo).collect(Collectors.toList());
                //获取店铺集合
                ExecuteDTO<List<GetStoreListResponse>> storeDTO = userPublicService.queryStoreList(storeNoList);
                if (storeDTO.successFlag() && CollectionUtils.isNotEmpty(storeDTO.getData())) {
                    storeGoodsDTOList.forEach(resDTO -> {
                        Optional<GetStoreListResponse> optional = storeDTO.getData().stream().filter(store -> resDTO.getStoreNo().equals(store.getStoreNo())).findFirst();
                        optional.ifPresent(getStoreListResponse -> resDTO.setStoreName(getStoreListResponse.getStoreName()));
                    });
                }
            }
        }
    }

    private void setCouponStatus(List<ResCouponSettingDTO> resCouponSettingDTOs) {
        if (CollectionUtils.isNotEmpty(resCouponSettingDTOs)) {
            resCouponSettingDTOs.forEach(ruleDto -> {
                // 草稿状态
                if (StringUtils.isNotBlank(ruleDto.getStatus()) && PromotionStatusEnum.ONLY_SAVED.getCode().equals(ruleDto.getStatus())) {
                    ruleDto.setStatus(PromotionStatusEnum.ONLY_SAVED.getCode());
                    // 活动有效期（1001：长期有效 1002：指定日期）
                } else if (EffectivePeriodTypeEnum.EFFECTIVE_PERIOD_TYPE_ONE.getCode().equals(ruleDto.getPeriodValidity())) {
                    ruleDto.setStatus(PromotionStatusEnum.IN_PROGRESS.getCode());
                    // 其他状态
                } else if (EffectivePeriodTypeEnum.EFFECTIVE_PERIOD_TYPE_TWO.getCode().equals(ruleDto.getPeriodValidity())
                        && ruleDto.getEffectiveTime() != null && ruleDto.getInvalidTime() != null) {
                    //未开始
                    if (ruleDto.getEffectiveTime().isAfter(LocalDateTime.now())) {
                        ruleDto.setStatus(PromotionStatusEnum.NOT_STARTED.getCode());
                        //进行中
                    } else if ((ruleDto.getEffectiveTime().isBefore(LocalDateTime.now()) || ruleDto.getEffectiveTime().equals(LocalDateTime.now()))
                            && (ruleDto.getInvalidTime().equals(LocalDateTime.now()) || ruleDto.getInvalidTime().isAfter(LocalDateTime.now()))) {
                        ruleDto.setStatus(PromotionStatusEnum.IN_PROGRESS.getCode());
                        //已结束
                    } else if (ruleDto.getInvalidTime().isBefore(LocalDateTime.now())) {
                        ruleDto.setStatus(PromotionStatusEnum.EXPIRED.getCode());
                    }
                }
            });
        }
    }

    private void setOneCouponStatus(ResCouponSettingDTO dto) {
        if (dto != null) {
            // 草稿状态
            if (StringUtils.isNotBlank(dto.getStatus()) && PromotionStatusEnum.ONLY_SAVED.getCode().equals(dto.getStatus())) {
                dto.setStatus(PromotionStatusEnum.ONLY_SAVED.getCode());
                // 活动有效期（1001：长期有效 1002：指定日期）
            } else if (EffectivePeriodTypeEnum.EFFECTIVE_PERIOD_TYPE_ONE.getCode().equals(dto.getPeriodValidity())) {
                dto.setStatus(PromotionStatusEnum.IN_PROGRESS.getCode());
                // 其他状态
            } else if (EffectivePeriodTypeEnum.EFFECTIVE_PERIOD_TYPE_TWO.getCode().equals(dto.getPeriodValidity())
                    && dto.getEffectiveTime() != null && dto.getInvalidTime() != null) {
                //未开始
                if (dto.getEffectiveTime().isAfter(LocalDateTime.now())) {
                    dto.setStatus(PromotionStatusEnum.NOT_STARTED.getCode());
                    //进行中
                } else if ((dto.getEffectiveTime().isBefore(LocalDateTime.now()) || dto.getEffectiveTime().equals(LocalDateTime.now()))
                        && (dto.getInvalidTime().equals(LocalDateTime.now()) || dto.getInvalidTime().isAfter(LocalDateTime.now()))) {
                    dto.setStatus(PromotionStatusEnum.IN_PROGRESS.getCode());
                    //已结束
                } else if (dto.getInvalidTime().isBefore(LocalDateTime.now())) {
                    dto.setStatus(PromotionStatusEnum.EXPIRED.getCode());
                }
            }
        }
    }

    /**
     * 获取非主品商品库存集合（包括子品和普通）
     *
     * @param goodsDTOList
     * @param warehouseFlag true 查询仓库，false 不查询仓库
     */
    public void getNotSeriesGoodsStockList(List<ResGoodsDTO> goodsDTOList, Boolean warehouseFlag) {
        try {
            if (CollectionUtils.isNotEmpty(goodsDTOList)) {
                List<String> goodsNoList = goodsDTOList.stream().map(ResGoodsDTO::getOriginalGoodsNo).collect(Collectors.toList());
                AtomReqGoodsRealStockDTO realStockDto = new AtomReqGoodsRealStockDTO();
                realStockDto.setGoodsNoList(goodsNoList);
                ExecuteDTO<List<AtomResGoodsRealStockDTO>> notSeriesExecuteDTO = legacyGoodsCenterService.getBatchGoodsRealStockByNos(realStockDto);
                if (notSeriesExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(notSeriesExecuteDTO.getData())) {
                    goodsDTOList.forEach(subDTO -> {
                        //查询商品库存到商品对象信息里面
                        Optional<AtomResGoodsRealStockDTO> realStockDTO = notSeriesExecuteDTO.getData().stream().filter(resStockDTO ->
                                StringUtils.equals(resStockDTO.getGoodsNo(), subDTO.getOriginalGoodsNo())).findFirst();
                        if (realStockDTO.isPresent()) {
                            subDTO.setRealStockNum(realStockDTO.get().getRealStockNum());
                            subDTO.setCanSaleStockNum(realStockDTO.get().getAvailableStockNum());
                        }
                    });
                    //查询仓库名
                    if (warehouseFlag) {
//                        this.getWarehouseNameList(goodsDTOList);
                    }
                }
            }
        } catch (Exception e) {
            log.error("查询非主品商品列表库存报错-入参--{}--{}--", goodsDTOList, warehouseFlag, e);
        }
    }

    /**
     * 获取主品商品库存集合（取子品库存的区间）
     *
     * @param goodsDTOList
     * @param warehouseFlag true 查询仓库，false 不查询仓库
     */
    public void getSeriesGoodsStockList(List<ResGoodsDTO> goodsDTOList, Boolean warehouseFlag) {
        try {
            if (CollectionUtils.isNotEmpty(goodsDTOList)) {
                AtomReqGoodsDTO atomReqGoodsDTO = new AtomReqGoodsDTO();
                atomReqGoodsDTO.setSeriesType(SeriesTypeEnum.SUB_GOODS.getCode());
                atomReqGoodsDTO.setParentGoodsNos(goodsDTOList.stream().map(ResGoodsDTO::getGoodsNo).collect(Collectors.toList()));
                atomReqGoodsDTO.setGoodsForm(GoodsFormEnum.SERIES_GOODS.getCode());
                //不分页
                atomReqGoodsDTO.setNoPage();
                //获取子品列表
                ExecuteDTO<ExecutePageDTO<AtomResGoodsDTO>> goodsDTO = legacyGoodsCenterService.getGoodsPage(atomReqGoodsDTO);
                if (goodsDTO.successFlag() && CollectionUtils.isNotEmpty(goodsDTO.getData().getRows())) {
                    List<String> goodsNoList = goodsDTO.getData().getRows().stream().map(AtomResGoodsDTO::getOriginalGoodsNo).collect(Collectors.toList());
                    //有子品再判断是否维护过库存
                    AtomReqGoodsRealStockDTO realStockDto = new AtomReqGoodsRealStockDTO();
                    realStockDto.setGoodsNoList(goodsNoList);
                    ExecuteDTO<List<AtomResGoodsRealStockDTO>> seriesExecuteDTO = legacyGoodsCenterService.getBatchGoodsRealStockByNos(realStockDto);
                    goodsDTOList.forEach(subDTO -> {
                        //通过原始商品编码匹配出该主品下的子品
                        List<AtomResGoodsDTO> goodsList = goodsDTO.getData().getRows().stream().filter(goods ->
                                StringUtils.equals(goods.getParentGoodsNo(), subDTO.getGoodsNo())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(goodsList)) {
                            //获取最小市场价
                            Optional<AtomResGoodsDTO> minMarketPrice = goodsList.stream().min(Comparator.comparing(AtomResGoodsDTO::getMarketPrice));
                            minMarketPrice.ifPresent(atomResGoodsDTO -> subDTO.setMinMarketPrice(atomResGoodsDTO.getMarketPrice()));
                            //获取最大市场价
                            Optional<AtomResGoodsDTO> maxMarketPrice = goodsList.stream().max(Comparator.comparing(AtomResGoodsDTO::getMarketPrice));
                            maxMarketPrice.ifPresent(atomResGoodsDTO -> subDTO.setMaxMarketPrice(atomResGoodsDTO.getMarketPrice()));
                            //获取最小采购价
                            Optional<AtomResGoodsDTO> minPurchasePrice = goodsList.stream().min(Comparator.comparing(AtomResGoodsDTO::getPurchasePrice));
                            minPurchasePrice.ifPresent(atomResGoodsDTO -> subDTO.setMinPurchasePrice(atomResGoodsDTO.getPurchasePrice()));
                            //获取最大采购价
                            Optional<AtomResGoodsDTO> maxPurchasePrice = goodsList.stream().max(Comparator.comparing(AtomResGoodsDTO::getPurchasePrice));
                            maxPurchasePrice.ifPresent(atomResGoodsDTO -> subDTO.setMaxPurchasePrice(atomResGoodsDTO.getPurchasePrice()));
                            //获取最小零售价
                            Optional<AtomResGoodsDTO> minRetailPrice = goodsList.stream().min(Comparator.comparing(AtomResGoodsDTO::getRetailPrice));
                            minRetailPrice.ifPresent(atomResGoodsDTO -> subDTO.setMinRetailPrice(atomResGoodsDTO.getRetailPrice()));
                            //获取最大零售价
                            Optional<AtomResGoodsDTO> maxRetailPrice = goodsList.stream().max(Comparator.comparing(AtomResGoodsDTO::getRetailPrice));
                            maxRetailPrice.ifPresent(atomResGoodsDTO -> subDTO.setMaxRetailPrice(atomResGoodsDTO.getRetailPrice()));
                            //获取最小云池供货价
                            Optional<AtomResGoodsDTO> minCloudPoolSupplyPrice = goodsList.stream().min(Comparator.comparing(AtomResGoodsDTO::getCloudPoolSupplyPrice));
                            minCloudPoolSupplyPrice.ifPresent(atomResGoodsDTO -> subDTO.setMinCloudPoolSupplyPrice(atomResGoodsDTO.getCloudPoolSupplyPrice()));
                            //获取最大云池供货价
                            Optional<AtomResGoodsDTO> maxCloudPoolSupplyPrice = goodsList.stream().max(Comparator.comparing(AtomResGoodsDTO::getCloudPoolSupplyPrice));
                            maxCloudPoolSupplyPrice.ifPresent(atomResGoodsDTO -> subDTO.setMaxCloudPoolSupplyPrice(atomResGoodsDTO.getCloudPoolSupplyPrice()));
                            //销量总数
                            /*
                            subDTO.setSalesVolume(goodsList.stream().map(AtomResGoodsDTO::getSalesVolume).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
                            */
                            //将子品的信息赋值到列表子品下
                            List<ResSubGoodsDTO> subGoodsDTOS = new ArrayList<>();
                            goodsList.forEach(subGoodsDTO -> {
                                ResSubGoodsDTO resSubGoodsDTO = BeanCopierUtil.copy(subGoodsDTO, ResSubGoodsDTO.class);
                                if (seriesExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(seriesExecuteDTO.getData())) {
                                    //查询商品库存到商品对象信息里面,通过子品原始商品编码匹配
                                    Optional<AtomResGoodsRealStockDTO> stockOptional = seriesExecuteDTO.getData().stream().filter(resStockDTO ->
                                            StringUtils.equals(resStockDTO.getGoodsNo(), subGoodsDTO.getOriginalGoodsNo())).findFirst();
                                    if (stockOptional.isPresent()) {
                                        //获取总和
                                        resSubGoodsDTO.setRealStockNum(stockOptional.get().getRealStockNum());
                                        resSubGoodsDTO.setCanSaleStockNum(stockOptional.get().getAvailableStockNum());
                                        if (subDTO.getRealStockNum() != null) {
                                            subDTO.setRealStockNum(subDTO.getRealStockNum().add(stockOptional.get().getRealStockNum()));
                                        } else {
                                            subDTO.setRealStockNum(stockOptional.get().getRealStockNum());
                                        }
                                        if (subDTO.getCanSaleStockNum() != null) {
                                            subDTO.setCanSaleStockNum(subDTO.getCanSaleStockNum().add(stockOptional.get().getAvailableStockNum()));
                                        } else {
                                            subDTO.setCanSaleStockNum(stockOptional.get().getAvailableStockNum());
                                        }
                                    }
                                }
                                subGoodsDTOS.add(resSubGoodsDTO);
                            });
                            ResSeriesGoodsDTO seriesGoodsDTO = new ResSeriesGoodsDTO();
                            seriesGoodsDTO.setSubGoodsList(subGoodsDTOS);
                            subDTO.setSeriesGoods(seriesGoodsDTO);
                        }
                    });
                    //查询仓库名
                    if (warehouseFlag) {
//                        this.getWarehouseNameList(goodsDTOList);
                    }
                }
            }
        } catch (Exception e) {
            log.error("查询主品商品列表库存报错-入参--{}--{}--", goodsDTOList, warehouseFlag, e);
        }
    }

    /**
     * 获取云池商品佣金
     *
     * @param goodsDTOList 商品集合
     */
    public void getCloudPoolPriceAndCommissionList(List<ResGoodsDTO> goodsDTOList) throws Exception {
        try {
            if (org.apache.dubbo.common.utils.CollectionUtils.isEmpty(goodsDTOList)) {
                //商品集合为空
                return;
            }

            AtomReqGoodsDTO atomReqGoodsDTO = new AtomReqGoodsDTO();
            atomReqGoodsDTO.setSeriesType(SeriesTypeEnum.SUB_GOODS.getCode());
            atomReqGoodsDTO.setParentGoodsNos(goodsDTOList.stream().map(ResGoodsDTO::getGoodsNo).collect(Collectors.toList()));
            atomReqGoodsDTO.setGoodsForm(GoodsFormEnum.SERIES_GOODS.getCode());
            //不分页
            atomReqGoodsDTO.setNoPage();
            //获取子品列表
            ExecuteDTO<ExecutePageDTO<AtomResGoodsDTO>> goodsDTO = legacyGoodsCenterService.getGoodsPage(atomReqGoodsDTO);

            //取出商品编号
            List<String> goodsNos = goodsDTOList.stream().map(ResGoodsDTO::getGoodsNo).collect(Collectors.toList());

            //获取云池商品佣金
            AtomReqCloudPoolGoodsCommissionConfigDTO atomReqCloudPoolGoodsCommissionConfigDTO = new AtomReqCloudPoolGoodsCommissionConfigDTO();
            atomReqCloudPoolGoodsCommissionConfigDTO.setParentGoodsNoList(goodsNos);
            List<AtomResCloudPoolGoodsCommissionConfigDTO> cloudPoolGoodsCommissionConfigDTOS = this.getCloudPoolGoodsCommissionList(atomReqCloudPoolGoodsCommissionConfigDTO);
            if (org.apache.dubbo.common.utils.CollectionUtils.isEmpty(cloudPoolGoodsCommissionConfigDTOS)) {
                //云池商品佣金集合为空
                return;
            }
            goodsDTOList.forEach(resGoodsDTO -> {
                if (GoodsFormEnum.SERIES_GOODS.getCode().equals(resGoodsDTO.getGoodsForm())) {
                    //系列商品
                    List<AtomResCloudPoolGoodsCommissionConfigDTO> commissionConfigDTOList = cloudPoolGoodsCommissionConfigDTOS.stream()
                            .filter(commissionConfigDTO -> StringUtils.equals(commissionConfigDTO.getParentGoodsNo(), resGoodsDTO.getGoodsNo()))
                            .collect(Collectors.toList());
                    if (org.apache.dubbo.common.utils.CollectionUtils.isNotEmpty(commissionConfigDTOList)) {
                        //获取最小云池市场价
                        Optional<AtomResCloudPoolGoodsCommissionConfigDTO> minCloudPoolMarketPrice = commissionConfigDTOList.stream()
                                .min(Comparator.comparing(AtomResCloudPoolGoodsCommissionConfigDTO::getMarketPrice));
                        minCloudPoolMarketPrice.ifPresent(atomResCloudPoolGoodsCommissionConfigDTO -> resGoodsDTO.setMinCloudPoolMarketPrice(
                                atomResCloudPoolGoodsCommissionConfigDTO.getMarketPrice()));
                        //获取最大云池市场价
                        Optional<AtomResCloudPoolGoodsCommissionConfigDTO> maxCloudPoolMarketPrice = commissionConfigDTOList.stream()
                                .max(Comparator.comparing(AtomResCloudPoolGoodsCommissionConfigDTO::getMarketPrice));
                        maxCloudPoolMarketPrice.ifPresent(atomResCloudPoolGoodsCommissionConfigDTO -> resGoodsDTO.setMaxCloudPoolMarketPrice(
                                atomResCloudPoolGoodsCommissionConfigDTO.getMarketPrice()));
                        //获取最小云池零售价
                        Optional<AtomResCloudPoolGoodsCommissionConfigDTO> minCloudPoolRetailPrice = commissionConfigDTOList.stream()
                                .min(Comparator.comparing(AtomResCloudPoolGoodsCommissionConfigDTO::getRetailPrice));
                        minCloudPoolRetailPrice.ifPresent(atomResCloudPoolGoodsCommissionConfigDTO -> resGoodsDTO.setMinCloudPoolRetailPrice(
                                atomResCloudPoolGoodsCommissionConfigDTO.getRetailPrice()));
                        //获取最大云池零售价
                        Optional<AtomResCloudPoolGoodsCommissionConfigDTO> maxCloudPoolRetailPrice = commissionConfigDTOList.stream()
                                .max(Comparator.comparing(AtomResCloudPoolGoodsCommissionConfigDTO::getRetailPrice));
                        maxCloudPoolRetailPrice.ifPresent(atomResCloudPoolGoodsCommissionConfigDTO -> resGoodsDTO.setMaxCloudPoolRetailPrice(
                                atomResCloudPoolGoodsCommissionConfigDTO.getRetailPrice()));

                        //最小代理人佣金比例（%）
                        Optional<AtomResCloudPoolGoodsCommissionConfigDTO> minAgentCommissionRatio = commissionConfigDTOList.stream()
                                .min(Comparator.comparing(AtomResCloudPoolGoodsCommissionConfigDTO::getAgentCommissionRatio));
                        minAgentCommissionRatio.ifPresent(atomResCloudPoolGoodsCommissionConfigDTO -> resGoodsDTO.setMinAgentCommissionRatio(
                                atomResCloudPoolGoodsCommissionConfigDTO.getAgentCommissionRatio()));

                        //最大代理人佣金比例（%）
                        Optional<AtomResCloudPoolGoodsCommissionConfigDTO> maxAgentCommissionRatio = commissionConfigDTOList.stream()
                                .max(Comparator.comparing(AtomResCloudPoolGoodsCommissionConfigDTO::getAgentCommissionRatio));
                        maxAgentCommissionRatio.ifPresent(atomResCloudPoolGoodsCommissionConfigDTO -> resGoodsDTO.setMaxAgentCommissionRatio(
                                atomResCloudPoolGoodsCommissionConfigDTO.getAgentCommissionRatio()));

                        //最小分销店佣金比例（%）
                        Optional<AtomResCloudPoolGoodsCommissionConfigDTO> minDistributionStoreCommissionRatio = commissionConfigDTOList.stream()
                                .min(Comparator.comparing(AtomResCloudPoolGoodsCommissionConfigDTO::getDistributionStoreCommissionRatio));
                        minDistributionStoreCommissionRatio.ifPresent(atomResCloudPoolGoodsCommissionConfigDTO -> resGoodsDTO.setMinDistributionStoreCommissionRatio(
                                atomResCloudPoolGoodsCommissionConfigDTO.getDistributionStoreCommissionRatio()));

                        //最大分销店佣金比例（%）
                        Optional<AtomResCloudPoolGoodsCommissionConfigDTO> maxDistributionStoreCommissionRatio = commissionConfigDTOList.stream()
                                .max(Comparator.comparing(AtomResCloudPoolGoodsCommissionConfigDTO::getDistributionStoreCommissionRatio));
                        maxDistributionStoreCommissionRatio.ifPresent(atomResCloudPoolGoodsCommissionConfigDTO -> resGoodsDTO.setMaxDistributionStoreCommissionRatio(
                                atomResCloudPoolGoodsCommissionConfigDTO.getDistributionStoreCommissionRatio()));

                        //最小代理人佣金
                        BigDecimal minAgentCommission = null;
                        //最大代理人佣金
                        BigDecimal maxAgentCommission = null;
                        //最小分销店佣金
                        BigDecimal minDistributionStoreCommission = null;
                        //最大分销店佣金
                        BigDecimal maxDistributionStoreCommission = null;
                        //最小平台服务费
                        BigDecimal minPlatformServiceCommission = null;
                        //最大平台服务费
                        BigDecimal maxPlatformServiceCommission = null;
                        for (AtomResCloudPoolGoodsCommissionConfigDTO commissionConfigDTO : commissionConfigDTOList) {
                            if (!goodsDTO.successFlag() || org.apache.dubbo.common.utils.CollectionUtils.isEmpty(goodsDTO.getData().getRows())) {
                                //无系列商品子品信息，直接退出云池
                                break;
                            }
                            List<AtomResGoodsDTO> atomResGoodsDTOList = goodsDTO.getData().getRows();

                            Optional<AtomResGoodsDTO> optional = atomResGoodsDTOList.stream().filter(atomResGoodsDTO -> StringUtils.equals(atomResGoodsDTO.getGoodsNo(), commissionConfigDTO.getGoodsNo())).findFirst();
                            if (!optional.isPresent()) {
                                //未找到当前子品的子品基础信息，退出当前循环
                                continue;
                            }
                            //毛利润
                            BigDecimal grossProfit = CloudPoolCommissionCalculationUtil.getGrossProfit(
                                    optional.get().getCloudPoolSupplyPrice(),
                                    commissionConfigDTO.getRetailPrice(),
                                    commissionConfigDTO.getActivityExpenses()
                            );
                            //计算代理人佣金
                            BigDecimal agentCommission = CloudPoolCommissionCalculationUtil.getCommission(commissionConfigDTO.getAgentCommissionRatio(), grossProfit);
                            if (minAgentCommission == null) {
                                minAgentCommission = agentCommission;
                                maxAgentCommission = agentCommission;
                            } else if (agentCommission.compareTo(minAgentCommission) < NumConstant.ZERO) {
                                minAgentCommission = agentCommission;
                            } else if (agentCommission.compareTo(maxAgentCommission) > NumConstant.ZERO) {
                                maxAgentCommission = agentCommission;
                            }
                            //计算分销店佣金
                            BigDecimal distributionStoreCommission = CloudPoolCommissionCalculationUtil.getCommission(commissionConfigDTO.getDistributionStoreCommissionRatio(), grossProfit);
                            if (minDistributionStoreCommission == null) {
                                minDistributionStoreCommission = distributionStoreCommission;
                                maxDistributionStoreCommission = distributionStoreCommission;
                            } else if (distributionStoreCommission.compareTo(minDistributionStoreCommission) < NumConstant.ZERO) {
                                minDistributionStoreCommission = distributionStoreCommission;
                            } else if (distributionStoreCommission.compareTo(maxDistributionStoreCommission) > NumConstant.ZERO) {
                                maxDistributionStoreCommission = distributionStoreCommission;
                            }
                            //计算平台服务费
                            BigDecimal platformServiceCommission = CloudPoolCommissionCalculationUtil.getCommission(commissionConfigDTO.getPlatformServiceCommissionRatio(), grossProfit);
                            if (minPlatformServiceCommission == null) {
                                minPlatformServiceCommission = platformServiceCommission;
                                maxPlatformServiceCommission = platformServiceCommission;
                            } else if (platformServiceCommission.compareTo(minPlatformServiceCommission) < NumConstant.ZERO) {
                                minPlatformServiceCommission = platformServiceCommission;
                            } else if (platformServiceCommission.compareTo(minPlatformServiceCommission) > NumConstant.ZERO) {
                                maxPlatformServiceCommission = distributionStoreCommission;
                            }

                        }
                        //最小代理人佣金
                        resGoodsDTO.setMinAgentCommission(minAgentCommission);
                        //最大代理人佣金
                        resGoodsDTO.setMaxAgentCommission(maxAgentCommission);
                        //最小分销店佣金
                        resGoodsDTO.setMinDistributionStoreCommission(minDistributionStoreCommission);
                        //最大分销店佣金
                        resGoodsDTO.setMaxDistributionStoreCommission(maxDistributionStoreCommission);
                        //最小平台服务费
                        resGoodsDTO.setMinPlatformServiceCommission(minPlatformServiceCommission);
                        //最大平台服务费
                        resGoodsDTO.setMaxPlatformServiceCommission(maxPlatformServiceCommission);
                    }
                } else {
                    //非系列商品
                    Optional<AtomResCloudPoolGoodsCommissionConfigDTO> commissionConfigOptional = cloudPoolGoodsCommissionConfigDTOS.stream()
                            .filter(commissionConfigDTO -> StringUtils.equals(commissionConfigDTO.getParentGoodsNo(), resGoodsDTO.getGoodsNo())).findFirst();
                    if (commissionConfigOptional.isPresent()) {
                        AtomResCloudPoolGoodsCommissionConfigDTO commissionConfigDTO = commissionConfigOptional.get();
                        //云池零售价
                        resGoodsDTO.setCloudPoolRetailPrice(commissionConfigOptional.get().getRetailPrice());
                        //云池市场价
                        resGoodsDTO.setCloudPoolMarketPrice(commissionConfigOptional.get().getMarketPrice());

                        //毛利润
                        BigDecimal grossProfit = CloudPoolCommissionCalculationUtil.getGrossProfit(
                                resGoodsDTO.getCloudPoolSupplyPrice(),
                                commissionConfigDTO.getRetailPrice(),
                                commissionConfigDTO.getActivityExpenses()
                        );
                        //代理人佣金比例（%）
                        resGoodsDTO.setAgentCommissionRatio(commissionConfigDTO.getAgentCommissionRatio());
                        //代理人佣金
                        resGoodsDTO.setAgentCommission(
                                CloudPoolCommissionCalculationUtil.getCommission(commissionConfigDTO.getAgentCommissionRatio(), grossProfit)
                        );
                        //分销店佣金比例（%）
                        resGoodsDTO.setDistributionStoreCommissionRatio(commissionConfigDTO.getDistributionStoreCommissionRatio());
                        //分销店佣金
                        resGoodsDTO.setDistributionStoreCommission(
                                CloudPoolCommissionCalculationUtil.getCommission(commissionConfigDTO.getDistributionStoreCommissionRatio(), grossProfit)
                        );
                        //平台服务费
                        resGoodsDTO.setPlatformServiceCommission(
                                CloudPoolCommissionCalculationUtil.getCommission(commissionConfigDTO.getPlatformServiceCommissionRatio(), grossProfit)
                        );
                    }
                }
            });
        } catch (Exception e) {
            log.error("查询商品云池价格与佣金列表报错-入参--{}--", goodsDTOList, e);
            throw e;
        }
    }

    /**
     * 获取云池商品佣金集合
     *
     * @param atomReqDTO 查询参数
     * @return List<AtomResCloudPoolGoodsCommissionConfigDTO>
     */
    private List<AtomResCloudPoolGoodsCommissionConfigDTO> getCloudPoolGoodsCommissionList(AtomReqCloudPoolGoodsCommissionConfigDTO atomReqDTO) {
        ExecuteDTO<List<AtomResCloudPoolGoodsCommissionConfigDTO>> executeDTO = atomCloudPoolGoodsCommissionConfigAnalysisService.getListConfigInfoByParam(atomReqDTO);
        checkExecuteDTO(executeDTO);

        return executeDTO.getData();
    }

    /**
     * 校验返回参数
     *
     * @param executeDTO 返回返回参数
     * <AUTHOR>
     */
    private void checkExecuteDTO(ExecuteDTO executeDTO) {
        if (executeDTO == null) {
            throw new BaseException(CommonCode.CODE_10000003);
        }
        if (!executeDTO.successFlag()) {
            throw new BaseException(executeDTO.getStatus(), executeDTO.getMsg());
        }
    }

    @Override
    public ExecuteDTO<List<ResVoucherSaleAndUsedCountDTO>> getVoucherSaleAndUsedCount(ReqCouponSettingDTO reqCouponSettingDTO) {
        CouponUserRecordVO couponUserRecordVO = new CouponUserRecordVO();
        couponUserRecordVO.setCouponNoList(reqCouponSettingDTO.getPromotionNoList());
        List<VoucherSaleAndUsedCountVO> voucherSaleAndUsedCountVOS = couponUserRecordDao.selectVoucherSaleAndUsedCount(couponUserRecordVO);
        if (ListUtil.isNotEmpty(voucherSaleAndUsedCountVOS)) {
            return ExecuteDTO.ok(BeanCopierUtil.copyList(voucherSaleAndUsedCountVOS, ResVoucherSaleAndUsedCountDTO.class));
        }
        return ExecuteDTO.ok(Collections.emptyList());
    }

    /**
     * 20230928蛋品-赵翔宇-商家橙豆-橙豆规则
     *
     * 千橙掌柜智能收银 -> 全部功能 -> 橙豆管理 -> 橙豆列表 -> 添加和修改橙豆规则, 配置优惠券, 查询可用的优惠券信息, 编辑规则时, 当前规则之前已经选择的优惠券, 需要回显
     * 需要满足以下三个条件 :
     * 优惠券类型：手动发粉丝券
     * 券使用期限类型：自用户获取XX天内可用
     * 使用渠道：包含门店开单
     *
     * @param reqVirtualCoinRuleCouponSettingDTO 请求参数
     * @return 响应参数
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResVirtualCoinRuleCouponSettingDTO>> getVirtualCoinRuleAvailableCouponSettingPage(ReqVirtualCoinRuleCouponSettingDTO reqVirtualCoinRuleCouponSettingDTO) {
        log.info("CouponSettingAnalysisServiceImpl-getVirtualRuleAvailableCouponSettingPage---start, params={}", JSON.toJSONString(reqVirtualCoinRuleCouponSettingDTO));

        Page<Object> pages = PageHelper.startPage(reqVirtualCoinRuleCouponSettingDTO);
        pages.setReasonable(false);

        AtomVirtualCoinRuleCouponSettingVo atomVirtualCoinRuleCouponSettingVo = BeanCopierUtil.copy(reqVirtualCoinRuleCouponSettingDTO, AtomVirtualCoinRuleCouponSettingVo.class);

        // 20230928蛋品-赵翔宇-商家橙豆-橙豆规则
        String virtualCoinRuleType = virtualCoinUtil.getVirtualCoinRuleType(reqVirtualCoinRuleCouponSettingDTO.getStoreNo(),
                reqVirtualCoinRuleCouponSettingDTO.getMerchantNo(), reqVirtualCoinRuleCouponSettingDTO.getLoginIdentity());

        // 券使用期限类型：自用户获取XX天内可用
        atomVirtualCoinRuleCouponSettingVo.setCouponPeriodValidity(CouponPeriodValidityEnum.COUPON_PERIOD_VALIDITY_THREE.getCode());
        // 使用渠道：包含门店开单, 目前有三种情况, '1001, 1002', '1001', '1002', 这里查询语句用的是不等于1001
        atomVirtualCoinRuleCouponSettingVo.setCouponUseChannel(CouponUseChannelEnum.COUPON_USE_CHANNEL_ONE.getCode());

        // 20230928 蛋品-赵翔宇-商家橙豆-橙豆规则, 店铺未加入共享店铺, 则按照店铺规则查询
        if (VirtualCoinRuleTypeEnum.STORE_RULE.getCode().equals(virtualCoinRuleType)) {
            // 优惠券类型：店铺-手动发粉丝券
            atomVirtualCoinRuleCouponSettingVo.setPromotionCouponType(PromotionTypeCouponEnum.SHOP_MANUAL_COUPON.getCode());
        } else {
            // 20230928 蛋品-赵翔宇-商家橙豆-橙豆规则, 店铺加入共享店铺, 需要和商家一样, 使用商家编号查询数据
            // 优惠券类型：商家-手动发粉丝券
            atomVirtualCoinRuleCouponSettingVo.setPromotionCouponType(PromotionTypeCouponEnum.MERCHANT_MANUAL_COUPON.getCode());
            atomVirtualCoinRuleCouponSettingVo.setStoreNo("");
        }

        log.info("CouponSettingAnalysisServiceImpl-getVirtualRuleAvailableCouponSettingPage-查询橙豆规则可用的优惠券入参: {}", JSON.toJSONString(atomVirtualCoinRuleCouponSettingVo));
        List<AtomVirtualCoinRuleCouponSettingVo> virtualCoinRuleAvailableCouponList = couponSettingDao.selectVirtualCoinRuleAvailableCouponList(atomVirtualCoinRuleCouponSettingVo);
        log.info("CouponSettingAnalysisServiceImpl-getVirtualRuleAvailableCouponSettingPage-查询橙豆规则可用的优惠券出参: {}", JSON.toJSONString(virtualCoinRuleAvailableCouponList));
        // 参数转换
        ExecutePageDTO<ResVirtualCoinRuleCouponSettingDTO> executePageDTO = new ExecutePageDTO<>();

        List<ResVirtualCoinRuleCouponSettingDTO> resVirtualCoinRuleCouponSettingDTOList = BeanCopierUtil.copyList(virtualCoinRuleAvailableCouponList, ResVirtualCoinRuleCouponSettingDTO.class);
        executePageDTO.setTotal(pages.getTotal());
        executePageDTO.setRows(resVirtualCoinRuleCouponSettingDTOList);

        return ExecuteDTO.ok(executePageDTO);
    }

    @Override
    public ExecuteDTO<List<ResMemberLevelCouponSettingDTO>> getMemberLevelAvailableCouponList(ReqCouponSettingDTO reqCouponSettingDTO) {
        AtomCouponSettingVo atomCouponSettingVo = BeanCopierUtil.copy(reqCouponSettingDTO, AtomCouponSettingVo.class);
        List<AtomCouponSettingVo> atomCouponSettingVoList = couponSettingDao.selectMemberLevelAvailableCouponList(atomCouponSettingVo);
        List<ResMemberLevelCouponSettingDTO> resMemberLevelCouponSettingDTOList = BeanCopierUtil.copyList(atomCouponSettingVoList, ResMemberLevelCouponSettingDTO.class);
        if (StringUtils.isNotBlank(reqCouponSettingDTO.getMemberLevelNo())) {
            ReqMemberLevelDTO reqMemberLevelDTO = new ReqMemberLevelDTO();
            reqMemberLevelDTO.setMemberLevelNo(reqCouponSettingDTO.getMemberLevelNo());
            ExecuteDTO<ResMemberLevelDTO> executeDTO = memberLevelProcessService.getMemberLevel(reqMemberLevelDTO);
            if (executeDTO.successFlag() && executeDTO.getData() != null) {
                List<String> couponNoList = executeDTO.getData().getCouponNoList();
                if (CollectionUtils.isNotEmpty(couponNoList)) {
                    for (ResMemberLevelCouponSettingDTO resMemberLevelCouponSettingDTO : resMemberLevelCouponSettingDTOList) {
                        if (couponNoList.contains(resMemberLevelCouponSettingDTO.getCouponNo())) {
                            resMemberLevelCouponSettingDTO.setSelectFlag(NumConstant.TWO);
                        } else {
                            resMemberLevelCouponSettingDTO.setSelectFlag(NumConstant.ONE);
                        }
                    }
                }
            }
        } else {
            for (ResMemberLevelCouponSettingDTO resMemberLevelCouponSettingDTO : resMemberLevelCouponSettingDTOList) {
                resMemberLevelCouponSettingDTO.setSelectFlag(NumConstant.ONE);
            }
        }
        return ExecuteDTO.ok(resMemberLevelCouponSettingDTOList);
    }

    /**
     * 获取优惠券活动-优惠券分发店铺
     * @param reqCouponStoreDTO
     * @return
     */
    @Override
    public ExecuteDTO<ResCouponStoreDTO> getCouponStore(ReqCouponStoreDTO reqCouponStoreDTO) {
        AtomReqPromotionStoreRelationDTO promotionStoreRelationDTO = new AtomReqPromotionStoreRelationDTO();
        promotionStoreRelationDTO.setPromotionNo(reqCouponStoreDTO.getPromotionNo());
        promotionStoreRelationDTO.setMerchantNo(reqCouponStoreDTO.getMerchantNo());
        ExecuteDTO<ResPromotionStoreRelationInfoDTO> executeDTO = storeRelationUtil.getStoreRelationInfo(promotionStoreRelationDTO);
        if (null == executeDTO) {
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        }
        if (!executeDTO.successFlag()) {
            return new ExecuteDTO<>(executeDTO.getStatus(), executeDTO.getMsg(), null);
        }
        ResCouponStoreDTO resCouponStoreDTO = BeanCopierUtil.copy(executeDTO.getData(), ResCouponStoreDTO.class);
        resCouponStoreDTO.setPromotionNo(reqCouponStoreDTO.getPromotionNo());
        resCouponStoreDTO.setCouponNo(reqCouponStoreDTO.getCouponNo());
        return ExecuteDTO.ok(resCouponStoreDTO);
    }

}
