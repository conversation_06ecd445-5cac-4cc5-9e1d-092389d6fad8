package cn.htdt.marketprocess.biz.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqCloudPoolGoodsCommissionConfigDTO;
import cn.htdt.marketprocess.api.operat.CloudPoolGoodsCommissionConfigOperatService;
import cn.htdt.marketprocess.biz.conversion.CloudPoolGoodsCommissionConfigAssert;
import cn.htdt.marketprocess.dto.request.ReqCloudPoolGoodsCommissionConfigDTO;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomCloudPoolGoodsCommissionConfigOperatService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-01-21
 * @Description 云池商品佣金配置操作
 **/
@Slf4j
@DubboService
public class CloudPoolGoodsCommissionConfigOperatServiceImpl implements CloudPoolGoodsCommissionConfigOperatService {
    @Autowired
    private CloudPoolGoodsCommissionConfigAssert configAssert;
    @Resource
    private AtomCloudPoolGoodsCommissionConfigOperatService configOperatService;
    /**
     * @Description 新增云池商品佣金配置信息
     * <AUTHOR>
     * @param configDTO
     * @return
     */
    @Override
    public ExecuteDTO saveConfig(ReqCloudPoolGoodsCommissionConfigDTO configDTO) {
        log.info("MarketProcess-CloudPoolGoodsCommissionConfigOperatServiceImpl-saveConfig-params-start");
        log.info("MarketProcess-CloudPoolGoodsCommissionConfigOperatServiceImpl-saveConfig-params={}", String.valueOf(configDTO));
        AtomReqCloudPoolGoodsCommissionConfigDTO commissionConfigDTO = BeanCopierUtil.copy(configDTO, AtomReqCloudPoolGoodsCommissionConfigDTO.class);
        this.configAssert.saveAssert(commissionConfigDTO);
        ExecuteDTO saveRes = this.configOperatService.save(commissionConfigDTO);
        log.info("MarketProcess-CloudPoolGoodsCommissionConfigOperatServiceImpl-saveConfig-params-end");
        return saveRes;
    }
    /**
     * @Description 批量新增云池商品佣金配置信息
     * <AUTHOR>
     * @param configDTOList
     * @return
     */
    @Override
    public ExecuteDTO batchSaveConfig(List<ReqCloudPoolGoodsCommissionConfigDTO> configDTOList) {
        log.info("MarketProcess-CloudPoolGoodsCommissionConfigOperatServiceImpl-batchSaveConfig-params-start");
        log.info("MarketProcess-CloudPoolGoodsCommissionConfigOperatServiceImpl-batchSaveConfig-params={}", String.valueOf(configDTOList));
        List<AtomReqCloudPoolGoodsCommissionConfigDTO> configDTOS = BeanCopierUtil.copyList(configDTOList, AtomReqCloudPoolGoodsCommissionConfigDTO.class);
        this.configAssert.batchSaveAssert(configDTOS);
        ExecuteDTO executeDTO = this.configOperatService.batchSave(configDTOS);
        log.info("MarketProcess-CloudPoolGoodsCommissionConfigOperatServiceImpl-batchSaveConfig-params-end");
        return executeDTO;
    }
    /**
     * @Description 修改云池商品佣金配置信息
     * <AUTHOR>
     * @param configDTO
     * @return
     */
    @Override
    public ExecuteDTO updateConfigByParam(ReqCloudPoolGoodsCommissionConfigDTO configDTO){
        log.info("MarketProcess-CloudPoolGoodsCommissionConfigOperatServiceImpl-updateConfigByParam-params-start");
        log.info("MarketProcess-CloudPoolGoodsCommissionConfigOperatServiceImpl-updateConfigByParam-params={}", String.valueOf(configDTO));
        AtomReqCloudPoolGoodsCommissionConfigDTO commissionConfigDTO = BeanCopierUtil.copy(configDTO, AtomReqCloudPoolGoodsCommissionConfigDTO.class);
        this.configAssert.saveAssert(commissionConfigDTO);
        ExecuteDTO executeDTO = this.configOperatService.updateByParam(commissionConfigDTO);
        log.info("MarketProcess-CloudPoolGoodsCommissionConfigOperatServiceImpl-updateConfigByParam-params-end");
        return executeDTO;
    }

}
