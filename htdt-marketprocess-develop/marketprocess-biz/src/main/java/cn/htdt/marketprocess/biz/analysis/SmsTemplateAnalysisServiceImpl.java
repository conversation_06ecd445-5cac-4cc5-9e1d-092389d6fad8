package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqSmsTemplateDTO;
import cn.htdt.marketcenter.dto.response.AtomResSmsTemplateDTO;
import cn.htdt.marketprocess.api.analysis.SmsTemplateAnalysisService;
import cn.htdt.marketprocess.biz.conversion.SmsTemplateAssert;
import cn.htdt.marketprocess.dto.request.ReqSmsTemplateDTO;
import cn.htdt.marketprocess.dto.response.ResSmsTemplateDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomSmsTemplateAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 * 短信模板表 数据查询服务接口实现类
 *
 * <AUTHOR>
 * @date 2021/6/28
 **/
@DubboService
@Slf4j
public class SmsTemplateAnalysisServiceImpl implements SmsTemplateAnalysisService {
    @Resource
    private AtomSmsTemplateAnalysisService atomSmsTemplateAnalysisService;

    @Resource
    private SmsTemplateAssert smsTemplateAssert;

    /**
     * 综合服务平台 -> 营销管理 -> 短信营销活动 -> 获取短信模板列表
     * 超级老板APP -> 会员 -> 短信营销 -> 获取短信模板列表
     *
     * @param reqDTO 查询参数
     * @return ExecuteDTO<List < AtomResSmsTemplateDTO>>
     * <AUTHOR>
     */
    @Override
    public ExecuteDTO<List<ResSmsTemplateDTO>> getSmsTemplateList(ReqSmsTemplateDTO reqDTO) {
        log.info("-------SmsTemplateAnalysisServiceImpl-->getSmsTemplateList,获取短信模板列表--start----");

        smsTemplateAssert.getSmsTemplateListAssert(reqDTO);

        log.info("-----getSmsTemplateList---->,查询参数:{}", reqDTO);

        AtomReqSmsTemplateDTO atomReqDTO = BeanCopierUtil.copy(reqDTO, AtomReqSmsTemplateDTO.class);
        ExecuteDTO<List<AtomResSmsTemplateDTO>> executeDTO = atomSmsTemplateAnalysisService.getSmsTemplateList(atomReqDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }

        List<ResSmsTemplateDTO> list = BeanCopierUtil.copyList(executeDTO.getData(), ResSmsTemplateDTO.class);

        log.info("-------SmsTemplateAnalysisServiceImpl-->getSmsTemplateList,获取短信模板列表----");
        return ExecuteDTO.success(list);
    }
}
