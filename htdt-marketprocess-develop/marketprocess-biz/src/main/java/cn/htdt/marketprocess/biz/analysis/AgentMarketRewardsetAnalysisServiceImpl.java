package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.marketprocess.api.analysis.AgentMarketRewardsetAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomAgentMarketRewardsetAnalysisService;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2021-01-20
 * @Description 分销商品|云池商品酬劳设置
 **/
@DubboService
public class AgentMarketRewardsetAnalysisServiceImpl implements AgentMarketRewardsetAnalysisService {

    @Resource
    private AtomAgentMarketRewardsetAnalysisService atomAgentMarketRewardsetAnalysisService;
}