package cn.htdt.marketprocess.biz.operat;

import cn.htdt.common.base.exception.BaseException;
import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.enums.MarketErrorCode;
import cn.htdt.common.dto.enums.UserErrorCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.market.*;
import cn.htdt.common.enums.user.IdentityEnum;
import cn.htdt.common.generator.MarketFormGenerator;
import cn.htdt.common.generator.SnowflakeUtils;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.ListUtil;
import cn.htdt.goodsprocess.api.operat.LegacyGoodsCenterService;
import cn.htdt.marketcenter.dto.request.*;
import cn.htdt.marketcenter.dto.response.AtomResCouponSettingDTO;
import cn.htdt.marketcenter.dto.response.AtomResCouponUserRecordDTO;
import cn.htdt.marketprocess.api.operat.CouponPromotionOperateService;
import cn.htdt.marketprocess.api.operat.SmsSendRecordOperatService;
import cn.htdt.marketprocess.biz.constant.CommonConstant;
import cn.htdt.marketprocess.biz.conversion.CouponPromotionSettingAssert;
import cn.htdt.marketprocess.biz.conversion.PromotionInfoAssert;
import cn.htdt.marketprocess.biz.utils.CouponInfoUtil;
import cn.htdt.marketprocess.biz.utils.UserInfoUtil;
import cn.htdt.marketprocess.dao.CouponSettingDao;
import cn.htdt.marketprocess.dao.CouponUserSuitStoreDao;
import cn.htdt.marketprocess.domain.CouponUserSuitStoreDomain;
import cn.htdt.marketprocess.dto.request.*;
import cn.htdt.marketprocess.dto.response.ResVirtualCoinRuleAndCouponInfoDetailDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomCouponSettingAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomCouponUserRecordAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomCouponSendRecordOperatService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomCouponSettingOperatService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomCouponUserRecordOperatService;
import cn.htdt.marketprocess.legacycenter.biz.operat.BaseServiceImpl;
import cn.htdt.marketprocess.vo.AtomCouponSettingVo;
import cn.htdt.middlewareprocess.api.htd.SmsChannelSendService;
import cn.htdt.usercenter.dto.request.ReqAgentDTO;
import cn.htdt.usercenter.dto.request.ReqFancDTO;
import cn.htdt.usercenter.dto.response.ResAgentDTO;
import cn.htdt.usercenter.dto.response.ResFancDTO;
import cn.htdt.userprocess.api.AgentProcessService;
import cn.htdt.userprocess.api.LegacyUserCenterService;
import cn.htdt.userprocess.api.UcFansProcessService;
import cn.htdt.userprocess.api.UserPublicService;
import cn.htdt.userprocess.dto.publicDto.request.GetStoreInfoRequest;
import cn.htdt.userprocess.dto.publicDto.response.GetFanInfoResponse;
import cn.htdt.userprocess.dto.publicDto.response.GetStoreInfoResponse;
import cn.htdt.userprocess.dto.request.AgentInfoRequest;
import cn.htdt.userprocess.dto.request.FanFileRequest;
import cn.htdt.userprocess.dto.request.GetFanInfoRequest;
import cn.htdt.userprocess.dto.request.ImportFanFileRequest;
import cn.htdt.userprocess.dto.response.AgentInfoResponse;
import cn.htdt.userprocess.dto.response.StoreInfoResponse;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 优惠券活动操作服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@DubboService
public class CouponPromotionOperateServiceImpl extends BaseServiceImpl implements CouponPromotionOperateService {

    @Autowired
    private PromotionInfoAssert promotionInfoAssert;

    @Autowired
    private CouponPromotionSettingAssert couponPromotionSettingAssert;

    @Resource
    private AtomCouponSettingOperatService atomCouponSettingOperatService;

    @Resource
    private AtomCouponSettingAnalysisService atomCouponSettingAnalysisService;

    @Resource
    private AtomCouponUserRecordAnalysisService atomCouponUserRecordAnalysisService;

    @Resource
    private AtomCouponSendRecordOperatService atomCouponSendRecordOperatService;

    @Resource
    private AtomCouponUserRecordOperatService atomCouponUserRecordOperatService;

//    @DubboReference
//    private LegacyGoodsCenterService legacyGoodsCenterService;

//    @DubboReference
//    private SmsChannelSendService smsChannelSendService;

    @DubboReference
    private AgentProcessService agentProcessService;

    @DubboReference
    private LegacyUserCenterService legacyUserCenterService;

    @DubboReference
    private UcFansProcessService ucFansProcessService;

    @DubboReference
    private UserPublicService userPublicService;

    @Autowired
    SmsSendRecordOperatService smsSendRecordOperatService;

    @Autowired
    private CouponInfoUtil couponInfoUtil;

    @Autowired
    private UserInfoUtil userInfoUtil;

    @Autowired
    private CouponUserSuitStoreDao couponUserSuitStoreDao;
    @Resource
    private CouponSettingDao couponSettingDao;

    @Override
    public ExecuteDTO batchDistributeCoupon(ReqDistributeCouponDTO reqDistributeCouponDTO) {
        log.info("marketprocess-CouponPromotionOperateServiceImpl-入参reqDistributeCouponDTO={}", JSON.toJSONString(reqDistributeCouponDTO));

        // 参数校验
        promotionInfoAssert.batchDistributeCouponAssert(reqDistributeCouponDTO);

        // 查询活动信息
        AtomReqCouponSettingDTO atomReqCouponSettingDTO = new AtomReqCouponSettingDTO();
        atomReqCouponSettingDTO.setCouponNo(reqDistributeCouponDTO.getCouponNo());
        ExecuteDTO<AtomResCouponSettingDTO> executeDTO =
                atomCouponSettingAnalysisService.getCouponSetting(atomReqCouponSettingDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        AtomResCouponSettingDTO atomResCouponSettingDTO = executeDTO.getData();
        if (null == executeDTO.getData()) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000100);
        }

        // 数据组装
        List<AtomReqCouponUserRecordDTO> atomReqCouponUserRecordDTOList = Lists.newArrayList();
        // 生成发放批次编号
        String sendBatchNo = MarketFormGenerator.genSendBatchNo();
        String batchNo = String.valueOf(SnowflakeUtils.genId());

        // 组装优惠券发送记录
        AtomReqCouponSendRecordDTO atomReqCouponSendRecordDTO = BeanCopierUtil.copy(reqDistributeCouponDTO,
                AtomReqCouponSendRecordDTO.class);
        atomReqCouponSendRecordDTO.setSendBatchNo(sendBatchNo);
        atomReqCouponSendRecordDTO.setPromotionNo(executeDTO.getData().getPromotionNo());
        if (WhetherEnum.YES.getCode().equals(reqDistributeCouponDTO.getSmsNoticeFlag())) {
            // 短信发送批次
            atomReqCouponSendRecordDTO.setSmsBatchNo(batchNo);
        }
        // 如果是粉丝
        if (NumConstant.ONE == reqDistributeCouponDTO.getImportType()) {

            List<ResFancDTO> resFancDTOList;
            // 如果是指定分组 获取分组信息
            if (SendCouponRuleEnum.COUPON_SOURCE_TYPE_THREE.getCode().equals(reqDistributeCouponDTO.getSendCouponRule())) {
                resFancDTOList = this.convertFanGroup(reqDistributeCouponDTO);
            } else {
                // 根据手机号批量查询粉丝编号
                ImportFanFileRequest importFanFileRequest = new ImportFanFileRequest();
                List<FanFileRequest> fileRequestList = Lists.newArrayList();
                reqDistributeCouponDTO.getPhones().stream().forEach(phone -> {
                    FanFileRequest fanFileRequest = new FanFileRequest();
                    fanFileRequest.setPhone(phone.getPhone());
                    fanFileRequest.setDsPhone(phone.getDsPhone());
                    fileRequestList.add(fanFileRequest);
                });
                importFanFileRequest.setFileRequestList(fileRequestList);
                importFanFileRequest.setStoreNo(reqDistributeCouponDTO.getStoreNo());
                importFanFileRequest.setStoreName(reqDistributeCouponDTO.getStoreName());
                // 查询粉丝信息 若粉丝不存在则创建粉丝账号
                ExecuteDTO<List<ResFancDTO>> fanExecuteDTO = ucFansProcessService.queryFansByPhones(importFanFileRequest);

                if (!fanExecuteDTO.successFlag()) {
                    return ExecuteDTO.error(fanExecuteDTO.getStatus(), fanExecuteDTO.getMsg());
                }
                resFancDTOList = fanExecuteDTO.getData();
            }
            //单批次发行量记录
            atomReqCouponSendRecordDTO.setSendCount(resFancDTOList.size());
            /*
             * 查询下店铺信息 粉丝券发放有两种情况
             * 1.店铺发放粉丝券
             *      此时storeNo就是当前店铺
             * 2. 平台发放粉丝券
             *      此时如果是单个用户发放 是可以选择某一个粉丝关注店铺的
             *      如果是指定多人发放 券可使用店铺默认是汇通达官方旗舰店
             * 此时storeNo必填
             */
            GetStoreInfoResponse getStoreInfoResponse = userInfoUtil.getStoreInfoByStoreNo(reqDistributeCouponDTO.getStoreNo());
            log.info("marketprocess-CouponPromotionOperateServiceImpl-查询粉丝信息-getStoreInfoResponse={}", JSON.toJSONString(getStoreInfoResponse));
            // 组装优惠券用户接受记录
            resFancDTOList.stream().forEach(resFancDTO -> {
                // 组装优惠券用户接受记录
                AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO = BeanCopierUtil.copy(executeDTO.getData(),
                        AtomReqCouponUserRecordDTO.class);
                atomReqCouponUserRecordDTO.setSendBatchNo(sendBatchNo);
                BeanCopierUtil.copy(reqDistributeCouponDTO, atomReqCouponUserRecordDTO);
                atomReqCouponUserRecordDTO.setFanNo(resFancDTO.getFanNo());
                // 生成编号
                atomReqCouponUserRecordDTO.setUserCouponNo(MarketFormGenerator.genUserCouponNo());
                atomReqCouponUserRecordDTO.setSourceType(CouponSourceTypeEnum.COUPON_SOURCE_TYPE_TWO.getCode());
                // 手机号
                atomReqCouponUserRecordDTO.setPhone(resFancDTO.getPhone());
                atomReqCouponUserRecordDTO.setDsPhone(resFancDTO.getDsPhone());
                // 用户使用券的券面额
                atomReqCouponUserRecordDTO.setUserCouponValue(executeDTO.getData().getCouponValue());
                // 店铺相关信息赋值
                atomReqCouponUserRecordDTO.setStoreName(getStoreInfoResponse.getStoreName());
                atomReqCouponUserRecordDTO.setMerchantName(getStoreInfoResponse.getMerchantName());
                atomReqCouponUserRecordDTO.setMerchantNo(getStoreInfoResponse.getMerchantNo());
                this.getCouponNowTime(atomResCouponSettingDTO, atomReqCouponUserRecordDTO);
                atomReqCouponUserRecordDTOList.add(atomReqCouponUserRecordDTO);
            });
        } else {
            // 查询代理人信息
            List<ReqAgentDTO> reqAgentDTOList = Lists.newArrayList();
            reqDistributeCouponDTO.getPhones().stream().forEach(phone-> {
                ReqAgentDTO reqAgentDTO = new ReqAgentDTO();
                reqAgentDTO.setPhone(phone.getPhone());
                reqAgentDTO.setDsPhone(phone.getDsPhone());
                reqAgentDTOList.add(reqAgentDTO);
            });
            ExecuteDTO<List<ResAgentDTO>> listExecuteDTO = agentProcessService.queryAgentsByPhones(reqAgentDTOList);
            log.info("marketprocess-CouponPromotionOperateServiceImpl-查询代理人信息listExecuteDTO={}", JSON.toJSONString(listExecuteDTO));
            if (!listExecuteDTO.successFlag()) {
                return ExecuteDTO.error(listExecuteDTO.getStatus(), listExecuteDTO.getMsg());
            }
            // 代理人信息不存在
            if (CollectionUtils.isEmpty(listExecuteDTO.getData())) {
                return ExecuteDTO.error(UserErrorCode.CODE_13000045);
            }
            listExecuteDTO.getData().stream().forEach(resAgentDTO -> {
                // 组装优惠券用户接受记录
                AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO = BeanCopierUtil.copy(executeDTO.getData(),
                        AtomReqCouponUserRecordDTO.class);
                BeanCopierUtil.copy(reqDistributeCouponDTO, atomReqCouponUserRecordDTO);
                // 代理人代理店铺信息
                atomReqCouponUserRecordDTO.setStoreNo(resAgentDTO.getStoreNo());
                atomReqCouponUserRecordDTO.setStoreName(resAgentDTO.getStoreName());
                atomReqCouponUserRecordDTO.setMerchantName(resAgentDTO.getMerchantName());
                atomReqCouponUserRecordDTO.setMerchantNo(resAgentDTO.getMerchantNo());
                // 如果支持自用 则设置粉丝编号
//                if (WhetherEnum.YES.getCode().equals(executeDTO.getData().getAgentLimitFlag())) {
//                }
                atomReqCouponUserRecordDTO.setFanNo(resAgentDTO.getFanNo());
                atomReqCouponUserRecordDTO.setAgentNo(resAgentDTO.getAgentNo());
                atomReqCouponUserRecordDTO.setPhone(resAgentDTO.getPhone());
                atomReqCouponUserRecordDTO.setDsPhone(resAgentDTO.getDsPhone());
                // 生成编号
                atomReqCouponUserRecordDTO.setUserCouponNo(MarketFormGenerator.genUserCouponNo());
                // 用户使用券的券面额
                atomReqCouponUserRecordDTO.setUserCouponValue(executeDTO.getData().getCouponValue());
                atomReqCouponUserRecordDTO.setSendBatchNo(sendBatchNo);
                atomReqCouponUserRecordDTO.setSourceType(CouponSourceTypeEnum.COUPON_SOURCE_TYPE_TWO.getCode());
                atomReqCouponUserRecordDTO.setSelfUseFlag(executeDTO.getData().getAgentLimitFlag());
                this.getCouponNowTime(atomResCouponSettingDTO, atomReqCouponUserRecordDTO);
                atomReqCouponUserRecordDTOList.add(atomReqCouponUserRecordDTO);
            });
        }

        ExecuteDTO sendExecuteDTO = sendSms4Coupon(reqDistributeCouponDTO, atomResCouponSettingDTO);

        // 添加用户记录
        atomCouponSendRecordOperatService.batchInsertCouponSendRecord(Lists.newArrayList(atomReqCouponSendRecordDTO));
        atomCouponUserRecordOperatService.batchInsertCouponUserRecord(atomReqCouponUserRecordDTOList);
        if (!sendExecuteDTO.successFlag()) {
            return ExecuteDTO.success(MarketErrorCode.CODE_17000504.getShowMsg());
        }
        return ExecuteDTO.success();
    }

    /**
     * 千橙掌柜智能收银 -> 全部功能 -> 橙豆管理-> 买橙豆, 充值橙豆送优惠券, 橙豆充值成功, 批量发券
     *
     * @param reqVirtualCoinRuleDistributeCouponDTO 请求参数
     * @return 结果
     */
    @Override
    public ExecuteDTO batchDistributeVirtualCoinCoupon(ReqVirtualCoinRuleDistributeCouponDTO reqVirtualCoinRuleDistributeCouponDTO) {
        log.info("CouponPromotionOperateServiceImpl------>batchDistributeVirtualCoinCoupon, 入参batchDistributeVirtualCoinCoupon={}", JSON.toJSONString(reqVirtualCoinRuleDistributeCouponDTO));

        // 参数校验
        promotionInfoAssert.batchDistributeVirtualCoinRuleCouponAssert(reqVirtualCoinRuleDistributeCouponDTO);
        // 查询店铺信息
        GetStoreInfoResponse getStoreInfoResponse = userInfoUtil.getStoreInfoByStoreNo(reqVirtualCoinRuleDistributeCouponDTO.getStoreNo());

        // 活动信息
        List<ResVirtualCoinRuleAndCouponInfoDetailDTO> virtualCoinRuleCouponInfos = reqVirtualCoinRuleDistributeCouponDTO.getVirtualCoinRuleCouponInfos();

        // 优惠券发送记录表
        List<AtomReqCouponSendRecordDTO> atomReqCouponSendRecordDTOList = Lists.newArrayList();
        // 优惠券用户接收记录表
        List<AtomReqCouponUserRecordDTO> atomReqCouponUserRecordDTOList = Lists.newArrayList();

        // 券适用店铺
        List<CouponUserSuitStoreDomain> couponUserSuitStoreDomainList = Lists.newArrayList();

        // 组装优惠券发送记录和组装优惠券用户接受记录
        virtualCoinRuleCouponInfos.forEach(ruleAndCouponInfoDetailDTO -> {

            // 生成发放批次编号
            String sendBatchNo = MarketFormGenerator.genSendBatchNo();

            // 组装优惠券发送记录
            AtomReqCouponSendRecordDTO atomReqCouponSendRecordDTO = BeanCopierUtil.copy(reqVirtualCoinRuleDistributeCouponDTO,
                    AtomReqCouponSendRecordDTO.class);

            atomReqCouponSendRecordDTO.setSendBatchNo(sendBatchNo);
            atomReqCouponSendRecordDTO.setPromotionNo(ruleAndCouponInfoDetailDTO.getPromotionNo());
            atomReqCouponSendRecordDTO.setCouponNo(ruleAndCouponInfoDetailDTO.getCouponNo());
            atomReqCouponSendRecordDTO.setModifyNo(reqVirtualCoinRuleDistributeCouponDTO.getCreateNo());
            atomReqCouponSendRecordDTO.setModifyName(reqVirtualCoinRuleDistributeCouponDTO.getCreateName());

            // 单批次发行量记录
            atomReqCouponSendRecordDTO.setSendCount(NumConstant.ONE);

            log.info("CouponPromotionOperateServiceImpl------>batchDistributeVirtualCoinCoupon, atomReqCouponSendRecordDTO={}", JSON.toJSONString(atomReqCouponSendRecordDTO));
            atomReqCouponSendRecordDTOList.add(atomReqCouponSendRecordDTO);

            // 组装优惠券用户接受记录
            AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO = BeanCopierUtil.copy(ruleAndCouponInfoDetailDTO,
                    AtomReqCouponUserRecordDTO.class);

            atomReqCouponUserRecordDTO.setSendBatchNo(sendBatchNo);
            atomReqCouponUserRecordDTO.setFanNo(reqVirtualCoinRuleDistributeCouponDTO.getFanNo());
            // 生成编号
            atomReqCouponUserRecordDTO.setUserCouponNo(MarketFormGenerator.genUserCouponNo());
            atomReqCouponUserRecordDTO.setSourceType(CouponSourceTypeEnum.COUPON_SOURCE_TYPE_TWO.getCode());
            // 手机号
            atomReqCouponUserRecordDTO.setPhone(reqVirtualCoinRuleDistributeCouponDTO.getPhone());
            atomReqCouponUserRecordDTO.setDsPhone(reqVirtualCoinRuleDistributeCouponDTO.getDsPhone());
            // 用户使用券的券面额
            atomReqCouponUserRecordDTO.setUserCouponValue(ruleAndCouponInfoDetailDTO.getCouponValue());
            // 店铺和操作人相关信息赋值
            atomReqCouponUserRecordDTO.setCreateNo(reqVirtualCoinRuleDistributeCouponDTO.getCreateNo());
            atomReqCouponUserRecordDTO.setCreateName(reqVirtualCoinRuleDistributeCouponDTO.getCreateName());
            atomReqCouponUserRecordDTO.setModifyNo(reqVirtualCoinRuleDistributeCouponDTO.getCreateNo());
            atomReqCouponUserRecordDTO.setModifyName(reqVirtualCoinRuleDistributeCouponDTO.getCreateName());
            atomReqCouponUserRecordDTO.setStoreNo(getStoreInfoResponse.getStoreNo());
            atomReqCouponUserRecordDTO.setStoreName(getStoreInfoResponse.getStoreName());
            atomReqCouponUserRecordDTO.setMerchantName(getStoreInfoResponse.getMerchantName());
            atomReqCouponUserRecordDTO.setMerchantNo(getStoreInfoResponse.getMerchantNo());

            AtomResCouponSettingDTO atomResCouponSettingDTO = BeanCopierUtil.copy(ruleAndCouponInfoDetailDTO, AtomResCouponSettingDTO.class);
            this.getCouponNowTime(atomResCouponSettingDTO, atomReqCouponUserRecordDTO);
            log.info("CouponPromotionOperateServiceImpl------>batchDistributeVirtualCoinCoupon, atomReqCouponUserRecordDTO={}", JSON.toJSONString(atomReqCouponUserRecordDTO));
            atomReqCouponUserRecordDTOList.add(atomReqCouponUserRecordDTO);

            // 20230928蛋品-赵翔宇-商家橙豆, 充值橙豆发券, 券适用店铺, 这边店铺为共享店铺的话, 不需要设置适用店铺为商家下所有的共享店铺, 再哪个店获得的券, 适用店铺就是哪个店
            CouponUserSuitStoreDomain couponUserSuitStoreDomain = BeanCopierUtil.copy(atomReqCouponUserRecordDTO, CouponUserSuitStoreDomain.class);
            couponUserSuitStoreDomainList.add(couponUserSuitStoreDomain);
        });

        // 添加优惠券发送记录
        atomCouponSendRecordOperatService.batchInsertCouponSendRecord(atomReqCouponSendRecordDTOList);
        // 添加优惠券用户接收记录
        atomCouponUserRecordOperatService.batchInsertCouponUserRecord(atomReqCouponUserRecordDTOList);

        // 添加券适用店铺
        log.info("*CouponPromotionOperateServiceImpl.batchDistributeVirtualCoinCoupon-couponUserSuitStoreDomainList:{}", JSON.toJSONString(couponUserSuitStoreDomainList));
        this.saveBatch(couponUserSuitStoreDomainList);
//        couponUserSuitStoreDao.batchInsert(couponUserSuitStoreDomainList);
        return ExecuteDTO.ok();
    }

    @Override
    public ExecuteDTO batchDistributeMemberLevelCoupon(ReqDistributeMemberLevelCouponDTO reqDistributeMemberLevelCouponDTO) {
        log.info("*CouponPromotionOperateServiceImpl.batchDistributeMemberLevelCoupon-reqDistributeMemberLevelCouponDTO:{}", reqDistributeMemberLevelCouponDTO);
        // 查询店铺信息
        GetStoreInfoResponse getStoreInfoResponse = userInfoUtil.getStoreInfoByStoreNo(reqDistributeMemberLevelCouponDTO.getStoreNo());
        // 活动信息
        List<ReqMemberLevelCouponDTO> reqMemberLevelCouponDTOList = reqDistributeMemberLevelCouponDTO.getReqMemberLevelCouponDTOList();
        // 优惠券发送记录表
        List<AtomReqCouponSendRecordDTO> atomReqCouponSendRecordDTOList = new ArrayList<>();
        // 优惠券用户接收记录表
        List<AtomReqCouponUserRecordDTO> atomReqCouponUserRecordDTOList = new ArrayList<>();
        List<CouponUserSuitStoreDomain> couponUserSuitStoreDomainList = Lists.newArrayList();
        // 组装优惠券发送记录和组装优惠券用户接受记录
        reqMemberLevelCouponDTOList.forEach(item -> {
            AtomCouponSettingVo atomCouponSettingVo = new AtomCouponSettingVo();
            atomCouponSettingVo.setCouponNo(item.getCouponNo());
            //券使用期限类型：自用户获取XX天内可用
            atomCouponSettingVo.setCouponPeriodValidity(CouponPeriodValidityEnum.COUPON_PERIOD_VALIDITY_THREE.getCode());
            List<AtomCouponSettingVo> atomCouponSettingVoList = couponSettingDao.selectMemberLevelAvailableCouponList(atomCouponSettingVo);
            if (CollectionUtils.isNotEmpty(atomCouponSettingVoList)) {
                BeanCopierUtil.copy(atomCouponSettingVoList.get(NumConstant.ZERO), item);
                // 生成发放批次编号
                String sendBatchNo = MarketFormGenerator.genSendBatchNo();
                // 组装优惠券发送记录
                AtomReqCouponSendRecordDTO atomReqCouponSendRecordDTO = BeanCopierUtil.copy(reqDistributeMemberLevelCouponDTO, AtomReqCouponSendRecordDTO.class);
                atomReqCouponSendRecordDTO.setSendBatchNo(sendBatchNo);
                atomReqCouponSendRecordDTO.setPromotionNo(item.getPromotionNo());
                atomReqCouponSendRecordDTO.setCouponNo(item.getCouponNo());
                // 单批次发行量记录
                atomReqCouponSendRecordDTO.setSendCount(NumConstant.ONE);
                atomReqCouponSendRecordDTOList.add(atomReqCouponSendRecordDTO);
                // 组装优惠券用户接受记录
                AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO = BeanCopierUtil.copy(item, AtomReqCouponUserRecordDTO.class);
                atomReqCouponUserRecordDTO.setSendBatchNo(sendBatchNo);
                atomReqCouponUserRecordDTO.setFanNo(reqDistributeMemberLevelCouponDTO.getFanNo());
                // 生成编号
                atomReqCouponUserRecordDTO.setUserCouponNo(MarketFormGenerator.genUserCouponNo());
                atomReqCouponUserRecordDTO.setSourceType(CouponSourceTypeEnum.COUPON_SOURCE_TYPE_TWO.getCode());
                // 手机号
                atomReqCouponUserRecordDTO.setPhone(reqDistributeMemberLevelCouponDTO.getPhone());
                atomReqCouponUserRecordDTO.setDsPhone(reqDistributeMemberLevelCouponDTO.getDsPhone());
                // 用户使用券的券面额
                atomReqCouponUserRecordDTO.setUserCouponValue(item.getCouponValue());
                // 店铺和操作人相关信息赋值
                atomReqCouponUserRecordDTO.setStoreNo(getStoreInfoResponse.getStoreNo());
                atomReqCouponUserRecordDTO.setStoreName(getStoreInfoResponse.getStoreName());
                atomReqCouponUserRecordDTO.setMerchantName(getStoreInfoResponse.getMerchantName());
                atomReqCouponUserRecordDTO.setMerchantNo(getStoreInfoResponse.getMerchantNo());
                AtomResCouponSettingDTO atomResCouponSettingDTO = BeanCopierUtil.copy(item, AtomResCouponSettingDTO.class);
                this.getCouponNowTime(atomResCouponSettingDTO, atomReqCouponUserRecordDTO);
                atomReqCouponUserRecordDTOList.add(atomReqCouponUserRecordDTO);
                //适用店铺
                reqDistributeMemberLevelCouponDTO.getSuitStoreNos().forEach(storeNo -> {
                    CouponUserSuitStoreDomain couponUserSuitStoreDomain = BeanCopierUtil.copy(atomReqCouponUserRecordDTO, CouponUserSuitStoreDomain.class);
                    couponUserSuitStoreDomain.setStoreNo(storeNo);
                    couponUserSuitStoreDomainList.add(couponUserSuitStoreDomain);
                });
            }
        });
        // 添加优惠券发送记录
        log.info("*CouponPromotionOperateServiceImpl.batchDistributeMemberLevelCoupon-atomReqCouponSendRecordDTOList:{}", atomReqCouponSendRecordDTOList);
        atomCouponSendRecordOperatService.batchInsertCouponSendRecord(atomReqCouponSendRecordDTOList);
        // 添加优惠券用户接收记录
        log.info("*CouponPromotionOperateServiceImpl.batchDistributeMemberLevelCoupon-atomReqCouponUserRecordDTOList:{}", atomReqCouponUserRecordDTOList);
        atomCouponUserRecordOperatService.batchInsertCouponUserRecord(atomReqCouponUserRecordDTOList);
        // 添加适用店铺
        log.info("*CouponPromotionOperateServiceImpl.batchDistributeMemberLevelCoupon-couponUserSuitStoreDomainList:{}", couponUserSuitStoreDomainList);
        this.saveBatch(couponUserSuitStoreDomainList);
        //        couponUserSuitStoreDao.batchInsert(couponUserSuitStoreDomainList);
        return ExecuteDTO.ok();
    }

    /**
     * 获取优惠券时间
     *
     * @param atomResCouponSettingDTO
     * @param atomReqCouponUserRecordDTO
     */
    private void getCouponNowTime(AtomResCouponSettingDTO atomResCouponSettingDTO, AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO) {
        if (CouponPeriodValidityEnum.COUPON_PERIOD_VALIDITY_THREE.getCode().equals(atomResCouponSettingDTO.getCouponPeriodValidity())) {
            Integer couponEffectiveDay = atomResCouponSettingDTO.getCouponEffectiveDay() == null ? NumConstant.ONE : atomResCouponSettingDTO.getCouponEffectiveDay();
            LocalDateTime ldt = LocalDateTime.now();
            LocalDateTime endDateTime = LocalDateTime.of(ldt.getYear(), ldt.getMonth(), ldt.getDayOfMonth(), 23, 59, 59, 0);
            atomReqCouponUserRecordDTO.setCouponEffectiveTime(ldt);
            atomReqCouponUserRecordDTO.setCouponInvalidTime(endDateTime.plusDays((long) (couponEffectiveDay - 1)));
        }
    }

    @Override
    public ExecuteDTO upOrDownShelvesCoupon(ReqUpOrDownShelvesCouponDTO reqUpOrDownShelvesCouponDTO) {
        log.info("upOrDownShelvesCoupon-入参：{}", reqUpOrDownShelvesCouponDTO);
        couponPromotionSettingAssert.upOrDownShelvesCouponAssert(reqUpOrDownShelvesCouponDTO);
        return atomCouponSettingOperatService.upOrDownShelvesCoupon(BeanCopierUtil.copy(reqUpOrDownShelvesCouponDTO, AtomReqUpOrDownShelvesCouponDTO.class));
    }

    @Override
    public ExecuteDTO collectionCoupons(ReqCollectionCouponsDTO reqCollectionCouponsDTO) throws BaseException {
        log.info("MarketProcess-CouponPromotionOperateServiceImpl-collectionCoupons-params-start");
        log.info("MarketProcess-CouponPromotionOperateServiceImpl-collectionCoupons-params={}", JSON.toJSONString(reqCollectionCouponsDTO));
        // 3s内不能再领
        // 领券参数校验
        couponPromotionSettingAssert.collectionCouponsAssert(reqCollectionCouponsDTO);
        // 查询活动信息并校验活动存在与否
        AtomReqCouponSettingDTO atomReqCouponSettingDTO = new AtomReqCouponSettingDTO();
        atomReqCouponSettingDTO.setCouponNo(reqCollectionCouponsDTO.getCouponNo());
        ExecuteDTO<AtomResCouponSettingDTO> executeDTO = atomCouponSettingAnalysisService.getCouponSetting(atomReqCouponSettingDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        AtomResCouponSettingDTO couponSettingDTO = executeDTO.getData();
        if (null == couponSettingDTO) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000100);
        }
        // 查询店铺
        GetStoreInfoRequest getStoreInfoRequest = new GetStoreInfoRequest();
        getStoreInfoRequest.setStoreNo(reqCollectionCouponsDTO.getStoreNo());
        ExecuteDTO<GetStoreInfoResponse> storeExecute = userPublicService.queryStoreInfo(getStoreInfoRequest);
        if (!storeExecute.successFlag()) {
            return ExecuteDTO.error(storeExecute.getStatus(), storeExecute.getMsg());
        }
        reqCollectionCouponsDTO.setMerchantNo(storeExecute.getData().getMerchantNo());
        // 为了不修改逻辑，改为默认值1，couponInfoUtil.checkCollectionCoupons会判断是否代理人领券，是会改成2(即代理人领券)
        reqCollectionCouponsDTO.setType(NumConstant.ONE);
        // 领券数据校验
        ExecuteDTO checkExecuteDTO = couponInfoUtil.checkCollectionCoupons(couponSettingDTO, reqCollectionCouponsDTO, agentProcessService);
        if (!checkExecuteDTO.successFlag()) {
            return checkExecuteDTO;
        }

        List<AtomReqCouponUserRecordDTO> atomReqCouponUserRecordDTOList = Lists.newArrayList();
        List<CouponUserSuitStoreDomain> couponUserSuitStoreDomainList = Lists.newArrayList();
        String sendBatchNo = MarketFormGenerator.genSendBatchNo();
        // 组装优惠券用户接受记录
        AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO = BeanCopierUtil.copy(couponSettingDTO, AtomReqCouponUserRecordDTO.class);
        // 粉丝
        if (WhetherEnum.NO.getCode().equals(reqCollectionCouponsDTO.getType())) {
            atomReqCouponUserRecordDTO.setFanNo(reqCollectionCouponsDTO.getFanNo());
            // 生成编号
            atomReqCouponUserRecordDTO.setUserCouponNo(MarketFormGenerator.genUserCouponNo());
            atomReqCouponUserRecordDTO.setSendBatchNo(sendBatchNo);
            atomReqCouponUserRecordDTO.setSourceType(CouponSourceTypeEnum.COUPON_SOURCE_TYPE_ONE.getCode());
            atomReqCouponUserRecordDTO.setUserCouponValue(couponSettingDTO.getCouponValue());
            atomReqCouponUserRecordDTO.setStoreNo(reqCollectionCouponsDTO.getStoreNo());

            if (storeExecute.successFlag() && storeExecute.getData() != null) {
                atomReqCouponUserRecordDTO.setStoreName(storeExecute.getData().getStoreName());
                atomReqCouponUserRecordDTO.setMerchantName(storeExecute.getData().getMerchantName());
                atomReqCouponUserRecordDTO.setMerchantNo(storeExecute.getData().getMerchantNo());
            }
            // 查询粉丝
            GetFanInfoRequest getFanInfoRequest = new GetFanInfoRequest();
            getFanInfoRequest.setFanNo(reqCollectionCouponsDTO.getFanNo());
            ExecuteDTO<GetFanInfoResponse> fanExecute = userPublicService.queryFanInfo(getFanInfoRequest);
            if (fanExecute.successFlag()) {
                if (fanExecute.getData() != null) {
                    atomReqCouponUserRecordDTO.setPhone(fanExecute.getData().getPhone());
                    atomReqCouponUserRecordDTO.setDsPhone(fanExecute.getData().getDsPhone());
                }
            }
            //获取优惠券开始时间和结束时间
            this.getCouponTimeAndEndTime(couponSettingDTO, atomReqCouponUserRecordDTO);
            atomReqCouponUserRecordDTOList.add(atomReqCouponUserRecordDTO);

            //商家优惠券适用店铺
            if(PromotionTypeEnum.MERCHANT_ONLINE_FANS_COUPON.getCode().equals(couponSettingDTO.getPromotionCouponType())){
                List<AtomCouponSettingVo> list = couponSettingDao.selectMerchantCouponList(couponSettingDTO.getCouponNo());
                if(CollectionUtils.isNotEmpty(list)){
                    if(ApplyTypeEnum.APPLY_TYPE_ALL.getCode().equals(list.get(0).getStoreType())){
                        List<StoreInfoResponse>  storeInfoResponseList = userInfoUtil.getStoreList(list.get(0).getMerchantNo());
                        storeInfoResponseList.stream().forEach(storeInfoResponse -> {
                            CouponUserSuitStoreDomain couponUserSuitStoreDomain = new CouponUserSuitStoreDomain();
                            couponUserSuitStoreDomain.setCouponNo(couponSettingDTO.getCouponNo());
                            couponUserSuitStoreDomain.setUserCouponNo(atomReqCouponUserRecordDTO.getUserCouponNo());
                            couponUserSuitStoreDomain.setStoreNo(storeInfoResponse.getStoreNo());
                            couponUserSuitStoreDomain.setCreateNo(atomReqCouponUserRecordDTO.getCreateNo());
                            couponUserSuitStoreDomain.setCreateName(atomReqCouponUserRecordDTO.getCreateName());
                            couponUserSuitStoreDomain.setModifyName(atomReqCouponUserRecordDTO.getCreateName());
                            couponUserSuitStoreDomain.setModifyNo(atomReqCouponUserRecordDTO.getCreateNo());
                            couponUserSuitStoreDomainList.add(couponUserSuitStoreDomain);
                        });
                    }else {
                        list.stream().forEach(atomCouponSettingVo -> {
                            if(StringUtils.isNotBlank(atomCouponSettingVo.getStoreNo())) {
                                CouponUserSuitStoreDomain couponUserSuitStoreDomain = new CouponUserSuitStoreDomain();
                                couponUserSuitStoreDomain.setCouponNo(couponSettingDTO.getCouponNo());
                                couponUserSuitStoreDomain.setUserCouponNo(atomReqCouponUserRecordDTO.getUserCouponNo());
                                couponUserSuitStoreDomain.setStoreNo(atomCouponSettingVo.getStoreNo());
                                couponUserSuitStoreDomain.setCreateNo(atomReqCouponUserRecordDTO.getCreateNo());
                                couponUserSuitStoreDomain.setCreateName(atomReqCouponUserRecordDTO.getCreateName());
                                couponUserSuitStoreDomain.setModifyName(atomReqCouponUserRecordDTO.getCreateName());
                                couponUserSuitStoreDomain.setModifyNo(atomReqCouponUserRecordDTO.getCreateNo());
                                couponUserSuitStoreDomainList.add(couponUserSuitStoreDomain);
                            }
                        });
                    }
                }
            }
        } else if (WhetherEnum.YES.getCode().equals(reqCollectionCouponsDTO.getType())) {
            AgentInfoRequest agentInfoRequest = new AgentInfoRequest();
            agentInfoRequest.setFanNo(reqCollectionCouponsDTO.getFanNo());
            ExecuteDTO<AgentInfoResponse> agentExecuteDTO = agentProcessService.getAgentInfo(agentInfoRequest);
            log.info("-CouponPromotionOperateServiceImpl-collectionCoupons-代理人领券-agentExecuteDTO={}", JSON.toJSONString(agentExecuteDTO));
            if (!agentExecuteDTO.successFlag()) {
                return ExecuteDTO.error(agentExecuteDTO.getStatus(), agentExecuteDTO.getMsg());
            }
            AgentInfoResponse resAgentDTO = agentExecuteDTO.getData();
            if (resAgentDTO == null) {
                return ExecuteDTO.error(MarketErrorCode.CODE_17000001, "该代理人不存在");
            }
            atomReqCouponUserRecordDTO.setPhone(resAgentDTO.getPhone());
            atomReqCouponUserRecordDTO.setDsPhone(resAgentDTO.getDsPhone());
            // 代理人代理店铺信息
            atomReqCouponUserRecordDTO.setStoreNo(resAgentDTO.getStoreNo());
            atomReqCouponUserRecordDTO.setStoreName(resAgentDTO.getStoreName());
            atomReqCouponUserRecordDTO.setMerchantName(resAgentDTO.getMerchantName());
            atomReqCouponUserRecordDTO.setMerchantNo(resAgentDTO.getMerchantNo());
            atomReqCouponUserRecordDTO.setFanNo(resAgentDTO.getFanNo());// 代理人粉丝号
            atomReqCouponUserRecordDTO.setAgentNo(resAgentDTO.getAgentNo());// 代理人粉丝号
            // 若该优惠券支持代理人自用
            atomReqCouponUserRecordDTO.setSelfUseFlag(couponSettingDTO.getAgentLimitFlag());
            // 生成编号
            atomReqCouponUserRecordDTO.setUserCouponNo(MarketFormGenerator.genUserCouponNo());
            atomReqCouponUserRecordDTO.setSendBatchNo(sendBatchNo);
            atomReqCouponUserRecordDTO.setSourceType(CouponSourceTypeEnum.COUPON_SOURCE_TYPE_ONE.getCode());
            atomReqCouponUserRecordDTO.setUserCouponValue(couponSettingDTO.getCouponValue());
            this.getCouponTimeAndEndTime(couponSettingDTO, atomReqCouponUserRecordDTO);
            atomReqCouponUserRecordDTOList.add(atomReqCouponUserRecordDTO);
        }

        log.info("MarketProcess-CouponPromotionOperateServiceImpl-collectionCoupons-领券前的params={}", JSON.toJSONString(atomReqCouponUserRecordDTOList));
        // 领券操作
        if (CollectionUtils.isNotEmpty(atomReqCouponUserRecordDTOList)) {
            // 添加用户记录
            atomCouponUserRecordOperatService.batchInsertCouponUserRecord(atomReqCouponUserRecordDTOList);
            // 领取时剩余减1
            AtomReqCouponSettingDTO dto = new AtomReqCouponSettingDTO();
            List<String> couponList = new ArrayList<>();
            couponList.add(couponSettingDTO.getCouponNo());
            dto.setPromotionNoList(couponList);
            atomCouponSettingOperatService.changeCouponRemain(dto);
            //优惠券适用店铺
            if(CollectionUtils.isNotEmpty(couponUserSuitStoreDomainList)) {
                this.saveBatch(couponUserSuitStoreDomainList);
//                couponUserSuitStoreDao.batchInsert(couponUserSuitStoreDomainList);
            }
        }
        log.info("MarketProcess-CouponPromotionOperateServiceImpl-collectionCoupons-params-end");
        return ExecuteDTO.success();
    }

    /**
     * 获取优惠券开始时间和结束时间
     *
     * @param couponSettingDTO
     * @param atomReqCouponUserRecordDTO
     */
    private void getCouponTimeAndEndTime(AtomResCouponSettingDTO couponSettingDTO, AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO) {
        if (CouponPeriodValidityEnum.COUPON_PERIOD_VALIDITY_THREE.getCode().equals(couponSettingDTO.getCouponPeriodValidity())) {
            Integer couponEffectiveDay = couponSettingDTO.getCouponEffectiveDay() == null ? NumConstant.ONE : couponSettingDTO.getCouponEffectiveDay();
            LocalDateTime ldt = LocalDateTime.now();
            LocalDateTime endDateTime = LocalDateTime.of(ldt.getYear(), ldt.getMonth(), ldt.getDayOfMonth(), 23, 59, 59, 0);
            atomReqCouponUserRecordDTO.setCouponEffectiveTime(ldt);
            atomReqCouponUserRecordDTO.setCouponInvalidTime(endDateTime.plusDays((long) (couponEffectiveDay - 1)));
        }
    }

    @Override
    public ExecuteDTO collectionAgentCoupons(ReqCollectionCouponsDTO reqCollectionCouponsDTO) throws BaseException {
        log.info("MarketProcess-CouponPromotionOperateServiceImpl-collectionAgentCoupons-params-start");
        log.info("MarketProcess-CouponPromotionOperateServiceImpl-collectionAgentCoupons-params={}", JSON.toJSONString(reqCollectionCouponsDTO));
        // 领券参数校验
        couponPromotionSettingAssert.collectionAgentCouponsAssert(reqCollectionCouponsDTO);
        // 查询活动信息并校验活动存在与否
        AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO = new AtomReqCouponUserRecordDTO();
        atomReqCouponUserRecordDTO.setUserCouponNo(reqCollectionCouponsDTO.getUserCouponNo());
        ExecuteDTO<AtomResCouponUserRecordDTO> executeDTO = atomCouponUserRecordAnalysisService.getOneCouponUserRecord(atomReqCouponUserRecordDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        AtomResCouponUserRecordDTO atomResCouponUserRecordDTO = executeDTO.getData();
        if (null == atomResCouponUserRecordDTO) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000501);
        }

        // 领券数据校验
        ExecuteDTO checkExecuteDTO = couponInfoUtil.checkCollectionAgentCoupons(atomResCouponUserRecordDTO, reqCollectionCouponsDTO);
        if (!checkExecuteDTO.successFlag()) {
            return checkExecuteDTO;
        }

        log.info("MarketProcess-CouponPromotionOperateServiceImpl-collectionAgentCoupons-领券前的params={}", JSON.toJSONString(atomResCouponUserRecordDTO));
        // 领券操作
        if (atomResCouponUserRecordDTO != null) {
            // 查询粉丝、只做关注操作
            GetFanInfoRequest getFanInfoRequest = new GetFanInfoRequest();
            getFanInfoRequest.setFanNo(reqCollectionCouponsDTO.getFanNo());
            getFanInfoRequest.setStoreNo(reqCollectionCouponsDTO.getStoreNo());
            getFanInfoRequest.setFollowSource(reqCollectionCouponsDTO.getFollowSource());
            log.info("MarketProcess-CouponPromotionOperateServiceImpl-collectionAgentCoupons-查询粉丝、只做关注操作的params={}", JSON.toJSONString(getFanInfoRequest));
            ExecuteDTO<GetFanInfoResponse> fanExecute = userPublicService.queryFollowStoreFanInfo(getFanInfoRequest);
            // 添加用户记录
            AtomReqCouponUserRecordDTO reqCouponUserRecordDTO = new AtomReqCouponUserRecordDTO();
            reqCouponUserRecordDTO.setUserCouponNo(reqCollectionCouponsDTO.getUserCouponNo());
            reqCouponUserRecordDTO.setFanNo(reqCollectionCouponsDTO.getFanNo());
            if (fanExecute.successFlag()) {
//                if(fanExecute.getData() != null) {
//                    reqCouponUserRecordDTO.setPhone(fanExecute.getData().getPhone());
//                }
            }
            // 获取配置
            AtomReqCouponSettingDTO atomReqCouponSettingDTO = new AtomReqCouponSettingDTO();
            atomReqCouponSettingDTO.setPromotionNo(atomResCouponUserRecordDTO.getPromotionNo());
            atomReqCouponSettingDTO.setCouponNo(atomResCouponUserRecordDTO.getCouponNo());
            ExecuteDTO<AtomResCouponSettingDTO> couponExecuteDTO = this.atomCouponSettingAnalysisService.getCouponSetting(atomReqCouponSettingDTO);
            log.info("MarketProcess-CouponPromotionOperateServiceImpl-collectionAgentCoupons-获取配置params={}", JSON.toJSONString(couponExecuteDTO));
            if (couponExecuteDTO.successFlag()) {
                AtomResCouponSettingDTO atomResCouponSettingDTO = couponExecuteDTO.getData();
                if (atomResCouponSettingDTO != null) {
                    // 配置-是否支持代理人使用(1:否 2:是)
                    if (WhetherEnum.YES.getCode().equals(atomResCouponSettingDTO.getAgentLimitFlag())) {
                        if (reqCollectionCouponsDTO.getFanNo().equals(atomResCouponUserRecordDTO.getFanNo())) {
                            reqCouponUserRecordDTO.setSelfUseFlag(WhetherEnum.YES.getCode());
                            reqCouponUserRecordDTO.setAgentPresentFlag(WhetherEnum.NO.getCode());
                        } else {
                            reqCouponUserRecordDTO.setSelfUseFlag(WhetherEnum.NO.getCode());
                            reqCouponUserRecordDTO.setAgentPresentFlag(WhetherEnum.YES.getCode());
                        }
                    } else {
                        reqCouponUserRecordDTO.setSelfUseFlag(WhetherEnum.NO.getCode());
                        reqCouponUserRecordDTO.setAgentPresentFlag(WhetherEnum.YES.getCode());
                    }
                }
            }
            reqCouponUserRecordDTO.setAgentPresentFlag(WhetherEnum.YES.getCode());
            log.info("MarketProcess-CouponPromotionOperateServiceImpl-collectionAgentCoupons-领取分享param={}", JSON.toJSONString(reqCouponUserRecordDTO));
            atomCouponUserRecordOperatService.changeCouponUserRecord(reqCouponUserRecordDTO);
        }

        log.info("MarketProcess-CouponPromotionOperateServiceImpl-collectionAgentCoupons-params-end");
        return ExecuteDTO.success();
    }



    /**
     * 优惠券短信发送
     *
     * @param reqDistributeCouponDTO
     * @return
     */
    private ExecuteDTO sendSms4Coupon(ReqDistributeCouponDTO reqDistributeCouponDTO, AtomResCouponSettingDTO atomResCouponSettingDTO) {

        // 判断是否需要发送短信
        if (WhetherEnum.YES.getCode().equals(reqDistributeCouponDTO.getSmsNoticeFlag())) {
            // 店铺发粉丝券短信通知   平台发代理人短信通知暂时不做
            if (PromotionTypeCouponEnum.SHOP_MANUAL_COUPON.getCode().equals(atomResCouponSettingDTO.getPromotionCouponType())) {
                ReqSmsSendRecordDTO smsSendRecordDTO = new ReqSmsSendRecordDTO();
                smsSendRecordDTO.setTelephoneList(reqDistributeCouponDTO.getPhones().stream().map(ReqDistributeCouponDTO.Phone::getPhone).collect(Collectors.toList()));
                smsSendRecordDTO.setDsTelephoneList(reqDistributeCouponDTO.getPhones().stream().map(ReqDistributeCouponDTO.Phone::getDsPhone).collect(Collectors.toList()));
                smsSendRecordDTO.setSendContent(reqDistributeCouponDTO.getSmsContent());
                smsSendRecordDTO.setSourceType(SmsSourceTypeEnum.COUPON.getCode());
                smsSendRecordDTO.setSmsType(SmsTypeEnum.STORE_COUPONS_ISSUED_FANS.getCode());
                smsSendRecordDTO.setStoreNo(reqDistributeCouponDTO.getStoreNo());
                smsSendRecordDTO.setStoreName(reqDistributeCouponDTO.getStoreName());
                smsSendRecordDTO.setMerchantNo(reqDistributeCouponDTO.getMerchantNo());
                smsSendRecordDTO.setMerchantName(reqDistributeCouponDTO.getMerchantName());
                smsSendRecordDTO.setCreateNo(reqDistributeCouponDTO.getCreateNo());
                smsSendRecordDTO.setCreateName(reqDistributeCouponDTO.getCreateName());
                smsSendRecordDTO.setModifyNo(reqDistributeCouponDTO.getModifyNo());
                smsSendRecordDTO.setMerchantName(reqDistributeCouponDTO.getModifyName());
                smsSendRecordDTO.setSendTimeType(SmsTimeStatusEnum.SOON_SENDING.getCode());
                return smsSendRecordOperatService.smsSend(smsSendRecordDTO);
            } else if (PromotionTypeCouponEnum.PLATFORM_MANUAL_AGENT_COUPON.getCode().equals(atomResCouponSettingDTO.getPromotionCouponType())
                    || PromotionTypeCouponEnum.PLATFORM_MANUAL_FANS_COUPON.getCode().equals(atomResCouponSettingDTO.getPromotionCouponType())) {
                ReqSmsSendRecordDTO smsSendRecordDTO = new ReqSmsSendRecordDTO();
                smsSendRecordDTO.setTelephoneList(reqDistributeCouponDTO.getPhones().stream().map(ReqDistributeCouponDTO.Phone::getPhone).collect(Collectors.toList()));
                smsSendRecordDTO.setDsTelephoneList(reqDistributeCouponDTO.getPhones().stream().map(ReqDistributeCouponDTO.Phone::getDsPhone).collect(Collectors.toList()));
                smsSendRecordDTO.setSendContent(reqDistributeCouponDTO.getSmsContent());
                smsSendRecordDTO.setSourceType(SmsSourceTypeEnum.COUPON.getCode());
                smsSendRecordDTO.setCreateNo(reqDistributeCouponDTO.getCreateNo());
                smsSendRecordDTO.setCreateName(reqDistributeCouponDTO.getCreateName());
                smsSendRecordDTO.setModifyNo(reqDistributeCouponDTO.getModifyNo());
                smsSendRecordDTO.setMerchantName(reqDistributeCouponDTO.getModifyName());
                if (PromotionTypeCouponEnum.PLATFORM_MANUAL_FANS_COUPON.getCode().equals(atomResCouponSettingDTO.getPromotionCouponType())) {
                    smsSendRecordDTO.setSmsType(SmsTypeEnum.PLATFORM_COUPONS_ISSUED_FANS.getCode());
                } else if (PromotionTypeCouponEnum.PLATFORM_MANUAL_AGENT_COUPON.getCode().equals(atomResCouponSettingDTO.getPromotionCouponType())) {
                    smsSendRecordDTO.setSmsType(SmsTypeEnum.PLATFORM_COUPONS_ISSUED_AGENT.getCode());
                }
                return smsSendRecordOperatService.platformSmsSend(smsSendRecordDTO);
            }
        }
        return ExecuteDTO.success();
    }

    /**
     * 转换粉丝分组
     *
     * @param reqDistributeCouponDTO
     * @return
     */
    private List<ResFancDTO> convertFanGroup(ReqDistributeCouponDTO reqDistributeCouponDTO) {
        if (CollectionUtils.isEmpty(reqDistributeCouponDTO.getGroupNos())) {
            return Lists.newArrayList();
        }
        List<ResFancDTO> resFancDTOList = new ArrayList<>();
        reqDistributeCouponDTO.getGroupNos().forEach(groupNo -> {
            int pageNum = NumConstant.ONE;

            // 获取粉丝分组列表
            ReqFancDTO reqFancDTO = new ReqFancDTO();
            reqFancDTO.setGroupNo(groupNo);
            reqFancDTO.setPageSize(CommonConstant.BATCH_DEFAULT);

            while (true) {
                reqFancDTO.setPageNum(pageNum);
                ExecuteDTO<ExecutePageDTO<ResFancDTO>> executePageDTOExecuteDTO = legacyUserCenterService.getPageGroupFanc(reqFancDTO);
                resFancDTOList.addAll(executePageDTOExecuteDTO.getData().getRows());
                // 如果获取的数据小于查询数据的时候 跳出循环
                if (executePageDTOExecuteDTO.getData().getRows().size() < CommonConstant.BATCH_DEFAULT) {
                    break;
                }
                pageNum++;
            }
        });

        // 待返回的券要发送给的粉丝数据集
        List<ResFancDTO> resultFansList = new ArrayList<>();
        // 去重
        if (CollectionUtils.isNotEmpty(resFancDTOList)) {
            // 粉丝去重，避免单次发券，入到粉丝领券记录表中的数据重复
            resultFansList = resFancDTOList.stream().filter(distinctByKey(ResFancDTO::getFanNo)).collect(Collectors.toList());
            // 获取去重的粉丝手机号，用于短信通知
            List<ReqDistributeCouponDTO.Phone> phones = Lists.newArrayList();
            resultFansList.stream().filter(distinctByKey(ResFancDTO::getDsPhone)).forEach(resFancDTO -> {
                ReqDistributeCouponDTO.Phone phone = new ReqDistributeCouponDTO.Phone();
                phone.setDsPhone(resFancDTO.getDsPhone());
                phone.setPhone(resFancDTO.getPhone());
                phones.add(phone);
            });
            reqDistributeCouponDTO.setPhones(phones);
        }
        return resultFansList;
    }

    /**
     * 自定义函数去重（采用 Predicate函数式判断，采用 Function获取比较key）
     * 内部维护一个 ConcurrentHashMap，并采用 putIfAbsent特性实现
     *
     * @param keyExtractor
     * @param <T>
     * @return
     */
    public static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    @Override
    public ExecuteDTO batchDistributeMerchantCoupon(ReqDistributeCouponDTO reqDistributeCouponDTO) {
        log.info("marketprocess-CouponPromotionOperateServiceImpl-batchDistributeMerchantCoupon-入参reqDistributeCouponDTO={}", JSON.toJSONString(reqDistributeCouponDTO));
        long beginTime = System.currentTimeMillis();
        // 参数校验
        promotionInfoAssert.batchDistributeMerchantCouponAssert(reqDistributeCouponDTO);
        // 查询活动信息
        AtomReqCouponSettingDTO atomReqCouponSettingDTO = new AtomReqCouponSettingDTO();
        atomReqCouponSettingDTO.setCouponNo(reqDistributeCouponDTO.getCouponNo());
        ExecuteDTO<AtomResCouponSettingDTO> executeDTO = atomCouponSettingAnalysisService.getCouponSetting(atomReqCouponSettingDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        AtomResCouponSettingDTO atomResCouponSettingDTO = executeDTO.getData();
        if (null == executeDTO.getData()) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000100);
        }

        // 数据组装
        List<AtomReqCouponUserRecordDTO> atomReqCouponUserRecordDTOList = Lists.newArrayList();
        List<CouponUserSuitStoreDomain> couponUserSuitStoreDomainList = Lists.newArrayList();
        // 生成发放批次编号
        String sendBatchNo = MarketFormGenerator.genSendBatchNo();
        String batchNo = String.valueOf(SnowflakeUtils.genId());

        // 组装优惠券发送记录
        AtomReqCouponSendRecordDTO atomReqCouponSendRecordDTO = BeanCopierUtil.copy(reqDistributeCouponDTO,
                AtomReqCouponSendRecordDTO.class);
        atomReqCouponSendRecordDTO.setSendBatchNo(sendBatchNo);
        atomReqCouponSendRecordDTO.setPromotionNo(executeDTO.getData().getPromotionNo());
        if (WhetherEnum.YES.getCode().equals(reqDistributeCouponDTO.getSmsNoticeFlag())) {
            // 短信发送批次
            atomReqCouponSendRecordDTO.setSmsBatchNo(batchNo);
        }
        // 如果是粉丝
        if (NumConstant.ONE == reqDistributeCouponDTO.getImportType()) {
            List<ResFancDTO> resFancDTOList;
            //如果是指定店铺
            if (SendCouponRuleEnum.COUPON_SOURCE_TYPE_FOUR.getCode().equals(reqDistributeCouponDTO.getSendCouponRule())) {
                ExecuteDTO<List<ResFancDTO>> resFancListExecuteDTO = this.convertFanStores(reqDistributeCouponDTO);
                if(!resFancListExecuteDTO.successFlag()){
                    return ExecuteDTO.error(resFancListExecuteDTO.getStatus(), resFancListExecuteDTO.getMsg());
                }
                resFancDTOList = resFancListExecuteDTO.getData();
            } else {
                ReqFancDTO reqFancDTO = new ReqFancDTO();
                reqFancDTO.setPhoneList(reqDistributeCouponDTO.getPhones().stream().map(ReqDistributeCouponDTO.Phone::getPhone).collect(Collectors.toList()));
                reqFancDTO.setMerchantNo(reqDistributeCouponDTO.getMerchantNo());
                ExecuteDTO<List<ResFancDTO>> fanExecuteDTO = ucFansProcessService.queryFansByPhoneList(reqFancDTO);
                if (!fanExecuteDTO.successFlag()) {
                    return ExecuteDTO.error(fanExecuteDTO.getStatus(), fanExecuteDTO.getMsg());
                }
                resFancDTOList = fanExecuteDTO.getData();
            }
            if(ListUtil.isEmpty(resFancDTOList)){
                return ExecuteDTO.error(CommonCode.CODE_10000002, "粉丝不存在");
            }
            //单批次发行量记录
            atomReqCouponSendRecordDTO.setSendCount(resFancDTOList.size());
            // 组装优惠券用户接受记录
            resFancDTOList.stream().forEach(resFancDTO -> {
                // 组装优惠券用户接受记录
                AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO = BeanCopierUtil.copy(executeDTO.getData(),
                        AtomReqCouponUserRecordDTO.class);
                atomReqCouponUserRecordDTO.setSendBatchNo(sendBatchNo);
                BeanCopierUtil.copy(reqDistributeCouponDTO, atomReqCouponUserRecordDTO);
                atomReqCouponUserRecordDTO.setFanNo(resFancDTO.getFanNo());
                // 生成编号
                atomReqCouponUserRecordDTO.setUserCouponNo(MarketFormGenerator.genUserCouponNo());
                atomReqCouponUserRecordDTO.setSourceType(CouponSourceTypeEnum.COUPON_SOURCE_TYPE_TWO.getCode());
                // 手机号
                atomReqCouponUserRecordDTO.setPhone(resFancDTO.getPhone());
                atomReqCouponUserRecordDTO.setDsPhone(resFancDTO.getDsPhone());
                // 用户使用券的券面额
                atomReqCouponUserRecordDTO.setUserCouponValue(executeDTO.getData().getCouponValue());
                // 商家相关信息赋值
                atomReqCouponUserRecordDTO.setMerchantName(reqDistributeCouponDTO.getMerchantName());
                atomReqCouponUserRecordDTO.setMerchantNo(reqDistributeCouponDTO.getMerchantNo());
                this.getCouponNowTime(atomResCouponSettingDTO, atomReqCouponUserRecordDTO);
                atomReqCouponUserRecordDTOList.add(atomReqCouponUserRecordDTO);
                //适用店铺
                reqDistributeCouponDTO.getSuitStoreNos().forEach(storeNo -> {
                    CouponUserSuitStoreDomain couponUserSuitStoreDomain = BeanCopierUtil.copy(atomReqCouponUserRecordDTO, CouponUserSuitStoreDomain.class);
                    couponUserSuitStoreDomain.setStoreNo(storeNo);
                    couponUserSuitStoreDomainList.add(couponUserSuitStoreDomain);
                });
            });
        }
        // 添加用户记录
        ExecuteDTO sendRecordExecuteDTO = atomCouponSendRecordOperatService.batchInsertCouponSendRecord(Lists.newArrayList(atomReqCouponSendRecordDTO));
        if(sendRecordExecuteDTO.successFlag()){
            ExecuteDTO userRecordExecuteDTO = atomCouponUserRecordOperatService.batchInsertCouponUserRecord(atomReqCouponUserRecordDTOList);
            if(userRecordExecuteDTO.successFlag()){
                this.saveBatch(couponUserSuitStoreDomainList);
//                couponUserSuitStoreDao.batchInsert(couponUserSuitStoreDomainList);
            } else {
                return userRecordExecuteDTO;
            }
        } else {
            return sendRecordExecuteDTO;
        }
//        //发送短信, 暂无此需求
//        ExecuteDTO sendExecuteDTO = sendSms4Coupon(reqDistributeCouponDTO, atomResCouponSettingDTO);
//        if (!sendExecuteDTO.successFlag()) {
//            return ExecuteDTO.success(MarketErrorCode.CODE_17000504.getShowMsg());
//        }
        log.info("marketprocess-CouponPromotionOperateServiceImpl-batchDistributeMerchantCoupon-end,costTime:{}", (System.currentTimeMillis() - beginTime));
        return ExecuteDTO.success();
    }

    /**
     * 指定店铺粉丝集合
     * @param reqDistributeCouponDTO
     * @return
     */
    private ExecuteDTO<List<ResFancDTO>> convertFanStores(ReqDistributeCouponDTO reqDistributeCouponDTO) {
        if (CollectionUtils.isEmpty(reqDistributeCouponDTO.getAppointStoreNos())) {
            return ExecuteDTO.success();
        }
        ReqFancDTO reqFancDTO = new ReqFancDTO();
        reqFancDTO.setMerchantNo(reqDistributeCouponDTO.getMerchantNo());
        reqFancDTO.setStoreNoList(reqDistributeCouponDTO.getAppointStoreNos());
        ExecuteDTO<List<ResFancDTO>> resFancListExecuteDTO = ucFansProcessService.queryStoresFans(reqFancDTO);
        if (resFancListExecuteDTO == null) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        return resFancListExecuteDTO;
    }

}
