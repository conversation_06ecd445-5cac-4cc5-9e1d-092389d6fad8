package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.CollectionUtils;
import cn.htdt.marketcenter.dto.request.ApplyPromoteRecordDto;
import cn.htdt.marketcenter.dto.request.AtomPromotionApplicationProcessingDTO;
import cn.htdt.marketcenter.dto.response.PromotionApplicationProcessingDTO;
import cn.htdt.marketprocess.api.analysis.PromotionApplicationProcessingAnalysisService;
import cn.htdt.marketprocess.dto.request.ReqApplyPromoteRecordDto;
import cn.htdt.marketprocess.dto.request.ReqPromotionApplicationProcessingDTO;
import cn.htdt.marketprocess.dto.response.ResPromotionApplicationProcessingDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomPromotionApplicationProcessingAnalysisService;
import cn.htdt.usercenter.dto.response.ResUUcMerchantDTO;
import cn.htdt.userprocess.api.LegacyUserCenterService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 推广申请处理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-12
 */
@DubboService
public class PromotionApplicationProcessingAnalysisServiceImpl implements PromotionApplicationProcessingAnalysisService {

    @Resource
    private AtomPromotionApplicationProcessingAnalysisService processingAnalysisService;

    @DubboReference
    private LegacyUserCenterService legacyUserCenterService;

    @Override
    public ExecuteDTO<ExecutePageDTO<ResPromotionApplicationProcessingDTO>> getPromotionApplicationProcessing(ReqPromotionApplicationProcessingDTO processingDTO) {
        AtomPromotionApplicationProcessingDTO applicationProcessingDTO = BeanCopierUtil.copy(processingDTO, AtomPromotionApplicationProcessingDTO.class);

        ExecuteDTO<ExecutePageDTO<PromotionApplicationProcessingDTO>> executeDTO = processingAnalysisService.getPromotionApplicationProcessing(applicationProcessingDTO);
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ExecutePageDTO<ResPromotionApplicationProcessingDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResPromotionApplicationProcessingDTO> processingDTOS = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResPromotionApplicationProcessingDTO.class);
        // 产品要求商家编码改为htd账号
        if (CollectionUtils.isNotEmpty(processingDTOS)) {
            List<String> merchantNoList = processingDTOS.stream().map(ResPromotionApplicationProcessingDTO::getMerchantNo).distinct().collect(Collectors.toList());
            // 根据商家编码查询htd账号，用于导出
            ExecuteDTO<List<ResUUcMerchantDTO>> merchantExecuteDTO = legacyUserCenterService.getByMerchantNos(merchantNoList);
            if (null == merchantExecuteDTO) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            if (!merchantExecuteDTO.successFlag()) {
                return ExecuteDTO.error(merchantExecuteDTO.getStatus(), merchantExecuteDTO.getMsg());
            }
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(merchantExecuteDTO.getData())) {
                processingDTOS.forEach(recordDTO -> {
                    merchantExecuteDTO.getData().forEach(merchant -> {
                        if (StringUtils.equals(recordDTO.getMerchantNo(), merchant.getMerchantNo())) {
                            recordDTO.setMerchantNo(merchant.getLoginAccount());
                        }
                    });
                });
            }
        }

        executePageDTO.setRows(processingDTOS);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    @Override
    public ExecuteDTO<Object> updateRecordInfoList(List<ReqApplyPromoteRecordDto> list) {
        List<ApplyPromoteRecordDto> applyPromoteRecordList = BeanCopierUtil.copyList(list,ApplyPromoteRecordDto.class);
        return processingAnalysisService.updateRecordInfoList(applyPromoteRecordList);
    }
}
