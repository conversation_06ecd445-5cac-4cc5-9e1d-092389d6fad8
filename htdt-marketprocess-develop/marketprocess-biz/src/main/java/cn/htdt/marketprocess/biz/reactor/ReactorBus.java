package cn.htdt.marketprocess.biz.reactor;

import cn.htdt.marketprocess.biz.reactor.inft.EventProcess;
import reactor.core.Reactor;
import reactor.core.spec.Reactors;
import reactor.event.Event;
import reactor.event.dispatch.Dispatcher;
import reactor.event.dispatch.WorkQueueDispatcher;

import static reactor.event.selector.Selectors.$;

/**
 * <AUTHOR>
 * @Date 2021-03-10
 * @Description
 **/
public class ReactorBus {
    private Reactor reactor = null;
    private Dispatcher dispatcher = null;
    private int poolSize;
    private int backLog;

    public void setPoolSize(int poolSize) {
        this.poolSize = poolSize;
    }

    public void setBackLog(int backLog) {
        this.backLog = backLog;
    }

    public void init() {
        this.dispatcher = new WorkQueueDispatcher("event_orderprocess", poolSize, backLog, null);
        this.reactor = Reactors.reactor().firstEventRouting().dispatcher(dispatcher).get();
    }

    public void stop() {
        this.dispatcher.shutdown();
        this.reactor = null;
    }

    public <T> void addnotify(String key, T t) {
        this.reactor.notify(key, Event.wrap(t));
    }

    public void addon(String key, EventProcess eventProcess) {
        this.reactor.on($(key), new ConsumerAdapter(eventProcess));
    }
}
