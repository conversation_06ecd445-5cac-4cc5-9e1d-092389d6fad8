package cn.htdt.marketprocess.biz.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.enums.market.HandleGetStatusEnum;
import cn.htdt.common.generator.MarketFormGenerator;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomPromotionApplicationProcessingDTO;
import cn.htdt.marketprocess.api.operat.PromotionApplicationProcessingOperatService;
import cn.htdt.marketprocess.biz.conversion.PromotionApplicationAssert;
import cn.htdt.marketprocess.dto.request.ReqPromotionApplicationProcessingDTO;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomPromotionApplicationProcessingOperatService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

/**
 * <p>
 * 推广申请处理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-12
 */
@Slf4j
@DubboService
public class PromotionApplicationProcessingOperatServiceImpl implements PromotionApplicationProcessingOperatService {

    @Resource
    private AtomPromotionApplicationProcessingOperatService processingOperatService;

    @Autowired
    private PromotionApplicationAssert promotionApplicationAssert;

    @Override
    public ExecuteDTO addPromotionApplication(ReqPromotionApplicationProcessingDTO applicationProcessingDTO) {
        log.info("MarketProcess-PromotionApplicationProcessingOperatServiceImpl-addPromotionApplication-params-start");
        promotionApplicationAssert.addPromotionApplicationAssert(applicationProcessingDTO);
        AtomPromotionApplicationProcessingDTO processingDTO = BeanCopierUtil.copy(applicationProcessingDTO, AtomPromotionApplicationProcessingDTO.class);
        processingDTO.setPopularizeNo(MarketFormGenerator.genPopularizeNo());
        //状态
        processingDTO.setHandleStatus(HandleGetStatusEnum.PENDING.getCode());
        ExecuteDTO executeDTO = processingOperatService.addPromotionApplication(processingDTO);
        log.info("MarketProcess-PromotionApplicationProcessingOperatServiceImpl-addPromotionApplication-params-end");
        return executeDTO;
    }

    @Override
    public ExecuteDTO modifyPromotionApplication(ReqPromotionApplicationProcessingDTO applicationProcessingDTO) {
        log.info("MarketProcess-PromotionApplicationProcessingOperatServiceImpl-modifyPromotionApplication-params-start");
        promotionApplicationAssert.modifyPromotionApplicationAssert(applicationProcessingDTO);
        AtomPromotionApplicationProcessingDTO processingDTO = BeanCopierUtil.copy(applicationProcessingDTO, AtomPromotionApplicationProcessingDTO.class);

        ExecuteDTO executeDTO = processingOperatService.modifyPromotionApplication(processingDTO);
        log.info("MarketProcess-PromotionApplicationProcessingOperatServiceImpl-modifyPromotionApplication-params-end");
        return executeDTO;
    }
}
