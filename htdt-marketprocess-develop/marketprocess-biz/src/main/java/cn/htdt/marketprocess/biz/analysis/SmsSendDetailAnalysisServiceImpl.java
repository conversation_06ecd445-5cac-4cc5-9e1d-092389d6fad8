package cn.htdt.marketprocess.biz.analysis;


import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.market.SmsErrorMsgEnum;
import cn.htdt.common.enums.market.SmsSendResultStatusEnum;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.CollectionUtils;
import cn.htdt.marketprocess.api.analysis.SmsSendDetailAnalysisService;
import cn.htdt.marketprocess.dao.SmsSendDetailDao;
import cn.htdt.marketprocess.domain.SmsSendDetailDomain;
import cn.htdt.marketprocess.dto.request.ReqSmsSendDetailDTO;
import cn.htdt.marketprocess.dto.response.ResSmsSendDetailDTO;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomSmsSendRecordOperatService;
import cn.htdt.marketprocess.vo.SmsSendDetailVo;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 短信发送详情表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-10
 */
@DubboService
@Slf4j
public class SmsSendDetailAnalysisServiceImpl implements SmsSendDetailAnalysisService {
    @Resource
    private SmsSendDetailDao smsSendDetailDao;
    @Resource
    private AtomSmsSendRecordOperatService atomSmsSendRecordOperatService;


    @Override
    public ExecuteDTO<List<ResSmsSendDetailDTO>> selectByParam(ReqSmsSendDetailDTO atomReqSmsSendDetailDTO) {
        List<ResSmsSendDetailDTO> smsSendDetailDTOS = new ArrayList<>();
        SmsSendDetailDomain smsSendDetailDomain = BeanCopierUtil.copy(atomReqSmsSendDetailDTO, SmsSendDetailDomain.class);
        List<SmsSendDetailVo> list = smsSendDetailDao.selectByParam(smsSendDetailDomain);
        if(CollectionUtils.isNotEmpty(list)){
            smsSendDetailDTOS = BeanCopierUtil.copyList(list,ResSmsSendDetailDTO.class);
            smsSendDetailDTOS.forEach(smsSendDetailDTO -> {
                if(SmsSendResultStatusEnum.SEND_FAILURE.getCode().equals(smsSendDetailDTO.getMsgStatus())){
                    smsSendDetailDTO.setDetailMsg(SmsErrorMsgEnum.getMsgByCode(smsSendDetailDTO.getDetailInfo()));
                }
            });
        }
        return ExecuteDTO.ok(smsSendDetailDTOS);
    }

    @Override
    public ExecuteDTO<ExecutePageDTO<ResSmsSendDetailDTO>> getSmsSendDetailPage(ReqSmsSendDetailDTO atomReqSmsSendDetailDTO) {
        List<ResSmsSendDetailDTO> smsSendDetailDTOS = new ArrayList<>();
        Page<Object> pages = PageHelper.startPage(atomReqSmsSendDetailDTO);
        SmsSendDetailDomain smsSendDetailDomain = BeanCopierUtil.copy(atomReqSmsSendDetailDTO, SmsSendDetailDomain.class);
        List<SmsSendDetailVo> list = smsSendDetailDao.selectByParam(smsSendDetailDomain);
        if(CollectionUtils.isNotEmpty(list)){
            smsSendDetailDTOS = BeanCopierUtil.copyList(list,ResSmsSendDetailDTO.class);
            smsSendDetailDTOS.forEach(smsSendDetailDTO -> {
                if(SmsSendResultStatusEnum.SEND_FAILURE.getCode().equals(smsSendDetailDTO.getMsgStatus())){
                    smsSendDetailDTO.setDetailMsg(SmsErrorMsgEnum.getMsgByCode(smsSendDetailDTO.getDetailInfo()));
                }
                smsSendDetailDTO.setMsgStatusType(SmsSendResultStatusEnum.getByCode(smsSendDetailDTO.getMsgStatus()).getType());
            });
        }

        return ExecuteDTO.ok(new ExecutePageDTO<>(pages.getTotal(), smsSendDetailDTOS));
    }

    @Override
    public ExecuteDTO saveBatchSmsSendDetail(List<ReqSmsSendDetailDTO> reqSmsSendDetailDTOList) {
        return atomSmsSendRecordOperatService.saveBatchSmsSendDetail(reqSmsSendDetailDTOList);
    }

    @Override
    public ExecuteDTO modifyBatchSmsSendDetail(List<ReqSmsSendDetailDTO> reqSmsSendDetailDTOList) {
        List<SmsSendDetailDomain> smsSendDetailDomainList = new ArrayList<>(reqSmsSendDetailDTOList.size());
        smsSendDetailDao.batchUpdateSmsSendDetail(smsSendDetailDomainList);
        return ExecuteDTO.success();
    }


}
