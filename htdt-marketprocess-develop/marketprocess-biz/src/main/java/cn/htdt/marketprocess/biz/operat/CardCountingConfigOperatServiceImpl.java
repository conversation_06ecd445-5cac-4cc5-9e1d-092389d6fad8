package cn.htdt.marketprocess.biz.operat;

import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.utils.ExecuteDTOUtil;
import cn.htdt.common.enums.AppChannelSourceEnum;
import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.constants.GoodsConstant;
import cn.htdt.common.enums.goods.*;
import cn.htdt.common.enums.market.ActivityTypeEnum;
import cn.htdt.common.enums.market.PromotionStatusEnum;
import cn.htdt.common.enums.market.PromotionTypeEnum;
import cn.htdt.common.enums.user.IdentityEnum;
import cn.htdt.common.generator.MarketFormGenerator;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.goodsprocess.api.operat.GoodsOperatService;
import cn.htdt.goodsprocess.dto.request.goods.ReqGoodsDTO;
import cn.htdt.goodsprocess.dto.request.goodsmedia.ReqGoodsPictureDTO;
import cn.htdt.goodsprocess.dto.response.goods.ResGoodsModifyStatusDTO;
import cn.htdt.marketprocess.api.operat.CardCountingConfigOperatService;
import cn.htdt.marketprocess.dao.CardCountingConfigDao;
import cn.htdt.marketprocess.dao.PromotionInfoDao;
import cn.htdt.marketprocess.dao.PromotionStoreRelationDao;
import cn.htdt.marketprocess.dao.PromotionVirtualGoodsRelationDao;
import cn.htdt.marketprocess.domain.CardCountingConfigDomain;
import cn.htdt.marketprocess.domain.PromotionInfoDomain;
import cn.htdt.marketprocess.domain.PromotionStoreRelationDomain;
import cn.htdt.marketprocess.domain.PromotionVirtualGoodsRelationDomain;
import cn.htdt.marketprocess.dto.request.ReqCardCountingConfigDTO;
import cn.htdt.marketprocess.vo.PromotionVirtualGoodsRelationVo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 计次卡设置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-11
 */
@Slf4j
@DubboService
public class CardCountingConfigOperatServiceImpl implements CardCountingConfigOperatService {

    @Resource
    private PromotionVirtualGoodsRelationDao promotionVirtualGoodsRelationDao;

    @Resource
    private PromotionInfoDao promotionInfoDao;

    @Resource
    private CardCountingConfigDao cardCountingConfigDao;

    @Resource
    private PromotionStoreRelationDao promotionStoreRelationDao;

    @DubboReference
    private GoodsOperatService goodsOperatService;

    @Value("${cardcounting.default-cardcounting-goods-Img:null}")
    private String defaultVirtualGoodsImg;

    @Override
    public ExecuteDTO<String> saveCardCountingConfig(ReqCardCountingConfigDTO reqCardCountingConfigDTO) {
        if (StringUtils.isBlank(reqCardCountingConfigDTO.getCardCountingNo())) {
            this.addCardCountingConfig(reqCardCountingConfigDTO);
        } else {
            this.updateCardCountingConfig(reqCardCountingConfigDTO);
        }

        if (StringUtils.isNotBlank(reqCardCountingConfigDTO.getCardCountingNo())) {
            return ExecuteDTO.ok(reqCardCountingConfigDTO.getCardCountingNo());
        } else {
            return ExecuteDTOUtil.error(CommonCode.CODE_10000003);
        }
    }

    private void addCardCountingConfig(ReqCardCountingConfigDTO reqCardCountingConfigDTO) {
        // 计次卡活动编号
        String cardCountingNo = MarketFormGenerator.genCardCountingNo();
        reqCardCountingConfigDTO.setCardCountingNo(cardCountingNo);
        reqCardCountingConfigDTO.setPromotionNo(cardCountingNo);

        // 存表card_counting_config
        CardCountingConfigDomain cardCountingConfigDomain = BeanCopierUtil.copy(reqCardCountingConfigDTO, CardCountingConfigDomain.class);
        cardCountingConfigDomain.setCardCountingNo(cardCountingNo);
        cardCountingConfigDao.insert(cardCountingConfigDomain);
        // 存表promotion_info
        PromotionInfoDomain promotionInfoDomain = BeanCopierUtil.copy(reqCardCountingConfigDTO, PromotionInfoDomain.class);
        promotionInfoDomain.setPromotionNo(cardCountingNo);
        promotionInfoDomain.setPromotionName(reqCardCountingConfigDTO.getCardCountingName());
        promotionInfoDomain.setSourceType(SourceTypeEnum.SOURCE_TYPE_1003.getCode());
        promotionInfoDomain.setPromotionType(PromotionTypeEnum.JCK.getCode());
        if (StringUtils.isBlank(promotionInfoDomain.getActivityType())) {
            promotionInfoDomain.setActivityType(ActivityTypeEnum.CARD_COUNTING.getCode());
        }
        promotionInfoDomain.setStatus(PromotionStatusEnum.IN_PROGRESS.getCode());
        if (WhetherEnum.YES.getCode().equals(promotionInfoDomain.getUpDownFlag())) {
            promotionInfoDomain.setLatestUpDownTime(LocalDateTime.now());
        }
        this.promotionInfoDao.insert(promotionInfoDomain);
        // 若为店铺，则添加
        PromotionStoreRelationDomain promotionStoreRelationDomain = BeanCopierUtil.copy(promotionInfoDomain, PromotionStoreRelationDomain.class);
        promotionStoreRelationDomain.setRuleNo(MarketFormGenerator.genStoreRuleNo());
        promotionStoreRelationDomain.setPromotionNo(cardCountingNo);
        promotionStoreRelationDomain.setUpDownFlag(WhetherEnum.NO.getCode());
        promotionStoreRelationDomain.setStoreSourceType(SourceTypeEnum.SOURCE_TYPE_1003.getCode());
        this.promotionStoreRelationDao.insert(promotionStoreRelationDomain);

        // 计次卡创建虚拟商品
        reqCardCountingConfigDTO.setPromotionNo(cardCountingNo);
        addOrUpdateVirtualGoods(reqCardCountingConfigDTO);

    }

    private void updateCardCountingConfig(ReqCardCountingConfigDTO reqCardCountingConfigDTO) {
        // 计次卡活动编号
        String cardCountingNo = reqCardCountingConfigDTO.getCardCountingNo();
        reqCardCountingConfigDTO.setPromotionNo(cardCountingNo);

        // 存表card_counting_config
        CardCountingConfigDomain cardCountingConfigDomain = BeanCopierUtil.copy(reqCardCountingConfigDTO, CardCountingConfigDomain.class);
        cardCountingConfigDomain.setCardCountingNo(cardCountingNo);
        cardCountingConfigDao.updateByParams(cardCountingConfigDomain);
        // 存表promotion_info
        PromotionInfoDomain promotionInfoDomain = BeanCopierUtil.copy(reqCardCountingConfigDTO, PromotionInfoDomain.class);
        promotionInfoDomain.setPromotionNo(cardCountingNo);
        promotionInfoDomain.setPromotionName(reqCardCountingConfigDTO.getCardCountingName());
        promotionInfoDomain.setPromotionType(PromotionTypeEnum.JCK.getCode());
        if (StringUtils.isBlank(promotionInfoDomain.getActivityType())) {
            promotionInfoDomain.setActivityType(ActivityTypeEnum.CARD_COUNTING.getCode());
        }
        promotionInfoDomain.setStatus(PromotionStatusEnum.IN_PROGRESS.getCode());
        if (WhetherEnum.YES.getCode().equals(promotionInfoDomain.getUpDownFlag())) {
            promotionInfoDomain.setLatestUpDownTime(LocalDateTime.now());
        }
        this.promotionInfoDao.updateByParams(promotionInfoDomain);

        // 计次卡创建虚拟商品
        reqCardCountingConfigDTO.setPromotionNo(cardCountingNo);
        addOrUpdateVirtualGoods(reqCardCountingConfigDTO);

    }

    private void addOrUpdateVirtualGoods(ReqCardCountingConfigDTO reqCardCountingConfigDTO) {
        QueryWrapper<PromotionVirtualGoodsRelationDomain> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("promotion_no", reqCardCountingConfigDTO.getPromotionNo());
        PromotionVirtualGoodsRelationDomain promotionVirtualGoodsRelationDomain = promotionVirtualGoodsRelationDao.selectOne(queryWrapper);
        if (null == promotionVirtualGoodsRelationDomain) {
            addVoucherVirtualGoods(reqCardCountingConfigDTO);
        } else {
            updateVoucherVirtualGoods(reqCardCountingConfigDTO, promotionVirtualGoodsRelationDomain);
        }
    }

    private void addVoucherVirtualGoods(ReqCardCountingConfigDTO reqCardCountingConfigDTO) {
        ReqGoodsDTO goodsDto = new ReqGoodsDTO();
        // 默认值
        goodsDto.setGoodsType(GoodsTypeEnum.VIRTUAL_GOODS.getCode());
        goodsDto.setVirtualGoodsType(VirtualGoodsTypeEnum.CARD_COUNTING.getCode());
        goodsDto.setGoodsSourceType(GoodsSourceTypeEnum.STORE.getCode());
        goodsDto.setBrandNo(GoodsConstant.DEFAULT_BRAND_NO);
        goodsDto.setCategoryNo(GoodsConstant.DEFAULT_CATEGORY_NO);
        goodsDto.setAppChannelSource(AppChannelSourceEnum.APP_BOSS_APP.getCode());
        goodsDto.setDeliveryWay(DeliveryWayEnum.SELF.getCode());
        goodsDto.setPaymentMethod(PaymentMethodEnum.ONLINEPAY.getCode());
        goodsDto.setGoodsForm(GoodsFormEnum.COMMON_GOODS.getCode());
        goodsDto.setLoginIdentity(IdentityEnum.STORE.getCode());
        // 默认上架
        goodsDto.setGoodsStatus(GoodsStatusEnum.ON_SHELF.getCode());
        // 默认计量单位
        goodsDto.setCalculationUnitNo(GoodsConstant.DEFAULT_CALCULATION_UNIT_NO);
        goodsDto.setCalculationUnitName(GoodsConstant.DEFAULT_CALCULATION_UNIT_NAME);
        goodsDto.setMainUnitNum(BigDecimal.ONE);
        // 是否启用串码，默认否
        goodsDto.setImeiFlag(WhetherEnum.NO.getCode());
        // 是否入仓，默认否
        goodsDto.setWarehouseFlag(WhetherEnum.NO.getCode());
        // 默认主图
        List<ReqGoodsPictureDTO> pictureList = new ArrayList<>(1);
        ReqGoodsPictureDTO reqGoodsPictureDTO = new ReqGoodsPictureDTO();
        reqGoodsPictureDTO.setPictureUrl(defaultVirtualGoodsImg);
        reqGoodsPictureDTO.setMainPictureFlag(WhetherEnum.YES.getCode());
        pictureList.add(reqGoodsPictureDTO);
        goodsDto.setPictureList(pictureList);

        // 创建人&门店信息
        goodsDto.setCreateNo(reqCardCountingConfigDTO.getCreateNo());
        goodsDto.setCreateName(reqCardCountingConfigDTO.getCreateName());
        goodsDto.setModifyNo(reqCardCountingConfigDTO.getModifyNo());
        goodsDto.setModifyName(reqCardCountingConfigDTO.getModifyName());
        goodsDto.setMerchantNo(reqCardCountingConfigDTO.getMerchantNo());
        goodsDto.setMerchantName(reqCardCountingConfigDTO.getMerchantName());
        goodsDto.setStoreNo(reqCardCountingConfigDTO.getStoreNo());
        goodsDto.setStoreName(reqCardCountingConfigDTO.getStoreName());

        goodsDto.setGoodsName(reqCardCountingConfigDTO.getCardCountingName());
        goodsDto.setThirdGoodsNo(reqCardCountingConfigDTO.getCardCountingNo());
        goodsDto.setRetailPrice(reqCardCountingConfigDTO.getSalePrice());
        goodsDto.setRealStockNum(new BigDecimal(String.valueOf(reqCardCountingConfigDTO.getTotalStockNum())));

        ExecuteDTO<ResGoodsModifyStatusDTO> executeDTO = goodsOperatService.addGoods(goodsDto);
        log.info("goodsOperatService.addGoods return executeDTO:{}", executeDTO);
        if (null != executeDTO && executeDTO.successFlag()) {
            String goodsNo = executeDTO.getData().getGoodsNo();
            PromotionVirtualGoodsRelationDomain promotionVirtualGoodsRelationDomain = BeanCopierUtil.copy(reqCardCountingConfigDTO, PromotionVirtualGoodsRelationDomain.class);
            promotionVirtualGoodsRelationDomain.setGoodsNo(goodsNo);
            promotionVirtualGoodsRelationDomain.setTotalNum(reqCardCountingConfigDTO.getTotalStockNum());
            promotionVirtualGoodsRelationDao.insert(promotionVirtualGoodsRelationDomain);
        }
    }

    private void updateVoucherVirtualGoods(ReqCardCountingConfigDTO reqCardCountingConfigDTO, PromotionVirtualGoodsRelationDomain promotionVirtualGoodsRelationDomain) {
        ReqGoodsDTO goodsDto = new ReqGoodsDTO();
        // 默认值
        goodsDto.setGoodsType(GoodsTypeEnum.VIRTUAL_GOODS.getCode());
        goodsDto.setVirtualGoodsType(VirtualGoodsTypeEnum.CARD_COUNTING.getCode());
        goodsDto.setGoodsSourceType(GoodsSourceTypeEnum.STORE.getCode());
        goodsDto.setBrandNo(GoodsConstant.DEFAULT_BRAND_NO);
        goodsDto.setCategoryNo(GoodsConstant.DEFAULT_CATEGORY_NO);
        goodsDto.setAppChannelSource(AppChannelSourceEnum.APP_BOSS_APP.getCode());
        goodsDto.setDeliveryWay(DeliveryWayEnum.SELF.getCode());
        goodsDto.setPaymentMethod(PaymentMethodEnum.ONLINEPAY.getCode());
        goodsDto.setGoodsForm(GoodsFormEnum.COMMON_GOODS.getCode());
        goodsDto.setLoginIdentity(IdentityEnum.STORE.getCode());
        // 默认上架
        goodsDto.setGoodsStatus(GoodsStatusEnum.ON_SHELF.getCode());
        // 默认计量单位
        goodsDto.setCalculationUnitNo(GoodsConstant.DEFAULT_CALCULATION_UNIT_NO);
        goodsDto.setCalculationUnitName(GoodsConstant.DEFAULT_CALCULATION_UNIT_NAME);
        goodsDto.setMainUnitNum(BigDecimal.ONE);
        // 是否启用串码，默认否
        goodsDto.setImeiFlag(WhetherEnum.NO.getCode());
        // 是否入仓，默认否
        goodsDto.setWarehouseFlag(WhetherEnum.NO.getCode());
        // 默认主图
        List<ReqGoodsPictureDTO> pictureList = new ArrayList<>(1);
        ReqGoodsPictureDTO reqGoodsPictureDTO = new ReqGoodsPictureDTO();
        reqGoodsPictureDTO.setPictureUrl(defaultVirtualGoodsImg);
        reqGoodsPictureDTO.setMainPictureFlag(WhetherEnum.YES.getCode());
        pictureList.add(reqGoodsPictureDTO);
        goodsDto.setPictureList(pictureList);

        // 修改人
        goodsDto.setModifyNo(reqCardCountingConfigDTO.getModifyNo());
        goodsDto.setModifyName(reqCardCountingConfigDTO.getModifyName());

        goodsDto.setGoodsNo(promotionVirtualGoodsRelationDomain.getGoodsNo());
        goodsDto.setGoodsName(reqCardCountingConfigDTO.getCardCountingName());
        goodsDto.setRetailPrice(reqCardCountingConfigDTO.getSalePrice());
        // 重新计算库存
        Integer totalNum = promotionVirtualGoodsRelationDomain.getTotalNum();
        int totalNumChange = reqCardCountingConfigDTO.getTotalStockNum() - totalNum;
        goodsDto.setRealStockNumChange(new BigDecimal(String.valueOf(totalNumChange)));

        ExecuteDTO<ResGoodsModifyStatusDTO> executeDTO = goodsOperatService.modifyGoods(goodsDto);
        log.info("goodsOperatService.modifyGoods return executeDTO:{}", executeDTO);
        if (null != executeDTO && executeDTO.successFlag()) {
            PromotionVirtualGoodsRelationVo updateVo = new PromotionVirtualGoodsRelationVo();
            updateVo.setTotalNum(reqCardCountingConfigDTO.getTotalStockNum());
            updateVo.setPromotionNo(reqCardCountingConfigDTO.getPromotionNo());
            promotionVirtualGoodsRelationDao.updateTotalNum(updateVo);
        }
    }

    @Override
    public ExecuteDTO<String> deleteCardCountingConfig(ReqCardCountingConfigDTO reqCouponSettingDTO) {
        String cardCountingNo = reqCouponSettingDTO.getCardCountingNo();
        if (StringUtils.isNotBlank(cardCountingNo)) {
            this.cardCountingConfigDao.deleteByCardCountingNo(cardCountingNo);
            this.promotionInfoDao.updateDeleteFlag(cardCountingNo);
            return ExecuteDTO.ok(cardCountingNo);
        } else {
            return ExecuteDTOUtil.error(CommonCode.CODE_10000001, "计次卡编号");
        }
    }
}
