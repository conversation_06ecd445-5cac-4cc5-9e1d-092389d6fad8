package cn.htdt.marketprocess.biz.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketprocess.api.operat.PromotionBrowsingRecordOperatService;
import cn.htdt.marketprocess.biz.conversion.PromotionBrowsingRecordAssert;
import cn.htdt.marketprocess.dao.PromotionBrowsingRecordDao;
import cn.htdt.marketprocess.domain.PromotionBrowsingRecordDomain;
import cn.htdt.marketprocess.dto.request.ReqPromotionBrowsingRecordDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * 活动浏览记录表
 *
 * <AUTHOR>
 */
@DubboService
@Slf4j
public class PromotionBrowsingRecordOperatServiceImpl implements PromotionBrowsingRecordOperatService {

    @Resource
    private PromotionBrowsingRecordDao promotionBrowsingRecordDao;

    @Resource
    private PromotionBrowsingRecordAssert promotionBrowsingRecordAssert;

    /**
     * 汇享购 -> 获取接龙活动详情 -> 保存粉丝活动浏览记录
     *
     * @param reqDTO 请求参数
     * @return ExecuteDTO<String>
     */
    @Override
    public ExecuteDTO<String> saveFansPromotionBrowseRecord(ReqPromotionBrowsingRecordDTO reqDTO) {
        log.info("-------PromotionBrowsingRecordOperatServiceImpl-->saveFansPromotionBrowseRecord,保存粉丝浏览记录--start--入参：{}--", reqDTO);
        promotionBrowsingRecordAssert.saveFansBrowseRecordAssert(reqDTO);
        PromotionBrowsingRecordDomain domain = BeanCopierUtil.copy(reqDTO, PromotionBrowsingRecordDomain.class);
        if (StringUtils.isBlank(domain.getStoreNo())) {
            domain.setStoreNo("");
        }
        log.info("-------promotionBrowsingRecordDao.savePromotionBrowsingRecord----入参：{}--", domain);
        promotionBrowsingRecordDao.savePromotionBrowsingRecord(domain);

        return ExecuteDTO.ok();
    }
}
