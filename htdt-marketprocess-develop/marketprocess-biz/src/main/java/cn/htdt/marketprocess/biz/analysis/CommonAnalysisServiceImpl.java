package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.base.exception.BaseException;
import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.goods.GoodsFormEnum;
import cn.htdt.common.enums.goods.SeriesTypeEnum;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.CloudPoolCommissionCalculationUtil;
import cn.htdt.goodscenter.dto.request.AtomReqGoodsDTO;
import cn.htdt.goodscenter.dto.request.AtomReqGoodsRealStockDTO;
import cn.htdt.goodscenter.dto.response.AtomResGoodsDTO;
import cn.htdt.goodscenter.dto.response.AtomResGoodsRealStockDTO;
import cn.htdt.goodsprocess.api.analysis.CloudPoolApplyAnalysisService;
import cn.htdt.goodsprocess.api.analysis.GoodsAnalysisService;
import cn.htdt.goodsprocess.api.operat.LegacyGoodsCenterService;
import cn.htdt.goodsprocess.dto.response.goods.ResGoodsDTO;
import cn.htdt.goodsprocess.dto.response.goods.ResSeriesGoodsDTO;
import cn.htdt.goodsprocess.dto.response.goods.ResSubGoodsDTO;
import cn.htdt.marketcenter.dto.request.AtomReqCloudPoolGoodsCommissionConfigDTO;
import cn.htdt.marketcenter.dto.response.AtomResCloudPoolGoodsCommissionConfigDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomAgentMarketRewardsetAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomCloudPoolGoodsCommissionConfigAnalysisService;
import cn.htdt.userprocess.api.UserPublicService;
import cn.htdt.userprocess.dto.publicDto.response.GetStoreListResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CommonAnalysisServiceImpl {

    @DubboReference
    private LegacyGoodsCenterService legacyGoodsCenterService;

    /**
     * 酬劳
     */
    @Resource
    private AtomAgentMarketRewardsetAnalysisService atomAgentMarketRewardsetAnalysisService;

    @DubboReference
    private CloudPoolApplyAnalysisService cloudPoolApplyAnalysisService;

    @DubboReference
    private GoodsAnalysisService goodsAnalysisService;

    @DubboReference
    private UserPublicService userPublicService;

    @Resource
    private AtomCloudPoolGoodsCommissionConfigAnalysisService atomCloudPoolGoodsCommissionConfigAnalysisService;


    protected void getStoreNameList(List<ResGoodsDTO> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            //获取有店铺编码商品
            List<ResGoodsDTO> storeGoodsDTOList = list.stream().filter(goodsDTO -> StringUtils.isNotBlank(goodsDTO.getStoreNo())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(storeGoodsDTOList)) {
                List<String> storeNoList = storeGoodsDTOList.stream().map(ResGoodsDTO::getStoreNo).collect(Collectors.toList());
                //获取店铺集合
                ExecuteDTO<List<GetStoreListResponse>> storeDTO = userPublicService.queryStoreList(storeNoList);
                if (storeDTO.successFlag() && CollectionUtils.isNotEmpty(storeDTO.getData())) {
                    storeGoodsDTOList.forEach(resDTO -> {
                        Optional<GetStoreListResponse> optional = storeDTO.getData().stream().filter(store -> resDTO.getStoreNo().equals(store.getStoreNo())).findFirst();
                        optional.ifPresent(getStoreListResponse -> resDTO.setStoreName(getStoreListResponse.getStoreName()));
                    });
                }
            }
        }
    }

    /**
     * 获取非主品商品库存集合（包括子品和普通）
     *
     * @param goodsDTOList
     * @param warehouseFlag true 查询仓库，false 不查询仓库
     */
    protected void getNotSeriesGoodsStockList(List<ResGoodsDTO> goodsDTOList, Boolean warehouseFlag) {
        try {
            if (CollectionUtils.isNotEmpty(goodsDTOList)) {
                List<String> goodsNoList = goodsDTOList.stream().map(ResGoodsDTO::getOriginalGoodsNo).collect(Collectors.toList());
                AtomReqGoodsRealStockDTO realStockDto = new AtomReqGoodsRealStockDTO();
                realStockDto.setGoodsNoList(goodsNoList);
                ExecuteDTO<List<AtomResGoodsRealStockDTO>> notSeriesExecuteDTO = legacyGoodsCenterService.getBatchGoodsRealStockByNos(realStockDto);
                if (notSeriesExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(notSeriesExecuteDTO.getData())) {
                    goodsDTOList.forEach(subDTO -> {
                        //查询商品库存到商品对象信息里面
                        Optional<AtomResGoodsRealStockDTO> realStockDTO = notSeriesExecuteDTO.getData().stream().filter(resStockDTO ->
                                StringUtils.equals(resStockDTO.getGoodsNo(), subDTO.getOriginalGoodsNo())).findFirst();
                        if (realStockDTO.isPresent()) {
                            subDTO.setRealStockNum(realStockDTO.get().getRealStockNum());
                            subDTO.setCanSaleStockNum(realStockDTO.get().getAvailableStockNum());
                        }
                    });
                    //查询仓库名
                    if (warehouseFlag) {
//                        this.getWarehouseNameList(goodsDTOList);
                    }
                }
            }
        } catch (Exception e) {
            log.error(String.format("查询非主品商品列表库存报错-入参--%s--%s--", goodsDTOList, warehouseFlag), e);
        }
    }

    /**
     * 获取主品商品库存集合（取子品库存的区间）
     *
     * @param goodsDTOList
     * @param warehouseFlag true 查询仓库，false 不查询仓库
     */
    protected void getSeriesGoodsStockList(List<ResGoodsDTO> goodsDTOList, Boolean warehouseFlag) {
        try {
            if (CollectionUtils.isNotEmpty(goodsDTOList)) {
                AtomReqGoodsDTO atomReqGoodsDTO = new AtomReqGoodsDTO();
                atomReqGoodsDTO.setSeriesType(SeriesTypeEnum.SUB_GOODS.getCode());
                atomReqGoodsDTO.setParentGoodsNos(goodsDTOList.stream().map(ResGoodsDTO::getGoodsNo).collect(Collectors.toList()));
                atomReqGoodsDTO.setGoodsForm(GoodsFormEnum.SERIES_GOODS.getCode());
                //不分页
                atomReqGoodsDTO.setNoPage();
                //获取子品列表
                ExecuteDTO<ExecutePageDTO<AtomResGoodsDTO>> goodsDTO = legacyGoodsCenterService.getGoodsPage(atomReqGoodsDTO);
                if (goodsDTO.successFlag() && CollectionUtils.isNotEmpty(goodsDTO.getData().getRows())) {
                    List<String> goodsNoList = goodsDTO.getData().getRows().stream().map(AtomResGoodsDTO::getOriginalGoodsNo).collect(Collectors.toList());
                    //有子品再判断是否维护过库存
                    AtomReqGoodsRealStockDTO realStockDto = new AtomReqGoodsRealStockDTO();
                    realStockDto.setGoodsNoList(goodsNoList);
                    ExecuteDTO<List<AtomResGoodsRealStockDTO>> seriesExecuteDTO = legacyGoodsCenterService.getBatchGoodsRealStockByNos(realStockDto);
                    goodsDTOList.forEach(subDTO -> {
                        //通过原始商品编码匹配出该主品下的子品
                        List<AtomResGoodsDTO> goodsList = goodsDTO.getData().getRows().stream().filter(goods ->
                                StringUtils.equals(goods.getParentGoodsNo(), subDTO.getGoodsNo())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(goodsList)) {
                            //获取最小市场价
                            Optional<AtomResGoodsDTO> minMarketPrice = goodsList.stream().min(Comparator.comparing(AtomResGoodsDTO::getMarketPrice));
                            minMarketPrice.ifPresent(atomResGoodsDTO -> subDTO.setMinMarketPrice(atomResGoodsDTO.getMarketPrice()));
                            //获取最大市场价
                            Optional<AtomResGoodsDTO> maxMarketPrice = goodsList.stream().max(Comparator.comparing(AtomResGoodsDTO::getMarketPrice));
                            maxMarketPrice.ifPresent(atomResGoodsDTO -> subDTO.setMaxMarketPrice(atomResGoodsDTO.getMarketPrice()));
                            //获取最小采购价
                            Optional<AtomResGoodsDTO> minPurchasePrice = goodsList.stream().min(Comparator.comparing(AtomResGoodsDTO::getPurchasePrice));
                            minPurchasePrice.ifPresent(atomResGoodsDTO -> subDTO.setMinPurchasePrice(atomResGoodsDTO.getPurchasePrice()));
                            //获取最大采购价
                            Optional<AtomResGoodsDTO> maxPurchasePrice = goodsList.stream().max(Comparator.comparing(AtomResGoodsDTO::getPurchasePrice));
                            maxPurchasePrice.ifPresent(atomResGoodsDTO -> subDTO.setMaxPurchasePrice(atomResGoodsDTO.getPurchasePrice()));
                            //获取最小零售价
                            Optional<AtomResGoodsDTO> minRetailPrice = goodsList.stream().min(Comparator.comparing(AtomResGoodsDTO::getRetailPrice));
                            minRetailPrice.ifPresent(atomResGoodsDTO -> subDTO.setMinRetailPrice(atomResGoodsDTO.getRetailPrice()));
                            //获取最大零售价
                            Optional<AtomResGoodsDTO> maxRetailPrice = goodsList.stream().max(Comparator.comparing(AtomResGoodsDTO::getRetailPrice));
                            maxRetailPrice.ifPresent(atomResGoodsDTO -> subDTO.setMaxRetailPrice(atomResGoodsDTO.getRetailPrice()));
                            //获取最小云池供货价
                            Optional<AtomResGoodsDTO> minCloudPoolSupplyPrice = goodsList.stream().min(Comparator.comparing(AtomResGoodsDTO::getCloudPoolSupplyPrice));
                            minCloudPoolSupplyPrice.ifPresent(atomResGoodsDTO -> subDTO.setMinCloudPoolSupplyPrice(atomResGoodsDTO.getCloudPoolSupplyPrice()));
                            //获取最大云池供货价
                            Optional<AtomResGoodsDTO> maxCloudPoolSupplyPrice = goodsList.stream().max(Comparator.comparing(AtomResGoodsDTO::getCloudPoolSupplyPrice));
                            maxCloudPoolSupplyPrice.ifPresent(atomResGoodsDTO -> subDTO.setMaxCloudPoolSupplyPrice(atomResGoodsDTO.getCloudPoolSupplyPrice()));
                            //销量总数
                            /*
                            subDTO.setSalesVolume(goodsList.stream().map(AtomResGoodsDTO::getSalesVolume).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
                            */
                            //将子品的信息赋值到列表子品下
                            List<ResSubGoodsDTO> subGoodsDTOS = new ArrayList<>();
                            goodsList.forEach(subGoodsDTO -> {
                                ResSubGoodsDTO resSubGoodsDTO = BeanCopierUtil.copy(subGoodsDTO, ResSubGoodsDTO.class);
                                if (seriesExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(seriesExecuteDTO.getData())) {
                                    //查询商品库存到商品对象信息里面,通过子品原始商品编码匹配
                                    Optional<AtomResGoodsRealStockDTO> stockOptional = seriesExecuteDTO.getData().stream().filter(resStockDTO ->
                                            StringUtils.equals(resStockDTO.getGoodsNo(), subGoodsDTO.getOriginalGoodsNo())).findFirst();
                                    if (stockOptional.isPresent()) {
                                        //获取总和
                                        resSubGoodsDTO.setRealStockNum(stockOptional.get().getRealStockNum());
                                        resSubGoodsDTO.setCanSaleStockNum(stockOptional.get().getAvailableStockNum());
                                        if (subDTO.getRealStockNum() != null) {
                                            subDTO.setRealStockNum(subDTO.getRealStockNum().add(stockOptional.get().getRealStockNum()));
                                        } else {
                                            subDTO.setRealStockNum(stockOptional.get().getRealStockNum());
                                        }
                                        if (subDTO.getCanSaleStockNum() != null) {
                                            subDTO.setCanSaleStockNum(subDTO.getCanSaleStockNum().add(stockOptional.get().getAvailableStockNum()));
                                        } else {
                                            subDTO.setCanSaleStockNum(stockOptional.get().getAvailableStockNum());
                                        }
                                    }
                                }
                                subGoodsDTOS.add(resSubGoodsDTO);
                            });
                            ResSeriesGoodsDTO seriesGoodsDTO = new ResSeriesGoodsDTO();
                            seriesGoodsDTO.setSubGoodsList(subGoodsDTOS);
                            subDTO.setSeriesGoods(seriesGoodsDTO);
                        }
                    });
                    //查询仓库名
                    if (warehouseFlag) {
//                        this.getWarehouseNameList(goodsDTOList);
                    }
                }
            }
        } catch (Exception e) {
            log.error(String.format("查询主品商品列表库存报错-入参--%s--%s--", goodsDTOList, warehouseFlag), e);
        }
    }

    /**
     * 获取云池商品佣金
     *
     * @param goodsDTOList 商品集合
     */
    protected void getCloudPoolPriceAndCommissionList(List<ResGoodsDTO> goodsDTOList) throws Exception {
        try {
            if (org.apache.dubbo.common.utils.CollectionUtils.isEmpty(goodsDTOList)) {
                //商品集合为空
                return;
            }

            AtomReqGoodsDTO atomReqGoodsDTO = new AtomReqGoodsDTO();
            atomReqGoodsDTO.setSeriesType(SeriesTypeEnum.SUB_GOODS.getCode());
            atomReqGoodsDTO.setParentGoodsNos(goodsDTOList.stream().map(ResGoodsDTO::getGoodsNo).collect(Collectors.toList()));
            atomReqGoodsDTO.setGoodsForm(GoodsFormEnum.SERIES_GOODS.getCode());
            //不分页
            atomReqGoodsDTO.setNoPage();
            //获取子品列表
            ExecuteDTO<ExecutePageDTO<AtomResGoodsDTO>> goodsDTO = legacyGoodsCenterService.getGoodsPage(atomReqGoodsDTO);

            //取出商品编号
            List<String> goodsNos = goodsDTOList.stream().map(ResGoodsDTO::getGoodsNo).collect(Collectors.toList());

            //获取云池商品佣金
            AtomReqCloudPoolGoodsCommissionConfigDTO atomReqCloudPoolGoodsCommissionConfigDTO = new AtomReqCloudPoolGoodsCommissionConfigDTO();
            atomReqCloudPoolGoodsCommissionConfigDTO.setParentGoodsNoList(goodsNos);
            List<AtomResCloudPoolGoodsCommissionConfigDTO> cloudPoolGoodsCommissionConfigDTOS = this.getCloudPoolGoodsCommissionList(atomReqCloudPoolGoodsCommissionConfigDTO);
            if (org.apache.dubbo.common.utils.CollectionUtils.isEmpty(cloudPoolGoodsCommissionConfigDTOS)) {
                //云池商品佣金集合为空
                return;
            }
            goodsDTOList.forEach(resGoodsDTO -> {
                if (GoodsFormEnum.SERIES_GOODS.getCode().equals(resGoodsDTO.getGoodsForm())) {
                    //系列商品
                    List<AtomResCloudPoolGoodsCommissionConfigDTO> commissionConfigDTOList = cloudPoolGoodsCommissionConfigDTOS.stream()
                            .filter(commissionConfigDTO -> StringUtils.equals(commissionConfigDTO.getParentGoodsNo(), resGoodsDTO.getGoodsNo()))
                            .collect(Collectors.toList());
                    if (org.apache.dubbo.common.utils.CollectionUtils.isNotEmpty(commissionConfigDTOList)) {
                        //获取最小云池市场价
                        Optional<AtomResCloudPoolGoodsCommissionConfigDTO> minCloudPoolMarketPrice = commissionConfigDTOList.stream()
                                .min(Comparator.comparing(AtomResCloudPoolGoodsCommissionConfigDTO::getMarketPrice));
                        minCloudPoolMarketPrice.ifPresent(atomResCloudPoolGoodsCommissionConfigDTO -> resGoodsDTO.setMinCloudPoolMarketPrice(
                                atomResCloudPoolGoodsCommissionConfigDTO.getMarketPrice()));
                        //获取最大云池市场价
                        Optional<AtomResCloudPoolGoodsCommissionConfigDTO> maxCloudPoolMarketPrice = commissionConfigDTOList.stream()
                                .max(Comparator.comparing(AtomResCloudPoolGoodsCommissionConfigDTO::getMarketPrice));
                        maxCloudPoolMarketPrice.ifPresent(atomResCloudPoolGoodsCommissionConfigDTO -> resGoodsDTO.setMaxCloudPoolMarketPrice(
                                atomResCloudPoolGoodsCommissionConfigDTO.getMarketPrice()));
                        //获取最小云池零售价
                        Optional<AtomResCloudPoolGoodsCommissionConfigDTO> minCloudPoolRetailPrice = commissionConfigDTOList.stream()
                                .min(Comparator.comparing(AtomResCloudPoolGoodsCommissionConfigDTO::getRetailPrice));
                        minCloudPoolRetailPrice.ifPresent(atomResCloudPoolGoodsCommissionConfigDTO -> resGoodsDTO.setMinCloudPoolRetailPrice(
                                atomResCloudPoolGoodsCommissionConfigDTO.getRetailPrice()));
                        //获取最大云池零售价
                        Optional<AtomResCloudPoolGoodsCommissionConfigDTO> maxCloudPoolRetailPrice = commissionConfigDTOList.stream()
                                .max(Comparator.comparing(AtomResCloudPoolGoodsCommissionConfigDTO::getRetailPrice));
                        maxCloudPoolRetailPrice.ifPresent(atomResCloudPoolGoodsCommissionConfigDTO -> resGoodsDTO.setMaxCloudPoolRetailPrice(
                                atomResCloudPoolGoodsCommissionConfigDTO.getRetailPrice()));

                        //最小代理人佣金比例（%）
                        Optional<AtomResCloudPoolGoodsCommissionConfigDTO> minAgentCommissionRatio = commissionConfigDTOList.stream()
                                .min(Comparator.comparing(AtomResCloudPoolGoodsCommissionConfigDTO::getAgentCommissionRatio));
                        minAgentCommissionRatio.ifPresent(atomResCloudPoolGoodsCommissionConfigDTO -> resGoodsDTO.setMinAgentCommissionRatio(
                                atomResCloudPoolGoodsCommissionConfigDTO.getAgentCommissionRatio()));

                        //最大代理人佣金比例（%）
                        Optional<AtomResCloudPoolGoodsCommissionConfigDTO> maxAgentCommissionRatio = commissionConfigDTOList.stream()
                                .max(Comparator.comparing(AtomResCloudPoolGoodsCommissionConfigDTO::getAgentCommissionRatio));
                        maxAgentCommissionRatio.ifPresent(atomResCloudPoolGoodsCommissionConfigDTO -> resGoodsDTO.setMaxAgentCommissionRatio(
                                atomResCloudPoolGoodsCommissionConfigDTO.getAgentCommissionRatio()));

                        //最小分销店佣金比例（%）
                        Optional<AtomResCloudPoolGoodsCommissionConfigDTO> minDistributionStoreCommissionRatio = commissionConfigDTOList.stream()
                                .min(Comparator.comparing(AtomResCloudPoolGoodsCommissionConfigDTO::getDistributionStoreCommissionRatio));
                        minDistributionStoreCommissionRatio.ifPresent(atomResCloudPoolGoodsCommissionConfigDTO -> resGoodsDTO.setMinDistributionStoreCommissionRatio(
                                atomResCloudPoolGoodsCommissionConfigDTO.getDistributionStoreCommissionRatio()));

                        //最大分销店佣金比例（%）
                        Optional<AtomResCloudPoolGoodsCommissionConfigDTO> maxDistributionStoreCommissionRatio = commissionConfigDTOList.stream()
                                .max(Comparator.comparing(AtomResCloudPoolGoodsCommissionConfigDTO::getDistributionStoreCommissionRatio));
                        maxDistributionStoreCommissionRatio.ifPresent(atomResCloudPoolGoodsCommissionConfigDTO -> resGoodsDTO.setMaxDistributionStoreCommissionRatio(
                                atomResCloudPoolGoodsCommissionConfigDTO.getDistributionStoreCommissionRatio()));

                        //最小代理人佣金
                        BigDecimal minAgentCommission = null;
                        //最大代理人佣金
                        BigDecimal maxAgentCommission = null;
                        //最小分销店佣金
                        BigDecimal minDistributionStoreCommission = null;
                        //最大分销店佣金
                        BigDecimal maxDistributionStoreCommission = null;
                        //最小平台服务费
                        BigDecimal minPlatformServiceCommission = null;
                        //最大平台服务费
                        BigDecimal maxPlatformServiceCommission = null;
                        for (AtomResCloudPoolGoodsCommissionConfigDTO commissionConfigDTO : commissionConfigDTOList) {
                            if (!goodsDTO.successFlag() || org.apache.dubbo.common.utils.CollectionUtils.isEmpty(goodsDTO.getData().getRows())) {
                                //无系列商品子品信息，直接退出云池
                                break;
                            }
                            List<AtomResGoodsDTO> atomResGoodsDTOList = goodsDTO.getData().getRows();

                            Optional<AtomResGoodsDTO> optional = atomResGoodsDTOList.stream().filter(atomResGoodsDTO -> StringUtils.equals(atomResGoodsDTO.getGoodsNo(), commissionConfigDTO.getGoodsNo())).findFirst();
                            if (!optional.isPresent()) {
                                //未找到当前子品的子品基础信息，退出当前循环
                                continue;
                            }
                            //毛利润
                            BigDecimal grossProfit = CloudPoolCommissionCalculationUtil.getGrossProfit(
                                    optional.get().getCloudPoolSupplyPrice(),
                                    commissionConfigDTO.getRetailPrice(),
                                    commissionConfigDTO.getActivityExpenses()
                            );
                            //计算代理人佣金
                            BigDecimal agentCommission = CloudPoolCommissionCalculationUtil.getCommission(commissionConfigDTO.getAgentCommissionRatio(), grossProfit);
                            if (minAgentCommission == null) {
                                minAgentCommission = agentCommission;
                                maxAgentCommission = agentCommission;
                            } else if (agentCommission.compareTo(minAgentCommission) < NumConstant.ZERO) {
                                minAgentCommission = agentCommission;
                            } else if (agentCommission.compareTo(maxAgentCommission) > NumConstant.ZERO) {
                                maxAgentCommission = agentCommission;
                            }
                            //计算分销店佣金
                            BigDecimal distributionStoreCommission = CloudPoolCommissionCalculationUtil.getCommission(commissionConfigDTO.getDistributionStoreCommissionRatio(), grossProfit);
                            if (minDistributionStoreCommission == null) {
                                minDistributionStoreCommission = distributionStoreCommission;
                                maxDistributionStoreCommission = distributionStoreCommission;
                            } else if (distributionStoreCommission.compareTo(minDistributionStoreCommission) < NumConstant.ZERO) {
                                minDistributionStoreCommission = distributionStoreCommission;
                            } else if (distributionStoreCommission.compareTo(maxDistributionStoreCommission) > NumConstant.ZERO) {
                                maxDistributionStoreCommission = distributionStoreCommission;
                            }
                            //计算平台服务费
                            BigDecimal platformServiceCommission = CloudPoolCommissionCalculationUtil.getCommission(commissionConfigDTO.getPlatformServiceCommissionRatio(), grossProfit);
                            if (minPlatformServiceCommission == null) {
                                minPlatformServiceCommission = platformServiceCommission;
                                maxPlatformServiceCommission = platformServiceCommission;
                            } else if (platformServiceCommission.compareTo(minPlatformServiceCommission) < NumConstant.ZERO) {
                                minPlatformServiceCommission = platformServiceCommission;
                            } else if (platformServiceCommission.compareTo(minPlatformServiceCommission) > NumConstant.ZERO) {
                                maxPlatformServiceCommission = distributionStoreCommission;
                            }

                        }
                        //最小代理人佣金
                        resGoodsDTO.setMinAgentCommission(minAgentCommission);
                        //最大代理人佣金
                        resGoodsDTO.setMaxAgentCommission(maxAgentCommission);
                        //最小分销店佣金
                        resGoodsDTO.setMinDistributionStoreCommission(minDistributionStoreCommission);
                        //最大分销店佣金
                        resGoodsDTO.setMaxDistributionStoreCommission(maxDistributionStoreCommission);
                        //最小平台服务费
                        resGoodsDTO.setMinPlatformServiceCommission(minPlatformServiceCommission);
                        //最大平台服务费
                        resGoodsDTO.setMaxPlatformServiceCommission(maxPlatformServiceCommission);
                    }
                } else {
                    //非系列商品
                    Optional<AtomResCloudPoolGoodsCommissionConfigDTO> commissionConfigOptional = cloudPoolGoodsCommissionConfigDTOS.stream()
                            .filter(commissionConfigDTO -> StringUtils.equals(commissionConfigDTO.getParentGoodsNo(), resGoodsDTO.getGoodsNo())).findFirst();
                    if (commissionConfigOptional.isPresent()) {
                        AtomResCloudPoolGoodsCommissionConfigDTO commissionConfigDTO = commissionConfigOptional.get();
                        //云池零售价
                        resGoodsDTO.setCloudPoolRetailPrice(commissionConfigOptional.get().getRetailPrice());
                        //云池市场价
                        resGoodsDTO.setCloudPoolMarketPrice(commissionConfigOptional.get().getMarketPrice());

                        //毛利润
                        BigDecimal grossProfit = CloudPoolCommissionCalculationUtil.getGrossProfit(
                                resGoodsDTO.getCloudPoolSupplyPrice(),
                                commissionConfigDTO.getRetailPrice(),
                                commissionConfigDTO.getActivityExpenses()
                        );
                        //代理人佣金比例（%）
                        resGoodsDTO.setAgentCommissionRatio(commissionConfigDTO.getAgentCommissionRatio());
                        //代理人佣金
                        resGoodsDTO.setAgentCommission(
                                CloudPoolCommissionCalculationUtil.getCommission(commissionConfigDTO.getAgentCommissionRatio(), grossProfit)
                        );
                        //分销店佣金比例（%）
                        resGoodsDTO.setDistributionStoreCommissionRatio(commissionConfigDTO.getDistributionStoreCommissionRatio());
                        //分销店佣金
                        resGoodsDTO.setDistributionStoreCommission(
                                CloudPoolCommissionCalculationUtil.getCommission(commissionConfigDTO.getDistributionStoreCommissionRatio(), grossProfit)
                        );
                        //平台服务费
                        resGoodsDTO.setPlatformServiceCommission(
                                CloudPoolCommissionCalculationUtil.getCommission(commissionConfigDTO.getPlatformServiceCommissionRatio(), grossProfit)
                        );
                    }
                }
            });
        } catch (Exception e) {
            log.error(String.format("查询商品云池价格与佣金列表报错-入参--%s--", goodsDTOList), e);
            throw e;
        }
    }

    /**
     * 获取云池商品佣金集合
     *
     * @param atomReqDTO 查询参数
     * @return List<AtomResCloudPoolGoodsCommissionConfigDTO>
     */
    private List<AtomResCloudPoolGoodsCommissionConfigDTO> getCloudPoolGoodsCommissionList(AtomReqCloudPoolGoodsCommissionConfigDTO atomReqDTO) {
        ExecuteDTO<List<AtomResCloudPoolGoodsCommissionConfigDTO>> executeDTO = atomCloudPoolGoodsCommissionConfigAnalysisService.getListConfigInfoByParam(atomReqDTO);
        checkExecuteDTO(executeDTO);

        return executeDTO.getData();
    }

    /**
     * 校验返回参数
     *
     * @param executeDTO 返回返回参数
     * <AUTHOR>
     */
    private void checkExecuteDTO(ExecuteDTO executeDTO) {
        if (executeDTO == null) {
            throw new BaseException(CommonCode.CODE_10000003);
        }
        if (!executeDTO.successFlag()) {
            throw new BaseException(executeDTO.getStatus(), executeDTO.getMsg());
        }
    }

}
