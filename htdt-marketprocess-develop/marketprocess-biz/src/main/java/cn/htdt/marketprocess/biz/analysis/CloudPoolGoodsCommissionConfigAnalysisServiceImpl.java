package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqCloudPoolGoodsCommissionConfigDTO;
import cn.htdt.marketcenter.dto.response.AtomResCloudPoolGoodsCommissionConfigDTO;
import cn.htdt.marketprocess.api.analysis.CloudPoolGoodsCommissionConfigAnalysisService;
import cn.htdt.marketprocess.biz.conversion.CloudPoolGoodsCommissionConfigAssert;
import cn.htdt.marketprocess.dto.request.ReqCloudPoolGoodsCommissionConfigDTO;
import cn.htdt.marketprocess.dto.response.ResCloudPoolGoodsCommissionConfigDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomCloudPoolGoodsCommissionConfigAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-01-21
 * @Description 云池商品佣金配置查询
 **/
@Slf4j
@DubboService
public class CloudPoolGoodsCommissionConfigAnalysisServiceImpl implements CloudPoolGoodsCommissionConfigAnalysisService {
    @Autowired
    private CloudPoolGoodsCommissionConfigAssert configAssert;
    @Resource
    private AtomCloudPoolGoodsCommissionConfigAnalysisService configAnalysisService;

    /**
     * 根据goodsNo，肯定是单条，包含所有信息
     * goodsNo，必须要存在，不然就是查询集合用 getListConfigInfoByParam
     * @param
     * @return
     * <AUTHOR>
     */
    @Override
    public ExecuteDTO<ResCloudPoolGoodsCommissionConfigDTO> getConfigInfoByGoodsNo(ReqCloudPoolGoodsCommissionConfigDTO configDTO) {
        log.info("MarketProcess-CloudPoolGoodsCommissionConfigAnalysisServiceImpl-getConfigInfoByGoodsNo-params-start");
        log.info("MarketProcess-CloudPoolGoodsCommissionConfigAnalysisServiceImpl-getConfigInfoByGoodsNo-params={}", String.valueOf(configDTO));
        AtomReqCloudPoolGoodsCommissionConfigDTO commissionConfigDTO = BeanCopierUtil.copy(configDTO, AtomReqCloudPoolGoodsCommissionConfigDTO.class);
        this.configAssert.getListConfigInfoByParam(commissionConfigDTO);
        ExecuteDTO<AtomResCloudPoolGoodsCommissionConfigDTO> configInfoByGoodsNo = this.configAnalysisService.getConfigInfoByGoodsNo(commissionConfigDTO);
        //判断状态
        if (!configInfoByGoodsNo.successFlag()) {
            return ExecuteDTO.error(configInfoByGoodsNo.getStatus(), configInfoByGoodsNo.getMsg());
        }
        AtomResCloudPoolGoodsCommissionConfigDTO configInfoByGoodsNoData = configInfoByGoodsNo.getData();
        ResCloudPoolGoodsCommissionConfigDTO cloudPoolGoodsCommissionConfigDTO = BeanCopierUtil.copy(configInfoByGoodsNoData, ResCloudPoolGoodsCommissionConfigDTO.class);
        log.info("MarketProcess-CloudPoolGoodsCommissionConfigAnalysisServiceImpl-getConfigInfoByGoodsNo-params-end");
        return ExecuteDTO.success(cloudPoolGoodsCommissionConfigDTO);
    }
    /**
     * 根据goodsNo，肯定是单条，包含所有信息
     * 查询集合塞 goodsNoList
     * @param
     * @return
     * <AUTHOR>
     */
    @Override
    public ExecuteDTO<List<ResCloudPoolGoodsCommissionConfigDTO>> getListConfigInfoByParam(ReqCloudPoolGoodsCommissionConfigDTO configDTO) {
        log.info("MarketProcess-CloudPoolGoodsCommissionConfigAnalysisServiceImpl-getListConfigInfoByParam-params-start");
        log.info("MarketProcess-CloudPoolGoodsCommissionConfigAnalysisServiceImpl-getListConfigInfoByParam-params={}", String.valueOf(configDTO));
        AtomReqCloudPoolGoodsCommissionConfigDTO commissionConfigDTO = BeanCopierUtil.copy(configDTO, AtomReqCloudPoolGoodsCommissionConfigDTO.class);
        this.configAssert.getListConfigInfoByParam(commissionConfigDTO);
        ExecuteDTO<List<AtomResCloudPoolGoodsCommissionConfigDTO>> listConfigInfoByParam = this.configAnalysisService.getListConfigInfoByParam(commissionConfigDTO);
        //判断状态
        if (!listConfigInfoByParam.successFlag()) {
            return ExecuteDTO.error(listConfigInfoByParam.getStatus(), listConfigInfoByParam.getMsg());
        }
        List<AtomResCloudPoolGoodsCommissionConfigDTO> infoByParamData = listConfigInfoByParam.getData();
        List<ResCloudPoolGoodsCommissionConfigDTO> commissionConfigList = BeanCopierUtil.copyList(infoByParamData, ResCloudPoolGoodsCommissionConfigDTO.class);
        log.info("MarketProcess-CloudPoolGoodsCommissionConfigAnalysisServiceImpl-getListConfigInfoByParam-params-end");
        return ExecuteDTO.success(commissionConfigList);
    }

}


