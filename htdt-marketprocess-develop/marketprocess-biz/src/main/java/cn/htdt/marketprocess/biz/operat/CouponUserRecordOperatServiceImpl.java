package cn.htdt.marketprocess.biz.operat;

import cn.htdt.common.dto.enums.MarketErrorCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.market.CouponPeriodValidityEnum;
import cn.htdt.common.enums.market.CouponSourceTypeEnum;
import cn.htdt.common.generator.MarketFormGenerator;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.CollectionUtils;
import cn.htdt.marketprocess.api.operat.CouponSettingOperatService;
import cn.htdt.marketprocess.api.operat.CouponUserRecordOperatService;
import cn.htdt.marketprocess.biz.conversion.CouponUserRecordAssert;
import cn.htdt.marketprocess.dao.CouponSettingDao;
import cn.htdt.marketprocess.dao.CouponUserRecordDao;
import cn.htdt.marketprocess.domain.CouponUserRecordDomain;
import cn.htdt.marketprocess.dto.request.ReqCouponSettingInfoDTO;
import cn.htdt.marketprocess.dto.request.ReqCouponUserRecordOrderDTO;
import cn.htdt.marketprocess.dto.response.ResCouponUserRecordDTO;
import cn.htdt.marketprocess.vo.AtomCouponSettingVo;
import cn.htdt.marketprocess.vo.CouponUserRecordVO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 券发放记录-目前代金券用
 *
 * <AUTHOR>
 */
@Slf4j
@DubboService
public class CouponUserRecordOperatServiceImpl implements CouponUserRecordOperatService {

    /**
     * 券活动
     */
    @Resource
    private CouponSettingOperatService couponSettingOperatService;
    /**
     * 券活动
     */
    @Resource
    private CouponSettingDao couponSettingDao;
    /**
     * 券记录
     */
    @Resource
    private CouponUserRecordDao couponUserRecordDao;

    @Resource
    private CouponUserRecordAssert couponUserRecordAssert;

    @Override
    public ExecuteDTO sendCouponUserRecord(ReqCouponUserRecordOrderDTO reqCouponUserRecordOrderDTO) {
        log.info("MarketProcess-CouponSendRecordOperatServiceImpl-sendCouponUserRecord-params-start");
        log.info("MarketProcess-CouponSendRecordOperatServiceImpl-sendCouponUserRecord-params={}", JSON.toJSONString(reqCouponUserRecordOrderDTO));
        ExecuteDTO executeDTO = couponUserRecordAssert.sendCouponUserRecordAssert(reqCouponUserRecordOrderDTO);
        if(!executeDTO.successFlag()) {
            return executeDTO;
        }
        CouponUserRecordVO couponUserRecordVO = new CouponUserRecordVO();
        couponUserRecordVO.setCouponNo(reqCouponUserRecordOrderDTO.getCouponNo());
        couponUserRecordVO.setOrderNo(reqCouponUserRecordOrderDTO.getOrderNo());
        couponUserRecordVO.setFanNo(reqCouponUserRecordOrderDTO.getFanNo());
        couponUserRecordVO.setUserCouponNo(reqCouponUserRecordOrderDTO.getUserCouponNo());
        List<CouponUserRecordVO> userRecordVOs = this.couponUserRecordDao.selectCouponUserRecords(couponUserRecordVO);
        log.info("MarketProcess-CouponSendRecordOperatServiceImpl-sendCouponUserRecord-userRecordVOs={}", JSON.toJSONString(userRecordVOs));
        if(CollectionUtils.isNotEmpty(userRecordVOs)) {
            log.info("已发放过，无需重复发放，订单号={}", reqCouponUserRecordOrderDTO.getOrderNo());
            return ExecuteDTO.success();
        }
        // 发放券-代金券
        AtomCouponSettingVo atomCouponSettingVo = new AtomCouponSettingVo();
        atomCouponSettingVo.setCouponNo(reqCouponUserRecordOrderDTO.getCouponNo());
        AtomCouponSettingVo couponSettingVo = this.couponSettingDao.selectCouponSetting(atomCouponSettingVo);
        if(couponSettingVo == null) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000100);
        }
        CouponUserRecordDomain userRecordDomain = BeanCopierUtil.copy(couponSettingVo, CouponUserRecordDomain.class);
        if(StringUtils.isNotBlank(userRecordDomain.getCouponPeriodValidity())
                && CouponPeriodValidityEnum.COUPON_PERIOD_VALIDITY_THREE.getCode().equals(userRecordDomain.getCouponPeriodValidity())) {
            int couponEffectiveDay = userRecordDomain.getCouponEffectiveDay();
            LocalDateTime ldt = LocalDateTime.now();
            LocalDateTime startDateTime = LocalDateTime.of(ldt.getYear(), ldt.getMonth(), ldt.getDayOfMonth(), ldt.getHour(), ldt.getMinute(), ldt.getSecond(), 0);
//            LocalDateTime endDateTime = LocalDateTime.of(ldt.getYear(), ldt.getMonth(), ldt.getDayOfMonth(), 23, 59, 59, 0);
            userRecordDomain.setCouponEffectiveTime(startDateTime);
            userRecordDomain.setCouponInvalidTime(startDateTime.plusDays((long)couponEffectiveDay));
        }
        if (StringUtils.isNotBlank(reqCouponUserRecordOrderDTO.getCouponName())) {
            userRecordDomain.setCouponName(reqCouponUserRecordOrderDTO.getCouponName());
        }
        userRecordDomain.setFanNo(reqCouponUserRecordOrderDTO.getFanNo());
        userRecordDomain.setSourceType(CouponSourceTypeEnum.COUPON_SOURCE_TYPE_TWO.getCode());
        userRecordDomain.setPhone(reqCouponUserRecordOrderDTO.getPhone());
        userRecordDomain.setDsPhone(reqCouponUserRecordOrderDTO.getDsPhone());// 加密
        userRecordDomain.setOrderNo(reqCouponUserRecordOrderDTO.getOrderNo());
        userRecordDomain.setSalePrice(reqCouponUserRecordOrderDTO.getSalePrice());
        userRecordDomain.setUserCouponValue(couponSettingVo.getCouponValue());
        for (int i= 0; i < reqCouponUserRecordOrderDTO.getGoodsNum(); i++) {
            LocalDateTime ldt = LocalDateTime.now();
            userRecordDomain.setCreateTime(ldt);
            userRecordDomain.setModifyTime(ldt);
            // 生成发放批次编号
            String sendBatchNo = MarketFormGenerator.genSendBatchNo();
            userRecordDomain.setSendBatchNo(sendBatchNo);
            userRecordDomain.setUserCouponNo(MarketFormGenerator.genUserCouponNo());
            this.couponUserRecordDao.insert(userRecordDomain);
        }
        return ExecuteDTO.success();
    }

    @Override
    public ExecuteDTO<List<ResCouponUserRecordDTO>> backCouponUserRecord(ReqCouponUserRecordOrderDTO reqCouponUserRecordOrderDTO) {
        log.info("MarketProcess-CouponSendRecordOperatServiceImpl-backCouponUserRecord-params-start");
        log.info("MarketProcess-CouponSendRecordOperatServiceImpl-backCouponUserRecord-params={}", JSON.toJSONString(reqCouponUserRecordOrderDTO));
        ExecuteDTO executeDTO = couponUserRecordAssert.backCouponUserRecordAssert(reqCouponUserRecordOrderDTO);
        if(!executeDTO.successFlag()) {
            return executeDTO;
        }
        CouponUserRecordVO couponUserRecordVO = new CouponUserRecordVO();
        couponUserRecordVO.setCouponNo(reqCouponUserRecordOrderDTO.getCouponNo());
        couponUserRecordVO.setOrderNo(reqCouponUserRecordOrderDTO.getOrderNo());
        couponUserRecordVO.setFanNo(reqCouponUserRecordOrderDTO.getFanNo());
        List<CouponUserRecordVO> userRecordVOs = this.couponUserRecordDao.selectCouponUserRecords(couponUserRecordVO);
        log.info("MarketProcess-CouponSendRecordOperatServiceImpl-backCouponUserRecord-userRecordVOs={}", JSON.toJSONString(userRecordVOs));
        if(CollectionUtils.isEmpty(userRecordVOs)) {
            log.info("无券，无需退券，订单号={}", reqCouponUserRecordOrderDTO.getOrderNo());
            return ExecuteDTO.success();
        }
        // 过滤掉已使用的券
        List<CouponUserRecordVO> notUserRecordVOs = userRecordVOs.stream().filter(d -> WhetherEnum.NO.getCode().equals(d.getUseFlag())).collect(Collectors.toList());
        List<ResCouponUserRecordDTO> couponUserRecordDTOS = BeanCopierUtil.copyList(userRecordVOs, ResCouponUserRecordDTO.class);
        // 获取已使用的券(要删除的券要退的数量)
        int useCount = NumConstant.ZERO;
        // 将未使用的券逻辑删除
        if(CollectionUtils.isNotEmpty(notUserRecordVOs)) {
            List<String> userCouponRecords = notUserRecordVOs.stream().map(CouponUserRecordVO::getUserCouponNo).distinct().collect(Collectors.toList());
            CouponUserRecordVO userRecordVO = new CouponUserRecordVO();
            int goodsNum = reqCouponUserRecordOrderDTO.getGoodsNum()==null ? NumConstant.ONE: reqCouponUserRecordOrderDTO.getGoodsNum();
            if(userCouponRecords.size() > goodsNum) {
                List<String> userCouponRecords2 = new ArrayList<>();
                for(int i=0; i< goodsNum; i++) {
                    userCouponRecords2.add(userCouponRecords.get(i));
                }
                userRecordVO.setUserCouponNoList(userCouponRecords2);
            } else {
                userRecordVO.setUserCouponNoList(userCouponRecords);
            }
            useCount = userRecordVO.getUserCouponNoList().size();
            this.couponUserRecordDao.delCouponUserRecord(userRecordVO);
        }
        // 退可售数量
        log.info("退可售数量，订单号={}", reqCouponUserRecordOrderDTO.getOrderNo());
        log.info("退可售数量，券号={}", reqCouponUserRecordOrderDTO.getCouponNo());
        log.info("退可售数量，useCount={}", useCount);
        ReqCouponSettingInfoDTO reqCouponSettingInfoDTO = new ReqCouponSettingInfoDTO();
        reqCouponSettingInfoDTO.setCouponNo(reqCouponUserRecordOrderDTO.getCouponNo());
        reqCouponSettingInfoDTO.setFanNo(reqCouponUserRecordOrderDTO.getFanNo());
        reqCouponSettingInfoDTO.setGoodsNum(useCount);
        reqCouponSettingInfoDTO.setBackFlag(WhetherEnum.YES.getCode());
        this.couponSettingOperatService.updateCouponSettingResource(reqCouponSettingInfoDTO);
        return ExecuteDTO.success(couponUserRecordDTOS);
    }

}
