package cn.htdt.marketprocess.biz.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.generator.MarketFormGenerator;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqGoodsPromotionBookingDTO;
import cn.htdt.marketprocess.api.operat.GoodsPromotionBookingOperateService;
import cn.htdt.marketprocess.biz.conversion.GoodsPromotionBookingAssert;
import cn.htdt.marketprocess.dto.request.ReqGoodsPromotionBookingDTO;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomGoodsPromotionBookingOperateService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

@Slf4j
@DubboService
public class GoodsPromotionBookingOperateServiceImpl implements GoodsPromotionBookingOperateService
{

    @Resource
    private AtomGoodsPromotionBookingOperateService atomGoodsPromotionBookingOperateService;

    @Autowired
    private GoodsPromotionBookingAssert goodsPromotionBookingAssert;

    /**
     * @param
     * @Description : 新增活动商品预约信息
     * <AUTHOR> 卜金隆
     * @date : 2021/7/20 17:59
     */
    @Override
    public ExecuteDTO saveGoodsPromotionBookingInfo(ReqGoodsPromotionBookingDTO reqGoodsPromotionBookingDTO) {
        log.info("-GoodsPromotionBookingOperateServiceImpl-saveGoodsPromotionBookingInfo-param={}", JSON.toJSONString(reqGoodsPromotionBookingDTO));
        this.goodsPromotionBookingAssert.saveGoodsPromotionBookingInfoAssert(reqGoodsPromotionBookingDTO);
        AtomReqGoodsPromotionBookingDTO bookingDTO = BeanCopierUtil.copy(reqGoodsPromotionBookingDTO, AtomReqGoodsPromotionBookingDTO.class);
        bookingDTO.setBookingNo(MarketFormGenerator.genGoodsBookingNoNo());
        ExecuteDTO executeDTO = this.atomGoodsPromotionBookingOperateService.saveGoodsPromotionBookingInfo(bookingDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        log.info("-GoodsPromotionBookingOperateServiceImpl-saveGoodsPromotionBookingInfo-end");
        return ExecuteDTO.success();
    }

    /**
     * @param
     * @Description : 修改活动商品预约信息
     * <AUTHOR> 卜金隆
     * @date : 2021/7/20 17:59
     */
    @Override
    public ExecuteDTO modifyGoodsPromotionBookingInfo(ReqGoodsPromotionBookingDTO reqGoodsPromotionBookingDTO) {
        log.info("-GoodsPromotionBookingOperateServiceImpl-modifyGoodsPromotionBookingInfo-param={}", JSON.toJSONString(reqGoodsPromotionBookingDTO));
        this.goodsPromotionBookingAssert.modifyGoodsPromotionBookingInfoAssert(reqGoodsPromotionBookingDTO);
        AtomReqGoodsPromotionBookingDTO goodsPromotionBookingDTO = BeanCopierUtil.copy(reqGoodsPromotionBookingDTO, AtomReqGoodsPromotionBookingDTO.class);
        ExecuteDTO executeDTO = this.atomGoodsPromotionBookingOperateService.modifyGoodsPromotionBookingInfo(goodsPromotionBookingDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        log.info("-GoodsPromotionBookingOperateServiceImpl-modifyGoodsPromotionBookingInfo-end");
        return ExecuteDTO.success();
    }

}
