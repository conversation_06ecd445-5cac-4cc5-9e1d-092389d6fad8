package cn.htdt.marketprocess.biz.rabbitmq.consumer;

import cn.htdt.common.dto.enums.MarketErrorCode;
import cn.htdt.common.dto.enums.UserErrorCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.market.SmsTypeEnum;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.ListUtil;
import cn.htdt.marketprocess.api.operat.SmsSendConfigOperatService;
import cn.htdt.marketprocess.biz.config.ConfigConstant;
import cn.htdt.marketprocess.biz.rabbitmq.config.DelayDeplayConfig;
import cn.htdt.marketprocess.biz.rabbitmq.dto.MessageDTO;
import cn.htdt.marketprocess.biz.rabbitmq.send.SmsTaskSender;
import cn.htdt.marketprocess.dao.SmsSendConfigDao;
import cn.htdt.marketprocess.domain.SmsSendConfigRelationDomain;
import cn.htdt.marketprocess.dto.request.ReqSmsSendTaskConfigDTO;
import cn.htdt.marketprocess.vo.SmsSendConfigVO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 短信营销-节日短信和生日关怀短信消费-消费死信队列，实现定时任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SmsTaskConsumer {

    @Resource
    private ConfigConstant configConstant;

    @Resource
    private SmsSendConfigDao smsSendConfigDao;

    @Resource
    private SmsSendConfigOperatService smsSendConfigOperatService;

    @Resource
    private SmsTaskSender smsTaskSender;
    // 节日短信补偿重试时间
    private static final LocalTime FESTIVAL_RETRY_TIME = LocalTime.of(20, 0);
    // 生日短信补偿重试时间
    private static final LocalTime BIRTHDAY_RETRY_TIME = LocalTime.of(18, 0);

    /**
     * 消费死信队列内容，发送短信
     *
     * @param msgDto MessageDTO
     */
    /* mtodo 暂时注释掉
    @RabbitHandler
    @RabbitListener(queues = DelayDeplayConfig.NORMAL_QUEUE_SMS_TASK_NAME + "${global.env:null}", containerFactory = "secondFactory")
    */
    public void mqSmsSendByDeadLetter(MessageDTO msgDto) {
        String messageId = msgDto.getMessageId();
        log.info("---SmsTaskConsumer:{}---start---", messageId);
        log.info("---SmsTaskConsumer:{}---入参:{},time:{}", messageId, JSON.toJSONString(msgDto), System.currentTimeMillis());
        String messageData = msgDto.getMessageData();
        SmsSendConfigVO smsSendConfigVO = JSON.parseObject(messageData, SmsSendConfigVO.class);
        log.info("---SmsTaskConsumer:{}---smsSendConfigVO：{}", messageId, JSON.toJSONString(smsSendConfigVO));
        if (SmsTypeEnum.BIRTHDAY_CARE.getCode().equals(smsSendConfigVO.getSmsType())) {
            dealBirthdayConfig(smsSendConfigVO, messageId);
        } else if (SmsTypeEnum.FESTIVAL_FANS.getCode().equals(smsSendConfigVO.getSmsType())) {
            dealFestivalConfig(smsSendConfigVO, messageId);
        } else {
            log.info("---SmsTaskConsumer:{}---不支持的配置类型---", messageId);
        }
        log.info("---SmsTaskConsumer:{}---finishd---", messageId);
    }

    private void dealFestivalConfig(SmsSendConfigVO smsSendConfigVO, String messageId) {
        String configNo = smsSendConfigVO.getConfigNo();
        SmsSendConfigVO smsSendConfigVoDB = smsSendConfigDao.selectSmsSendConfigInfo(smsSendConfigVO);
        log.info("---SmsTaskConsumer:{}---smsSendConfigVoDB:{}", messageId, JSON.toJSONString(smsSendConfigVoDB));
        if (NumConstant.TWO != smsSendConfigVoDB.getUseFlag()) {
            log.info("---SmsTaskConsumer:{}---configNo:{},配置未启用,退出执行", messageId, configNo);
            return;
        }
        if (!WhetherEnum.NO.getCode().equals(smsSendConfigVoDB.getDeleteFlag())) {
            log.info("---SmsTaskConsumer:{}---configNo:{},配置已删除,退出执行", messageId, configNo);
            return;
        }
        // 校验是否修改
        if (smsSendConfigVoDB.getModifyTime().isAfter(smsSendConfigVO.getModifyTime())) {
            log.info("---SmsTaskConsumer:{}---configNo:{},配置已修改,退出执行", messageId, configNo);
            return;
        }
        if (ListUtil.isEmpty(smsSendConfigVoDB.getSmsSendConfigRelationDomains())) {
            log.info("---SmsTaskConsumer:{}---configNo:{},未配置节日,退出执行", messageId, configNo);
            return;
        }
        // 校验执行时间是否是今天
        boolean executeAble = false;
        for (SmsSendConfigRelationDomain domain : smsSendConfigVoDB.getSmsSendConfigRelationDomains()) {
            if (domain.getNextExecuteTime().toLocalDate().equals(LocalDate.now())) {
                executeAble = true;
            }
        }
        if (!executeAble) {
            log.info("---SmsTaskConsumer:{}---configNo:{},今天无需执行,退出执行", messageId, configNo);
            return;
        }

        ReqSmsSendTaskConfigDTO reqSmsSendTaskConfigDTO = BeanCopierUtil.copy(smsSendConfigVoDB, ReqSmsSendTaskConfigDTO.class);
        ExecuteDTO<String> executeDTO = smsSendConfigOperatService.processSendConfig(reqSmsSendTaskConfigDTO);
        if (null != executeDTO && !executeDTO.successFlag()) {
            if (MarketErrorCode.CODE_17002001.getCode().equals(executeDTO.getStatus())) {
                // 设定补偿时间为20点，并重新写入MQ等待执行
                if (FESTIVAL_RETRY_TIME.isAfter(LocalTime.now())) {
                    smsSendConfigVoDB.setSendTime(FESTIVAL_RETRY_TIME);
                    smsTaskSender.setTask(smsSendConfigVoDB);
                    log.info("---SmsTaskConsumer:{}---configNo:{},短信余额不足,写入补偿队列", messageId, configNo);
                } else {
                    log.info("---SmsTaskConsumer:{}---configNo:{},短信余额不足,超过补偿时间，放弃补偿", messageId, configNo);
                }
            } else if (UserErrorCode.CODE_13000053.getCode().equals(executeDTO.getStatus())) {
                // 设定补偿时间为20点，并重新写入MQ等待执行
                if (FESTIVAL_RETRY_TIME.isAfter(LocalTime.now())) {
                    smsSendConfigVoDB.setSendTime(FESTIVAL_RETRY_TIME);
                    smsTaskSender.setTask(smsSendConfigVoDB);
                    log.info("---SmsTaskConsumer:{}---configNo:{},查询店铺信息失败,写入补偿队列", messageId, configNo);
                } else {
                    log.info("---SmsTaskConsumer:{}---configNo:{},查询店铺信息失败,超过补偿时间，放弃补偿", messageId, configNo);
                }
            }
        }
    }

    private void dealBirthdayConfig(SmsSendConfigVO smsSendConfigVO, String messageId) {
        String configNo = smsSendConfigVO.getConfigNo();
        SmsSendConfigVO smsSendConfigVoDB = smsSendConfigDao.selectSmsSendConfigInfo(smsSendConfigVO);
        if (NumConstant.TWO != smsSendConfigVoDB.getUseFlag()) {
            log.info("---SmsTaskConsumer:{}---configNo:{},配置未启用,退出执行", messageId, configNo);
            return;
        }
        if (!WhetherEnum.NO.getCode().equals(smsSendConfigVoDB.getDeleteFlag())) {
            log.info("---SmsTaskConsumer:{}---configNo:{},配置已删除,退出执行", messageId, configNo);
            return;
        }
        // 校验执行时间是否是今天
        if (LocalDate.now().getDayOfMonth() != configConstant.getBirthdayDayOfMonth()) {
            log.info("---SmsTaskConsumer:{}---configNo:{},今天无需执行,退出执行", messageId, configNo);
            return;
        }

        ReqSmsSendTaskConfigDTO reqSmsSendTaskConfigDTO = BeanCopierUtil.copy(smsSendConfigVoDB, ReqSmsSendTaskConfigDTO.class);
        ExecuteDTO<String> executeDTO = smsSendConfigOperatService.processSendConfig(reqSmsSendTaskConfigDTO);
        if (null != executeDTO && !executeDTO.successFlag()) {
            if (MarketErrorCode.CODE_17002001.getCode().equals(executeDTO.getStatus())) {
                // 设定补偿时间为18点，并重新写入MQ等待执行
                if (BIRTHDAY_RETRY_TIME.isAfter(LocalTime.now())) {
                    smsSendConfigVoDB.setSendTime(BIRTHDAY_RETRY_TIME);
                    smsTaskSender.setTask(smsSendConfigVoDB);
                    log.info("---SmsTaskConsumer:{}---configNo:{},短信余额不足,写入补偿队列", messageId, configNo);
                } else {
                    log.info("---SmsTaskConsumer:{}---configNo:{},短信余额不足,超过补偿时间，放弃补偿", messageId, configNo);
                }
            } else if (UserErrorCode.CODE_13000053.getCode().equals(executeDTO.getStatus())) {
                // 设定补偿时间为18点，并重新写入MQ等待执行
                if (BIRTHDAY_RETRY_TIME.isAfter(LocalTime.now())) {
                    smsSendConfigVoDB.setSendTime(BIRTHDAY_RETRY_TIME);
                    smsTaskSender.setTask(smsSendConfigVoDB);
                    log.info("---SmsTaskConsumer:{}---configNo:{},查询店铺信息失败,写入补偿队列", messageId, configNo);
                } else {
                    log.info("---SmsTaskConsumer:{}---configNo:{},查询店铺信息失败,超过补偿时间，放弃补偿", messageId, configNo);
                }
            }
        }
    }

}
