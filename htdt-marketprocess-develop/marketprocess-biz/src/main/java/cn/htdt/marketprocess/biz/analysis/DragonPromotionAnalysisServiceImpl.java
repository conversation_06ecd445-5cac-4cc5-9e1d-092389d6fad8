package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.utils.ExecuteDTOUtil;
import cn.htdt.common.enums.PlatformTypeEnum;
import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.market.PlatformCountDownWordsEnum;
import cn.htdt.common.enums.market.PromotionTypeEnum;
import cn.htdt.common.enums.market.SecKillGoodsStatusEnum;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.DateUtil;
import cn.htdt.common.utils.ListUtil;
import cn.htdt.marketcenter.dto.request.AtomReqPromotionInfoDTO;
import cn.htdt.marketcenter.dto.response.AtomResGoodsPromotionRuleDTO;
import cn.htdt.marketprocess.api.analysis.DragonPromotionAnalysisService;
import cn.htdt.marketprocess.api.analysis.GoodsPromotionRuleAnalysisService;
import cn.htdt.marketprocess.dao.GoodsPromotionRuleDao;
import cn.htdt.marketprocess.dto.request.ReqPromotionInfoDTO;
import cn.htdt.marketprocess.dto.request.ReqPromotionQueryDTO;
import cn.htdt.marketprocess.dto.response.ResCommunitySolitaireDetailDTO;
import cn.htdt.marketprocess.dto.response.ResCommunitySolitaireGoodsDTO;
import cn.htdt.marketprocess.dto.response.ResIndexDragonPromotionDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomGoodsPromotionRuleAnalysisService;
import cn.htdt.marketprocess.vo.AtomGoodsPromotionRuleVo;
import cn.htdt.ordercenter.dto.request.AtomReqOrderPromotionDTO;
import cn.htdt.orderprocess.api.OrderPromotionAnalysisService;
import cn.htdt.orderprocess.dto.response.ResSoPromotionBuyerDTO;
import cn.htdt.usercenter.dto.response.ResFancDTO;
import cn.htdt.userprocess.api.UcFansProcessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 社群接龙活动查询服务
 *
 * <AUTHOR>
 * @date 2022-08-17
 */
@DubboService
@Slf4j
public class DragonPromotionAnalysisServiceImpl implements DragonPromotionAnalysisService {

    @Resource
    AtomGoodsPromotionRuleAnalysisService atomGoodsPromotionRuleAnalysisService;

    @Resource
    private GoodsPromotionRuleDao goodsPromotionRuleDao;

    @Resource
    GoodsPromotionRuleAnalysisService goodsPromotionRuleAnalysisService;

    @DubboReference
    OrderPromotionAnalysisService orderPromotionAnalysisService;

    @DubboReference
    UcFansProcessService ucFansProcessService;

    @Value("${img.default-headerImg:null}")
    private String DEFAULT_HEADER_IMG;

    @Override
    public ExecuteDTO<ResIndexDragonPromotionDTO> getIndexGragonGoodsList(ReqPromotionInfoDTO reqDTO) {
        log.info("getIndexLimitTimeGoodsList-入参：{}", reqDTO);
        // 查询商品接龙活动信息
        AtomReqPromotionInfoDTO infoDTO = BeanCopierUtil.copy(reqDTO, AtomReqPromotionInfoDTO.class);
        ExecuteDTO<List<AtomResGoodsPromotionRuleDTO>> promotionExecuteDTO =
                atomGoodsPromotionRuleAnalysisService.getLimitTimePromotionForHxg(infoDTO);
        if (null == promotionExecuteDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!promotionExecuteDTO.successFlag()) {
            return ExecuteDTO.error(promotionExecuteDTO.getStatus(), promotionExecuteDTO.getMsg());
        }
        ResIndexDragonPromotionDTO resIndexDragonPromotionDTO = null;
        List<AtomResGoodsPromotionRuleDTO> list = promotionExecuteDTO.getData();
        if(CollectionUtils.isNotEmpty(list)){
            for (AtomResGoodsPromotionRuleDTO atomResGoodsPromotionRuleDTO : list) {
                //查询当前活动上架的商品
                ReqPromotionQueryDTO reqPromotionQueryDTO = new ReqPromotionQueryDTO();
                reqPromotionQueryDTO.setPromotionNo(atomResGoodsPromotionRuleDTO.getPromotionNo());
                ExecuteDTO<ResCommunitySolitaireDetailDTO> executeDTO = goodsPromotionRuleAnalysisService.getCommunitySolitaireDetailByPromotionNo(reqPromotionQueryDTO);
                List<ResCommunitySolitaireGoodsDTO> goodsDTOList = new ArrayList<>();
                if(executeDTO.successFlag()){
                    goodsDTOList.addAll(executeDTO.getData().getGoodsList().stream().filter(p -> WhetherEnum.NO.getCode().equals(p.getGoodsDeleteFlag())).collect(Collectors.toList()));
                }
                // 如果商品都被删除，则不取此活动
                if (ListUtil.isEmpty(goodsDTOList)) {
                    continue;
                }
                resIndexDragonPromotionDTO = new ResIndexDragonPromotionDTO();
                resIndexDragonPromotionDTO.setPromotionNo(atomResGoodsPromotionRuleDTO.getPromotionNo());
                resIndexDragonPromotionDTO.setPromotionName(atomResGoodsPromotionRuleDTO.getPromotionName());
                resIndexDragonPromotionDTO.setPromotionSize(list.size());
                LocalDateTime startTime = atomResGoodsPromotionRuleDTO.getEffectiveTime();
                LocalDateTime endTime = atomResGoodsPromotionRuleDTO.getInvalidTime();
                // 当前时间
                LocalDateTime nowTime = DateUtil.getLocalDateTime();
                // 如果当前时间在活动最开始之前，则取当前时间到活动场次开始时间的秒数差
                if (nowTime.isBefore(startTime)) {
                    // 计算距开始的倒计时
                    resIndexDragonPromotionDTO.setCountdownTimeWords(PlatformCountDownWordsEnum.FROM_START.getType());
                    resIndexDragonPromotionDTO.setCountdownTimeStamp(DateUtil.getLocalDateTimeDiffSecond(nowTime, startTime));
                    // 按钮展示未开始
                    resIndexDragonPromotionDTO.setPromotionStatus(SecKillGoodsStatusEnum.NOT_START.getCode());
                } else if (!nowTime.isAfter(endTime)) {
                    // 如果当前时间不大于活动的结束时间，说明活动处于进行中，则取当前时间和活动结束时间差的秒数
                    // 计算距结束的倒计时
                    resIndexDragonPromotionDTO.setCountdownTimeWords(PlatformCountDownWordsEnum.FROM_END.getType());
                    resIndexDragonPromotionDTO.setCountdownTimeStamp(DateUtil.getLocalDateTimeDiffSecond(nowTime, endTime));
                    // 设置成进行中
                    resIndexDragonPromotionDTO.setPromotionStatus(SecKillGoodsStatusEnum.STARTING.getCode());
                } else {
                    // 当前时间大于活动的结束时间，说明当前场次的活动已经结束，活动倒计时设置为0
                    resIndexDragonPromotionDTO.setCountdownTimeStamp(0L);
                    // 按钮设置成已结束
                    resIndexDragonPromotionDTO.setPromotionStatus(SecKillGoodsStatusEnum.END.getCode());
                }
                //查询当前活动上架的商品
                List<String> imageList = goodsDTOList.stream().map(ResCommunitySolitaireGoodsDTO::getMainPictureUrl).collect(Collectors.toList());
                resIndexDragonPromotionDTO.setGoodImages(imageList);

                AtomReqOrderPromotionDTO atomReqOrderPromotionDTO = new AtomReqOrderPromotionDTO();
                atomReqOrderPromotionDTO.setPromotionNo(atomResGoodsPromotionRuleDTO.getPromotionNo());
                //查询当前活动参与人总数
                ExecuteDTO<Integer> execute = orderPromotionAnalysisService.getPromotionBuyerNum(atomReqOrderPromotionDTO);
                if(execute.successFlag()){
                    resIndexDragonPromotionDTO.setJoinNumbers(execute.getData()> NumConstant.MAX ? NumConstant.MAX:execute.getData());
                }
                //查询参与人-粉丝头像
                ExecuteDTO<List<ResSoPromotionBuyerDTO>> listExecuteDTO = orderPromotionAnalysisService.getPromotionBuyer(atomReqOrderPromotionDTO);
                if(listExecuteDTO.successFlag()){
                    List<ResSoPromotionBuyerDTO> resSoPromotionBuyerDTOS = listExecuteDTO.getData();
                    if(CollectionUtils.isNotEmpty(resSoPromotionBuyerDTOS)) {
                        List<String> buyers = resSoPromotionBuyerDTOS.stream().map(ResSoPromotionBuyerDTO::getBuyerNo).collect(Collectors.toList());
                        ExecuteDTO<List<ResFancDTO>> resFancDTOList = ucFansProcessService.getFanByFanNoList(buyers);
                        List<String> headList = new ArrayList<>();
                        resFancDTOList.getData().stream().forEach(s -> {
                            if(StringUtils.isEmpty(s.getHeadImg())){
                                headList.add(DEFAULT_HEADER_IMG);

                            }else{
                                headList.add(s.getHeadImg());
                            }

                        });
                        resIndexDragonPromotionDTO.setFansImages(headList);
                    }
                }
                // 取第一个符合条件的活动后就退出循环
                break;
            }
        }
        return ExecuteDTO.ok(resIndexDragonPromotionDTO);
    }

    @Override
    public ExecuteDTO<ResIndexDragonPromotionDTO> getIndexGragonGoodsList4SJ(ReqPromotionInfoDTO reqDTO) {
        log.info("getIndexGragonGoodsList4SJ-入参：{}", reqDTO);
        // 查询商品接龙活动信息
        AtomGoodsPromotionRuleVo queryVo = BeanCopierUtil.copy(reqDTO, AtomGoodsPromotionRuleVo.class);
        queryVo.setPromotionTypeList(Collections.singletonList(PromotionTypeEnum.COMMUNITY_SOLITAIRE_SJ.getCode()));
        queryVo.setStoreNo(reqDTO.getStoreNo());
        queryVo.setSourceType(PlatformTypeEnum.PLATFORM_MERCHANT.getCode());
        queryVo.setMerchantNo(reqDTO.getMerchantNo());
        List<AtomGoodsPromotionRuleVo> goodsPromotionRuleVos = goodsPromotionRuleDao.selectSJPromotionList(queryVo);
        if(CollectionUtils.isNotEmpty(goodsPromotionRuleVos)){
            for (AtomGoodsPromotionRuleVo goodsPromotionRuleVo : goodsPromotionRuleVos) {
                //查询当前活动上架的商品
                ReqPromotionQueryDTO reqPromotionQueryDTO = new ReqPromotionQueryDTO();
                reqPromotionQueryDTO.setPromotionNo(goodsPromotionRuleVo.getPromotionNo());
                ExecuteDTO<ResCommunitySolitaireDetailDTO> executeDTO = goodsPromotionRuleAnalysisService.getCommunitySolitaireDetailByPromotionNo(reqPromotionQueryDTO);
                List<ResCommunitySolitaireGoodsDTO> goodsDTOList = new ArrayList<>();
                if(executeDTO.successFlag()){
                    goodsDTOList.addAll(executeDTO.getData().getGoodsList().stream().filter(p -> WhetherEnum.NO.getCode().equals(p.getGoodsDeleteFlag())).collect(Collectors.toList()));
                }
                // 如果商品都被删除，则不取此活动
                if (ListUtil.isEmpty(goodsDTOList)) {
                    continue;
                }
                ResIndexDragonPromotionDTO resIndexDragonPromotionDTO = new ResIndexDragonPromotionDTO();
                resIndexDragonPromotionDTO.setPromotionNo(goodsPromotionRuleVo.getPromotionNo());
                resIndexDragonPromotionDTO.setPromotionName(goodsPromotionRuleVo.getPromotionName());
                resIndexDragonPromotionDTO.setPromotionSize(goodsPromotionRuleVos.size());
                LocalDateTime startTime = goodsPromotionRuleVo.getEffectiveTime();
                LocalDateTime endTime = goodsPromotionRuleVo.getInvalidTime();
                // 当前时间
                LocalDateTime nowTime = DateUtil.getLocalDateTime();
                // 如果当前时间在活动最开始之前，则取当前时间到活动场次开始时间的秒数差
                if (nowTime.isBefore(startTime)) {
                    // 计算距开始的倒计时
                    resIndexDragonPromotionDTO.setCountdownTimeWords(PlatformCountDownWordsEnum.FROM_START.getType());
                    resIndexDragonPromotionDTO.setCountdownTimeStamp(DateUtil.getLocalDateTimeDiffSecond(nowTime, startTime));
                    // 按钮展示未开始
                    resIndexDragonPromotionDTO.setPromotionStatus(SecKillGoodsStatusEnum.NOT_START.getCode());
                } else if (!nowTime.isAfter(endTime)) {
                    // 如果当前时间不大于活动的结束时间，说明活动处于进行中，则取当前时间和活动结束时间差的秒数
                    // 计算距结束的倒计时
                    resIndexDragonPromotionDTO.setCountdownTimeWords(PlatformCountDownWordsEnum.FROM_END.getType());
                    resIndexDragonPromotionDTO.setCountdownTimeStamp(DateUtil.getLocalDateTimeDiffSecond(nowTime, endTime));
                    // 设置成进行中
                    resIndexDragonPromotionDTO.setPromotionStatus(SecKillGoodsStatusEnum.STARTING.getCode());
                } else {
                    // 当前时间大于活动的结束时间，说明当前场次的活动已经结束，活动倒计时设置为0
                    resIndexDragonPromotionDTO.setCountdownTimeStamp(0L);
                    // 按钮设置成已结束
                    resIndexDragonPromotionDTO.setPromotionStatus(SecKillGoodsStatusEnum.END.getCode());
                }
                //查询当前活动上架的商品
                List<String> imageList = goodsDTOList.stream().map(ResCommunitySolitaireGoodsDTO::getMainPictureUrl).collect(Collectors.toList());
                resIndexDragonPromotionDTO.setGoodImages(imageList);

                AtomReqOrderPromotionDTO atomReqOrderPromotionDTO = new AtomReqOrderPromotionDTO();
                atomReqOrderPromotionDTO.setPromotionNo(goodsPromotionRuleVo.getPromotionNo());
                //查询当前活动参与人总数
                ExecuteDTO<Integer> execute = orderPromotionAnalysisService.getPromotionBuyerNum(atomReqOrderPromotionDTO);
                if(execute.successFlag()){
                    resIndexDragonPromotionDTO.setJoinNumbers(execute.getData()> NumConstant.MAX ? NumConstant.MAX:execute.getData());
                }
                //查询参与人-粉丝头像
                ExecuteDTO<List<ResSoPromotionBuyerDTO>> listExecuteDTO = orderPromotionAnalysisService.getPromotionBuyer(atomReqOrderPromotionDTO);
                if(listExecuteDTO.successFlag()){
                    List<ResSoPromotionBuyerDTO> resSoPromotionBuyerDTOS = listExecuteDTO.getData();
                    if(CollectionUtils.isNotEmpty(resSoPromotionBuyerDTOS)) {
                        List<String> buyers = resSoPromotionBuyerDTOS.stream().map(ResSoPromotionBuyerDTO::getBuyerNo).collect(Collectors.toList());
                        ExecuteDTO<List<ResFancDTO>> resFancDTOList = ucFansProcessService.getFanByFanNoList(buyers);
                        List<String> headList = new ArrayList<>();
                        for (ResFancDTO s : resFancDTOList.getData()) {
                            if (StringUtils.isEmpty(s.getHeadImg())) {
                                headList.add(DEFAULT_HEADER_IMG);
                            } else {
                                headList.add(s.getHeadImg());
                            }
                        }
                        resIndexDragonPromotionDTO.setFansImages(headList);
                    }
                }
                // 取第一个符合条件的活动后就退出循环
                return ExecuteDTO.ok(resIndexDragonPromotionDTO);
            }
        }
        return ExecuteDTO.ok(null);
    }

}
