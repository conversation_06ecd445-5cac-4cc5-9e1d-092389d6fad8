package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqSmsTemplateDTO;
import cn.htdt.marketcenter.dto.request.AtomReqSmsTrafficDTO;
import cn.htdt.marketcenter.dto.response.AtomResSmsTemplateDTO;
import cn.htdt.marketcenter.dto.response.AtomResSmsTrafficDTO;
import cn.htdt.marketcenter.dto.response.AtomResStoreSmsTrafficDTO;
import cn.htdt.marketprocess.api.analysis.SmsTrafficAnalysisService;
import cn.htdt.marketprocess.biz.conversion.SmsTrafficAssert;
import cn.htdt.marketprocess.biz.utils.SmsUtil;
import cn.htdt.marketprocess.dao.SmsTrafficDao;
import cn.htdt.marketprocess.domain.SmsTrafficDomain;
import cn.htdt.marketprocess.dto.request.ReqSmsTrafficDTO;
import cn.htdt.marketprocess.dto.response.ResSmsTrafficDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomSmsTemplateAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomSmsTrafficAnalysisService;
import cn.htdt.marketprocess.legacycenter.biz.conversion.AtomSmsTrafficAssert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 * 短信流量 数据查询服务接口实现类
 *
 * <AUTHOR>
 * @date 2021/7/6
 **/
@DubboService
@Slf4j
public class SmsTrafficAnalysisServiceImpl implements SmsTrafficAnalysisService {
    @Resource
    private AtomSmsTrafficAnalysisService atomSmsTrafficAnalysisService;
    @Resource
    private AtomSmsTemplateAnalysisService atomSmsTemplateAnalysisService;
    @Resource
    private SmsTrafficDao smsTrafficDao;
    @Resource
    private SmsTrafficAssert smsTrafficAssert;
    @Resource
    private SmsUtil smsUtil;

    @Resource
    private AtomSmsTrafficAssert atomSmsTrafficAssert;
    /**
     * 综合服务平台 -> 营销管理 -> 短信营销活动 -> 短信营销数据统计
     * 超级老板APP -> 会员 -> 短信营销 -> 短信营销数据统计
     *
     * @param reqDTO 查询参数
     * @return ExecuteDTO<ResSmsTrafficDTO>
     * <AUTHOR>
     */
    @Override
    public ExecuteDTO<ResSmsTrafficDTO> getSmsTraffic(ReqSmsTrafficDTO reqDTO) {
        log.info("-------SmsTrafficAnalysisServiceImpl-->getSmsTrafficAssert,获取短信流量信息--start----");

        smsTrafficAssert.getSmsTrafficAssert(reqDTO);

        log.info("-----getSmsTrafficAssert---->,查询参数:{}", reqDTO);

        //获取短信流量信息
        AtomReqSmsTrafficDTO atomReqDTO = BeanCopierUtil.copy(reqDTO, AtomReqSmsTrafficDTO.class);
        ExecuteDTO<AtomResSmsTrafficDTO> executeDTO = atomSmsTrafficAnalysisService.getSmsTraffic(atomReqDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResSmsTrafficDTO resSmsTrafficDTO;
        if (executeDTO.getData() != null) {
            resSmsTrafficDTO = BeanCopierUtil.copy(executeDTO.getData(), ResSmsTrafficDTO.class);
        } else {
            resSmsTrafficDTO = new ResSmsTrafficDTO();
        }

        //获取短信余额
        resSmsTrafficDTO.setSmsRemainingNum(smsUtil.getSmsRemainingNum(reqDTO.getStoreNo()));

        //获取短信模板数
        AtomReqSmsTemplateDTO atomReqSmsTemplateDTO = BeanCopierUtil.copy(reqDTO, AtomReqSmsTemplateDTO.class);
        ExecuteDTO<List<AtomResSmsTemplateDTO>> templateListExecuteDTO = atomSmsTemplateAnalysisService.getSmsTemplateList(atomReqSmsTemplateDTO);
        if (templateListExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(templateListExecuteDTO.getData())) {
            resSmsTrafficDTO.setSmsTemplateNum(templateListExecuteDTO.getData().size());
        }

        log.info("-------SmsTrafficAnalysisServiceImpl-->getSmsTrafficAssert,获取短信流量信息----");
        return ExecuteDTO.success(resSmsTrafficDTO);
    }
    /**
     * 平台根据参数统计店铺总剩余短信条数
     *
     * @param reqDTO 查询参数
     * @return ExecuteDTO<Integer>
     * <AUTHOR>
     */
    @Override
    public ExecuteDTO<Integer> countStoreSurplus(ReqSmsTrafficDTO reqDTO) {
        log.info("-------SmsTrafficAnalysisServiceImpl-->countStoreSurplus,平台根据参数统计店铺总剩余短信条数--start----");
        //获取短信流量信息
        AtomReqSmsTrafficDTO atomReqDTO = BeanCopierUtil.copy(reqDTO, AtomReqSmsTrafficDTO.class);
        ExecuteDTO<Integer> executeDTO = atomSmsTrafficAnalysisService.countStoreSurplus(atomReqDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        return ExecuteDTO.success(executeDTO.getData());

    }

    @Override
    public ExecuteDTO<List<AtomResStoreSmsTrafficDTO>> getStoreSmsRemainingInfo(ReqSmsTrafficDTO reqDTO) {
        if (CollectionUtils.isEmpty(reqDTO.getStoreNoList())) {
            return ExecuteDTO.error(CommonCode.CODE_10000001, "storeNoList");
        }
        ReqSmsTrafficDTO reqSmsTrafficDTO = BeanCopierUtil.copy(reqDTO, ReqSmsTrafficDTO.class);

        ExecuteDTO<List<AtomResStoreSmsTrafficDTO>> executeDTO = atomSmsTrafficAnalysisService.getStoreSmsRemainingInfo(reqSmsTrafficDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        List<AtomResStoreSmsTrafficDTO> executeDTOData = executeDTO.getData();
        if (CollectionUtils.isNotEmpty(executeDTOData)) {
            // 循环设置短信剩余条数
            executeDTOData.forEach(smsTrafficDTO -> {
                if (StringUtils.isNotBlank(smsTrafficDTO.getStoreNo())) {
                    smsTrafficDTO.setSmsRemainingNum(smsUtil.getSmsRemainingNum(smsTrafficDTO.getStoreNo()));
                }
            });
        }
        return ExecuteDTO.success(executeDTOData);
    }

    @Override
    public ExecuteDTO<List<AtomResStoreSmsTrafficDTO>> getALLSmsTraffic(ReqSmsTrafficDTO reqDTO) {
        List<SmsTrafficDomain> smsTrafficDomainList = smsTrafficDao.selectByParam(new SmsTrafficDomain());
        List<AtomResStoreSmsTrafficDTO> list = BeanCopierUtil.copyList(smsTrafficDomainList,AtomResStoreSmsTrafficDTO.class);
        return ExecuteDTO.success(list);
    }

    @Override
    public ExecuteDTO modifySmsTrafficByIncr(AtomReqSmsTrafficDTO atomReqDTO) {
        log.info("-------AtomSmsTrafficOperatServiceImpl-->addSmsTraffic,新增短信流量信息--start----");
        atomSmsTrafficAssert.checkStoreNoAssert(atomReqDTO);
        //编辑redis短信余额（加）
        int smsBalance = (int) smsUtil.redisIncreaseSmsBalance(atomReqDTO.getStoreNo(), atomReqDTO.getSmsRemainingNum().intValue());

        SmsTrafficDomain domain = BeanCopierUtil.copy(atomReqDTO, SmsTrafficDomain.class);
        smsTrafficDao.updateByIncr(domain);

        log.info("-------AtomSmsTrafficOperatServiceImpl-->addSmsTraffic,新增短信流量信息--end----");
        return ExecuteDTO.success();
    }

    @Override
    public ExecuteDTO modifySmsTrafficByDecr(AtomReqSmsTrafficDTO atomReqDTO) {
        log.info("-------AtomSmsTrafficOperatServiceImpl-->addSmsTraffic,新增短信流量信息--start----");
        atomSmsTrafficAssert.checkStoreNoAssert(atomReqDTO);
        //编辑redis短信余额（减）
        ExecuteDTO executeDTO =  smsUtil.redisDeductSmsBalance(atomReqDTO.getStoreNo(), atomReqDTO.getSmsRemainingNum().intValue());

        SmsTrafficDomain domain = BeanCopierUtil.copy(atomReqDTO, SmsTrafficDomain.class);
        smsTrafficDao.updateByDecr(domain);

        log.info("-------AtomSmsTrafficOperatServiceImpl-->addSmsTraffic,新增短信流量信息--end----");
        return ExecuteDTO.success();
    }


}
