package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqAgentMarketBillDTO;
import cn.htdt.marketcenter.dto.response.AgentNumShareSumDTO;
import cn.htdt.marketcenter.dto.response.AtomResAgentMarketBillDTO;
import cn.htdt.marketprocess.api.analysis.AgentMarketBillAnalysisService;
import cn.htdt.marketprocess.dto.request.ReqAgentMarketBillDTO;
import cn.htdt.marketprocess.dto.response.ResAgentMarketBillDTO;
import cn.htdt.marketprocess.dto.response.ResAgentNumShareSumDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomAgentMarketBillAnalysisService;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2021-01-14
 * @Description 任务和营销清单原子查询服务
 */
@DubboService
public class AgentMarketBillAnalysisServiceImpl implements AgentMarketBillAnalysisService {

    @Resource
    private AtomAgentMarketBillAnalysisService atomAgentMarketBillAnalysisService;

    @Override
    public ExecuteDTO<ResAgentMarketBillDTO> getAgentMarketBill(ReqAgentMarketBillDTO reqAgentMarketBillDTO) {
        AtomReqAgentMarketBillDTO atomReqAgentMarketBillDTO = BeanCopierUtil.copy(reqAgentMarketBillDTO, AtomReqAgentMarketBillDTO.class);
        ExecuteDTO<AtomResAgentMarketBillDTO> executeDTO = atomAgentMarketBillAnalysisService.getAgentMarketBill(atomReqAgentMarketBillDTO);
        if(!executeDTO.successFlag()){
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResAgentMarketBillDTO resAgentMarketBillDTO = BeanCopierUtil.copy(executeDTO.getData(), ResAgentMarketBillDTO.class);
        return ExecuteDTO.success(resAgentMarketBillDTO);
    }

    @Override
    public ExecuteDTO<Integer> selectshareSumByStoreNo(ReqAgentMarketBillDTO reqAgentMarketBillDTO) {
        AtomReqAgentMarketBillDTO atomReqAgentMarketBillDTO = BeanCopierUtil.copy(reqAgentMarketBillDTO, AtomReqAgentMarketBillDTO.class);
        ExecuteDTO<Integer> executeDTO = atomAgentMarketBillAnalysisService.selectshareSumByStoreNo(atomReqAgentMarketBillDTO);
        if (!executeDTO.successFlag()){
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        return ExecuteDTO.success(executeDTO.getData());
    }

    @Override
    public ExecuteDTO<ResAgentNumShareSumDTO> selectAgentNumShareSumByStoreNo(ReqAgentMarketBillDTO reqAgentMarketBillDTO) {
        AtomReqAgentMarketBillDTO atomReqAgentMarketBillDTO = BeanCopierUtil.copy(reqAgentMarketBillDTO, AtomReqAgentMarketBillDTO.class);
        ExecuteDTO<AgentNumShareSumDTO> executeDTO = atomAgentMarketBillAnalysisService.selectAgentNumShareSumByStoreNo(atomReqAgentMarketBillDTO);
        if (!executeDTO.successFlag()){
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        return ExecuteDTO.success(BeanCopierUtil.copy(executeDTO.getData(),ResAgentNumShareSumDTO.class));
    }

}
