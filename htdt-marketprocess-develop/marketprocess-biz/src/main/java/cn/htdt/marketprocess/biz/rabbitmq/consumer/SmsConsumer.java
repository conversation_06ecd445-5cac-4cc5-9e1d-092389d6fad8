package cn.htdt.marketprocess.biz.rabbitmq.consumer;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketprocess.biz.rabbitmq.config.DirectDeplayConfig;
import cn.htdt.middlewareprocess.api.htd.SmsChannelSendService;
import cn.htdt.middlewareprocess.dto.request.htd.AtomReqListSmsChannelDTO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 对象消费者-短信发送
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SmsConsumer {

    @DubboReference
    private SmsChannelSendService smsChannelSendService;

    @Value("${spring.profiles.active}")
    private String env;
    /**
     * 消费队列内容，发送短信
     *
     * @param reqListSmsChannelDTO
     */
    @RabbitHandler
    @RabbitListener(queues = DirectDeplayConfig.NORMAL_QUEUE_SMS_SEND_NAME + "${spring.profiles.active}")
    public void mqSmsSend(AtomReqListSmsChannelDTO reqListSmsChannelDTO) {
        log.info("---消费MQ---mqSmsSendByDeadLetter---queueName：{}，入参:：{}", DirectDeplayConfig.NORMAL_QUEUE_SMS_SEND_NAME + env, JSON.toJSONString(reqListSmsChannelDTO));
        this.smsSend(reqListSmsChannelDTO);
    }

    /**
     * 消费死信队列内容，发送短信
     *
     * @param reqListSmsChannelDTO
     */
    @RabbitHandler
    @RabbitListener(queues = DirectDeplayConfig.DEAD_LETTER_QUEUE_SMS_SEND_NAME)
    public void mqSmsSendByDeadLetter(AtomReqListSmsChannelDTO reqListSmsChannelDTO) {
        log.info("---消费MQ---mqSmsSendByDeadLetter---queueName：{}，入参:：{}", DirectDeplayConfig.DEAD_LETTER_QUEUE_SMS_SEND_NAME, JSON.toJSONString(reqListSmsChannelDTO));
        this.smsSend(reqListSmsChannelDTO);
    }

    /**
     * 短信发送
     *
     * @param reqListSmsChannelDTO
     */
    public void smsSend(AtomReqListSmsChannelDTO reqListSmsChannelDTO) {
        ExecuteDTO executeDTO = smsChannelSendService.smsChannelSendList(reqListSmsChannelDTO);
        log.info("---短信发送---结果：{}", executeDTO);
    }

}
