package cn.htdt.marketprocess.biz.rabbitmq.config;

import lombok.Data;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;

/**
 * RabbitMq虚拟配置类
 *
 * <AUTHOR>
 */
@Data
public abstract class AbstractRabbitMqConfig {

    protected String host;
    protected int port;
    protected String username;
    protected String password;
    protected String virtualHost;

    protected ConnectionFactory connectionFactory() {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(host);
        connectionFactory.setPort(port);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);
        connectionFactory.setVirtualHost(virtualHost);
        return connectionFactory;
    }
}
