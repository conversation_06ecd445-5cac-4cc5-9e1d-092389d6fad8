package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.base.exception.BaseException;
import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.constants.StringNumConstant;
import cn.htdt.common.enums.market.*;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.BigDecimalUtil;
import cn.htdt.common.utils.CollectionUtils;
import cn.htdt.common.utils.ListUtil;
import cn.htdt.marketcenter.dto.request.AtomReqVirtualCoinRuleDTO;
import cn.htdt.marketcenter.dto.response.AtomResVirtualCoinRuleDTO;
import cn.htdt.marketprocess.api.analysis.VirtualCoinRuleAnalysisService;
import cn.htdt.marketprocess.biz.conversion.VirtualCoinRuleAssert;
import cn.htdt.marketprocess.biz.utils.VirtualCoinUtil;
import cn.htdt.marketprocess.dao.VirtualCoinRuleCouponRelationDao;
import cn.htdt.marketprocess.dao.VirtualCoinRuleDao;
import cn.htdt.marketprocess.domain.VirtualCoinRuleDomain;
import cn.htdt.marketprocess.dto.request.ReqVirtualCoinRuleDTO;
import cn.htdt.marketprocess.dto.response.ResVirtualCoinRuleAndCouponInfoDTO;
import cn.htdt.marketprocess.dto.response.ResVirtualCoinRuleAndCouponInfoDetailDTO;
import cn.htdt.marketprocess.dto.response.ResVirtualCoinRuleDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomVirtualCoinRuleAnalysisService;
import cn.htdt.marketprocess.vo.VirtualCoinRuleAndCouponInfoDetailVo;
import cn.htdt.marketprocess.vo.VirtualCoinRuleAndCouponInfoVo;
import cn.htdt.marketprocess.vo.VirtualCoinRuleCouponRelationVO;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/1/25
 */
@Slf4j
@DubboService
public class VirtualCoinRuleAnalysisServiceImpl implements VirtualCoinRuleAnalysisService {

    @Resource
    private AtomVirtualCoinRuleAnalysisService virtualCoinRuleAnalysisService;

    @Resource
    private VirtualCoinRuleDao virtualCoinRuleDao;

    @Resource
    private VirtualCoinRuleCouponRelationDao virtualCoinRuleCouponRelationDao;

    @Resource
    private VirtualCoinUtil virtualCoinUtil;

    @Resource
    private VirtualCoinRuleAssert virtualCoinRuleAssert;

    /**
     * 查询橙豆规则列表
     *
     * @param virtualCoinRuleDTO
     * @return
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResVirtualCoinRuleDTO>> selectVirtualCoinRuleList(ReqVirtualCoinRuleDTO virtualCoinRuleDTO) {
        log.info("-VirtualCoinRuleAnalysisServiceImpl-selectVirtualCoinRuleList-param-start,{}", JSON.toJSONString(virtualCoinRuleDTO));
        AtomReqVirtualCoinRuleDTO reqVirtualCoinRuleDTO = BeanCopierUtil.copy(virtualCoinRuleDTO, AtomReqVirtualCoinRuleDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResVirtualCoinRuleDTO>> executePageDTOExecuteDTO =
                virtualCoinRuleAnalysisService.selectVirtualCoinRuleList(reqVirtualCoinRuleDTO);
        List<ResVirtualCoinRuleDTO> atomResVirtualCoinRuleDTOS =
                BeanCopierUtil.copyList(executePageDTOExecuteDTO.getData().getRows(), ResVirtualCoinRuleDTO.class);
        log.info("-VirtualCoinRuleAnalysisServiceImpl-selectVirtualCoinRuleList-param-end,{}", JSON.toJSONString(virtualCoinRuleDTO));
        return ExecuteDTO.success(new ExecutePageDTO<>(executePageDTOExecuteDTO.getData().getTotal(),atomResVirtualCoinRuleDTOS));
    }

    /**
     * 千橙掌柜智能收银 -> 全部功能 -> 橙豆管理-> 买橙豆, 查询橙豆规则列表和对应的优惠券信息
     *
     * 20230928 蛋品-赵翔宇-商家橙豆-橙豆规则
     *
     * @param virtualCoinRuleDTO 请求参数
     * @return 响应参数
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResVirtualCoinRuleAndCouponInfoDTO>> selectVirtualCoinRuleAndCouponInfoList(ReqVirtualCoinRuleDTO virtualCoinRuleDTO) {
        log.info("-VirtualCoinRuleAnalysisServiceImpl-selectVirtualCoinRuleAndCouponInfoList-param-start,{}", JSON.toJSONString(virtualCoinRuleDTO));

        virtualCoinRuleAssert.virtualCoinRuleAssert(virtualCoinRuleDTO);

        String virtualCoinRuleType = virtualCoinUtil.getVirtualCoinRuleType(virtualCoinRuleDTO.getStoreNo(), virtualCoinRuleDTO.getMerchantNo(), virtualCoinRuleDTO.getLoginIdentity());

        Page<Object> page = PageHelper.startPage(virtualCoinRuleDTO);
        VirtualCoinRuleAndCouponInfoVo queryVirtualCoinRuleVO = BeanCopierUtil.copy(virtualCoinRuleDTO, VirtualCoinRuleAndCouponInfoVo.class);
        queryVirtualCoinRuleVO.setRuleType(virtualCoinRuleType);

        List<VirtualCoinRuleAndCouponInfoVo> coinRuleAndCouponInfoVos;

        // 券使用期限类型：自用户获取XX天内可用
        queryVirtualCoinRuleVO.setCouponPeriodValidity(CouponPeriodValidityEnum.COUPON_PERIOD_VALIDITY_THREE.getCode());
        // 使用渠道：包含门店开单, 目前有三种情况, '1001, 1002', '1001', '1002', 这里查询语句用的是不等于1001
        queryVirtualCoinRuleVO.setCouponUseChannel(CouponUseChannelEnum.COUPON_USE_CHANNEL_ONE.getCode());

        if (VirtualCoinRuleTypeEnum.STORE_RULE.getCode().equals(virtualCoinRuleType)) {
            // 优惠券类型：手动发粉丝券
            queryVirtualCoinRuleVO.setPromotionCouponType(PromotionTypeCouponEnum.SHOP_MANUAL_COUPON.getCode());
            coinRuleAndCouponInfoVos = virtualCoinRuleDao.getVirtualCoinRuleList(queryVirtualCoinRuleVO);
        } else {
            // 20230928蛋品-赵翔宇-商家橙豆-橙豆规则, 店铺加入共享店铺, 需要和商家一样, 使用商家编号查询数据
            queryVirtualCoinRuleVO.setPromotionCouponType(PromotionTypeCouponEnum.MERCHANT_MANUAL_COUPON.getCode());
            queryVirtualCoinRuleVO.setStoreNo("");
            coinRuleAndCouponInfoVos = virtualCoinRuleDao.getVirtualCoinRuleList(queryVirtualCoinRuleVO);
        }

        // 橙豆规则不为空, 则去查询规则对应的优惠券信息
        getVirtualCoinRuleCouponInfo(queryVirtualCoinRuleVO, coinRuleAndCouponInfoVos);

        // 响应的参数
        List<ResVirtualCoinRuleAndCouponInfoDTO> resRuleAndCouponInfoDTOList = BeanCopierUtil.copyList(coinRuleAndCouponInfoVos, ResVirtualCoinRuleAndCouponInfoDTO.class);
        log.info("-VirtualCoinRuleAnalysisServiceImpl-selectVirtualCoinRuleAndCouponInfoList-param-end,{}", JSON.toJSONString(resRuleAndCouponInfoDTOList));
        return ExecuteDTO.ok(new ExecutePageDTO<>(page.getTotal(), resRuleAndCouponInfoDTOList));
    }

    /**
     * 查询橙豆规则对应的优惠券信息
     *
     * @param queryVirtualCoinRuleVO 请求参数
     * @param coinRuleAndCouponInfoVos 橙豆规则列表
     */
    private void getVirtualCoinRuleCouponInfo(VirtualCoinRuleAndCouponInfoVo queryVirtualCoinRuleVO, List<VirtualCoinRuleAndCouponInfoVo> coinRuleAndCouponInfoVos) {
        if (CollectionUtils.isNotEmpty(coinRuleAndCouponInfoVos)) {
            // 橙豆规则编号集合
            List<String> virtualNoList = coinRuleAndCouponInfoVos.stream().map(VirtualCoinRuleDomain::getVirtualNo).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(virtualNoList)) {
                VirtualCoinRuleCouponRelationVO virtualCoinRuleCouponRelationVO = BeanCopierUtil.copy(queryVirtualCoinRuleVO, VirtualCoinRuleCouponRelationVO.class);
                virtualCoinRuleCouponRelationVO.setVirtualNoList(virtualNoList);
                log.info("getVirtualCoinRuleCouponInfo--->查询橙豆规则对应的优惠券信息, 入参: {}", JSON.toJSONString(virtualCoinRuleCouponRelationVO));
                List<VirtualCoinRuleAndCouponInfoVo> virtualCoinRuleAndCouponInfoList =
                        virtualCoinRuleCouponRelationDao.getVirtualCoinRuleAndCouponInfoList(virtualCoinRuleCouponRelationVO);
                log.info("getVirtualCoinRuleCouponInfo--->查询橙豆规则对应的优惠券信息, 出参: {}", JSON.toJSONString(virtualCoinRuleAndCouponInfoList));
                if (CollectionUtils.isNotEmpty(virtualCoinRuleAndCouponInfoList)) {
                    // 根据橙豆规则编号分组, 计算券的数量和优惠面额
                    Map<String, List<VirtualCoinRuleAndCouponInfoVo>> virtualCoinRuleCouponInfoMap =
                                        virtualCoinRuleAndCouponInfoList
                                                        .stream()
                                                        .collect(Collectors.groupingBy(VirtualCoinRuleDomain::getVirtualNo));

                    coinRuleAndCouponInfoVos.forEach(coinRule -> {
                        String virtualNo = coinRule.getVirtualNo();
                        log.info("getVirtualCoinRuleCouponInfo, virtualNo: {}", virtualNo);
                        if (virtualCoinRuleCouponInfoMap.containsKey(virtualNo)) {
                            // 橙豆对应的优惠券信息
                            List<VirtualCoinRuleAndCouponInfoVo> couponInfoVos = virtualCoinRuleCouponInfoMap.get(virtualNo);

                            // 计算橙豆规则对应的优惠券数量
                            coinRule.setCouponTotalCount(ListUtil.getSize(couponInfoVos));

                            // 计算橙豆规则对应的优惠面额
                            BigDecimal couponTotalValue = BigDecimal.ZERO;

                            for (VirtualCoinRuleAndCouponInfoVo couponInfoVo : couponInfoVos) {
                                // 如果为满减券, 总价值为所有选中优惠券面值之和
                                if (CouponTypeEnum.COUPON_REDUCE.getCode().equals(couponInfoVo.getCouponType())) {
                                    couponTotalValue = BigDecimalUtil.add(couponTotalValue, couponInfoVo.getCouponTotalValue());
                                } else if (CouponTypeEnum.COUPON_DISCONUT.getCode().equals(couponInfoVo.getCouponType())) {
                                    // 如果为折扣券, 优惠面额 = 门槛 * 折扣
                                    BigDecimal tempCouponValue = BigDecimalUtil.multiply(couponInfoVo.getDiscountThreshold(), couponInfoVo.getCouponTotalValue());
                                    BigDecimal realCouponValue = BigDecimalUtil.divide(tempCouponValue, new BigDecimal(StringNumConstant.TEN));
                                    couponTotalValue = BigDecimalUtil.add(couponTotalValue, realCouponValue);
                                }
                            }
                            coinRule.setCouponTotalValue(couponTotalValue);
                        } else {
                            coinRule.setCouponTotalCount(NumConstant.ZERO);
                            coinRule.setCouponTotalValue(BigDecimal.ZERO);
                        }
                    });
                } else {
                    coinRuleAndCouponInfoVos.forEach(coinRuleAndCouponInfoVo -> {
                        coinRuleAndCouponInfoVo.setCouponTotalCount(NumConstant.ZERO);
                        coinRuleAndCouponInfoVo.setCouponTotalValue(BigDecimal.ZERO);
                    });
                }
            }
        }
    }

    /**
     * 千橙掌柜智能收银 -> 全部功能 -> 橙豆管理 -> 买橙豆 -> 橙豆规则列表, 查看某个规则对应的优惠券详细信息
     * @param virtualCoinRuleDTO 请求参数
     * @return 响应参数
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResVirtualCoinRuleAndCouponInfoDetailDTO>> queryVirtualCoinRuleAndCouponInfoDetail(ReqVirtualCoinRuleDTO virtualCoinRuleDTO) {
        log.info("-VirtualCoinRuleAnalysisServiceImpl-queryVirtualCoinRuleAndCouponInfoDetail-param-start,{}", JSON.toJSONString(virtualCoinRuleDTO));

        virtualCoinRuleAssert.virtualCoinRuleAndCouponDetailAssert(virtualCoinRuleDTO);

        VirtualCoinRuleDomain virtualCoinRuleDetail = getVirtualCoinRuleDetail(virtualCoinRuleDTO);

        Page<Object> page = PageHelper.startPage(virtualCoinRuleDTO);
        VirtualCoinRuleAndCouponInfoVo virtualCoinRuleAndCouponInfoVo = BeanCopierUtil.copy(virtualCoinRuleDTO, VirtualCoinRuleAndCouponInfoVo.class);

        virtualCoinRuleAndCouponInfoVo.setRuleType(virtualCoinRuleDetail.getRuleType());

        // 券使用期限类型：自用户获取XX天内可用
        virtualCoinRuleAndCouponInfoVo.setCouponPeriodValidity(CouponPeriodValidityEnum.COUPON_PERIOD_VALIDITY_THREE.getCode());
        // 使用渠道：包含门店开单, 目前有三种情况, '1001, 1002', '1001', '1002', 这里查询语句用的是不等于1001
        virtualCoinRuleAndCouponInfoVo.setCouponUseChannel(CouponUseChannelEnum.COUPON_USE_CHANNEL_ONE.getCode());

        if (VirtualCoinRuleTypeEnum.STORE_RULE.getCode().equals(virtualCoinRuleDetail.getRuleType())) {
            // 优惠券类型：手动发粉丝券
            virtualCoinRuleAndCouponInfoVo.setPromotionCouponType(PromotionTypeCouponEnum.SHOP_MANUAL_COUPON.getCode());
        } else {
            // 20230928蛋品-赵翔宇-商家橙豆-橙豆规则, 店铺加入共享店铺, 需要和商家一样, 使用商家编号查询数据
            virtualCoinRuleAndCouponInfoVo.setPromotionCouponType(PromotionTypeCouponEnum.MERCHANT_MANUAL_COUPON.getCode());
            virtualCoinRuleAndCouponInfoVo.setStoreNo("");
        }

        List<VirtualCoinRuleAndCouponInfoDetailVo> coinRuleAndCouponDetailVos = virtualCoinRuleDao.queryVirtualCoinRuleAndCouponInfoDetail(virtualCoinRuleAndCouponInfoVo);

        // 响应的参数
        List<ResVirtualCoinRuleAndCouponInfoDetailDTO> resRuleAndCouponDetailList = BeanCopierUtil.copyList(coinRuleAndCouponDetailVos, ResVirtualCoinRuleAndCouponInfoDetailDTO.class);
        log.info("-VirtualCoinRuleAnalysisServiceImpl-queryVirtualCoinRuleAndCouponInfoDetail-param-end,{}", JSON.toJSONString(resRuleAndCouponDetailList));
        return ExecuteDTO.ok(new ExecutePageDTO<>(page.getTotal(), resRuleAndCouponDetailList));
    }

    /**
     *  20230928蛋品-赵翔宇-商家橙豆-橙豆规则
     *
     * @param virtualCoinRuleDTO virtualCoinRuleDTO请求参数
     * @return 橙豆规则详情
     */
    private VirtualCoinRuleDomain getVirtualCoinRuleDetail(ReqVirtualCoinRuleDTO virtualCoinRuleDTO) {
        VirtualCoinRuleDomain virtualCoinRuleDomain = new VirtualCoinRuleDomain();
        virtualCoinRuleDomain.setVirtualNo(virtualCoinRuleDTO.getVirtualNo());
        List<VirtualCoinRuleDomain> virtualCoinRuleDomainList = virtualCoinRuleDao.queryVirtualCoinRuleList(virtualCoinRuleDomain);
        if (CollectionUtils.isEmpty(virtualCoinRuleDomainList)) {
            throw new BaseException(CommonCode.CODE_10000003, "橙豆规则不存在");
        }
        return ListUtil.getFirst(virtualCoinRuleDomainList);
    }

    /**
     * 查询橙豆规则
     *
     * @param virtualCoinRuleDTO
     * @return
     */
    @Override
    public ExecuteDTO<ResVirtualCoinRuleDTO> selectVirtualCoinRule(ReqVirtualCoinRuleDTO virtualCoinRuleDTO) {
        log.info("-VirtualCoinRuleAnalysisServiceImpl-selectVirtualCoinRule-param-start,{}", JSON.toJSONString(virtualCoinRuleDTO));
        AtomReqVirtualCoinRuleDTO reqVirtualCoinRuleDTO = BeanCopierUtil.copy(virtualCoinRuleDTO, AtomReqVirtualCoinRuleDTO.class);
        ExecuteDTO<AtomResVirtualCoinRuleDTO> atomResVirtualCoinRuleDTOExecuteDTO =
                virtualCoinRuleAnalysisService.selectVirtualCoinRule(reqVirtualCoinRuleDTO);
        ResVirtualCoinRuleDTO resVirtualCoinRuleDTO =
                BeanCopierUtil.copy(atomResVirtualCoinRuleDTOExecuteDTO, ResVirtualCoinRuleDTO.class);
        log.info("-VirtualCoinRuleAnalysisServiceImpl-selectVirtualCoinRule-param-end,{}", JSON.toJSONString(virtualCoinRuleDTO));
        return ExecuteDTO.success(resVirtualCoinRuleDTO);
    }
}
