package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.market.TaskStatusEnum;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqAgentTaskRelStoreDTO;
import cn.htdt.marketcenter.dto.request.AtomReqJoinStoreDTO;
import cn.htdt.marketcenter.dto.response.AtomResAgentTaskRelStoreDTO;
import cn.htdt.marketcenter.dto.response.AtomResJoinStoreDTO;
import cn.htdt.marketprocess.api.analysis.AgentTaskRelStoreAnalysisService;
import cn.htdt.marketprocess.dto.request.ReqAgentTaskRelStoreDTO;
import cn.htdt.marketprocess.dto.request.ReqJoinStoreDTO;
import cn.htdt.marketprocess.dto.response.ResAgentTaskRelStoreDTO;
import cn.htdt.marketprocess.dto.response.ResJoinStoreDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomAgentTaskRelStoreAnalysisService;
import cn.htdt.userprocess.api.UserPublicService;
import cn.htdt.userprocess.dto.publicDto.response.GetStoreListResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/1/27 9:55
 */
@Slf4j
@DubboService
public class AgentTaskRelStoreAnalysisServiceImpl implements AgentTaskRelStoreAnalysisService {


    @Resource
    private AtomAgentTaskRelStoreAnalysisService atomAgentTaskRelStoreAnalysisService;

    @DubboReference
    private UserPublicService userPublicService;
    @Override
    public ExecuteDTO<ExecutePageDTO<ResJoinStoreDTO>> getJoinStoreListByParams(ReqJoinStoreDTO reqJoinStoreDTO) {
        log.info("-------AgentTaskRelStoreAnalysisServiceImpl-->getJoinStoreListByParams --start----");

        AtomReqJoinStoreDTO atomReqJoinStoreDTO = BeanCopierUtil.copy(reqJoinStoreDTO, AtomReqJoinStoreDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResJoinStoreDTO>> executeDTO = this.atomAgentTaskRelStoreAnalysisService.getJoinStoreListByParams(atomReqJoinStoreDTO);
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        //参数转换
        ExecutePageDTO<ResJoinStoreDTO> executePageDTO = new ExecutePageDTO<>();
        List<AtomResJoinStoreDTO> atomResJoinStoreDTOList= executeDTO.getData().getRows();
        List<ResJoinStoreDTO> resJoinStoreDTOS = BeanCopierUtil.copyList(atomResJoinStoreDTOList, ResJoinStoreDTO.class);
        List<String> storeNoList = new ArrayList();
        if (CollectionUtils.isNotEmpty(atomResJoinStoreDTOList)){
            atomResJoinStoreDTOList.forEach(agentTaskDTO -> {
                storeNoList.add(agentTaskDTO.getStoreNo());
            });
        }
        //查询店铺名称
        ExecuteDTO<List<GetStoreListResponse>> storeDTO =userPublicService.queryStoreList(storeNoList);
        if(storeDTO.successFlag() && CollectionUtils.isNotEmpty(storeDTO.getData())){
            resJoinStoreDTOS.forEach(resDTO->{
                Optional<GetStoreListResponse> optional = storeDTO.getData().stream().filter(store-> resDTO.getStoreNo().equals(store.getStoreNo())).findFirst();
                if(optional.isPresent()){
                    resDTO.setStoreName(optional.get().getStoreName());
                }
                resDTO.setStatusStr(TaskStatusEnum.getByCode(resDTO.getStatus()).getType());
            });
        }
        executePageDTO.setRows(resJoinStoreDTOS);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    @Override
    public ExecuteDTO<ExecutePageDTO<ResAgentTaskRelStoreDTO>> getAgentTaskRelStoreListByPage(ReqAgentTaskRelStoreDTO reqAgentTaskRelStoreDTO) {
        AtomReqAgentTaskRelStoreDTO atomReqAgentTaskRelStoreDTO = BeanCopierUtil.copy(reqAgentTaskRelStoreDTO, AtomReqAgentTaskRelStoreDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResAgentTaskRelStoreDTO>> executeDTO = atomAgentTaskRelStoreAnalysisService.getAgentTaskRelStoreListByPage(atomReqAgentTaskRelStoreDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ExecutePageDTO<ResAgentTaskRelStoreDTO> executePageDTO = new ExecutePageDTO<>();
        List<String> storeNoList = new ArrayList<>();
        List<ResAgentTaskRelStoreDTO> resAgentTaskRelStoreDTOList = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResAgentTaskRelStoreDTO.class);
        for (ResAgentTaskRelStoreDTO resAgentTaskRelStoreDTO :resAgentTaskRelStoreDTOList) {
            storeNoList.add(resAgentTaskRelStoreDTO.getStoreNo());
        }
        //根据storeNo查询storeName
        if(CollectionUtils.isNotEmpty(storeNoList)) {
            ExecuteDTO<List<GetStoreListResponse>> storeExecuteDTO = userPublicService.queryStoreList(storeNoList);
            if(!storeExecuteDTO.successFlag()){
                return storeExecuteDTO.error(executeDTO.getStatus(), storeExecuteDTO.getMsg());
            }
            Map<String,String> storeNameMap = new HashMap<>();
            for (GetStoreListResponse store: storeExecuteDTO.getData()) {
                storeNameMap.put(store.getStoreNo(), store.getStoreName());
            }
            for (ResAgentTaskRelStoreDTO resAgentTaskRelStoreDTO : resAgentTaskRelStoreDTOList) {
                //店铺信息
                resAgentTaskRelStoreDTO.setStoreName(storeNameMap.get(resAgentTaskRelStoreDTO.getStoreNo()));
            }
        }
        executePageDTO.setRows(resAgentTaskRelStoreDTOList);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }
}
