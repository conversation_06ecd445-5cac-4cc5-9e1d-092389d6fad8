package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.base.exception.BaseException;
import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.dto.utils.ExecuteDTOUtil;
import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.market.*;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.*;
import cn.htdt.marketcenter.dto.response.*;
import cn.htdt.marketprocess.api.analysis.EnrollRuleAnalysisService;
import cn.htdt.marketprocess.dao.EnrollDrawRecordDao;
import cn.htdt.marketprocess.dao.EnrollRuleDao;
import cn.htdt.marketprocess.domain.EnrollDrawRecordDomain;
import cn.htdt.marketprocess.dto.request.ReqApplyPromoteRecordDto;
import cn.htdt.marketprocess.dto.request.ReqEnrollRecordDTO;
import cn.htdt.marketprocess.dto.request.ReqEnrollRuleListDTO;
import cn.htdt.marketprocess.dto.response.*;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomEnrollImportStoreAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomEnrollRecordAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomEnrollRuleAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomPromotionStoreDetailAnalysisService;
import cn.htdt.marketprocess.vo.EnrollDrawRecordVo;
import cn.htdt.marketprocess.vo.EnrollPromotionVO;
import cn.htdt.usercenter.dto.response.ResUUcMerchantDTO;
import cn.htdt.userprocess.api.LegacyUserCenterService;
import cn.htdt.userprocess.api.StoreProcessService;
import cn.htdt.userprocess.dto.request.user.ReqStoresDTO;
import cn.htdt.userprocess.dto.response.StoreInfoResponse;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/4/2 15:21
 */
@Slf4j
@DubboService
public class EnrollRuleAnalysisServiceImpl implements EnrollRuleAnalysisService {

    @Resource
    private EnrollDrawRecordDao enrollDrawRecordDao;

    @Resource
    private EnrollRuleDao enrollRuleDao;

    @Resource
    private AtomEnrollRuleAnalysisService atomEnrollRuleAnalysisService;

    @Resource
    private AtomEnrollRecordAnalysisService atomEnrollRecordAnalysisService;

    @Resource
    private AtomEnrollImportStoreAnalysisService atomEnrollImportStoreAnalysisService;
    @DubboReference
    private StoreProcessService storeProcessService;

    @DubboReference
    private LegacyUserCenterService legacyUserCenterService;

    @Resource
    private AtomPromotionStoreDetailAnalysisService atomPromotionStoreDetailAnalysisService;

    /**
     * @param reqEnrollRuleListDTO
     * @Description : 报名活动列表查询
     * <AUTHOR> 高繁
     * @date : 2021/4/1 16:59
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResEnrollRuleListDTO>> getEnrollPromotionList(ReqEnrollRuleListDTO reqEnrollRuleListDTO) {
        log.info(String.format("报名活动列表查询入参:%s", JSON.toJSONString(reqEnrollRuleListDTO)));
        AtomReqEnrollRuleListDTO atomReqEnrollRuleListDTO = BeanCopierUtil.copy(reqEnrollRuleListDTO, AtomReqEnrollRuleListDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResEnrollRuleListDTO>> executeDTO = atomEnrollRuleAnalysisService.getEnrollPromotionList(atomReqEnrollRuleListDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTOUtil.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ExecutePageDTO<ResEnrollRuleListDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResEnrollRuleListDTO> resEnrollRuleListDTOS = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResEnrollRuleListDTO.class);
        //查询参与店铺数
        if (CollectionUtils.isNotEmpty(resEnrollRuleListDTOS)) {
            if (null == reqEnrollRuleListDTO.getQueryBaseInfoFlag() || !WhetherEnum.YES.getCode().equals(reqEnrollRuleListDTO.getQueryBaseInfoFlag())) {
                List<String> promotionNoList = resEnrollRuleListDTOS.stream().map(ResEnrollRuleListDTO::getPromotionNo).collect(Collectors.toList());
                ExecuteDTO<List<AtomResEnrollRuleShopNumDTO>> enrollShopNumList = atomEnrollRuleAnalysisService.getEnrollShopNumList(promotionNoList);
                if (enrollShopNumList.successFlag() && CollectionUtils.isNotEmpty(enrollShopNumList.getData())) {
                    List<AtomResEnrollRuleShopNumDTO> atomResEnrollRuleShopNumDTOS = enrollShopNumList.getData();
                    resEnrollRuleListDTOS.forEach(resEnrollRuleListDTO ->
                            atomResEnrollRuleShopNumDTOS.stream().filter(enrollRuleShopNumDTO ->
                                    Objects.equals(resEnrollRuleListDTO.getPromotionNo(), enrollRuleShopNumDTO.getPromotionNo())).forEach(
                                    s -> {
                                        resEnrollRuleListDTO.setJoinStoreNum(s.getShopNum());
                                        resEnrollRuleListDTO.setApplyNum(s.getApplyNum());
                                        resEnrollRuleListDTO.setWaitNum(s.getWaitNum());
                                        resEnrollRuleListDTO.setDeliveredNum(s.getDeliveredNum());
                                    }));
                }
            }
            resEnrollRuleListDTOS.forEach(enrollRuleListDTO -> {
                // 草稿状态
                if (PromotionStatusEnum.ONLY_SAVED.getCode().equals(enrollRuleListDTO.getStatus())) {
                    enrollRuleListDTO.setPromotionStatus(TaskTimeStatusEnum.TASK_DRAFT.getCode());
                } else {
                    //未开始
                    if (null != enrollRuleListDTO.getEffectiveTime() && enrollRuleListDTO.getEffectiveTime().isAfter(LocalDateTime.now())) {
                        enrollRuleListDTO.setPromotionStatus(TaskTimeStatusEnum.TASK_NO.getCode());
                    } else if (null != enrollRuleListDTO.getInvalidTime() && enrollRuleListDTO.getInvalidTime().isBefore(LocalDateTime.now())) {
                        //已结束
                        enrollRuleListDTO.setPromotionStatus(TaskTimeStatusEnum.TASK_YES.getCode());
                    } else {
                        //进行中
                        enrollRuleListDTO.setPromotionStatus(TaskTimeStatusEnum.TASK_IN.getCode());
                    }
                }
                enrollRuleListDTO.setDelButtonFlag(WhetherEnum.YES.getCode());
            });
        }
        executePageDTO.setRows(resEnrollRuleListDTOS);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    /**
     * @param reqEnrollRuleListDTO
     * @Description : 店铺查平台报名活动列表查询
     * <AUTHOR> 卜金隆
     * @date : 2022/3/28 16:59
     */
    @Override
    public ExecuteDTO<List<ResEnrollRuleListDTO>> getStoreEnrollPromotionList(ReqEnrollRuleListDTO reqEnrollRuleListDTO) {
        log.info(String.format("店铺查平台报名活动列表查询:%s", JSON.toJSONString(reqEnrollRuleListDTO)));
        if (StringUtils.isBlank(reqEnrollRuleListDTO.getStoreNo())) {
            throw new BaseException(CommonCode.CODE_10000001, "storeNo");
        }
        AtomReqEnrollRuleListDTO listDTO = new AtomReqEnrollRuleListDTO();
        listDTO.setNoPage();
        listDTO.setStatus(reqEnrollRuleListDTO.getStatus());
        listDTO.setStoreType(StoreTypeEnum.STORE_TYPE_ONE.getCode());
        listDTO.setUpDownFlag(reqEnrollRuleListDTO.getUpDownFlag());
        ExecuteDTO<ExecutePageDTO<AtomResEnrollRuleListDTO>> executeDTO = atomEnrollRuleAnalysisService.getEnrollPromotionList(listDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        List<ResEnrollRuleListDTO> resEnrollRuleListDTOS = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResEnrollRuleListDTO.class);
        //查询参与店铺数
        if (CollectionUtils.isNotEmpty(resEnrollRuleListDTOS)) {
            List<String> promotionNoList = new ArrayList<>();
            resEnrollRuleListDTOS.forEach(resEnrollRuleListDTO -> {
                promotionNoList.add(resEnrollRuleListDTO.getPromotionNo());
            });
            ExecuteDTO<List<AtomResEnrollRuleShopNumDTO>> enrollShopNumList = atomEnrollRuleAnalysisService.getEnrollShopNumList(promotionNoList);
            if (enrollShopNumList.successFlag() && CollectionUtils.isNotEmpty(enrollShopNumList.getData())) {
                List<AtomResEnrollRuleShopNumDTO> atomResEnrollRuleShopNumDTOS = enrollShopNumList.getData();
                resEnrollRuleListDTOS = resEnrollRuleListDTOS.stream().map(resEnrollRuleListDTO -> {
                    atomResEnrollRuleShopNumDTOS.stream().filter(enrollRuleShopNumDTO ->
                            Objects.equals(resEnrollRuleListDTO.getPromotionNo(), enrollRuleShopNumDTO.getPromotionNo())).forEach(
                            s -> resEnrollRuleListDTO.setJoinStoreNum(s.getShopNum()));
                    return resEnrollRuleListDTO;
                }).collect(Collectors.toList());
            }
            resEnrollRuleListDTOS.forEach(enrollRuleListDTO -> {
                //未开始
                if (null != enrollRuleListDTO.getEffectiveTime() && enrollRuleListDTO.getEffectiveTime().isAfter(LocalDateTime.now())) {
                    enrollRuleListDTO.setPromotionStatus(TaskTimeStatusEnum.TASK_NO.getCode());
                } else if (null != enrollRuleListDTO.getInvalidTime() && enrollRuleListDTO.getInvalidTime().isBefore(LocalDateTime.now())) {
                    //已结束
                    enrollRuleListDTO.setPromotionStatus(TaskTimeStatusEnum.TASK_YES.getCode());
                } else {
                    //进行中
                    enrollRuleListDTO.setPromotionStatus(TaskTimeStatusEnum.TASK_IN.getCode());
                }
            });
        }
        List<ResEnrollRuleListDTO> resDto = new ArrayList<>();
        this.checkEnrollRoot(resEnrollRuleListDTOS, reqEnrollRuleListDTO, resDto);
        return ExecuteDTO.success(resDto);
    }

    /**
     * @param reqEnrollRuleListDTO reqEnrollRuleListDTO
     * @Description : 店铺查询可报名活动列表
     * <AUTHOR> lixiang
     * @date : 2023-02-18
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResEnrollRuleListDTO>> getStoreCanEnrollPromotionList(ReqEnrollRuleListDTO reqEnrollRuleListDTO) {
        log.info(String.format("店铺查平台报名活动列表查询:%s", JSON.toJSONString(reqEnrollRuleListDTO)));
        if (StringUtils.isBlank(reqEnrollRuleListDTO.getStoreNo())) {
            throw new BaseException(CommonCode.CODE_10000001, "storeNo");
        }
        // 店铺信息
        ReqStoresDTO reqStoresDTO = new ReqStoresDTO();
        reqStoresDTO.setStoreNo(reqEnrollRuleListDTO.getStoreNo());
        ExecuteDTO<StoreInfoResponse> storeInfoDTO = storeProcessService.getStoreInfoByStoreNo(reqStoresDTO);
        if (!storeInfoDTO.successFlag()) {
            throw new BaseException(CommonCode.CODE_10000003);
        }
        StoreInfoResponse storeInfo = storeInfoDTO.getData();
        EnrollPromotionVO queryVO = new EnrollPromotionVO();
        queryVO.setPromotionName(reqEnrollRuleListDTO.getPromotionName());
        // 根据店铺编码和店铺三级地址编码查询可报名活动
        queryVO.setStoreNo(storeInfo.getStoreNo());
        List<String> applyAreaList = new ArrayList<>(3);
        if (StringUtils.isNotBlank(storeInfo.getProvinceCode())) {
            applyAreaList.add(storeInfo.getProvinceCode());
        }
        if (StringUtils.isNotBlank(storeInfo.getCityCode())) {
            applyAreaList.add(storeInfo.getCityCode());
        }
        if (StringUtils.isNotBlank(storeInfo.getRegionCode())) {
            applyAreaList.add(storeInfo.getRegionCode());
        }
        queryVO.setApplyAreaList(applyAreaList);
        queryVO.setStatus(reqEnrollRuleListDTO.getStatus());

        Page<Object> pages = PageHelper.startPage(reqEnrollRuleListDTO);
        List<EnrollPromotionVO> ruleList = enrollRuleDao.selectStoreCanEnrollPromotionList(queryVO);
        List<ResEnrollRuleListDTO> resEnrollRuleListDTOS = BeanCopierUtil.copyList(ruleList, ResEnrollRuleListDTO.class);
        //查询参与店铺数
        if (CollectionUtils.isNotEmpty(resEnrollRuleListDTOS)) {
            if (null == reqEnrollRuleListDTO.getQueryBaseInfoFlag() || !WhetherEnum.YES.getCode().equals(reqEnrollRuleListDTO.getQueryBaseInfoFlag())) {
                List<String> promotionNoList = resEnrollRuleListDTOS.stream().map(ResEnrollRuleListDTO::getPromotionNo).collect(Collectors.toList());
                ExecuteDTO<List<AtomResEnrollRuleShopNumDTO>> enrollShopNumList = atomEnrollRuleAnalysisService.getEnrollShopNumList(promotionNoList);
                if (enrollShopNumList.successFlag() && CollectionUtils.isNotEmpty(enrollShopNumList.getData())) {
                    List<AtomResEnrollRuleShopNumDTO> atomResEnrollRuleShopNumDTOS = enrollShopNumList.getData();
                    resEnrollRuleListDTOS.forEach(resEnrollRuleListDTO -> {
                        atomResEnrollRuleShopNumDTOS.stream().filter(enrollRuleShopNumDTO ->
                                Objects.equals(resEnrollRuleListDTO.getPromotionNo(), enrollRuleShopNumDTO.getPromotionNo())).forEach(
                                s -> {
                                    resEnrollRuleListDTO.setJoinStoreNum(s.getShopNum());
                                    resEnrollRuleListDTO.setApplyNum(s.getApplyNum());
                                    resEnrollRuleListDTO.setWaitNum(s.getWaitNum());
                                    resEnrollRuleListDTO.setDeliveredNum(s.getDeliveredNum());
                                });
                    });
                }
            }
            resEnrollRuleListDTOS.forEach(enrollRuleListDTO -> {
                // 草稿状态
                if (PromotionStatusEnum.ONLY_SAVED.getCode().equals(enrollRuleListDTO.getStatus())) {
                    enrollRuleListDTO.setPromotionStatus(TaskTimeStatusEnum.TASK_DRAFT.getCode());
                } else {
                    //未开始
                    if (null != enrollRuleListDTO.getEffectiveTime() && enrollRuleListDTO.getEffectiveTime().isAfter(LocalDateTime.now())) {
                        enrollRuleListDTO.setPromotionStatus(TaskTimeStatusEnum.TASK_NO.getCode());
                    } else if (null != enrollRuleListDTO.getInvalidTime() && enrollRuleListDTO.getInvalidTime().isBefore(LocalDateTime.now())) {
                        //已结束
                        enrollRuleListDTO.setPromotionStatus(TaskTimeStatusEnum.TASK_YES.getCode());
                    } else {
                        //进行中
                        enrollRuleListDTO.setPromotionStatus(TaskTimeStatusEnum.TASK_IN.getCode());
                    }
                }
            });
        }
        return ExecuteDTOUtil.success(new ExecutePageDTO<>(pages.getTotal(), resEnrollRuleListDTOS));
    }

    /**
     * @param reqEnrollRuleListDTO
     * @Description : 根据参数查询报名活动明细
     * <AUTHOR> 高繁
     * @date : 2021/4/6 17:24
     */
    @Override
    public ExecuteDTO<ResEnrollRuleListDTO> getEnrollPromotionInfo(ReqEnrollRuleListDTO reqEnrollRuleListDTO) {
        log.info(String.format("报名活动明细查询入参:%s", JSON.toJSONString(reqEnrollRuleListDTO)));
        AtomReqEnrollRuleListDTO atomReqEnrollRuleListDTO = BeanCopierUtil.copy(reqEnrollRuleListDTO, AtomReqEnrollRuleListDTO.class);
        ExecuteDTO<AtomResEnrollRuleListDTO> executeDTO = atomEnrollRuleAnalysisService.getEnrollPromotionInfo(atomReqEnrollRuleListDTO);
        log.info(String.format("报名活动明细查询结果:%s", JSON.toJSONString(executeDTO)));
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResEnrollRuleListDTO resEnrollRuleListDTO = BeanCopierUtil.copy(executeDTO.getData(), ResEnrollRuleListDTO.class);
        if (resEnrollRuleListDTO != null) {
            //未开始
            if (null != resEnrollRuleListDTO.getEffectiveTime() && resEnrollRuleListDTO.getEffectiveTime().isAfter(LocalDateTime.now())) {
                resEnrollRuleListDTO.setPromotionStatus(TaskTimeStatusEnum.TASK_NO.getCode());
            } else if (null != resEnrollRuleListDTO.getInvalidTime() && resEnrollRuleListDTO.getInvalidTime().isBefore(LocalDateTime.now())) {
                //已结束
                resEnrollRuleListDTO.setPromotionStatus(TaskTimeStatusEnum.TASK_YES.getCode());
            } else {
                //进行中
                resEnrollRuleListDTO.setPromotionStatus(TaskTimeStatusEnum.TASK_IN.getCode());
            }
        }
        return ExecuteDTO.success(resEnrollRuleListDTO);
    }

    /**
     * @param reqEnrollRecordDTO
     * @Description : 根据参数查询报名记录
     * <AUTHOR> 高繁
     * @date : 2021/4/6 17:38
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResEnrollDrawRecordDTO>> getEnrollRecordList(ReqEnrollRecordDTO reqEnrollRecordDTO) {
        log.info(String.format("报名记录列表查询入参:%s", JSON.toJSONString(reqEnrollRecordDTO)));
        AtomReqEnrollRecordDTO atomReqEnrollRecordDTO = BeanCopierUtil.copy(reqEnrollRecordDTO, AtomReqEnrollRecordDTO.class);
        ExecutePageDTO<ResEnrollDrawRecordDTO> executePageDTO = new ExecutePageDTO<>();
        // 如果页面传了根据商家htd账号值来过滤查询报名记录时，则根据htd账号查询商家编码来转换查询
        if (StringUtils.isNotBlank(reqEnrollRecordDTO.getLoginAccount())) {
            List<String> loginAccountList = new ArrayList<>();
            loginAccountList.add(reqEnrollRecordDTO.getLoginAccount());
            ExecuteDTO<List<String>> merchantExecuteDTO = legacyUserCenterService.getByLoginAccounts(loginAccountList);
            if (null == merchantExecuteDTO) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            if (!merchantExecuteDTO.successFlag()) {
                return ExecuteDTO.error(merchantExecuteDTO.getStatus(), merchantExecuteDTO.getMsg());
            }
            if (CollectionUtils.isNotEmpty(merchantExecuteDTO.getData())) {
                atomReqEnrollRecordDTO.setMerchantNo(merchantExecuteDTO.getData().get(NumConstant.ZERO));
            } else {
                executePageDTO.setRows(null);
                executePageDTO.setTotal(0);
                return ExecuteDTO.success(executePageDTO);
            }
        }
        ExecuteDTO<ExecutePageDTO<AtomResEnrollDrawRecordDTO>> executeDTO = atomEnrollRecordAnalysisService.getEnrollRecordList(atomReqEnrollRecordDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        List<ResEnrollDrawRecordDTO> resEnrollDrawRecordDTOS = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResEnrollDrawRecordDTO.class);
        // 商家编码替换成htd账号
        if (CollectionUtils.isNotEmpty(resEnrollDrawRecordDTOS)) {
            List<String> merchantNoList = resEnrollDrawRecordDTOS.stream().map(ResEnrollDrawRecordDTO::getMerchantNo).distinct().collect(Collectors.toList());
            // 根据商家编码查询htd账号，用于导出
            ExecuteDTO<List<ResUUcMerchantDTO>> merchantExecuteDTO = legacyUserCenterService.getByMerchantNos(merchantNoList);
            if (null == merchantExecuteDTO) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            if (!merchantExecuteDTO.successFlag()) {
                return ExecuteDTO.error(merchantExecuteDTO.getStatus(), merchantExecuteDTO.getMsg());
            }
            if (CollectionUtils.isNotEmpty(merchantExecuteDTO.getData())) {
                resEnrollDrawRecordDTOS.forEach(recordDTO -> {
                    merchantExecuteDTO.getData().forEach(merchant -> {
                        if (StringUtils.equals(recordDTO.getMerchantNo(), merchant.getMerchantNo())) {
                            recordDTO.setLoginAccount(merchant.getLoginAccount());
                        }
                    });
                });
            }
        }
        executePageDTO.setRows(resEnrollDrawRecordDTOS);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    /**
     * @param reqEnrollRecordDTO
     * @Description : 店铺报名记录 去重
     * <AUTHOR> 卜金隆
     * @date : 2022/4/2 15:38
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResEnrollDrawRecordDTO>> getBossAppEnrollRecordList(ReqEnrollRecordDTO reqEnrollRecordDTO) {
        log.info(String.format("报名记录列表查询入参:%s", JSON.toJSONString(reqEnrollRecordDTO)));
        AtomReqEnrollRecordDTO atomReqEnrollRecordDTO = BeanCopierUtil.copy(reqEnrollRecordDTO, AtomReqEnrollRecordDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResEnrollDrawRecordDTO>> executeDTO = atomEnrollRecordAnalysisService.getEnrollRecordList(atomReqEnrollRecordDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ExecutePageDTO<ResEnrollDrawRecordDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResEnrollDrawRecordDTO> resEnrollDrawRecordDTOS = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResEnrollDrawRecordDTO.class);
        if (CollectionUtils.isNotEmpty(resEnrollDrawRecordDTOS)) {
            Iterator<ResEnrollDrawRecordDTO> iterator = resEnrollDrawRecordDTOS.iterator();
            // 存储对应活动下面最新的一条报名记录
            Map<String, Integer> latestRecordMap = new HashMap<>();
            while (iterator.hasNext()) {
                ResEnrollDrawRecordDTO next = iterator.next();
                if (null == latestRecordMap.get(next.getPromotionNo())) {
                    latestRecordMap.put(next.getPromotionNo(), 1);
                } else {
                    // 不是对应活动下最新的一条报名记录，从列表移除，不展示
                    iterator.remove();
                }
            }
        }
        executePageDTO.setRows(resEnrollDrawRecordDTOS);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    /**
     * @param reqEnrollRecordDTO reqEnrollRecordDTO
     * @Description : 根据参数查询报名记录,按活动分组取最新一条
     * <AUTHOR> lixiang
     * @date : 2023-02-18
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResEnrollDrawRecordDTO>> getStoreEnrollRecordList(ReqEnrollRecordDTO reqEnrollRecordDTO) {
        log.info(String.format("报名记录列表查询入参:%s", JSON.toJSONString(reqEnrollRecordDTO)));
        Page<Object> pages = PageHelper.startPage(reqEnrollRecordDTO);
        EnrollDrawRecordVo atomReqEnrollRecordDTO = BeanCopierUtil.copy(reqEnrollRecordDTO, EnrollDrawRecordVo.class);
        List<EnrollDrawRecordDomain> recordDomainList = enrollDrawRecordDao.selectEnrollRecordListGroupByPromotionNo(atomReqEnrollRecordDTO);
        ExecutePageDTO<ResEnrollDrawRecordDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResEnrollDrawRecordDTO> resEnrollDrawRecordDTOS = BeanCopierUtil.copyList(recordDomainList, ResEnrollDrawRecordDTO.class);
        executePageDTO.setRows(resEnrollDrawRecordDTOS);
        executePageDTO.setTotal(pages.getTotal());
        return ExecuteDTOUtil.success(executePageDTO);
    }

    /**
     * @param reqEnrollRecordDTO
     * @Description : 报名记录统计接口
     * <AUTHOR> 卜金隆
     * @date : 2022/3/24 17:14
     */
    @Override
    public ExecuteDTO<ResEnrollApplyCountDTO> enrollApplyCount(ReqEnrollRecordDTO reqEnrollRecordDTO) {
        log.info(String.format("报名记录统计接口:%s", JSON.toJSONString(reqEnrollRecordDTO)));
        if (StringUtils.isBlank(reqEnrollRecordDTO.getPromotionNo())) {
            throw new BaseException(CommonCode.CODE_10000001, "PromotionNo");
        }
        AtomReqEnrollRecordDTO enrollRecordDTO = BeanCopierUtil.copy(reqEnrollRecordDTO, AtomReqEnrollRecordDTO.class);
        ExecuteDTO<AtomResEnrollApplyCountDTO> executeDTO = atomEnrollRecordAnalysisService.enrollApplyCount(enrollRecordDTO);
        if (executeDTO == null) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        return ExecuteDTO.success(BeanCopierUtil.copy(executeDTO.getData(), ResEnrollApplyCountDTO.class));
    }

    /**
     * @param reqEnrollRecordDTO
     * @Description : 导出历史导入商家
     * <AUTHOR> 卜金隆
     * @date : 2022/3/24 17:14
     */
    @Override
    public ExecuteDTO<List<ResExportMerchantDTO>> exportMerchant(ResEnrollRuleListDTO reqEnrollRecordDTO) {
        log.info(String.format("导出历史导入商家:%s", JSON.toJSONString(reqEnrollRecordDTO)));
        List<ResExportMerchantDTO> resList = new ArrayList<>();
        // 导入店铺报名
        if (EnrollStoreTypeEnum.STORE_TYPE_ONE.getCode().equals(reqEnrollRecordDTO.getStoreType()) && ApplyTypeEnum.APPLY_TYPE_IMPORT.getCode().equals(reqEnrollRecordDTO.getApplyType())) {
            AtomReqEnrollImportStoreDTO atomReqEnrollImportStoreDTO = new AtomReqEnrollImportStoreDTO();
            atomReqEnrollImportStoreDTO.setPromotionNo(reqEnrollRecordDTO.getPromotionNo());
            ExecuteDTO<List<AtomResEnrollImportStoreDTO>> listExecuteDTO = atomEnrollImportStoreAnalysisService.getEnrollImportStoreList(atomReqEnrollImportStoreDTO);
            if (!listExecuteDTO.successFlag()) {
                throw new BaseException(CommonCode.CODE_10000001.getCode(), "报名店铺信息查询失败");
            }
            if (CollectionUtils.isNotEmpty(listExecuteDTO.getData())) {
                List<String> merchantNoList = listExecuteDTO.getData().stream().map(AtomResEnrollImportStoreDTO::getMerchantNo).distinct().collect(Collectors.toList());
                // 根据商家编码查询htd账号，用于导出
                ExecuteDTO<List<ResUUcMerchantDTO>> merchantExecuteDTO = legacyUserCenterService.getByMerchantNos(merchantNoList);
                if (null == merchantExecuteDTO) {
                    return ExecuteDTO.error(CommonCode.CODE_10000003);
                }
                if (!merchantExecuteDTO.successFlag()) {
                    return ExecuteDTO.error(merchantExecuteDTO.getStatus(), merchantExecuteDTO.getMsg());
                }
                if (CollectionUtils.isNotEmpty(merchantExecuteDTO.getData())) {
                    merchantNoList.forEach(merchantNo -> {
                        merchantExecuteDTO.getData().forEach(merchant -> {
                            if (StringUtils.equals(merchantNo, merchant.getMerchantNo()) && StringUtils.isNotBlank(merchant.getLoginAccount())) {
                                ResExportMerchantDTO resExportMerchantDTO = new ResExportMerchantDTO();
                                resExportMerchantDTO.setMerchantNo(merchant.getLoginAccount());
                                resExportMerchantDTO.setOperatorNo(NumConstant.ZERO);
                                resList.add(resExportMerchantDTO);
                            }
                        });
                    });
                }
            }
        } else if (EnrollStoreTypeEnum.STORE_TYPE_TWO.getCode().equals(reqEnrollRecordDTO.getStoreType())) {
            AtomReqPromotionStoreDetailDTO recordDTO = new AtomReqPromotionStoreDetailDTO();
            recordDTO.setPromotionNo(reqEnrollRecordDTO.getPromotionNo());
            ExecuteDTO<List<AtomResPromotionStoreDetailDTO>> enrollRecordList = atomPromotionStoreDetailAnalysisService.selectPromotionStoreDetailList(recordDTO);
            if (!enrollRecordList.successFlag()) {
                throw new BaseException(CommonCode.CODE_10000001.getCode(), "报名店铺信息查询失败");
            }
            if (CollectionUtils.isNotEmpty(enrollRecordList.getData())) {
                List<String> merchantNoList = enrollRecordList.getData().stream().map(AtomResPromotionStoreDetailDTO::getMerchantNo).distinct().collect(Collectors.toList());
                // 根据商家编码查询htd账号，用于导出
                ExecuteDTO<List<ResUUcMerchantDTO>> merchantExecuteDTO = legacyUserCenterService.getByMerchantNos(merchantNoList);
                if (null == merchantExecuteDTO) {
                    return ExecuteDTO.error(CommonCode.CODE_10000003);
                }
                if (!merchantExecuteDTO.successFlag()) {
                    return ExecuteDTO.error(merchantExecuteDTO.getStatus(), merchantExecuteDTO.getMsg());
                }
                if (CollectionUtils.isNotEmpty(merchantExecuteDTO.getData())) {
                    merchantNoList.forEach(merchantNo -> {
                        merchantExecuteDTO.getData().forEach(merchant -> {
                            if (StringUtils.equals(merchantNo, merchant.getMerchantNo()) && StringUtils.isNotBlank(merchant.getLoginAccount())) {
                                ResExportMerchantDTO resExportMerchantDTO = new ResExportMerchantDTO();
                                resExportMerchantDTO.setMerchantNo(merchant.getLoginAccount());
                                resExportMerchantDTO.setOperatorNo(NumConstant.ZERO);
                                resList.add(resExportMerchantDTO);
                            }
                        });
                    });
                }
            }
        }

        return ExecuteDTO.success(resList);
    }

    /**
     * @param reqEnrollRecordDTO
     * @Description : 导入的可参与店铺
     * <AUTHOR> 卜金隆
     * @date : 2022/3/24 17:14
     */
    @Override
    public ExecuteDTO<List<ResEnrollImportStoreDTO>> exportStore(ResEnrollRuleListDTO reqEnrollRecordDTO) {
        log.info(String.format("导入的可参与店铺:%s", JSON.toJSONString(reqEnrollRecordDTO)));
        AtomReqEnrollImportStoreDTO atomReqEnrollImportStoreDTO = new AtomReqEnrollImportStoreDTO();
        atomReqEnrollImportStoreDTO.setPromotionNo(reqEnrollRecordDTO.getPromotionNo()); ;
        ExecuteDTO<List<AtomResEnrollImportStoreDTO>> listExecuteDTO = atomEnrollImportStoreAnalysisService.getEnrollImportStoreList(atomReqEnrollImportStoreDTO);
        if (!listExecuteDTO.successFlag()) {
            throw new BaseException(CommonCode.CODE_10000001.getCode(), "报名店铺信息查询失败");
        }
        List<ResEnrollImportStoreDTO> importStoreDTO = BeanCopierUtil.copyList(listExecuteDTO.getData(), ResEnrollImportStoreDTO.class);

        return ExecuteDTO.success(importStoreDTO);
    }

    @Override
    public ExecuteDTO<Object> updateRecordInfoList(List<ReqApplyPromoteRecordDto> list) {
        List<ApplyPromoteRecordDto> applyPromoteRecordList = BeanCopierUtil.copyList(list, ApplyPromoteRecordDto.class);
        return atomEnrollRecordAnalysisService.updateRecordInfoList(applyPromoteRecordList);
    }

    /**
     * 根据报名规则校验店铺是否有报名权限
     */
    private void checkEnrollRoot(List<ResEnrollRuleListDTO> ruleListDTOS, ReqEnrollRuleListDTO reqEnrollRuleListDTO, List<ResEnrollRuleListDTO> resEnrollRuleListDTOList) {
        if (CollectionUtils.isEmpty(ruleListDTOS)) {
            return;
        }
        // 店铺信息

        ReqStoresDTO reqStoresDTO = new ReqStoresDTO();
        reqStoresDTO.setStoreNo(reqEnrollRuleListDTO.getStoreNo());
        ExecuteDTO<StoreInfoResponse> storeInfoDTO = storeProcessService.getStoreInfoByStoreNo(reqStoresDTO);
        if (!storeInfoDTO.successFlag()) {
            throw new BaseException(CommonCode.CODE_10000003);
        }
        StoreInfoResponse storeInfo = storeInfoDTO.getData();
        ruleListDTOS.forEach(ruleInfo -> {
            // 全部店铺参加直接返回
            if (ApplyTypeEnum.APPLY_TYPE_ALL.getCode().equals(ruleInfo.getApplyType())) {
                resEnrollRuleListDTOList.add(ruleInfo);
            }
            // 区域范围内类型 根据当前店铺地址判断
            else if (ApplyTypeEnum.APPLY_TYPE_AREA.getCode().equals(ruleInfo.getApplyType())) {
                if (StringUtils.isEmpty(ruleInfo.getApplyArea())) {
                    throw new BaseException(CommonCode.CODE_10000002.getCode(), "区域范围");
                }
                List<String> areaList =
                        Lists.newArrayList(ruleInfo.getApplyArea().split("\\|"));
                if ((StringUtils.isNotBlank(storeInfo.getProvinceCode()) && areaList.contains(storeInfo.getProvinceCode()))
                        || (StringUtils.isNotBlank(storeInfo.getCityCode()) && areaList.contains(storeInfo.getCityCode()))
                        || (StringUtils.isNotBlank(storeInfo.getRegionCode()) && areaList.contains(storeInfo.getRegionCode()))) {
                    resEnrollRuleListDTOList.add(ruleInfo);
                }
            }
            // 指定店铺类型 根据导入数据判断是否包含当前店铺
            else if (ApplyTypeEnum.APPLY_TYPE_IMPORT.getCode().equals(ruleInfo.getApplyType())) {
                AtomReqEnrollImportStoreDTO atomReqEnrollImportStoreDTO = new AtomReqEnrollImportStoreDTO();
                atomReqEnrollImportStoreDTO.setPromotionNo(ruleInfo.getPromotionNo());
                ExecuteDTO<List<AtomResEnrollImportStoreDTO>> listExecuteDTO = atomEnrollImportStoreAnalysisService.getEnrollImportStoreList(atomReqEnrollImportStoreDTO);
                if (!listExecuteDTO.successFlag()) {
                    throw new BaseException(CommonCode.CODE_10000001.getCode(), "报名店铺信息查询失败");
                }
                if (CollectionUtils.isNotEmpty(listExecuteDTO.getData())) {
                    List<AtomResEnrollImportStoreDTO> dtoData = listExecuteDTO.getData();
                    List<String> storeList = dtoData.stream().map(AtomResEnrollImportStoreDTO::getStoreNo).collect(Collectors.toList());
                    if (storeList.contains(reqEnrollRuleListDTO.getStoreNo())) {
                        resEnrollRuleListDTOList.add(ruleInfo);
                    }
                }
            }
        });
    }
}
