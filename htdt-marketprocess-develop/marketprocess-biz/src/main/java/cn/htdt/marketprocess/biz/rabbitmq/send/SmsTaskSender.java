package cn.htdt.marketprocess.biz.rabbitmq.send;

import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.market.SmsTypeEnum;
import cn.htdt.common.utils.ListUtil;
import cn.htdt.marketprocess.biz.config.ConfigConstant;
import cn.htdt.marketprocess.biz.rabbitmq.config.DelayDeplayConfig;
import cn.htdt.marketprocess.biz.rabbitmq.dto.MessageDTO;
import cn.htdt.marketprocess.dao.SmsSendConfigDao;
import cn.htdt.marketprocess.domain.SmsSendConfigDomain;
import cn.htdt.marketprocess.domain.SmsSendConfigRelationDomain;
import cn.htdt.marketprocess.vo.SmsSendConfigVO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.UUID;

/**
 * 生日关怀短信和节日短信配置转MQ-短信发送
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SmsTaskSender {
    @Value("${global.env:null}")
    private String env;

    @Resource
    private ConfigConstant configConstant;

    @Resource
    private AmqpTemplate timingRabbitTemplate;

    @Resource
    private SmsSendConfigDao smsSendConfigDao;

    /**
     * 发送消息-营销短信发送
     *
     * @param smsSendConfigVO 消息内容
     */
    public void setTask(SmsSendConfigVO smsSendConfigVO) {
        String messageId = UUID.randomUUID().toString();
        String msgStr = JSON.toJSONString(smsSendConfigVO);
        String configNo = smsSendConfigVO.getConfigNo();
        log.info("------setTask---smsSendConfigVO:{}", msgStr);

        if (NumConstant.TWO != smsSendConfigVO.getUseFlag()) {
            log.info("---SmsTaskSender:{}---configNo:{},配置未启用,退出执行", messageId, configNo);
            return;
        }
        if (!WhetherEnum.NO.getCode().equals(smsSendConfigVO.getDeleteFlag())) {
            log.info("---SmsTaskSender:{}---configNo:{},配置已删除,退出执行", messageId, configNo);
            return;
        }
        if (SmsTypeEnum.FESTIVAL_FANS.getCode().equals(smsSendConfigVO.getSmsType())
                && ListUtil.isEmpty(smsSendConfigVO.getSmsSendConfigRelationDomains())) {
            log.info("---SmsTaskSender:{}---configNo:{},未配置节日,退出执行", messageId, configNo);
            return;
        }
        // 校验执行时间是否是今天
        boolean executeAble = false;
        if (SmsTypeEnum.BIRTHDAY_CARE.getCode().equals(smsSendConfigVO.getSmsType())) {
            if (LocalDate.now().getDayOfMonth() == configConstant.getBirthdayDayOfMonth()) {
                executeAble = true;
            }
        } else if (SmsTypeEnum.FESTIVAL_FANS.getCode().equals(smsSendConfigVO.getSmsType())) {
            for (SmsSendConfigRelationDomain domain : smsSendConfigVO.getSmsSendConfigRelationDomains()) {
                // 如果只执行一次并且已执行过，则下一次执行时间为空
                if (null != domain.getNextExecuteTime()
                        && domain.getNextExecuteTime().toLocalDate().equals(LocalDate.now())) {
                    executeAble = true;
                }
            }
        } else {
            log.info("---SmsTaskConsumer:{}---不支持的配置类型---", messageId);
        }

        if (!executeAble) {
            log.info("---SmsTaskSender:{}---configNo:{},今天无需执行,退出执行", messageId, configNo);
            return;
        }

        // 计算时间差
        int sendTimeSeconds = smsSendConfigVO.getSendTime().toSecondOfDay();
        int nowTimeSeconds = LocalTime.now().toSecondOfDay();
        long messageTTL = (sendTimeSeconds - nowTimeSeconds) * 1000L;

         if (messageTTL < 0) {
             messageTTL = 1000L;
         }

        MessageDTO msgDto = new MessageDTO();
        msgDto.setCreateTime(String.valueOf(System.currentTimeMillis()));
        msgDto.setMessageId(messageId);
        msgDto.setMessageData(msgStr);
        msgDto.setMessageTTL(messageTTL);
        log.info("---SmsTaskSender:{}---configNo:{},加入MQ", messageId, configNo);
        this.timingRabbitTemplate.convertAndSend(DelayDeplayConfig.NORMAL_DELAYED_EXCHANGE_NAME + env,
                DelayDeplayConfig.NORMAL_QUEUE_SMS_TASK_ROUTING_KEY + env,
                msgDto,
                message -> {
                    message.getMessageProperties().setHeader("x-delay" , msgDto.getMessageTTL());
                    return message;
                });
    }

    public void setTask(String configNo) {
        SmsSendConfigDomain queryDomain = new SmsSendConfigDomain();
        queryDomain.setUseFlag(WhetherEnum.YES.getCode());
        queryDomain.setConfigNo(configNo);
        List<SmsSendConfigVO> smsSendConfigVOS = smsSendConfigDao.selectSmsSendConfigs(queryDomain);
        ListUtil.forEachIfNotEmpty(smsSendConfigVOS, this::setTask);
    }
}
