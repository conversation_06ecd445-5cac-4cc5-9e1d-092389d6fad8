package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.base.exception.BaseException;
import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.enums.MarketErrorCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.constants.PromotionKeyConstant;
import cn.htdt.common.enums.goods.*;
import cn.htdt.common.enums.market.*;
import cn.htdt.common.redis.utils.RedisBaseUtil;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.BigDecimalUtil;
import cn.htdt.common.utils.DateUtil;
import cn.htdt.goodsprocess.api.analysis.GoodsAnalysisService;
import cn.htdt.goodsprocess.dto.request.goods.ReqGoodsDTO;
import cn.htdt.goodsprocess.dto.response.goods.ResGoodsDTO;
import cn.htdt.goodsprocess.dto.response.goodstag.ResGoodsTagDTO;
import cn.htdt.marketcenter.dto.request.*;
import cn.htdt.marketcenter.dto.response.AtomResAgentMarketRewardsetDTO;
import cn.htdt.marketcenter.dto.response.AtomResGoodsPromotionGoodsRelationDTO;
import cn.htdt.marketcenter.dto.response.AtomResGoodsPromotionPeriodDTO;
import cn.htdt.marketcenter.dto.response.AtomResGoodsPromotionRuleDTO;
import cn.htdt.marketprocess.api.analysis.LimitTimePromotionAnalysisService;
import cn.htdt.marketprocess.api.analysis.PreDeterminedCheckService;
import cn.htdt.marketprocess.biz.conversion.LimitTimePromotionAssert;
import cn.htdt.marketprocess.common.util.MarketThreadExecutorService;
import cn.htdt.marketprocess.dto.request.ReqGoodsPromotionPeriodDTO;
import cn.htdt.marketprocess.dto.request.ReqLimitTimePeriodGoodsPageDTO;
import cn.htdt.marketprocess.dto.request.ReqPromotionGoodsOrderCheckDTO;
import cn.htdt.marketprocess.dto.request.ReqPromotionInfoDTO;
import cn.htdt.marketprocess.dto.response.*;
import cn.htdt.marketprocess.legacycenter.api.analysis.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * 限时购活动查询服务
 *
 * <AUTHOR>
 * @date 2021-09-28
 */
@DubboService
@Slf4j
public class LimitTimePromotionAnalysisServiceImpl implements LimitTimePromotionAnalysisService {

    @Resource
    private AtomGoodsPromotionRuleAnalysisService atomGoodsPromotionRuleAnalysisService;

    @Resource
    private AtomGoodsPromotionPeriodAnalysisService atomGoodsPromotionPeriodAnalysisService;

    @Resource
    private AtomGoodsPromotionGoodsRelationAnalysisService atomGoodsPromotionGoodsRelationAnalysisService;

    @DubboReference
    private GoodsAnalysisService goodsAnalysisService;

    @Resource
    private AtomAgentMarketRewardsetAnalysisService atomAgentMarketRewardsetAnalysisService;

    @Resource
    private AtomGoodsPromotionBookingAnalysisService atomGoodsPromotionBookingAnalysisService;

    @Autowired
    private LimitTimePromotionAssert limitTimePromotionAssert;

    @Autowired
    private PreDeterminedCheckService preDeterminedCheckService;

    @Autowired
    private RedisBaseUtil redisBaseUtil;

    @Autowired
    private GoodsPromotionGoodsRelationAnalysisServiceImpl goodsPromotionGoodsRelationAnalysisServiceImpl;

    /**
     * 根据店铺编号查询有效限时购活动列表-汇享购
     *
     * @param promotionInfoDTO 查询参数
     * @return 限时购活动列表
     * <AUTHOR>
     * @date 2021-09-26
     */
    @Override
    public ExecuteDTO<List<ResGoodsPromotionRuleDTO>> getLimitTimePromotionForHxg(ReqPromotionInfoDTO promotionInfoDTO) {
        log.info("getLimitTimePromotionForHxg-入参：{}", promotionInfoDTO);
        // 入参校验
        limitTimePromotionAssert.getLimitTimePromotionForHxgAssert(promotionInfoDTO);
        // 查询限时购活动信息
        AtomReqPromotionInfoDTO infoDTO = BeanCopierUtil.copy(promotionInfoDTO, AtomReqPromotionInfoDTO.class);
        // 设置查询的活动类型为限时购活动
        infoDTO.setPromotionType(PromotionTypeEnum.LIMIT_TIME.getCode());
        ExecuteDTO<List<AtomResGoodsPromotionRuleDTO>> executeDTO = atomGoodsPromotionRuleAnalysisService.getLimitTimePromotionForHxg(infoDTO);
        if (null == executeDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }

        // 出参转换
        List<ResGoodsPromotionRuleDTO> goodsPromotionRuleDTOList = BeanCopierUtil.copyList(executeDTO.getData(), ResGoodsPromotionRuleDTO.class);

        // 汇赚钱模块，校验是否查询出来的活动列表数据，是否包含分销商品，如果没有分销商品，则去掉活动
        if (SecKillLocationEnum.H_Z_Q_SECKILL.getCode().equals(promotionInfoDTO.getLimitTimeLocation())
                && CollectionUtils.isNotEmpty(goodsPromotionRuleDTOList)) {
            this.removeNoDistributeGoodsPromotion(goodsPromotionRuleDTOList);
        }

        //查询限时购活动时间段
        this.getPromotionPeriodList(goodsPromotionRuleDTOList);

        // 返回结果
        return ExecuteDTO.success(goodsPromotionRuleDTOList);
    }

    /**
     * 汇赚钱模块，删除不包含分销商品的活动数据
     *
     * @param promotionRuleDTOList 待返回的活动列表数据
     * <AUTHOR>
     * @date 2021-12-14
     */
    public void removeNoDistributeGoodsPromotion(List<ResGoodsPromotionRuleDTO> promotionRuleDTOList) {
        // 初始化计数器
        CountDownLatch countDownLatch = new CountDownLatch(promotionRuleDTOList.size());
        // 多线程进行查询校验活动下是否有分销商品
        for (ResGoodsPromotionRuleDTO goodsPromotionRuleDTO : promotionRuleDTOList) {
            MarketThreadExecutorService.getInstance()
                    .getBoundedThreadPool()
                    .execute(() -> {
                                // 查询校验活动下是否有分销商品
                                this.checkPromotionGoodsData(goodsPromotionRuleDTO, countDownLatch);
                            }
                    );
        }
        try {
            // 阻塞当前线程，直到所有子线程都执行countDown方法才会继续执行
            countDownLatch.await();
        } catch (InterruptedException e) {
            log.error("闭锁报错", e);
            Thread.currentThread().interrupt();
        }

        // 移除没有分销商品数据的活动
        promotionRuleDTOList.removeIf(goodsPromotionRuleDTO ->
                !(WhetherEnum.YES.getCode().equals(goodsPromotionRuleDTO.getHaveDistributeGoods())));
    }

    /**
     * 查询校验活动下是否有分销商品
     *
     * @param promotionRuleDTO 活动数据
     * <AUTHOR>
     * @date 2021-12-14
     */
    private void checkPromotionGoodsData(ResGoodsPromotionRuleDTO promotionRuleDTO, CountDownLatch countDownLatch) {
        try {
            // 根据活动编码查询活动下设置的商品数据
            AtomReqGoodsPromotionGoodsRelationDTO reqGoodsRelation = new AtomReqGoodsPromotionGoodsRelationDTO();
            reqGoodsRelation.setPromotionNo(promotionRuleDTO.getPromotionNo());
            // 只查子品数据，避免平台活动的云池商品中主品是分销商品，但某个子品不是分销商品，导致误删除活动数据
            reqGoodsRelation.setChildFlag(WhetherEnum.YES.getCode());
            ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> promotionGoodsList =
                    atomGoodsPromotionGoodsRelationAnalysisService.getPromotionPeriodGoodsList(reqGoodsRelation);
            if (promotionGoodsList != null && promotionGoodsList.successFlag()
                    && CollectionUtils.isNotEmpty(promotionGoodsList.getData())) {
                // 取出所有的商品编码
                List<String> goodsNoList = promotionGoodsList.getData().stream()
                        .map(AtomResGoodsPromotionGoodsRelationDTO::getGoodsNo).collect(Collectors.toList());
                // 根据商品编码查询过滤出分销商品
                ReqGoodsDTO reqGoodsDTO = new ReqGoodsDTO();
                reqGoodsDTO.setStoreNo(promotionRuleDTO.getStoreNo());
                reqGoodsDTO.setNoPage();
                // 可用的商品
                reqGoodsDTO.setDisableFlag(NumConstant.TWO);
                // 店铺活动，根据商品编码查询；平台活动，根据云池商品编码查询
                if (SourceTypeEnum.SOURCE_TYPE_1003.getCode().equals(promotionRuleDTO.getSourceType())) {
                    reqGoodsDTO.setGoodsNos(goodsNoList);
                    // 只查询分销商品
                    reqGoodsDTO.setDistributeGoodsFlag(WhetherEnum.YES.getCode());
                } else {
                    reqGoodsDTO.setCloudPoolGoodsNos(goodsNoList);
                }
                ExecuteDTO<ExecutePageDTO<ResGoodsDTO>> goodsExecute = goodsAnalysisService.getGoodsPage(reqGoodsDTO);
                // 找到分销商品时，设置当前活动包含分销商品标识
                if (goodsExecute != null && goodsExecute.successFlag() && goodsExecute.getData() != null
                        && CollectionUtils.isNotEmpty(goodsExecute.getData().getRows())) {
                    promotionRuleDTO.setHaveDistributeGoods(WhetherEnum.YES.getCode());
                } else {
                    // 查询具体的商品详情数据异常时，打印日志，方便定位
                    log.info("getGoodsPage-出参：{}", goodsExecute);
                }
            } else {
                // 查询活动下关联的商品异常时，打印日志，方便定位
                log.info("getPromotionPeriodGoodsList-出参：{}", promotionGoodsList);
            }
        } catch (Exception e) {
            log.error("查询校验活动下是否有分销商品出错：", e);
        } finally {
            if (countDownLatch != null) {
                countDownLatch.countDown();
            }
        }
    }

    /**
     * 根据限时购活动编号查询活动场次数据
     *
     * @param promotionRuleDTOList 限时购活动数据
     * <AUTHOR>
     * @date 2021-09-26
     */
    private void getPromotionPeriodList(List<ResGoodsPromotionRuleDTO> promotionRuleDTOList) {
        if (CollectionUtils.isNotEmpty(promotionRuleDTOList)) {
            // 取出限时购活动的编号，查询活动场次信息
            List<String> promotionNoList = promotionRuleDTOList.stream().map(ResGoodsPromotionRuleDTO::getPromotionNo).collect(Collectors.toList());
            AtomReqGoodsPromotionPeriodDTO reqDTO = new AtomReqGoodsPromotionPeriodDTO();
            reqDTO.setPromotionNoList(promotionNoList);
            ExecuteDTO<List<AtomResGoodsPromotionPeriodDTO>> executePeriodDTO = atomGoodsPromotionPeriodAnalysisService.getGoodsPromotionPeriodByPromotionNo(reqDTO);
            log.info("查询限时购活动场次信息出参：{}", executePeriodDTO);
            // 查询结果与返回的限时购活动数据匹配
            if (executePeriodDTO != null && executePeriodDTO.successFlag() && CollectionUtils.isNotEmpty(executePeriodDTO.getData())) {
                promotionRuleDTOList.forEach(limitTimePromotion -> executePeriodDTO.getData().forEach(periodDTO -> {
                    if (periodDTO.getPromotionNo().equals(limitTimePromotion.getPromotionNo())) {
                        // 汇总同一限时购活动下的活动场次数据
                        ResGoodsPromotionPeriodDTO periodEntity = BeanCopierUtil.copy(periodDTO, ResGoodsPromotionPeriodDTO.class);
                        List<ResGoodsPromotionPeriodDTO> periodDTOList = limitTimePromotion.getLimitTimePromotionPeriods();
                        if (CollectionUtils.isEmpty(periodDTOList)) {
                            periodDTOList = new ArrayList<>();
                        }
                        periodDTOList.add(periodEntity);
                        limitTimePromotion.setLimitTimePromotionPeriods(periodDTOList);
                    }
                }));
            }
        }
    }

    /**
     * 首页限时购活动商品列表
     *
     * @param reqDTO 查询参数
     * @return 限时购活动以及商品信息
     * <AUTHOR>
     * @date 2021-09-28
     */
    @Override
    public ExecuteDTO<ResIndexLimitTimePromotionDTO> getIndexLimitTimeGoodsList(ReqPromotionInfoDTO reqDTO) {
        log.info("getIndexLimitTimeGoodsList-入参：{}", reqDTO);
        // 入参校验
        limitTimePromotionAssert.getIndexLimitTimeGoodsListAssert(reqDTO);
        // 查询限时购活动信息
        AtomReqPromotionInfoDTO reqPromotionInfoDTO = BeanCopierUtil.copy(reqDTO, AtomReqPromotionInfoDTO.class);
        ExecuteDTO<List<AtomResGoodsPromotionRuleDTO>> executeDTO = atomGoodsPromotionRuleAnalysisService.getLimitTimeForHxgIndex(reqPromotionInfoDTO);
        log.info("查询限时购活动信息出参：{}", executeDTO);
        if (null == executeDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        if (CollectionUtils.isEmpty(executeDTO.getData())) {
            return ExecuteDTO.success();
        }
        List<AtomResGoodsPromotionRuleDTO> promotionRuleDTOList = executeDTO.getData();

        // 封装响应结果
        ResIndexLimitTimePromotionDTO indexLimitTimePromotionDTO = new ResIndexLimitTimePromotionDTO();

        // 获取首页最新有效的活动场次的商品数据
        this.getLatestLimitTimeGoods(reqDTO, indexLimitTimePromotionDTO, promotionRuleDTOList);

        // 出参数据扩展
        this.extendPromotionGoodsData(indexLimitTimePromotionDTO);

        // 返回结果
        return ExecuteDTO.success(indexLimitTimePromotionDTO);
    }

    /**
     * 限时购首页数据出参扩展
     *
     * @param indexLimitTimePromotionDTO 待返回的结果数据
     */
    private void extendPromotionGoodsData(ResIndexLimitTimePromotionDTO indexLimitTimePromotionDTO) {
        if (null != indexLimitTimePromotionDTO && CollectionUtils.isNotEmpty(indexLimitTimePromotionDTO.getGoodsList())) {
            // 查询出所有的促销活动标签
            List<ResGoodsTagDTO> goodsPromotionTagList = this.goodsPromotionGoodsRelationAnalysisServiceImpl.getGoodsPromotionTagName();
            // 根据商品对应的参与的促销活动，找到对应的限时购活动标签
            List<String> tagNameList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(goodsPromotionTagList)) {
                for (ResGoodsTagDTO resGoodsTagDTO : goodsPromotionTagList) {
                    if (StringUtils.isNotBlank(resGoodsTagDTO.getTagPictureUrl())
                            && indexLimitTimePromotionDTO.getGoodsList().get(0).getPromotionType().equals(resGoodsTagDTO.getPromotionType())) {
                        tagNameList.add(resGoodsTagDTO.getTagPictureUrl());
                    }
                }
            }
            for (ResLimitTimeGoodsDTO limitTimeGoodsDTO : indexLimitTimePromotionDTO.getGoodsList()) {
                // 设置活动标签
                if (CollectionUtils.isNotEmpty(tagNameList)) {
                    limitTimeGoodsDTO.setActiveTagNames(tagNameList);
                }
                // 计算首页限时购活动倒计时和按钮状态
                this.getIndexCountdownTimeAndButton(limitTimeGoodsDTO);
            }
        }
    }

    /**
     * 计算限时购首页商品活动的倒计时和按钮状态
     *
     * @param limitTimeGoodsDTO 限时购活动商品数据
     * <AUTHOR>
     */
    private void getIndexCountdownTimeAndButton(ResLimitTimeGoodsDTO limitTimeGoodsDTO) {
        // 先根据活动的开始和结束时间，计算倒计时和按钮的未开始、进行中、已结束
        // 活动的开始时间和结束时间
        LocalDateTime startTime = LocalDateTime.of(limitTimeGoodsDTO.getPromotionDate(), limitTimeGoodsDTO.getStartTime());
        LocalDateTime endTime = LocalDateTime.of(limitTimeGoodsDTO.getPromotionDate(), limitTimeGoodsDTO.getEndTime());
        // 当前时间
        LocalDateTime nowTime = DateUtil.getLocalDateTime();
        // 如果当前时间在活动最开始之前，则取当前时间到具体活动场次开始时间的秒数差
        if (nowTime.isBefore(startTime)) {
            limitTimeGoodsDTO.setCountdownTimeWords(LimitTimeCountDownWordsEnum.FROM_START.getType());
            // 计算距开始的倒计时
            limitTimeGoodsDTO.setCountdownTimeStamp(DateUtil.getLocalDateTimeDiffSecond(nowTime, startTime));
            // 按钮展示未开始
            limitTimeGoodsDTO.setButton(SecKillGoodsStatusEnum.NOT_START.getCode());
        } else if (!nowTime.isAfter(endTime)) {
            // 如果当前时间不大于活动的结束时间，说明活动处于进行中，则取当前时间和活动结束时间差的秒数
            // 计算距结束的倒计时
            limitTimeGoodsDTO.setCountdownTimeWords(LimitTimeCountDownWordsEnum.FROM_END.getType());
            limitTimeGoodsDTO.setCountdownTimeStamp(DateUtil.getLocalDateTimeDiffSecond(nowTime, endTime));
            // 设置成进行中
            limitTimeGoodsDTO.setButton(SecKillGoodsStatusEnum.STARTING.getCode());
        } else {
            // 当前时间大于活动的结束时间，说明当前场次的活动已经结束，活动倒计时设置为0
            limitTimeGoodsDTO.setCountdownTimeStamp(0L);
            // 按钮设置成已结束
            limitTimeGoodsDTO.setButton(SecKillGoodsStatusEnum.END.getCode());
        }

        // 如果活动已结束，则直接退出
        if (SecKillGoodsStatusEnum.END.getCode().equals(limitTimeGoodsDTO.getButton())) {
            return;
        }
        // 超过限时购商品促销库存，按钮设置成已抢光，直接退出
        if (limitTimeGoodsDTO.getRemainStockNum() != null && limitTimeGoodsDTO.getRemainStockNum() <= NumConstant.ZERO) {
            limitTimeGoodsDTO.setButton(SecKillGoodsStatusEnum.NOT_HAVING.getCode());
            return;
        }
        // 如果商品已下架，按钮设置成已下架，直接退出
        if (GoodsStatusEnum.OFF_SHELF.getCode().equals(limitTimeGoodsDTO.getGoodsShowStatus())) {
            limitTimeGoodsDTO.setButton(SecKillGoodsStatusEnum.REMOVED.getCode());
            return;
        }
        // 如果没有可售库存了，按钮设置成已售罄
        if (limitTimeGoodsDTO.getCanSaleStockNum() != null
                && limitTimeGoodsDTO.getCanSaleStockNum().compareTo(BigDecimal.valueOf(NumConstant.ZERO)) <= NumConstant.ZERO) {
            limitTimeGoodsDTO.setButton(SecKillGoodsStatusEnum.SOLD_OUT.getCode());
        }
    }

    /**
     * 获取首页最新有效的活动场次的商品数据
     *
     * @param reqDTO        前端页面传递过来的查询参数
     * @param resultDTO     响应结果DTO
     * @param promotionList 限时购活动
     */
    private void getLatestLimitTimeGoods(ReqPromotionInfoDTO reqDTO, ResIndexLimitTimePromotionDTO resultDTO
            , List<AtomResGoodsPromotionRuleDTO> promotionList) {
        // 排序活动数据
        List<AtomResGoodsPromotionRuleDTO> sortPromotionList = this.sortPromotionData(promotionList);
        if (CollectionUtils.isNotEmpty(sortPromotionList)) {
            // 计算限时购活动数量以及活动的场次数
            Set<String> promotionNoSet = new HashSet<>();
            Set<String> periodNoSet = new HashSet<>();
            sortPromotionList.forEach(promotion -> {
                promotionNoSet.add(promotion.getPromotionNo());
                periodNoSet.add(promotion.getPeriodNo());
            });
            resultDTO.setPromotionSize(promotionNoSet.size());
            resultDTO.setPeriodSize(periodNoSet.size());
            // 如果活动等于1，则取第一个活动的promotionNo返回给前台，用于点击 “查看更多” 进入当前活动的详情页
            if (promotionNoSet.size() == NumConstant.ONE) {
                resultDTO.setPromotionNo(new ArrayList<>(promotionNoSet).get(NumConstant.ZERO));
            }
            // 存放返回的限时购商品数据
            List<ResLimitTimeGoodsDTO> limitTimeGoodsDTOList = new ArrayList<>();
            for (AtomResGoodsPromotionRuleDTO promotionDTO : sortPromotionList) {
                // 找到商品符合数量的商品后，退出循环查询
                if (CollectionUtils.isNotEmpty(limitTimeGoodsDTOList)) {
                    break;
                }
                // 查询活动场次下的商品列表
                AtomReqGoodsPromotionGoodsRelationDTO goodsRelationDTO = new AtomReqGoodsPromotionGoodsRelationDTO();
                goodsRelationDTO.setPromotionNo(promotionDTO.getPromotionNo());
                goodsRelationDTO.setPeriodNo(promotionDTO.getPeriodNo());
                ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> goodsExecuteDTO =
                        atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList(goodsRelationDTO);
                if (null == goodsExecuteDTO) {
                    throw new BaseException(CommonCode.CODE_10000003);
                }
                if (!goodsExecuteDTO.successFlag()) {
                    throw new BaseException(goodsExecuteDTO.getStatus(), goodsExecuteDTO.getMsg());
                }
                if (CollectionUtils.isNotEmpty(goodsExecuteDTO.getData())) {
                    List<ResGoodsPromotionGoodsRelationDTO> relationDTOList = BeanCopierUtil.copyList(goodsExecuteDTO.getData()
                            , ResGoodsPromotionGoodsRelationDTO.class);
                    relationDTOList.forEach(relationDTO -> {
                        // 取设置的限时购活动规则数据
                        relationDTO.setEffectiveTime(promotionDTO.getEffectiveTime());  //活动开始时间
                        relationDTO.setInvalidTime(promotionDTO.getInvalidTime());  //活动结束时间
                        relationDTO.setPromotionDate(promotionDTO.getPromotionDate());  //当前活动场次对应的具体日期
                        relationDTO.setStartTime(promotionDTO.getStartTime());    //活动时间段的开始时间
                        relationDTO.setEndTime(promotionDTO.getEndTime());    //活动时间段的结束时间
                        relationDTO.setPromotionName(promotionDTO.getPromotionName());  //活动名称
                        relationDTO.setSourceType(promotionDTO.getSourceType());    //活动来源
                        relationDTO.setPromotionType(promotionDTO.getPromotionType());  //活动类型
                        relationDTO.setUserScope(promotionDTO.getUserScope());  //活动对象
                    });
                    // 根据限时购活动关联的商品查询商品详细信息
                    this.getIndexLimitTimeGoodsDetailList(reqDTO, limitTimeGoodsDTOList, relationDTOList);
                }
            }
            resultDTO.setGoodsList(limitTimeGoodsDTOList);
        }
    }

    /**
     * 根据活动场次距当前时间的长短进行升序排
     *
     * @param promotionList 排序之前的活动数据
     * @return 排序之后的活动数据
     * <AUTHOR>
     * @date 2021-12-03
     */
    public List<AtomResGoodsPromotionRuleDTO> sortPromotionData(List<AtomResGoodsPromotionRuleDTO> promotionList) {
        // 存储返回的数据
        List<AtomResGoodsPromotionRuleDTO> resultList = new ArrayList<>();
        // 获取当前时间
        LocalDateTime localDateTime = DateUtil.getLocalDateTime();
        // 因为活动存在跨天的数据，先把活动数据拆分到对应天的场次，舍去已经过期不展示的场次数据
        promotionList.forEach(promotionDTO -> {
            long diffDay = DateUtil.getLocalDateDiffDay(promotionDTO.getEffectiveTime().toLocalDate()
                    , promotionDTO.getInvalidTime().toLocalDate());
            // 不跨天
            if (diffDay == NumConstant.ZERO) {
                // 设置具体的活动场次对应的日期
                promotionDTO.setPromotionDate(promotionDTO.getEffectiveTime().toLocalDate());
                // 具体活动场次的结束时间
                LocalDateTime endTime = LocalDateTime.of(promotionDTO.getPromotionDate(), promotionDTO.getEndTime());
                if (!(localDateTime.isAfter(endTime))) {
                    // 计算和当前时间的秒数差值，用于后续排序，找最近的活动
                    // 如果计算的值是负数，表示正在进行中，负的越多，表示该进行中的活动场次越早开始，优先展示
                    LocalDateTime startTime = LocalDateTime.of(promotionDTO.getPromotionDate(), promotionDTO.getStartTime());
                    long diffSeconds = DateUtil.getDiffSeconds(localDateTime, startTime);
                    promotionDTO.setSecondsCount(diffSeconds);
                    resultList.add(promotionDTO);
                }
            } else {
                // 跨天，按天拆分
                for (long i = 0; i <= diffDay; i++) {
                    AtomResGoodsPromotionRuleDTO entity = BeanCopierUtil.copy(promotionDTO, AtomResGoodsPromotionRuleDTO.class);
                    // 设置具体的活动场次对应的日期
                    entity.setPromotionDate(promotionDTO.getEffectiveTime().toLocalDate().plusDays(i));
                    // 当天场次的开始和结束
                    LocalDateTime startTime = LocalDateTime.of(entity.getPromotionDate(), entity.getStartTime());
                    LocalDateTime endTime = LocalDateTime.of(entity.getPromotionDate(), entity.getEndTime());
                    if (!(localDateTime.isAfter(endTime))) {
                        // 计算和当前时间的秒数差值，用于后续排序，找最近的活动
                        // 如果计算的值是负数，表示正在进行中，负的越多，表示该进行中的活动场次越早开始，优先展示
                        long diffSeconds = DateUtil.getDiffSeconds(localDateTime, startTime);
                        entity.setSecondsCount(diffSeconds);
                        resultList.add(entity);
                    }
                }
            }
        });

        // 根据优先展示规则进行排序
        resultList.sort(Comparator.comparing(AtomResGoodsPromotionRuleDTO::getSecondsCount));

        // 返回结果
        return resultList;
    }

    /**
     * 获取汇享购首页限时购活动商品详情列表
     *
     * @param reqDTO                前端页面传过来的查询参数
     * @param limitTimeGoodsDTOList 要返回的限时购活动场次下的商品数据
     * @param relationDTOList       限时购活动设置的商品数据
     */
    private void getIndexLimitTimeGoodsDetailList(ReqPromotionInfoDTO reqDTO, List<ResLimitTimeGoodsDTO> limitTimeGoodsDTOList
            , List<ResGoodsPromotionGoodsRelationDTO> relationDTOList) {
        //筛选出非子品的商品，包括主品与普通商品
        List<ResGoodsPromotionGoodsRelationDTO> notSubGoodsList = relationDTOList.stream().filter(goodsDTO ->
                WhetherEnum.NO.getCode().equals(goodsDTO.getSubGoodsFlag())).collect(Collectors.toList());
        //筛选出子品的商品
        List<ResGoodsPromotionGoodsRelationDTO> subGoodsList = relationDTOList.stream().filter(goodsDTO ->
                WhetherEnum.YES.getCode().equals(goodsDTO.getSubGoodsFlag())).collect(Collectors.toList());
        // 没有主品 + 普通商品的话，直接退出
        if (CollectionUtils.isEmpty(notSubGoodsList)) {
            return;
        }
        ReqGoodsDTO reqGoodsDTO = new ReqGoodsDTO();
        reqGoodsDTO.setStoreNo(reqDTO.getStoreNo());
        reqGoodsDTO.setNoPage();
        // 汇赚钱进入时，只查询分销商品
        if (SecKillLocationEnum.H_Z_Q_SECKILL.getCode().equals(reqDTO.getLimitTimeLocation())) {
            reqGoodsDTO.setDistributeGoodsFlag(WhetherEnum.YES.getCode());
        }
        // 可用的商品
        reqGoodsDTO.setDisableFlag(NumConstant.TWO);
        reqGoodsDTO.setGoodsNos(notSubGoodsList.stream().map(ResGoodsPromotionGoodsRelationDTO::getGoodsNo).collect(Collectors.toList()));
        //查询商品信息主品+普通商品
        log.info("--goodsAnalysisService.getGoodsPage-start-reqGoodsDTO-{}", reqGoodsDTO);
        ExecuteDTO<ExecutePageDTO<ResGoodsDTO>> goodsExecute = goodsAnalysisService.getGoodsPage(reqGoodsDTO);
        if (goodsExecute != null && goodsExecute.successFlag() && goodsExecute.getData() != null
                && CollectionUtils.isNotEmpty(goodsExecute.getData().getRows())) {
            // 汇赚钱页面，需要查询出所有子品的商品数据，用于计算佣金
            if (CollectionUtils.isNotEmpty(subGoodsList)
                    && SecKillLocationEnum.H_Z_Q_SECKILL.getCode().equals(reqDTO.getLimitTimeLocation())) {
                this.getSubGoodsInfo(subGoodsList, reqGoodsDTO);
            }

            List<ResGoodsDTO> resGoodsDTOList = goodsExecute.getData().getRows();
            //将商品信息遍历赋值到限时购活动商品列表下
            for (ResGoodsPromotionGoodsRelationDTO notSubGoods : notSubGoodsList) {
                Optional<ResGoodsDTO> optional = resGoodsDTOList.stream().
                        filter(goods -> notSubGoods.getGoodsNo().equals(goods.getGoodsNo()))
                        .findFirst();
                if (optional.isPresent()) {
                    ResGoodsDTO resGoodsDTO = optional.get();
                    ResLimitTimeGoodsDTO limitTimeGoodsDTO = BeanCopierUtil.copy(resGoodsDTO, ResLimitTimeGoodsDTO.class);
                    // 20230928 蛋品 多单位商品
                    if (MultiUnitTypeEnum.MAIN_UNIT.getCode().equals(resGoodsDTO.getMultiUnitType()) && StringUtils.isNotBlank(resGoodsDTO.getCalculationUnitName())) {
                        limitTimeGoodsDTO.setGoodsName(resGoodsDTO.getGoodsName() + "(" + resGoodsDTO.getCalculationUnitName() + ")");
                    }
                    // 取出活动商品配置的信息
                    limitTimeGoodsDTO.setLimitBuyNum(notSubGoods.getLimitBuyNum());   //商品个人限购数
                    limitTimeGoodsDTO.setPromotionPrice(notSubGoods.getPromotionPrice()); //活动价格
                    limitTimeGoodsDTO.setPromotionNo(notSubGoods.getPromotionNo());   //活动编号
                    limitTimeGoodsDTO.setPromotionName(notSubGoods.getPromotionName());   //活动名称
                    limitTimeGoodsDTO.setEffectiveTime(notSubGoods.getEffectiveTime());   //活动开始时间
                    limitTimeGoodsDTO.setInvalidTime(notSubGoods.getInvalidTime());   //活动结束时间
                    limitTimeGoodsDTO.setPromotionDate(notSubGoods.getPromotionDate()); //活动场次具体对应的日期
                    limitTimeGoodsDTO.setPeriodNo(notSubGoods.getPeriodNo());   //活动场次时间段编号
                    limitTimeGoodsDTO.setStartTime(notSubGoods.getStartTime()); //商品活动时间段开始时间
                    limitTimeGoodsDTO.setEndTime(notSubGoods.getEndTime()); //商品活动时间段结束时间
                    limitTimeGoodsDTO.setRemainStockNum(notSubGoods.getRemainStockNum()); //商品促销活动剩余库存
                    limitTimeGoodsDTO.setPromotionType(notSubGoods.getPromotionType()); //活动类型
                    limitTimeGoodsDTO.setUserScope(notSubGoods.getUserScope()); //互动对象
                    //如果是主品，则需要查询出子品的最小限时购价格
                    if (SeriesTypeEnum.PARENT_GOODS.getCode().equals(resGoodsDTO.getSeriesType())) {
                        if (CollectionUtils.isNotEmpty(subGoodsList)) {
                            // 找出对应主品以及对应活动下的子品集合
                            List<ResGoodsPromotionGoodsRelationDTO> currentParentSubList = subGoodsList.stream()
                                    .filter(subGoods -> subGoods.getParentGoodsNo().equals(notSubGoods.getGoodsNo())
                                            && subGoods.getPromotionNo().equals(notSubGoods.getPromotionNo())).collect(Collectors.toList());
                            // 计算对应主品下的所有子品中最大和最小的秒杀价格
                            if (CollectionUtils.isNotEmpty(currentParentSubList)) {
                                // 最小限时购价格
                                Optional<ResGoodsPromotionGoodsRelationDTO> min = currentParentSubList.stream()
                                        .min(Comparator.comparing(ResGoodsPromotionGoodsRelationDTO::getPromotionPrice));
                                if (min.isPresent()) {
                                    ResGoodsPromotionGoodsRelationDTO goodsRelationDTO = min.get();
                                    limitTimeGoodsDTO.setMinPromotionPrice(goodsRelationDTO.getPromotionPrice());
                                    // 个人限购数取最小限时购价格商品的个人限购数
                                    limitTimeGoodsDTO.setLimitBuyNum(goodsRelationDTO.getLimitBuyNum());
                                }
                                // 最大limitTimeGoodsDTO价格
                                Optional<ResGoodsPromotionGoodsRelationDTO> max = currentParentSubList.stream()
                                        .max(Comparator.comparing(ResGoodsPromotionGoodsRelationDTO::getPromotionPrice));
                                max.ifPresent(maxGoodsRelationDTO -> limitTimeGoodsDTO.setMaxPromotionPrice(maxGoodsRelationDTO.getPromotionPrice()));
                                // 所有子品中的活动剩余库存值汇总，用于页面展示主品的状态是不是已抢光
                                Integer remainStockTotalNum = 0;
                                for (ResGoodsPromotionGoodsRelationDTO goodsRelationDTO : currentParentSubList) {
                                    if (null != goodsRelationDTO && null != goodsRelationDTO.getRemainStockNum()) {
                                        remainStockTotalNum = remainStockTotalNum + goodsRelationDTO.getRemainStockNum();
                                    }
                                }
                                limitTimeGoodsDTO.setRemainStockNum(remainStockTotalNum); //商品促销活动剩余库存
                                // 计算酬劳
                                this.calculateReward(limitTimeGoodsDTO, currentParentSubList);
                            }
                        }
                    } else {
                        // 非系列商品
                        List<ResGoodsPromotionGoodsRelationDTO> goodsList = new ArrayList<>();
                        notSubGoods.setCloudPoolSupplyPrice(resGoodsDTO.getCloudPoolSupplyPrice());
                        goodsList.add(notSubGoods);
                        // 计算酬劳
                        this.calculateReward(limitTimeGoodsDTO, goodsList);
                    }

                    limitTimeGoodsDTOList.add(limitTimeGoodsDTO);
                    // 商品限时购商品等于 3 ，结束逻辑判断
                    if (limitTimeGoodsDTOList.size() > NumConstant.THREE) {
                        return;
                    }
                }
            }
        }
    }

    /**
     * 查询子品信息，用于计算酬劳
     *
     * @param subGoodsList 限时购活动关联的子品数据
     * @param reqGoodsDTO  查询的请求参数
     * <AUTHOR>
     * @date 2021-09-28
     */
    private void getSubGoodsInfo(List<ResGoodsPromotionGoodsRelationDTO> subGoodsList
            , ReqGoodsDTO reqGoodsDTO) {
        reqGoodsDTO.setGoodsNos(subGoodsList.stream().map(ResGoodsPromotionGoodsRelationDTO::getGoodsNo).collect(Collectors.toList()));
        ExecuteDTO<ExecutePageDTO<ResGoodsDTO>> subGoodsExecuteDTO = goodsAnalysisService.getGoodsPage(reqGoodsDTO);
        log.info("查询子品的商品信息结果出参：{}", subGoodsExecuteDTO);
        if (subGoodsExecuteDTO == null || !subGoodsExecuteDTO.successFlag()) {
            throw new BaseException(CommonCode.CODE_10000003);
        }
        // 取出子品相关信息
        if (subGoodsExecuteDTO.getData() != null && CollectionUtils.isNotEmpty(subGoodsExecuteDTO.getData().getRows())) {
            subGoodsList.forEach(subGoods -> {
                Optional<ResGoodsDTO> optional = subGoodsExecuteDTO.getData().getRows().stream()
                        .filter(goods -> subGoods.getGoodsNo().equals(goods.getGoodsNo()))
                        .findFirst();
                if (optional.isPresent()) {
                    ResGoodsDTO goodsDTO = optional.get();
                    // 取零售价格，用于计算佣金
                    subGoods.setRetailPrice(goodsDTO.getRetailPrice()); //子品的零售价
                }
            });
        }
    }

    /**
     * 计算酬劳
     *
     * @param limitTimeGoodsDTO 响应的限时购活动商品数据
     * @param goodsList         限时购活动关联的商品
     */
    private void calculateReward(ResLimitTimeGoodsDTO limitTimeGoodsDTO, List<ResGoodsPromotionGoodsRelationDTO> goodsList) {
        // 获取云池商品的主品佣金信息
        if (GoodsSourceTypeEnum.CLOUD_POOL_DISTRIBUTE.getCode().equals(limitTimeGoodsDTO.getGoodsSourceType())) {
            List<BigDecimal> calculateList = new ArrayList<>();
            for (ResGoodsPromotionGoodsRelationDTO goodsRelationDTO : goodsList) {
                // 活动费用
                BigDecimal activityExpenses = goodsRelationDTO.getActivityExpenses();
                // 活动价格
                BigDecimal promotionPrice = goodsRelationDTO.getPromotionPrice();
                // 供货价格
                BigDecimal cloudPoolSupplyPrice = goodsRelationDTO.getCloudPoolSupplyPrice();
                // 佣金比率
                BigDecimal agentCommissionRatio = goodsRelationDTO.getAgentCommissionRatio();
                if (null == activityExpenses || null == promotionPrice || null == cloudPoolSupplyPrice || null == agentCommissionRatio) {
                    continue;
                }
                // 计算佣金
                BigDecimal oneHundred = new BigDecimal(NumConstant.ONE_HUNDRED);
                // 按比例计算佣金 (活动费用+活动价格-供货价)*百分比
                BigDecimal calculate = BigDecimalUtil.setScale(agentCommissionRatio.divide(oneHundred)
                        .multiply(activityExpenses.add(promotionPrice).subtract(cloudPoolSupplyPrice)));
                calculateList.add(calculate);
            }
            // 求最大值
            if (CollectionUtils.isNotEmpty(calculateList)) {
                BigDecimal calculateMax = Collections.max(calculateList);
                log.info("******************云池商品最大佣金计算值：{}", calculateMax);
                calculateMax = BigDecimalUtil.setScale(calculateMax);
                limitTimeGoodsDTO.setReward("￥" + calculateMax);
            }
            limitTimeGoodsDTO.setRewardType(RewardTypeEnum.YJ.getCode());
            limitTimeGoodsDTO.setRewardTypeValue(RewardTypeEnum.YJ.getType());
        }

        // 分销商品
        if (NumConstant.TWO == limitTimeGoodsDTO.getDistributeGoodsFlag()) {
            // 返回的酬劳信息
            StringBuilder result = new StringBuilder();
            // 分销酬劳设置
            AtomReqAgentMarketRewardsetDTO atomReqAgentMarketRewardsetDTO = new AtomReqAgentMarketRewardsetDTO();
            // limitTimeGoodsDTO是非子品，所以直接取goodsNo
            atomReqAgentMarketRewardsetDTO.setTaskOrGoodsNo(limitTimeGoodsDTO.getGoodsNo());
            atomReqAgentMarketRewardsetDTO.setMarketType(MarketTypeEnum.StoreDistribution.getCode());
            ExecuteDTO<List<AtomResAgentMarketRewardsetDTO>> listExecuteDTO =
                    atomAgentMarketRewardsetAnalysisService.getList(atomReqAgentMarketRewardsetDTO);
            if (null == listExecuteDTO || !listExecuteDTO.successFlag()) {
                throw new BaseException(CommonCode.CODE_10000003);
            }
            // 取最大的活动价格
            BigDecimal maxPromotionPrice = null;
            Optional<ResGoodsPromotionGoodsRelationDTO> realSalePriceOptional = goodsList.stream()
                    .max(Comparator.comparing(ResGoodsPromotionGoodsRelationDTO::getPromotionPrice));
            if (realSalePriceOptional.isPresent()) {
                maxPromotionPrice = realSalePriceOptional.get().getPromotionPrice();
            }
            List<AtomResAgentMarketRewardsetDTO> rewardLists = listExecuteDTO.getData();
            if (CollectionUtils.isNotEmpty(rewardLists)) {
                AtomResAgentMarketRewardsetDTO atomResAgentMarketRewardsetDTO = rewardLists.get(0);
                if (null != atomResAgentMarketRewardsetDTO) {
                    // 佣金类型
                    Integer rewardType = atomResAgentMarketRewardsetDTO.getRewardType();
                    limitTimeGoodsDTO.setRewardType(atomResAgentMarketRewardsetDTO.getRewardType());
                    if (RewardTypeEnum.getByCode(rewardType) != null) {
                        limitTimeGoodsDTO.setRewardTypeValue(RewardTypeEnum.getByCode(rewardType).getType());
                    }
                    switch (rewardType) {
                        case NumConstant.ONE:
                            // 佣金
                            BigDecimal oneHundred = new BigDecimal(NumConstant.ONE_HUNDRED);
                            if (null != atomResAgentMarketRewardsetDTO.getYjOrHjb() && maxPromotionPrice != null) {
                                // 按比例计算佣金
                                BigDecimal calculateResult = BigDecimalUtil.setScale(atomResAgentMarketRewardsetDTO.getYjOrHjb().divide(oneHundred)
                                        .multiply(maxPromotionPrice));
                                // 四舍五入保留2位小数
                                calculateResult = BigDecimalUtil.setScale(calculateResult);
                                result.append("￥").append(calculateResult);
                            } else {
                                log.info("*********佣金接口返回空值，请后台查看问题*********************");
                            }
                            break;
                        case NumConstant.TWO:
                            if (null != atomResAgentMarketRewardsetDTO.getYjOrHjb()) {
                                // 汇金币
                                result.append("汇金币").append(atomResAgentMarketRewardsetDTO.getYjOrHjb()).append("个");
                            } else {
                                log.info("*********汇金币接口返回空值，请后台查看问题*********************");
                            }
                            break;
                        case NumConstant.THREE:
                            // 礼品
                            result.append(atomResAgentMarketRewardsetDTO.getRelatedName());
                            break;
                        case NumConstant.FOUR:
                            // 现金券
                            result.append("现金券(满￥").append(atomResAgentMarketRewardsetDTO.getRelatedUp()).append("减")
                                    .append(atomResAgentMarketRewardsetDTO.getRelatedLess()).append(")");
                            break;
                        case NumConstant.FIVE:
                            // 服务劵
                            result.append(atomResAgentMarketRewardsetDTO.getRelatedName());
                            break;
                        default:
                            log.info("*********酬劳设置未匹配到类型，请后台查看问题*********************");
                    }
                }
            }
            if (StringUtils.isNotBlank(result.toString())) {
                limitTimeGoodsDTO.setReward(result.toString());
            }
        }
    }

    /**
     * 限时购活动详情商品聚合页
     *
     * @param reqDTO 查询参数
     * @return 限时购活动详情商品
     * <AUTHOR>
     * @date 2021-10-08
     */
    @Override
    public ExecuteDTO<ResLimitTimeGoodsPageDTO> getLimitTimeGoodsPage(ReqPromotionInfoDTO reqDTO) {
        log.info("getLimitTimeGoodsPage-入参：{}", reqDTO);
        // 入参校验
        limitTimePromotionAssert.getLimitTimeGoodsPageAssert(reqDTO);
        // 根据活动编号查询活动信息
        AtomReqPromotionInfoDTO promotionInfoDTO = BeanCopierUtil.copy(reqDTO, AtomReqPromotionInfoDTO.class);
        ExecuteDTO<AtomResGoodsPromotionRuleDTO> executeDTO =
                atomGoodsPromotionRuleAnalysisService.getGoodsPromotionRuleByPromotionNo(promotionInfoDTO);
        if (null == executeDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        if (executeDTO.getData() == null) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000100);
        }
        ResGoodsPromotionRuleDTO promotionRuleDTO = BeanCopierUtil.copy(executeDTO.getData(), ResGoodsPromotionRuleDTO.class);

        // 查询限时购活动时间场次数据
        AtomReqGoodsPromotionPeriodDTO reqPeriodDTO = new AtomReqGoodsPromotionPeriodDTO();
        reqPeriodDTO.setPromotionNo(promotionRuleDTO.getPromotionNo());
        // 如果是活动详情页查询，则只查询指定的场次数据
        if (WhetherEnum.YES.getCode().equals(reqDTO.getQuerySubGoodsFlag())) {
            reqPeriodDTO.setPeriodNo(reqDTO.getPeriodNo());
        }
        ExecuteDTO<List<AtomResGoodsPromotionPeriodDTO>> executePeriodDTO =
                atomGoodsPromotionPeriodAnalysisService.getGoodsPromotionPeriodByPromotionNo(reqPeriodDTO);
        if (null == executePeriodDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!executePeriodDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }

        // 封装响应的结果
        ResLimitTimeGoodsPageDTO resLimitTimeGoodsPageDTO = new ResLimitTimeGoodsPageDTO();
        promotionRuleDTO.setLimitTimePromotionPeriods(BeanCopierUtil.copyList(executePeriodDTO.getData(), ResGoodsPromotionPeriodDTO.class));
        resLimitTimeGoodsPageDTO.setLimitTimePromotion(promotionRuleDTO);

        // 查询距当前时间最近场次的活动商品数据，返回给前端展示
        this.getLatestPeriodPromotionGoods(resLimitTimeGoodsPageDTO, reqDTO);

        // 返回结果
        return ExecuteDTO.success(resLimitTimeGoodsPageDTO);
    }

    /**
     * 查询距当前时间最近场次的活动商品数据
     *
     * <AUTHOR>
     * @date 2021-10-08
     */
    private void getLatestPeriodPromotionGoods(ResLimitTimeGoodsPageDTO resLimitTimeGoodsPageDTO, ReqPromotionInfoDTO reqDTO) {
        ResGoodsPromotionRuleDTO limitTimePromotion = resLimitTimeGoodsPageDTO.getLimitTimePromotion();
        if (limitTimePromotion != null && CollectionUtils.isNotEmpty(limitTimePromotion.getLimitTimePromotionPeriods())) {
            // 限时购活动商品详情页查询调用，根据页面传递的参数，只会根据活动编号查询出一个确定的活动场次
            if (WhetherEnum.YES.getCode().equals(reqDTO.getQuerySubGoodsFlag())) {
                limitTimePromotion.getLimitTimePromotionPeriods().get(0).setPromotionDate(reqDTO.getPromotionDate());
                ResGoodsPromotionPeriodDTO periodDTO = limitTimePromotion.getLimitTimePromotionPeriods().get(0);
                resLimitTimeGoodsPageDTO.setGoodsList(this.getLimitTimePromotionGoodsPage(reqDTO, limitTimePromotion
                        , periodDTO));
            } else {
                // 非限时购活动商品详情页调用
                // 活动跨天，拓展每天的场次数据给前端展示
                long diffDay = DateUtil.getLocalDateDiffDay(limitTimePromotion.getEffectiveTime().toLocalDate()
                        , limitTimePromotion.getInvalidTime().toLocalDate());
                List<ResGoodsPromotionPeriodDTO> periodDTOList = new ArrayList<>();
                for (long i = 0; i <= diffDay; i++) {
                    final long dayNum = i;
                    limitTimePromotion.getLimitTimePromotionPeriods().forEach(periodDTO -> {
                        ResGoodsPromotionPeriodDTO newPeriodDTO = BeanCopierUtil.copy(periodDTO, ResGoodsPromotionPeriodDTO.class);
                        newPeriodDTO.setPromotionDate(DateUtil.getLocalDateByTime(limitTimePromotion.getEffectiveTime()).plusDays(dayNum));
                        periodDTOList.add(newPeriodDTO);
                    });
                }
                limitTimePromotion.setLimitTimePromotionPeriods(periodDTOList);
                resLimitTimeGoodsPageDTO.setLimitTimePromotion(limitTimePromotion);

                // 找出页面凸显的时间段
                // 备注：同一个活动场次下，只会有一个活动场次是进行中
                LocalDateTime localDateTime = DateUtil.getLocalDateTime();
                Long minDiffSeconds = null; // 距当前时间相差最小的秒数
                int index = 0; //记录距当前最近场次数据的索引值，默认开始取第一个
                for (int i = 0; i < limitTimePromotion.getLimitTimePromotionPeriods().size(); i++) {
                    ResGoodsPromotionPeriodDTO periodDTO = limitTimePromotion.getLimitTimePromotionPeriods().get(i);
                    // 刚进入限时购活动详情页
                    if (StringUtils.isBlank(reqDTO.getPeriodNo()) && reqDTO.getPromotionDate() == null) {
                        // 活动场次开始时间
                        LocalDateTime promotionStartTime = LocalDateTime.of(periodDTO.getPromotionDate(), periodDTO.getStartTime());
                        // 活动场次结束时间
                        LocalDateTime promotionEndTime = LocalDateTime.of(periodDTO.getPromotionDate(), periodDTO.getEndTime());
                        // 计算当前时间和当前活动场次相差的秒数
                        Long diffSecond;
                        // 如果活动场次没有开始，则计算当前时间和场次开始时间的秒数差
                        if (localDateTime.isBefore(promotionStartTime)) {
                            diffSecond = DateUtil.getLocalDateTimeDiffSecond(localDateTime, promotionStartTime);
                        } else if (!localDateTime.isAfter(promotionEndTime)) {
                            // 如果活动场次进行中，则计算当前时间和场次的秒数差设置为0
                            diffSecond = 0L;
                        } else {
                            // 如果活动场次已经结束，则计算当前时间和场次结束时间的秒数差
                            diffSecond = DateUtil.getLocalDateTimeDiffSecond(localDateTime, promotionEndTime);
                        }
                        // 比较时间差，找出凸显的活动场场次
                        if (minDiffSeconds == null) {
                            minDiffSeconds = diffSecond;
                            index = i;
                            limitTimePromotion.getLimitTimePromotionPeriods().get(index).setShowFlag(WhetherEnum.YES.getCode());
                        } else if (minDiffSeconds.compareTo(diffSecond) > 0) {
                            minDiffSeconds = diffSecond;
                            // 将原先设置成凸显的标识置为非凸显
                            limitTimePromotion.getLimitTimePromotionPeriods().get(index).setShowFlag(WhetherEnum.NO.getCode());
                            // 更新距当前时间最近的索引记录
                            index = i;
                            // 将新的距当前最近的场次设置成凸显标识
                            limitTimePromotion.getLimitTimePromotionPeriods().get(index).setShowFlag(WhetherEnum.YES.getCode());
                        } else {
                            // 标记为非凸显
                            limitTimePromotion.getLimitTimePromotionPeriods().get(i).setShowFlag(WhetherEnum.NO.getCode());
                        }
                    } else {
                        // 点击活动场次tab时，直接匹配对应的场次和活动日期，设置凸显状态
                        if (reqDTO.getPeriodNo().equals(periodDTO.getPeriodNo()) && reqDTO.getPromotionDate().equals(periodDTO.getPromotionDate())) {
                            limitTimePromotion.getLimitTimePromotionPeriods().get(i).setShowFlag(WhetherEnum.YES.getCode());
                            index = i;
                        } else {
                            limitTimePromotion.getLimitTimePromotionPeriods().get(i).setShowFlag(WhetherEnum.NO.getCode());
                        }
                    }
                }

                // 查询最近场次的商品数据
                ResGoodsPromotionPeriodDTO periodDTO = limitTimePromotion.getLimitTimePromotionPeriods().get(index);
                resLimitTimeGoodsPageDTO.setGoodsList(this.getLimitTimePromotionGoodsPage(reqDTO, limitTimePromotion
                        , periodDTO));

                // 该场次的活动开始时间
                LocalDateTime startTime = LocalDateTime.of(periodDTO.getPromotionDate(), periodDTO.getStartTime());
                // 该场次的活动结束时间
                LocalDateTime endTime = LocalDateTime.of(periodDTO.getPromotionDate(), periodDTO.getEndTime());
                // 如果活动场次未开始，则统计已预约的数量
                if (localDateTime.isBefore(startTime)) {
                    AtomReqGoodsPromotionBookingDTO bookingDTO = new AtomReqGoodsPromotionBookingDTO();
                    bookingDTO.setPromotionNo(periodDTO.getPromotionNo());
                    bookingDTO.setPromotionType(PromotionTypeEnum.LIMIT_TIME.getCode());
                    bookingDTO.setPromotionDate(periodDTO.getPromotionDate());
                    bookingDTO.setPeriodNo(periodDTO.getPeriodNo());
                    ExecuteDTO<Integer> bookingNumExecuteDTO = atomGoodsPromotionBookingAnalysisService.getPromotionGoodsBookingNum(bookingDTO);
                    log.info("查询未开始活动场次下商品预约数出参：{}", bookingNumExecuteDTO);
                    if (null == bookingNumExecuteDTO) {
                        throw new BaseException(CommonCode.CODE_10000003);
                    }
                    if (!bookingNumExecuteDTO.successFlag()) {
                        throw new BaseException(bookingNumExecuteDTO.getStatus(), bookingNumExecuteDTO.getMsg());
                    }
                    Integer bookingNum = bookingNumExecuteDTO.getData() == null ? 0 : bookingNumExecuteDTO.getData();
                    limitTimePromotion.getLimitTimePromotionPeriods().get(index).setBookingNum(bookingNum);
                } else if (!(localDateTime.isAfter(endTime))) {
                    // 正在进行中的场次，统计活动场次下的商品下单总数
                    // 促销活动各场次已购买商品数量 promotion:period:buygoodsnum:活动编号:年月日:时间段编号
                    String redisKey = String.format(PromotionKeyConstant.PROMOTION_PERIOD_BUYGOODSNUM_PREFIX,
                            periodDTO.getPromotionNo(),
                            periodDTO.getPromotionDate().toString().replaceAll("-", ""),
                            periodDTO.getPeriodNo());
                    Object orderNum = redisBaseUtil.get(redisKey);
                    limitTimePromotion.getLimitTimePromotionPeriods().get(index)
                            .setOrderNum(orderNum == null ? 0 : Integer.parseInt(orderNum.toString()));
                }
            }
        }
    }

    /**
     * 获取限时购活动下的商品数据
     *
     * @param reqDTO             页面请求参数
     * @param limitTimePromotion 限时购活动信息
     * @param periodDTO          限时购活动场次信息
     * @return 限时购活动下的商品数据
     * <AUTHOR>
     * @date 2021-10-08
     */
    private List<ResLimitTimeGoodsDTO> getLimitTimePromotionGoodsPage(ReqPromotionInfoDTO reqDTO, ResGoodsPromotionRuleDTO limitTimePromotion
            , ResGoodsPromotionPeriodDTO periodDTO) {
        AtomReqGoodsPromotionGoodsRelationDTO reqGoodsRelationDTO = BeanCopierUtil.copy(reqDTO, AtomReqGoodsPromotionGoodsRelationDTO.class);
        reqGoodsRelationDTO.setPromotionNo(periodDTO.getPromotionNo());
        reqGoodsRelationDTO.setPeriodNo(periodDTO.getPeriodNo());
        ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> goodsExecuteDTO =
                atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList(reqGoodsRelationDTO);
        if (null == goodsExecuteDTO) {
            throw new BaseException(CommonCode.CODE_10000003);
        }
        if (!goodsExecuteDTO.successFlag()) {
            throw new BaseException(goodsExecuteDTO.getStatus(), goodsExecuteDTO.getMsg());
        }

        // 返回的活动场次下的商品数据
        List<ResLimitTimeGoodsDTO> limitTimeGoodsDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(goodsExecuteDTO.getData())) {
            List<ResGoodsPromotionGoodsRelationDTO> relationDTOList = BeanCopierUtil.copyList(
                    goodsExecuteDTO.getData(), ResGoodsPromotionGoodsRelationDTO.class);
            //筛选出非子品的商品，包括主品与普通商品
            List<ResGoodsPromotionGoodsRelationDTO> notSubGoodsList = relationDTOList.stream().filter(goodsDTO ->
                    WhetherEnum.NO.getCode().equals(goodsDTO.getSubGoodsFlag())).collect(Collectors.toList());
            //筛选出子品的商品
            List<ResGoodsPromotionGoodsRelationDTO> subGoodsList = relationDTOList.stream().filter(goodsDTO ->
                    WhetherEnum.YES.getCode().equals(goodsDTO.getSubGoodsFlag())).collect(Collectors.toList());
            //列表查询排除子品，限时购活动商品聚合页调用
            if (!WhetherEnum.YES.getCode().equals(reqDTO.getQuerySubGoodsFlag())) {
                this.exchangeGoodsPromotionGoodsInfo(reqDTO, limitTimePromotion, periodDTO, limitTimeGoodsDTOList
                        , notSubGoodsList, subGoodsList);
            } else {
                //指定商品编号查询不排除子品，限时购活动商品详情页调用
                this.exchangeGoodsPromotionGoodsInfo(reqDTO, limitTimePromotion, periodDTO, limitTimeGoodsDTOList
                        , relationDTOList, subGoodsList);
            }
        }
        return limitTimeGoodsDTOList;
    }

    /**
     * 追加限时购活动商品信息
     *
     * @param reqDTO                查询参数
     * @param limitTimePromotion    限时购活动信息
     * @param periodDTO             活动场次数据
     * @param limitTimeGoodsDTOList 获取的限时购活动商品信息
     * @param goodsList             限时购活动关联的商品
     * @param subGoodsList          限时购活动关联的商品的子品数据
     */
    private void exchangeGoodsPromotionGoodsInfo(ReqPromotionInfoDTO reqDTO, ResGoodsPromotionRuleDTO limitTimePromotion
            , ResGoodsPromotionPeriodDTO periodDTO, List<ResLimitTimeGoodsDTO> limitTimeGoodsDTOList
            , List<ResGoodsPromotionGoodsRelationDTO> goodsList, List<ResGoodsPromotionGoodsRelationDTO> subGoodsList) {
        ReqGoodsDTO reqGoodsDTO = new ReqGoodsDTO();
        //店铺活动则根据goodsNo查询
        reqGoodsDTO.setGoodsNos(goodsList.stream().map(ResGoodsPromotionGoodsRelationDTO::getGoodsNo).collect(Collectors.toList()));
        reqGoodsDTO.setNoPage();
        reqGoodsDTO.setStoreNo(reqDTO.getStoreNo());
        // 限时购活动商品列表调用，排除子品，必须保证查询的商品状态是上架的
//        if (!WhetherEnum.YES.getCode().equals(reqDTO.getQuerySubGoodsFlag())) {
//            //上架状态
//            reqGoodsDTO.setGoodsStatus(GoodsStatusEnum.ON_SHELF.getCode());
//        }
        // 可用的商品
        reqGoodsDTO.setDisableFlag(NumConstant.TWO);
        // 如果是汇赚钱进入限时购商品聚合页列表，只查询分销商品
        if (LimitTimeLocationEnum.H_Z_Q_LIMIT_TIME.getCode().equals(reqDTO.getLimitTimeLocation())) {
            reqGoodsDTO.setDistributeGoodsFlag(WhetherEnum.YES.getCode());
        }
        //查询商品信息
        log.info("--goodsAnalysisService.getGoodsPage-start-reqGoodsDTO-{}", reqGoodsDTO);
        ExecuteDTO<ExecutePageDTO<ResGoodsDTO>> goodsExecute = goodsAnalysisService.getGoodsPage(reqGoodsDTO);
        log.info("--goodsAnalysisService.getGoodsPage-end");
        if (goodsExecute != null && goodsExecute.successFlag() && goodsExecute.getData() != null
                && CollectionUtils.isNotEmpty(goodsExecute.getData().getRows())) {
            List<ResGoodsDTO> resGoodsDTOList = goodsExecute.getData().getRows();
            // 查询出所有子品的商品数据，用于计算佣金
            if (CollectionUtils.isNotEmpty(subGoodsList)) {
                this.getSubGoodsInfo(subGoodsList, reqGoodsDTO);
            }
            //将商品信息遍历赋值到限时购活动商品列表下
            goodsList.forEach(goodsDTO -> {
                Optional<ResGoodsDTO> optional = resGoodsDTOList.stream()
                        .filter(goods -> goodsDTO.getGoodsNo().equals(goods.getGoodsNo())).findFirst();
                if (optional.isPresent()) {
                    ResGoodsDTO resGoodsDTO = optional.get();
                    ResLimitTimeGoodsDTO limitTimeGoodsDTO = BeanCopierUtil.copy(resGoodsDTO, ResLimitTimeGoodsDTO.class);
                    // 20230928 蛋品 多单位商品
                    if (MultiUnitTypeEnum.MAIN_UNIT.getCode().equals(resGoodsDTO.getMultiUnitType()) && StringUtils.isNotBlank(resGoodsDTO.getCalculationUnitName())) {
                        limitTimeGoodsDTO.setGoodsName(resGoodsDTO.getGoodsName() + "(" + resGoodsDTO.getCalculationUnitName() + ")");
                    }
                    limitTimeGoodsDTO.setLimitBuyNum(goodsDTO.getLimitBuyNum());    //个人限购数
                    limitTimeGoodsDTO.setPromotionPrice(goodsDTO.getPromotionPrice());    //限时购价格
                    limitTimeGoodsDTO.setPromotionNo(limitTimePromotion.getPromotionNo());  //活动编号
                    limitTimeGoodsDTO.setPromotionName(limitTimePromotion.getPromotionName());  //活动名称
                    limitTimeGoodsDTO.setPeriodNo(goodsDTO.getPeriodNo());  //活动场次编号
                    limitTimeGoodsDTO.setEffectiveTime(limitTimePromotion.getEffectiveTime());  //活动有效期开始时间
                    limitTimeGoodsDTO.setInvalidTime(limitTimePromotion.getInvalidTime());  //活动有效期结束时间
                    limitTimeGoodsDTO.setPromotionDate(periodDTO.getPromotionDate());   //当前活动场次对应的活动日期
                    limitTimeGoodsDTO.setStartTime(periodDTO.getStartTime());    //活动场次开始时间
                    limitTimeGoodsDTO.setEndTime(periodDTO.getEndTime());    //活动场次结束时间
                    limitTimeGoodsDTO.setRemainStockNum(goodsDTO.getRemainStockNum());    //活动商品剩余促销库存
                    limitTimeGoodsDTO.setSettingStockNum(goodsDTO.getSettingStockNum());    //商品设置的活动库存
                    limitTimeGoodsDTO.setPromotionType(limitTimePromotion.getPromotionType());  //活动类型
                    limitTimeGoodsDTO.setUserScope(limitTimePromotion.getUserScope());  //活动对象
                    limitTimeGoodsDTO.setUpDownFlag(limitTimePromotion.getUpDownFlag());    //活动上下架状态
                    limitTimeGoodsDTO.setDeliveryFlag(limitTimePromotion.getDeliveryFlag());    //配送方式是否以商品自身配置为主
                    limitTimeGoodsDTO.setDeliveryWay(limitTimePromotion.getDeliveryWay());  //活动设置的配送方式
                    // 对于限时购活动商品详情页调用时，相关取数据的逻辑统一放到了 hxg 应用层做处理
                    if (!WhetherEnum.YES.getCode().equals(reqDTO.getQuerySubGoodsFlag())) {
                        //如果是主品，则需要查询出子品的最小限时购价
                        if (SeriesTypeEnum.PARENT_GOODS.getCode().equals(resGoodsDTO.getSeriesType()) && CollectionUtils.isNotEmpty(subGoodsList)) {
                            // 找出对应主品下的子品商品
                            List<ResGoodsPromotionGoodsRelationDTO> currentParentSubList = subGoodsList.stream()
                                    .filter(subGoods -> subGoods.getParentGoodsNo().equals(goodsDTO.getGoodsNo())).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(currentParentSubList)) {
                                // 最小限时购价
                                Optional<ResGoodsPromotionGoodsRelationDTO> minOptional = subGoodsList.stream().filter(
                                        subGoods -> subGoods.getParentGoodsNo().equals(goodsDTO.getGoodsNo()))
                                        .min(Comparator.comparing(ResGoodsPromotionGoodsRelationDTO::getPromotionPrice));
                                //设置最小限时购价
                                if (minOptional.isPresent()) {
                                    ResGoodsPromotionGoodsRelationDTO goodsRelationDTO = minOptional.get();
                                    limitTimeGoodsDTO.setMinPromotionPrice(goodsRelationDTO.getPromotionPrice());
                                    // 个人限购数取最低限时购价格商品对应的个人限购数
                                    limitTimeGoodsDTO.setLimitBuyNum(goodsRelationDTO.getLimitBuyNum());
                                }
                                // 取最大限时购价
                                Optional<ResGoodsPromotionGoodsRelationDTO> maxOptional = subGoodsList.stream().filter(
                                        subGoods -> subGoods.getParentGoodsNo().equals(goodsDTO.getGoodsNo()))
                                        .max(Comparator.comparing(ResGoodsPromotionGoodsRelationDTO::getPromotionPrice));
                                maxOptional.ifPresent(resGoodsPromotionGoodsRelationDTO -> limitTimeGoodsDTO.setMaxPromotionPrice(
                                        resGoodsPromotionGoodsRelationDTO.getPromotionPrice()));

                                // 汇总子品中所有的活动剩余库存以及设置的活动库存
                                Integer remainStockTotalNum = 0;
                                Integer settingStockTotalNum = 0;
                                for (ResGoodsPromotionGoodsRelationDTO goodsRelationDTO : currentParentSubList) {
                                    if (null != goodsRelationDTO && null != goodsRelationDTO.getRemainStockNum()) {
                                        remainStockTotalNum = remainStockTotalNum + goodsRelationDTO.getRemainStockNum();
                                    }
                                    if (null != goodsRelationDTO && null != goodsRelationDTO.getSettingStockNum()) {
                                        settingStockTotalNum = settingStockTotalNum + goodsRelationDTO.getSettingStockNum();
                                    }
                                }
                                limitTimeGoodsDTO.setRemainStockNum(remainStockTotalNum);    //所有子品的活动商品剩余促销库存
                                limitTimeGoodsDTO.setSettingStockNum(settingStockTotalNum);   //所有子品的设置活动库存
                                // 计算酬劳
                                this.calculateReward(limitTimeGoodsDTO, currentParentSubList);
                            }
                        } else {
                            // 非系列商品
                            List<ResGoodsPromotionGoodsRelationDTO> goods = new ArrayList<>();
                            goodsDTO.setCloudPoolSupplyPrice(resGoodsDTO.getCloudPoolSupplyPrice());
                            goods.add(goodsDTO);
                            // 计算酬劳
                            this.calculateReward(limitTimeGoodsDTO, goods);
                        }
                    }
                    limitTimeGoodsDTOList.add(limitTimeGoodsDTO);
                }
            });
        }
    }

    /**
     * 限时购活动商品下单校验
     *
     * @param reqDTO
     * @return
     * <AUTHOR>
     * @date 2021-10-22
     */
    @Override
    public ExecuteDTO limitTimeGoodsOrderCheck(ReqPromotionGoodsOrderCheckDTO reqDTO) {
        log.info("limitTimeGoodsPlacingOrderCheck-入参：{}", reqDTO);
        // 入参校验
        limitTimePromotionAssert.limitTimeGoodsOrderCheckAssert(reqDTO);

        // 根据商品编号查询商品信息
        ReqGoodsDTO goodsDto = new ReqGoodsDTO();
        goodsDto.setDisableFlag(NumConstant.TWO);   // 查询有效的商品
        goodsDto.setGoodsNo(reqDTO.getGoodsNo());
        ExecuteDTO<ResGoodsDTO> goodsInfoExecuteDTO = goodsAnalysisService.getGoods(goodsDto);
        if (null == goodsInfoExecuteDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!goodsInfoExecuteDTO.successFlag()) {
            return ExecuteDTO.error(goodsInfoExecuteDTO.getStatus(), goodsInfoExecuteDTO.getMsg());
        }
        if (null == goodsInfoExecuteDTO.getData()) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000616);
        }
        ResGoodsDTO goodsInfo = goodsInfoExecuteDTO.getData();
        BigDecimal canSaleStockNum = BigDecimal.valueOf(0);
        if (goodsInfo.getCanSaleStockNum() != null) {
            canSaleStockNum = goodsInfo.getCanSaleStockNum();
        }
        if ((canSaleStockNum.subtract(BigDecimal.valueOf(reqDTO.getBuyNum()))).compareTo(BigDecimal.valueOf(0)) < 0) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000625);
        }

        // 云池商品时，取cloudPoolGoodsNo，否则取 goodsNo
        String goodsNo;
        if (GoodsSourceTypeEnum.CLOUD_POOL_DISTRIBUTE.getCode().equals(goodsInfo.getGoodsSourceType())) {
            goodsNo = goodsInfo.getCloudPoolGoodsNo();
        } else {
            goodsNo = goodsInfo.getGoodsNo();
        }

        // 查询限时购活动商品配置的活动信息
        AtomReqGoodsPromotionGoodsRelationDTO goodsRelationDTO = new AtomReqGoodsPromotionGoodsRelationDTO();
        goodsRelationDTO.setPromotionNo(reqDTO.getPromotionNo());
        goodsRelationDTO.setPeriodNo(reqDTO.getPeriodNo());
        goodsRelationDTO.setGoodsNo(goodsNo);
        ExecuteDTO<AtomResGoodsPromotionGoodsRelationDTO> goodsAndRuleExecuteDTO = atomGoodsPromotionGoodsRelationAnalysisService.getGoodsAndRule(goodsRelationDTO);
        if (null == goodsAndRuleExecuteDTO || !goodsAndRuleExecuteDTO.successFlag()) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (null == goodsAndRuleExecuteDTO.getData()) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000616);
        }
        AtomResGoodsPromotionGoodsRelationDTO goodsAndRule = goodsAndRuleExecuteDTO.getData();
        // 如果活动配置限制新用户参与时，校验用户是否为新用户
        if (UserScopeEnum.NEW_FANS.getCode().equals(goodsAndRule.getUserScope())) {
            // 判断是否限制新老用户，如果是云池商品，表示平台，store传 null
            ExecuteDTO<Boolean> checkNewFansExecuteDTO = preDeterminedCheckService.checkNewFans(reqDTO.getFanNo(),
                    GoodsSourceTypeEnum.CLOUD_POOL_DISTRIBUTE.getCode().equals(goodsInfo.getGoodsSourceType()) ? null : reqDTO.getStoreNo());
            if (!checkNewFansExecuteDTO.successFlag()) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            // 活动设置了限制新用户，但该用户已经下过单不是新用户了，提示信息给前端
            if (!checkNewFansExecuteDTO.getData()) {
                return ExecuteDTO.error(MarketErrorCode.CODE_17000626);
            }
        } else if (UserScopeEnum.OLD_FANS.getCode().equals(goodsAndRule.getUserScope())) {
            // 如果活动配置限制老用户参与时，校验用户是否为老用户
            // 判断是否限制新老用户，如果是云池商品，表示平台，store传 null
            ExecuteDTO<Boolean> checkNewFansExecuteDTO = preDeterminedCheckService.checkNewFans(reqDTO.getFanNo(),
                    GoodsSourceTypeEnum.CLOUD_POOL_DISTRIBUTE.getCode().equals(goodsInfo.getGoodsSourceType()) ? null : reqDTO.getStoreNo());
            if (!checkNewFansExecuteDTO.successFlag()) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            // 活动设置了限制老用户，但该用户没有下过单，是新用户，提示信息给前端
            if (checkNewFansExecuteDTO.getData()) {
                return ExecuteDTO.error(MarketErrorCode.CODE_17000629);
            }
        }

        // 如果是平台活动的云池商品，则查询根据商品编码查询商品数据，替换成cloudPoolGoodsNo取缓存数据
        if (SourceTypeEnum.SOURCE_TYPE_1001.getCode().equals(goodsAndRule.getSourceType())) {
            reqDTO.setGoodsNo(goodsNo);
        }

        // 活动中每人秒杀总限购数
        Integer limitBuyTotalNum = goodsAndRule.getLimitBuyTotalNum() == null ? 0 : goodsAndRule.getLimitBuyTotalNum();
        // 个人已购买商品总数
        String userBuyGoodsTotalNumKey = String.format(PromotionKeyConstant.PROMOTION_USER_BUYGOODSNUM_PREFIX, reqDTO.getPromotionNo(), reqDTO.getFanNo());
        Object userBuyGoodsTotalNumObject = redisBaseUtil.get(userBuyGoodsTotalNumKey);
        Integer userBuyGoodsTotalNum = (userBuyGoodsTotalNumObject == null || StringUtils.isBlank(userBuyGoodsTotalNumObject.toString())) ?
                reqDTO.getBuyNum() : (Integer.parseInt(userBuyGoodsTotalNumObject.toString()) + reqDTO.getBuyNum());
        // 商品个人限购数
        Integer limitBuyNum = goodsAndRule.getLimitBuyNum() == null ? 0 : goodsAndRule.getLimitBuyNum();
        // 个人已购买商品数
        String userBuyGoodsNumKey = String.format(PromotionKeyConstant.PROMOTION_USER_GOODS_BUYGOODSNUM_PREFIX
                , reqDTO.getPromotionNo(), reqDTO.getPeriodNo(), reqDTO.getGoodsNo(), reqDTO.getFanNo());
        Object userBuyGoodsNumObject = redisBaseUtil.get(userBuyGoodsNumKey);
        Integer userBuyGoodsNum = (userBuyGoodsNumObject == null || StringUtils.isBlank(userBuyGoodsNumObject.toString())) ?
                reqDTO.getBuyNum() : (Integer.parseInt(userBuyGoodsNumObject.toString()) + reqDTO.getBuyNum());

        // 如果 小于活动中每人秒杀总限购数，但超过个人限购数，则提示 抱歉，已超限制数量，请修改数量重新下单！
        if (userBuyGoodsTotalNum.compareTo(limitBuyTotalNum) < 0 && userBuyGoodsNum.compareTo(limitBuyNum) > 0) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000627);
        }
        // 如果 大于活动中每人秒杀总限购数 或者 大于个人限购数，则提示 抱歉，当前购买数量已超优惠限制，不支持继续购买！
        if (userBuyGoodsTotalNum.compareTo(limitBuyTotalNum) > 0 || userBuyGoodsNum.compareTo(limitBuyNum) > 0) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000628);
        }

        return ExecuteDTO.success();
    }

    /**
     * 分页查看促销活动场次参与的商品信息
     *
     * <AUTHOR>
     * @date 2021-11-04
     */
    @Override
    public ExecuteDTO<List<ResLimitTimePeriodGoodsPageDTO>> getPromotionPeriodGoodsPage(ReqLimitTimePeriodGoodsPageDTO reqDTO) {
        log.info("getPromotionPeriodGoodsPage-入参：{}", reqDTO);
        limitTimePromotionAssert.getPromotionPeriodGoodsPageAssert(reqDTO);

        // 存储最后返回的结果
        List<ResLimitTimePeriodGoodsPageDTO> resultList = new ArrayList<>();
        // 查询活动场次下的商品数据
        for (ReqPromotionInfoDTO reqPromotionInfoDTO : reqDTO.getPromotionInfoList()) {
            // 根据活动编号和活动场次编号分页查询商品数据
            AtomReqGoodsPromotionGoodsRelationDTO reqPromotionGoods = new AtomReqGoodsPromotionGoodsRelationDTO();
            reqPromotionGoods.setPromotionNo(reqPromotionInfoDTO.getPromotionNo());
            reqPromotionGoods.setPeriodNo(reqPromotionInfoDTO.getPeriodNo());
            // 排除主品
            reqPromotionGoods.setChildFlag(WhetherEnum.YES.getCode());
//            reqPromotionGoods.setPageNum(reqDTO.getPageNum());
//            reqPromotionGoods.setPageSize(reqDTO.getPageSize());
            reqPromotionGoods.setNoPage();
            ExecuteDTO<ExecutePageDTO<AtomResGoodsPromotionGoodsRelationDTO>> goodsRelationPage =
                    atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationPage(reqPromotionGoods);
            if (null == goodsRelationPage) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            if (!goodsRelationPage.successFlag()) {
                return ExecuteDTO.error(goodsRelationPage.getStatus(), goodsRelationPage.getMsg());
            }
            // 出参转换
            ResLimitTimePeriodGoodsPageDTO periodGoodsPageDTO = new ResLimitTimePeriodGoodsPageDTO();
            periodGoodsPageDTO.setPeriodNo(reqPromotionInfoDTO.getPeriodNo());
            ExecutePageDTO<ResGoodsPromotionGoodsRelationDTO> pageDTO = new ExecutePageDTO<>();
            if (null != goodsRelationPage.getData()) {
                List<ResGoodsPromotionGoodsRelationDTO> goodsList = BeanCopierUtil.copyList(goodsRelationPage.getData().getRows(),
                        ResGoodsPromotionGoodsRelationDTO.class);
                pageDTO.setTotal(goodsRelationPage.getData().getTotal());
                // 查询商品基本信息数据
                if (CollectionUtils.isNotEmpty(goodsList)) {
                    ExecuteDTO goodsBaseInfoExecuteDTO = this.getPromotionGoodsBaseInfo(goodsList);
                    // 查询失败，直接返回
                    if (!goodsBaseInfoExecuteDTO.successFlag()) {
                        return goodsBaseInfoExecuteDTO;
                    }
                }
                // 设置分页行记录数据
                pageDTO.setRows(goodsList);
            }
            periodGoodsPageDTO.setGoodsPage(pageDTO);
            resultList.add(periodGoodsPageDTO);
        }

        // 返回结果
        return ExecuteDTO.success(resultList);
    }

    /**
     * 获取活动商品的基本信息
     *
     * @param goodsList 查询出来的活动商品
     * <AUTHOR>
     * @date 2021-11-04
     */
    private ExecuteDTO getPromotionGoodsBaseInfo(List<ResGoodsPromotionGoodsRelationDTO> goodsList) {
        // 从查询出来的活动商品中提取出商品编码，用于查询这些商品的基础信息
        List<String> goodsNoList = goodsList.stream().filter(goods -> StringUtils.isNotBlank(goods.getGoodsNo()))
                .map(ResGoodsPromotionGoodsRelationDTO::getGoodsNo).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(goodsNoList)) {
            return ExecuteDTO.success();
        }
        // 查询商品基础信息
        ReqGoodsDTO reqGoodsDTO = new ReqGoodsDTO();
        // 设置要查询的商品
        reqGoodsDTO.setGoodsNos(goodsNoList);
        // 不分页查询
        reqGoodsDTO.setNoPage();
        ExecuteDTO<ExecutePageDTO<ResGoodsDTO>> goodsPage = goodsAnalysisService.getGoodsPage(reqGoodsDTO);
        if (null == goodsPage) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!goodsPage.successFlag()) {
            return ExecuteDTO.error(goodsPage.getStatus(), goodsPage.getMsg());
        }
        // 活动参与的商品信息匹配
        if ((null != goodsPage.getData() && CollectionUtils.isNotEmpty(goodsPage.getData().getRows()))) {
            for (ResGoodsPromotionGoodsRelationDTO goodsRelationDTO : goodsList) {
                Optional<ResGoodsDTO> goodsDTOOptional = goodsPage.getData().getRows().stream()
                        .filter(resGoodsDTO -> StringUtils.equals(goodsRelationDTO.getGoodsNo(), resGoodsDTO.getGoodsNo()))
                        .findFirst();
                if (goodsDTOOptional.isPresent()) {
                    ResGoodsDTO goodsDTO = goodsDTOOptional.get();
                    // 商品形式
                    goodsRelationDTO.setGoodsForm(goodsDTO.getGoodsForm());
                    // 商品名称
                    goodsRelationDTO.setGoodsName(goodsDTO.getGoodsName());
                    // 规格属性
                    StringBuilder attributeNames = new StringBuilder();
                    if (StringUtils.isNotBlank(goodsDTO.getFirstAttributeValueName())) {
                        attributeNames.append(goodsDTO.getFirstAttributeValueName());
                    }
                    if (StringUtils.isNotBlank(goodsDTO.getSecondAttributeValueName())) {
                        if (StringUtils.isNotBlank(attributeNames.toString())) {
                            attributeNames.append("-");
                        }
                        attributeNames.append(goodsDTO.getSecondAttributeValueName());
                    }
                    if (StringUtils.isNotBlank(goodsDTO.getThirdAttributeValueName())) {
                        if (StringUtils.isNotBlank(attributeNames.toString())) {
                            attributeNames.append("-");
                        }
                        attributeNames.append(goodsDTO.getThirdAttributeValueName());
                    }
                    if (StringUtils.isNotBlank(attributeNames)) {
                        goodsRelationDTO.setAttributeNames(attributeNames.toString());
                    }
                    // 零售价
                    goodsRelationDTO.setRetailPrice(goodsDTO.getRetailPrice());
                    // 可售库存
                    goodsRelationDTO.setCanSaleStockNum(goodsDTO.getCanSaleStockNum());
                    // 店铺编号
                    goodsRelationDTO.setStoreNo(goodsDTO.getStoreNo());
                    // 店铺名称
                    goodsRelationDTO.setStoreName(goodsDTO.getStoreName());
                    // 商品展示状态
                    goodsRelationDTO.setGoodsShowStatusValue(goodsDTO.getGoodsShowStatusValue());
                    // 20230928蛋品 lixiang  商品管理 多单位商品
                    goodsRelationDTO.setMultiUnitType(goodsDTO.getMultiUnitType());
                    goodsRelationDTO.setMultiUnitGoodsNo(goodsDTO.getMultiUnitGoodsNo());
                    goodsRelationDTO.setCalculationUnitNo(goodsDTO.getCalculationUnitNo());
                    goodsRelationDTO.setCalculationUnitName(goodsDTO.getCalculationUnitName());
                }
            }
        }
        return ExecuteDTO.success();
    }

    /**
     * 查询活动场次下是否存在互斥的商品
     *
     * @param periodDTOs 请求参数
     * <AUTHOR>
     * @date 2021-11-30
     */
    @Override
    public ExecuteDTO<List<ResGoodsPromotionPeriodDTO>> getPromotionUnAvailableGoods(List<ReqGoodsPromotionPeriodDTO> periodDTOs) {
        log.info("getPromotionUnAvailableGoods-入参：{}", periodDTOs);
        // 入参校验
        limitTimePromotionAssert.getPromotionUnAvailableGoodsAssert(periodDTOs);
        // 初始化CountDownLatch
        CountDownLatch countDownLatch = new CountDownLatch(periodDTOs.size());
        // 响应结果
        List<ResGoodsPromotionPeriodDTO> resultList = new ArrayList<>();
        for (int i = 0; i < periodDTOs.size(); i++) {
            final int index = i;
            MarketThreadExecutorService.getInstance()
                    .getBoundedThreadPool()
                    .execute(() -> {
                                // 找活动场次下互斥的商品
                                this.findUnAvailableGoods(periodDTOs.get(index), countDownLatch, resultList);
                            }
                    );
        }
        try {
            // 阻塞当前线程，直到所有子线程都执行countDown方法才会继续执行
            countDownLatch.await();
        } catch (InterruptedException e) {
            log.error("闭锁报错", e);
            Thread.currentThread().interrupt();
        }
        // 出参数据分析处理
        for (ResGoodsPromotionPeriodDTO result : resultList) {
            if (WhetherEnum.YES.getCode().equals(result.getErrorFlag())) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
        }
        return ExecuteDTO.success(resultList);
    }

    /**
     * 找活动场次下互斥的商品
     *
     * <AUTHOR>
     * @date 2021-11-30
     */
    private void findUnAvailableGoods(ReqGoodsPromotionPeriodDTO periodDTO, CountDownLatch countDownLatch
            , List<ResGoodsPromotionPeriodDTO> resultList) {
        // 返回结果
        ResGoodsPromotionPeriodDTO resultDTO = BeanCopierUtil.copy(periodDTO, ResGoodsPromotionPeriodDTO.class);
        try {
            // 首先根据活动编号和时间段编号查询活动场次信息
            AtomReqGoodsPromotionPeriodDTO reqPromotionPeriodDTO = new AtomReqGoodsPromotionPeriodDTO();
            reqPromotionPeriodDTO.setPromotionNo(periodDTO.getPromotionNo());
            reqPromotionPeriodDTO.setPeriodNo(periodDTO.getPeriodNo());
            ExecuteDTO<AtomResGoodsPromotionPeriodDTO> promotionPeriodDTOExecuteDTO =
                    atomGoodsPromotionPeriodAnalysisService.selectPromotionPeriodInfo(reqPromotionPeriodDTO);
            if (null == promotionPeriodDTOExecuteDTO || !promotionPeriodDTOExecuteDTO.successFlag()
                    || promotionPeriodDTOExecuteDTO.getData() == null) {
                log.info("selectPromotionPeriodInfo异常：{}", promotionPeriodDTOExecuteDTO);
                resultDTO.setErrorFlag(WhetherEnum.YES.getCode());
                resultList.add(resultDTO);
                return;
            }
            // 查询互斥的商品
            // 先查询出和当前活动存在互斥的商品
            AtomReqGoodsPromotionGoodsRelationDTO reqGoodsDTO = BeanCopierUtil.copy(promotionPeriodDTOExecuteDTO.getData()
                    , AtomReqGoodsPromotionGoodsRelationDTO.class);
            reqGoodsDTO.setStoreNo(periodDTO.getStoreNo());
            reqGoodsDTO.setQueryType(NumConstant.TWO);
            ExecuteDTO<List<String>> noChoiceGoodsNoList =
                    atomGoodsPromotionGoodsRelationAnalysisService.getNoChoiceGoodsNoList(reqGoodsDTO);
            if (noChoiceGoodsNoList == null || !noChoiceGoodsNoList.successFlag()) {
                log.info("getNoChoiceGoodsNoList异常：{}", noChoiceGoodsNoList);
                resultDTO.setErrorFlag(WhetherEnum.YES.getCode());
                resultList.add(resultDTO);
                return;
            }
            // 然后根据查询出来的互斥商品，去找当前活动场次中，是否存在这些互斥商品
            if (CollectionUtils.isNotEmpty(noChoiceGoodsNoList.getData())) {
                AtomReqGoodsPromotionGoodsRelationDTO reqUnAvailableGoods = new AtomReqGoodsPromotionGoodsRelationDTO();
                reqUnAvailableGoods.setPromotionNo(periodDTO.getPromotionNo());
                reqUnAvailableGoods.setPeriodNo(periodDTO.getPeriodNo());
                reqUnAvailableGoods.setGoodsNoList(noChoiceGoodsNoList.getData());
                ExecuteDTO<List<String>> promotionUnAvailableGoods =
                        atomGoodsPromotionGoodsRelationAnalysisService.getPromotionUnAvailableGoods(reqUnAvailableGoods);
                if (promotionUnAvailableGoods == null || !promotionUnAvailableGoods.successFlag()) {
                    log.info("getPromotionUnAvailableGoods：{}", promotionUnAvailableGoods);
                    resultDTO.setErrorFlag(WhetherEnum.YES.getCode());
                    resultList.add(resultDTO);
                    return;
                }
                if (CollectionUtils.isNotEmpty(promotionUnAvailableGoods.getData())) {
                    // 存在互斥的商品时，设置标识返回给前端，让前端提示给用户
                    resultDTO.setUnAvailableFlag(WhetherEnum.YES.getCode());
                } else {
                    resultDTO.setUnAvailableFlag(WhetherEnum.NO.getCode());
                }
            } else {
                resultDTO.setUnAvailableFlag(WhetherEnum.NO.getCode());
            }
            resultDTO.setErrorFlag(WhetherEnum.NO.getCode());
            resultList.add(resultDTO);
        } catch (Exception e) {
            log.error("找互斥商品出错：", e);
            resultDTO.setErrorFlag(WhetherEnum.YES.getCode());
            resultList.add(resultDTO);
        } finally {
            if (countDownLatch != null) {
                countDownLatch.countDown();
            }
        }
    }

}