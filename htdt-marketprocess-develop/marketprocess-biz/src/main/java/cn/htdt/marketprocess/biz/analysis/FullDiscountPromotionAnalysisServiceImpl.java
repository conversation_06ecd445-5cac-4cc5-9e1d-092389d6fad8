package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.market.PromotionStatusEnum;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.DateUtil;
import cn.htdt.marketcenter.dto.request.AtomReqPromotionInfoDTO;
import cn.htdt.marketcenter.dto.response.AtomResGoodsPromotionRuleDTO;
import cn.htdt.marketprocess.api.analysis.FullDiscountPromotionAnalysisService;
import cn.htdt.marketprocess.biz.conversion.FullDiscountPromotionAssert;
import cn.htdt.marketprocess.dto.request.ReqPromotionInfoDTO;
import cn.htdt.marketprocess.dto.response.ResGoodsPromotionRuleDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomGoodsPromotionPeriodAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomGoodsPromotionRuleAnalysisService;
import cn.htdt.orderprocess.api.OrderPromotionAnalysisService;
import cn.htdt.orderprocess.dto.request.ReqSoPromotionDTO;
import cn.htdt.orderprocess.dto.response.ResSoPromotionOrderCountDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 满减满折活动服务类
 *
 * <AUTHOR>
 * @date 2022-02-21
 */
@DubboService
@Slf4j
public class FullDiscountPromotionAnalysisServiceImpl implements FullDiscountPromotionAnalysisService {

    @Autowired
    private FullDiscountPromotionAssert fullDiscountPromotionAssert;

    @Resource
    private AtomGoodsPromotionRuleAnalysisService atomGoodsPromotionRuleAnalysisService;

    @Resource
    private AtomGoodsPromotionPeriodAnalysisService atomGoodsPromotionPeriodAnalysisService;

    @DubboReference
    private OrderPromotionAnalysisService orderPromotionAnalysisService;

    /**
     * 查询满减满折活动列表
     *
     * <AUTHOR>
     * @date 2022-02-21
     */
    @Override
    public ExecuteDTO<List<ResGoodsPromotionRuleDTO>> getFullDiscountList(ReqPromotionInfoDTO reqDTO) {
        log.info("getFullDiscountListForSmartCashier-入参：{}", reqDTO);
        fullDiscountPromotionAssert.getFullDiscountListAssert(reqDTO);

        // 查询活动
        AtomReqPromotionInfoDTO promotionInfoDTO = new AtomReqPromotionInfoDTO();
        promotionInfoDTO.setStoreNo(reqDTO.getStoreNo());
        ExecuteDTO<List<AtomResGoodsPromotionRuleDTO>> executeDTO =
                atomGoodsPromotionRuleAnalysisService.getFullDiscountList(promotionInfoDTO);
        if (null == executeDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }

        // 存储返回的结果集
        List<ResGoodsPromotionRuleDTO> resultList = new ArrayList<>();

        // 数据格式化，进行中的活动优先放前面展示
        if (CollectionUtils.isNotEmpty(executeDTO.getData())) {
            // 存放未开始的活动
            List<ResGoodsPromotionRuleDTO> notStartPromotionList = new ArrayList<>();
            LocalDateTime localDateTime = DateUtil.getLocalDateTime();
            for (AtomResGoodsPromotionRuleDTO promotionDTO : executeDTO.getData()) {
                // 进行中的活动
                if (!localDateTime.isBefore(promotionDTO.getEffectiveTime())
                        && !localDateTime.isAfter(promotionDTO.getInvalidTime())) {
                    promotionDTO.setStatus(PromotionStatusEnum.IN_PROGRESS.getCode());
                    ResGoodsPromotionRuleDTO promotionRuleDTO = BeanCopierUtil.copy(promotionDTO, ResGoodsPromotionRuleDTO.class);
                    promotionRuleDTO.setOrderNum(NumConstant.ZERO); // 订单数初始化
                    resultList.add(promotionRuleDTO);
                } else {
                    promotionDTO.setStatus(PromotionStatusEnum.NOT_STARTED.getCode());
                    ResGoodsPromotionRuleDTO promotionRuleDTO = BeanCopierUtil.copy(promotionDTO, ResGoodsPromotionRuleDTO.class);
                    promotionRuleDTO.setOrderNum(NumConstant.ZERO); // 订单数初始化
                    notStartPromotionList.add(promotionRuleDTO);
                }
            }
            // 进行中的满减满折活动获取成功下单数
            if (CollectionUtils.isNotEmpty(resultList)) {
                // 入参转换
                ReqSoPromotionDTO reqSoPromotionDTO = new ReqSoPromotionDTO();
                // 获取活动编号
                List<String> promotionNoList = resultList.stream().map(ResGoodsPromotionRuleDTO::getPromotionNo).collect(Collectors.toList());
                reqSoPromotionDTO.setPromotionNoList(promotionNoList);
                // 查询活动下的订单数据
                ExecuteDTO<List<ResSoPromotionOrderCountDTO>> orderCountExecuteDTO = orderPromotionAnalysisService.getSeckillOrderCount(reqSoPromotionDTO);
                if (null == orderCountExecuteDTO) {
                    return ExecuteDTO.error(CommonCode.CODE_10000003);
                }
                if (!orderCountExecuteDTO.successFlag()) {
                    return ExecuteDTO.error(orderCountExecuteDTO.getStatus(), orderCountExecuteDTO.getMsg());
                }
                resultList.forEach(goodsPromotion -> {
                    if (CollectionUtils.isNotEmpty(orderCountExecuteDTO.getData())) {
                        orderCountExecuteDTO.getData().forEach(orderCountDTO -> {
                            if (StringUtils.isNotBlank(goodsPromotion.getPromotionNo())
                                    && goodsPromotion.getPromotionNo().equals(orderCountDTO.getPromotionNo())) {
                                goodsPromotion.setOrderNum(orderCountDTO.getOrderNum());
                            }
                        });
                    }
                });
            }
            if (CollectionUtils.isNotEmpty(notStartPromotionList)) {
                resultList.addAll(notStartPromotionList);
            }
        }

        // 返回结果数据
        return ExecuteDTO.success(resultList);
    }

    /**
     * 查询满减满折活动基本信息
     *
     * <AUTHOR>
     * @date 2022-02-22
     */
    @Override
    public ExecuteDTO<List<ResGoodsPromotionRuleDTO>> getFullDiscountByPromotionNo(ReqPromotionInfoDTO promotionInfoDTO) {
        log.info("getFullDiscountByPromotionNo-入参：{}", promotionInfoDTO);
        // 入参校验
        fullDiscountPromotionAssert.getFullDiscountByPromotionNoAssert(promotionInfoDTO);

        // 查询满减满折活动信息
        AtomReqPromotionInfoDTO reqDTO = BeanCopierUtil.copy(promotionInfoDTO, AtomReqPromotionInfoDTO.class);
        ExecuteDTO<List<AtomResGoodsPromotionRuleDTO>> executeDTO = atomGoodsPromotionRuleAnalysisService.getFullDiscountByPromotionNo(reqDTO);
        if (null == executeDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }

        // 返回结果
        return ExecuteDTO.success(BeanCopierUtil.copyList(executeDTO.getData(), ResGoodsPromotionRuleDTO.class));
    }
}
