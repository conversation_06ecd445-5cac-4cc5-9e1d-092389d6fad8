package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.enums.BossLogoffErrorEnum;
import cn.htdt.common.dto.enums.HxgLogoffErrorEnum;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.constants.StringNumConstant;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.DataBaseUtil;
import cn.htdt.marketcenter.dto.request.AtomReqUserPointsDTO;
import cn.htdt.marketcenter.dto.response.AtomResUserPointsDTO;
import cn.htdt.marketprocess.api.analysis.UserPointsAnalysisService;
import cn.htdt.marketprocess.biz.conversion.UserPointsAssert;
import cn.htdt.marketprocess.biz.utils.PointsUtil;
import cn.htdt.marketprocess.dao.UserPointsDao;
import cn.htdt.marketprocess.dao.UserPointsEffectDao;
import cn.htdt.marketprocess.domain.UserPointsDomain;
import cn.htdt.marketprocess.domain.UserVirtualCoinDomain;
import cn.htdt.marketprocess.dto.request.ReqUserPointsDTO;
import cn.htdt.marketprocess.dto.response.ResUserPointEffectDTO;
import cn.htdt.marketprocess.dto.response.ResUserPointsDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomUserPointsAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomUserPointsRecordAnalysisService;
import cn.htdt.marketprocess.vo.ReqMarketTouchingEffectVO;
import cn.htdt.marketprocess.vo.UserPointsVO;
import cn.htdt.usercenter.dto.request.ReqFancDTO;
import cn.htdt.usercenter.dto.response.ResFancDTO;
import cn.htdt.userprocess.api.LegacyUserCenterService;
import cn.htdt.userprocess.api.UcFansProcessService;
import cn.htdt.userprocess.dto.response.StoreInfoResponse;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@RefreshScope
@DubboService
public class UserPointsAnalysisServiceImpl implements UserPointsAnalysisService {
    @Resource
    AtomUserPointsAnalysisService userPointsAnalysisService;

    @Resource
    AtomUserPointsRecordAnalysisService atomUserPointsRecordAnalysisService;

    @DubboReference
    private LegacyUserCenterService legacyUserCenterService;

    @DubboReference
    private UcFansProcessService ucFansProcessService;

    @Resource
    UserPointsAssert userPointsAssert;

    @Resource
    private UserPointsDao userPointsDao;

    @Resource
    private UserPointsEffectDao userPointsEffectDao;

    @Resource
    private DataBaseUtil dataBaseUtil;

    @Resource
    private PointsUtil pointsUtil;

    /**
     * @see UserPointsAnalysisService#getSimpleUserPoints(ReqUserPointsDTO)
     *
     * 20230928蛋品-赵翔宇-商家积分, orderprocess会调用此方法
     */
    @Override
    public ExecuteDTO<ResUserPointsDTO> getSimpleUserPoints(ReqUserPointsDTO userPointsDTO) {
        log.info("UserPointsAnalysisServiceImpl.getSimpleUserPoints----param----{}", JSON.toJSONString(userPointsDTO));
        userPointsAssert.getSimpleUserPoints(userPointsDTO);
        AtomReqUserPointsDTO atomReqUserPointsDTO = BeanCopierUtil.copy(userPointsDTO, AtomReqUserPointsDTO.class);
        //20230928蛋品-wh-查询店铺是否是共享店铺
        StoreInfoResponse storeInfoResponse = pointsUtil.queryStore(userPointsDTO.getStoreNo());
        // 20230928蛋品-zxy-积分, 传入的积分类型为空, 则根据是否为共享店铺来设置积分类型
        if (StringUtils.isBlank(atomReqUserPointsDTO.getRuleType())) {
            log.info("getSimpleUserPoints-->ruleType is blank, set ruleType");
            if(storeInfoResponse.getMemberSharing() == NumConstant.ONE){
                atomReqUserPointsDTO.setRuleType(String.valueOf(NumConstant.ONE));
            }else {
                atomReqUserPointsDTO.setRuleType(String.valueOf(NumConstant.TWO));
            }
        }
        // 20230928蛋品-赵翔宇-商家积分, orderprocess会调用此方法
        if (StringUtils.isBlank(userPointsDTO.getMerchantNo())) {
            atomReqUserPointsDTO.setMerchantNo(storeInfoResponse.getMerchantNo());
        }
        log.info("UserPointsAnalysisServiceImpl.getSimpleUserPoints-查询入参: {}", JSON.toJSONString(atomReqUserPointsDTO));
        ExecuteDTO<AtomResUserPointsDTO> atomExecuteDTO = userPointsAnalysisService.getSimpleUserPoints(atomReqUserPointsDTO);
        log.info("UserPointsAnalysisServiceImpl.getSimpleUserPoints-查询出参: {}", JSON.toJSONString(atomExecuteDTO));
        //判断状态
        if (!atomExecuteDTO.successFlag()) {
            return ExecuteDTO.error(atomExecuteDTO.getStatus(), atomExecuteDTO.getMsg());
        }
        if (null == atomExecuteDTO.getData()) {
            return ExecuteDTO.success(null);
        }
        ResUserPointsDTO userPoints = BeanCopierUtil.copy(atomExecuteDTO.getData(), ResUserPointsDTO.class);
        return ExecuteDTO.success(userPoints);
    }

    /**
     * @see UserPointsAnalysisService#getUserPointsPage(ReqUserPointsDTO)
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResUserPointsDTO>> getUserPointsPage(ReqUserPointsDTO userPointsDTO) {
        log.info("UserPointsAnalysisServiceImpl.getUserPoints----param----{}", JSON.toJSONString(userPointsDTO));
        userPointsAssert.getUserPoints(userPointsDTO);
        AtomReqUserPointsDTO atomReqUserPointsDTO = BeanCopierUtil.copy(userPointsDTO, AtomReqUserPointsDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResUserPointsDTO>> atomExecuteDTO = null;
        //20230928蛋品-wh-查询店铺是否是共享店铺
        StoreInfoResponse storeInfoResponse = pointsUtil.queryStore(userPointsDTO.getStoreNo());
        if(storeInfoResponse.getMemberSharing() == NumConstant.ONE){
            atomReqUserPointsDTO.setRuleType(String.valueOf(NumConstant.ONE));
            atomExecuteDTO = userPointsAnalysisService.getUserPointsPage(atomReqUserPointsDTO);
        }else {
            atomReqUserPointsDTO.setMerchantNo(storeInfoResponse.getMerchantNo());
            atomExecuteDTO = userPointsAnalysisService.getSharingStoreUserPointsPage(atomReqUserPointsDTO);
        }
        //判断状态
        if (!atomExecuteDTO.successFlag()) {
            return ExecuteDTO.error(atomExecuteDTO.getStatus(), atomExecuteDTO.getMsg());
        }
        List<ResUserPointsDTO> userPoints = BeanCopierUtil.copyList(atomExecuteDTO.getData().getRows(), ResUserPointsDTO.class);
        //完善粉丝信息
        if (CollectionUtils.isNotEmpty(userPoints)) {
            List<String> fansNoList = userPoints.stream().map(ResUserPointsDTO::getFansNo).collect(Collectors.toList());
            ReqFancDTO reqFancDTO = new ReqFancDTO();
            reqFancDTO.setFansNoList(fansNoList);
            reqFancDTO.setStoreNo(userPointsDTO.getStoreNo());
            ExecuteDTO<List<ResFancDTO>> fancDTOExecuteDTO = legacyUserCenterService.getFansInfoByNos(reqFancDTO);
            if (fancDTOExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(fancDTOExecuteDTO.getData())) {
                List<ResFancDTO> fancDTOList = fancDTOExecuteDTO.getData();
                userPoints.forEach(resUserPointsDTO -> {
                    Optional<ResFancDTO> optional = fancDTOList.stream().filter(resFancDTO -> resFancDTO.getFanNo().equals(resUserPointsDTO.getFansNo())).findFirst();
                    if (optional.isPresent()) {
                        ResFancDTO resFancDTO = optional.get();
                        resUserPointsDTO.setName(resFancDTO.getName());
                        resUserPointsDTO.setStoreFanName(resFancDTO.getStoreFanName());
                    }
                });
            }
        }
        log.info("UserPointsAnalysisServiceImpl.getUserPoints----end----");
        return ExecuteDTO.success(new ExecutePageDTO<>(atomExecuteDTO.getData().getTotal(), userPoints));
    }

    /**
     * @see UserPointsAnalysisService#getUserConvert(ReqUserPointsDTO)
     */
    @Override
    public ExecuteDTO<ResUserPointsDTO> getUserConvert(ReqUserPointsDTO userPointsDTO) {
        log.info("UserPointsAnalysisServiceImpl.getUserConvert----param----{}", JSON.toJSONString(userPointsDTO));
        userPointsAssert.getUserConvert(userPointsDTO);
        AtomReqUserPointsDTO atomReqUserPointsDTO = BeanCopierUtil.copy(userPointsDTO, AtomReqUserPointsDTO.class);
        //20230928蛋品-wh-查询店铺是否是共享店铺
        StoreInfoResponse storeInfoResponse = pointsUtil.queryStore(userPointsDTO.getStoreNo());
        if(storeInfoResponse.getMemberSharing() == NumConstant.ONE){
            atomReqUserPointsDTO.setRuleType(String.valueOf(NumConstant.ONE));
            atomReqUserPointsDTO.setMerchantNo("");
        }else {
            atomReqUserPointsDTO.setRuleType(String.valueOf(NumConstant.TWO));
            atomReqUserPointsDTO.setStoreNo("");
            atomReqUserPointsDTO.setMerchantNo(storeInfoResponse.getMerchantNo());
        }

        ExecuteDTO<AtomResUserPointsDTO> atomExecuteDTO = userPointsAnalysisService.getUserConvert(atomReqUserPointsDTO);
        if (!atomExecuteDTO.successFlag()) {
            return ExecuteDTO.error(atomExecuteDTO.getStatus(), atomExecuteDTO.getMsg());
        }
        log.info("UserPointsAnalysisServiceImpl.getUserConvert----兑换记录出参----{}", JSON.toJSONString(atomExecuteDTO.getData()));
        ResUserPointsDTO resUserPointsDTO = BeanCopierUtil.copy(atomExecuteDTO.getData(), ResUserPointsDTO.class);
        if (resUserPointsDTO != null) {
            ReqFancDTO reqFancDTO = new ReqFancDTO();
            reqFancDTO.setFansNoList(Lists.newArrayList(resUserPointsDTO.getFansNo()));
            reqFancDTO.setStoreNo(userPointsDTO.getStoreNo());
            ExecuteDTO<List<ResFancDTO>> fancDTOExecuteDTO = legacyUserCenterService.getFansInfoByNos(reqFancDTO);
            if (fancDTOExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(fancDTOExecuteDTO.getData())) {
                ResFancDTO resFancDTO = fancDTOExecuteDTO.getData().get(NumConstant.ZERO);
                resUserPointsDTO.setName(resFancDTO.getName());
                resUserPointsDTO.setStoreFanName(resFancDTO.getStoreFanName());
            }
        }
        log.info("UserPointsAnalysisServiceImpl.getUserConvert----出参----{}", JSON.toJSONString(resUserPointsDTO));
        return ExecuteDTO.success(resUserPointsDTO);
    }

    @Override
    public ExecuteDTO<ResUserPointsDTO> getStoreTotalPoints(ReqUserPointsDTO reqUserPointsDTO) {
        log.info("UserPointsAnalysisServiceImpl.getStoreTotalPoints----param----{}", JSON.toJSONString(reqUserPointsDTO));
        userPointsAssert.getStoreTotalPoints(reqUserPointsDTO);
        AtomReqUserPointsDTO atomReqUserPointsDTO = BeanCopierUtil.copy(reqUserPointsDTO, AtomReqUserPointsDTO.class);
        //20230928蛋品-wh-查询店铺是否是共享店铺
        StoreInfoResponse storeInfoResponse = pointsUtil.queryStore(reqUserPointsDTO.getStoreNo());
        if(storeInfoResponse.getMemberSharing() == NumConstant.ONE){
            atomReqUserPointsDTO.setRuleType(String.valueOf(NumConstant.ONE));
        }else {
            atomReqUserPointsDTO.setRuleType(String.valueOf(NumConstant.TWO));
            atomReqUserPointsDTO.setMerchantNo(storeInfoResponse.getMerchantNo());
        }

        ExecuteDTO<AtomResUserPointsDTO> atomExecuteDTO = userPointsAnalysisService.getStoreTotalPoints(atomReqUserPointsDTO);
        if (!atomExecuteDTO.successFlag()) {
            return ExecuteDTO.error(atomExecuteDTO.getStatus(), atomExecuteDTO.getMsg());
        }
        ResUserPointsDTO resUserPointsDTO = BeanCopierUtil.copy(atomExecuteDTO.getData(), ResUserPointsDTO.class);
        return ExecuteDTO.success(resUserPointsDTO);
    }

    /**
     * BossApp，店铺&单店角色 -> 工作台 -> 数据报表 -> 营销权益效果 -> 积分权益触达效果
     * 或者 -> 卖货 -> 我要看数据 -> 营销权益触达
     *
     * @param atomReqUserPointsDTO 请求参数
     * @return 响应参数
     */
    @Override
    public ExecuteDTO<ResUserPointEffectDTO> getStoreUserPointsEffect(ReqUserPointsDTO atomReqUserPointsDTO) {

        log.info("UserPointsAnalysisServiceImpl.getStoreUserPointsEffect----param----{}", JSON.toJSONString(atomReqUserPointsDTO));
        userPointsAssert.getStoreTotalPoints(atomReqUserPointsDTO);

        UserPointsVO userPointsVO = BeanCopierUtil.copy(atomReqUserPointsDTO, UserPointsVO.class);
        // 查询累计积分发放和累计积分使用
        //20230928蛋品-wh-查询店铺是否是共享店铺
        StoreInfoResponse storeInfoResponse = pointsUtil.queryStore(atomReqUserPointsDTO.getStoreNo());
        if(storeInfoResponse.getMemberSharing() == NumConstant.ONE){
            userPointsVO.setRuleType(String.valueOf(NumConstant.ONE));
        }else {
            userPointsVO.setRuleType(String.valueOf(NumConstant.TWO));
        }
        UserPointsVO outVo = userPointsDao.selectStoreTotalPoints(userPointsVO);
        log.info("UserPointsAnalysisServiceImpl.getStoreUserPointsEffect----outVo----{}", JSON.toJSONString(outVo));

        if (null == outVo) {
            log.error("UserPointsAnalysisServiceImpl.getStoreUserPointsEffect.selectStoreTotalPoints, response data is null");
            outVo = new UserPointsVO();
        }
        if (null == outVo.getTotalGain()) {
            outVo.setTotalGain(NumConstant.ZERO);
        }

        if (null == outVo.getTotalReduce()) {
            outVo.setTotalReduce(NumConstant.ZERO);
        }

        // 响应数据
        ResUserPointEffectDTO resUserPointEffectDTO = new ResUserPointEffectDTO();
        resUserPointEffectDTO.setTotalGain(outVo.getTotalGain());
        resUserPointEffectDTO.setTotalReduce(outVo.getTotalReduce());

        // 查询账户剩余积分数>=0的店铺粉丝人数, 已排除无效粉丝
        ReqMarketTouchingEffectVO reqUserPointsEffectVO = new ReqMarketTouchingEffectVO();
        reqUserPointsEffectVO.setStoreNo(atomReqUserPointsDTO.getStoreNo());
        reqUserPointsEffectVO.setMarketDatabaseName(dataBaseUtil.getMarketDataBaseName());
        reqUserPointsEffectVO.setUserDatabaseName(dataBaseUtil.getUserDataBaseName());
        int hasPointsFansCount = userPointsEffectDao.selectHasPointsFansCount(reqUserPointsEffectVO);

        log.info("UserPointsAnalysisServiceImpl.getStoreUserPointsEffect----selectHasPointsFansCount----{}", hasPointsFansCount);
        resUserPointEffectDTO.setPointsFansNum(hasPointsFansCount);

        log.info("UserPointsAnalysisServiceImpl.getStoreUserPointsEffect----resUserPointEffectDTO----{}", resUserPointEffectDTO);
        return ExecuteDTO.ok(resUserPointEffectDTO);
    }

    /**
     * @param userPointsDTO
     * @Description : 批量查询粉丝积分信息
     * <AUTHOR> 高繁
     * @date : 2021/11/5 15:01
     */
    @Override
    public ExecuteDTO<List<ResUserPointsDTO>> getUserPointsList(ReqUserPointsDTO userPointsDTO) {
        log.info("UserPointsAnalysisServiceImpl.getUserPointsList----param----{}", JSON.toJSONString(userPointsDTO));
        userPointsAssert.getUserPointsListAssert(userPointsDTO);
        AtomReqUserPointsDTO atomReqUserPointsDTO = BeanCopierUtil.copy(userPointsDTO, AtomReqUserPointsDTO.class);
        ExecuteDTO<List<AtomResUserPointsDTO>> executeDTO = userPointsAnalysisService.getUserPointsList(atomReqUserPointsDTO);
        if (executeDTO.successFlag() && CollectionUtils.isNotEmpty(executeDTO.getData())) {
            return ExecuteDTO.success(BeanCopierUtil.copyList(executeDTO.getData(), ResUserPointsDTO.class));

        }
        return ExecuteDTO.success();
    }

    @Override
    public ExecuteDTO<Integer> getMerchantRemainPoints(ReqUserPointsDTO reqUserPointsDTO) {
        log.info("UserPointsAnalysisServiceImpl.getMerchantRemainPoints----param----{}", reqUserPointsDTO.getMerchantNo());
        AtomReqUserPointsDTO atomReqUserPointsDTO = BeanCopierUtil.copy(reqUserPointsDTO, AtomReqUserPointsDTO.class);
        ExecuteDTO<Integer> remainPointsExecuteDTO = userPointsAnalysisService.getMerchantRemainPoints(atomReqUserPointsDTO);
        if (remainPointsExecuteDTO.successFlag()
                && null != remainPointsExecuteDTO.getData()
                && remainPointsExecuteDTO.getData() > 0) {
            return ExecuteDTO.error(BossLogoffErrorEnum.UNUSED_POINTS, remainPointsExecuteDTO.getData());
        }
        return remainPointsExecuteDTO;
    }

    @Override
    public ExecuteDTO<Integer> getFansRemainPoints(ReqUserPointsDTO reqUserPointsDTO) {
        log.info("UserPointsAnalysisServiceImpl.getFansRemainPoints----param----{}", reqUserPointsDTO.getFansNo());
        AtomReqUserPointsDTO atomReqUserPointsDTO = BeanCopierUtil.copy(reqUserPointsDTO, AtomReqUserPointsDTO.class);
        ExecuteDTO<Integer> remainPointsExecuteDTO = userPointsAnalysisService.getFansRemainPoints(atomReqUserPointsDTO);
        if (remainPointsExecuteDTO.successFlag()
                && null != remainPointsExecuteDTO.getData()
                && remainPointsExecuteDTO.getData() > 0) {
            return ExecuteDTO.error(HxgLogoffErrorEnum.UNUSED_POINTS, remainPointsExecuteDTO.getData());
        }
        return remainPointsExecuteDTO;
    }

    @Override
    public ExecuteDTO<List<String>> getHasPointsStoreNoList(ReqUserPointsDTO userPointsDTO) {
        userPointsAssert.getHasPointsStoreNoListAssert(userPointsDTO);

        // 20230928蛋品-赵翔宇-商家积分
        List<String> storeNoList = userPointsDTO.getStoreNoList();
        UserPointsVO userPointsVO = new UserPointsVO();
        userPointsVO.setStoreNoList(storeNoList);
        // 积分类型为店铺
        userPointsVO.setRuleType(StringNumConstant.ONE);
        log.info("getHasPointsStoreNoList--->查询参数: {}", JSON.toJSONString(userPointsVO));
        List<String> list = userPointsDao.selectHasPointsStoreNoList(userPointsVO);
        return ExecuteDTO.ok(list);
    }

    @Override
    public ExecuteDTO<ResUserPointsDTO> getMerchantNoSimpleUserPoints(ReqUserPointsDTO userPointsDTO) {
        log.info("UserPointsAnalysisServiceImpl.getMerchantNoSimpleUserPoints----param----{}", JSON.toJSONString(userPointsDTO));
        AtomReqUserPointsDTO atomReqUserPointsDTO = BeanCopierUtil.copy(userPointsDTO, AtomReqUserPointsDTO.class);
        ExecuteDTO<AtomResUserPointsDTO> atomExecuteDTO = userPointsAnalysisService.getMerchantNoSimpleUserPoints(atomReqUserPointsDTO);
        //判断状态
        if (!atomExecuteDTO.successFlag()) {
            return ExecuteDTO.error(atomExecuteDTO.getStatus(), atomExecuteDTO.getMsg());
        }
        if (null == atomExecuteDTO.getData()) {
            return ExecuteDTO.success(null);
        }
        ResUserPointsDTO userPoints = BeanCopierUtil.copy(atomExecuteDTO.getData(), ResUserPointsDTO.class);
        return ExecuteDTO.success(userPoints);
    }

    @Override
    public ExecuteDTO<ExecutePageDTO<ResUserPointsDTO>> getMerchantUserPointsPage(ReqUserPointsDTO userPointsDTO) {
        log.info("UserPointsAnalysisServiceImpl.getMerchantUserPointsPage----param----{}",JSON.toJSONString(userPointsDTO));
        AtomReqUserPointsDTO atomReqUserPointsDTO = BeanCopierUtil.copy(userPointsDTO, AtomReqUserPointsDTO.class);
        //20230928蛋品-wh-商家-查询商家下共享店铺所有粉丝信息去重，且有积分的
        ExecuteDTO<ExecutePageDTO<AtomResUserPointsDTO>> atomExecuteDTO = userPointsAnalysisService.getSharingStoreUserPointsPage(atomReqUserPointsDTO);
        //判断状态
        if (!atomExecuteDTO.successFlag()) {
            return ExecuteDTO.error(atomExecuteDTO.getStatus(), atomExecuteDTO.getMsg());
        }
        List<ResUserPointsDTO> userPoints = BeanCopierUtil.copyList(atomExecuteDTO.getData().getRows(), ResUserPointsDTO.class);
        log.info("UserPointsAnalysisServiceImpl.getMerchantUserPointsPage----end----");
        return ExecuteDTO.success(new ExecutePageDTO<>(atomExecuteDTO.getData().getTotal(), userPoints));
    }

    /**
     * 20230928蛋品-赵翔宇-商家积分, 查询商家下商家积分不等于0的粉丝数量
     *
     * @param userPointsDTO 查询参数
     * @return 粉丝数量
     */
    @Override
    public ExecuteDTO<Integer> getMerchantHasPointsFanCount(ReqUserPointsDTO userPointsDTO) {
        userPointsAssert.getMerchantHasPointsFanCountAssert(userPointsDTO);

        UserPointsVO userPointsVO = BeanCopierUtil.copy(userPointsDTO, UserPointsVO.class);
        userPointsVO.setRuleType(StringNumConstant.TWO);
        int merchantHasPointsFanCount = userPointsDao.getMerchantHasPointsFanCount(userPointsVO);

        return ExecuteDTO.ok(merchantHasPointsFanCount);
    }

    @Override
    public ExecuteDTO<ExecutePageDTO<ResUserPointsDTO>> getHxgUserPointsPage(ReqUserPointsDTO userPointsDTO) {
        log.info("UserPointsAnalysisServiceImpl.getUserPoints----param----{}", JSON.toJSONString(userPointsDTO));
        userPointsAssert.getUserPoints(userPointsDTO);
        AtomReqUserPointsDTO atomReqUserPointsDTO = BeanCopierUtil.copy(userPointsDTO, AtomReqUserPointsDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResUserPointsDTO>> atomExecuteDTO = userPointsAnalysisService.getUserPointsPage(atomReqUserPointsDTO);
        //判断状态
        if (!atomExecuteDTO.successFlag()) {
            return ExecuteDTO.error(atomExecuteDTO.getStatus(), atomExecuteDTO.getMsg());
        }
        List<ResUserPointsDTO> userPoints = BeanCopierUtil.copyList(atomExecuteDTO.getData().getRows(), ResUserPointsDTO.class);
        //完善粉丝信息
        if (CollectionUtils.isNotEmpty(userPoints)) {
            List<String> fansNoList = userPoints.stream().map(ResUserPointsDTO::getFansNo).collect(Collectors.toList());
            ReqFancDTO reqFancDTO = new ReqFancDTO();
            reqFancDTO.setFansNoList(fansNoList);
            reqFancDTO.setStoreNo(userPointsDTO.getStoreNo());
            ExecuteDTO<List<ResFancDTO>> fancDTOExecuteDTO = legacyUserCenterService.getFansInfoByNos(reqFancDTO);
            if (fancDTOExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(fancDTOExecuteDTO.getData())) {
                List<ResFancDTO> fancDTOList = fancDTOExecuteDTO.getData();
                userPoints.forEach(resUserPointsDTO -> {
                    Optional<ResFancDTO> optional = fancDTOList.stream().filter(resFancDTO -> resFancDTO.getFanNo().equals(resUserPointsDTO.getFansNo())).findFirst();
                    if (optional.isPresent()) {
                        ResFancDTO resFancDTO = optional.get();
                        resUserPointsDTO.setName(resFancDTO.getName());
                        resUserPointsDTO.setStoreFanName(resFancDTO.getStoreFanName());
                    }
                });
            }
        }
        log.info("UserPointsAnalysisServiceImpl.getUserPoints----end----");
        return ExecuteDTO.success(new ExecutePageDTO<>(atomExecuteDTO.getData().getTotal(), userPoints));
    }


}
