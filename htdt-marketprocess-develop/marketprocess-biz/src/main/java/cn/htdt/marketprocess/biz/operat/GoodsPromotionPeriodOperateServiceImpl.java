package cn.htdt.marketprocess.biz.operat;

import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.generator.MarketFormGenerator;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqGoodsPromotionGoodsRelationDTO;
import cn.htdt.marketcenter.dto.request.AtomReqGoodsPromotionPeriodDTO;
import cn.htdt.marketprocess.api.operat.GoodsPromotionPeriodOperateService;
import cn.htdt.marketprocess.biz.conversion.GoodsPromotionPeriodAssert;
import cn.htdt.marketprocess.dto.request.ReqGoodsPromotionPeriodDTO;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomGoodsPromotionGoodsRelationOperateService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomGoodsPromotionPeriodOperateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

/**
 * 活动场次操作 service
 *
 * <AUTHOR>
 * @date 2021-09-23
 */
@Slf4j
@DubboService
public class GoodsPromotionPeriodOperateServiceImpl implements GoodsPromotionPeriodOperateService {

    @Autowired
    private GoodsPromotionPeriodAssert goodsPromotionPeriodAssert;

    @Resource
    private AtomGoodsPromotionPeriodOperateService atomGoodsPromotionPeriodOperateService;

    @Resource
    private AtomGoodsPromotionGoodsRelationOperateService atomGoodsPromotionGoodsRelationOperateService;

    /**
     * 新增商品促销活动的时间段信息
     *
     * @param periodDTO
     * @return
     * <AUTHOR>
     * @date 2021-06-28
     */
    @Override
    public ExecuteDTO<String> saveGoodsPromotionPeriod(ReqGoodsPromotionPeriodDTO periodDTO) {
        log.info("**saveGoodsPromotionPeriod-入参：{}", periodDTO);
        // 入参校验
        goodsPromotionPeriodAssert.saveGoodsPromotionPeriodAssert(periodDTO);

        // 入参转换
        AtomReqGoodsPromotionPeriodDTO reqPeriodDTO = BeanCopierUtil.copy(periodDTO, AtomReqGoodsPromotionPeriodDTO.class);
        // 生成活动场次编号
        String periodNo = MarketFormGenerator.genGoodsPeriodNo();
        reqPeriodDTO.setPeriodNo(periodNo);
        ExecuteDTO executeDTO = atomGoodsPromotionPeriodOperateService.saveGoodsPromotionPeriod(reqPeriodDTO);
        if (null == executeDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }

        // 返回活动场次编号
        return ExecuteDTO.success(periodNo);
    }

    /**
     * 根据活动编号时间段编号，删除促销活动时间段数据
     *
     * @param periodDTO
     * @return
     * <AUTHOR>
     * @date 2021-09-23
     */
    @Override
    public ExecuteDTO deletePeriodByNo(ReqGoodsPromotionPeriodDTO periodDTO) {
        log.info("**deletePeriodByNo-入参：{}", periodDTO);
        // 入参校验
        goodsPromotionPeriodAssert.deletePeriodByNoAssert(periodDTO);

        // 删除对应场次下的关联商品数据
        AtomReqGoodsPromotionGoodsRelationDTO relationDTO = BeanCopierUtil.copy(periodDTO, AtomReqGoodsPromotionGoodsRelationDTO.class);
        ExecuteDTO deleteGoodsExecuteDTO = atomGoodsPromotionGoodsRelationOperateService.deletePromotionGoodsByNo(relationDTO);
        if (null == deleteGoodsExecuteDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!deleteGoodsExecuteDTO.successFlag()) {
            return ExecuteDTO.error(deleteGoodsExecuteDTO.getStatus(), deleteGoodsExecuteDTO.getMsg());
        }

        // 删除活动场次数据
        AtomReqGoodsPromotionPeriodDTO reqPeriodDTO = BeanCopierUtil.copy(periodDTO, AtomReqGoodsPromotionPeriodDTO.class);
        ExecuteDTO deletePeriodExecuteDTO = atomGoodsPromotionPeriodOperateService.deletePeriodByNo(reqPeriodDTO);
        if (null == deletePeriodExecuteDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!deletePeriodExecuteDTO.successFlag()) {
            return ExecuteDTO.error(deletePeriodExecuteDTO.getStatus(), deletePeriodExecuteDTO.getMsg());
        }

        // 返回结果
        return ExecuteDTO.success();
    }

}
