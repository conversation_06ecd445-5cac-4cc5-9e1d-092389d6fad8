package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqCouponSendRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomResCouponSendRecordDTO;
import cn.htdt.marketprocess.api.analysis.CouponSendRecordAnalysisService;
import cn.htdt.marketprocess.dto.request.ReqCouponSendRecordDTO;
import cn.htdt.marketprocess.dto.response.ResCouponSendRecordDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomCouponSendRecordAnalysisService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 优惠券发券记录查询服务
 *
 * <AUTHOR>
 */
@Slf4j
@DubboService
public class CouponSendRecordAnalysisServiceImpl implements CouponSendRecordAnalysisService {
    /**
     * 优惠券活动
     */
    @Resource
    private AtomCouponSendRecordAnalysisService atomCouponSendRecordAnalysisService;


    /**
     * 获取分页优惠券发送记录
     * @param reqCouponSettingDTO
     * @return
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResCouponSendRecordDTO>> getCouponSendRecordPage(ReqCouponSendRecordDTO reqCouponSettingDTO) {
        log.info("MarketProcess-CouponSendRecordAnalysisServiceImpl-getCouponSendRecordPage-params-start");
        log.info("MarketProcess-CouponSendRecordAnalysisServiceImpl-getCouponSendRecordPage-params={}", JSON.toJSONString(reqCouponSettingDTO));
        AtomReqCouponSendRecordDTO reqCouponSendRecordDTO = BeanCopierUtil.copy(reqCouponSettingDTO, AtomReqCouponSendRecordDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResCouponSendRecordDTO>> couponSendRecordListPage = atomCouponSendRecordAnalysisService.getCouponSendRecordListPage(reqCouponSendRecordDTO);
        log.info("MarketProcess-CouponSendRecordAnalysisServiceImpl-getCouponSendRecordPage-return={}", JSON.toJSONString(couponSendRecordListPage));
        log.info("MarketProcess-CouponSendRecordAnalysisServiceImpl-getCouponSendRecordPage-end");
        //判断状态
        if (!couponSendRecordListPage.successFlag()) {
            return ExecuteDTO.error(couponSendRecordListPage.getStatus(), couponSendRecordListPage.getMsg());
        }
        //参数转换
        ExecutePageDTO<ResCouponSendRecordDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResCouponSendRecordDTO> resCouponSendRecordDTOS = BeanCopierUtil.copyList(couponSendRecordListPage.getData().getRows(), ResCouponSendRecordDTO.class);
        if (CollectionUtils.isNotEmpty(resCouponSendRecordDTOS)){
            this.setAllSendCount(resCouponSendRecordDTOS);
        }
        executePageDTO.setRows(resCouponSendRecordDTOS);
        executePageDTO.setTotal(couponSendRecordListPage.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }
    /**
     * 塞总发送量
     * */
    private void setAllSendCount(List<ResCouponSendRecordDTO> resCouponSendRecordDTOS) {
        Integer allSendCount = resCouponSendRecordDTOS.stream().collect(Collectors.summingInt(ResCouponSendRecordDTO::getSendCount));
        resCouponSendRecordDTOS.forEach(couponSendRecord->{couponSendRecord.setAllSendCount(allSendCount);});
    }
}
