package cn.htdt.marketprocess.biz.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketprocess.biz.conversion.GiftCardSecretKeyAssert;
import cn.htdt.marketprocess.dto.request.ReqGiftCardSecretKeyDTO;
import cn.htdt.marketprocess.dao.GiftCardSecretKeyDao;
import cn.htdt.marketprocess.domain.GiftCardSecretKeyDomain;
import cn.htdt.marketprocess.api.operat.GiftCardSecretKeyOperateService;
import cn.htdt.marketprocess.legacycenter.biz.operat.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/11 16:17
 */
@Slf4j
@DubboService
public class GiftCardSecretKeyOperateServiceImpl extends BaseServiceImpl implements GiftCardSecretKeyOperateService {

    @Resource
    private GiftCardSecretKeyDao giftCardSecretKeyDao;

    @Resource
    private GiftCardSecretKeyAssert giftCardSecretKeyAssert;

    @Override
    public ExecuteDTO batchAddGiftCardSecretKey(List<ReqGiftCardSecretKeyDTO> reqGiftCardSecretKeyDTOList) {
        log.info("**AtomGiftCardSecretKeyOperateServiceImpl.批量新增卡密**start**");
        log.info("**AtomGiftCardSecretKeyOperateServiceImpl.batchAddGiftCardSecretKey入参：{}", reqGiftCardSecretKeyDTOList);
        List<GiftCardSecretKeyDomain> giftCardSecretKeyDomainList = BeanCopierUtil.copyList(reqGiftCardSecretKeyDTOList, GiftCardSecretKeyDomain.class);
        // 数据入库
        this.saveBatch(giftCardSecretKeyDomainList);
        log.info("**AtomGiftCardSecretKeyOperateServiceImpl.批量新增卡密**end**");
        // 返回结果
        return ExecuteDTO.success();
    }

    @Override
    public ExecuteDTO batchUpdateGiftCardSecretKey(List<ReqGiftCardSecretKeyDTO> reqGiftCardSecretKeyDTOList) {
        log.info("**AtomGiftCardSecretKeyOperateServiceImpl.批量编辑卡密**start**");
        log.info("**AtomGiftCardSecretKeyOperateServiceImpl.batchUpdateGiftCardSecretKey：{}", reqGiftCardSecretKeyDTOList);
        List<GiftCardSecretKeyDomain> giftCardSecretKeyDomainList = BeanCopierUtil.copyList(reqGiftCardSecretKeyDTOList, GiftCardSecretKeyDomain.class);
        // 数据入库
        giftCardSecretKeyDao.batchUpdateGiftCardSecretKey(giftCardSecretKeyDomainList);
        log.info("**AtomGiftCardSecretKeyOperateServiceImpl.批量编辑卡密**end**");
        // 返回结果
        return ExecuteDTO.success();
    }

    /**
     * 编辑卡密数据
     *
     * @param reqGiftCardSecretKeyDTO 请求参数
     * @return ExecuteDTO
     */
    @Override
    public ExecuteDTO updateGiftCardSecretKey(ReqGiftCardSecretKeyDTO reqGiftCardSecretKeyDTO) {
        log.info("**AtomGiftCardSecretKeyOperateServiceImpl.**start**");
        log.info("---AtomGiftCardSecretKeyOperateServiceImpl.updateGiftCardSecretKey---编辑卡密数据,入参：{}", reqGiftCardSecretKeyDTO);
        giftCardSecretKeyAssert.updateGiftCardSecretKeyAssert(reqGiftCardSecretKeyDTO);
        GiftCardSecretKeyDomain domain = BeanCopierUtil.copy(reqGiftCardSecretKeyDTO, GiftCardSecretKeyDomain.class);
        // 数据入库
        giftCardSecretKeyDao.updateByParam(domain);
        log.info("---AtomGiftCardSecretKeyOperateServiceImpl.编辑卡密数据**end**");
        // 返回结果
        return ExecuteDTO.success();
    }

    /**
     * 编辑礼品卡卡密状态
     *
     * @param reqGiftCardSecretKeyDTO 请求参数
     * @return ExecuteDTO
     */
    @Override
    public ExecuteDTO modifyGiftCardSecretKeyStatus(ReqGiftCardSecretKeyDTO reqGiftCardSecretKeyDTO) {
        log.info("---AtomGiftCardSecretKeyOperateServiceImpl.updateGiftCardSecretKeyStatus---编辑礼品卡卡密状态,入参：{}", reqGiftCardSecretKeyDTO);
        giftCardSecretKeyAssert.modifyGiftCardSecretKeyStatusAssert(reqGiftCardSecretKeyDTO);
        GiftCardSecretKeyDomain domain = BeanCopierUtil.copy(reqGiftCardSecretKeyDTO, GiftCardSecretKeyDomain.class);
        // 数据入库
        giftCardSecretKeyDao.updateGiftCardSecretKeyStatus(domain);
        log.info("---AtomGiftCardSecretKeyOperateServiceImpl.编辑礼品卡卡密状态**end**");
        // 返回结果
        return ExecuteDTO.success();
    }
}
