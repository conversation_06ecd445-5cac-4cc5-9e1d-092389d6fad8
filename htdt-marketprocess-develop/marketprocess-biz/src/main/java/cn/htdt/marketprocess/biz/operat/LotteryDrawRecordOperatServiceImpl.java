package cn.htdt.marketprocess.biz.operat;

import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.enums.MarketErrorCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.OrderStatusEnum;
import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.goods.BillTypeEnum;
import cn.htdt.common.enums.goods.OperTypeEnum;
import cn.htdt.common.enums.goods.SourceTypeEnum;
import cn.htdt.common.enums.market.*;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.goodsprocess.api.analysis.GoodsAnalysisService;
import cn.htdt.goodsprocess.api.operat.GoodsRealStockOperatService;
import cn.htdt.goodsprocess.dto.request.goods.ReqGoodsDTO;
import cn.htdt.goodsprocess.dto.request.goodsrealstock.ReqGoodsStockDTO;
import cn.htdt.goodsprocess.dto.response.goods.ResGoodsDTO;
import cn.htdt.marketcenter.dto.request.AtomReqLotteryDrawRecordDTO;
import cn.htdt.marketcenter.dto.request.AtomReqLotteryRewardGoodsRelationDTO;
import cn.htdt.marketcenter.dto.response.AtomResLotteryDrawRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomResLotteryRewardGoodsRelationDTO;
import cn.htdt.marketprocess.api.operat.LotteryDrawRecordOperatService;
import cn.htdt.marketprocess.api.operat.UserPointsOperateService;
import cn.htdt.marketprocess.biz.conversion.LotteryDrawRecordAssert;
import cn.htdt.marketprocess.dao.CouponUserRecordDao;
import cn.htdt.marketprocess.dto.request.ReqLotteryDrawRecordDTO;
import cn.htdt.marketprocess.dto.request.ReqUserPointsRecordDTO;
import cn.htdt.marketprocess.dto.response.ResLotteryDrawRecordDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomLotteryDrawRecordAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomPromotionRewardGoodsRelationAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomCouponUserRecordOperatService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomLotteryDrawRecordOperatService;
import cn.htdt.marketprocess.vo.CouponUserRecordVO;
import cn.htdt.ordercenter.dto.request.AtomReqSoDTO;
import cn.htdt.ordercenter.dto.response.AtomResSoDTO;
import cn.htdt.orderprocess.api.LegacyOrderCenterService;
import cn.htdt.orderprocess.api.OrderOperatService;
import cn.htdt.orderprocess.api.ReturnOrderOperatService;
import cn.htdt.orderprocess.dto.request.ReqBatchCancelOrderDTO;
import cn.htdt.orderprocess.dto.request.ReqOrderDTO;
import cn.htdt.orderprocess.dto.request.ReqTakeOrderDTO;
import cn.htdt.orderprocess.dto.request.returnorder.ReqLaunchReturnResourceDTO;
import cn.htdt.orderprocess.dto.request.writeoff.ReqGoodsWriteOffDTO;
import cn.htdt.orderprocess.dto.request.writeoff.ReqOrderWriteOffDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@DubboService
public class LotteryDrawRecordOperatServiceImpl implements LotteryDrawRecordOperatService {

    @Autowired
    private LotteryDrawRecordAssert lotteryDrawRecordAssert;

    @Resource
    private AtomLotteryDrawRecordOperatService atomLotteryDrawRecordOperatService;

    @Resource
    private AtomLotteryDrawRecordAnalysisService atomLotteryDrawRecordAnalysisService;

    @DubboReference
    private ReturnOrderOperatService returnOrderOperatService;

    @DubboReference
    private GoodsAnalysisService goodsAnalysisService;

    @DubboReference
    private LegacyOrderCenterService legacyOrderCenterService;

    /**
     * 活动奖品商品关联
     */
    @Resource
    private AtomPromotionRewardGoodsRelationAnalysisService atomPromotionRewardGoodsRelationAnalysisService;

    @DubboReference
    private OrderOperatService orderOperatService;

    @DubboReference
    private GoodsRealStockOperatService goodsRealStockOperatService;

    @Resource
    private AtomCouponUserRecordOperatService atomCouponUserRecordOperatService;

    @Resource
    private UserPointsOperateService userPointsOperateService;

    @Resource
    private CouponUserRecordDao couponUserRecordDao;


    @Override
    public ExecuteDTO invalidLotteryDrawRecord(ReqLotteryDrawRecordDTO reqLotteryDrawRecordDTO) {
        lotteryDrawRecordAssert.invalidLotteryDrawRecordAssert(reqLotteryDrawRecordDTO);
        AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO = BeanCopierUtil.copy(reqLotteryDrawRecordDTO, AtomReqLotteryDrawRecordDTO.class);
        if (StringUtils.isNotBlank(reqLotteryDrawRecordDTO.getRecordNoList())) {
            atomReqLotteryDrawRecordDTO.setRecordNos(Splitter.on(",").splitToList(reqLotteryDrawRecordDTO.getRecordNoList()));
        }
        if (StringUtils.isNotBlank(reqLotteryDrawRecordDTO.getOrderNoList())) {
            atomReqLotteryDrawRecordDTO.setOrderNos(Splitter.on(",").splitToList(reqLotteryDrawRecordDTO.getOrderNoList()));
        }
        // 蛋品, 查询中奖记录编号
        ExecuteDTO<List<AtomResLotteryDrawRecordDTO>> listExecuteDTO = atomLotteryDrawRecordAnalysisService.getRewardRecordList(atomReqLotteryDrawRecordDTO);

        List<String> userCouponNoList = new ArrayList<>();
        if (listExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(listExecuteDTO.getData())) {

            listExecuteDTO.getData().forEach(atomResLotteryDrawRecordDTO -> {
                if (LotteryRewardTypeEnum.REWARD_TYPE_TWO.getCode().equals(atomResLotteryDrawRecordDTO.getRewardType())) {
                    //优惠券
                    userCouponNoList.add(atomResLotteryDrawRecordDTO.getUserCouponNo());
                }
            });

            // 去查询券是否使用, 如果作废的券当中, 有已经使用的券, 则报错
            if (CollectionUtils.isNotEmpty(userCouponNoList)) {
                CouponUserRecordVO couponUserRecordVO = new CouponUserRecordVO();
                couponUserRecordVO.setUserCouponNoList(userCouponNoList);
                couponUserRecordVO.setUseFlag(WhetherEnum.YES.getCode());
                List<String> usedCouponNoList = couponUserRecordDao.getUsedCoupon(couponUserRecordVO);
                if (CollectionUtils.isNotEmpty(usedCouponNoList)) {
                    log.info("invalidLotteryDrawRecord, exist used coupon, usedCouponNoList: {}", JSON.toJSONString(usedCouponNoList));
                    return ExecuteDTO.error(MarketErrorCode.CODE_17000505);
                }
            }
        }

        ExecuteDTO executeDTO = atomLotteryDrawRecordOperatService.invalidLotteryDrawRecord(atomReqLotteryDrawRecordDTO);
        if (CollectionUtils.isEmpty(atomReqLotteryDrawRecordDTO.getOrderNos())) {
            //查询订单编号，进行订单作废
            ExecuteDTO<List<String>> orderExecuteDTO = atomLotteryDrawRecordAnalysisService.getOrderNoListByRecordNos(atomReqLotteryDrawRecordDTO.getRecordNos());
            log.info(String.format("查询订单编号结果:%s", JSON.toJSONString(executeDTO)));
            if (orderExecuteDTO.successFlag() && CollectionUtils.isEmpty(orderExecuteDTO.getData())) {
                atomReqLotteryDrawRecordDTO.setOrderNos(orderExecuteDTO.getData());
            }
        }
        if (CollectionUtils.isNotEmpty(atomReqLotteryDrawRecordDTO.getOrderNos())) {
            ReqTakeOrderDTO reqTakeOrderDTO = new ReqTakeOrderDTO();
            reqTakeOrderDTO.setOrderNos(atomReqLotteryDrawRecordDTO.getOrderNos());
            log.info(String.format("作废订单数据入参:%s", JSON.toJSONString(reqTakeOrderDTO)));
            ExecuteDTO executeDTO1 = orderOperatService.isTakeOrder(reqTakeOrderDTO);
            log.info(String.format("作废订单数据结果:%s", JSON.toJSONString(executeDTO1)));
        }
        //若中奖记录是优惠券，同时作废优惠券数据
//        if (listExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(listExecuteDTO.getData())) {
//            invalidateCoupon(listExecuteDTO.getData());
//        }
        invalidateDrawRecordCoupon(userCouponNoList);

        return executeDTO;
    }


    @Override
    public ExecuteDTO launchLotteryReturnResource(ReqLotteryDrawRecordDTO reqLotteryDrawRecordDTO) {
        log.info("LotteryDrawRecordOperatServiceImpl#launchLotteryReturnResource----param----{}", JSONObject.toJSONString(reqLotteryDrawRecordDTO));
        lotteryDrawRecordAssert.launchLotteryReturnResource(reqLotteryDrawRecordDTO);
        String sourceOrderNo = reqLotteryDrawRecordDTO.getOrderNo();
        //查出该退单订单的所有中奖记录列表
        ExecuteDTO<List<AtomResLotteryDrawRecordDTO>> recordsExecuteDTO = atomLotteryDrawRecordAnalysisService.getRewardRecordListBySourceOrderNo(sourceOrderNo);

        if (recordsExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(recordsExecuteDTO.getData())) {
            List<AtomResLotteryDrawRecordDTO> drawRecords = recordsExecuteDTO.getData();
            log.info("launchLotteryReturnResource--订单下的中奖记录----{}",JSONObject.toJSONString(drawRecords));
            //中奖记录作废
            invalidateLotteryDrawRecord(drawRecords);
            //实物礼品产生的订单作废 只有实物礼品 orderNo才有数据
            invalidateGiftOrder(reqLotteryDrawRecordDTO, drawRecords);
            //优惠券数据作废
            invalidateCoupon(drawRecords);
            //如果是店铺的奖品 需要退积分
            invalidatePoints(drawRecords);
            log.info("LotteryDrawRecordOperatServiceImpl#launchLotteryReturnResource----end----");
        }
        return ExecuteDTO.success();
    }

    @Override
    public ExecuteDTO batchInvalidLotteryDrawRecord(ReqLotteryDrawRecordDTO reqLotteryDrawRecordDTO) {
        return null;
    }

    @Override
    public ExecuteDTO exportLotteryDrawRecord(ReqLotteryDrawRecordDTO reqLotteryDrawRecordDTO) {
        return null;
    }

    @Override
    public ExecuteDTO<Integer> modifyLotteryDrawRecordWriteOff(ReqLotteryDrawRecordDTO reqLotteryDrawRecordDTO) {
        log.info("-LotteryDrawRecordOperatServiceImpl-modifyLotteryDrawRecordWriteOff-start");
        log.info("-LotteryDrawRecordOperatServiceImpl-modifyLotteryDrawRecordWriteOff-param={}", JSON.toJSONString(reqLotteryDrawRecordDTO));
        lotteryDrawRecordAssert.modifyLotteryDrawRecordWriteOffAssert(reqLotteryDrawRecordDTO);
        AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO = BeanCopierUtil.copy(reqLotteryDrawRecordDTO, AtomReqLotteryDrawRecordDTO.class);
        // 先校验是否存在
        ExecuteDTO<AtomResLotteryDrawRecordDTO> atomExecuteDTO = atomLotteryDrawRecordAnalysisService.getWriteOffListByOrderNo(atomReqLotteryDrawRecordDTO);
        if (!atomExecuteDTO.successFlag()) {
            return ExecuteDTO.error(atomExecuteDTO.getStatus(), atomExecuteDTO.getMsg());
        }
        if (atomExecuteDTO.getData() == null) {
            return ExecuteDTO.error(CommonCode.CODE_10000001, "data");
        }
        if (!WriteOffStatusEnum.ALLOW_WRITE_OFF.getCode().equals(atomExecuteDTO.getData().getWriteOffStatus())) {
            return ExecuteDTO.error(CommonCode.CODE_10000002, "核销状态不对");
        }
        // 实物奖品 发放状态 未领取 -> 已领取
        atomReqLotteryDrawRecordDTO.setSendReceiveStatus(SendReceiveStatusEnum.HAS_RECEIVE.getCode());
        if (StringUtils.isNotBlank(atomExecuteDTO.getData().getRewardNo())) {
            atomReqLotteryDrawRecordDTO.setRewardNo(atomExecuteDTO.getData().getRewardNo());
        }
        // 批量核销状态
        ExecuteDTO<Integer> executeDTO = atomLotteryDrawRecordOperatService.modifyLotteryDrawRecordWriteOff(atomReqLotteryDrawRecordDTO);
        if (CollectionUtils.isNotEmpty(atomReqLotteryDrawRecordDTO.getMarketGoodsWriteOffList())) {
            ExecuteDTO writeOffExecuteDTO = orderOperatService.deliverySoItemWriteOff(
                    BeanCopierUtil.copyList(atomReqLotteryDrawRecordDTO.getMarketGoodsWriteOffList(), ReqGoodsWriteOffDTO.class),
                    BeanCopierUtil.copy(reqLotteryDrawRecordDTO, ReqOrderWriteOffDTO.class));

            if (!writeOffExecuteDTO.successFlag()) {
                return ExecuteDTO.error(writeOffExecuteDTO.getStatus(), writeOffExecuteDTO.getMsg());
            }
        }

        log.info("-LotteryDrawRecordOperatServiceImpl-modifyLotteryDrawRecordWriteOff-同步到订单-atomExecuteDTO={}", JSON.toJSONString(atomExecuteDTO));
        // 同步到订单
        if (StringUtils.isNotEmpty(atomExecuteDTO.getData().getOrderNo())) {
//            List<ReqOrderDTO> reqSoDTOList = new ArrayList<>();
//            ReqOrderDTO reqOrderDTO = new ReqOrderDTO();
//            reqOrderDTO.setOrderNo(atomExecuteDTO.getData().getOrderNo());
//            reqSoDTOList.add(reqOrderDTO);
//            orderOperatService.modifyDistributionWriteOffGoodsList(reqSoDTOList);
            List<AtomReqSoDTO> atomReqSoDTOS = new ArrayList<>();
            AtomReqSoDTO atomReqSoDTO = new AtomReqSoDTO();
            atomReqSoDTO.setOrderNo(atomExecuteDTO.getData().getOrderNo());
            atomReqSoDTO.setOrderStatus(OrderStatusEnum.HAS_COMPLETE.getCode());
            atomReqSoDTO.setOrderReceiptLastDate(LocalDateTime.now());
            atomReqSoDTO.setWriteOffStatus(WhetherEnum.YES.getCode());
            atomReqSoDTO.setWriteOffTime(LocalDateTime.now());
            atomReqSoDTOS.add(atomReqSoDTO);
            log.info("-LotteryDrawRecordOperatServiceImpl-modifyLotteryDrawRecordWriteOff-同步到订单-入参-atomReqSoDTOS={}", JSON.toJSONString(atomReqSoDTOS));
            legacyOrderCenterService.modifyWriteOffGoodsList(atomReqSoDTOS);
        }
        // 出库扣减库存-前提是实体奖品且非临时商品、店铺
        this.outStock(atomExecuteDTO.getData(), reqLotteryDrawRecordDTO);
        log.info("-LotteryDrawRecordOperatServiceImpl-modifyLotteryDrawRecordWriteOff-end");
        return executeDTO;
    }

    /**
     * 出库
     */
    private ExecuteDTO outStock(AtomResLotteryDrawRecordDTO recordDTO, ReqLotteryDrawRecordDTO reqLotteryDrawRecordDTO) {
        log.info("-LotteryDrawRecordOperatServiceImpl-modifyLotteryDrawRecordWriteOff-outStock-recordDTO.getRewardType()：{}", JSON.toJSONString(recordDTO.getRewardType()));
        // 实物奖品
        if (LotteryRewardTypeEnum.REWARD_TYPE_ONE.getCode().equals(recordDTO.getRewardType())) {
            // 获取中奖记录-实物奖品
            AtomReqLotteryRewardGoodsRelationDTO atomReqLotteryRewardGoodsRelationDTO = new AtomReqLotteryRewardGoodsRelationDTO();
            atomReqLotteryRewardGoodsRelationDTO.setRewardNo(recordDTO.getRewardNo());
            ExecuteDTO<AtomResLotteryRewardGoodsRelationDTO> executeDTO = atomPromotionRewardGoodsRelationAnalysisService.getLotteryRewardGoodsRelation(atomReqLotteryRewardGoodsRelationDTO);
            log.info("-LotteryDrawRecordOperatServiceImpl-modifyLotteryDrawRecordWriteOff-outStock-出参：{}", JSON.toJSONString(executeDTO));
            if (!executeDTO.successFlag()) {
                return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
            }
            AtomResLotteryRewardGoodsRelationDTO relationDTO = executeDTO.getData();
            // ***************************店铺才会出库****************************
            if (relationDTO != null && StringUtils.isNotEmpty(relationDTO.getSourceType()) && SourceTypeEnum.SOURCE_TYPE_1003.getCode().equals(relationDTO.getSourceType())) {
                //组装出库数据
                List<ReqGoodsStockDTO> goodsStockDTOList = new ArrayList<>();
                ReqGoodsStockDTO goods = new ReqGoodsStockDTO();
                //商品编号
                goods.setGoodsNo(relationDTO.getGoodsNo());
                //单据编号
                goods.setBillCode(recordDTO.getOrderNo());
                //操作类型
                goods.setOperType(OperTypeEnum.OPER_TYPE_DEL.getCode());
                //来源单据类型
                goods.setBillType(BillTypeEnum.BILL_TYPE_SEND_OUT.getCode());
                //身份信息(店铺)
                goods.setLoginIdentity(3);
                //店铺信息
                goods.setStoreName(recordDTO.getStoreName());
                goods.setStoreNo(recordDTO.getStoreNo());
                // 默认1
                goods.setStockNum(BigDecimal.ONE);
                // 仓库信息
                goods.setWarehouseNo(reqLotteryDrawRecordDTO.getWarehouseNo());
                goods.setWarehouseName(reqLotteryDrawRecordDTO.getWarehouseName());
                goods.setGoodsImeiAddNos(reqLotteryDrawRecordDTO.getGoodsImeiAddNos());
                goodsStockDTOList.add(goods);

                //20230809蛋品-wxb-库存管理-批量出库
                /*List<ReqGoodsMultiUnitStockNumDTO> reqGoodsMultiUnitStockNumDTOS = BeanCopierUtil.copyList(goodsStockDTOList, ReqGoodsMultiUnitStockNumDTO.class);
                ExecuteDTO<Map<String, ResGoodsMultiUnitStockNumDTO>> multiUnitGoodsStockNum = goodsAnalysisService.getMultiUnitGoodsStockNum(reqGoodsMultiUnitStockNumDTOS);
                Map<String, ResGoodsMultiUnitStockNumDTO> multiUnitGoodsStockMap = multiUnitGoodsStockNum.getData();
                if (multiUnitGoodsStockNum.successFlag() && null != multiUnitGoodsStockMap){
                    ResGoodsMultiUnitStockNumDTO resGoodsMultiUnitStockNumDTO = multiUnitGoodsStockMap.get(goods.getGoodsNo());
                    if (null != resGoodsMultiUnitStockNumDTO){
                        goodsStockDTOList.get(NumConstant.ZERO).setGoodsNo(resGoodsMultiUnitStockNumDTO.getMultiUnitGoodsNo());
                        //如果是多单位商品的辅计量单位--换算成主计量单位的规格
                        goodsStockDTOList.get(NumConstant.ZERO).setStockNum(resGoodsMultiUnitStockNumDTO.getMultiUnitStockNum());
                    }
                }*/

                log.info("-LotteryDrawRecordOperatServiceImpl-modifyLotteryDrawRecordWriteOff-outStock-batchOutFromStock-入参：{}", JSON.toJSONString(goodsStockDTOList));
                ExecuteDTO executeDTOs = goodsRealStockOperatService.batchOutFromStock(goodsStockDTOList);
                log.info("-LotteryDrawRecordOperatServiceImpl-modifyLotteryDrawRecordWriteOff-outStock-batchOutFromStock-出参：{}", JSON.toJSONString(executeDTOs));
                if (!executeDTOs.successFlag()) {
                    return ExecuteDTO.error(executeDTOs.getStatus(), executeDTOs.getMsg());
                }
            }
        }
        return ExecuteDTO.success();
    }

    @Override
    public ExecuteDTO<Integer> batchModifyLotteryDrawRecordWriteOff(List<ReqLotteryDrawRecordDTO> reqLotteryDrawRecordDTOs) {
        log.info("-LotteryDrawRecordOperatServiceImpl-batchModifyLotteryDrawRecordWriteOff-start");
        log.info("-LotteryDrawRecordOperatServiceImpl-batchModifyLotteryDrawRecordWriteOff-param={}", JSON.toJSONString(reqLotteryDrawRecordDTOs));
        lotteryDrawRecordAssert.batchModifyLotteryDrawRecordWriteOffAssert(reqLotteryDrawRecordDTOs);
        List<AtomReqLotteryDrawRecordDTO> atomReqLotteryDrawRecordDTOs = BeanCopierUtil.copyList(reqLotteryDrawRecordDTOs, AtomReqLotteryDrawRecordDTO.class);
        // 先校验是否存在
        AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO = new AtomReqLotteryDrawRecordDTO();
        List<String> orderNos = new ArrayList<>();
        atomReqLotteryDrawRecordDTOs.forEach(dto -> {
            orderNos.add(dto.getOrderNo());
        });
        atomReqLotteryDrawRecordDTO.setOrderNos(orderNos);
        ExecuteDTO<List<AtomResLotteryDrawRecordDTO>> atomExecuteDTO = atomLotteryDrawRecordAnalysisService.getWriteOffListByOrderNos(atomReqLotteryDrawRecordDTO);
        if (!atomExecuteDTO.successFlag()) {
            return ExecuteDTO.error(atomExecuteDTO.getStatus(), atomExecuteDTO.getMsg());
        }
        if (CollectionUtils.isEmpty(atomExecuteDTO.getData())) {
            return ExecuteDTO.error(CommonCode.CODE_10000001, "data");
        }
        if (atomExecuteDTO.getData().size() != atomReqLotteryDrawRecordDTOs.size()) {
            return ExecuteDTO.error(CommonCode.CODE_10000002, "查询数据对应不上");
        }
        boolean flag = true;
        for (AtomResLotteryDrawRecordDTO dto : atomExecuteDTO.getData()) {
            if (!WriteOffStatusEnum.ALLOW_WRITE_OFF.getCode().equals(dto.getWriteOffStatus())) {
                flag = false;
                break;
            }
        }
        if (!flag) {
            return ExecuteDTO.error(CommonCode.CODE_10000002, "存在记录核销状态不对");
        }
        atomReqLotteryDrawRecordDTOs = atomReqLotteryDrawRecordDTOs.stream().map(lotteryDrawRecordDTO -> {
            atomExecuteDTO.getData().stream().filter(data->
                    Objects.equals(lotteryDrawRecordDTO.getOrderNo(), data.getOrderNo()) && Objects.equals(lotteryDrawRecordDTO.getWriteOffCode(), data.getWriteOffCode())).forEach(
                    s -> {
                        lotteryDrawRecordDTO.setRewardNo(s.getRewardNo());
                    });
            return lotteryDrawRecordDTO;
        }).collect(Collectors.toList());
        // 批量核销状态
        ExecuteDTO<Integer> executeDTO = atomLotteryDrawRecordOperatService.batchModifyLotteryDrawRecordWriteOff(atomReqLotteryDrawRecordDTOs);

        // 批量出库
        for (AtomResLotteryDrawRecordDTO dto : atomExecuteDTO.getData()) {
            for (ReqLotteryDrawRecordDTO recordDTO : reqLotteryDrawRecordDTOs) {
                if (dto.getOrderNo().equals(recordDTO.getOrderNo())) {
                    ExecuteDTO outExecuteDTO = this.outStock(dto, recordDTO);
                    if (!outExecuteDTO.successFlag()) {
                        return ExecuteDTO.error(outExecuteDTO.getStatus(), outExecuteDTO.getMsg());
                    }
                    break;
                }
            }
        }

        log.info("-LotteryDrawRecordOperatServiceImpl-batchModifyLotteryDrawRecordWriteOff-批量同步到订单-atomExecuteDTO={}", JSON.toJSONString(atomExecuteDTO));
        // 同步到订单
        if (CollectionUtils.isNotEmpty(atomExecuteDTO.getData())) {
            List<AtomResLotteryDrawRecordDTO> dtos = atomExecuteDTO.getData();
            List<ReqOrderDTO> reqSoDTOList = new ArrayList<>();
            dtos.forEach(dto -> {
                ReqOrderDTO reqOrderDTO = new ReqOrderDTO();
                reqOrderDTO.setOrderNo(dto.getOrderNo());
                reqOrderDTO.setOrderStatus(OrderStatusEnum.HAS_COMPLETE.getCode());
                reqSoDTOList.add(reqOrderDTO);
            });
            orderOperatService.modifyDistributionWriteOffGoodsList(reqSoDTOList);
        }
        log.info("-LotteryDrawRecordOperatServiceImpl-batchModifyLotteryDrawRecordWriteOff-end");
        return executeDTO;
    }


    private List<String> generateRecordNos(List<AtomResLotteryDrawRecordDTO> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().map(AtomResLotteryDrawRecordDTO::getRecordNo).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * 优惠券作废
     *
     * @param drawRecords 订单的中奖记录
     */
    private void invalidateCoupon(List<AtomResLotteryDrawRecordDTO> drawRecords) {
        try{
            log.info("launchLotteryReturnResource--优惠券作废入参----{}", JSONObject.toJSONString(drawRecords));
            List<String> couponNos = new ArrayList<>();
            drawRecords.forEach(atomResLotteryDrawRecordDTO -> {
                if (LotteryRewardTypeEnum.REWARD_TYPE_TWO.getCode().equals(atomResLotteryDrawRecordDTO.getRewardType())) {
                    //优惠券
                    couponNos.add(atomResLotteryDrawRecordDTO.getUserCouponNo());
                }
            });
            //作废优惠券
            if (CollectionUtils.isNotEmpty(couponNos)) {
                ExecuteDTO disableCouponexecuteDTO = atomCouponUserRecordOperatService.disableCouponUserRecord(couponNos);
                log.info("launchLotteryReturnResource----优惠券作废结果----", JSONObject.toJSONString(disableCouponexecuteDTO));
            }
        }catch (Exception e){
            log.error("LotteryDrawRecordOperatServiceImpl#invalidateCoupon----error----{}",e.getMessage());
        }
    }

    /**
     * 蛋品, 优惠券作废
     *
     * @param couponNos 中奖记录券编号集合
     */
    private void invalidateDrawRecordCoupon(List<String> couponNos) {
        try{
            log.info("invalidateDrawRecordCoupon--优惠券作废入参----{}", JSONObject.toJSONString(couponNos));
            //作废优惠券
            if (CollectionUtils.isNotEmpty(couponNos)) {
                ExecuteDTO disableCouponexecuteDTO = atomCouponUserRecordOperatService.disableCouponUserRecord(couponNos);
                log.info("invalidateDrawRecordCoupon----优惠券作废结果----: {}", JSONObject.toJSONString(disableCouponexecuteDTO));
            }
        }catch (Exception e){
            log.error("LotteryDrawRecordOperatServiceImpl#invalidateDrawRecordCoupon----error----{}",e.getMessage());
        }
    }

    /**
     * 作废中奖记录
     *
     * @param drawRecords 订单的中奖记录
     */
    private void invalidateLotteryDrawRecord(List<AtomResLotteryDrawRecordDTO> drawRecords) {
        try {
            log.info("LotteryDrawRecordOperatServiceImpl#invalidateLotteryDrawRecord--中奖记录作废start");
            //中奖的记录编号列表
            List<String> recordNos = generateRecordNos(drawRecords);
            AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO = new AtomReqLotteryDrawRecordDTO();
            atomReqLotteryDrawRecordDTO.setRecordNos(recordNos);
            ExecuteDTO invalidExecuteDTO = atomLotteryDrawRecordOperatService.invalidLotteryDrawRecord(atomReqLotteryDrawRecordDTO);
            log.info("LotteryDrawRecordOperatServiceImpl#invalidateLotteryDrawRecord----中奖记录作废结果", JSONObject.toJSONString(invalidExecuteDTO));
        }catch (Exception e){
            log.error("LotteryDrawRecordOperatServiceImpl#invalidateLotteryDrawRecord----error----{}",e.getMessage());
        }
    }


    /**
     * <p>
     * * 实物礼品资源回退
     * * 对于平台实物礼品 未发货的非临时商品
     * * 对于店铺实物礼品 未核销的非临时商品
     * * 需要返回锁定库存
     * * </p>
     *
     * @param reqLotteryDrawRecordDTO 参数信息
     * @param drawRecords             中奖记录编号
     */
    private void invalidateGiftOrder(ReqLotteryDrawRecordDTO reqLotteryDrawRecordDTO, List<AtomResLotteryDrawRecordDTO> drawRecords) {
        try {
            log.info("invalidateGiftOrder----start----");
            //退库存
            returnStock(drawRecords);
            //关闭订单
            closeOrder(reqLotteryDrawRecordDTO, drawRecords);
            log.info("invalidateGiftOrder----end----");
        }catch (Exception e){
            log.error("LotteryDrawRecordOperatServiceImpl#----error----{}",e.getMessage());
        }
    }

    /**
     * 退库存
     *
     * @param drawRecords 中奖记录
     */
    private ExecuteDTO returnStock(List<AtomResLotteryDrawRecordDTO> drawRecords) {
        log.info("returnStock----start----");
        ExecuteDTO executeDTO =ExecuteDTO.success();
        //过滤出实物礼品的中奖记录
        List<AtomResLotteryDrawRecordDTO> giftDrawRecords = drawRecords
                .stream()
                .filter(drawRecord -> LotteryRewardTypeEnum.REWARD_TYPE_ONE.getCode().equals(drawRecord.getRewardType()))
                .collect(Collectors.toList());
        //去除临时商品
        List<AtomResLotteryDrawRecordDTO> withoutTempDrawRecords = filterTempGoods(giftDrawRecords);
        for (AtomResLotteryDrawRecordDTO drawRecord : withoutTempDrawRecords) {
            if (SourceTypeEnum.SOURCE_TYPE_1001.getCode().equals(drawRecord.getSourceType())) {
                AtomReqSoDTO atomReqSoDTO = new AtomReqSoDTO();
                atomReqSoDTO.setOrderNo(drawRecord.getOrderNo());
                ExecuteDTO<AtomResSoDTO> executeDTO2 = legacyOrderCenterService.getSo(atomReqSoDTO);
                if (executeDTO2.successFlag()
                        && null != executeDTO2.getData()
                        && (OrderStatusEnum.WAITING_CONFIRM.getCode().equals(executeDTO2.getData().getOrderStatus())
                             ||OrderStatusEnum.WAITING_SEND.getCode().equals(executeDTO2.getData().getOrderStatus()))) {
                    executeDTO= returnStockDeduction(drawRecord);
                }
            } else {
                if (WriteOffStatusEnum.ALLOW_WRITE_OFF.getCode().equals(drawRecord.getWriteOffStatus())) {
                    executeDTO=  returnStockDeduction(drawRecord);
                }
            }

        }
        log.info("returnStock----出参----{}",JSONObject.toJSONString(executeDTO));
        return executeDTO;
    }

    /**
     * 关闭实物奖品的订单
     *
     * @param reqLotteryDrawRecordDTO 订单号/操作人信息
     * @param drawRecords             中奖记录
     */
    private void closeOrder(ReqLotteryDrawRecordDTO reqLotteryDrawRecordDTO, List<AtomResLotteryDrawRecordDTO> drawRecords) {
        log.info("closeOrder----start----");
        List<String> recordNos = generateRecordNos(drawRecords);
        ExecuteDTO<List<String>> orderExecuteDTO = atomLotteryDrawRecordAnalysisService.getOrderNoListByRecordNos(recordNos);
        if (orderExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(orderExecuteDTO.getData())) {
            orderExecuteDTO.getData().removeAll(Collections.singleton(""));
            ReqBatchCancelOrderDTO reqBatchCancelOrderDTO = new ReqBatchCancelOrderDTO();
            reqBatchCancelOrderDTO.setOrderNos(orderExecuteDTO.getData());
            reqBatchCancelOrderDTO.setModifyNo(reqLotteryDrawRecordDTO.getModifyNo());
            reqBatchCancelOrderDTO.setModifyName(reqLotteryDrawRecordDTO.getModifyName());
            log.info("LotteryDrawRecordOperatServiceImpl#invalidateGiftOrder----作废订单数据入参----{}", JSON.toJSONString(reqBatchCancelOrderDTO));
            ExecuteDTO executeDTO1 = orderOperatService.batchCloseOrder(reqBatchCancelOrderDTO);
            log.info("LotteryDrawRecordOperatServiceImpl#invalidateGiftOrder----作废订单数据结果----{}", JSON.toJSONString(executeDTO1));
        }
        log.info("closeOrder----end----");
    }

    /**
     * 退库存
     *
     * @param drawRecord 订单编号
     * @return 返回值
     */
    private ExecuteDTO returnStockDeduction(AtomResLotteryDrawRecordDTO drawRecord) {
        log.info("returnStockDeduction----start----");
        ReqLaunchReturnResourceDTO reqLaunchReturnResourceDTO = new ReqLaunchReturnResourceDTO();
        reqLaunchReturnResourceDTO.setOrderNo(drawRecord.getOrderNo());
        reqLaunchReturnResourceDTO.setReturnResourceType(NumConstant.TWO);
        ExecuteDTO executeDTO=returnOrderOperatService.launchReturnResource(reqLaunchReturnResourceDTO);
        log.info("returnStockDeduction----结果----{}",JSONObject.toJSONString(executeDTO));
        return executeDTO;
    }

    /**
     * 积分回退
     *
     * @param drawRecords 订单的中奖记录
     */
    private void invalidatePoints(List<AtomResLotteryDrawRecordDTO> drawRecords) {
        try {
            log.info("LotteryDrawRecordOperatServiceImpl#invalidatePoints--start----");
            // List<AtomResLotteryDrawRecordDTO> list = listExecuteDTO.getData();
            int points = drawRecords.stream().filter(item -> LotteryRewardTypeEnum.REWARD_TYPE_SIX.getCode().equals(item.getRewardType()))
                    .collect(Collectors.toList())
                    .stream()
                    .mapToInt(e -> Integer.valueOf(e.getRewardValue()))
                    .sum();
            if (points > 0) {
                String storeNo = drawRecords.get(0).getStoreNo();
                String fansNo = drawRecords.get(0).getFanNo();
                ReqUserPointsRecordDTO recordDTO = new ReqUserPointsRecordDTO();
                recordDTO.setFansNo(fansNo);
                recordDTO.setStoreNo(storeNo);
                recordDTO.setChangeNum(points);
                recordDTO.setOperateType(PointsOperateTypeEnum.POINTS_DEDUCT.getCode());
                recordDTO.setPointChangeEvent(PointChangeEventEnum.LOTTERY_RETURN.getCode());
                recordDTO.setOrderNo(drawRecords.get(0).getSourceOrderNo());
                ExecuteDTO executeDTO = userPointsOperateService.changeUserPoints(recordDTO);
                log.info("LotteryDrawRecordOperatServiceImpl#invalidatePoints--结果----{}", JSONObject.toJSONString(executeDTO));
            }
        }catch (Exception e){
            log.error("LotteryDrawRecordOperatServiceImpl#invalidatePoints----error----{}",e.getMessage());
        }
    }

    /**
     * 过滤掉临时商品
     *
     * @param giftDrawRecords 实物礼品中奖记录
     * @return 过滤掉临时商品的实物礼品中奖记录
     */
    private List<AtomResLotteryDrawRecordDTO> filterTempGoods(List<AtomResLotteryDrawRecordDTO> giftDrawRecords) {
        log.info("filterTempGoods----start----");
        List<String> goodsNos = giftDrawRecords.stream().map(AtomResLotteryDrawRecordDTO::getGoodsNo).collect(Collectors.toList());
        ReqGoodsDTO reqGoodsDTO = new ReqGoodsDTO();
        reqGoodsDTO.setGoodsNos(goodsNos);
        //不进行分页查询
        reqGoodsDTO.setPageSize(0);
        ExecuteDTO<ExecutePageDTO<ResGoodsDTO>> executeDTO = goodsAnalysisService.getGoodsInfoByParams(reqGoodsDTO);
        if (executeDTO.successFlag()) {
            List<ResGoodsDTO> goodsList = executeDTO.getData().getRows();
            if (giftDrawRecords.size() == goodsList.size()) {
                return giftDrawRecords;
            } else {
                Iterator<AtomResLotteryDrawRecordDTO> iterator = giftDrawRecords.iterator();
                while (iterator.hasNext()) {
                    AtomResLotteryDrawRecordDTO tempIt = iterator.next();
                    int index = 0;
                    for (ResGoodsDTO tempGoods : goodsList) {
                        index++;
                        if (tempGoods.getGoodsNo().equals(tempIt.getGoodsNo())) {
                            break;
                        }
                        if (index == goodsList.size()) {
                            iterator.remove();
                        }
                    }
                }

                return giftDrawRecords;
            }
        }
        return giftDrawRecords;
    }

    @Override
    public ExecuteDTO<ResLotteryDrawRecordDTO> getWriteOffCode(ReqLotteryDrawRecordDTO reqLotteryDrawRecordDTO) {
        // 参数校验
        if (StringUtils.isBlank(reqLotteryDrawRecordDTO.getWriteOffCode())) {
            return ExecuteDTO.error(CommonCode.CODE_10000001, "writeOffCode");
        }
        AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO = BeanCopierUtil.copy(reqLotteryDrawRecordDTO, AtomReqLotteryDrawRecordDTO.class);
        ExecuteDTO<AtomResLotteryDrawRecordDTO> atomResLotteryDrawRecordExecute = atomLotteryDrawRecordOperatService.getWriteOffCode(atomReqLotteryDrawRecordDTO);
        if (!atomResLotteryDrawRecordExecute.successFlag()) {
            return ExecuteDTO.error(atomResLotteryDrawRecordExecute.getStatus(), atomResLotteryDrawRecordExecute.getMsg());
        } else if (atomResLotteryDrawRecordExecute.getData() == null) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (WriteOffStatusEnum.WRITE_OFF.getCode().equals(atomResLotteryDrawRecordExecute.getData().getWriteOffStatus())) {
            return ExecuteDTO.error(CommonCode.CODE_10000003.getCode(),"此核销码已核销");
        }
        return ExecuteDTO.success(BeanCopierUtil.copy(atomResLotteryDrawRecordExecute.getData(), ResLotteryDrawRecordDTO.class));
    }
}
