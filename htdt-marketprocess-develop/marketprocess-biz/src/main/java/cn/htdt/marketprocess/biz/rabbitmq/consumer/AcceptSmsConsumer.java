package cn.htdt.marketprocess.biz.rabbitmq.consumer;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.market.*;
import cn.htdt.common.generator.MarketFormGenerator;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.CollectionUtils;
import cn.htdt.marketcenter.dto.request.AtomReqSmsBalanceOperateRecordDTO;
import cn.htdt.marketcenter.dto.request.AtomReqSmsTrafficDTO;
import cn.htdt.marketprocess.biz.rabbitmq.config.DirectDeplayConfig;
import cn.htdt.marketprocess.biz.utils.SmsUtil;
import cn.htdt.marketprocess.dao.GiftCardSecretKeyDao;
import cn.htdt.marketprocess.dao.SmsSendDetailDao;
import cn.htdt.marketprocess.dao.SmsSendRecordDao;
import cn.htdt.marketprocess.domain.GiftCardSecretKeyDomain;
import cn.htdt.marketprocess.domain.SmsSendDetailDomain;
import cn.htdt.marketprocess.domain.SmsSendRecordDomain;
import cn.htdt.marketprocess.dto.request.message.ReqMwMsgResultDTO;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomSmsBalanceOperateRecordOperatService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomSmsTrafficOperatService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/16 15:29
 */
@Slf4j
@Component
public class AcceptSmsConsumer {

    @Resource
    SmsSendDetailDao smsSendDetailDao;

    @Resource
    SmsSendRecordDao smsSendRecordDao;

    @Resource
    private GiftCardSecretKeyDao giftCardSecretKeyDao;

    @Resource
    private SmsUtil smsUtil;


    @Resource
    private AtomSmsTrafficOperatService atomSmsTrafficOperatService;

    @Resource
    private AtomSmsBalanceOperateRecordOperatService atomSmsBalanceOperateRecordOperatService;

    /**
     * 消费队列内容，短信
     *
     * @param reqMwMsgResultDTOList
     */
    @RabbitHandler
    @RabbitListener(queues = DirectDeplayConfig.NORMAL_QUEUE_SMS_ACCEPT_NAME)
    public void mqSmsSend(List<ReqMwMsgResultDTO> reqMwMsgResultDTOList) {
        log.info("---消费MQ---mqSmsAcceptByDeadLetter---queueName：{}，入参:：{}", DirectDeplayConfig.NORMAL_QUEUE_SMS_ACCEPT_NAME, JSON.toJSONString(reqMwMsgResultDTOList));
        this.smsSend(reqMwMsgResultDTOList);
    }

    /**
     * 消费死信队列内容，接收短信
     *
     * @param reqMwMsgResultDTOList
     */
    @RabbitHandler
    @RabbitListener(queues = DirectDeplayConfig.DEAD_LETTER_QUEUE_SMS_ACCEPT_NAME)
    public void mqSmsSendByDeadLetter(List<ReqMwMsgResultDTO> reqMwMsgResultDTOList) {
        log.info("---消费MQ---mqSmsAcceptByDeadLetter---queueName：{}，入参:：{}", DirectDeplayConfig.DEAD_LETTER_QUEUE_SMS_ACCEPT_NAME, JSON.toJSONString(reqMwMsgResultDTOList));
        this.smsSend(reqMwMsgResultDTOList);
    }

    /**
     * 短信接收
     *
     * @param reqMwMsgResultDTOList
     */
    public void smsSend(List<ReqMwMsgResultDTO> reqMwMsgResultDTOList) {
        try {
            //更新主批次短信状态
            List<SmsSendDetailDomain> smsSendDetailDomainList = new ArrayList<>(reqMwMsgResultDTOList.size());
            reqMwMsgResultDTOList.forEach(reqMwMsgResultDTO -> {
                SmsSendDetailDomain smsSendDetailDomain = new SmsSendDetailDomain();
                smsSendDetailDomain.setBatchNo(reqMwMsgResultDTO.getMsgId());
                smsSendDetailDomain.setDsTelephone(reqMwMsgResultDTO.getDsMobileNum());
                smsSendDetailDomain.setMsgStatus(getSmsSendResultStatus(reqMwMsgResultDTO.getIsSuccess()));
                smsSendDetailDomain.setDetailInfo(reqMwMsgResultDTO.getDetailInfo());
                if (SmsSendResultStatusEnum.SEND_FAILURE.getCode().equals(reqMwMsgResultDTO.getMsgStatus())) {
                    smsSendDetailDomain.setDetailMsg(SmsErrorMsgEnum.getMsgByCode(reqMwMsgResultDTO.getDetailInfo()));
                }
                if (StringUtils.isNotBlank(reqMwMsgResultDTO.getIsSuccess())) {
                    smsSendDetailDomain.setIsSuccess(Integer.valueOf(reqMwMsgResultDTO.getIsSuccess()));
                }
                smsSendDetailDomainList.add(smsSendDetailDomain);
            });
            int executeDTO = smsSendDetailDao.batchUpdateSmsSendDetail(smsSendDetailDomainList);
            log.info("---主批次短信更新---结果：{}", executeDTO);
            reqMwMsgResultDTOList.forEach(reqMwMsgResultDTO -> {
                SmsSendDetailDomain smsSendDetailDomain = new SmsSendDetailDomain();
                smsSendDetailDomain.setSonBatchNo(reqMwMsgResultDTO.getMsgId());
                smsSendDetailDomain.setDsTelephone(reqMwMsgResultDTO.getDsMobileNum());
                smsSendDetailDomain.setMsgStatus(getSmsSendResultStatus(reqMwMsgResultDTO.getIsSuccess()));
                smsSendDetailDomain.setDetailInfo(reqMwMsgResultDTO.getDetailInfo());
                if (SmsSendResultStatusEnum.SEND_FAILURE.getCode().equals(reqMwMsgResultDTO.getMsgStatus())) {
                    smsSendDetailDomain.setDetailMsg(SmsErrorMsgEnum.getMsgByCode(reqMwMsgResultDTO.getDetailInfo()));
                }
                if (StringUtils.isNotBlank(reqMwMsgResultDTO.getIsSuccess())) {
                    smsSendDetailDomain.setIsSuccess(Integer.valueOf(reqMwMsgResultDTO.getIsSuccess()));
                }
                smsSendDetailDomainList.add(smsSendDetailDomain);
            });
            int executeSonDTO = smsSendDetailDao.batchUpdateSmsSendDetail(smsSendDetailDomainList);
            log.info("---子批次短信更新---结果：{}", executeSonDTO);
            //通过批次号查询是否是礼品卡短信发送场景
            List<String> sonBatchNoList = reqMwMsgResultDTOList.stream().map(ReqMwMsgResultDTO::getMsgId).collect(Collectors.toList());
            List<SmsSendDetailDomain> sendDetailDomainList = smsSendDetailDao.selectSendDetailBysonBatchNoList(sonBatchNoList);
            if (CollectionUtils.isNotEmpty(sendDetailDomainList)) {
                //通过卡密修改发送状态
                List<GiftCardSecretKeyDomain> giftCardSecretKeyDomainList = new ArrayList<>();
                sendDetailDomainList.forEach(sendDetailDomain -> {
                    //判断提货卡卡密不为空
                    if(StringUtils.isNotBlank(sendDetailDomain.getSecretKey())){
                        GiftCardSecretKeyDomain giftCardSecretKeyDomain = new GiftCardSecretKeyDomain();
                        giftCardSecretKeyDomain.setSecretKey(sendDetailDomain.getSecretKey());
                        giftCardSecretKeyDomain.setMsgStatus(sendDetailDomain.getMsgStatus());
                        giftCardSecretKeyDomainList.add(giftCardSecretKeyDomain);
                    }
                });
                log.info("-------smsSend,礼品卡-短信发送场景更新短信发送状态入参：{}", giftCardSecretKeyDomainList);
                if(CollectionUtils.isNotEmpty(giftCardSecretKeyDomainList)){
                    giftCardSecretKeyDao.batchUpdateGiftCardSecretKey(giftCardSecretKeyDomainList);
                }
                //存在这种有主子批次概念的短信记录，同步短信记录的定时任务无法处理到这些数据（捞不到子数据，主数据的批次号中台也未记录）。
                //所以在这边直接更新短信发送记录（同时更新主子记录）
                List<String> batchNoList = sendDetailDomainList.stream().map(SmsSendDetailDomain::getBatchNo).distinct().collect(Collectors.toList());
                //先查询所有的子批次号发送记录
                List<SmsSendRecordDomain> smsSendSonRecordDomainList = smsSendRecordDao.selectSendDetailBysonBatchNoList(batchNoList);
                //根据推送结果跟新成功失败条数
                if (CollectionUtils.isNotEmpty(smsSendSonRecordDomainList)) {
                    //筛选所有的待发送的短信记录
                    List<SmsSendRecordDomain> smsInSendingRecordDomainList = smsSendSonRecordDomainList.stream()
                            .filter(sms -> SmsSendStatusEnum.IN_SENDING.getCode().equals(sms.getSendStatus()))
                            .collect(Collectors.toList());
                    //和回调记录做匹配，计算出失败的数量加回
                    List<SmsSendRecordDomain> smsSendFailRecordDomainList = groupAndSum(matchAndCount(smsInSendingRecordDomainList, reqMwMsgResultDTOList));
                    log.info("-------smsSend,短信发送失败待加回数据：{}", smsSendFailRecordDomainList);
                    if(CollectionUtils.isNotEmpty(smsSendFailRecordDomainList)){
                        for(SmsSendRecordDomain smsSendRecordDomain : smsSendFailRecordDomainList){
                            if (StringUtils.isEmpty(smsSendRecordDomain.getStoreNo()) || smsSendRecordDomain.getSendFailNum() == null || smsSendRecordDomain.getSendFailNum() < NumConstant.ONE) {
                                // 1.非店铺发送短信，无需回滚短信余额
                                // 2.没有发送失败条数，无需回滚短信余额
                                continue;
                            }
                            //redis余额回滚失败条数
                            int smsBalance = (int) smsUtil.redisIncreaseSmsBalance(smsSendRecordDomain.getStoreNo(), smsSendRecordDomain.getSendFailNum());
                            AtomReqSmsTrafficDTO atomReqDTO = new AtomReqSmsTrafficDTO();
                            atomReqDTO.setStoreNo(smsSendRecordDomain.getStoreNo());
                            atomReqDTO.setSmsRemainingNum(Long.valueOf(smsSendRecordDomain.getSendFailNum()));
                            atomReqDTO.setSendSuccessNum(Long.valueOf(smsSendRecordDomain.getSendFailNum()));
                            atomReqDTO.setSendSuccessTimes(Long.valueOf(smsSendRecordDomain.getSendPeopleNum()));
                            // 回滚数据库
                            ExecuteDTO execute = atomSmsTrafficOperatService.modifySmsTrafficByIncr(atomReqDTO);
                            //新增短信余额操作记录
                            AtomReqSmsBalanceOperateRecordDTO atomReqSmsBalanceOperateRecordDTO = BeanCopierUtil.copy(smsSendRecordDomain, AtomReqSmsBalanceOperateRecordDTO.class);
                            atomReqSmsBalanceOperateRecordDTO.setRecordNo(MarketFormGenerator.smsBalanceOperateRecordNo());
                            atomReqSmsBalanceOperateRecordDTO.setOperateType(SmsBalanceOperateTypeEnum.SET_MEAL.getCode());
                            atomReqSmsBalanceOperateRecordDTO.setChangeNum(smsSendRecordDomain.getSendFailNum());
                            atomReqSmsBalanceOperateRecordDTO.setBeforeChangeNum(smsBalance - smsSendRecordDomain.getSendFailNum());
                            atomReqSmsBalanceOperateRecordDTO.setAfterChangeNum(smsBalance);
                            atomReqSmsBalanceOperateRecordDTO.setChangeSource(SmsTrafficChangeSourceEnum.GROUP_MESSAGE.getCode());
                            atomReqSmsBalanceOperateRecordDTO.setChangeOrderNo(smsSendRecordDomain.getBatchNo());
                            atomSmsBalanceOperateRecordOperatService.addSmsBalanceOperateRecord(atomReqSmsBalanceOperateRecordDTO);
                        }
                    }

                    List<SmsSendRecordDomain> sendSonRecordDomainList = matchAndCount(smsSendSonRecordDomainList, reqMwMsgResultDTOList);
                    log.info("-------smsSend,批量更新子批次数据入参：{}", sendSonRecordDomainList);
                    smsSendRecordDao.batchUpdateSmsSendRecord(sendSonRecordDomainList);
                    //汇总结果，更新父批次数据
                    List<SmsSendRecordDomain> smsSendRecordDomainList = groupAndSum(sendSonRecordDomainList);
                    smsSendRecordDomainList.forEach(smsSendRecordDomain -> {
                        if(smsSendRecordDomain.getSendSuccessNum()==0){
                            smsSendRecordDomain.setSendStatus(SmsSendStatusEnum.SEND_FAILURE.getCode());
                        }else{
                            smsSendRecordDomain.setSendStatus(SmsSendStatusEnum.SEND_SUCCESS.getCode());
                        }
                    });
                    log.info("-------smsSend,批量更新父批次数据入参：{}", smsSendRecordDomainList);
                    smsSendRecordDao.batchUpdateSmsSendRecordByBatchNo(smsSendRecordDomainList);

                }
            }
        } catch (Exception e) {
            log.error("AcceptSmsConsumer接口报错msg：", e);
            Thread.currentThread().interrupt();
        }
    }

    private Integer getSmsSendResultStatus(String issuccess) {
        Integer res;
        //短信发送状态，与msg_status对应， 0成功，1：异常
        switch (issuccess) {
            case "0":
                res = SmsSendResultStatusEnum.SEND_SUCCESS.getCode();
                break;
            case "1":
                res = SmsSendResultStatusEnum.SEND_FAILURE.getCode();
                break;
            default:
                res = SmsSendResultStatusEnum.IN_SENDING.getCode();
                break;
        }
        return res;
    }


    /**
     * 匹配短信发送记录，计算成功失败条数
     *
     * @param smsList
     * @param mwList
     * @return
     */
    private static List<SmsSendRecordDomain> matchAndCount(List<SmsSendRecordDomain> smsList, List<ReqMwMsgResultDTO> mwList) {
        Map<String, Integer> successMap = new HashMap<>();
        Map<String, Integer> failMap = new HashMap<>();

        //去除批次号和手机号重复的数据（有成功的保留成功的数据）
        Map<String, ReqMwMsgResultDTO> resultMap = new HashMap<>();
        for (ReqMwMsgResultDTO dto : mwList) {
            String key = dto.getMsgId() + dto.getMobileNum();
            if (resultMap.containsKey(key)) {
                ReqMwMsgResultDTO existingDto = resultMap.get(key);
                if (dto.getIsSuccess() != existingDto.getIsSuccess()) {
                    if (SmsSendResultStatusEnum.SEND_SUCCESS.getCode().equals(dto.getIsSuccess())) {
                        resultMap.put(key, dto);
                    }
                }
            } else {
                resultMap.put(key, dto);
            }
        }
        List<ReqMwMsgResultDTO> reqMwMsgResultDTOList = new ArrayList<>(resultMap.values());
        // 记录成功和失败的数量
        for (ReqMwMsgResultDTO mw : reqMwMsgResultDTOList) {
            String sonBatchNo = mw.getMsgId();
            //短信发送状态，与msg_status对应， 0成功，1：异常
            if ("0".equals(mw.getIsSuccess())) {
                successMap.put(sonBatchNo, successMap.getOrDefault(sonBatchNo, 0) + 1);
            } else if ("1".equals(mw.getIsSuccess())) {
                failMap.put(sonBatchNo, failMap.getOrDefault(sonBatchNo, 0) + 1);
            }
        }

        // 更新短信发送记录
        for (SmsSendRecordDomain sms : smsList) {
            String sonBatchNo = sms.getSonBatchNo();
            if (failMap.containsKey(sonBatchNo)) {
                sms.setSendFailNum(failMap.get(sonBatchNo) * sms.getSmsSingleNum());
                sms.setSendSuccessNum(0);
                sms.setSendStatus(SmsSendStatusEnum.SEND_FAILURE.getCode());
            }
            if (successMap.containsKey(sonBatchNo)) {
                sms.setSendSuccessNum(successMap.get(sonBatchNo) * sms.getSmsSingleNum());
                sms.setSendFailNum(0);
                sms.setSendStatus(SmsSendStatusEnum.SEND_SUCCESS.getCode());
            }
        }
        return smsList;
    }


    /**
     * 根据父批次号分组求和成功失败条数
     *
     * @param records
     * @return
     */
    private static List<SmsSendRecordDomain> groupAndSum(List<SmsSendRecordDomain> records) {
        Map<String, SmsSendRecordDomain> resultMap = new HashMap<>();
        for (SmsSendRecordDomain record : records) {
            String batchNo = record.getBatchNo();
            SmsSendRecordDomain result = resultMap.get(batchNo);
            if (result == null) {
                result = new SmsSendRecordDomain();
                result.setBatchNo(batchNo);
                result.setSendSuccessNum(0); // 初始化计数器
                result.setSendFailNum(0); // 初始化计数器
                resultMap.put(batchNo, result);
            }
            result.setStoreNo(record.getStoreNo());
            result.setStoreName(record.getStoreName());
            result.setSendSuccessNum(result.getSendSuccessNum() + record.getSendSuccessNum());
            result.setSendFailNum(result.getSendFailNum() + record.getSendFailNum());
        }
        return new ArrayList<>(resultMap.values());
    }

    /*public static void main(String[] args) {
        List<SmsSendRecordDomain> smsList = new ArrayList<>();
        SmsSendRecordDomain smsSendRecordDomain = new SmsSendRecordDomain();
        smsSendRecordDomain.setBatchNo("111");smsSendRecordDomain.setSendFailNum(2);smsSendRecordDomain.setSendSuccessNum(2);smsSendRecordDomain.setStoreNo("321");
        smsList.add(smsSendRecordDomain);
        List<ReqMwMsgResultDTO> mwList= new ArrayList<>();
        ReqMwMsgResultDTO reqMwMsgResultDTO = new ReqMwMsgResultDTO();
        reqMwMsgResultDTO.setIsSuccess("1");reqMwMsgResultDTO.setMsgId("111");
        mwList.add(reqMwMsgResultDTO);
        System.out.println(groupAndSum(matchAndCount(smsList, mwList)));

    }*/
}
