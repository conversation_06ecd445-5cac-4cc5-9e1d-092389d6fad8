package cn.htdt.marketprocess.biz.reactor;

import cn.htdt.marketprocess.biz.reactor.inft.EventProcess;
import lombok.extern.slf4j.Slf4j;
import reactor.event.Event;
import reactor.function.Consumer;

/**
 * <AUTHOR>
 * @Date 2020-09-16
 * @Description
 **/
@Slf4j
public class ConsumerAdapter implements Consumer<Event<?>> {

    private EventProcess eventProcess;

    ConsumerAdapter(EventProcess eventProcess) {
        this.eventProcess = eventProcess;
    }

    @Override
    public void accept(Event<?> t) {
        try {
            this.eventProcess.process(t.getData());
        } catch (Exception e) {
            log.error("******ConsumerAdapter-accept-", e);
        }
    }
}
