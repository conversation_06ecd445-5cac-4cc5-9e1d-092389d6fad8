package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.base.exception.BaseException;
import cn.htdt.common.dto.enums.MarketErrorCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.dto.utils.ExecuteDTOUtil;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.market.CardPeriodValidityEnum;
import cn.htdt.common.enums.market.CardStatusEnum;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketprocess.api.analysis.CardCountingFansAnalysisService;
import cn.htdt.marketprocess.biz.conversion.CardCountingFansAssert;
import cn.htdt.marketprocess.dao.CardCountingConfigDao;
import cn.htdt.marketprocess.dao.CardCountingFansDao;
import cn.htdt.marketprocess.dao.CardCountingFansUseRecordDao;
import cn.htdt.marketprocess.domain.CardCountingFansUseRecordDomain;
import cn.htdt.marketprocess.dto.request.ReqCardCountingFansDTO;
import cn.htdt.marketprocess.dto.request.ReqCardCountingFansUseRecordDTO;
import cn.htdt.marketprocess.dto.response.ResCardCountingFansCheckMsgDTO;
import cn.htdt.marketprocess.dto.response.ResCardCountingFansDTO;
import cn.htdt.marketprocess.dto.response.ResCardCountingFansUseRecordDTO;
import cn.htdt.marketprocess.dto.response.ResCardCountingStoreDTO;
import cn.htdt.marketprocess.vo.CardCountingConfigVO;
import cn.htdt.marketprocess.vo.CardCountingFansVO;
import cn.htdt.marketprocess.vo.CardCountingStoreVO;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 计次卡粉丝表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-13
 */
@Slf4j
@DubboService
public class CardCountingFansAnalysisServiceImpl implements CardCountingFansAnalysisService {

    @Resource
    private CardCountingFansDao cardCountingFansDao;

    @Resource
    private CardCountingConfigDao cardCountingConfigDao;

    @Resource
    private CardCountingFansUseRecordDao cardCountingFansUseRecordDao;

    @Autowired
    private CardCountingFansAssert cardCountingFansAssert;

    @Override
    public ExecuteDTO<ResCardCountingFansCheckMsgDTO> checkCardCountingFans(ReqCardCountingFansDTO reqCardCountingFansDTO) throws BaseException {
        log.info("-CardCountingFansAnalysisServiceImpl-checkCardCountingFans-param={}", JSON.toJSONString(reqCardCountingFansDTO));
        // 参数校验
        cardCountingFansAssert.checkCardCountingFansAssert(reqCardCountingFansDTO);
        ResCardCountingFansCheckMsgDTO resCardCountingFansCheckMsgDTO = new ResCardCountingFansCheckMsgDTO();
        // 判断活动是否存在
        CardCountingFansVO cardCountingFansVO = new CardCountingFansVO();
        cardCountingFansVO.setOrderNo(reqCardCountingFansDTO.getOrderNo());
        cardCountingFansVO.setFanNo(reqCardCountingFansDTO.getFanNo());
        cardCountingFansVO.setPromotionNo(reqCardCountingFansDTO.getPromotionNo());
        cardCountingFansVO.setCardCountingNo(reqCardCountingFansDTO.getCardCountingNo());
        CardCountingFansVO cardFansVO = this.cardCountingFansDao.selectOneByParam(cardCountingFansVO);
        log.info("-CardCountingFansAnalysisServiceImpl-checkCardCountingFans-活动信息={}", JSON.toJSONString(cardFansVO));
        if (cardFansVO == null) {
            resCardCountingFansCheckMsgDTO.setCheckFlag(Boolean.FALSE);
            resCardCountingFansCheckMsgDTO.setFailCode(MarketErrorCode.CODE_17001207.getCode());
            resCardCountingFansCheckMsgDTO.setFailMsg(MarketErrorCode.CODE_17001207.getInMsg());
        } else {
            int cardCounts = cardFansVO.getCardCounts()==null? NumConstant.ZERO: cardFansVO.getCardCounts();
            int cardRemainCounts = cardFansVO.getCardRemainCounts()==null? NumConstant.ZERO: cardFansVO.getCardRemainCounts();
            // 计次卡次数已全部被使用，是否确认退款？
            if(cardCounts != NumConstant.ZERO && cardRemainCounts == NumConstant.ZERO) {
                resCardCountingFansCheckMsgDTO.setCheckFlag(Boolean.FALSE);
                resCardCountingFansCheckMsgDTO.setFailCode(MarketErrorCode.CODE_17001208.getCode());
                resCardCountingFansCheckMsgDTO.setFailMsg(MarketErrorCode.CODE_17001208.getInMsg());
            }
            // 计次卡已使用%s次，是否确认退款
            if(cardCounts != NumConstant.ZERO && cardRemainCounts != NumConstant.ZERO && cardCounts != cardRemainCounts) {
                resCardCountingFansCheckMsgDTO.setCheckFlag(Boolean.FALSE);
                resCardCountingFansCheckMsgDTO.setFailCode(MarketErrorCode.CODE_17001209.getCode());
                resCardCountingFansCheckMsgDTO.setFailMsg(String.format(MarketErrorCode.CODE_17001209.getInMsg(), (cardCounts-cardRemainCounts)));
            }
        }
        return ExecuteDTOUtil.success(resCardCountingFansCheckMsgDTO);
    }

    @Override
    public ExecuteDTO<ExecutePageDTO<ResCardCountingFansDTO>> getCardCountingFansPage(ReqCardCountingFansDTO reqCardCountingFansDTO) {
        log.info("MarketProcess-CardCountingFansAnalysisServiceImpl-getCardCountingFansPage-params-start");
        log.info("MarketProcess-CardCountingFansAnalysisServiceImpl-getCardCountingFansPage-params={}", JSON.toJSONString(reqCardCountingFansDTO));
        Page<Object> pages = PageHelper.startPage(reqCardCountingFansDTO);
        CardCountingFansVO cardCountingFansVO = BeanCopierUtil.copy(reqCardCountingFansDTO, CardCountingFansVO.class);
        List<CardCountingFansVO> fansVOS = cardCountingFansDao.selectByParam(cardCountingFansVO);
        log.info("MarketProcess-CardCountingFansAnalysisServiceImpl-getCardCountingFansPage-return={}", JSON.toJSONString(fansVOS));
        log.info("MarketProcess-CardCountingFansAnalysisServiceImpl-getCardCountingFansPage-end");
        //参数转换
        ExecutePageDTO<ResCardCountingFansDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResCardCountingFansDTO> resCardCountingFansDTOS = BeanCopierUtil.copyList(fansVOS, ResCardCountingFansDTO.class);
        // 设置计次卡已过期状态
        this.setCardCountingFansStatus(resCardCountingFansDTOS);
        executePageDTO.setRows(resCardCountingFansDTOS);
        executePageDTO.setTotal(pages.getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    // 计次卡状态 1000可使用 1001已使用 1002已过期 1003已失效
    private void setCardCountingFansStatus(List<ResCardCountingFansDTO> resCardCountingFansDTOS) {
        if (CollectionUtils.isNotEmpty(resCardCountingFansDTOS)) {
            resCardCountingFansDTOS.forEach(fansDto -> {
                // 活动有效期（1001：长期有效 1002：指定日期 1003:自领取后天数有效）
                if (CardPeriodValidityEnum.CARD_PERIOD_VALIDITY_TWO.getCode().equals(fansDto.getCardPeriodValidity())
                    || CardPeriodValidityEnum.CARD_PERIOD_VALIDITY_THREE.getCode().equals(fansDto.getCardPeriodValidity())) {
                    //1002已过期
                    if (fansDto.getCardInvalidTime().isBefore(LocalDateTime.now())) {
                        fansDto.setCardStatus(CardStatusEnum.CARD_STATUS_TWO.getCode());
                    }
                }
            });
        }
    }

    @Override
    public ExecuteDTO<ResCardCountingFansDTO> getCardCountingFansInfo(ReqCardCountingFansDTO reqCardCountingFansDTO) {
        log.info("MarketProcess-CardCountingFansAnalysisServiceImpl-getCardCountingFansInfo-params-start");
        log.info("MarketProcess-CardCountingFansAnalysisServiceImpl-getCardCountingFansInfo-params={}", JSON.toJSONString(reqCardCountingFansDTO));
        CardCountingFansVO cardCountingFansVO = BeanCopierUtil.copy(reqCardCountingFansDTO, CardCountingFansVO.class);
        CardCountingFansVO fansVO = cardCountingFansDao.selectOneByParam(cardCountingFansVO);
        log.info("MarketProcess-CardCountingFansAnalysisServiceImpl-getCardCountingFansInfo-return={}", JSON.toJSONString(fansVO));
        log.info("MarketProcess-CardCountingFansAnalysisServiceImpl-getCardCountingFansInfo-end");
        //参数转换
        ResCardCountingFansDTO resCardCountingFansDTO = BeanCopierUtil.copy(fansVO, ResCardCountingFansDTO.class);
        if(resCardCountingFansDTO != null) {
            // 活动有效期（1001：长期有效 1002：指定日期 1003:自领取后天数有效）
            if (CardPeriodValidityEnum.CARD_PERIOD_VALIDITY_TWO.getCode().equals(resCardCountingFansDTO.getCardPeriodValidity())
                    || CardPeriodValidityEnum.CARD_PERIOD_VALIDITY_THREE.getCode().equals(resCardCountingFansDTO.getCardPeriodValidity())) {
                //1002已过期
                if (resCardCountingFansDTO.getCardInvalidTime().isBefore(LocalDateTime.now())) {
                    resCardCountingFansDTO.setCardStatus(CardStatusEnum.CARD_STATUS_TWO.getCode());
                }
            }
        }
        return ExecuteDTO.success(resCardCountingFansDTO);
    }

    @Override
    public ExecuteDTO<ResCardCountingFansDTO> getCardCountingFansInfoByOrder(ReqCardCountingFansDTO reqCardCountingFansDTO) {
        log.info("MarketProcess-CardCountingFansAnalysisServiceImpl-getCardCountingFansInfoByOrder-params-start");
        log.info("MarketProcess-CardCountingFansAnalysisServiceImpl-getCardCountingFansInfoByOrder-params={}", JSON.toJSONString(reqCardCountingFansDTO));
        CardCountingFansVO cardCountingFansVO = BeanCopierUtil.copy(reqCardCountingFansDTO, CardCountingFansVO.class);
        CardCountingFansVO fansVO = cardCountingFansDao.selectOneCardByParam(cardCountingFansVO);
        log.info("MarketProcess-CardCountingFansAnalysisServiceImpl-getCardCountingFansInfoByOrder-return={}", JSON.toJSONString(fansVO));
        log.info("MarketProcess-CardCountingFansAnalysisServiceImpl-getCardCountingFansInfoByOrder-end");
        //参数转换
        ResCardCountingFansDTO resCardCountingFansDTO = BeanCopierUtil.copy(fansVO, ResCardCountingFansDTO.class);
        if(resCardCountingFansDTO != null) {
            // 活动有效期（1001：长期有效 1002：指定日期 1003:自领取后天数有效）
            if (CardPeriodValidityEnum.CARD_PERIOD_VALIDITY_TWO.getCode().equals(resCardCountingFansDTO.getCardPeriodValidity())
                    || CardPeriodValidityEnum.CARD_PERIOD_VALIDITY_THREE.getCode().equals(resCardCountingFansDTO.getCardPeriodValidity())) {
                //1002已过期
                if (resCardCountingFansDTO.getCardInvalidTime().isBefore(LocalDateTime.now())) {
                    resCardCountingFansDTO.setCardStatus(CardStatusEnum.CARD_STATUS_TWO.getCode());
                }
            }
        }
        return ExecuteDTO.success(resCardCountingFansDTO);
    }

    @Override
    public ExecuteDTO<ExecutePageDTO<ResCardCountingFansUseRecordDTO>> getCardCountingFansUseRecordPage(ReqCardCountingFansUseRecordDTO reqCardCountingFansUseRecordDTO) {
        log.info("MarketProcess-CardCountingFansAnalysisServiceImpl-getCardCountingFansUseRecordPage-params-start");
        log.info("MarketProcess-CardCountingFansAnalysisServiceImpl-getCardCountingFansUseRecordPage-params={}", JSON.toJSONString(reqCardCountingFansUseRecordDTO));
        Page<Object> pages = PageHelper.startPage(reqCardCountingFansUseRecordDTO);
        CardCountingFansUseRecordDomain cardCountingFansUseRecordDomain = BeanCopierUtil.copy(reqCardCountingFansUseRecordDTO, CardCountingFansUseRecordDomain.class);
        List<CardCountingFansUseRecordDomain> domains = cardCountingFansUseRecordDao.selectByParam(cardCountingFansUseRecordDomain);
        log.info("MarketProcess-CardCountingFansAnalysisServiceImpl-getCardCountingFansUseRecordPage-return={}", JSON.toJSONString(domains));
        log.info("MarketProcess-CardCountingFansAnalysisServiceImpl-getCardCountingFansUseRecordPage-end");
        //参数转换
        ExecutePageDTO<ResCardCountingFansUseRecordDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResCardCountingFansUseRecordDTO> resCardCountingFansUseRecordDTOS = BeanCopierUtil.copyList(domains, ResCardCountingFansUseRecordDTO.class);
        executePageDTO.setRows(resCardCountingFansUseRecordDTOS);
        executePageDTO.setTotal(pages.getTotal());
        return ExecuteDTOUtil.success(executePageDTO);
    }

    @Override
    public ExecuteDTO<List<ResCardCountingFansUseRecordDTO>> getCardCountingFansUseRecord(ReqCardCountingFansUseRecordDTO reqCardCountingFansUseRecordDTO) {
        log.info("MarketProcess-CardCountingFansAnalysisServiceImpl-getCardCountingFansUseRecord-params-start");
        log.info("MarketProcess-CardCountingFansAnalysisServiceImpl-getCardCountingFansUseRecord-params={}", JSON.toJSONString(reqCardCountingFansUseRecordDTO));
        CardCountingFansUseRecordDomain cardCountingFansUseRecordDomain = BeanCopierUtil.copy(reqCardCountingFansUseRecordDTO, CardCountingFansUseRecordDomain.class);
        List<CardCountingFansUseRecordDomain> domains = cardCountingFansUseRecordDao.selectByParam(cardCountingFansUseRecordDomain);
        log.info("MarketProcess-CardCountingFansAnalysisServiceImpl-getCardCountingFansUseRecord-return={}", JSON.toJSONString(domains));
        log.info("MarketProcess-CardCountingFansAnalysisServiceImpl-getCardCountingFansUseRecord-end");
        //参数转换
        List<ResCardCountingFansUseRecordDTO> resCardCountingFansUseRecordDTOS = BeanCopierUtil.copyList(domains, ResCardCountingFansUseRecordDTO.class);
        return ExecuteDTOUtil.success(resCardCountingFansUseRecordDTOS);
    }

    @Override
    public ExecuteDTO<Integer> getCardCountingFansCount(ReqCardCountingFansDTO reqCardCountingFansDTO) {
        CardCountingFansVO cardCountingFansVO = BeanCopierUtil.copy(reqCardCountingFansDTO, CardCountingFansVO.class);
        Integer count = cardCountingFansDao.selectCardCountingFansCount(cardCountingFansVO);
        return ExecuteDTOUtil.success(Optional.ofNullable(count).orElse(NumConstant.ZERO));
    }

    @Override
    public ExecuteDTO<List<ResCardCountingStoreDTO>> getStoreListByFanNo(ReqCardCountingFansDTO reqCardCountingFansDTO) {
        List<CardCountingStoreVO> cardCountingStoreVOS = cardCountingFansDao.selectStoreListByFanNo(reqCardCountingFansDTO.getFanNo(), reqCardCountingFansDTO.getMerchantNo());
        return ExecuteDTOUtil.success(BeanCopierUtil.copyList(cardCountingStoreVOS, ResCardCountingStoreDTO.class));
    }

}
