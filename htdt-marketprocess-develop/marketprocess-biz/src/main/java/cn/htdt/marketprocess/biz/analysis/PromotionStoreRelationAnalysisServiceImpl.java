package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.enums.MarketErrorCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.constants.PromotionKeyConstant;
import cn.htdt.common.enums.market.StoreTypeEnum;
import cn.htdt.common.enums.market.StoreUseEnum;
import cn.htdt.common.redis.utils.RedisUtil;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.CollectionUtils;
import cn.htdt.common.utils.ListUtil;
import cn.htdt.marketcenter.dto.request.AtomReqPromotionStoreRelationDTO;
import cn.htdt.marketcenter.dto.response.AtomResPromotionStoreRelationDTO;
import cn.htdt.marketprocess.api.analysis.PromotionStoreRelationAnalysisService;
import cn.htdt.marketprocess.biz.conversion.PromotionStoreRelationAssert;
import cn.htdt.marketprocess.dao.PromotionStoreRelationDao;
import cn.htdt.marketprocess.domain.PromotionStoreRelationDomain;
import cn.htdt.marketprocess.dto.request.ReqPromotionStoreRelationDTO;
import cn.htdt.marketprocess.dto.response.ResPromotionStoreRelationDTO;
import cn.htdt.marketprocess.dto.response.ResPromotionStoreRelationStatisticsDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomPromotionStoreRelationAnalysisService;
import cn.htdt.marketprocess.vo.PromotionStoreRelationVo;
import cn.htdt.userprocess.api.StoreProcessService;
import cn.htdt.userprocess.dto.request.MerchantStoreRequest;
import cn.htdt.userprocess.dto.response.MerchantStoreResponse;
import cn.htdt.userprocess.dto.response.StoreInfoResponse;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;

import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import com.cronutils.utils.StringUtils;
import com.google.common.collect.Lists;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;


/**
 * 活动店铺规则查询服务
 * <AUTHOR>
 * @date 2023-06-05
 */
@Slf4j
@DubboService
public class PromotionStoreRelationAnalysisServiceImpl implements PromotionStoreRelationAnalysisService {

    @Resource
    private AtomPromotionStoreRelationAnalysisService promotionStoreRelationAnalysisService;
    
    @DubboReference
    private StoreProcessService storeProcessService;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private PromotionStoreRelationAssert promotionStoreRelationAssert;

    @Resource
    private PromotionStoreRelationDao promotionStoreRelationDao;

    /**
     * 统计商家社群接龙分发店铺信息
     * <AUTHOR>
     * @date 2023-06-05
     */
	@Override
	public ExecuteDTO<ResPromotionStoreRelationStatisticsDTO> getPromotionStoreInfo(ReqPromotionStoreRelationDTO reqPromotionStoreRelationDTO) {
	 	AtomReqPromotionStoreRelationDTO promotionStoreRelationDTO = new AtomReqPromotionStoreRelationDTO();
	 	promotionStoreRelationDTO.setPromotionNo(reqPromotionStoreRelationDTO.getPromotionNo());
        promotionStoreRelationDTO.setMerchantNo(reqPromotionStoreRelationDTO.getMerchantNo());
        promotionStoreRelationDTO.setDisableFlag(StoreUseEnum.ENABLE.getCode());
        ExecuteDTO<List<AtomResPromotionStoreRelationDTO>> resPromotionStoreRelationDTO = promotionStoreRelationAnalysisService.getStoreRelationList(promotionStoreRelationDTO);
        if (null == resPromotionStoreRelationDTO) {
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        }
        if (!resPromotionStoreRelationDTO.successFlag()) {
            return new ExecuteDTO<>(resPromotionStoreRelationDTO.getStatus(), resPromotionStoreRelationDTO.getMsg(), null);
        }
        //全部店铺,先获取缓存,再查询接口
        String cacheKey = String.format(PromotionKeyConstant.PROMOTION_MERCHANT_ALL_STORE_PREFIX, reqPromotionStoreRelationDTO.getMerchantNo());
        List<MerchantStoreResponse> merchantStoreList = null;
        if(redisUtil.get(cacheKey) != null){
            merchantStoreList = JSON.parseArray((String)redisUtil.get(cacheKey), MerchantStoreResponse.class);
        } else {
            MerchantStoreRequest merchantStoreRequest = new MerchantStoreRequest();
            merchantStoreRequest.setMerchantNo(reqPromotionStoreRelationDTO.getMerchantNo());
            ExecuteDTO<List<MerchantStoreResponse>> storeResponseExecuteDTO = storeProcessService.queryMerchantStore(merchantStoreRequest);
            if (null == storeResponseExecuteDTO) {
                return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
            }
            if (!storeResponseExecuteDTO.successFlag()) {
                return new ExecuteDTO<>(storeResponseExecuteDTO.getStatus(), storeResponseExecuteDTO.getMsg(), null);
            }
            merchantStoreList = storeResponseExecuteDTO.getData();
            redisUtil.set(cacheKey, JSON.toJSONString(merchantStoreList), NumConstant.THIRTY);
        }
		if(CollectionUtils.isEmpty(merchantStoreList)) {
			return new ExecuteDTO<>(MarketErrorCode.CODE_17000636.getCode(), MarketErrorCode.CODE_17000636.getInMsg(), null);
		}
		//
        ResPromotionStoreRelationStatisticsDTO statistics = new ResPromotionStoreRelationStatisticsDTO();
        List<AtomResPromotionStoreRelationDTO> dataList = resPromotionStoreRelationDTO.getData();
        if(CollectionUtils.isNotEmpty(dataList)) {
        	dataList.forEach(storeRelation -> {
            	if(StringUtils.isEmpty(storeRelation.getStoreNo())) {
            		statistics.setStoreType(storeRelation.getStoreType());
            	}
            });
        	if(StoreTypeEnum.STORE_TYPE_THREE.getCode().equals(statistics.getStoreType())) {
        		//指定店铺
        		List<String> storeNameList = Lists.newArrayList();
        		Map<String, MerchantStoreResponse> merchantStoreResponseMap = merchantStoreList.stream().collect(Collectors.toMap(MerchantStoreResponse::getStoreNo, store -> store));
        		dataList.forEach(storeRelation -> {
                    if(!StringUtils.isEmpty(storeRelation.getStoreNo())
                            && storeRelation.getDisableFlag().equals(NumConstant.ONE)) {
                        MerchantStoreResponse merchantStoreResponse = merchantStoreResponseMap.get(storeRelation.getStoreNo());
                        storeNameList.add(merchantStoreResponse.getStoreName());
                    }
        		});
        		statistics.setStoreNameList(storeNameList);
                statistics.setStoreNum(storeNameList.size());
        	} else if(StoreTypeEnum.STORE_TYPE_ONE.getCode().equals(statistics.getStoreType())){
				//全部店铺
    			statistics.setStoreNum(merchantStoreList.size());
    			statistics.setStoreNameList(merchantStoreList.stream().map(MerchantStoreResponse::getStoreName).collect(Collectors.toList()));
        	}
        }
        return ExecuteDTO.ok(statistics);
	}

    /**
     * 查询商家社群接龙分发店铺列表
     * <AUTHOR>
     * @date 2023-06-07
     */
    @Override
    public ExecuteDTO<List<ResPromotionStoreRelationDTO>> getStoreRelationList(ReqPromotionStoreRelationDTO reqPromotionStoreRelationDTO) {
        AtomReqPromotionStoreRelationDTO promotionStoreRelationDTO = new AtomReqPromotionStoreRelationDTO();
        promotionStoreRelationDTO.setPromotionNo(reqPromotionStoreRelationDTO.getPromotionNo());
        promotionStoreRelationDTO.setMerchantNo(reqPromotionStoreRelationDTO.getMerchantNo());
        ExecuteDTO<List<AtomResPromotionStoreRelationDTO>> resPromotionStoreRelationDTO = promotionStoreRelationAnalysisService.getStoreRelationList(promotionStoreRelationDTO);
        if (null == resPromotionStoreRelationDTO) {
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        }
        if (!resPromotionStoreRelationDTO.successFlag()) {
            return new ExecuteDTO<>(resPromotionStoreRelationDTO.getStatus(), resPromotionStoreRelationDTO.getMsg(), null);
        }
        return ExecuteDTO.ok(BeanCopierUtil.copyList(resPromotionStoreRelationDTO.getData(), ResPromotionStoreRelationDTO.class));
    }

    /**
     * 蛋品, 查询商家抽奖活动, 可以参加活动的店铺信息
     *
     * @param reqPromotionStoreRelationDTO 查询参数
     * <AUTHOR>
     */
    @Override
    public ExecuteDTO<List<ResPromotionStoreRelationDTO>> getMerchantLotterySuitStoreList(ReqPromotionStoreRelationDTO reqPromotionStoreRelationDTO) {
        promotionStoreRelationAssert.getMerchantLotteryStoreRelationListAssert(reqPromotionStoreRelationDTO);

        PromotionStoreRelationVo promotionStoreRelationVo = new PromotionStoreRelationVo();
        promotionStoreRelationVo.setPromotionNo(reqPromotionStoreRelationDTO.getPromotionNo());
        promotionStoreRelationVo.setMerchantNo(reqPromotionStoreRelationDTO.getMerchantNo());
        List<PromotionStoreRelationDomain> merchantLotteryStoreRelationList = promotionStoreRelationDao.selectMerchantLotteryStoreRelationList(promotionStoreRelationVo);
        if (CollectionUtils.isEmpty(merchantLotteryStoreRelationList)) {
            log.error("getMerchantLotterySuitStoreList, result is null, 入参: {}", JSON.toJSONString(reqPromotionStoreRelationDTO));
            return ExecuteDTO.error(MarketErrorCode.CODE_17000600);
        }

        PromotionStoreRelationDomain merchantLotteryStoreRelationDomain = ListUtil.getFirst(merchantLotteryStoreRelationList);

        // 全部店铺可参与的话, 返回空集合
        if (StoreTypeEnum.STORE_TYPE_ONE.getCode().equals(merchantLotteryStoreRelationDomain.getStoreType())) {
            return ExecuteDTO.ok(Collections.emptyList());
        } else {
            // 指定店铺参与的话, 查询可以参加活动的店铺编号和名称
            promotionStoreRelationVo = new PromotionStoreRelationVo();
            promotionStoreRelationVo.setPromotionNo(merchantLotteryStoreRelationDomain.getPromotionNo());
            List<PromotionStoreRelationDomain> suitStoreList = promotionStoreRelationDao.selectMerchantLotteryStoreRelationList(promotionStoreRelationVo);
            if (CollectionUtils.isEmpty(suitStoreList)) {
                log.error("getMerchantLotterySuitStoreList, suitStoreList is empty, 入参: {}", JSON.toJSONString(reqPromotionStoreRelationDTO));
                return ExecuteDTO.error(MarketErrorCode.CODE_17000600);
            }

            List<ResPromotionStoreRelationDTO> merchantLotterySuitStoreList = BeanCopierUtil.copyList(suitStoreList, ResPromotionStoreRelationDTO.class);

            // 查询并且设置店铺名称
            MerchantStoreRequest merchantStoreRequest = new MerchantStoreRequest();
            merchantStoreRequest.setMerchantNo(ListUtil.getFirst(suitStoreList).getMerchantNo());
            ExecuteDTO<List<StoreInfoResponse>> storeInfoListExecute = storeProcessService.getManyStoreSelect(merchantStoreRequest);
            if (CollectionUtils.isNotEmpty(storeInfoListExecute.getData())) {
                List<StoreInfoResponse> storeInfoList = storeInfoListExecute.getData();
                merchantLotterySuitStoreList.forEach(lotterySuitStore -> {
                    Optional<StoreInfoResponse> storeInfoOption
                            = storeInfoList
                            .stream()
                            .filter(storeInfoResponse ->
                                    org.apache.commons.lang3.StringUtils.equals(lotterySuitStore.getStoreNo(), storeInfoResponse.getStoreNo()))
                            .findFirst();

                    // 设置店铺名称
                    storeInfoOption.ifPresent(storeInfoResponse -> lotterySuitStore.setStoreName(storeInfoResponse.getStoreName()));
                });
            }
            return ExecuteDTO.ok(merchantLotterySuitStoreList);
        }
    }

}
