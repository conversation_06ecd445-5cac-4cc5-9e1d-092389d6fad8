package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketprocess.api.analysis.PromotionBrowsingRecordAnalysisService;
import cn.htdt.marketprocess.biz.conversion.PromotionBrowsingRecordAssert;
import cn.htdt.marketprocess.dao.PromotionBrowsingRecordDao;
import cn.htdt.marketprocess.domain.PromotionBrowsingRecordDomain;
import cn.htdt.marketprocess.dto.request.ReqPromotionInfoDTO;
import cn.htdt.marketprocess.dto.response.ResPromotionBrowsingRecordDTO;
import cn.htdt.marketprocess.vo.PromotionInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 * 活动浏览记录表
 *
 * <AUTHOR>
 */
@DubboService
@Slf4j
public class PromotionBrowsingRecordAnalysisServiceImpl implements PromotionBrowsingRecordAnalysisService {

    @Resource
    private PromotionBrowsingRecordDao promotionBrowsingRecordDao;

    @Resource
    private PromotionBrowsingRecordAssert promotionBrowsingRecordAssert;

    /**
     * 查询浏览促销活动的粉丝数
     *
     * @param reqPromotionInfoDTO reqPromotionInfoDTO
     * @return int
     */
    @Override
    public ExecuteDTO<Integer> selectBrowseFansCount(ReqPromotionInfoDTO reqPromotionInfoDTO) {
        PromotionInfoVO promotionInfoVO = BeanCopierUtil.copy(reqPromotionInfoDTO, PromotionInfoVO.class);
        int browseFansCount = promotionBrowsingRecordDao.selectBrowseFansCount(promotionInfoVO);
        return ExecuteDTO.ok(browseFansCount);
    }

    /**
     * 查询活动浏览次数
     *
     * @param reqPromotionInfoDTO
     * @return ExecuteDTO<List < ResPromotionBrowsingRecordDTO>>
     */
    @Override
    public ExecuteDTO<List<ResPromotionBrowsingRecordDTO>> selectPromotionBrowseRecordCount(ReqPromotionInfoDTO reqPromotionInfoDTO) {
        log.info("-------PromotionBrowsingRecordAnalysisServiceImpl-->selectPromotionBrowseRecordCount,查询活动浏览次数--start--入参：{}--", reqPromotionInfoDTO);
        promotionBrowsingRecordAssert.selectPromotionBrowseRecordCountAssert(reqPromotionInfoDTO);
        PromotionInfoVO promotionInfoVO = BeanCopierUtil.copy(reqPromotionInfoDTO, PromotionInfoVO.class);
        log.info("-------promotionBrowsingRecordDao.selectPromotionBrowseRecordCount----入参：{}--", promotionInfoVO);
        List<PromotionBrowsingRecordDomain> domainList = promotionBrowsingRecordDao.selectPromotionBrowseRecordCount(promotionInfoVO);

        return ExecuteDTO.success(BeanCopierUtil.copyList(domainList, ResPromotionBrowsingRecordDTO.class));
    }
}
