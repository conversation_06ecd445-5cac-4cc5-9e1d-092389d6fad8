package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.enums.GoodsErrorCode;
import cn.htdt.common.dto.enums.HxgLogoffErrorEnum;
import cn.htdt.common.dto.enums.MarketErrorCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.market.LotteryRewardTypeEnum;
import cn.htdt.common.enums.market.WriteOffStatusEnum;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.goodscenter.dto.request.AtomReqGoodsDTO;
import cn.htdt.goodscenter.dto.request.AtomReqWarehouseGoodsRelationDTO;
import cn.htdt.goodscenter.dto.response.AtomResGoodsDTO;
import cn.htdt.goodscenter.dto.response.AtomResGoodsWarehouseDTO;
import cn.htdt.goodsprocess.api.operat.LegacyGoodsCenterService;
import cn.htdt.marketcenter.dto.request.AtomReqLotteryDrawNumDTO;
import cn.htdt.marketcenter.dto.request.AtomReqLotteryDrawRecordDTO;
import cn.htdt.marketcenter.dto.request.AtomReqLotteryRewardGoodsRelationDTO;
import cn.htdt.marketcenter.dto.response.AtomResLotteryDrawNumDTO;
import cn.htdt.marketcenter.dto.response.AtomResLotteryDrawRecordCountVO;
import cn.htdt.marketcenter.dto.response.AtomResLotteryDrawRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomResLotteryRewardGoodsRelationDTO;
import cn.htdt.marketprocess.api.analysis.LotteryDrawRecordAnalysisService;
import cn.htdt.marketprocess.biz.conversion.LotteryDrawRecordAssert;
import cn.htdt.marketprocess.dto.request.ReqLotteryDrawNumDTO;
import cn.htdt.marketprocess.dto.request.ReqLotteryDrawRecordDTO;
import cn.htdt.marketprocess.dto.request.ReqWriteOffRecordDTO;
import cn.htdt.marketprocess.dto.response.ResLotteryDrawNumDTO;
import cn.htdt.marketprocess.dto.response.ResLotteryDrawRecordCountVO;
import cn.htdt.marketprocess.dto.response.ResLotteryDrawRecordDTO;
import cn.htdt.marketprocess.dto.response.ResWriteOffRecordDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomLotteryDrawRecordAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomPromotionRewardGoodsRelationAnalysisService;
import cn.htdt.orderprocess.api.LegacyOrderCenterService;
import cn.htdt.usercenter.dto.request.ReqFancDTO;
import cn.htdt.usercenter.dto.response.ResFancDTO;
import cn.htdt.userprocess.api.LegacyUserCenterService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@DubboService
public class LotteryDrawRecordAnalysisServiceImpl implements LotteryDrawRecordAnalysisService {


    @Resource
    private AtomLotteryDrawRecordAnalysisService atomLotteryDrawRecordAnalysisService;
    /**
     * 活动奖品商品关联
     */
    @Resource
    private AtomPromotionRewardGoodsRelationAnalysisService atomPromotionRewardGoodsRelationAnalysisService;

    @DubboReference
    private LegacyOrderCenterService legacyOrderCenterService;
    /**
     * 商品信息
     */
    @DubboReference
    private LegacyGoodsCenterService legacyGoodsCenterService;

    @DubboReference
    private LegacyUserCenterService legacyUserCenterService;
    @Autowired
    private LotteryDrawRecordAssert lotteryDrawRecordAssert;

    @Override
    public ExecuteDTO<ExecutePageDTO<ResLotteryDrawRecordDTO>> getLotteryDrawRecordPage(ReqLotteryDrawRecordDTO reqLotteryDrawRecordDTO) {
        AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO = BeanCopierUtil.copy(reqLotteryDrawRecordDTO, AtomReqLotteryDrawRecordDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResLotteryDrawRecordDTO>> executeDTO = atomLotteryDrawRecordAnalysisService.getLotteryDrawRecordPage(atomReqLotteryDrawRecordDTO);
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        //参数转换
        ExecutePageDTO<ResLotteryDrawRecordDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResLotteryDrawRecordDTO> resAgentRewardRecordDTOList = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResLotteryDrawRecordDTO.class);
        // 添加备注名
        if (StringUtils.isNotBlank(reqLotteryDrawRecordDTO.getStoreNo()) && CollectionUtils.isNotEmpty(resAgentRewardRecordDTOList)) {
            ReqFancDTO reqFancDTO = new ReqFancDTO();
            List<String> fansList = resAgentRewardRecordDTOList.stream().map(ResLotteryDrawRecordDTO::getFanNo).collect(Collectors.toList());
            reqFancDTO.setFansNoList(fansList);
            reqFancDTO.setStoreNo(reqLotteryDrawRecordDTO.getStoreNo());
            ExecuteDTO<List<ResFancDTO>> fanByFanNoList = legacyUserCenterService.getFansInfoByNos(reqFancDTO);
            if (!fanByFanNoList.successFlag()) {
                return ExecuteDTO.error(fanByFanNoList.getStatus(), fanByFanNoList.getMsg());
            }
            if (CollectionUtils.isNotEmpty(fanByFanNoList.getData())) {
                resAgentRewardRecordDTOList.forEach(recordDTO -> {
                    // 塞昵称
                    if (org.apache.dubbo.common.utils.CollectionUtils.isNotEmpty(fanByFanNoList.getData())) {
                        fanByFanNoList.getData().forEach(resFancDTO -> {
                            if (recordDTO.getFanNo().equals(resFancDTO.getFanNo())) {
                                recordDTO.setName(resFancDTO.getNickName());
                                recordDTO.setStoreFanName(resFancDTO.getStoreFanName());
                            }
                        });
                    }
                });
            }
        }
        if (CollectionUtils.isNotEmpty(resAgentRewardRecordDTOList)) {
            resAgentRewardRecordDTOList.forEach(lotteryDrawRecord->{
                lotteryDrawRecord.setRewardTypeName(LotteryRewardTypeEnum.getByCode(lotteryDrawRecord.getRewardType()).getType());
            });
        }

        executePageDTO.setRows(resAgentRewardRecordDTOList);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    @Override
    public ExecuteDTO<Integer> getLotteryDrawRecordCount(ReqLotteryDrawRecordDTO reqLotteryDrawRecordDTO) {
        log.info("-LotteryDrawRecordAnalysisServiceImpl-getLotteryDrawRecordCount-param={}", JSON.toJSONString(reqLotteryDrawRecordDTO));
        AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO = BeanCopierUtil.copy(reqLotteryDrawRecordDTO, AtomReqLotteryDrawRecordDTO.class);
        ExecuteDTO<Integer> executeDTO = atomLotteryDrawRecordAnalysisService.getLotteryDrawRecordCount(atomReqLotteryDrawRecordDTO);
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        log.info("-LotteryDrawRecordAnalysisServiceImpl-getLotteryDrawRecordCount-return={}", JSON.toJSONString(executeDTO));
        return ExecuteDTO.success(executeDTO.getData());
    }

    @Override
    public ExecuteDTO<List<ResLotteryDrawRecordCountVO>> getLotteryDrawRecordCountList(ReqLotteryDrawRecordDTO reqLotteryDrawRecordDTO) {
        log.info("-LotteryDrawRecordAnalysisServiceImpl-getLotteryDrawRecordCountList-param={}", JSON.toJSONString(reqLotteryDrawRecordDTO));
        if (reqLotteryDrawRecordDTO == null || StringUtils.isBlank(reqLotteryDrawRecordDTO.getPromotionNo())) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO = BeanCopierUtil.copy(reqLotteryDrawRecordDTO, AtomReqLotteryDrawRecordDTO.class);
        ExecuteDTO<List<AtomResLotteryDrawRecordCountVO>> executeDTO = atomLotteryDrawRecordAnalysisService.getLotteryDrawRecordCountList(atomReqLotteryDrawRecordDTO);
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        List<ResLotteryDrawRecordCountVO> lotteryDrawRecordDTOS = BeanCopierUtil.copyList(executeDTO.getData(), ResLotteryDrawRecordCountVO.class);
        if (CollectionUtils.isNotEmpty(lotteryDrawRecordDTOS)) {
            lotteryDrawRecordDTOS.forEach(lotteryDrawRecord->{
                lotteryDrawRecord.setRewardTypeName(LotteryRewardTypeEnum.getByCode(lotteryDrawRecord.getRewardType()).getType());
            });
        }
        log.info("-LotteryDrawRecordAnalysisServiceImpl-getLotteryDrawRecordCountList-return={}", JSON.toJSONString(executeDTO));
        return ExecuteDTO.success(lotteryDrawRecordDTOS);
    }

    @Override
    public ExecuteDTO<List<ResLotteryDrawRecordDTO>> listLotteryDrawRecord(ReqLotteryDrawRecordDTO reqLotteryDrawRecordDTO) {
        ExecuteDTO<List<AtomResLotteryDrawRecordDTO>> executeDTO = atomLotteryDrawRecordAnalysisService.listLotteryDrawRecord(BeanCopierUtil.copy(reqLotteryDrawRecordDTO, AtomReqLotteryDrawRecordDTO.class));
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        return ExecuteDTO.success(BeanCopierUtil.copyList(executeDTO.getData(), ResLotteryDrawRecordDTO.class));
    }

    /**
     * @param reqWriteOffRecordDTO
     * @Description : 抽奖活动中奖核销记录列表
     * <AUTHOR> 高繁
     * @date : 2021/3/22 15:17
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResWriteOffRecordDTO>> getWriteOffList(ReqWriteOffRecordDTO reqWriteOffRecordDTO) {
        AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO = BeanCopierUtil.copy(reqWriteOffRecordDTO, AtomReqLotteryDrawRecordDTO.class);
        if(StringUtils.isNotBlank(reqWriteOffRecordDTO.getRecordNoList())){
            atomReqLotteryDrawRecordDTO.setRecordNos(Arrays.asList(reqWriteOffRecordDTO.getRecordNoList().split(",")));
        }
        ExecuteDTO<ExecutePageDTO<AtomResLotteryDrawRecordDTO>> executeDTO = atomLotteryDrawRecordAnalysisService.getWriteOffList(atomReqLotteryDrawRecordDTO);
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        //参数转换
        ExecutePageDTO<ResWriteOffRecordDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResWriteOffRecordDTO> resWriteOffRecordDTOS = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResWriteOffRecordDTO.class);
        // 初始化返回数据
        ExecuteDTO executeDTO1 = this.setInitData(resWriteOffRecordDTOS);
        if (!executeDTO1.successFlag()) {
            return executeDTO1;
        }
        executePageDTO.setRows(resWriteOffRecordDTOS);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    private ExecuteDTO setInitData(List<ResWriteOffRecordDTO> resWriteOffRecordDTOS) {
        if (CollectionUtils.isNotEmpty(resWriteOffRecordDTOS)) {
            List<String> orderNos = new ArrayList<>();
            List<String> goodsNo = new ArrayList<>();
            resWriteOffRecordDTOS.forEach(resWriteOffRecordDTO -> {
                orderNos.add(resWriteOffRecordDTO.getOrderNo());
                goodsNo.add(resWriteOffRecordDTO.getGoodsNo());
            });
            //查询已签收订单
            resWriteOffRecordDTOS.forEach(resWriteOffRecordDTO -> {

                if (WriteOffStatusEnum.ALLOW_WRITE_OFF.getCode().equals(resWriteOffRecordDTO.getWriteOffStatus())) {
                    //待核销且可核销
                    resWriteOffRecordDTO.setWriteOffFlag(NumConstant.ONE);
                } else {
                    //
                    resWriteOffRecordDTO.setWriteOffFlag(NumConstant.TWO);
                }
            });

            // 批量查询商品仓库信息以及是否启用串码信息
            AtomReqWarehouseGoodsRelationDTO atomReqWarehouseGoodsRelationDTO = new AtomReqWarehouseGoodsRelationDTO();
            atomReqWarehouseGoodsRelationDTO.setGoodsNos(goodsNo);
            ExecuteDTO<List<AtomResGoodsWarehouseDTO>> goodsExecuteDTO = legacyGoodsCenterService.getWarehouseGoodsInfoByParams(atomReqWarehouseGoodsRelationDTO);
            if (!goodsExecuteDTO.successFlag()) {
                return ExecuteDTO.error(goodsExecuteDTO.getStatus(), goodsExecuteDTO.getMsg());
            }
            if (CollectionUtils.isNotEmpty(goodsExecuteDTO.getData())) {
                goodsExecuteDTO.getData().stream().forEach(atomResGoodsWarehouseDTO -> {
                    resWriteOffRecordDTOS.stream().filter(resGoodsDTO -> atomResGoodsWarehouseDTO.getGoodsNo().equals(resGoodsDTO.getGoodsNo())).forEach(s -> {
                        s.setImeiFlag(atomResGoodsWarehouseDTO.getImeiFlag());
                        s.setWarehouseFlag(atomResGoodsWarehouseDTO.getWarehouseFlag());
                        // 过滤掉虚拟仓
                        s.setGoodsWarehouseList(BeanCopierUtil.copyList(atomResGoodsWarehouseDTO.getWarehouseList().stream()
                                .filter(atomResWarehouseGoodsRelationDTO -> WhetherEnum.NO.getCode().equals(atomResWarehouseGoodsRelationDTO.getVirtualWarehouseFlag()))
                                .collect(Collectors.toList()), ResWriteOffRecordDTO.GoodsWarehouse.class));
                    });
                });
            }
        }
        return ExecuteDTO.success();
    }

    /**
     * @param reqLotteryDrawRecordDTO
     * @Description : 根据订单编号查询中奖基本信息
     * <AUTHOR> 高繁
     * @date : 2021/3/26 15:05
     */
    @Override
    public ExecuteDTO<ResLotteryDrawRecordDTO> getLotteryDrawInfo(ReqLotteryDrawRecordDTO reqLotteryDrawRecordDTO) {
        return null;
    }

    /**
     * @param reqLotteryDrawNumDTO
     * @Description : 查询活动下中奖次数
     * <AUTHOR> 高繁
     * @date : 2021/4/9 15:27
     */
    @Override
    public ExecuteDTO<ResLotteryDrawNumDTO> getDrawNumByPromotion(ReqLotteryDrawNumDTO reqLotteryDrawNumDTO) {
        AtomReqLotteryDrawNumDTO atomReqLotteryDrawNumDTO = BeanCopierUtil.copy(reqLotteryDrawNumDTO, AtomReqLotteryDrawNumDTO.class);
        ExecuteDTO<AtomResLotteryDrawNumDTO> atomResLotteryDrawNumDTOExecuteDTO = atomLotteryDrawRecordAnalysisService.getDrawNumByPromotion(atomReqLotteryDrawNumDTO);
        return ExecuteDTO.success(BeanCopierUtil.copy(atomResLotteryDrawNumDTOExecuteDTO.getData(), ResLotteryDrawNumDTO.class));
    }

    /**
     * @Description 查询核销码在当前会员店是否存在
     * <AUTHOR>
     * @Date 2021/4/13 11:30
     * @Param reqLotteryDrawRecordDto
     * @Return ExecuteDTO
     */
    @Override
    public ExecuteDTO<ResWriteOffRecordDTO> getWriteOffCodeIsExist(ReqLotteryDrawRecordDTO reqLotteryDrawRecordDto) {
        log.info("-LotteryDrawRecordAnalysisServiceImpl-getWriteOffCodeIsExist-入参：{}", JSON.toJSONString(reqLotteryDrawRecordDto));
        lotteryDrawRecordAssert.getWriteOffCodeIsExistAssert(reqLotteryDrawRecordDto);
        AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDto = BeanCopierUtil.copy(reqLotteryDrawRecordDto, AtomReqLotteryDrawRecordDTO.class);
        ExecuteDTO<AtomResLotteryDrawRecordDTO> writeOffCodeIsExist = atomLotteryDrawRecordAnalysisService.getWriteOffCodeIsExist(atomReqLotteryDrawRecordDto);
        if (!writeOffCodeIsExist.successFlag()) {
            return ExecuteDTO.error(writeOffCodeIsExist.getStatus(), writeOffCodeIsExist.getMsg());
        }
        AtomResLotteryDrawRecordDTO atomResLotteryDrawRecordDTO = writeOffCodeIsExist.getData();
        if (atomResLotteryDrawRecordDTO == null) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000110);
        }
        if (atomResLotteryDrawRecordDTO.getWriteOffStatus() == NumConstant.TWO) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000111);
        }
        if (atomResLotteryDrawRecordDTO.getWriteOffStatus() == NumConstant.THREE) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000112);
        }
        ResWriteOffRecordDTO resWriteOffRecordDTO = BeanCopierUtil.copy(atomResLotteryDrawRecordDTO, ResWriteOffRecordDTO.class);
        log.info("-LotteryDrawRecordAnalysisServiceImpl-getWriteOffCodeIsExist-出参：{}", JSON.toJSONString(resWriteOffRecordDTO));
        if (resWriteOffRecordDTO != null) {
            List<ResWriteOffRecordDTO> resWriteOffRecordDTOS = new ArrayList<>();
            resWriteOffRecordDTOS.add(resWriteOffRecordDTO);
            // 初始化返回数据
            ExecuteDTO executeDTO1 = this.setInitData(resWriteOffRecordDTOS);
            if (!executeDTO1.successFlag()) {
                return executeDTO1;
            }
            resWriteOffRecordDTO = resWriteOffRecordDTOS.get(NumConstant.ZERO);
        }
        return ExecuteDTO.success(resWriteOffRecordDTO);
    }

    @Override
    public ExecuteDTO<Integer> checkGoodsToImei(ReqLotteryDrawRecordDTO reqLotteryDrawRecordDTO) {
        log.info("-LotteryDrawRecordAnalysisServiceImpl-checkGoodsToImei-入参：{}", JSON.toJSONString(reqLotteryDrawRecordDTO));
        lotteryDrawRecordAssert.checkGoodsToImeiAssert(reqLotteryDrawRecordDTO);
        // 获取中奖记录
        AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO = BeanCopierUtil.copy(reqLotteryDrawRecordDTO, AtomReqLotteryDrawRecordDTO.class);
        ExecuteDTO<AtomResLotteryDrawRecordDTO> recordDTOExecuteDTO = atomLotteryDrawRecordAnalysisService.getWriteOffCodeIsExist(atomReqLotteryDrawRecordDTO);
        if (!recordDTOExecuteDTO.successFlag()) {
            return ExecuteDTO.error(recordDTOExecuteDTO.getStatus(), recordDTOExecuteDTO.getMsg());
        }
        if (recordDTOExecuteDTO.getData() == null) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000114);
        }
        AtomResLotteryDrawRecordDTO atomResLotteryDrawRecordDTO = recordDTOExecuteDTO.getData();
        // 实物奖品
        if (LotteryRewardTypeEnum.REWARD_TYPE_ONE.getCode().equals(atomResLotteryDrawRecordDTO.getRewardType())) {
            // 获取中奖记录-实物奖品
            AtomReqLotteryRewardGoodsRelationDTO atomReqLotteryRewardGoodsRelationDTO = BeanCopierUtil.copy(reqLotteryDrawRecordDTO, AtomReqLotteryRewardGoodsRelationDTO.class);
            ExecuteDTO<AtomResLotteryRewardGoodsRelationDTO> executeDTO = atomPromotionRewardGoodsRelationAnalysisService.getLotteryRewardGoodsRelation(atomReqLotteryRewardGoodsRelationDTO);
            log.info("-LotteryDrawRecordAnalysisServiceImpl-checkGoodsToImei-出参：{}", JSON.toJSONString(executeDTO));
            if (!executeDTO.successFlag()) {
                return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
            }
            if (executeDTO.getData() != null) {
                //            return ExecuteDTO.error(MarketErrorCode.CODE_17000114, "该中奖记录不存在");
                // 获取商品信息
                AtomReqGoodsDTO atomReqGoodsDTO = new AtomReqGoodsDTO();
                atomReqGoodsDTO.setGoodsNo(executeDTO.getData().getGoodsNo());
                log.info("-LotteryDrawRecordAnalysisServiceImpl-checkGoodsToImei-商品-入参：{}", JSON.toJSONString(atomReqGoodsDTO));
                ExecuteDTO<AtomResGoodsDTO> goodsDTOExecuteDTO = legacyGoodsCenterService.getGoodsByNo(atomReqGoodsDTO);
                log.info("-LotteryDrawRecordAnalysisServiceImpl-checkGoodsToImei-商品-出参：{}", JSON.toJSONString(goodsDTOExecuteDTO));
                if (!goodsDTOExecuteDTO.successFlag()) {
                    return ExecuteDTO.error(goodsDTOExecuteDTO.getStatus(), goodsDTOExecuteDTO.getMsg());
                }
                AtomResGoodsDTO atomResGoodsDTO = goodsDTOExecuteDTO.getData();
                if (atomResGoodsDTO == null) {
                    return ExecuteDTO.error(GoodsErrorCode.CODE_12000001);
                }
                // 判断该商品是否启动了串码
                if (atomReqGoodsDTO.getImeiFlag() != null && WhetherEnum.YES.getCode().equals(atomReqGoodsDTO.getImeiFlag())) {
                    return ExecuteDTO.success(NumConstant.ONE);
                }
            }
        }
        return ExecuteDTO.success(NumConstant.ZERO);
    }

    @Override
    public ExecuteDTO<Integer> batchCheckGoodsToImei(ReqLotteryDrawRecordDTO reqLotteryDrawRecordDTO) {
        log.info("-LotteryDrawRecordAnalysisServiceImpl-batchCheckGoodsToImei-入参：{}", JSON.toJSONString(reqLotteryDrawRecordDTO));
        lotteryDrawRecordAssert.batchCheckGoodsToImeiAssert(reqLotteryDrawRecordDTO);
        // 获取中奖记录
        AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO = new AtomReqLotteryDrawRecordDTO();
        List<String> recordNos = reqLotteryDrawRecordDTO.getRecordNos();
        atomReqLotteryDrawRecordDTO.setRecordNos(recordNos);
        ExecuteDTO<List<AtomResLotteryDrawRecordDTO>> recordDTOExecuteDTO = atomLotteryDrawRecordAnalysisService.batchGetLotteryDrawRecord(atomReqLotteryDrawRecordDTO);
        if (!recordDTOExecuteDTO.successFlag()) {
            return ExecuteDTO.error(recordDTOExecuteDTO.getStatus(), recordDTOExecuteDTO.getMsg());
        }
        List<AtomResLotteryDrawRecordDTO> recordDTOS = recordDTOExecuteDTO.getData();
        if (CollectionUtils.isEmpty(recordDTOS)) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000115);
        }
        if (recordDTOS.size() < recordNos.size()) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000115, "存在未获取中奖的记录");
        }
        for (AtomResLotteryDrawRecordDTO atomResLotteryDrawRecordDTO : recordDTOS) {
            // 实物奖品
            if (LotteryRewardTypeEnum.REWARD_TYPE_ONE.getCode().equals(atomResLotteryDrawRecordDTO.getRewardType())) {
                // 获取中奖记录-实物奖品
                AtomReqLotteryRewardGoodsRelationDTO atomReqLotteryRewardGoodsRelationDTO = BeanCopierUtil.copy(reqLotteryDrawRecordDTO, AtomReqLotteryRewardGoodsRelationDTO.class);
                ExecuteDTO<AtomResLotteryRewardGoodsRelationDTO> executeDTO = atomPromotionRewardGoodsRelationAnalysisService.getLotteryRewardGoodsRelation(atomReqLotteryRewardGoodsRelationDTO);
                log.info("-LotteryDrawRecordAnalysisServiceImpl-checkGoodsToImei-出参：{}", JSON.toJSONString(executeDTO));
                if (!executeDTO.successFlag()) {
                    return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
                }
                if (executeDTO.getData() != null) {
                    // 获取商品信息
                    AtomReqGoodsDTO atomReqGoodsDTO = new AtomReqGoodsDTO();
                    atomReqGoodsDTO.setGoodsNo(executeDTO.getData().getGoodsNo());
                    ExecuteDTO<AtomResGoodsDTO> goodsDTOExecuteDTO = legacyGoodsCenterService.getGoodsByNo(atomReqGoodsDTO);
                    if (!goodsDTOExecuteDTO.successFlag()) {
                        return ExecuteDTO.error(goodsDTOExecuteDTO.getStatus(), goodsDTOExecuteDTO.getMsg());
                    }
                    AtomResGoodsDTO atomResGoodsDTO = goodsDTOExecuteDTO.getData();
                    if (atomResGoodsDTO == null) {
                        return ExecuteDTO.error(GoodsErrorCode.CODE_12000001);
                    }
                    // 判断该商品是否启动了串码
                    if (atomReqGoodsDTO.getImeiFlag() != null && WhetherEnum.YES.getCode().equals(atomReqGoodsDTO.getImeiFlag())) {
                        return ExecuteDTO.success(NumConstant.ONE);
                    }
                }
            }
        }
        return ExecuteDTO.success(NumConstant.ZERO);
    }

    @Override
    public ExecuteDTO<Integer> getFansUnusedCoupons(String fansNo) {
        log.info("LotteryDrawRecordAnalysisServiceImpl.getFansUnusedCoupons----param----{}", fansNo);
        ExecuteDTO<Integer> remainPointsExecuteDTO = atomLotteryDrawRecordAnalysisService.getFansUnusedCoupons(fansNo);
        if (remainPointsExecuteDTO.successFlag()
                && null != remainPointsExecuteDTO.getData()
                && remainPointsExecuteDTO.getData() > 0) {
            return ExecuteDTO.error(HxgLogoffErrorEnum.UNUSED_COUPONS, remainPointsExecuteDTO.getData());
        }
        return remainPointsExecuteDTO;
    }

    @Override
    public ExecuteDTO<Integer> getUnconvertedGift(String fansNo) {
        log.info("LotteryDrawRecordAnalysisServiceImpl.getUnconvertedGift----param----{}", fansNo);
        ExecuteDTO<Integer> remainPointsExecuteDTO = atomLotteryDrawRecordAnalysisService.getUnconvertedGift(fansNo);
        if (remainPointsExecuteDTO.successFlag()
                && null != remainPointsExecuteDTO.getData()
                && remainPointsExecuteDTO.getData() > 0) {
            return ExecuteDTO.error(HxgLogoffErrorEnum.UNCONVERTED_GIFT, remainPointsExecuteDTO.getData());
        }
        return remainPointsExecuteDTO;
    }
}
