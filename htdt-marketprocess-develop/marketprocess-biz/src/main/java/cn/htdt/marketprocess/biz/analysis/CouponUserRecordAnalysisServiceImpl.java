package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.base.exception.BaseException;
import cn.htdt.common.dto.enums.BossLogoffErrorEnum;
import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.enums.GoodsErrorCode;
import cn.htdt.common.dto.enums.MarketErrorCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.constants.GoodsConstant;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.goods.GoodsFormEnum;
import cn.htdt.common.enums.goods.GoodsSourceTypeEnum;
import cn.htdt.common.enums.goods.SourceTypeEnum;
import cn.htdt.common.enums.market.*;
import cn.htdt.common.utils.*;
import cn.htdt.goodscenter.dto.request.AtomReqGoodsDTO;
import cn.htdt.goodscenter.dto.response.AtomResGoodsDTO;
import cn.htdt.goodsprocess.api.analysis.GoodsAnalysisService;
import cn.htdt.goodsprocess.api.operat.LegacyGoodsCenterService;
import cn.htdt.goodsprocess.dto.request.goods.ReqGoodsDTO;
import cn.htdt.marketcenter.dto.request.AtomReqCouponCanUseRecordDTO;
import cn.htdt.marketcenter.dto.request.AtomReqCouponUserRecordDTO;
import cn.htdt.marketcenter.dto.request.AtomReqCouponUserSuitStoreDTO;
import cn.htdt.marketcenter.dto.response.*;
import cn.htdt.marketprocess.api.analysis.CouponUserRecordAnalysisService;
import cn.htdt.marketprocess.biz.conversion.CouponSettingAssert;
import cn.htdt.marketprocess.biz.conversion.CouponUserRecordAssert;
import cn.htdt.marketprocess.biz.utils.CouponInfoUtil;
import cn.htdt.marketprocess.dao.*;
import cn.htdt.marketprocess.domain.CouponStoreCategoryRelationDomain;
import cn.htdt.marketprocess.domain.CouponUserRecordDomain;
import cn.htdt.marketprocess.domain.PromotionVirtualGoodsRelationDomain;
import cn.htdt.marketprocess.dto.request.*;
import cn.htdt.marketprocess.dto.response.*;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomCouponUserRecordAnalysisService;
import cn.htdt.marketprocess.vo.*;
import cn.htdt.orderprocess.api.OrderAnalysisService;
import cn.htdt.orderprocess.api.OrderPromotionAnalysisService;
import cn.htdt.orderprocess.dto.request.ReqOrderItemDTO;
import cn.htdt.orderprocess.dto.request.ReqSoPromotionDTO;
import cn.htdt.orderprocess.dto.response.ResSoPromotionItemDTO;
import cn.htdt.usercenter.dto.request.ReqFancDTO;
import cn.htdt.usercenter.dto.response.ResFancDTO;
import cn.htdt.userprocess.api.*;
import cn.htdt.userprocess.dto.publicDto.response.GetStoreListResponse;
import cn.htdt.userprocess.dto.request.AgentInfoRequest;
import cn.htdt.userprocess.dto.request.GetFansRequest;
import cn.htdt.userprocess.dto.request.MerchantStoreRequest;
import cn.htdt.userprocess.dto.response.AgentInfoResponse;
import cn.htdt.userprocess.dto.response.GetFansResponse;
import cn.htdt.userprocess.dto.response.MerchantStoreResponse;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import javax.annotation.Resource;
import javax.xml.rpc.holders.BigDecimalHolder;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 优惠券领发记录表
 *
 * <AUTHOR>
 */
@Slf4j
@RefreshScope
@DubboService
public class CouponUserRecordAnalysisServiceImpl implements CouponUserRecordAnalysisService {

    /**
     * 优惠券使用记录
     */
    @Resource
    private AtomCouponUserRecordAnalysisService atomCouponUserRecordAnalysisService;

    @DubboReference
    private LegacyGoodsCenterService legacyGoodsCenterService;

    @DubboReference
    private AgentProcessService agentProcessService;

    @DubboReference
    private StoreProcessService storeProcessService;
    /**
     * 获取所有店铺用
     */
    @DubboReference
    private UserPublicService userPublicService;
    /**
     * 获取粉丝信息
     */
    @DubboReference
    private UcFansProcessService ucFansProcessService;

    @DubboReference
    private LegacyUserCenterService legacyUserCenterService;

    @DubboReference
    private OrderPromotionAnalysisService orderPromotionAnalysisService;

    @Resource
    private CouponStoreCategoryRelationDao couponStoreCategoryRelationDao;

    @DubboReference
    private GoodsAnalysisService goodsAnalysisService;

    @DubboReference
    private OrderAnalysisService orderAnalysisService;

    /**
     * 券记录
     */
    @Resource
    private CouponUserRecordDao couponUserRecordDao;

    @Resource
    private CouponUserRecordEffectDao couponUserRecordEffectDao;

    @Autowired
    PromotionVirtualGoodsRelationDao promotionVirtualGoodsRelationDao;

    @Autowired
    private CouponSettingAssert couponSettingAssert;

    @Resource
    private CouponUserRecordAssert couponUserRecordAssert;

    @Resource
    private DataBaseUtil dataBaseUtil;

    @Resource
    private CouponInfoUtil couponInfoUtil;

    @Resource
    private CouponUserSuitStoreDao couponUserSuitStoreDao;

    @Resource
    private CouponSettingDao couponSettingDao;

    @Override
    public ExecuteDTO<ExecutePageDTO<ResCouponUserRecordDTO>> getCouponUserRecordPage(ReqCouponUserRecordDTO reqCouponUserRecordDTO) throws BaseException {
        log.info("MarketProcess--CouponUserRecordAnalysisServiceImpl-getCouponSendRecordPage-param={}", JSON.toJSONString(reqCouponUserRecordDTO));
        AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO = BeanCopierUtil.copy(reqCouponUserRecordDTO, AtomReqCouponUserRecordDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResCouponUserRecordDTO>> executeDTO = atomCouponUserRecordAnalysisService.getCouponUserRecordPage(atomReqCouponUserRecordDTO);
        log.info("MarketProcess-CouponUserRecordAnalysisServiceImpl-getCouponSendRecordPage-return={}", JSON.toJSONString(executeDTO));
        log.info("MarketProcess-CouponUserRecordAnalysisServiceImpl-getCouponSendRecordPage-end");
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        //参数转换
        ExecutePageDTO<ResCouponUserRecordDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResCouponUserRecordDTO> resCouponUserRecordDTOS = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResCouponUserRecordDTO.class);
        if (CollectionUtils.isNotEmpty(resCouponUserRecordDTOS)) {
            ExecuteDTO<List<ResFancDTO>> fanByFanNoList = null;
            List<String> fansList = resCouponUserRecordDTOS.stream().map(ResCouponUserRecordDTO::getFanNo).collect(Collectors.toList());
            AtomCouponSettingVo atomCouponSettingVo = new AtomCouponSettingVo();
            atomCouponSettingVo.setCouponNo(reqCouponUserRecordDTO.getCouponNo());
            AtomCouponSettingVo resAtomCouponSettingVo = couponSettingDao.selectCouponSetting(atomCouponSettingVo);
            if (resAtomCouponSettingVo != null && SourceTypeEnum.SOURCE_TYPE_1002.getCode().equals(resAtomCouponSettingVo.getSourceType())) {
                //商家发券领券查询粉丝信息
                fanByFanNoList = legacyUserCenterService.getFanByFanNos(fansList);
            } else {
                ReqFancDTO reqFancDTO = new ReqFancDTO();
                reqFancDTO.setFansNoList(fansList);
                reqFancDTO.setStoreNo(resCouponUserRecordDTOS.get(0).getStoreNo());
                fanByFanNoList = legacyUserCenterService.getFansInfoByNos(reqFancDTO);
            }
            if (!fanByFanNoList.successFlag()) {
                return ExecuteDTO.error(fanByFanNoList.getStatus(), fanByFanNoList.getMsg());
            }
            List<ResFancDTO> resFancList = fanByFanNoList.getData();
            if (CollectionUtils.isNotEmpty(resFancList)) {
                resCouponUserRecordDTOS.forEach(couponUserRecordDTO -> {
                    // 塞昵称
                    if (CollectionUtils.isNotEmpty(resFancList)) {
                        resFancList.forEach(resFancDTO -> {
                            if (couponUserRecordDTO.getFanNo().equals(resFancDTO.getFanNo())) {
                                couponUserRecordDTO.setNickName(resFancDTO.getNickName());
                                couponUserRecordDTO.setStoreFanName(resFancDTO.getStoreFanName());
                            }
                        });
                    }
                    // 赛优惠券的状态数据
                    // 未使用、已使用状态
                    CouponShowStatusEnum couponShowStatusEnum = CouponShowStatusEnum.getByCode(couponUserRecordDTO.getUseFlag());
                    if (couponShowStatusEnum != null) {
                        couponUserRecordDTO.setCouponShowStatus(couponShowStatusEnum.getType());
                    }
                    // 如果不是长期有效的优惠券，判断是否过期，过期的话，更新成已过期
                    if (!(CouponPeriodValidityEnum.COUPON_PERIOD_VALIDITY_ONE.getCode().equals(couponUserRecordDTO.getCouponPeriodValidity()))
                            && couponUserRecordDTO.getCouponInvalidTime() != null
                            && DateUtil.getLocalDateTime().isAfter(couponUserRecordDTO.getCouponInvalidTime())) {
                        couponUserRecordDTO.setCouponShowStatus(CouponShowStatusEnum.EXPIRE.getType());
                    }
                });
            }
            //查询适用店铺信息
            if (StringUtils.isBlank(reqCouponUserRecordDTO.getStoreNo()) && StringUtils.isNotBlank(reqCouponUserRecordDTO.getMerchantNo())) {
                ExecuteDTO suitStoreExecuteDTO = setSuitStoreInfo(resCouponUserRecordDTOS, reqCouponUserRecordDTO);
                if (!suitStoreExecuteDTO.successFlag()) {
                    return ExecuteDTO.error(suitStoreExecuteDTO.getStatus(), suitStoreExecuteDTO.getMsg());
                }
            }

        }
        executePageDTO.setRows(resCouponUserRecordDTOS);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    /**
     * 查询适用店铺信息
     *
     * @param resCouponUserRecordDTOS
     * @param reqCouponUserRecordDTO
     */
    private ExecuteDTO setSuitStoreInfo(List<ResCouponUserRecordDTO> resCouponUserRecordDTOS, ReqCouponUserRecordDTO reqCouponUserRecordDTO) {
        List<CouponUserSuitStoreVO> couponUserSuitStoreVOList = BeanCopierUtil.copyList(resCouponUserRecordDTOS, CouponUserSuitStoreVO.class);
        List<CouponUserSuitStoreVO> couponUserSuitStoreVOs = couponUserSuitStoreDao.batchQueryCouponUserSuitStoreList(couponUserSuitStoreVOList);
        if (ListUtil.isNotEmpty(couponUserSuitStoreVOs) && StringUtils.isNotBlank(reqCouponUserRecordDTO.getMerchantNo())) {
            MerchantStoreRequest merchantStoreRequest = new MerchantStoreRequest();
            merchantStoreRequest.setMerchantNo(reqCouponUserRecordDTO.getMerchantNo());
            ExecuteDTO<List<MerchantStoreResponse>> merchantStoreResponseExecuteDTO = storeProcessService.queryMerchantStore(merchantStoreRequest);
            if (null == merchantStoreResponseExecuteDTO) {
                return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
            }
            if (!merchantStoreResponseExecuteDTO.successFlag()) {
                return new ExecuteDTO<>(merchantStoreResponseExecuteDTO.getStatus(), merchantStoreResponseExecuteDTO.getMsg(), null);
            }
            List<MerchantStoreResponse> merchantStoreResponseList = merchantStoreResponseExecuteDTO.getData();
            if (ListUtil.isNotEmpty(merchantStoreResponseList)) {
                Map<String, MerchantStoreResponse> merchantStoreMap = merchantStoreResponseList.stream().collect(Collectors.toMap(MerchantStoreResponse::getStoreNo, store -> store));
                Map<String, List<CouponUserSuitStoreVO>> resCouponUserSuitStoreMap = couponUserSuitStoreVOs.stream().collect(Collectors.groupingBy(CouponUserSuitStoreVO::getUserCouponNo));
                resCouponUserRecordDTOS.forEach(resCouponUserRecord -> {
                    List<CouponUserSuitStoreVO> suitStoreVoList = resCouponUserSuitStoreMap.get(resCouponUserRecord.getUserCouponNo());
                    if (CollectionUtils.isNotEmpty(suitStoreVoList)) {
                        List<String> suitStoreNoList = suitStoreVoList.stream().map(CouponUserSuitStoreVO::getStoreNo).collect(Collectors.toList());
                        List<String> suitStoreNameList = Lists.newArrayList();
                        suitStoreNoList.forEach(storeNo -> {
                            suitStoreNameList.add(merchantStoreMap.get(storeNo).getStoreName());
                        });
                        resCouponUserRecord.setSuitStoreNameList(suitStoreNameList);
                    } else {
                        log.error("setSuitStoreInfo--->suitStoreVoList are empty, resCouponUserRecord: {}", JSON.toJSONString(resCouponUserRecord));
                    }
                });
            }
        }
        return ExecuteDTO.success();
    }

    @Override
    public ExecuteDTO<List<ResCouponUserRecordDTO>> getCouponUserRecordList(ReqCouponUserRecordDTO reqCouponUserRecordDTO) {
        log.info("MarketProcess--CouponUserRecordAnalysisServiceImpl-getCouponUserRecordList-param={}", JSON.toJSONString(reqCouponUserRecordDTO));
        CouponUserRecordVO couponUserRecordVO = BeanCopierUtil.copy(reqCouponUserRecordDTO, CouponUserRecordVO.class);
        List<CouponUserRecordVO> list = couponUserRecordDao.selectCouponUserRecords(couponUserRecordVO);
        List<ResCouponUserRecordDTO> resList = BeanCopierUtil.copyList(list, ResCouponUserRecordDTO.class);
        log.info("MarketProcess-CouponUserRecordAnalysisServiceImpl-getCouponUserRecordList-return={}", JSON.toJSONString(resList));
        return ExecuteDTO.ok(resList);
    }

    /**
     * @param reqCouponCanUseRecordDTO
     * @Description : 查询用户+商品可用的优惠券数量
     * <AUTHOR> 高繁
     * @date : 2021/4/27 10:32
     */
    @Override
    public ExecuteDTO<List<ResCouponCanUserRecordDTO>> getCouponCanUse(ReqCouponCanUseRecordDTO reqCouponCanUseRecordDTO) {
        log.info("MarketProcess--CouponUserRecordAnalysisServiceImpl-getCouponCanUseCount-param={}", JSON.toJSONString(reqCouponCanUseRecordDTO));
        AtomReqCouponCanUseRecordDTO atomReqCouponCanUseRecordDTO = BeanCopierUtil.copy(reqCouponCanUseRecordDTO, AtomReqCouponCanUseRecordDTO.class);
        List<ReqCouponGoodsAmountDTO> goodsInfoList = reqCouponCanUseRecordDTO.getGoodsInfo();
        List<AtomResCouponCanUserRecordDTO> effectiveCouponList = new ArrayList<>();

        Map<String, List<ReqCouponGoodsAmountDTO>> storeNoGroupMap = goodsInfoList.stream().collect(Collectors.groupingBy(ReqCouponGoodsAmountDTO::getStoreNo));
        for (Map.Entry<String, List<ReqCouponGoodsAmountDTO>> orderNoMap : storeNoGroupMap.entrySet()) {
            List<ReqCouponGoodsAmountDTO> storeGoodsInfo = orderNoMap.getValue();
            String storeNo = orderNoMap.getKey();
            atomReqCouponCanUseRecordDTO.setStoreNo(storeNo);
            ExecuteDTO<List<AtomResCouponCanUserRecordDTO>> executeDTO = atomCouponUserRecordAnalysisService.getCouponCanUse(atomReqCouponCanUseRecordDTO);
            if (!executeDTO.successFlag()) {
                return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
            }
            log.info(String.format("可用优惠券未过滤数据:%s", JSON.toJSONString(executeDTO)));
            if (CollectionUtils.isNotEmpty(executeDTO.getData())) {
                Map<String, ReqCouponGoodsAmountDTO> goodsMap = ListUtil.toMapKeepFirst(storeGoodsInfo, ReqCouponGoodsAmountDTO::getGoodsNo);
                //商品总金额
                BigDecimal goodsTotalAmount = storeGoodsInfo.stream().map(ReqCouponGoodsAmountDTO::getGoodsAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                //查询商品主信息
                AtomReqGoodsDTO reqGoodsDTO = new AtomReqGoodsDTO();
                reqGoodsDTO.setNoPage();
                reqGoodsDTO.setGoodsNos(new ArrayList<>(goodsMap.keySet()));
                ExecuteDTO<ExecutePageDTO<AtomResGoodsDTO>> executePageDTOExecuteDTO = legacyGoodsCenterService.getGoodsPage(reqGoodsDTO);
                if (!executePageDTOExecuteDTO.successFlag() || executePageDTOExecuteDTO.getData() == null
                        || CollectionUtils.isEmpty(executePageDTOExecuteDTO.getData().getRows())) {
                    return ExecuteDTO.error(executePageDTOExecuteDTO.getStatus(), executePageDTOExecuteDTO.getMsg());
                }

                //过滤优惠券门槛信息
                List<ResGoodsRecordDTO> goodsList = BeanCopierUtil.copyList(executePageDTOExecuteDTO.getData().getRows(), ResGoodsRecordDTO.class);

                //合并金额数据
                AtomicReference<BigDecimal> merchantGoodsTotalAmount = new AtomicReference<>(BigDecimal.ZERO);
                List<BigDecimal> merchantGoodsAmountList = new ArrayList<>();
                for (ResGoodsRecordDTO resGoodsRecordDTO : goodsList) {
                    ReqCouponGoodsAmountDTO reqCouponGoodsAmountDTO = goodsMap.get(resGoodsRecordDTO.getGoodsNo());
                    if (null != reqCouponGoodsAmountDTO) {
                        resGoodsRecordDTO.setGoodsAmount(reqCouponGoodsAmountDTO.getGoodsAmount());
                        if (GoodsSourceTypeEnum.STORE.getCode().equals(resGoodsRecordDTO.getGoodsSourceType()) && StringUtils.isNotBlank(resGoodsRecordDTO.getMerchantGoodsNo())) {
                            merchantGoodsTotalAmount.accumulateAndGet(resGoodsRecordDTO.getGoodsAmount(), BigDecimal::add);
                            merchantGoodsAmountList.add(resGoodsRecordDTO.getGoodsAmount());
                        }
                    }
                }
                //按照品牌分组
                Map<String, List<ResGoodsRecordDTO>> brandGroupMap = goodsList.stream().collect(Collectors.groupingBy(ResGoodsRecordDTO::getBrandNo));
                //按照品类分组
                Map<String, List<ResGoodsRecordDTO>> categoryGroupMap = goodsList.stream().collect(Collectors.groupingBy(ResGoodsRecordDTO::getCategoryNo));
                //数据格式转换
                List<String> brandList = new ArrayList<>(brandGroupMap.keySet());
                List<String> categoryList = new ArrayList<>(categoryGroupMap.keySet());
                for (ResGoodsRecordDTO resGoodsDTO : goodsList) {
                    //云池商品系列品重新查询主品编码
                    log.info(String.format("商品基本信息:%s", JSON.toJSONString(resGoodsDTO)));
                    if (GoodsFormEnum.SERIES_GOODS.getCode().equals(resGoodsDTO.getGoodsForm()) && GoodsSourceTypeEnum.CLOUD_POOL_DISTRIBUTE.getCode().equals(resGoodsDTO.getGoodsSourceType())) {
                        AtomReqGoodsDTO goodsDTO = new AtomReqGoodsDTO();
                        goodsDTO.setNoPage();
                        goodsDTO.setGoodsNo(resGoodsDTO.getCloudPoolGoodsNo());
                        ExecuteDTO<ExecutePageDTO<AtomResGoodsDTO>> cloudExecute = legacyGoodsCenterService.getGoodsPage(goodsDTO);
                        log.info(String.format("云池系列品查询结果:%s", JSON.toJSONString(cloudExecute)));
                        if (!cloudExecute.successFlag() || cloudExecute.getData() == null
                                || CollectionUtils.isEmpty(cloudExecute.getData().getRows())) {
                            return ExecuteDTO.error(cloudExecute.getStatus(), cloudExecute.getMsg());
                        }
                        resGoodsDTO.setParentGoodsNo(cloudExecute.getData().getRows().get(NumConstant.ZERO).getParentGoodsNo());
                    }
                }
                for (AtomResCouponCanUserRecordDTO atomResCouponCanUserRecordDTO : executeDTO.getData()) {
                    AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO = new AtomReqCouponUserRecordDTO();
                    atomReqCouponUserRecordDTO.setCouponNo(atomResCouponCanUserRecordDTO.getCouponNo());
                    if (reqCouponCanUseRecordDTO.getIsCloudGoods() != null && NumConstant.TWO == reqCouponCanUseRecordDTO.getIsCloudGoods()) {
                        // 非云池券
                        if (CouponUseScopeEnum.COUPON_USE_SCOPE_FIVE.getCode().equals(atomResCouponCanUserRecordDTO.getCouponUseScope())) {
                            //店铺指定品牌
                            ExecuteDTO<List<String>> brandExecuteDTO = atomCouponUserRecordAnalysisService.getCouponBrandList(atomReqCouponUserRecordDTO);
                            if (brandExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(brandExecuteDTO.getData())) {
                                //配置信息不为空，判断配置品牌编号是否包含当前订单中的商品
                                BigDecimalHolder totalAmount = new BigDecimalHolder(BigDecimal.ZERO);
                                List<BigDecimal> goodsAmountList = new ArrayList<>();
                                for (String brandNo : brandList) {
                                    if (brandExecuteDTO.getData().contains(brandNo)) {
                                        //计算该品牌下商品总金额
                                        List<ResGoodsRecordDTO> resGoodsRecordDTOList = brandGroupMap.get(brandNo);
                                        resGoodsRecordDTOList.forEach(resGoodsRecordDTO -> {
                                            totalAmount.value = totalAmount.value.add(resGoodsRecordDTO.getGoodsAmount());
                                            goodsAmountList.add(resGoodsRecordDTO.getGoodsAmount());
                                        });
                                    }
                                }
                                if (totalAmount.value.compareTo(atomResCouponCanUserRecordDTO.getDiscountThreshold()) >= NumConstant.ZERO) {
                                    discountCouponCalculate(atomResCouponCanUserRecordDTO, goodsAmountList);
                                    effectiveCouponList.add(atomResCouponCanUserRecordDTO);
                                    continue;
                                }
                            }
                        } else if (CouponUseScopeEnum.COUPON_USE_SCOPE_FOUR.getCode().equals(atomResCouponCanUserRecordDTO.getCouponUseScope())) {
                            //店铺指定类目
                            ExecuteDTO<List<String>> categoryExecuteDTO = atomCouponUserRecordAnalysisService.getCouponCategoryList(atomReqCouponUserRecordDTO);
                            if (categoryExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(categoryExecuteDTO.getData())) {
                                //配置信息不为空，判断配置品类编号是否包含当前订单中的商品
                                BigDecimalHolder totalAmount = new BigDecimalHolder(BigDecimal.ZERO);
                                List<BigDecimal> goodsAmountList = new ArrayList<>();
                                for (String categoryNo : categoryList) {
                                    if (categoryExecuteDTO.getData().contains(categoryNo)) {
                                        //品类下商品总金额
                                        List<ResGoodsRecordDTO> resGoodsRecordDTOList = categoryGroupMap.get(categoryNo);
                                        resGoodsRecordDTOList.forEach(resGoodsRecordDTO -> {
                                            totalAmount.value = totalAmount.value.add(resGoodsRecordDTO.getGoodsAmount());
                                            goodsAmountList.add(resGoodsRecordDTO.getGoodsAmount());
                                        });
                                    }
                                }
                                //所有分类商品金额总和大于优惠券门槛
                                if (totalAmount.value.compareTo(atomResCouponCanUserRecordDTO.getDiscountThreshold()) >= NumConstant.ZERO) {
                                    discountCouponCalculate(atomResCouponCanUserRecordDTO, goodsAmountList);
                                    effectiveCouponList.add(atomResCouponCanUserRecordDTO);
                                    continue;
                                }
                            }
                        } else if (CouponUseScopeEnum.COUPON_USE_SCOPE_SIX.getCode().equals(atomResCouponCanUserRecordDTO.getCouponUseScope())) {
                            //20230825蛋品-倪浩轩-店铺折扣券-门店开单-历史bug修复
                            //店铺指定商品
                            ExecuteDTO<List<String>> goodsExecuteDTO = atomCouponUserRecordAnalysisService.getCouponGoodsList(atomReqCouponUserRecordDTO);
                            if (goodsExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(goodsExecuteDTO.getData())) {
                                AtomicReference<BigDecimal> storeGoodsTotalAmount = new AtomicReference<>(BigDecimal.ZERO);
                                List<BigDecimal> goodsAmountList = new ArrayList<>();
                                //配置信息不为空，判断配置商品编号是否包含当前订单中的商品
                                for (ResGoodsRecordDTO resGoodsDTO : goodsList) {
                                    if ((goodsExecuteDTO.getData().contains(resGoodsDTO.getGoodsNo()) || goodsExecuteDTO.getData().contains(resGoodsDTO.getParentGoodsNo()))) {
                                        storeGoodsTotalAmount.accumulateAndGet(resGoodsDTO.getGoodsAmount(), BigDecimal::add);
                                        goodsAmountList.add(resGoodsDTO.getGoodsAmount());
                                    }
                                }
                                if (BigDecimalUtil.ge(storeGoodsTotalAmount.get(), atomResCouponCanUserRecordDTO.getDiscountThreshold())) {
                                    discountCouponCalculate(atomResCouponCanUserRecordDTO, goodsAmountList);
                                    effectiveCouponList.add(atomResCouponCanUserRecordDTO);
                                    continue;
                                }
                            }
                        } else if (CouponUseScopeEnum.COUPON_USE_SCOPE_ONE.getCode().equals(atomResCouponCanUserRecordDTO.getCouponUseScope())) {
                            //20230825蛋品-倪浩轩-店铺折扣券-门店开单-历史bug修复
                            //店铺全场通用
                            AtomicReference<BigDecimal> storeGoodsTotalAmount = new AtomicReference<>(BigDecimal.ZERO);
                            List<BigDecimal> goodsAmountList = new ArrayList<>();
                            //配置信息不为空，判断配置商品编号是否包含当前订单中的商品
                            for (ResGoodsRecordDTO resGoodsDTO : goodsList) {
                                if (!GoodsSourceTypeEnum.CLOUD_POOL_DISTRIBUTE.getCode().equals(resGoodsDTO.getGoodsSourceType())) {
                                    storeGoodsTotalAmount.accumulateAndGet(resGoodsDTO.getGoodsAmount(), BigDecimal::add);
                                    goodsAmountList.add(resGoodsDTO.getGoodsAmount());
                                }
                            }
                            if (BigDecimalUtil.ge(storeGoodsTotalAmount.get(), atomResCouponCanUserRecordDTO.getDiscountThreshold())) {
                                discountCouponCalculate(atomResCouponCanUserRecordDTO, goodsAmountList);
                                effectiveCouponList.add(atomResCouponCanUserRecordDTO);
                                continue;
                            }
                        } else if (CouponUseScopeEnum.COUPON_USE_SCOPE_EIGHT.getCode().equals(atomResCouponCanUserRecordDTO.getCouponUseScope())) {
                            // 商家商品通用,如果商家商品总金额大于券阈值，则券可用
                            if (BigDecimalUtil.ge(merchantGoodsTotalAmount.get(), atomResCouponCanUserRecordDTO.getDiscountThreshold())) {
                                discountCouponCalculate(atomResCouponCanUserRecordDTO, merchantGoodsAmountList);
                                effectiveCouponList.add(atomResCouponCanUserRecordDTO);
                                continue;
                            }
                        } else if (CouponUseScopeEnum.COUPON_USE_SCOPE_NINE.getCode().equals(atomResCouponCanUserRecordDTO.getCouponUseScope())) {
                            // 限指定商家商品
                            ExecuteDTO<List<String>> goodsExecuteDTO = atomCouponUserRecordAnalysisService.getCouponGoodsList(atomReqCouponUserRecordDTO);
                            if (goodsExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(goodsExecuteDTO.getData())) {
                                AtomicReference<BigDecimal> curMerchantGoodsTotalAmount = new AtomicReference<>(BigDecimal.ZERO);
                                List<BigDecimal> goodsAmountList = new ArrayList<>();
                                //配置信息不为空，判断配置商品编号是否包含当前订单中的商品,并且包含商品总金额大于等于券使用阈值
                                for (ResGoodsRecordDTO resGoodsDTO : goodsList) {
                                    if (GoodsSourceTypeEnum.STORE.getCode().equals(resGoodsDTO.getGoodsSourceType())
                                            && StringUtils.isNotBlank(resGoodsDTO.getMerchantGoodsNo())
                                            && goodsExecuteDTO.getData().contains(resGoodsDTO.getMerchantGoodsNo())) {
                                        curMerchantGoodsTotalAmount.accumulateAndGet(resGoodsDTO.getGoodsAmount(), BigDecimal::add);
                                        goodsAmountList.add(resGoodsDTO.getGoodsAmount());
                                    }
                                }
                                if (BigDecimalUtil.ge(curMerchantGoodsTotalAmount.get(), atomResCouponCanUserRecordDTO.getDiscountThreshold())) {
                                    discountCouponCalculate(atomResCouponCanUserRecordDTO, goodsAmountList);
                                    effectiveCouponList.add(atomResCouponCanUserRecordDTO);
                                    continue;
                                }
                            }
                        }
                    }
                    for (ResGoodsRecordDTO resGoodsDTO : goodsList) {
                        log.info("商品基本信息:{}", JSON.toJSONString(resGoodsDTO));
                        if (reqCouponCanUseRecordDTO.getIsCloudGoods() != null && NumConstant.ONE == reqCouponCanUseRecordDTO.getIsCloudGoods() && GoodsSourceTypeEnum.CLOUD_POOL_DISTRIBUTE.getCode().equals(resGoodsDTO.getGoodsSourceType())) {
                            //云池券
                            if (CouponUseScopeEnum.COUPON_USE_SCOPE_TWO.getCode().equals(atomResCouponCanUserRecordDTO.getCouponUseScope())) {
                                //云池商品通用,查询商品是否存在云池商品
                                if (goodsTotalAmount.compareTo(atomResCouponCanUserRecordDTO.getDiscountThreshold()) >= NumConstant.ZERO) {
                                    effectiveCouponList.add(atomResCouponCanUserRecordDTO);
                                    break;
                                }
                            } else if (CouponUseScopeEnum.COUPON_USE_SCOPE_THREE.getCode().equals(atomResCouponCanUserRecordDTO.getCouponUseScope())) {
                                //指定云池商品,查询券设置的云池商品编号
                                ExecuteDTO<List<String>> goodsExecuteDTO = atomCouponUserRecordAnalysisService.getCouponGoodsList(atomReqCouponUserRecordDTO);
                                if (goodsExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(goodsExecuteDTO.getData())) {
                                    //配置信息不为空，判断配置商品编号是否包含当前订单中的商品，并且满额
                                    if ((goodsExecuteDTO.getData().contains(resGoodsDTO.getCloudPoolGoodsNo()) || goodsExecuteDTO.getData().contains(resGoodsDTO.getParentGoodsNo())) && resGoodsDTO.getGoodsAmount().compareTo(atomResCouponCanUserRecordDTO.getDiscountThreshold()) >= NumConstant.ZERO) {
                                        effectiveCouponList.add(atomResCouponCanUserRecordDTO);
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return ExecuteDTO.success(BeanCopierUtil.copyList(effectiveCouponList, ResCouponCanUserRecordDTO.class));
    }

    /**
     * 20230807蛋品-倪浩轩-店铺折扣券-购物车
     * 折扣后的金额计算
     *
     * @param atomResCouponCanUserRecordDTO
     * @param goodsAmountList
     */
    private void discountCouponCalculate(AtomResCouponCanUserRecordDTO atomResCouponCanUserRecordDTO, List<BigDecimal> goodsAmountList) {
        if (CouponTypeEnum.COUPON_DISCONUT.getCode().equals(atomResCouponCanUserRecordDTO.getCouponType())) {
            atomResCouponCanUserRecordDTO.setDiscountCouponValue(atomResCouponCanUserRecordDTO.getUserCouponValue());
            BigDecimal totalCouponAmount = BigDecimal.ZERO;
            for (BigDecimal goodsAmount : goodsAmountList) {
                BigDecimal userCouponValueItem = goodsAmount.subtract(goodsAmount.multiply(atomResCouponCanUserRecordDTO.getUserCouponValue()).divide(BigDecimal.TEN, 2, RoundingMode.DOWN)).setScale(2, RoundingMode.DOWN);
                totalCouponAmount = BigDecimalUtil.add(totalCouponAmount, userCouponValueItem);
            }

            atomResCouponCanUserRecordDTO.setUserCouponValue(totalCouponAmount);
        }
    }

    /**
     * 查询代金券列表 通过用户+商品筛选
     *
     * @param reqVoucherListDTO
     * @return ExecuteDTO<ResVoucherListDTO>
     * <AUTHOR>
     */
    @Override
    public ExecuteDTO<ResVoucherListDTO> getVoucherListByFansAndStore(ReqVoucherListDTO reqVoucherListDTO) {
        log.info("MarketProcess--CouponUserRecordAnalysisServiceImpl-getVoucherListByFansAndStore-param={}", JSON.toJSONString(reqVoucherListDTO));
        // 获取粉丝店铺下代金券
        AtomReqCouponUserRecordDTO atomReqCouponCanUseRecordDTO = new AtomReqCouponUserRecordDTO();
        atomReqCouponCanUseRecordDTO.setFanNo(reqVoucherListDTO.getFanNo());
        atomReqCouponCanUseRecordDTO.setStoreNo(reqVoucherListDTO.getStoreNo());
        atomReqCouponCanUseRecordDTO.setCouponType(CouponTypeEnum.VOUCHER.getCode());
        atomReqCouponCanUseRecordDTO.setUseFlag(WhetherEnum.NO.getCode());
        log.info("getVoucherListByFansAndStore-atomCouponUserRecordAnalysisService.getMyCouponList-param={}", atomReqCouponCanUseRecordDTO);
        ExecuteDTO<List<AtomResCouponUserRecordDTO>> executeDTO = atomCouponUserRecordAnalysisService.getMyCouponList(atomReqCouponCanUseRecordDTO);
        log.info("getVoucherListByFansAndStore-atomCouponUserRecordAnalysisService.getMyCouponList-结果集={}", executeDTO);

        // 查询结果
        ResVoucherListDTO result = new ResVoucherListDTO();
        if (executeDTO.successFlag() && CollectionUtils.isNotEmpty(executeDTO.getData())) {
            // 判断是够使用优惠券
            boolean useCouponsFlag = false;
            if (CollectionUtils.isNotEmpty(reqVoucherListDTO.getUserCouponNoList())) {
                AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO = new AtomReqCouponUserRecordDTO();
                atomReqCouponUserRecordDTO.setFanNo(reqVoucherListDTO.getFanNo());
                atomReqCouponUserRecordDTO.setUserCouponNoList(reqVoucherListDTO.getUserCouponNoList());
                ExecuteDTO<List<AtomResCouponUserRecordDTO>> useCouponExecuteDTO = atomCouponUserRecordAnalysisService.getMyCouponList(atomReqCouponUserRecordDTO);
                if (useCouponExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(useCouponExecuteDTO.getData())) {
                    useCouponsFlag = true;
                }
            }

            //查询商品主信息
            AtomReqGoodsDTO atomReqGoodsDTO = new AtomReqGoodsDTO();
            atomReqGoodsDTO.setNoPage();
            atomReqGoodsDTO.setGoodsNos(reqVoucherListDTO.getGoodsNoList());
            ExecuteDTO<ExecutePageDTO<AtomResGoodsDTO>> executePageDTOExecuteDTO = legacyGoodsCenterService.getGoodsPage(atomReqGoodsDTO);
            if (!executePageDTOExecuteDTO.successFlag()
                    || executePageDTOExecuteDTO.getData() == null
                    || CollectionUtils.isEmpty(executePageDTOExecuteDTO.getData().getRows())) {
                return ExecuteDTO.error(executePageDTOExecuteDTO.getStatus(), executePageDTOExecuteDTO.getMsg());
            }
            List<AtomResGoodsDTO> resGoodsDTOList = executePageDTOExecuteDTO.getData().getRows();
            //商品销售类目
            List<String> categoryList = resGoodsDTOList.stream().map(AtomResGoodsDTO::getCategoryNo).distinct().collect(Collectors.toList());

            //查询商品店铺类目信息
            ReqGoodsDTO reqGoodsDTO = new ReqGoodsDTO();
            reqGoodsDTO.setGoodsNos(reqVoucherListDTO.getGoodsNoList());
            ExecuteDTO<List<String>> goodsStoreCategoryExecuteDTO = goodsAnalysisService.getGoodsStoreCategory(reqGoodsDTO);
            if (!goodsStoreCategoryExecuteDTO.successFlag()) {
                return ExecuteDTO.error(goodsStoreCategoryExecuteDTO.getStatus(), goodsStoreCategoryExecuteDTO.getMsg());
            }
            //商品店铺类目
            List<String> storeCategoryList = CollectionUtils.isEmpty(goodsStoreCategoryExecuteDTO.getData()) ? null : goodsStoreCategoryExecuteDTO.getData();

            // 当前时间
            LocalDateTime currentTime = LocalDateTime.now();
            for (AtomResCouponUserRecordDTO atomResCouponCanUserRecordDTO : executeDTO.getData()) {
                // 不可用原因
                List<String> notAvailableReason = Lists.newArrayList();

                // 当前时间超过结束时间
                if (!CouponPeriodValidityEnum.COUPON_PERIOD_VALIDITY_ONE.getCode().equals(atomResCouponCanUserRecordDTO.getCouponPeriodValidity())
                        && currentTime.isAfter(atomResCouponCanUserRecordDTO.getCouponInvalidTime())) {
                    continue;
                }

                // 代金券不可用原因：商品不在用券范围内
                AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO = new AtomReqCouponUserRecordDTO();
                atomReqCouponUserRecordDTO.setCouponNo(atomResCouponCanUserRecordDTO.getCouponNo());
                if (CouponUseScopeEnum.COUPON_USE_SCOPE_FOUR.getCode().equals(atomResCouponCanUserRecordDTO.getCouponUseScope())) {
                    // 限指定销售类目使用
                    log.info("限指定销售类目使用-couponNo={}", atomResCouponCanUserRecordDTO.getCouponNo());
                    ExecuteDTO<List<String>> categoryExecuteDTO = atomCouponUserRecordAnalysisService.getCouponCategoryList(atomReqCouponUserRecordDTO);
                    if (categoryExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(categoryExecuteDTO.getData())) {
                        //配置信息不为空，判断配置品类编号是否包含当前订单中的商品
                        if (CollectionUtils.isEmpty(CollectionUtils.intersection(categoryExecuteDTO.getData(), categoryList))) {
                            //商品不在用券范围内
                            notAvailableReason.add(MarketErrorCode.CODE_17001004.getInMsg());
                        }
                    }
                } else if (CouponUseScopeEnum.COUPON_USE_SCOPE_SEVEN.getCode().equals(atomResCouponCanUserRecordDTO.getCouponUseScope())) {
                    // 限指定店铺类目使用
                    log.info("限指定店铺类目使用-couponNo={}", atomResCouponCanUserRecordDTO.getCouponNo());
                    CouponCategoryRelationVO couponCategoryRelationVO = new CouponCategoryRelationVO();
                    couponCategoryRelationVO.setCouponNo(atomResCouponCanUserRecordDTO.getCouponNo());
                    List<CouponStoreCategoryRelationDomain> domains = this.couponStoreCategoryRelationDao.selectCouponStoreCategoryRelation(couponCategoryRelationVO);
                    if (CollectionUtils.isNotEmpty(domains)) {
                        List<String> couponSettingStoreCategoryNoList = domains.stream().map(CouponStoreCategoryRelationDomain::getCategoryNo).distinct().collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(storeCategoryList) || ListUtil.getFirst(storeCategoryList) == null) {
                            // 商品店铺类目为空，循环代金券指定店铺类目是否指定无分类可用，判断是否在使用范围
                            if (!couponSettingStoreCategoryNoList.contains(GoodsConstant.DEFAULT_STORE_CATEGORY_NO)) {
                                //商品不在用券范围内
                                notAvailableReason.add(MarketErrorCode.CODE_17001004.getInMsg());
                            }
                        } else {
                            // 商品店铺类目不为空，直接取商品店铺类目与代金券指定店铺类目的交集，判断是否在使用范围
                            //配置信息不为空，判断配置品类编号是否包含当前订单中的商品
                            if (CollectionUtils.isEmpty(CollectionUtils.intersection(couponSettingStoreCategoryNoList, storeCategoryList))) {
                                //商品不在用券范围内
                                notAvailableReason.add(MarketErrorCode.CODE_17001004.getInMsg());
                            }
                        }
                    }
                } else if (CouponUseScopeEnum.COUPON_USE_SCOPE_SIX.getCode().equals(atomResCouponCanUserRecordDTO.getCouponUseScope())) {
                    // 限指定商品使用
                    log.info("限指定商品使用-couponNo={}", atomResCouponCanUserRecordDTO.getCouponNo());
                    ExecuteDTO<List<String>> goodsExecuteDTO = atomCouponUserRecordAnalysisService.getCouponGoodsList(atomReqCouponUserRecordDTO);
                    if (goodsExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(goodsExecuteDTO.getData())) {
                        //配置信息不为空，判断配置商品编号是否包含当前订单中的商品
                        if (CollectionUtils.isEmpty(
                                CollectionUtils.intersection(
                                        goodsExecuteDTO.getData(),
                                        resGoodsDTOList.stream().map(AtomResGoodsDTO::getParentGoodsNo).distinct().collect(Collectors.toList())))) {
                            //商品不在用券范围内
                            notAvailableReason.add(MarketErrorCode.CODE_17001004.getInMsg());
                        }
                    } else {
                        notAvailableReason.add(MarketErrorCode.CODE_17001004.getInMsg());
                    }
                }

                // 代金券不可用原因：此券不支持千橙掌柜收银/汇享购下单使用
                if (atomResCouponCanUserRecordDTO.getCouponUseChannel().indexOf(reqVoucherListDTO.getCouponUseChannel()) < NumConstant.ZERO) {
                    if (CouponUseChannelEnum.COUPON_USE_CHANNEL_TWO.getCode().equals(reqVoucherListDTO.getCouponUseChannel())) {
                        notAvailableReason.add(MarketErrorCode.CODE_17001006.getInMsg());
                    } else {
                        notAvailableReason.add(MarketErrorCode.CODE_17001005.getInMsg());
                    }
                }

                // 代金券不可用原因：还未到券可用时间
                if (!CouponPeriodValidityEnum.COUPON_PERIOD_VALIDITY_ONE.getCode().equals(atomResCouponCanUserRecordDTO.getCouponPeriodValidity())
                        && currentTime.isBefore(atomResCouponCanUserRecordDTO.getCouponEffectiveTime())) {
                    notAvailableReason.add(MarketErrorCode.CODE_17001007.getInMsg());
                }

                // 代金券不可用原因：本单已使用优惠券，不可与代金券同时使用
                if (useCouponsFlag) {
                    notAvailableReason.add(MarketErrorCode.CODE_17001008.getInMsg());
                }

                // 代金券不可用原因：所选商品超过1款
                if (reqVoucherListDTO.getGoodsNoList().size() > NumConstant.ONE) {
                    notAvailableReason.add(MarketErrorCode.CODE_17001009.getInMsg());
                }

                // 代金券不可用原因：商品金额小于代金券金额门槛
                if (reqVoucherListDTO.getTotalAmount().compareTo(atomResCouponCanUserRecordDTO.getDiscountThreshold()) < NumConstant.ZERO) {
                    notAvailableReason.add(MarketErrorCode.CODE_17001010.getInMsg());
                }

                ResCouponCanUserRecordDTO resCouponCanUserRecordDTO = BeanCopierUtil.copy(atomResCouponCanUserRecordDTO, ResCouponCanUserRecordDTO.class);
                resCouponCanUserRecordDTO.setCouponUseScopeValue(CouponUseScopeEnum.getTypeByCode(resCouponCanUserRecordDTO.getCouponUseScope()));
                resCouponCanUserRecordDTO.setNotAvailableReason(notAvailableReason);
                if (CollectionUtils.isEmpty(notAvailableReason)) {
                    result.getListAvailable().add(resCouponCanUserRecordDTO);
                } else {
                    result.getListNotAvailable().add(resCouponCanUserRecordDTO);
                }
            }
        }

        return ExecuteDTO.success(result);
    }

    /**
     * @param reqCouponOrderDTO
     * @Description : 查询优惠券基本信息
     * <AUTHOR> 高繁
     * @date : 2021/5/10 17:45
     */
    @Override
    public ExecuteDTO<ResCouponDetailsDTO> getCouponInfo(ReqCouponOrderDTO reqCouponOrderDTO) {
        log.info("MarketProcess--CouponUserRecordAnalysisServiceImpl-getCouponInfo-param={}", JSON.toJSONString(reqCouponOrderDTO));
        AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO = BeanCopierUtil.copy(reqCouponOrderDTO, AtomReqCouponUserRecordDTO.class);
        ExecuteDTO<AtomResCouponOrderRecordDTO> executeDTO = atomCouponUserRecordAnalysisService.getCouponInfoByUser(atomReqCouponUserRecordDTO);
        log.info("MarketProcess--CouponUserRecordAnalysisServiceImpl-getCouponInfo-result={}", JSON.toJSONString(executeDTO));
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResCouponDetailsDTO resCouponDetailsDTO = BeanCopierUtil.copy(executeDTO.getData(), ResCouponDetailsDTO.class);
        return ExecuteDTO.success(resCouponDetailsDTO);
    }

    /**
     * @param reqCouponOrderDTO
     * @Description : 查询用户券编号对应的券基本信息
     * <AUTHOR> 卜金隆
     * @date : 2021/6/1 21:29
     */
    @Override
    public ExecuteDTO<ResCouponUserRecordDTO> getCouponDetailInfoByUser(ReqCouponOrderDTO reqCouponOrderDTO) {
        log.info("MarketProcess--CouponUserRecordAnalysisServiceImpl-getCouponDetailInfoByUser-param={}", JSON.toJSONString(reqCouponOrderDTO));
        AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO = BeanCopierUtil.copy(reqCouponOrderDTO, AtomReqCouponUserRecordDTO.class);
        ExecuteDTO<AtomResCouponUserRecordDTO> executeDTO = atomCouponUserRecordAnalysisService.getCouponDetailInfoByUser(atomReqCouponUserRecordDTO);
        log.info("MarketProcess--CouponUserRecordAnalysisServiceImpl-getCouponDetailInfoByUser-result={}", JSON.toJSONString(executeDTO));
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResCouponUserRecordDTO resCouponDetailsDTO = BeanCopierUtil.copy(executeDTO.getData(), ResCouponUserRecordDTO.class);
        if (resCouponDetailsDTO != null) {
            if (CouponPeriodValidityEnum.COUPON_PERIOD_VALIDITY_ONE.getCode().equals(resCouponDetailsDTO.getCouponPeriodValidity())) {
                resCouponDetailsDTO.setIsPastCoupon(NumConstant.ZERO);
            } else if (CouponPeriodValidityEnum.COUPON_PERIOD_VALIDITY_TWO.getCode().equals(resCouponDetailsDTO.getCouponPeriodValidity())
                    || CouponPeriodValidityEnum.COUPON_PERIOD_VALIDITY_THREE.getCode().equals(resCouponDetailsDTO.getCouponPeriodValidity())) {
                if (LocalDateTime.now().isAfter(resCouponDetailsDTO.getCouponInvalidTime())) {
                    resCouponDetailsDTO.setIsPastCoupon(NumConstant.ONE);
                } else {
                    resCouponDetailsDTO.setIsPastCoupon(NumConstant.ZERO);
                }
            }
            if (resCouponDetailsDTO.getIsPastCoupon() == null) {
                resCouponDetailsDTO.setIsPastCoupon(NumConstant.ONE);
            }
            if (StringUtils.isNotBlank(reqCouponOrderDTO.getFanNo()) && StringUtils.isNotBlank(reqCouponOrderDTO.getStoreNo())) {
                // 当前用户剩余可获得张数
                atomReqCouponUserRecordDTO.setPromotionNo(resCouponDetailsDTO.getPromotionNo());
                atomReqCouponUserRecordDTO.setCouponNo(resCouponDetailsDTO.getCouponNo());
                atomReqCouponUserRecordDTO.setStoreNo(reqCouponOrderDTO.getStoreNo());
                atomReqCouponUserRecordDTO.setFanNo(reqCouponOrderDTO.getFanNo());
                // 优惠券可获得次数配置
                ExecuteDTO<AtomResCouponRecordDTO> couponUserRecordCounts = atomCouponUserRecordAnalysisService.getCouponUserRecordCounts(atomReqCouponUserRecordDTO);
                if (null != couponUserRecordCounts && null != couponUserRecordCounts.getData() && null != couponUserRecordCounts.getData().getStoreUserTotalNum()) {
                    resCouponDetailsDTO.setUserRemainNum(resCouponDetailsDTO.getUserTotalNum() - couponUserRecordCounts.getData().getStoreUserTotalNum());
                } else {
                    resCouponDetailsDTO.setUserRemainNum(resCouponDetailsDTO.getUserTotalNum());
                }
            } else {
                resCouponDetailsDTO.setUserRemainNum(resCouponDetailsDTO.getUserTotalNum());
            }
            // 个人剩余购买数量-代金券用
            if (PromotionTypeCouponEnum.VOUCHER.getCode().equals(resCouponDetailsDTO.getPromotionCouponType())) {
                // 获取当前粉丝已购买数量
                CouponUserRecordVO couponUserRecordVO = new CouponUserRecordVO();
                couponUserRecordVO.setFanNo(reqCouponOrderDTO.getFanNo());
                couponUserRecordVO.setCouponNo(reqCouponOrderDTO.getCouponNo());
                couponUserRecordVO.setStoreNo(reqCouponOrderDTO.getStoreNo());
                int count = couponUserRecordDao.selectCouponCountByTerm(couponUserRecordVO);
                // 获取商品编号
                PromotionVirtualGoodsRelationVo queryVo = new PromotionVirtualGoodsRelationVo();
                queryVo.setPromotionNo(reqCouponOrderDTO.getCouponNo());
                PromotionVirtualGoodsRelationDomain goodsRelationDomain = this.promotionVirtualGoodsRelationDao.selectOneByParams(queryVo);
                if (goodsRelationDomain == null) {
                    return ExecuteDTO.error(GoodsErrorCode.CODE_12000001.getCode(), GoodsErrorCode.CODE_12000001.getInMsg());
                }
                // 获取当前粉丝购买但未支付的数量
                ReqOrderItemDTO reqOrderItemDTO = new ReqOrderItemDTO();
                reqOrderItemDTO.setGoodsNo(goodsRelationDomain.getGoodsNo());
                reqOrderItemDTO.setBuyerNo(reqCouponOrderDTO.getFanNo());
                ExecuteDTO<Integer> countExecuteDTO = this.orderAnalysisService.getVirtualGoodsSaleNum(reqOrderItemDTO);
                if (!countExecuteDTO.successFlag()) {
                    return ExecuteDTO.error(GoodsErrorCode.CODE_12000001.getCode(), "订单" + GoodsErrorCode.CODE_12000001.getInMsg());
                }
                Integer orderCount = countExecuteDTO.getData() == null ? NumConstant.ZERO : countExecuteDTO.getData();
                log.info("MarketProcess-CouponUserRecordAnalysisServiceImpl-getCouponDetailInfoByUser-count={}", count);
                log.info("MarketProcess-CouponUserRecordAnalysisServiceImpl-getCouponDetailInfoByUser-orderCount={}", orderCount);
                // 若购买数量>剩余限购数，则提示：“已超限购数量，你最多还可买X个”（X取值当前粉丝账户剩余可购数量）
                int userTotalNum = resCouponDetailsDTO.getUserTotalNum() == null ? NumConstant.ZERO : resCouponDetailsDTO.getUserTotalNum();
                int maxBuyNum = userTotalNum - count - orderCount;
                resCouponDetailsDTO.setUserRemainNum(Math.max(maxBuyNum, NumConstant.ZERO));
            }
            // 适用店铺列表
            List<CouponUserSuitStoreVO> suitStoreVOS = couponUserSuitStoreDao.querySuitStoreList(reqCouponOrderDTO.getUserCouponNo());
            if (ListUtil.isNotEmpty(suitStoreVOS)) {
                List<String> storeNos = suitStoreVOS.stream().map(CouponUserSuitStoreVO::getStoreNo).collect(Collectors.toList());
                ExecuteDTO<List<GetStoreListResponse>> resStoreListEo = userPublicService.queryStoreList(storeNos);
                if (null != resStoreListEo && ListUtil.isNotEmpty(resStoreListEo.getData())) {
                    List<ResPromotionStoreRelationDTO> suitStoreList = BeanCopierUtil.copyList(resStoreListEo.getData(), ResPromotionStoreRelationDTO.class);
                    resCouponDetailsDTO.setSuitStoreList(suitStoreList);
                }
            }
        }
        return ExecuteDTO.success(resCouponDetailsDTO);
    }

    /**
     * @param reqCouponCanUseRecordDTO
     * @Description : 查询用户+ 单个商品 可用的优惠券  （不加金额判断）
     * <AUTHOR> 卜金隆
     * @date : 2021/5/10 10:32
     */
    @Override
    public ExecuteDTO<List<ResCouponCanUserRecordDTO>> getCouponCanUseList(ReqCouponCanUseRecordDTO reqCouponCanUseRecordDTO) {
        log.info("MarketProcess--CouponUserRecordAnalysisServiceImpl-getCouponCanUseList-param={}", JSON.toJSONString(reqCouponCanUseRecordDTO));
        AtomReqCouponCanUseRecordDTO atomReqCouponCanUseRecordDTO = BeanCopierUtil.copy(reqCouponCanUseRecordDTO, AtomReqCouponCanUseRecordDTO.class);
        ExecuteDTO<List<AtomResCouponCanUserRecordDTO>> executeDTO = atomCouponUserRecordAnalysisService.getCouponCanUse(atomReqCouponCanUseRecordDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        List<AtomResCouponCanUserRecordDTO> effectiveCouponList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(executeDTO.getData())) {
            //查询商品主信息
            AtomReqGoodsDTO reqGoodsDTO = new AtomReqGoodsDTO();
            reqGoodsDTO.setNoPage();
            List<String> goodsNoList = new ArrayList<>();
            goodsNoList.add(reqCouponCanUseRecordDTO.getGoodsInfo().get(NumConstant.ZERO).getGoodsNo());
            reqGoodsDTO.setGoodsNos(goodsNoList);
            ExecuteDTO<ExecutePageDTO<AtomResGoodsDTO>> executePageDTOExecuteDTO = legacyGoodsCenterService.getGoodsPage(reqGoodsDTO);
            if (!executePageDTOExecuteDTO.successFlag() || executePageDTOExecuteDTO.getData() == null
                    || CollectionUtils.isEmpty(executePageDTOExecuteDTO.getData().getRows())) {
                return ExecuteDTO.error(executePageDTOExecuteDTO.getStatus(), executePageDTOExecuteDTO.getMsg());
            }
            AtomResGoodsDTO goodsDTO = executePageDTOExecuteDTO.getData().getRows().get(NumConstant.ZERO);

            ResGoodsCouponConditionDTO couponConditionDTO = this.couponInfoUtil.getResGoodsCouponConditionDTO1(executeDTO.getData());
            //店铺指定品牌
            Map<String, List<String>> couponBrandList = couponConditionDTO.getCouponBrandList();
            //店铺指定类目
            Map<String, List<String>> couponCategoryList = couponConditionDTO.getCouponCategoryList();
            //券指定商品
            Map<String, List<String>> couponGoodsList = couponConditionDTO.getCouponGoodsList();
            for (AtomResCouponCanUserRecordDTO atomResCouponCanUserRecordDTO : executeDTO.getData()) {
                if (GoodsSourceTypeEnum.CLOUD_POOL_DISTRIBUTE.getCode().equals(goodsDTO.getGoodsSourceType())) {
                    log.info("云池商品基本信息:{}", JSON.toJSONString(goodsDTO));
                    //云池券
                    if (CouponUseScopeEnum.COUPON_USE_SCOPE_TWO.getCode().equals(atomResCouponCanUserRecordDTO.getCouponUseScope())) {
                        effectiveCouponList.add(atomResCouponCanUserRecordDTO);
                    } else if (CouponUseScopeEnum.COUPON_USE_SCOPE_THREE.getCode().equals(atomResCouponCanUserRecordDTO.getCouponUseScope())) {
                        //指定云池商品,查询券设置的云池商品编号
                        //店铺指定类目
                        List<String> goodsNo = couponGoodsList.get(atomResCouponCanUserRecordDTO.getCouponNo());
                        if (CollectionUtils.isNotEmpty(goodsNo) && goodsNo.contains(goodsDTO.getCloudPoolGoodsNo())) {
                            effectiveCouponList.add(atomResCouponCanUserRecordDTO);
                        }
                    }
                } else if (GoodsSourceTypeEnum.STORE.getCode().equals(goodsDTO.getGoodsSourceType()) && StringUtils.isNotBlank(goodsDTO.getMerchantGoodsNo())) {
                    // 商家商品通用
                    if (CouponUseScopeEnum.COUPON_USE_SCOPE_ONE.getCode().equals(atomResCouponCanUserRecordDTO.getCouponUseScope())
                            || CouponUseScopeEnum.COUPON_USE_SCOPE_EIGHT.getCode().equals(atomResCouponCanUserRecordDTO.getCouponUseScope())) {
                        effectiveCouponList.add(atomResCouponCanUserRecordDTO);
                    } else if (CouponUseScopeEnum.COUPON_USE_SCOPE_NINE.getCode().equals(atomResCouponCanUserRecordDTO.getCouponUseScope())) {
                        // 指定商家商品
                        List<String> goodsNos = couponGoodsList.get(atomResCouponCanUserRecordDTO.getCouponNo());
                        if (CollectionUtils.isNotEmpty(goodsNos) && goodsNos.contains(goodsDTO.getMerchantGoodsNo())) {
                            effectiveCouponList.add(atomResCouponCanUserRecordDTO);
                        }
                    }
                } else {
                    if (CouponUseScopeEnum.COUPON_USE_SCOPE_ONE.getCode().equals(atomResCouponCanUserRecordDTO.getCouponUseScope())) {
                        effectiveCouponList.add(atomResCouponCanUserRecordDTO);
                    } else if (CouponUseScopeEnum.COUPON_USE_SCOPE_FIVE.getCode().equals(atomResCouponCanUserRecordDTO.getCouponUseScope())) {
                        List<String> brands = couponBrandList.get(atomResCouponCanUserRecordDTO.getCouponNo());
                        if (CollectionUtils.isNotEmpty(brands) && brands.contains(goodsDTO.getBrandNo())) {
                            effectiveCouponList.add(atomResCouponCanUserRecordDTO);
                        }
                    } else if (CouponUseScopeEnum.COUPON_USE_SCOPE_FOUR.getCode().equals(atomResCouponCanUserRecordDTO.getCouponUseScope())) {
                        //店铺指定类目
                        List<String> category = couponCategoryList.get(atomResCouponCanUserRecordDTO.getCouponNo());
                        if (CollectionUtils.isNotEmpty(category) && category.contains(goodsDTO.getCategoryNo())) {
                            effectiveCouponList.add(atomResCouponCanUserRecordDTO);
                        }
                    } else if (CouponUseScopeEnum.COUPON_USE_SCOPE_SIX.getCode().equals(atomResCouponCanUserRecordDTO.getCouponUseScope())) {
                        //店铺指定商品
                        List<String> goodsNo = couponGoodsList.get(atomResCouponCanUserRecordDTO.getCouponNo());
                        if (CollectionUtils.isNotEmpty(goodsNo) && goodsNo.contains(goodsDTO.getGoodsNo())) {
                            effectiveCouponList.add(atomResCouponCanUserRecordDTO);
                        }
                    }
                }
            }
        }
        return ExecuteDTO.success(BeanCopierUtil.copyList(effectiveCouponList, ResCouponCanUserRecordDTO.class));
    }

    /**
     * @param reqCouponUserRecordDTO
     * @Description : hxg-我的优惠券数量统计
     * <AUTHOR> 卜金隆
     * @date : 2021/5/11 17:01
     */
    @Override
    public ExecuteDTO<ResUserCouponNumDTO> getMyCouponNum(ReqCouponUserRecordDTO reqCouponUserRecordDTO) {
        log.info("MarketProcess--CouponUserRecordAnalysisServiceImpl-getMyCouponNum-param={}", JSON.toJSONString(reqCouponUserRecordDTO));
        this.couponSettingAssert.getMyCouponList(reqCouponUserRecordDTO);

        // 我的优惠券数量统计  未使用
        AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO = new AtomReqCouponUserRecordDTO();
        atomReqCouponUserRecordDTO.setFanNo(reqCouponUserRecordDTO.getFanNo());
        atomReqCouponUserRecordDTO.setUseFlag(NumConstant.ONE);
        atomReqCouponUserRecordDTO.setMyCouponType(CouponListTypeEnum.COUPON_APPLY.getCode());
        if (CouponListTypeEnum.COUPON_APPLY.getCode().equals(reqCouponUserRecordDTO.getMyCouponType())) {
            atomReqCouponUserRecordDTO.setStoreNo(reqCouponUserRecordDTO.getStoreNo());
        }
        atomReqCouponUserRecordDTO.setCouponType(reqCouponUserRecordDTO.getCouponType());
        atomReqCouponUserRecordDTO.setMerchantNo(reqCouponUserRecordDTO.getMerchantNo());
        ExecuteDTO<Integer> applyNum = atomCouponUserRecordAnalysisService.getMyCouponNum(atomReqCouponUserRecordDTO);
        log.info("MarketProcess-CouponUserRecordAnalysisServiceImpl-getMyCouponNum-我的优惠券未使用return={}", JSON.toJSONString(applyNum));
        //判断状态
        if (!applyNum.successFlag()) {
            return ExecuteDTO.error(applyNum.getStatus(), applyNum.getMsg());
        }
        // 我的优惠券数量统计  已使用
        AtomReqCouponUserRecordDTO alreadyParam = new AtomReqCouponUserRecordDTO();
        alreadyParam.setFanNo(reqCouponUserRecordDTO.getFanNo());
        alreadyParam.setUseFlag(NumConstant.TWO);
        alreadyParam.setMyCouponType(CouponListTypeEnum.COUPON_ALREADY.getCode());
        if (CouponListTypeEnum.COUPON_ALREADY.getCode().equals(reqCouponUserRecordDTO.getMyCouponType())) {
            alreadyParam.setStoreNo(reqCouponUserRecordDTO.getStoreNo());
        }
        alreadyParam.setCouponType(reqCouponUserRecordDTO.getCouponType());
        alreadyParam.setMerchantNo(reqCouponUserRecordDTO.getMerchantNo());

        ExecuteDTO<Integer> alreadyNum = atomCouponUserRecordAnalysisService.getMyCouponNum(alreadyParam);
        log.info("MarketProcess-CouponUserRecordAnalysisServiceImpl-getMyCouponNum-我的优惠券已使用return={}", JSON.toJSONString(alreadyNum));
        //判断状态
        if (!alreadyNum.successFlag()) {
            return ExecuteDTO.error(alreadyNum.getStatus(), alreadyNum.getMsg());
        }
        // 我的优惠券数量统计  已过期
        AtomReqCouponUserRecordDTO pastParam = new AtomReqCouponUserRecordDTO();
        pastParam.setFanNo(reqCouponUserRecordDTO.getFanNo());
        pastParam.setMyCouponType(CouponListTypeEnum.COUPON_PAST.getCode());
        if (CouponListTypeEnum.COUPON_PAST.getCode().equals(reqCouponUserRecordDTO.getMyCouponType())) {
            pastParam.setStoreNo(reqCouponUserRecordDTO.getStoreNo());
        }
        pastParam.setCouponType(reqCouponUserRecordDTO.getCouponType());
        pastParam.setMerchantNo(reqCouponUserRecordDTO.getMerchantNo());

        ExecuteDTO<Integer> pastNum = atomCouponUserRecordAnalysisService.getMyCouponNum(pastParam);
        log.info("MarketProcess-CouponUserRecordAnalysisServiceImpl-getMyCouponNum-我的优惠券已过期return={}", JSON.toJSONString(pastNum));
        //判断状态
        if (!pastNum.successFlag()) {
            return ExecuteDTO.error(pastNum.getStatus(), pastNum.getMsg());
        }
        ResUserCouponNumDTO resUserCouponNumDTO = new ResUserCouponNumDTO();
        resUserCouponNumDTO.setApplyCouponNum(applyNum.getData());
        resUserCouponNumDTO.setAlreadyCouponNum(alreadyNum.getData());
        resUserCouponNumDTO.setPastouponNum(pastNum.getData());
        return ExecuteDTO.success(resUserCouponNumDTO);
    }

    /**
     * @param reqCouponUserRecordDTO
     * @param reqCouponUserRecordDTO myCouponType  1001：未使用 1002：已使用： 1003：已过期
     * @Description : hxg-我的优惠券列表
     * <AUTHOR> 卜金隆
     * @date : 2021/5/11 17:01
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResMyCouponInfoDTO>> getMyCouponList(ReqCouponUserRecordDTO reqCouponUserRecordDTO) {
        log.info("MarketProcess--CouponUserRecordAnalysisServiceImpl-getMyCouponList-param={}", JSON.toJSONString(reqCouponUserRecordDTO));
        this.couponSettingAssert.getMyCouponList(reqCouponUserRecordDTO);
        ExecutePageDTO<ResMyCouponInfoDTO> executePageDTO = new ExecutePageDTO<>();
        AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO = BeanCopierUtil.copy(reqCouponUserRecordDTO, AtomReqCouponUserRecordDTO.class);
        // 添加券使用状态
        if (CouponListTypeEnum.COUPON_APPLY.getCode().equals(reqCouponUserRecordDTO.getMyCouponType())) {
            atomReqCouponUserRecordDTO.setUseFlag(NumConstant.ONE);
        } else if (CouponListTypeEnum.COUPON_ALREADY.getCode().equals(reqCouponUserRecordDTO.getMyCouponType())) {
            atomReqCouponUserRecordDTO.setUseFlag(NumConstant.TWO);
        }
        // 我的优惠券
        ExecuteDTO<ExecutePageDTO<AtomResCouponUserRecordDTO>> myCouponList = atomCouponUserRecordAnalysisService.getMyCouponListWithPage(atomReqCouponUserRecordDTO);
        log.info("MarketProcess-CouponUserRecordAnalysisServiceImpl-getMyCouponList-我的优惠券列表return={}", JSON.toJSONString(myCouponList));
        //判断状态
        if (!myCouponList.successFlag()) {
            return ExecuteDTO.error(myCouponList.getStatus(), myCouponList.getMsg());
        }
        List<ResCouponUserRecordDTO> resCouponUserRecordDTOS = BeanCopierUtil.copyList(myCouponList.getData().getRows(), ResCouponUserRecordDTO.class);
        if (CollectionUtils.isEmpty(resCouponUserRecordDTOS)) {
            executePageDTO.setTotal(NumConstant.ZERO);
            executePageDTO.setRows(new ArrayList<>());
            return ExecuteDTO.success(executePageDTO);
        }
        // 处理代金券列表
        if (StringUtils.isNotBlank(reqCouponUserRecordDTO.getCouponType()) && CouponTypeEnum.VOUCHER.getCode().equals(reqCouponUserRecordDTO.getCouponType())) {
            List<String> userCouponNoList = resCouponUserRecordDTOS.stream().map(ResCouponUserRecordDTO::getUserCouponNo).collect(Collectors.toList());
            ReqSoPromotionDTO reqSoPromotionDTO = new ReqSoPromotionDTO();
            reqSoPromotionDTO.setUserCouponNoList(userCouponNoList);
            ExecuteDTO<List<ResSoPromotionItemDTO>> listExecuteDTO = this.orderPromotionAnalysisService.getOrderItemsByUserCouponNos(reqSoPromotionDTO);
            if (!listExecuteDTO.successFlag()) {
                return ExecuteDTO.error(myCouponList.getStatus(), myCouponList.getMsg());
            }
            if (CollectionUtils.isNotEmpty(listExecuteDTO.getData())) {
                Map<String, List<ResSoPromotionItemDTO>> userCouponNoGroupMap = listExecuteDTO.getData().stream().collect(Collectors.groupingBy(ResSoPromotionItemDTO::getUserCouponNo));
                resCouponUserRecordDTOS.stream().forEach(dto -> {
                    List<ResSoPromotionItemDTO> resSoPromotionItemDTOs = userCouponNoGroupMap.get(dto.getUserCouponNo()) == null ? null : userCouponNoGroupMap.get(dto.getUserCouponNo());
                    if (resSoPromotionItemDTOs != null) {
                        String goodsNames = resSoPromotionItemDTOs.stream().map(ResSoPromotionItemDTO::getGoodsName).collect(Collectors.joining(","));
                        dto.setGoodsNames(goodsNames);
                        // @TODO 目前商品只对应一个，以后支持多个时可以再优化
                        dto.setCouponOrderNo(resSoPromotionItemDTOs.get(NumConstant.ZERO).getOrderNo());
                    }
                });
            }

        }
        // 处理券标识字段
        this.setCouponFlag(resCouponUserRecordDTOS, reqCouponUserRecordDTO);
        //参数转换
        if (CollectionUtils.isNotEmpty(resCouponUserRecordDTOS)) {
            resCouponUserRecordDTOS.stream().forEach(dto -> {
                dto.setHxgCouponEffectiveTime(dto.getCouponEffectiveTime());
                dto.setHxgCouponInvalidTime(dto.getCouponInvalidTime());
            });
        }
        List<ResMyCouponInfoDTO> resMyCouponInfoDTOS = BeanCopierUtil.copyList(resCouponUserRecordDTOS, ResMyCouponInfoDTO.class);
        executePageDTO.setRows(resMyCouponInfoDTOS);
        executePageDTO.setTotal(myCouponList.getData().getTotal());
        log.info("MarketProcess-CouponUserRecordAnalysisServiceImpl-getMyCouponList-我的优惠券列表end");
        return ExecuteDTO.success(executePageDTO);
    }

    /**
     * @param reqCouponUserRecordDTO
     * @param reqCouponUserRecordDTO myCouponType 1001：未使用 1002：已使用： 1003：已过期
     * @Description : 一体机粉丝优惠券列表
     * <AUTHOR> hxj
     * @date : 2021/8/12 15:53
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResCouponUserRecordDTO>> getMyCouponListWithPage(ReqCouponUserRecordDTO reqCouponUserRecordDTO) {
        log.info("MarketProcess--CouponUserRecordAnalysisServiceImpl-getMyCouponListWithPage-param={}", JSON.toJSONString(reqCouponUserRecordDTO));
        ExecutePageDTO<ResCouponUserRecordDTO> executePageDTO = new ExecutePageDTO();
        this.couponSettingAssert.getMyCouponList(reqCouponUserRecordDTO);
        // 店铺名称
//        Map<String, String> storeNameMap = new HashMap<>();
        AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO = BeanCopierUtil.copy(reqCouponUserRecordDTO, AtomReqCouponUserRecordDTO.class);
        // 添加券使用状态
        if (CouponListTypeEnum.COUPON_APPLY.getCode().equals(reqCouponUserRecordDTO.getMyCouponType())) {
            atomReqCouponUserRecordDTO.setUseFlag(NumConstant.ONE);
        } else if (CouponListTypeEnum.COUPON_ALREADY.getCode().equals(reqCouponUserRecordDTO.getMyCouponType())) {
            atomReqCouponUserRecordDTO.setUseFlag(NumConstant.TWO);
        }
        // 我的优惠券
        ExecuteDTO<ExecutePageDTO<AtomResCouponUserRecordDTO>> myCouponList = atomCouponUserRecordAnalysisService.getMyCouponListWithPage(atomReqCouponUserRecordDTO);
        log.info("MarketProcess-CouponUserRecordAnalysisServiceImpl-getMyCouponList-我的优惠券列表return={}", JSON.toJSONString(myCouponList));
        //判断状态
        if (!myCouponList.successFlag()) {
            return ExecuteDTO.error(myCouponList.getStatus(), myCouponList.getMsg());
        }
        List<ResCouponUserRecordDTO> resCouponUserRecordDTOS = BeanCopierUtil.copyList(myCouponList.getData().getRows(), ResCouponUserRecordDTO.class);
        if (CollectionUtils.isEmpty(resCouponUserRecordDTOS)) {
            return ExecuteDTO.success();
        }
        // 处理券标识字段
        this.setCouponFlag(resCouponUserRecordDTOS, reqCouponUserRecordDTO);
        log.info("MarketProcess-CouponUserRecordAnalysisServiceImpl-getMyCouponListWithPage-我的优惠券列表返回return={}", JSON.toJSONString(resCouponUserRecordDTOS));
        executePageDTO.setTotal(myCouponList.getData().getTotal());
        executePageDTO.setRows(resCouponUserRecordDTOS);
        return ExecuteDTO.success(executePageDTO);
    }

    /**
     * @param reqCouponUserRecordDTO
     * @Description : hxg-我的 给粉丝发券列表
     * <AUTHOR> 卜金隆
     * @date : 2021/6/3 17:01
     */
    @Override
    public ExecuteDTO<List<ResCouponUserRecordDTO>> getMySendFanCouponList(ReqCouponUserRecordDTO reqCouponUserRecordDTO) {
        log.info("MarketProcess--CouponUserRecordAnalysisServiceImpl-getMySendFanCouponList-param={}", JSON.toJSONString(reqCouponUserRecordDTO));
        if (StringUtils.isBlank(reqCouponUserRecordDTO.getFanNo())) {
            throw new BaseException(CommonCode.CODE_10000001, "代理人");
        }
        AgentInfoRequest agentInfoRequest = new AgentInfoRequest();
        agentInfoRequest.setFanNo(reqCouponUserRecordDTO.getFanNo());
        ExecuteDTO<AgentInfoResponse> agentExecuteDTO = agentProcessService.getAgentInfo(agentInfoRequest);
        if (!agentExecuteDTO.successFlag()) {
            return ExecuteDTO.error(agentExecuteDTO.getStatus(), agentExecuteDTO.getMsg());
        }
        AgentInfoResponse resAgentDTO = agentExecuteDTO.getData();
        if (resAgentDTO == null) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000001, "该代理人不存在");
        }

        AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO = BeanCopierUtil.copy(reqCouponUserRecordDTO, AtomReqCouponUserRecordDTO.class);
        // 添加券使用状态
        atomReqCouponUserRecordDTO.setUseFlag(NumConstant.ONE);
        // 代理人发券类型   3001:平台-手动发代理人券3003:平台-网店代理人领券
        List<String> promotionCouponTypes = new ArrayList<>();
        // 粉丝平台+粉丝店铺
        promotionCouponTypes.add(PromotionTypeCouponEnum.PLATFORM_MANUAL_AGENT_COUPON.getCode());
        promotionCouponTypes.add(PromotionTypeCouponEnum.PLATFORM_ONLINE_AGENT_COUPON.getCode());
        atomReqCouponUserRecordDTO.setPromotionCouponTypeList(promotionCouponTypes);
        atomReqCouponUserRecordDTO.setAgentPresentFlag(NumConstant.ONE);
        atomReqCouponUserRecordDTO.setMyCouponType(CouponListTypeEnum.COUPON_APPLY.getCode());
        //代理人店铺
        atomReqCouponUserRecordDTO.setStoreNo(resAgentDTO.getStoreNo());
        // 不过滤不可自用券
        atomReqCouponUserRecordDTO.setCouponShowType("1002");
        // 我的优惠券
        ExecuteDTO<List<AtomResCouponUserRecordDTO>> myCouponList = atomCouponUserRecordAnalysisService.getMyCouponList(atomReqCouponUserRecordDTO);
        //判断状态
        if (!myCouponList.successFlag()) {
            return ExecuteDTO.error(myCouponList.getStatus(), myCouponList.getMsg());
        }
        List<ResCouponUserRecordDTO> resCouponUserRecordDTOS = BeanCopierUtil.copyList(myCouponList.getData(), ResCouponUserRecordDTO.class);
        if (CollectionUtils.isEmpty(resCouponUserRecordDTOS)) {
            return ExecuteDTO.success();
        }
        // 处理券标识字段
        this.setCouponFlag(resCouponUserRecordDTOS, reqCouponUserRecordDTO);
        log.info("MarketProcess-CouponUserRecordAnalysisServiceImpl-getMySendFanCouponList-给粉丝发券列表return={}", JSON.toJSONString(resCouponUserRecordDTOS));
        return ExecuteDTO.success(resCouponUserRecordDTOS);
    }

    @Override
    public ExecuteDTO<ExecutePageDTO<ResCouponStoreNumDTO>> getMyCouponStoreNo(ReqCouponUserRecordDTO reqCouponUserRecordDTO) {
        log.info("MarketProcess--CouponUserRecordAnalysisServiceImpl-getMyCouponStoreNo-param={}", JSON.toJSONString(reqCouponUserRecordDTO));
        ExecutePageDTO<ResCouponStoreNumDTO> executePageDTO = new ExecutePageDTO<>();
        AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO = BeanCopierUtil.copy(reqCouponUserRecordDTO, AtomReqCouponUserRecordDTO.class);
        // 添加券使用状态
        if (CouponListTypeEnum.COUPON_APPLY.getCode().equals(reqCouponUserRecordDTO.getMyCouponType())) {
            atomReqCouponUserRecordDTO.setUseFlag(NumConstant.ONE);
        } else if (CouponListTypeEnum.COUPON_ALREADY.getCode().equals(reqCouponUserRecordDTO.getMyCouponType())) {
            atomReqCouponUserRecordDTO.setUseFlag(NumConstant.TWO);
        }
        ExecuteDTO<ExecutePageDTO<AtomResCouponStoreNumDTO>> myCouponStoreNo = atomCouponUserRecordAnalysisService.getMyCouponStoreNo(atomReqCouponUserRecordDTO);
        //判断状态
        if (!myCouponStoreNo.successFlag()) {
            return ExecuteDTO.error(myCouponStoreNo.getStatus(), myCouponStoreNo.getMsg());
        }
        if (CollectionUtils.isEmpty(myCouponStoreNo.getData().getRows())) {
            executePageDTO.setTotal(NumConstant.ZERO);
            executePageDTO.setRows(new ArrayList<>());
            return ExecuteDTO.success(executePageDTO);
        }
        List<ResCouponStoreNumDTO> couponStoreNumDTOS = BeanCopierUtil.copyList(myCouponStoreNo.getData().getRows(), ResCouponStoreNumDTO.class);
        // 店铺编码查店铺名称
        List<String> storeNoList = couponStoreNumDTOS.stream().map(ResCouponStoreNumDTO::getStoreNo).collect(Collectors.toList());
        //获取店铺集合
        log.info("MarketProcess--CouponUserRecordAnalysisServiceImpl-getMyCouponStoreNo-param={}", JSON.toJSONString(storeNoList));
        ExecuteDTO<List<GetStoreListResponse>> storeListDTO = userPublicService.queryStoreList(storeNoList);
        if (!storeListDTO.successFlag()) {
            return ExecuteDTO.error(storeListDTO.getStatus(), storeListDTO.getMsg());
        }
        if (CollectionUtils.isEmpty(storeListDTO.getData())) {
            log.info("MarketProcess--userPublicService-queryStoreList-return={}", JSON.toJSONString(storeListDTO));
            executePageDTO.setTotal(NumConstant.ZERO);
            executePageDTO.setRows(new ArrayList<>());
            return ExecuteDTO.success(executePageDTO);
        }
        List<GetStoreListResponse> storeInfos = storeListDTO.getData();
        couponStoreNumDTOS.forEach(couponDTOS -> {
            storeInfos.forEach(storeInfo -> {
                if (couponDTOS.getStoreNo().equals(storeInfo.getStoreNo())) {
                    couponDTOS.setStoreName(storeInfo.getStoreName());
                }
            });
        });
        executePageDTO.setRows(couponStoreNumDTOS);
        executePageDTO.setTotal(myCouponStoreNo.getData().getTotal());
        log.info("MarketProcess-CouponUserRecordAnalysisServiceImpl-getMySendFanCouponList-我的优惠券每个店铺的张数end");
        return ExecuteDTO.success(executePageDTO);
    }

    /**
     * @param reqCouponUserRecordDTO
     * @Description : 获取店铺内，粉丝可用优惠券数量
     * <AUTHOR> 高繁
     * @date : 2021/11/5 17:22
     */
    @Override
    public ExecuteDTO<Integer> getFansCanUseCouponNum(ReqCouponUserRecordDTO reqCouponUserRecordDTO) {
        log.info("----CouponUserRecordAnalysisServiceImpl---getFansCanUseCouponNum---params:{}", JSON.toJSONString(reqCouponUserRecordDTO));
        this.couponSettingAssert.getUserUseCouponNumAssert(reqCouponUserRecordDTO);
        AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO = BeanCopierUtil.copy(reqCouponUserRecordDTO, AtomReqCouponUserRecordDTO.class);
        ExecuteDTO<Integer> executeDTO = atomCouponUserRecordAnalysisService.getFansCanUseCouponNum(atomReqCouponUserRecordDTO);
        log.info("----CouponUserRecordAnalysisServiceImpl---getFansCanUseCouponNum---executeDTO:{}", JSON.toJSONString(executeDTO));
        return executeDTO;
    }

    /**
     * 查询店铺内, 粉丝可用的满减券和代金券数量
     *
     * @param reqCouponUserRecordDTO 请求参数
     * @return 结果
     */
    @Override
    public ExecuteDTO<ResCouponStaticDTO> getFansCanUseCouponCount(ReqCouponUserRecordDTO reqCouponUserRecordDTO) {
        log.info("----CouponUserRecordAnalysisServiceImpl---getFansCanUseCouponCount---params:{}", JSON.toJSONString(reqCouponUserRecordDTO));

        List<ResCouponStaticDTO> list = new ArrayList<>();
        CouponUserRecordDomain couponUserRecordDomain = BeanCopierUtil.copy(reqCouponUserRecordDTO, CouponUserRecordDomain.class);
        List<CouponUserRecordVO> userRecordVOList = couponUserRecordDao.selectFansCanUseCouponCount(couponUserRecordDomain);

        // 返回的数据
        ResCouponStaticDTO resCouponStaticDTO = new ResCouponStaticDTO();

        if (CollectionUtils.isEmpty(userRecordVOList)) {
            return ExecuteDTO.ok(resCouponStaticDTO);
        }

        // 目前只有满减券、折扣券和代金券
        Optional<CouponUserRecordVO> reduceCouponOptional = userRecordVOList.stream().
                filter(userRecordVO -> CouponTypeEnum.COUPON_REDUCE.getCode().
                        equals(userRecordVO.getCouponType())).findFirst();

        // 获取满减券的数量
        if (reduceCouponOptional.isPresent()) {
            Integer reduceCouponNum = reduceCouponOptional.get().getCouponNum();
            resCouponStaticDTO.setReduceCouponNum(reduceCouponNum);
        }

        // 获取代金券的数量
        Optional<CouponUserRecordVO> voucherCouponOptional = userRecordVOList.stream().
                filter(userRecordVO -> CouponTypeEnum.VOUCHER.getCode().
                        equals(userRecordVO.getCouponType())).findFirst();
        if (voucherCouponOptional.isPresent()) {
            Integer voucherCouponNum = voucherCouponOptional.get().getCouponNum();
            resCouponStaticDTO.setVoucherCouponNum(voucherCouponNum);
        }

        //20230928蛋品-吴鑫鑫-营销管理-优惠券折扣券
        // 获取折扣券的数量
        Optional<CouponUserRecordVO> discountCouponOptional = userRecordVOList.stream().
                filter(userRecordVO -> CouponTypeEnum.COUPON_DISCONUT.getCode().
                        equals(userRecordVO.getCouponType())).findFirst();
        if (discountCouponOptional.isPresent()) {
            Integer discountCouponNum = discountCouponOptional.get().getCouponNum();
            resCouponStaticDTO.setDiscountCouponNum(discountCouponNum);
        }

        log.info("----CouponUserRecordAnalysisServiceImpl---getFansCanUseCouponCount---结果:{}", JSON.toJSONString(resCouponStaticDTO));
        return ExecuteDTO.ok(resCouponStaticDTO);
    }

    @Override
    public ExecuteDTO<Integer> getUnusedCouponCount(ReqCouponUserRecordDTO reqCouponUserRecordDTO) {
        log.info("----CouponUserRecordAnalysisServiceImpl---getUnusedCouponCount---param----{}", JSON.toJSONString(reqCouponUserRecordDTO));
        AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO = BeanCopierUtil.copy(reqCouponUserRecordDTO, AtomReqCouponUserRecordDTO.class);
        ExecuteDTO<Integer> unusedCOuntExecuteDTO = atomCouponUserRecordAnalysisService.getUnusedCouponCount(atomReqCouponUserRecordDTO);
        if (unusedCOuntExecuteDTO.successFlag()
                && null != unusedCOuntExecuteDTO.getData()
                && unusedCOuntExecuteDTO.getData() > 0) {
            return ExecuteDTO.error(BossLogoffErrorEnum.UNUSED_COUPONS, unusedCOuntExecuteDTO.getData());
        }
        return ExecuteDTO.success();
    }

    @Override
    public ExecuteDTO<Integer> checkCouponRecord(ReqCouponUserRecordOrderDTO reqCouponUserRecordOrderDTO) {
        log.info("MarketProcess-CouponPromotionAnalysisServiceImpl-checkCouponRecord-params-start");
        log.info("MarketProcess-CouponPromotionAnalysisServiceImpl-checkCouponRecord-params={}", JSON.toJSONString(reqCouponUserRecordOrderDTO));
        ExecuteDTO executeDTO = couponUserRecordAssert.checkCouponRecordAssert(reqCouponUserRecordOrderDTO);
        if (!executeDTO.successFlag()) {
            return executeDTO;
        }
        CouponUserRecordVO couponUserRecordVO = new CouponUserRecordVO();
        couponUserRecordVO.setCouponNo(reqCouponUserRecordOrderDTO.getCouponNo());
        couponUserRecordVO.setOrderNo(reqCouponUserRecordOrderDTO.getOrderNo());
        List<CouponUserRecordVO> userRecordVOs = this.couponUserRecordDao.selectCouponUserRecords(couponUserRecordVO);
        log.info("MarketProcess-CouponPromotionAnalysisServiceImpl-checkCouponRecord-userRecordVOs={}", JSON.toJSONString(userRecordVOs));
        if (cn.htdt.common.utils.CollectionUtils.isEmpty(userRecordVOs)) {
            log.info("checkCouponRecord-校验无券，无需退券，订单号={}", reqCouponUserRecordOrderDTO.getOrderNo());
            return ExecuteDTO.success();
        }
        // 过滤已使用的券
        List<CouponUserRecordVO> notUserRecordVOs = userRecordVOs.stream().filter(d -> WhetherEnum.NO.getCode().equals(d.getUseFlag())).collect(Collectors.toList());
        // 代金券均被使用，判断代金券已全部被使用，是否确认退款？
        if (cn.htdt.common.utils.CollectionUtils.isEmpty(notUserRecordVOs)) {
            log.info("判断代金券已全部被使用，是否确认退款？");
            return ExecuteDTO.error(MarketErrorCode.CODE_17001011);
        }
        // 申请售后数量>购买的数量
        if (reqCouponUserRecordOrderDTO.getGoodsNum() > userRecordVOs.size()) {
            log.info("申请售后数量>购买的数量");
            return ExecuteDTO.error(MarketErrorCode.CODE_17001011);
        }
        // 获取已使用的券
        List<CouponUserRecordVO> useUserRecordVOs = userRecordVOs.stream().filter(d -> WhetherEnum.YES.getCode().equals(d.getUseFlag())).collect(Collectors.toList());
        // 校验存在部分被使用XX张代金券已使用，是否确认退款？
        if (CollectionUtils.isNotEmpty(useUserRecordVOs) && reqCouponUserRecordOrderDTO.getGoodsNum() > notUserRecordVOs.size()) {
            log.info("校验存在部分被使用XX张代金券已使用，是否确认退款？");
            return ExecuteDTO.error(MarketErrorCode.CODE_17001012.getCode(), String.format(MarketErrorCode.CODE_17001012.getShowMsg(), useUserRecordVOs.size()));
        }
        return ExecuteDTO.success();
    }

    /**
     * BossApp，店铺&单店角色 -> 工作台 -> 数据报表 -> 营销权益效果 -> 券类权益触达效果
     * 或者 -> 卖货 -> 我要看数据 -> 营销权益触达
     *
     * @param reqCouponUserRecordDTO 请求参数
     * @return 店铺的券的统计情况
     */
    @Override
    public ExecuteDTO<ResCouponEffectDTO> getStoreCouponStaticByParam(ReqCouponUserRecordDTO reqCouponUserRecordDTO) {
        log.info("getStoreCouponStaticByParam--start, reqCouponUserRecordDTO: {}", reqCouponUserRecordDTO);

        ExecuteDTO executeDTO = couponUserRecordAssert.checkStoreCouponStaticAssert(reqCouponUserRecordDTO);
        if (!executeDTO.successFlag()) {
            return executeDTO;
        }

        CouponUserRecordDomain couponUserRecordDomain = BeanCopierUtil.copy(reqCouponUserRecordDTO, CouponUserRecordDomain.class);
        // 查询发放券总数和已使用券总数
        CouponUserRecordStaticVO storeCouponStatic = couponUserRecordDao.getStoreCouponStaticByParam(couponUserRecordDomain);
        log.info("getStoreCouponStaticByParam----> storeCouponStatic: {}", storeCouponStatic);
        if (null == storeCouponStatic) {
            log.error("getStoreCouponStaticByParam----> storeCouponStatic result is null");
            storeCouponStatic = new CouponUserRecordStaticVO();
            storeCouponStatic.setProvideCouponTotal(NumConstant.ZERO);
            storeCouponStatic.setUseCouponTotal(NumConstant.ZERO);
        }

        // 返回的数据
        ResCouponEffectDTO resCouponEffectDTO = BeanCopierUtil.copy(storeCouponStatic, ResCouponEffectDTO.class);

        // 查询领券粉丝数和用券粉丝数, 已排除无效粉丝
        ReqMarketTouchingEffectVO reqMarketTouchingEffectVO = new ReqMarketTouchingEffectVO();
        reqMarketTouchingEffectVO.setStoreNo(reqCouponUserRecordDTO.getStoreNo());
        reqMarketTouchingEffectVO.setMarketDatabaseName(dataBaseUtil.getMarketDataBaseName());
        reqMarketTouchingEffectVO.setUserDatabaseName(dataBaseUtil.getUserDataBaseName());
        CouponUserRecordStaticVO storeCouponStaticVO = couponUserRecordEffectDao.getStoreCouponStaticByParam(reqMarketTouchingEffectVO);

        if (null == storeCouponStaticVO) {
            log.error("getStoreCouponStaticByParam----> storeCouponStaticVO is null");
            storeCouponStaticVO = new CouponUserRecordStaticVO();
            storeCouponStaticVO.setCollectCouponFansNum(NumConstant.ZERO);
            storeCouponStaticVO.setUseCouponTotal(NumConstant.ZERO);
        }

        resCouponEffectDTO.setCollectCouponFansNum(storeCouponStaticVO.getCollectCouponFansNum());
        resCouponEffectDTO.setUseCouponFansNum(storeCouponStaticVO.getUseCouponFansNum());

        log.info("getStoreCouponStaticByParam----> storeCouponStaticVO: {}", storeCouponStaticVO);
        // 用券粉丝数, 如果小于0(可能为-1), 则设置为0
        if (resCouponEffectDTO.getUseCouponFansNum() < NumConstant.ZERO) {
            resCouponEffectDTO.setUseCouponFansNum(NumConstant.ZERO);
        }

        // 券使用率:粉丝券数使用/粉丝券数获得
        String couponUseRate = getRate(resCouponEffectDTO.getUseCouponTotal(), resCouponEffectDTO.getProvideCouponTotal());
        resCouponEffectDTO.setCouponUseRate(couponUseRate);

        log.info("getStoreCouponStaticByParam--end, responseData: {}", resCouponEffectDTO);
        return ExecuteDTO.ok(resCouponEffectDTO);
    }

    //    202503东启
    @Override
    public ExecuteDTO<ExecutePageDTO<ResCouponEffectDTO>> getStoreCouponStaticByPage(ReqCouponUserRecordDTO reqCouponUserRecordDTO) {

        ExecuteDTO executeDTO = couponUserRecordAssert.checkStoreCouponStaticAssert(reqCouponUserRecordDTO);
        if (!executeDTO.successFlag()) {
            return executeDTO;
        }
        Page<Object> pages = PageHelper.startPage(reqCouponUserRecordDTO);
        // 查询发放券总数和已使用券总数
        List<CouponUserRecordStaticVO> couponUserRecordStaticVOS = couponUserRecordDao.getStoreCouponStaticByPage(reqCouponUserRecordDTO);
        log.info("getStoreCouponStaticByPage----> couponUserRecordStaticVOS: {}", couponUserRecordStaticVOS);
        ExecutePageDTO<ResCouponEffectDTO> executePageDTO = new ExecutePageDTO<>();
        if (CollectionUtils.isNotEmpty(couponUserRecordStaticVOS)) {

            // 组装查询
            couponUserRecordStaticVOS.stream().forEach(couponUserRecordStaticVO -> {
                // 查询领券粉丝数和用券粉丝数, 已排除无效粉丝
                ReqMarketTouchingEffectVO reqMarketTouchingEffectVO = new ReqMarketTouchingEffectVO();

                reqMarketTouchingEffectVO.setStoreNo(reqCouponUserRecordDTO.getStoreNo());


                if (null != reqCouponUserRecordDTO.getStoreNoList()) {// 多店铺列表 查询
                    if (reqCouponUserRecordDTO.getStoreNoList().size() > 0) {
                        reqMarketTouchingEffectVO.setStoreNoList(reqCouponUserRecordDTO.getStoreNoList());
                    }
                }

                reqMarketTouchingEffectVO.setCouponNo(couponUserRecordStaticVO.getPromotionNo());
                reqMarketTouchingEffectVO.setMarketDatabaseName(dataBaseUtil.getMarketDataBaseName());
                reqMarketTouchingEffectVO.setUserDatabaseName(dataBaseUtil.getUserDataBaseName());
                CouponUserRecordStaticVO storeCouponStaticVO = couponUserRecordEffectDao.getStoreCouponStaticByParam(reqMarketTouchingEffectVO);
                if (null == storeCouponStaticVO) {
                    log.error("getStoreCouponStaticByParam----> storeCouponStaticVO is null");
                    storeCouponStaticVO = new CouponUserRecordStaticVO();
                    storeCouponStaticVO.setCollectCouponFansNum(NumConstant.ZERO);
                    storeCouponStaticVO.setUseCouponTotal(NumConstant.ZERO);
                }

                couponUserRecordStaticVO.setCollectCouponFansNum(storeCouponStaticVO.getCollectCouponFansNum());
                couponUserRecordStaticVO.setUseCouponFansNum(storeCouponStaticVO.getUseCouponFansNum() < NumConstant.ZERO ? NumConstant.ZERO : storeCouponStaticVO.getUseCouponFansNum());

                log.info("getStoreCouponStaticByParam----> storeCouponStaticVO: {}", storeCouponStaticVO);
                // 用券粉丝数, 如果小于0(可能为-1), 则设置为0
                if (couponUserRecordStaticVO.getUseCouponFansNum() < NumConstant.ZERO) {
                    couponUserRecordStaticVO.setUseCouponFansNum(NumConstant.ZERO);
                }
                // 券使用率:粉丝券数使用/粉丝券数获得
                String couponUseRate = getRate(couponUserRecordStaticVO.getUseCouponTotal(), couponUserRecordStaticVO.getProvideCouponTotal());
                couponUserRecordStaticVO.setCouponUseRate(couponUseRate);
                // 用券粉丝率：用券粉丝数/店铺粉丝数
                GetFansRequest getFansRequest = new GetFansRequest();
                getFansRequest.setStoreNumber(couponUserRecordStaticVO.getStoreNo());
                ExecuteDTO<GetFansResponse> fanExecuteDTO = userPublicService.getTotalFansCount(getFansRequest);
                String useCouponFansRate = getRate(couponUserRecordStaticVO.getUseCouponFansNum(), fanExecuteDTO.getData().getTotalFansCount());
                couponUserRecordStaticVO.setUseCouponFansRate(useCouponFansRate);
                // 券触达粉丝率：券获得粉丝数/店铺粉丝数
                String couponTouchingFansRate = getRate(couponUserRecordStaticVO.getCollectCouponFansNum(), fanExecuteDTO.getData().getTotalFansCount());
                couponUserRecordStaticVO.setCouponReachNum(couponTouchingFansRate);
            });
        }

        executePageDTO.setRows(BeanCopierUtil.copyList(couponUserRecordStaticVOS, ResCouponEffectDTO.class));
        executePageDTO.setTotal(pages.getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    /**
     * 获取占比
     *
     * @param numOne 参数1
     * @param numTwo 参数2
     * @return 占比
     */
    private String getRate(Integer numOne, Integer numTwo) {
        if (numOne == null || numTwo == null) {
            log.error("getRate, param is null, numOne: {}, numTwo: {}", numOne, numTwo);
            throw new BaseException(CommonCode.CODE_10000003);
        }
        if (numTwo == NumConstant.ZERO) {
            return "0";
        }
        BigDecimal tempRate = BigDecimalUtil.divide(new BigDecimal(numOne), new BigDecimal(numTwo)).multiply(new BigDecimal(NumConstant.ONE_HUNDRED));
        BigDecimal rate = BigDecimalUtil.setScale(tempRate, BigDecimal.ROUND_UP);
        log.info("------>getRate, numOne: {}, numTwo: {}, tempRate: {}, rate: {}", numOne, numTwo, tempRate, rate);
        return rate.toString();
    }

    /**
     * 店铺指定品牌
     */
    private Map<String, List<String>> getCouponBrandList(AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO) {
        //店铺指定品牌
        ExecuteDTO<Map<String, List<String>>> batchCouponBrandList = atomCouponUserRecordAnalysisService.getBatchCouponBrandList(atomReqCouponUserRecordDTO);
        log.info(String.format("**CouponPromotionAnalysisServiceImpl**getCouponGoodsList-店铺指定品牌:%s", JSON.toJSONString(batchCouponBrandList)));
        if (!batchCouponBrandList.successFlag()) {
            throw new BaseException(CommonCode.CODE_10000003);
        }
        return batchCouponBrandList.getData();
    }

    /**
     * 店铺指定类目
     */
    private Map<String, List<String>> getCouponCategoryList(AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO) {
        ExecuteDTO<Map<String, List<String>>> batchCouponCategoryList = atomCouponUserRecordAnalysisService.getBatchCouponCategoryList(atomReqCouponUserRecordDTO);
        log.info(String.format("**CouponPromotionAnalysisServiceImpl**getCouponGoodsList-店铺指定类目:%s", JSON.toJSONString(batchCouponCategoryList)));
        if (!batchCouponCategoryList.successFlag()) {
            throw new BaseException(CommonCode.CODE_10000003);
        }
        return batchCouponCategoryList.getData();
    }

    /**
     * 券指定商品
     */
    private Map<String, List<String>> getCouponGoodsList(AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO) {
        ExecuteDTO<Map<String, List<String>>> batchCouponGoodsList = atomCouponUserRecordAnalysisService.getBatchCouponGoodsList(atomReqCouponUserRecordDTO);
        log.info(String.format("**CouponPromotionAnalysisServiceImpl**getCouponGoodsList-券指定商品:%s", JSON.toJSONString(batchCouponGoodsList)));
        if (!batchCouponGoodsList.successFlag()) {
            throw new BaseException(CommonCode.CODE_10000003);
        }
        return batchCouponGoodsList.getData();
    }

    /**
     * 处理券标识字段
     */
    private void setCouponFlag(List<ResCouponUserRecordDTO> resCouponUserRecordDTOS, ReqCouponUserRecordDTO reqCouponUserRecordDTO) {
        // 券标识
        resCouponUserRecordDTOS.forEach(couponUser -> {
            if (CouponUseScopeShowEnum.COUPON_USE_SCOPE_TWO.getCode().equals(couponUser.getCouponUseScope())) {
                couponUser.setCouponSubhead(PromotionSourceTypeEnum.PROMOTION_SOURCE_TYPE_ONE.getType());
                couponUser.setCouponAfter(CouponUseScopeShowEnum.COUPON_USE_SCOPE_TWO.getType());
            } else if (CouponUseScopeShowEnum.COUPON_USE_SCOPE_THREE.getCode().equals(couponUser.getCouponUseScope())) {
                couponUser.setCouponSubhead(PromotionSourceTypeEnum.PROMOTION_SOURCE_TYPE_ONE.getType());
                couponUser.setCouponAfter(CouponUseScopeShowEnum.COUPON_USE_SCOPE_THREE.getType());
            } else if (CouponUseScopeShowEnum.COUPON_USE_SCOPE_FOUR.getCode().equals(couponUser.getCouponUseScope())) {
                couponUser.setCouponSubhead(PromotionSourceTypeEnum.PROMOTION_SOURCE_TYPE_THREE.getType());
                couponUser.setCouponAfter(CouponUseScopeShowEnum.COUPON_USE_SCOPE_FOUR.getType());
            } else if (CouponUseScopeShowEnum.COUPON_USE_SCOPE_FIVE.getCode().equals(couponUser.getCouponUseScope())) {
                couponUser.setCouponSubhead(PromotionSourceTypeEnum.PROMOTION_SOURCE_TYPE_THREE.getType());
                couponUser.setCouponAfter(CouponUseScopeShowEnum.COUPON_USE_SCOPE_FIVE.getType());
            } else if (CouponUseScopeShowEnum.COUPON_USE_SCOPE_SIX.getCode().equals(couponUser.getCouponUseScope())) {
                couponUser.setCouponSubhead(PromotionSourceTypeEnum.PROMOTION_SOURCE_TYPE_THREE.getType());
                couponUser.setCouponAfter(CouponUseScopeShowEnum.COUPON_USE_SCOPE_SIX.getType());
            } else if (CouponUseScopeShowEnum.COUPON_USE_SCOPE_ONE.getCode().equals(couponUser.getCouponUseScope())) {
                if (PromotionTypeCouponEnum.PLATFORM_MANUAL_AGENT_COUPON.getCode().equals(couponUser.getPromotionCouponType())
                        || PromotionTypeCouponEnum.PLATFORM_MANUAL_FANS_COUPON.getCode().equals(couponUser.getPromotionCouponType())
                        || PromotionTypeCouponEnum.PLATFORM_ONLINE_AGENT_COUPON.getCode().equals(couponUser.getPromotionCouponType())
                        || PromotionTypeCouponEnum.PLATFORM_ONLINE_FANS_COUPON.getCode().equals(couponUser.getPromotionCouponType())
                ) {
                    couponUser.setCouponSubhead(PromotionSourceTypeEnum.PROMOTION_SOURCE_TYPE_ONE.getType());
                    couponUser.setCouponAfter(CouponUseScopeShowEnum.COUPON_USE_SCOPE_ONE.getType());
                } else {
                    couponUser.setCouponSubhead(PromotionSourceTypeEnum.PROMOTION_SOURCE_TYPE_THREE.getType());
                    couponUser.setCouponAfter(CouponUseScopeShowEnum.COUPON_USE_SCOPE_SEVEN.getType());
                }
            }
            // 未使用页面判断 是否快过期（三天后过期，则设置即将过期标识）
            if (!CouponPeriodValidityEnum.COUPON_PERIOD_VALIDITY_ONE.getCode().equals(couponUser.getCouponPeriodValidity())
                    && LocalDateTime.now().plusDays(NumConstant.THREE).isAfter(couponUser.getCouponInvalidTime())) {
                couponUser.setIsPastCoupon(NumConstant.ONE);
            } else {
                couponUser.setIsPastCoupon(NumConstant.ZERO);
            }
        });
    }
}

