package cn.htdt.marketprocess.biz.operat;

import cn.htdt.common.base.exception.BaseException;
import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.OrderPaymentStatusEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.market.SmsBalanceOperateTypeEnum;
import cn.htdt.common.enums.market.SmsTrafficChangeSourceEnum;
import cn.htdt.common.generator.MarketFormGenerator;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqSmsBalanceOperateRecordDTO;
import cn.htdt.marketcenter.dto.request.AtomReqSmsRechargeOrderDTO;
import cn.htdt.marketcenter.dto.request.AtomReqSmsTrafficDTO;
import cn.htdt.marketcenter.dto.response.AtomResSmsRechargeOrderDTO;
import cn.htdt.marketprocess.api.operat.SmsRechargeOrderOperatService;
import cn.htdt.marketprocess.biz.conversion.SmsRechargeOrderAssert;
import cn.htdt.marketprocess.biz.utils.SmsUtil;
import cn.htdt.marketprocess.dto.request.ReqSmsRechargeOrderDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomSmsRechargeOrderAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomSmsBalanceOperateRecordOperatService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomSmsRechargeOrderOperatService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomSmsTrafficOperatService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;

/**
 * 短信模板表 操作服务接口实现类
 *
 * <AUTHOR>
 * @date 2021/6/28
 **/
@DubboService
@Slf4j
public class SmsRechargeOrderOperatServiceImpl implements SmsRechargeOrderOperatService {
    @Resource
    private AtomSmsRechargeOrderOperatService atomSmsRechargeOrderOperatService;
    @Resource
    private AtomSmsRechargeOrderAnalysisService atomSmsRechargeOrderAnalysisService;
    @Resource
    private AtomSmsTrafficOperatService atomSmsTrafficOperatService;
    @Resource
    private AtomSmsBalanceOperateRecordOperatService atomSmsBalanceOperateRecordOperatService;

    @Resource
    private SmsRechargeOrderAssert smsRechargeOrderAssert;
    @Resource
    private SmsUtil smsUtil;

    /**
     * 综合服务平台 -> 营销管理 -> 短信营销活动 -> 短信充值
     * 超级老板APP -> 会员 -> 短信营销 -> 短信充值
     *
     * @param reqDTO 请求参数
     * @return ExecuteDTO
     * <AUTHOR>
     */
    @Override
    public ExecuteDTO addSmsRechargeOrder(ReqSmsRechargeOrderDTO reqDTO) {
        log.info("-------SmsRechargeOrderOperatServiceImpl-->addSmsRechargeOrder,新增短信充值订单--start----");
        smsRechargeOrderAssert.addSmsRechargeOrderAssert(reqDTO);

        AtomReqSmsRechargeOrderDTO atomReqSmsTemplateDTO = BeanCopierUtil.copy(reqDTO, AtomReqSmsRechargeOrderDTO.class);

        ExecuteDTO executeDTO = atomSmsRechargeOrderOperatService.addSmsRechargeOrder(atomReqSmsTemplateDTO);

        log.info("-------SmsTemplateOperatServiceImpl-->saveSmsTemplate,保存短信模板--end----");
        return executeDTO;
    }

    /**
     * 短信充值收银台回调 -> 修改短信充值订单
     *
     * @param reqDTO 请求参数
     * @return ExecuteDTO
     * <AUTHOR>
     */
    @Override
    public ExecuteDTO modifySmsRechargeOrder(ReqSmsRechargeOrderDTO reqDTO) {
        log.info("-------SmsRechargeOrderOperatServiceImpl-->modifySmsRechargeOrder,修改短信充值订单--start:{}", JSON.toJSONString(reqDTO));
        smsRechargeOrderAssert.modifySmsRechargeOrderAssert(reqDTO);


        if (!OrderPaymentStatusEnum.PAID.getCode().equals(reqDTO.getOrderPaymentStatus())) {
            //支付状态不是已支付，暂无操作
            return ExecuteDTO.success();
        }

        //获取短信充值订单
        AtomReqSmsRechargeOrderDTO atomReqSmsRechargeOrderDTO = new AtomReqSmsRechargeOrderDTO();
        atomReqSmsRechargeOrderDTO.setOrderNo(reqDTO.getOrderNo());
        atomReqSmsRechargeOrderDTO.setNoPage();
        ExecuteDTO<ExecutePageDTO<AtomResSmsRechargeOrderDTO>> smsRechargeOrderDTOExecuteDTO = atomSmsRechargeOrderAnalysisService.getSmsRechargeOrderListByPage(atomReqSmsRechargeOrderDTO);
        if (!smsRechargeOrderDTOExecuteDTO.successFlag()) {
            return ExecuteDTO.error(smsRechargeOrderDTOExecuteDTO.getStatus(), smsRechargeOrderDTOExecuteDTO.getMsg());
        }
        if (smsRechargeOrderDTOExecuteDTO.getData() == null || CollectionUtils.isEmpty(smsRechargeOrderDTOExecuteDTO.getData().getRows())) {
            return ExecuteDTO.error(smsRechargeOrderDTOExecuteDTO.getStatus(), smsRechargeOrderDTOExecuteDTO.getMsg());
        }
        AtomResSmsRechargeOrderDTO atomResSmsRechargeOrderDTO = smsRechargeOrderDTOExecuteDTO.getData().getRows().get(NumConstant.ZERO);
        if (OrderPaymentStatusEnum.PAID.getCode().equals(atomResSmsRechargeOrderDTO.getOrderPaymentStatus())) {
            //已充值成功，直接返回成功
            return ExecuteDTO.success();
        }

        //1.修改短信充值订单
        BeanUtils.copyProperties(reqDTO, atomReqSmsRechargeOrderDTO);
        atomReqSmsRechargeOrderDTO.setModifyNo("admin");
        atomReqSmsRechargeOrderDTO.setModifyName("admin");
        this.checkExecuteDTO(atomSmsRechargeOrderOperatService.modifySmsRechargeOrder(atomReqSmsRechargeOrderDTO));

        //2.编辑redis短信余额（加）
        int smsBalance = (int) smsUtil.redisIncreaseSmsBalance(atomResSmsRechargeOrderDTO.getStoreNo(), atomResSmsRechargeOrderDTO.getRechargeNum());

        //3.修改短信余额（增加）
        AtomReqSmsTrafficDTO atomReqSmsTrafficDTO = new AtomReqSmsTrafficDTO();
        atomReqSmsTrafficDTO.setStoreNo(atomResSmsRechargeOrderDTO.getStoreNo());
        atomReqSmsTrafficDTO.setSmsRemainingNum(Long.valueOf(atomResSmsRechargeOrderDTO.getRechargeNum()));
        atomReqSmsTrafficDTO.setModifyNo("admin");
        atomReqSmsTrafficDTO.setModifyName("admin");
        this.checkExecuteDTO(atomSmsTrafficOperatService.modifySmsTrafficByIncr(atomReqSmsTrafficDTO));

        //4.新增短信余额操作记录
        AtomReqSmsBalanceOperateRecordDTO atomReqSmsBalanceOperateRecordDTO = BeanCopierUtil.copy(reqDTO, AtomReqSmsBalanceOperateRecordDTO.class);
        atomReqSmsBalanceOperateRecordDTO.setRecordNo(MarketFormGenerator.smsBalanceOperateRecordNo());
        atomReqSmsBalanceOperateRecordDTO.setOperateType(SmsBalanceOperateTypeEnum.SET_MEAL.getCode());
        atomReqSmsBalanceOperateRecordDTO.setChangeNum(atomResSmsRechargeOrderDTO.getRechargeNum());
        atomReqSmsBalanceOperateRecordDTO.setBeforeChangeNum(smsBalance - atomResSmsRechargeOrderDTO.getRechargeNum());
        atomReqSmsBalanceOperateRecordDTO.setAfterChangeNum(smsBalance);
        atomReqSmsBalanceOperateRecordDTO.setChangeSource(SmsTrafficChangeSourceEnum.RECHARGE.getCode());
        atomReqSmsBalanceOperateRecordDTO.setChangeOrderNo(atomResSmsRechargeOrderDTO.getOrderNo());
        atomReqSmsBalanceOperateRecordDTO.setMerchantNo(atomResSmsRechargeOrderDTO.getMerchantNo());
        atomReqSmsBalanceOperateRecordDTO.setMerchantName(atomResSmsRechargeOrderDTO.getMerchantName());
        atomReqSmsBalanceOperateRecordDTO.setStoreNo(atomResSmsRechargeOrderDTO.getStoreNo());
        atomReqSmsBalanceOperateRecordDTO.setStoreName(atomResSmsRechargeOrderDTO.getStoreName());
        atomReqSmsBalanceOperateRecordDTO.setCreateNo("admin");
        atomReqSmsBalanceOperateRecordDTO.setCreateName("admin");
        this.checkExecuteDTO(atomSmsBalanceOperateRecordOperatService.addSmsBalanceOperateRecord(atomReqSmsBalanceOperateRecordDTO));

        //创建短信合同
        this.addSMSContractJobDeal(reqDTO.getOrderNo(), reqDTO.getTradeType());
        log.info("-------SmsTemplateOperatServiceImpl-->saveSmsTemplate,保存短信模板--end----");
        return ExecuteDTO.success();
    }

    @Override
    public ExecuteDTO modifySmsRechargeOrderOne(ReqSmsRechargeOrderDTO reqSmsRechargeOrderDTO) {
        AtomReqSmsRechargeOrderDTO copy = BeanCopierUtil.copy(reqSmsRechargeOrderDTO, AtomReqSmsRechargeOrderDTO.class);
        return atomSmsRechargeOrderOperatService.modifySmsRechargeOrder(copy);
    }

    @Override
    public ExecuteDTO addSMSContractJobDeal(String orderNo, String tradeType) {
        //查询订单信息
        AtomReqSmsRechargeOrderDTO atomReqSmsRechargeOrderDTO = new AtomReqSmsRechargeOrderDTO();
        atomReqSmsRechargeOrderDTO.setOrderNo(orderNo);
        ExecuteDTO<ExecutePageDTO<AtomResSmsRechargeOrderDTO>> smsRechargeOrderListByPage = atomSmsRechargeOrderAnalysisService.getSmsRechargeOrderListByPage(atomReqSmsRechargeOrderDTO);
        if (!smsRechargeOrderListByPage.successFlag()) {
            return ExecuteDTO.error(CommonCode.CODE_10000003, "查询短信订单信息失败!");
        }

        if (CollectionUtils.isEmpty(smsRechargeOrderListByPage.getData().getRows())) {
            return ExecuteDTO.error(CommonCode.CODE_10000002, "无此订单");
        }

        return ExecuteDTO.success();
    }


    /**
     * 校验返回参数
     *
     * @param executeDTO 返回返回参数
     * <AUTHOR>
     */
    private void checkExecuteDTO(ExecuteDTO executeDTO) {
        if (executeDTO == null) {
            throw new BaseException(CommonCode.CODE_10000003);
        }
        if (!executeDTO.successFlag()) {
            throw new BaseException(executeDTO.getStatus(), executeDTO.getMsg());
        }
    }
}
