package cn.htdt.marketprocess.biz.operat;

import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.enums.market.PointConfigTypeEnum;
import cn.htdt.common.enums.market.PointLimitEnum;
import cn.htdt.common.enums.market.PointUseChannelEnum;
import cn.htdt.common.generator.IdGenerator;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqPointsConfigLogDTO;
import cn.htdt.marketprocess.api.analysis.PointsConfigAnalysisService;
import cn.htdt.marketprocess.api.operat.PointsConfigLogOperateService;
import cn.htdt.marketprocess.biz.conversion.PointsConfigLogAssert;
import cn.htdt.marketprocess.dto.request.ReqPointsConfigDTO;
import cn.htdt.marketprocess.dto.request.ReqPointsConfigLogDTO;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomPointsConfigLogOperateService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@DubboService
public class PointsConfigLogOperateServiceImpl implements PointsConfigLogOperateService {
    @Resource
    private AtomPointsConfigLogOperateService atomPointsConfigLogOperateService;

    @Resource
    PointsConfigAnalysisService pointsConfigAnalysisService;

    @Resource
    private PointsConfigLogAssert configLogAssert;

    /**
     * @see PointsConfigLogOperateService#addEnablePointsConfigLog(ReqPointsConfigLogDTO)
     */
    @Override
    public ExecuteDTO<String> addEnablePointsConfigLog(ReqPointsConfigLogDTO reqPointsConfigLogDTO) {
        log.info("PointsConfigLogOperateServiceImpl.addEnablePointsConfigLog----param----{}", JSON.toJSONString(reqPointsConfigLogDTO));
        try {
            configLogAssert.addEnablePointsConfigLog(reqPointsConfigLogDTO);
        } catch (Exception e) {
            log.error("PointsConfigLogOperateServiceImpl.addEnablePointsConfigLog----error----{}",e.getMessage());
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
            //粉丝下单和关注店铺需要拼接操作内容
            if (PointConfigTypeEnum.POINT_ORDER.getCode().equals(reqPointsConfigLogDTO.getConfigType())
                    || PointConfigTypeEnum.POINT_STORE.getCode().equals(reqPointsConfigLogDTO.getConfigType()) || PointConfigTypeEnum.POINT_PAY.getCode().equals(reqPointsConfigLogDTO.getConfigType())) {
                reqPointsConfigLogDTO.setOperateContent(joinConifContent(reqPointsConfigLogDTO));
            }
            AtomReqPointsConfigLogDTO atomReqPointsConfigLogDTO = BeanCopierUtil.copy(reqPointsConfigLogDTO, AtomReqPointsConfigLogDTO.class);
            String recordNo = IdGenerator.getDid();
            atomReqPointsConfigLogDTO.setLogNo(recordNo);
            atomPointsConfigLogOperateService.addPointsConfigLog(atomReqPointsConfigLogDTO);
            return ExecuteDTO.success(recordNo);
    }

    @Override
    public ExecuteDTO<String> addDisablePointsConfigLog(ReqPointsConfigLogDTO reqPointsConfigLogDTO) {
        log.info("PointsConfigLogOperateServiceImpl.addDisablePointsConfigLog----param----{}", JSON.toJSONString(reqPointsConfigLogDTO));
        try {
            configLogAssert.addDisablePointsConfigLog(reqPointsConfigLogDTO);
        }catch (Exception e){
            log.error("PointsConfigLogOperateServiceImpl.addDisablePointsConfigLog----error----{}",e.getMessage());
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
            //记录表的数据来源于当前积分配置的信息
            ReqPointsConfigDTO reqPointsConfigDTO = BeanCopierUtil.copy(reqPointsConfigLogDTO, ReqPointsConfigDTO.class);
            //记录表操作不影响正常业务流程
            ExecuteDTO<ReqPointsConfigDTO> pointsConfigInfo = pointsConfigAnalysisService.getPointsConfigByConfigNo(reqPointsConfigDTO);
            if (pointsConfigInfo.successFlag()) {
                BeanCopierUtil.copy(pointsConfigInfo.getData(), reqPointsConfigLogDTO);
            }
            return this.addEnablePointsConfigLog(reqPointsConfigLogDTO);
    }

    /**
     * 拼接积分配置操作内容
     *
     * @param reqPointsConfigLogDTO 积分配置信息
     * @return 操作内容
     */
    private String joinConifContent(ReqPointsConfigLogDTO reqPointsConfigLogDTO) {
        StringBuffer operateContent = new StringBuffer();
        String configType = reqPointsConfigLogDTO.getConfigType();
        if (PointConfigTypeEnum.POINT_ORDER.getCode().equals(configType)) {
            operateContent.append("订单实付金额每满").append(reqPointsConfigLogDTO.getOrderAmount()).append("元，赠送").append(reqPointsConfigLogDTO.getPresentPoints()).append("个积分");
        }
        if (PointConfigTypeEnum.POINT_STORE.getCode().equals(configType)) {
            operateContent.append("首次关注店铺，赠送").append(reqPointsConfigLogDTO.getPresentPoints()).append("个积分");
        }
        if(PointConfigTypeEnum.POINT_PAY.getCode().equals(configType)){
            operateContent.append(reqPointsConfigLogDTO.getDeductionScale()).append("积分抵扣1分钱，");
            if(PointLimitEnum.NO_LIMIT.getCode().equals(reqPointsConfigLogDTO.getOrderAmountLimit())){
                operateContent.append("不限制订单金额门槛，");
            }else {
                operateContent.append("订单应付金额门槛最低").append(reqPointsConfigLogDTO.getOrderHandleAmount()).append("元，");
            }
            if(PointLimitEnum.NO_LIMIT.getCode().equals(reqPointsConfigLogDTO.getDeductionAmountLimit())){
                operateContent.append("不限制抵扣金额上限，");
            }else {
                operateContent.append("抵扣金额上限").append(reqPointsConfigLogDTO.getPointsDeductionLimit()).append("积分，");
            }
            if(StringUtils.isNotBlank(reqPointsConfigLogDTO.getUseChannel())){
              String[] strings =  reqPointsConfigLogDTO.getUseChannel().split(",");
              if(null !=strings && strings.length != 0) {
                  String useChannel = "";
                  for (int i = 0; i < strings.length; i++) {
                      useChannel += PointUseChannelEnum.getByCode(strings[i]).getType() + "/";
                  }
                  useChannel = useChannel.substring(0, useChannel.length() - 1);
                  operateContent.append(useChannel).append("可使用积分抵扣");
              }
            }
        }
        return operateContent.toString();
    }
}
