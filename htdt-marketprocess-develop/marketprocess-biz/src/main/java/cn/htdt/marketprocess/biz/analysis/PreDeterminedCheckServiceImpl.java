package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.utils.ExecuteDTOUtil;
import cn.htdt.common.enums.OrderStatusEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.goods.SourceTypeEnum;
import cn.htdt.marketprocess.api.analysis.PreDeterminedCheckService;
import cn.htdt.marketprocess.biz.utils.UserInfoUtil;
import cn.htdt.ordercenter.dto.request.AtomReqSoDTO;
import cn.htdt.orderprocess.api.LegacyOrderCenterService;
import cn.htdt.userprocess.dto.response.MerchantInfoResponseT;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * 预设条件判断相关服务
 *
 * <AUTHOR>
 */
@Slf4j
@DubboService
public class PreDeterminedCheckServiceImpl implements PreDeterminedCheckService {

    @Resource
    private UserInfoUtil userInfoUtil;

    @DubboReference
    private LegacyOrderCenterService legacyOrderCenterService;


    @Override
    public ExecuteDTO<Boolean> checkNewFans(String fanNo, String storeNo) {
        return this.checkNewFans(fanNo, storeNo, null);
    }

    @Override
    public ExecuteDTO<Boolean> checkNewFans(String fanNo, String storeNo, String merchantNo) {
        if (StringUtils.isBlank(fanNo)) {
            return ExecuteDTOUtil.error(CommonCode.CODE_10000001);
        }
        // 1. 判断当前用户是否下过单
        AtomReqSoDTO atomReqSoDTO = new AtomReqSoDTO();
        atomReqSoDTO.setBuyerNo(fanNo);
        atomReqSoDTO.setStoreNo(storeNo);
        atomReqSoDTO.setMerchantNo(merchantNo);
        atomReqSoDTO.setOrderStatus(OrderStatusEnum.HAD_PAYED.getCode());
        ExecuteDTO<Integer> executeDTO = legacyOrderCenterService.countByByParams(atomReqSoDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTOUtil.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        // 未下过订单 则为新用户
        if (executeDTO.getData() == NumConstant.ZERO) {
            return ExecuteDTOUtil.success(Boolean.TRUE);
        }
        return ExecuteDTOUtil.success(Boolean.FALSE);
    }

    /**
     * 新粉丝判断, 兼容商家拆盲盒活动
     *
     * @param fanNo               粉丝编号
     * @param storeNo             编号店铺
     * @param merchantNo          商家编号
     * @param promotionSourceType 抽奖活动来源, 参考枚举: SourceTypeEnum
     * @return 是否为新粉丝
     */
    @Override
    public ExecuteDTO<Boolean> checkNewFans(String fanNo, String storeNo, String merchantNo, String promotionSourceType) {
        if (StringUtils.isBlank(fanNo)) {
            log.error("checkNewFans--->fanNo is blank");
            return ExecuteDTOUtil.error(CommonCode.CODE_10000001);
        }
        // 如果为平台或者店铺的活动
        if (SourceTypeEnum.SOURCE_TYPE_1001.getCode().equals(promotionSourceType)
                || SourceTypeEnum.SOURCE_TYPE_1003.getCode().equals(promotionSourceType)) {
            return checkNewFans(fanNo, SourceTypeEnum.SOURCE_TYPE_1001.getCode().equals(promotionSourceType) ? null : storeNo);
        } else {
            log.info("checkNewFans--->merchant lotteryDraw promotion");
            if (StringUtils.isBlank(merchantNo)) {
                MerchantInfoResponseT merchantInfo = userInfoUtil.getMerchantInfo(storeNo);
                merchantNo = merchantInfo.getMerchantNo();
            }
            return checkNewFans(fanNo, null , merchantNo);
        }
    }

}
