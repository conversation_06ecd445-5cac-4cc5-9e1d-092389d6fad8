package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.base.exception.BaseException;
import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.enums.MarketErrorCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.dto.utils.ExecuteDTOUtil;
import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.constants.PromotionKeyConstant;
import cn.htdt.common.enums.goods.GoodsSourceTypeEnum;
import cn.htdt.common.enums.goods.GoodsStatusEnum;
import cn.htdt.common.enums.goods.MultiUnitTypeEnum;
import cn.htdt.common.enums.goods.SourceTypeEnum;
import cn.htdt.common.enums.market.*;
import cn.htdt.common.enums.user.BelongCompanyEnum;
import cn.htdt.common.enums.user.DeleteFlagEnum;
import cn.htdt.common.redis.utils.RedisUtil;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.ListUtil;
import cn.htdt.goodsprocess.api.analysis.GoodsAnalysisService;
import cn.htdt.goodsprocess.api.analysis.SaleCategoryAnalysisService;
import cn.htdt.goodsprocess.dto.request.goods.ReqGoodsDTO;
import cn.htdt.goodsprocess.dto.request.salecategory.ReqSaleCategoryRelationDTO;
import cn.htdt.goodsprocess.dto.response.goods.ResGoodsDTO;
import cn.htdt.goodsprocess.dto.response.salecategory.ResSaleCategoryDTO;
import cn.htdt.marketcenter.dto.request.*;
import cn.htdt.marketcenter.dto.response.*;
import cn.htdt.marketprocess.api.analysis.GoodsPromotionRuleAnalysisService;
import cn.htdt.marketprocess.biz.api.GoodsPromotionImageService;
import cn.htdt.marketprocess.biz.api.GoodsPromotionMultiLevelService;
import cn.htdt.marketprocess.biz.conversion.GoodsPromotionAssert;
import cn.htdt.marketprocess.biz.conversion.PromotionInfoAssert;
import cn.htdt.marketprocess.biz.utils.UserInfoUtil;
import cn.htdt.marketprocess.dao.PromotionStoreRelationDao;
import cn.htdt.marketprocess.dto.request.*;
import cn.htdt.marketprocess.dto.request.communitysolitaire.PromotionImageDTO;
import cn.htdt.marketprocess.dto.response.*;
import cn.htdt.marketprocess.legacycenter.api.analysis.*;
import cn.htdt.marketprocess.vo.AtomStoreMemberPriceRuleVo;
import cn.htdt.userprocess.api.StoreProcessService;
import cn.htdt.userprocess.dto.request.MerchantStoreRequest;
import cn.htdt.userprocess.dto.response.MerchantInfoResponseT;
import cn.htdt.userprocess.dto.response.MerchantStoreResponse;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 详细说明.
 * <p>
 * Copyright: Copyright (c) 2021/6/29 11:23
 * <p>
 * Company: htd
 * <p>
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@DubboService
@Slf4j
public class GoodsPromotionRuleAnalysisServiceImpl implements GoodsPromotionRuleAnalysisService {

    @Resource
    private AtomGoodsPromotionRuleAnalysisService atomGoodsPromotionRuleAnalysisService;

    @Resource
    private AtomGoodsPromotionPeriodAnalysisService atomGoodsPromotionPeriodAnalysisService;

    @Resource
    private AtomGoodsPromotionGoodsRelationAnalysisService atomGoodsPromotionGoodsRelationAnalysisService;

    @Resource
    private AtomGoodsPromotionCategoryRelationAnalysisService atomGoodsPromotionCategoryRelationAnalysisService;

    @Resource
    private PromotionStoreRelationDao promotionStoreRelationDao;

    @DubboReference
    private GoodsAnalysisService goodsAnalysisService;

    @DubboReference
    private SaleCategoryAnalysisService saleCategoryAnalysisService;

    @Autowired
    private PromotionInfoAssert promotionInfoAssert;

    @Autowired
    private GoodsPromotionAssert goodsPromotionAssert;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private LimitTimePromotionAnalysisServiceImpl limitTimePromotionAnalysisService;

    @Resource
    private GoodsPromotionImageService goodsPromotionImageService;

    @Resource
    private GoodsPromotionMultiLevelService goodsPromotionMultiLevelService;

    @Resource
    private AtomPromotionStoreRelationAnalysisService promotionStoreRelationAnalysisService;

    @DubboReference
    private StoreProcessService storeProcessService;

    @Resource
    private UserInfoUtil userInfoUtil;

    /**
     * @param promotionInfoDTO
     * @Description : 商品促销活动列表查询
     * <AUTHOR> 张宇
     * @date : 2021/6/29 16:59
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResGoodsPromotionRuleDTO>> getGoodsPromotionRuleList(ReqPromotionInfoDTO promotionInfoDTO) {
        //条件判断
        this.promotionInfoAssert.dtoAssert(promotionInfoDTO);
        log.info("--GoodsPromotionRuleAnalysisServiceImpl.getGoodsPromotionRuleList-入参-promotionInfoDTO-{}", promotionInfoDTO);
        AtomReqPromotionInfoDTO infoDTO = BeanCopierUtil.copy(promotionInfoDTO, AtomReqPromotionInfoDTO.class);
        if (ListUtil.isNotEmpty(promotionInfoDTO.getPromotionTypes())) {
            infoDTO.setPromotionTypeList(promotionInfoDTO.getPromotionTypes());
        }
        ExecuteDTO<ExecutePageDTO<AtomResGoodsPromotionRuleDTO>> executeDTO = atomGoodsPromotionRuleAnalysisService.getGoodsPromotionRuleList(infoDTO);
        if (executeDTO.successFlag()) {
            return ExecuteDTO.success(new ExecutePageDTO<>(executeDTO.getData().getTotal(), BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResGoodsPromotionRuleDTO.class)));
        }
        return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
    }

    /**
     * @param promotionInfoDTO
     * @Description : 根据店铺编号查询有效促销活动列表-汇享购
     * <AUTHOR> 张宇
     * @date : 2021/6/29 16:59
     */
    @Override
    public ExecuteDTO<List<ResGoodsPromotionRuleDTO>> getGoodsPromotionRuleListForHxg(ReqPromotionInfoDTO promotionInfoDTO) {
        //条件判断
        this.promotionInfoAssert.dtoAssert(promotionInfoDTO);
        log.info("--GoodsPromotionRuleAnalysisServiceImpl.getGoodsPromotionRuleList-入参-getGoodsPromotionRuleListForHxg-{}", promotionInfoDTO);
        AtomReqPromotionInfoDTO infoDTO = BeanCopierUtil.copy(promotionInfoDTO, AtomReqPromotionInfoDTO.class);
        // todo xh 2023-06-08 目标二：营销管理 汇通数科体系不取平台营销活动
        MerchantInfoResponseT merchantInfoResponse = this.userInfoUtil.getMerchantInfo(promotionInfoDTO.getStoreNo());
        if (BelongCompanyEnum.getByCode(merchantInfoResponse.getBelongCompany()) == BelongCompanyEnum.HTSK) {
            infoDTO.setListSourceType(Arrays.asList(PromotionSourceTypeEnum.PROMOTION_SOURCE_TYPE_TWO.getCode(),
                    PromotionSourceTypeEnum.PROMOTION_SOURCE_TYPE_THREE.getCode()));
        }
        ExecuteDTO<List<AtomResGoodsPromotionRuleDTO>> executeDTO = atomGoodsPromotionRuleAnalysisService.getGoodsPromotionRuleListForHxg(infoDTO);
        if (executeDTO.successFlag()) {
            List<ResGoodsPromotionRuleDTO> goodsPromotionRuleDTOList = BeanCopierUtil.copyList(executeDTO.getData(), ResGoodsPromotionRuleDTO.class);
            // 汇赚钱模块，校验是否查询出来的活动列表数据，是否包含分销商品，如果没有分销商品，则去掉活动
            if (SecKillLocationEnum.H_Z_Q_SECKILL.getCode().equals(promotionInfoDTO.getSecKillLocation())
                    && CollectionUtils.isNotEmpty(goodsPromotionRuleDTOList)) {
                limitTimePromotionAnalysisService.removeNoDistributeGoodsPromotion(goodsPromotionRuleDTOList);
            }
            //查询秒杀活动时间段
            this.getSecKillPromotionPeriodList(goodsPromotionRuleDTOList);
            this.filterByGoodsStatus(goodsPromotionRuleDTOList, promotionInfoDTO);
            return ExecuteDTO.success(goodsPromotionRuleDTOList);
        }
        return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());

    }

    /**
     * 查询秒杀活动时间段
     *
     * @param goodsPromotionRuleDTOList
     */
    private void getSecKillPromotionPeriodList(List<ResGoodsPromotionRuleDTO> goodsPromotionRuleDTOList) {
        if (CollectionUtils.isNotEmpty(goodsPromotionRuleDTOList)) {
            //如果是秒杀活动则需要查询秒杀时间段
            List<ResGoodsPromotionRuleDTO> secKillPromotionList = goodsPromotionRuleDTOList.stream().filter(ruleDTO ->
                    PromotionTypeEnum.SENCOND_KILL.getCode().equals(ruleDTO.getPromotionType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(secKillPromotionList)) {
                List<String> promotionNoList = secKillPromotionList.stream().map(ResGoodsPromotionRuleDTO::getPromotionNo).collect(Collectors.toList());
                AtomReqGoodsPromotionPeriodDTO reqDTO = new AtomReqGoodsPromotionPeriodDTO();
                reqDTO.setPromotionNoList(promotionNoList);
                ExecuteDTO<List<AtomResGoodsPromotionPeriodDTO>> executePeriodDTO = atomGoodsPromotionPeriodAnalysisService.getGoodsPromotionPeriodByPromotionNo(reqDTO);
                if (executePeriodDTO.successFlag() && CollectionUtils.isNotEmpty(executePeriodDTO.getData())) {
                    secKillPromotionList.forEach(secKillPromotion -> {
                        Optional<AtomResGoodsPromotionPeriodDTO> optional = executePeriodDTO.getData().stream().filter(peridDTO ->
                                peridDTO.getPromotionNo().equals(secKillPromotion.getPromotionNo())).findFirst();
                        if (optional.isPresent()) {
                            AtomResGoodsPromotionPeriodDTO periodDTO = optional.get();
                            secKillPromotion.setPeriodNo(periodDTO.getPeriodNo());
                            secKillPromotion.setDailyStartTime(periodDTO.getStartTime());
                            secKillPromotion.setDailyEndTime(periodDTO.getEndTime());
                            secKillPromotion.setStartTime(periodDTO.getStartTime());
                            secKillPromotion.setEndTime(periodDTO.getEndTime());
                        }
                    });
                }
            }
        }
    }

    private void filterByGoodsStatus(List<ResGoodsPromotionRuleDTO> goodsPromotionRuleDTOList, ReqPromotionInfoDTO promotionInfoDTO) {
        Iterator<ResGoodsPromotionRuleDTO> iterator = goodsPromotionRuleDTOList.iterator();
        while (iterator.hasNext()) {
            ResGoodsPromotionRuleDTO resGoodsPromotionRuleDTO = iterator.next();
            //查询活动下的商品列表
            AtomReqGoodsPromotionGoodsRelationDTO goodsRelationDTO = BeanCopierUtil.copy(resGoodsPromotionRuleDTO, AtomReqGoodsPromotionGoodsRelationDTO.class);
            goodsRelationDTO.setPromotionNo(resGoodsPromotionRuleDTO.getPromotionNo());
            log.info("--atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList-start-goodsRelationDTO-{}", goodsRelationDTO);
            ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> goodsExecuteDTO = atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList(goodsRelationDTO);
            log.info("--atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList-end");
            if (goodsExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(goodsExecuteDTO.getData())) {
                List<String> goodsNos = goodsExecuteDTO.getData().stream().map(AtomResGoodsPromotionGoodsRelationDTO::getGoodsNo).collect(Collectors.toList());
                ReqGoodsDTO reqGoodsDTO = new ReqGoodsDTO();
                reqGoodsDTO.setStoreNo(promotionInfoDTO.getStoreNo());
                reqGoodsDTO.setGoodsStatus(GoodsStatusEnum.ON_SHELF.getCode());
                reqGoodsDTO.setPageSize(NumConstant.ONE);
                if (SourceTypeEnum.SOURCE_TYPE_1003.getCode().equals(resGoodsPromotionRuleDTO.getSourceType())) {
                    reqGoodsDTO.setGoodsNos(goodsNos);
                } else {
                    reqGoodsDTO.setCloudPoolGoodsNos(goodsNos);
                }
                //查询商品信息主品+普通商品
                log.info("--goodsAnalysisService.getGoodsPage-start-reqGoodsDTO-{}", reqGoodsDTO);
                ExecuteDTO<ExecutePageDTO<ResGoodsDTO>> goodsExecute = goodsAnalysisService.getGoodsSelectPage(reqGoodsDTO);
                log.info("--goodsAnalysisService.getGoodsPage-end");
                if (goodsExecute.successFlag() && goodsExecute.getData() != null) {
                    long onShelfGoodsCount = goodsExecute.getData().getTotal();
                    if (onShelfGoodsCount < 1) {
                        iterator.remove();
                    }
                }
            }
        }
    }

    /**
     * 商家查询平台的促销活动
     *
     * @param promotionInfoDTO
     * @return
     * <AUTHOR>
     * @date 2021-07-01
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResGoodsPromotionRuleDTO>> merchantGetPlatformPromotion(ReqPromotionInfoDTO promotionInfoDTO) {
        log.info("--GoodsPromotionRuleAnalysisServiceImpl.merchantGetPlatformPromotion-入参-promotionInfoDTO-{}", promotionInfoDTO);
        //条件判断
        goodsPromotionAssert.merchantGetPlatformPromotionAssert(promotionInfoDTO);

        // 入参转换
        AtomReqPromotionInfoDTO infoDTO = BeanCopierUtil.copy(promotionInfoDTO, AtomReqPromotionInfoDTO.class);

        // 查询数据库
        ExecuteDTO<ExecutePageDTO<AtomResGoodsPromotionRuleDTO>> executeDTO = atomGoodsPromotionRuleAnalysisService.merchantGetPlatformPromotion(infoDTO);
        if (executeDTO == null) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }

        ExecutePageDTO<ResGoodsPromotionRuleDTO> pageResult = new ExecutePageDTO<>();
        // 出参转换
        if (executeDTO.getData() != null) {
            List<ResGoodsPromotionRuleDTO> list = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResGoodsPromotionRuleDTO.class);
            pageResult.setRows(list);
            pageResult.setTotal(executeDTO.getData().getTotal());
        }

        // 返回结果
        return ExecuteDTO.success(pageResult);
    }

    /**
     * 店铺查询平台的促销活动
     *
     * @param promotionInfoDTO
     * @return
     * <AUTHOR>
     * @date 2021-07-01
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResGoodsPromotionRuleDTO>> storeGetPlatformPromotion(ReqPromotionInfoDTO promotionInfoDTO) {
        log.info("--GoodsPromotionRuleAnalysisServiceImpl.storeGetPlatformPromotion-入参-promotionInfoDTO-{}", promotionInfoDTO);
        //条件判断
        goodsPromotionAssert.storeGetPlatformPromotionAssert(promotionInfoDTO);

        // 入参转换
        AtomReqPromotionInfoDTO infoDTO = BeanCopierUtil.copy(promotionInfoDTO, AtomReqPromotionInfoDTO.class);

        // 查询数据库
        ExecuteDTO<ExecutePageDTO<AtomResGoodsPromotionRuleDTO>> executeDTO = atomGoodsPromotionRuleAnalysisService.storeGetPlatformPromotion(infoDTO);
        if (executeDTO == null) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }

        ExecutePageDTO<ResGoodsPromotionRuleDTO> pageResult = new ExecutePageDTO<>();
        // 出参转换
        if (executeDTO.getData() != null) {
            List<ResGoodsPromotionRuleDTO> list = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResGoodsPromotionRuleDTO.class);
            pageResult.setRows(list);
            pageResult.setTotal(executeDTO.getData().getTotal());
        }

        // 返回结果
        return ExecuteDTO.success(pageResult);
    }

    /**
     * @param promotionNo 活动编码
     * @Description : 查询活动基本信息
     * <AUTHOR> 高繁
     * @date : 2021/7/5 15:39
     */
    @Override
    public ExecuteDTO<ResGoodsPromotionRuleDTO> getGoodsPromotionRule(String promotionNo) {
        if (StringUtils.isBlank(promotionNo)) {
            throw new BaseException(CommonCode.CODE_10000001, "promotionNo");
        }
        //获取缓存数据
        String cacheKey = String.format(PromotionKeyConstant.SECOND_SKILL_PROMOTION_RULE, promotionNo);
        if (redisUtil.get(cacheKey) != null) {
            ResGoodsPromotionRuleDTO resGoodsPromotionRuleDTO = JSON.parseObject((String) redisUtil.get(cacheKey), ResGoodsPromotionRuleDTO.class);
            log.info(String.format("缓存活动数据:%s", (String) redisUtil.get(cacheKey)));
            return ExecuteDTO.success(resGoodsPromotionRuleDTO);
        } else {
            AtomReqPromotionInfoDTO reqPromotionInfoDTO = new AtomReqPromotionInfoDTO();
            reqPromotionInfoDTO.setPromotionNo(promotionNo);
            ExecuteDTO<AtomResGoodsPromotionRuleDTO> executeDTO = atomGoodsPromotionRuleAnalysisService.getGoodsPromotionRuleByPromotionNo(reqPromotionInfoDTO);
            if (!executeDTO.successFlag()) {
                return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
            }
            return ExecuteDTO.success(BeanCopierUtil.copy(executeDTO.getData(), ResGoodsPromotionRuleDTO.class));
        }


    }

    /**
     * 根据活动编号查询活动的基本信息以及促销规则信息
     *
     * @param promotionInfoDTO
     * @return
     * <AUTHOR>
     * @date 2021-07-05
     */
    @Override
    public ExecuteDTO<ResGoodsPromotionRuleInfoDTO> getGoodsPromotionRuleInfo(ReqPromotionInfoDTO promotionInfoDTO) {
        log.info("--GoodsPromotionRuleAnalysisServiceImpl.getGoodsPromotionRuleInfo-入参-promotionInfoDTO-{}", promotionInfoDTO);
        // 入参校验
        goodsPromotionAssert.getGoodsPromotionRuleInfoAssert(promotionInfoDTO);

        // 查询促销活动的活动规则以及促销规则信息
        AtomReqPromotionInfoDTO atomReqPromotionInfoDTO = new AtomReqPromotionInfoDTO();
        atomReqPromotionInfoDTO.setPromotionNo(promotionInfoDTO.getPromotionNo());
        ExecuteDTO<AtomResGoodsPromotionRuleDTO> ruleInfoExecuteDTO = atomGoodsPromotionRuleAnalysisService.getGoodsPromotionRuleByPromotionNo(atomReqPromotionInfoDTO);
        if (null == ruleInfoExecuteDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!ruleInfoExecuteDTO.successFlag()) {
            return ExecuteDTO.error(ruleInfoExecuteDTO.getStatus(), ruleInfoExecuteDTO.getMsg());
        }
        if (null == ruleInfoExecuteDTO.getData()) {
            return ExecuteDTO.success();
        }

        // 出参转换
        ResGoodsPromotionRuleInfoDTO resGoodsPromotionRuleInfoDTO = BeanCopierUtil.copy(ruleInfoExecuteDTO.getData(), ResGoodsPromotionRuleInfoDTO.class);

        // 查询活动的时间段信息
        AtomReqGoodsPromotionPeriodDTO reqDTO = new AtomReqGoodsPromotionPeriodDTO();
        reqDTO.setPromotionNo(promotionInfoDTO.getPromotionNo());
        ExecuteDTO<List<AtomResGoodsPromotionPeriodDTO>> periodListExecuteDTO = atomGoodsPromotionPeriodAnalysisService.getGoodsPromotionPeriodByPromotionNo(reqDTO);
        if (null == periodListExecuteDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!periodListExecuteDTO.successFlag()) {
            return ExecuteDTO.error(periodListExecuteDTO.getStatus(), periodListExecuteDTO.getMsg());
        }

        // 活动时间段出参转换
        List<ResGoodsPromotionPeriodDTO> periodDTOList = BeanCopierUtil.copyList(periodListExecuteDTO.getData(), ResGoodsPromotionPeriodDTO.class);
        resGoodsPromotionRuleInfoDTO.setGoodsPromotionPeriodDTOList(periodDTOList);

        // 返回结果
        return ExecuteDTO.success(resGoodsPromotionRuleInfoDTO);
    }

    @Override
    public ExecuteDTO<ResGoodsPromotionPeriodDTO> getPromotionPeriodInfo(ReqGoodsPromotionPeriodDTO reqDTO) {
        log.info("--GoodsPromotionRuleAnalysisServiceImpl.getPromotionPeriodInfo-入参-ReqGoodsPromotionPeriodDTO-{}", reqDTO);
        // 入参校验
        goodsPromotionAssert.getPromotionPeriodInfo(reqDTO);
        // 首先根据活动编号和时间段编号查询活动场次信息
        AtomReqGoodsPromotionPeriodDTO periodDTO = BeanCopierUtil.copy(reqDTO, AtomReqGoodsPromotionPeriodDTO.class);
        ExecuteDTO<AtomResGoodsPromotionPeriodDTO> promotionPeriodDTOExecuteDTO = atomGoodsPromotionPeriodAnalysisService.selectPromotionPeriodInfo(periodDTO);
        if (null == promotionPeriodDTOExecuteDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!promotionPeriodDTOExecuteDTO.successFlag()) {
            return ExecuteDTO.error(promotionPeriodDTOExecuteDTO.getStatus(), promotionPeriodDTOExecuteDTO.getMsg());
        }
        return ExecuteDTO.success(BeanCopierUtil.copy(promotionPeriodDTOExecuteDTO.getData(), ResGoodsPromotionPeriodDTO.class));
    }

    /**
     * 功能描述: 查询活动时间
     *
     * @param queryDto
     * @author: LIXIANG
     * @date: 2023-04-20
     */
    @Override
    public ExecuteDTO<List<ResGoodsPromotionPeriodDTO>> getPromotionPeriodInfo(ReqPromotionQueryDTO queryDto) {
        AtomReqGoodsPromotionPeriodDTO periodDTO = new AtomReqGoodsPromotionPeriodDTO();
        Set<String> promotionNos = new HashSet<>();
        if (StringUtils.isNotBlank(queryDto.getPromotionNo())) {
            promotionNos.add(queryDto.getPromotionNo());
        }
        if (ListUtil.isNotEmpty(queryDto.getPromotionNos())) {
            promotionNos.addAll(queryDto.getPromotionNos());
        }
        periodDTO.setPromotionNoList(new ArrayList<>(promotionNos));
        ExecuteDTO<List<AtomResGoodsPromotionPeriodDTO>> executePeriodDTO = atomGoodsPromotionPeriodAnalysisService.getGoodsPromotionPeriodByPromotionNo(periodDTO);
        if (null == executePeriodDTO) {
            return ExecuteDTOUtil.error(CommonCode.CODE_10000003);
        } else if (!executePeriodDTO.successFlag()) {
            return ExecuteDTOUtil.error(executePeriodDTO.getStatus(), executePeriodDTO.getMsg());
        } else {
            return ExecuteDTO.ok(BeanCopierUtil.copyList(executePeriodDTO.getData(), ResGoodsPromotionPeriodDTO.class));
        }
    }


    /**
     * 查询对应场次下的活动基本信息
     *
     * @param reqDTO 查询条件
     * @date 2021-12-06
     */
    @Override
    public ExecuteDTO<ResGoodsPromotionPeriodDTO> getPromotionPeriodBaseInfo(ReqGoodsPromotionPeriodDTO reqDTO) {
        log.info("getPromotionPeriodBaseInfo-入参：{}", reqDTO);
        // 入参校验
        goodsPromotionAssert.getPromotionPeriodBaseInfoAssert(reqDTO);

        // 查询数据
        AtomReqGoodsPromotionPeriodDTO reqBaseInfo = BeanCopierUtil.copy(reqDTO, AtomReqGoodsPromotionPeriodDTO.class);
        ExecuteDTO<AtomResGoodsPromotionPeriodDTO> executeDTO =
                atomGoodsPromotionPeriodAnalysisService.getPromotionPeriodBaseInfo(reqBaseInfo);
        if (null == executeDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }

        // 返回结果
        return ExecuteDTO.success(BeanCopierUtil.copy(executeDTO.getData(), ResGoodsPromotionPeriodDTO.class));
    }

    /**
     * 千橙掌柜收银-商品列表-查询进行中的满减满折活动
     *
     * @param reqDTO 查询参数
     * @return 进行中的满减满折活动列表
     * <AUTHOR>
     * @date 2022-02-19
     */
    @Override
    public ExecuteDTO<List<ResGoodsPromotionRuleInfoDTO>> getFullDiscountForSmartCashier(ReqPromotionInfoDTO reqDTO) {
        log.info("getFullDiscountForSmartCashier-入参：{}", reqDTO);
        // 入参校验
        goodsPromotionAssert.getFullDiscountAssert(reqDTO);

        // 查询满减满折活动
        AtomReqPromotionInfoDTO reqPromotionInfoDTO = BeanCopierUtil.copy(reqDTO, AtomReqPromotionInfoDTO.class);
        ExecuteDTO<List<AtomResGoodsPromotionRuleDTO>> executeDTO =
                this.atomGoodsPromotionRuleAnalysisService.getFullDiscountForSmartCashier(reqPromotionInfoDTO);
        if (null == executeDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }

        // 返回结果
        return ExecuteDTO.success(BeanCopierUtil.copyList(executeDTO.getData(), ResGoodsPromotionRuleInfoDTO.class));
    }

    //    202503
    @Override
    public ExecuteDTO<ResFullDiscountInfoDTO> getFullDiscountInfo(ReqFullDiscountInfoAddDTO reqDTO) {
        log.info("getFullDiscountInfo-入参：{}", reqDTO);
        // 入参校验
        this.goodsPromotionAssert.getFullDiscountInfo(reqDTO);

        // 查询促销活动的活动规则以及促销规则信息
        AtomReqPromotionInfoDTO atomReqPromotionInfoDTO = new AtomReqPromotionInfoDTO();
        atomReqPromotionInfoDTO.setPromotionNo(reqDTO.getPromotionNo());
        ExecuteDTO<AtomResGoodsPromotionRuleDTO> ruleInfoExecuteDTO = atomGoodsPromotionRuleAnalysisService.getGoodsPromotionRuleByPromotionNo(atomReqPromotionInfoDTO);
        if (null == ruleInfoExecuteDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!ruleInfoExecuteDTO.successFlag()) {
            return ExecuteDTO.error(ruleInfoExecuteDTO.getStatus(), ruleInfoExecuteDTO.getMsg());
        }
        if (null == ruleInfoExecuteDTO.getData()) {
            return ExecuteDTO.success();
        }

        // 出参转换
        ResFullDiscountInfoDTO resGoodsPromotionRuleInfoDTO = BeanCopierUtil.copy(ruleInfoExecuteDTO.getData(), ResFullDiscountInfoDTO.class);

        // 查询活动的时间段信息
        AtomReqGoodsPromotionPeriodDTO periodDTO = new AtomReqGoodsPromotionPeriodDTO();
        periodDTO.setPromotionNo(reqDTO.getPromotionNo());
        ExecuteDTO<List<AtomResGoodsPromotionPeriodDTO>> periodListExecuteDTO = atomGoodsPromotionPeriodAnalysisService.getGoodsPromotionPeriodByPromotionNo(periodDTO);
        if (null == periodListExecuteDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!periodListExecuteDTO.successFlag()) {
            return ExecuteDTO.error(periodListExecuteDTO.getStatus(), periodListExecuteDTO.getMsg());
        }

        // 活动时间段出参转换
        List<ResGoodsPromotionPeriodDTO> periodDTOList = BeanCopierUtil.copyList(periodListExecuteDTO.getData(), ResGoodsPromotionPeriodDTO.class);
        resGoodsPromotionRuleInfoDTO.setGoodsPromotionPeriodDTOList(periodDTOList);

        // 规则档位查询
        List<ResFullDiscountInfoMultiLevelDTO> multiLevelDTOS = goodsPromotionMultiLevelService.getMultiLevelsByPromotionNo(reqDTO.getPromotionNo());
        if (ListUtil.isNotEmpty(multiLevelDTOS)) {
            resGoodsPromotionRuleInfoDTO.setMultiLevelDTOS(multiLevelDTOS);
        }

        // 根据活动编号查询该店铺活动参与的所有商品数据
        AtomReqGoodsPromotionGoodsRelationDTO reqPromotionGoods = new AtomReqGoodsPromotionGoodsRelationDTO();
        reqPromotionGoods.setPromotionNo(reqDTO.getPromotionNo());
        // 促销活动查询时，会传活动场次编号，用于查询对应活动场次的商品数据
        reqPromotionGoods.setPeriodNo(reqDTO.getPeriodNo());

        ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> executeDTO = atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList(reqPromotionGoods);
        if (null == executeDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        List<AtomResGoodsPromotionGoodsRelationDTO> promotionGoodsList = executeDTO.getData();
        if (CollectionUtils.isNotEmpty(promotionGoodsList)) {
            List<ResCouponGoodsRelationDTO> goodsRelationDTOS = BeanCopierUtil.copyList(promotionGoodsList, ResCouponGoodsRelationDTO.class);
            this.setGoodsInfo(goodsRelationDTOS);
            resGoodsPromotionRuleInfoDTO.setReqCouponGoodsRelationDTOS(goodsRelationDTOS);
        }
        // 类目关联类目
        AtomReqGoodsPromotionCategoryRelationDTO relationDTO = new AtomReqGoodsPromotionCategoryRelationDTO();
        relationDTO.setPromotionNo(reqDTO.getPromotionNo());
        ExecuteDTO<List<AtomResGoodsPromotionCategoryRelationDTO>> categoryByParam = atomGoodsPromotionCategoryRelationAnalysisService.getGoodsPromotionCategoryByParam(relationDTO);
        if (null == categoryByParam) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!categoryByParam.successFlag()) {
            return ExecuteDTO.error(categoryByParam.getStatus(), categoryByParam.getMsg());
        }
        if (CollectionUtils.isNotEmpty(categoryByParam.getData())) {
            List<ResCouponCategoryRelationDTO> categoryRelationDTOS = BeanCopierUtil.copyList(categoryByParam.getData(), ResCouponCategoryRelationDTO.class);
            if (CollectionUtils.isNotEmpty(categoryRelationDTOS)) {
                List<String> categoryNoList = categoryRelationDTOS.stream().map(ResCouponCategoryRelationDTO::getCategoryNo).collect(Collectors.toList());
                ReqSaleCategoryRelationDTO relation = new ReqSaleCategoryRelationDTO();
                relation.setCategoryNoList(categoryNoList);
                ExecuteDTO<List<ResSaleCategoryDTO>> saleCategoryInfo = saleCategoryAnalysisService.getSaleCategoryInfo(relation);
                if (null == saleCategoryInfo) {
                    return ExecuteDTO.error(CommonCode.CODE_10000003);
                }
                if (!saleCategoryInfo.successFlag()) {
                    return ExecuteDTO.error(saleCategoryInfo.getStatus(), saleCategoryInfo.getMsg());
                }
                if (CollectionUtils.isNotEmpty(saleCategoryInfo.getData())) {
                    List<ResSaleCategoryDTO> categoryInfoData = saleCategoryInfo.getData();
                    categoryRelationDTOS.forEach(couponCategoryRelation -> {
                        categoryInfoData.forEach(categoryInfo -> {
                            if (couponCategoryRelation.getCategoryNo().equals(categoryInfo.getCategoryNo())) {
                                couponCategoryRelation.setFirstCategoryNo(categoryInfo.getFirstCategoryNo());
                                couponCategoryRelation.setSecondCategoryNo(categoryInfo.getSecondCategoryNo());
                                couponCategoryRelation.setThirdCategoryNo(categoryInfo.getThirdCategoryNo());
                            }
                        });
                    });
                }
            }
            resGoodsPromotionRuleInfoDTO.setReqCouponCategoryRelationDTOS(categoryRelationDTOS);
        }

        // 返回结果
        return ExecuteDTO.success(resGoodsPromotionRuleInfoDTO);
    }

    /**
     * 千橙掌柜收银-塞商品信息
     *
     * <AUTHOR>
     * @date 2022-02-22
     */
    private void setGoodsInfo(List<ResCouponGoodsRelationDTO> goodsRelationDTOS) {
        List<String> goodsNoList = goodsRelationDTOS.stream().map(ResCouponGoodsRelationDTO::getGoodsNo).collect(Collectors.toList());
        ReqGoodsDTO goodsDTO = new ReqGoodsDTO();
        goodsDTO.setNoPage();
        goodsDTO.setGoodsNos(goodsNoList);
        ExecuteDTO<ExecutePageDTO<ResGoodsDTO>> goodsExecuteDTO = goodsAnalysisService.getGoodsPage(goodsDTO);
        if (goodsExecuteDTO.successFlag()) {
            List<ResGoodsDTO> goodsList = goodsExecuteDTO.getData().getRows();
            // 商品map
            Map<String, ResGoodsDTO> goodsDTOMap = goodsList.stream().collect(Collectors.toMap(ResGoodsDTO::getGoodsNo, ResGoodsDTO -> ResGoodsDTO));
            goodsRelationDTOS.forEach(goods -> {
                ResGoodsDTO resGoodsDTO = goodsDTOMap.get(goods.getGoodsNo());
                goods.setSalesVolume(resGoodsDTO.getSalesVolume());
                goods.setCanSaleStockNum(resGoodsDTO.getCanSaleStockNum());
                goods.setMainPictureUrl(resGoodsDTO.getMainPictureUrl());
                goods.setGoodsName(resGoodsDTO.getGoodsName());
                goods.setRetailPrice(resGoodsDTO.getRetailPrice());
                goods.setGoodsForm(resGoodsDTO.getGoodsForm());
            });
        }

    }

    @Override
    public ExecuteDTO<List<ResFullDiscountInfoMultiLevelDTO>> getMultiLevelsByPromotionNos(ReqPromotionInfoDTO reqPromotionInfoDTO) {
        // 规则档位查询
        List<ResFullDiscountInfoMultiLevelDTO> multiLevelDTOS = goodsPromotionMultiLevelService.getMultiLevelsByPromotionNos(reqPromotionInfoDTO.getPromotionNoList());
        if (ListUtil.isNotEmpty(multiLevelDTOS)) {
            return ExecuteDTO.ok(multiLevelDTOS);
        } else {
            return ExecuteDTO.ok(Collections.emptyList());
        }
    }

    /**
     * 千橙APP查询特惠促销活动详情
     *
     * @param queryDTO queryDTO
     * @return 活动详情
     * <AUTHOR>
     * @date 2022-07-22
     */
    @Override
    public ExecuteDTO<ResVipPriceDetailDTO> getVipPriceDetailByPromotionNo(ReqPromotionQueryDTO queryDTO) {
        log.info("getVipPriceDetailByPromotionNo-入参：{}", queryDTO);
        final String promotionNo = queryDTO.getPromotionNo();

        // 查询促销活动的活动规则以及促销规则信息
        AtomReqPromotionInfoDTO atomReqPromotionInfoDTO = new AtomReqPromotionInfoDTO();
        atomReqPromotionInfoDTO.setPromotionNo(promotionNo);
        ExecuteDTO<AtomResGoodsPromotionRuleDTO> ruleInfoExecuteDTO = atomGoodsPromotionRuleAnalysisService.getGoodsPromotionRuleByPromotionNo(atomReqPromotionInfoDTO);
        if (null == ruleInfoExecuteDTO) {
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        }
        if (!ruleInfoExecuteDTO.successFlag()) {
            return new ExecuteDTO<>(ruleInfoExecuteDTO.getStatus(), ruleInfoExecuteDTO.getMsg(), null);
        }
        if (null == ruleInfoExecuteDTO.getData()) {
            return ExecuteDTO.ok();
        }

        // 出参转换
        ResVipPriceDetailDTO resVipPriceDetailDTO = BeanCopierUtil.copy(ruleInfoExecuteDTO.getData(), ResVipPriceDetailDTO.class);

        // 查询活动的时间段信息
        AtomReqGoodsPromotionPeriodDTO periodDTO = new AtomReqGoodsPromotionPeriodDTO();
        periodDTO.setPromotionNo(promotionNo);
        ExecuteDTO<List<AtomResGoodsPromotionPeriodDTO>> periodListExecuteDTO = atomGoodsPromotionPeriodAnalysisService.getGoodsPromotionPeriodByPromotionNo(periodDTO);
        if (null == periodListExecuteDTO) {
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        }
        if (!periodListExecuteDTO.successFlag()) {
            return new ExecuteDTO<>(periodListExecuteDTO.getStatus(), periodListExecuteDTO.getMsg(), null);
        }

        // 活动时间段
        AtomResGoodsPromotionPeriodDTO promotionPeriodDTO = ListUtil.getFirst(periodListExecuteDTO.getData());
        resVipPriceDetailDTO.setStartTime(promotionPeriodDTO.getStartTime());
        resVipPriceDetailDTO.setEndTime(promotionPeriodDTO.getEndTime());

        // 根据活动编号查询该店铺活动参与的所有商品数据
        AtomReqGoodsPromotionGoodsRelationDTO reqPromotionGoods = new AtomReqGoodsPromotionGoodsRelationDTO();
        reqPromotionGoods.setPromotionNo(promotionNo);
        // 促销活动查询时，会传活动场次编号，用于查询对应活动场次的商品数据
        reqPromotionGoods.setPeriodNo(promotionPeriodDTO.getPeriodNo());

        ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> executeDTO = atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList(reqPromotionGoods);
        if (null == executeDTO) {
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        }
        if (!executeDTO.successFlag()) {
            return new ExecuteDTO<>(executeDTO.getStatus(), executeDTO.getMsg(), null);
        }
        List<AtomResGoodsPromotionGoodsRelationDTO> promotionGoodsList = executeDTO.getData();
        if (CollectionUtils.isNotEmpty(promotionGoodsList)) {
            List<ResVipPriceGoodsDTO> goodsRelationDTOS = BeanCopierUtil.copyList(promotionGoodsList, ResVipPriceGoodsDTO.class);
            this.convertVipPriceGoodsInfo(goodsRelationDTOS);
            resVipPriceDetailDTO.setGoodsList(goodsRelationDTOS);
        }
        // 返回结果
        return ExecuteDTO.ok(resVipPriceDetailDTO);
    }

    /**
     * 特惠促销活动商品列表
     *
     * <AUTHOR>
     * @date 2022-07-22
     */
    private void convertVipPriceGoodsInfo(List<ResVipPriceGoodsDTO> goodsRelationDTOS) {
        List<String> goodsNoList = goodsRelationDTOS.stream().map(ResVipPriceGoodsDTO::getGoodsNo).collect(Collectors.toList());
        Map<String, ResGoodsDTO> goodsDTOMap = this.getGoodsDTOMap(goodsNoList);
        goodsRelationDTOS.forEach(goods -> {
            ResGoodsDTO resGoodsDTO = goodsDTOMap.get(goods.getGoodsNo());
            if (null != resGoodsDTO) {//goodsFormValue
                goods.setGoodsName(resGoodsDTO.getGoodsName());
                goods.setCanSaleStockNum(resGoodsDTO.getCanSaleStockNum());
                goods.setCalculationUnitNo(resGoodsDTO.getCalculationUnitNo());
                goods.setCalculationUnitName(resGoodsDTO.getCalculationUnitName());
                goods.setFirstAttributeName(resGoodsDTO.getFirstAttributeName());
                goods.setFirstAttributeValueName(resGoodsDTO.getFirstAttributeValueName());
                goods.setSecondAttributeName(resGoodsDTO.getSecondAttributeName());
                goods.setSecondAttributeValueName(resGoodsDTO.getSecondAttributeValueName());
                goods.setThirdAttributeName(resGoodsDTO.getThirdAttributeName());
                goods.setThirdAttributeValueName(resGoodsDTO.getThirdAttributeValueName());
                goods.setMainPictureUrl(resGoodsDTO.getMainPictureUrl());
                goods.setRetailPrice(resGoodsDTO.getRetailPrice());
                goods.setGoodsStatus(resGoodsDTO.getGoodsStatus());
                goods.setGoodsDeleteFlag(WhetherEnum.NO.getCode());
                goods.setGoodsForm(resGoodsDTO.getGoodsForm());
                if(StringUtils.isNotEmpty(resGoodsDTO.getGoodsFormValue())){// 20250514 东启修改 历史遗留
                    if(resGoodsDTO.getGoodsFormValue().indexOf("多单位") != -1){
                        goods.setGoodsForm("1004");
                    }
                }
                // 20230928蛋品 lixiang  商品管理 多单位商品，多单位主品名称拼接单位名称
                goods.setMultiUnitType(resGoodsDTO.getMultiUnitType());
                goods.setMultiUnitGoodsNo(resGoodsDTO.getMultiUnitGoodsNo());
                if (MultiUnitTypeEnum.MAIN_UNIT.getCode().equals(goods.getMultiUnitType())) {
                    goods.setGoodsName(goods.getGoodsName() + "(" + goods.getCalculationUnitName() + ")");
                }
                // 规格属性
                StringBuilder attributeNames = new StringBuilder();
                if (StringUtils.isNotBlank(resGoodsDTO.getFirstAttributeValueName())) {
                    attributeNames.append(resGoodsDTO.getFirstAttributeValueName());
                }
                if (StringUtils.isNotBlank(resGoodsDTO.getSecondAttributeValueName())) {
                    if (StringUtils.isNotBlank(attributeNames.toString())) {
                        attributeNames.append("-");
                    }
                    attributeNames.append(resGoodsDTO.getSecondAttributeValueName());
                }
                if (StringUtils.isNotBlank(resGoodsDTO.getThirdAttributeValueName())) {
                    if (StringUtils.isNotBlank(attributeNames.toString())) {
                        attributeNames.append("-");
                    }
                    attributeNames.append(resGoodsDTO.getThirdAttributeValueName());
                }
                if (StringUtils.isNotBlank(attributeNames)) {
                    goods.setAttributeNames(attributeNames.toString());
                }
            }
        });
    }

    /**
     * 千橙APP查询社群接龙活动详情
     *
     * @param queryDTO queryDTO
     * @return 活动详情
     * <AUTHOR>
     * @date 2022-07-22
     */
    @Override
    public ExecuteDTO<ResCommunitySolitaireDetailDTO> getCommunitySolitaireDetailByPromotionNo(ReqPromotionQueryDTO queryDTO) {
        log.info("getCommunitySolitaireDetailByPromotionNo-入参：{}", queryDTO);
        final String promotionNo = queryDTO.getPromotionNo();

        // 查询促销活动的活动规则以及促销规则信息
        AtomReqPromotionInfoDTO atomReqPromotionInfoDTO = new AtomReqPromotionInfoDTO();
        atomReqPromotionInfoDTO.setPromotionNo(promotionNo);
        ExecuteDTO<AtomResGoodsPromotionRuleDTO> ruleInfoExecuteDTO = atomGoodsPromotionRuleAnalysisService.getGoodsPromotionRuleByPromotionNo(atomReqPromotionInfoDTO);
        if (null == ruleInfoExecuteDTO) {
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        }
        if (!ruleInfoExecuteDTO.successFlag()) {
            return new ExecuteDTO<>(ruleInfoExecuteDTO.getStatus(), ruleInfoExecuteDTO.getMsg(), null);
        }
        if (null == ruleInfoExecuteDTO.getData()) {
            return ExecuteDTO.ok();
        }

        // 出参转换
        ResCommunitySolitaireDetailDTO resCommunitySolitaireDetailDTO = BeanCopierUtil.copy(ruleInfoExecuteDTO.getData(), ResCommunitySolitaireDetailDTO.class);

        // 根据活动编号查询该店铺活动参与的所有商品数据

        AtomReqGoodsPromotionGoodsRelationDTO reqPromotionGoods = new AtomReqGoodsPromotionGoodsRelationDTO();
        reqPromotionGoods.setPromotionNo(promotionNo);
        ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> executeDTO = atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList(reqPromotionGoods);
        if (null == executeDTO) {
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        }
        if (!executeDTO.successFlag()) {
            return new ExecuteDTO<>(executeDTO.getStatus(), executeDTO.getMsg(), null);
        }

        List<AtomResGoodsPromotionGoodsRelationDTO> promotionGoodsList = executeDTO.getData();
        if (CollectionUtils.isNotEmpty(promotionGoodsList)) {
            List<ResCommunitySolitaireGoodsDTO> goodsRelationDTOS = BeanCopierUtil.copyList(promotionGoodsList, ResCommunitySolitaireGoodsDTO.class);
            this.convertCommunitySolitaireGoodsInfo(goodsRelationDTOS);
            resCommunitySolitaireDetailDTO.setGoodsList(goodsRelationDTOS);
        }

        if (PromotionTypeEnum.COMMUNITY_SOLITAIRE_SJ.getCode().equals(resCommunitySolitaireDetailDTO.getPromotionType())) {
            //分发店铺
            AtomReqPromotionStoreRelationDTO promotionStoreRelationDTO = new AtomReqPromotionStoreRelationDTO();
            promotionStoreRelationDTO.setPromotionNo(promotionNo);
            promotionStoreRelationDTO.setMerchantNo(resCommunitySolitaireDetailDTO.getMerchantNo());
            promotionStoreRelationDTO.setDisableFlag(StoreUseEnum.ENABLE.getCode());
            ExecuteDTO<List<AtomResPromotionStoreRelationDTO>> resPromotionStoreRelationDTO = promotionStoreRelationAnalysisService.getStoreRelationList(promotionStoreRelationDTO);
            if (null == resPromotionStoreRelationDTO) {
                return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
            }
            if (!resPromotionStoreRelationDTO.successFlag()) {
                return new ExecuteDTO<>(resPromotionStoreRelationDTO.getStatus(), resPromotionStoreRelationDTO.getMsg(), null);
            }
            List<AtomResPromotionStoreRelationDTO> resPromotionStoreRelationList = resPromotionStoreRelationDTO.getData();
            if (CollectionUtils.isNotEmpty(resPromotionStoreRelationList)) {
                //查询商家所有店铺信息
                MerchantStoreRequest merchantStoreRequest = new MerchantStoreRequest();
                merchantStoreRequest.setMerchantNo(resCommunitySolitaireDetailDTO.getMerchantNo());
                ExecuteDTO<List<MerchantStoreResponse>> merchantStoreResponseExecuteDTO = storeProcessService.queryMerchantStore(merchantStoreRequest);
                if (null == merchantStoreResponseExecuteDTO) {
                    return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
                }
                if (!merchantStoreResponseExecuteDTO.successFlag()) {
                    return new ExecuteDTO<>(merchantStoreResponseExecuteDTO.getStatus(), merchantStoreResponseExecuteDTO.getMsg(), null);
                }
                List<MerchantStoreResponse> merchantStoreResponseList = merchantStoreResponseExecuteDTO.getData();
                //商家没有店铺
                if (CollectionUtils.isEmpty(merchantStoreResponseList)) {
                    return new ExecuteDTO<>(MarketErrorCode.CODE_17000636.getCode(), MarketErrorCode.CODE_17000636.getInMsg(), null);
                }
                List<ResPromotionStoreRelationDTO> storeRelationListDtos = BeanCopierUtil.copyList(resPromotionStoreRelationList, ResPromotionStoreRelationDTO.class);
                //设置活动分发店铺类型 1001全部 1003指定店铺
                storeRelationListDtos.forEach(storeRelation -> {
                    if (StringUtils.isEmpty(storeRelation.getStoreNo())) {
                        resCommunitySolitaireDetailDTO.setStoreType(storeRelation.getStoreType());
                    }
                });
                //指定店铺
                if (StoreTypeEnum.STORE_TYPE_THREE.getCode().equals(resCommunitySolitaireDetailDTO.getStoreType())) {
                    List<ResPromotionStoreRelationDTO> storeRelationList = storeRelationListDtos.stream()
                            .filter(storeRelation -> StringUtils.isNotEmpty(storeRelation.getStoreNo())).collect(Collectors.toList());
                    Map<String, MerchantStoreResponse> merchantStoreMap = merchantStoreResponseList.stream().collect(Collectors.toMap(MerchantStoreResponse::getStoreNo, store -> store));
                    storeRelationList.forEach(storeRelation -> {
                        MerchantStoreResponse storeResponse = merchantStoreMap.get(storeRelation.getStoreNo());
                        if (storeResponse != null) {
                            storeRelation.setStoreName(storeResponse.getStoreName());
                            storeRelation.setBusinessModel(storeResponse.getBusinessModel());
                        }
                    });
                    //指定店铺列表
                    resCommunitySolitaireDetailDTO.setStoreList(storeRelationList);
                    //店铺查询接龙详情，活动上下架状态
                    if (StringUtils.isNotEmpty(queryDTO.getStoreNo())) {
                        storeRelationList.forEach(storeRelation -> {
                            if (queryDTO.getStoreNo().equals(storeRelation.getStoreNo())) {
                                if (StoreUpDownEnum.UP.getCode().equals(resCommunitySolitaireDetailDTO.getUpDownFlag())
                                        && StoreUpDownEnum.UP.getCode().equals(storeRelation.getUpDownFlag())) {
                                    resCommunitySolitaireDetailDTO.setUpDownFlag(StoreUpDownEnum.UP.getCode());
                                } else {
                                    resCommunitySolitaireDetailDTO.setUpDownFlag(StoreUpDownEnum.DOWN.getCode());
                                }
                            }
                        });
                    }
                }
            }
        }

        // 活动图片
        List<PromotionImageDTO> promotionImageDTOS = goodsPromotionImageService.getImagesByPromotionNo(promotionNo);
        resCommunitySolitaireDetailDTO.setPromotionImages(BeanCopierUtil.copyList(promotionImageDTOS, PromotionImageDTO.class));

        // 返回结果
        return ExecuteDTO.ok(resCommunitySolitaireDetailDTO);
    }

    /**
     * 社群接龙活动商品列表
     *
     * <AUTHOR>
     * @date 2022-07-22
     */
    private void convertCommunitySolitaireGoodsInfo(List<ResCommunitySolitaireGoodsDTO> goodsRelationDTOS) {
        List<String> goodsNoList = goodsRelationDTOS.stream().map(ResCommunitySolitaireGoodsDTO::getGoodsNo).collect(Collectors.toList());
        Map<String, ResGoodsDTO> goodsDTOMap = this.getGoodsDTOMap(goodsNoList);
        goodsRelationDTOS.forEach(goods -> {
            ResGoodsDTO resGoodsDTO = goodsDTOMap.get(goods.getGoodsNo());
            if (null != resGoodsDTO) {
                goods.setGoodsName(resGoodsDTO.getGoodsName());
                goods.setCanSaleStockNum(resGoodsDTO.getCanSaleStockNum());
                goods.setCalculationUnitNo(resGoodsDTO.getCalculationUnitNo());
                goods.setCalculationUnitName(resGoodsDTO.getCalculationUnitName());
                goods.setFirstAttributeName(resGoodsDTO.getFirstAttributeName());
                goods.setFirstAttributeValueName(resGoodsDTO.getFirstAttributeValueName());
                goods.setSecondAttributeName(resGoodsDTO.getSecondAttributeName());
                goods.setSecondAttributeValueName(resGoodsDTO.getSecondAttributeValueName());
                goods.setThirdAttributeName(resGoodsDTO.getThirdAttributeName());
                goods.setThirdAttributeValueName(resGoodsDTO.getThirdAttributeValueName());
                goods.setMainPictureUrl(resGoodsDTO.getMainPictureUrl());
                goods.setRetailPrice(resGoodsDTO.getRetailPrice());
                goods.setMarketPrice(resGoodsDTO.getMarketPrice());
                if (GoodsSourceTypeEnum.MERCHANT.getCode().equals(resGoodsDTO.getGoodsSourceType())) {
                    // 商家商品全部认为是上架
                    goods.setGoodsStatus(GoodsStatusEnum.ON_SHELF.getCode());
                } else {
                    goods.setGoodsStatus(resGoodsDTO.getGoodsStatus());
                }
                goods.setGoodsDeleteFlag(resGoodsDTO.getDeleteFlag());
                goods.setGoodsContent(resGoodsDTO.getContent());
                goods.setGoodsForm(resGoodsDTO.getGoodsForm());
                goods.setGoodsFormValue(resGoodsDTO.getGoodsFormValue());
                if (CollectionUtils.isNotEmpty(resGoodsDTO.getDetailPictureList())) {
                    List<String> detailPictureList = Lists.newArrayList();
                    resGoodsDTO.getDetailPictureList().forEach(resGoodsMediaDTO -> detailPictureList.add(resGoodsMediaDTO.getPictureUrl()));
                    goods.setDetailPictureList(detailPictureList);
                }
                // 20230928蛋品 lixiang  商品管理 多单位商品，多单位主品名称拼接单位名称
                goods.setMultiUnitType(resGoodsDTO.getMultiUnitType());
                goods.setMultiUnitGoodsNo(resGoodsDTO.getMultiUnitGoodsNo());
                if (MultiUnitTypeEnum.MAIN_UNIT.getCode().equals(goods.getMultiUnitType())) {
                    goods.setGoodsName(goods.getGoodsName() + "(" + goods.getCalculationUnitName() + ")");
                }
            }
        });
    }

    private Map<String, ResGoodsDTO> getGoodsDTOMap(List<String> goodsNoList) {
        Map<String, ResGoodsDTO> resultMap = new HashMap<>();

        ReqGoodsDTO goodsDTO = new ReqGoodsDTO();
        goodsDTO.setNoPage();
        goodsDTO.setGoodsNos(goodsNoList);
        goodsDTO.setIsAuxiliaryUnit(WhetherEnum.YES.getCode());
        ExecuteDTO<ExecutePageDTO<ResGoodsDTO>> goodsExecuteDTO = goodsAnalysisService.getGoodsPage(goodsDTO);
        if (goodsExecuteDTO.successFlag()) {
            List<ResGoodsDTO> goodsList = goodsExecuteDTO.getData().getRows();
            Map<String, ResGoodsDTO> goodsDTOMap = ListUtil.toMapKeepFirst(goodsList, ResGoodsDTO::getGoodsNo);
            goodsDTOMap.forEach((k, v) -> v.setDeleteFlag(WhetherEnum.NO.getCode()));
            resultMap.putAll(goodsDTOMap);
            // 如果商品不存在，则以可能被删除，需查询商品名称和主图
            goodsNoList.removeAll(goodsDTOMap.keySet());
            Map<String, ResGoodsDTO> notExistsgoodsDTOMap = this.getAllGoodsByGoodsNos(goodsNoList);
            notExistsgoodsDTOMap.forEach((k, v) -> {
                v.setCanSaleStockNum(BigDecimal.ZERO);
                v.setGoodsStatus(GoodsStatusEnum.OFF_SHELF.getCode());
                v.setDeleteFlag(WhetherEnum.YES.getCode());
                resultMap.putIfAbsent(k, v);
            });
        }

        // 获取商品详情图片和详情描述
        if (CollectionUtils.isNotEmpty(resultMap.keySet())) {
            ReqGoodsDTO reqGoodsDTO = new ReqGoodsDTO();
            reqGoodsDTO.setGoodsNos(Lists.newArrayList(resultMap.keySet()));
            ExecuteDTO<List<ResGoodsDTO>> goodsImageTextDetailsExecuteDTO = goodsAnalysisService.getGoodsImageTextDetails(reqGoodsDTO);

            if (goodsImageTextDetailsExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(goodsImageTextDetailsExecuteDTO.getData())) {
                goodsImageTextDetailsExecuteDTO.getData().forEach(goodsImageTextDetails -> {
                    if (resultMap.containsKey(goodsImageTextDetails.getGoodsNo())) {
                        ResGoodsDTO resGoodsDTO = resultMap.get(goodsImageTextDetails.getGoodsNo());

                        resGoodsDTO.setDetailPictureList(goodsImageTextDetails.getDetailPictureList());
                        resGoodsDTO.setContent(goodsImageTextDetails.getContent());
                    }
                });
            }
        }
        return resultMap;
    }

    /**
     * 查询商品基本信息（包含已删除商品）（包含基本信息和商品主图）
     *
     * @return
     */
    private Map<String, ResGoodsDTO> getAllGoodsByGoodsNos(List<String> goodsNoList) {
        if (ListUtil.isNotEmpty(goodsNoList)) {
            ReqGoodsDTO goodsDTO = new ReqGoodsDTO();
            goodsDTO.setNoPage();
            goodsDTO.setGoodsNos(goodsNoList);
            ExecuteDTO<List<ResGoodsDTO>> goodsExecuteDTO = goodsAnalysisService.getAllGoodsByGoodsNos(goodsDTO);
            if (null != goodsExecuteDTO && ListUtil.isNotEmpty(goodsExecuteDTO.getData())) {
                List<ResGoodsDTO> goodsList = goodsExecuteDTO.getData();
                return ListUtil.toMapKeepFirst(goodsList, ResGoodsDTO::getGoodsNo);
            }
        }
        return new HashMap<>();
    }

    /**
     * 千橙APP查询会员价活动详情
     *
     * @param queryDTO queryDTO
     * @return 活动详情
     * <AUTHOR>
     * @date 2023-04-20
     * <p>
     * 202503东启
     */
    @Override
    public ExecuteDTO<ResMemberPriceDetailDTO> getMemberPriceDetailByPromotionNo(ReqPromotionQueryDTO queryDTO) {
        log.info("getMemberPriceDetailByPromotionNo-入参：{}", queryDTO);
        final String promotionNo = queryDTO.getPromotionNo();

        // 查询促销活动的活动规则以及促销规则信息
        AtomReqPromotionInfoDTO atomReqPromotionInfoDTO = new AtomReqPromotionInfoDTO();
        atomReqPromotionInfoDTO.setPromotionNo(promotionNo);
        ExecuteDTO<AtomResGoodsPromotionRuleDTO> ruleInfoExecuteDTO = atomGoodsPromotionRuleAnalysisService.getGoodsPromotionRuleByPromotionNo(atomReqPromotionInfoDTO);
        if (null == ruleInfoExecuteDTO) {
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        }
        if (!ruleInfoExecuteDTO.successFlag()) {
            return new ExecuteDTO<>(ruleInfoExecuteDTO.getStatus(), ruleInfoExecuteDTO.getMsg(), null);
        }
        if (null == ruleInfoExecuteDTO.getData()) {
            return ExecuteDTO.ok();
        }

        // 出参转换
        ResMemberPriceDetailDTO resMemberPriceDetailDTO = BeanCopierUtil.copy(ruleInfoExecuteDTO.getData(), ResMemberPriceDetailDTO.class);

        // 查询活动的时间段信息
        AtomReqGoodsPromotionPeriodDTO periodDTO = new AtomReqGoodsPromotionPeriodDTO();
        periodDTO.setPromotionNo(promotionNo);
        ExecuteDTO<List<AtomResGoodsPromotionPeriodDTO>> periodListExecuteDTO = atomGoodsPromotionPeriodAnalysisService.getGoodsPromotionPeriodByPromotionNo(periodDTO);
        if (null == periodListExecuteDTO) {
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        }
        if (!periodListExecuteDTO.successFlag()) {
            return new ExecuteDTO<>(periodListExecuteDTO.getStatus(), periodListExecuteDTO.getMsg(), null);
        }
        // 活动时间段
        AtomResGoodsPromotionPeriodDTO promotionPeriodDTO = ListUtil.getFirst(periodListExecuteDTO.getData());
        resMemberPriceDetailDTO.setRepectType(promotionPeriodDTO.getRepectType());
        resMemberPriceDetailDTO.setRepectVal(promotionPeriodDTO.getRepectVal());
        resMemberPriceDetailDTO.setStartTime(promotionPeriodDTO.getStartTime());
        resMemberPriceDetailDTO.setEndTime(promotionPeriodDTO.getEndTime());

        if (GoodsPromotionScopeEnum.SCOPE_TYPE_GOODS.getCode().equals(resMemberPriceDetailDTO.getGoodsPromotionScope())) {
            // 根据活动编号查询该店铺活动参与的所有商品数据
            AtomReqGoodsPromotionGoodsRelationDTO reqPromotionGoods = new AtomReqGoodsPromotionGoodsRelationDTO();
            reqPromotionGoods.setPromotionNo(promotionNo);
            // 促销活动查询时，会传活动场次编号，用于查询对应活动场次的商品数据
            reqPromotionGoods.setPeriodNo(promotionPeriodDTO.getPeriodNo());

            ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> executeDTO = atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList(reqPromotionGoods);
            if (null == executeDTO) {
                return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
            }
            if (!executeDTO.successFlag()) {
                return new ExecuteDTO<>(executeDTO.getStatus(), executeDTO.getMsg(), null);
            }
            List<AtomResGoodsPromotionGoodsRelationDTO> promotionGoodsList = executeDTO.getData();
            if (CollectionUtils.isNotEmpty(promotionGoodsList)) {
                List<ResVipPriceGoodsDTO> goodsRelationDTOS = BeanCopierUtil.copyList(promotionGoodsList, ResVipPriceGoodsDTO.class);
                this.convertVipPriceGoodsInfo(goodsRelationDTOS);
                resMemberPriceDetailDTO.setGoodsList(goodsRelationDTOS);
            }


        }
        if (GoodsPromotionScopeEnum.SCOPE_TYPE_CATEGORY.getCode().equals(resMemberPriceDetailDTO.getGoodsPromotionScope())) {
            // 202503

            AtomReqGoodsPromotionCategoryRelationDTO relationDTO = new AtomReqGoodsPromotionCategoryRelationDTO();
            relationDTO.setPromotionNo(promotionNo);
            ExecuteDTO<List<AtomResGoodsPromotionCategoryRelationDTO>> categoryByParam = atomGoodsPromotionCategoryRelationAnalysisService.getGoodsPromotionCategoryByParam(relationDTO);
            if (null == categoryByParam) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            if (!categoryByParam.successFlag()) {
                return ExecuteDTO.error(categoryByParam.getStatus(), categoryByParam.getMsg());
            }
            if (CollectionUtils.isNotEmpty(categoryByParam.getData())) {
                List<ResCouponCategoryRelationDTO> categoryRelationDTOS = BeanCopierUtil.copyList(categoryByParam.getData(), ResCouponCategoryRelationDTO.class);
                if (CollectionUtils.isNotEmpty(categoryRelationDTOS)) {
                    List<String> categoryNoList = categoryRelationDTOS.stream().map(ResCouponCategoryRelationDTO::getCategoryNo).collect(Collectors.toList());
                    ReqSaleCategoryRelationDTO relation = new ReqSaleCategoryRelationDTO();
                    relation.setCategoryNoList(categoryNoList);
                    ExecuteDTO<List<ResSaleCategoryDTO>> saleCategoryInfo = saleCategoryAnalysisService.getSaleCategoryInfo(relation);
                    if (null == saleCategoryInfo) {
                        return ExecuteDTO.error(CommonCode.CODE_10000003);
                    }
                    if (!saleCategoryInfo.successFlag()) {
                        return ExecuteDTO.error(saleCategoryInfo.getStatus(), saleCategoryInfo.getMsg());
                    }
                    if (CollectionUtils.isNotEmpty(saleCategoryInfo.getData())) {
                        List<ResSaleCategoryDTO> categoryInfoData = saleCategoryInfo.getData();
                        categoryRelationDTOS.forEach(couponCategoryRelation -> {
                            categoryInfoData.forEach(categoryInfo -> {
                                if (couponCategoryRelation.getCategoryNo().equals(categoryInfo.getCategoryNo())) {
                                    couponCategoryRelation.setFirstCategoryNo(categoryInfo.getFirstCategoryNo());
                                    couponCategoryRelation.setSecondCategoryNo(categoryInfo.getSecondCategoryNo());
                                    couponCategoryRelation.setThirdCategoryNo(categoryInfo.getThirdCategoryNo());
                                }
                            });
                        });
                    }
                }
                resMemberPriceDetailDTO.setReqCouponCategoryRelationDTOS(categoryRelationDTOS);
            }
        }


        // 返回结果
        return ExecuteDTO.ok(resMemberPriceDetailDTO);
    }

    @Override
    public ExecuteDTO<ExecutePageDTO<ResStoreMemberPriceRuleDTO>> storeMenberPricePromotions(ReqStoreMemberPriceRuleDTO req) {

        Page pages = PageHelper.startPage(req);

        AtomStoreMemberPriceRuleVo atomStoreMemberPriceRuleVo = BeanCopierUtil.copy(req, AtomStoreMemberPriceRuleVo.class);
        atomStoreMemberPriceRuleVo.setPromotionType(PromotionTypeEnum.MEMBER_PRICE.getCode());
        atomStoreMemberPriceRuleVo.setSourceType(SourceTypeEnum.SOURCE_TYPE_1002.getCode());
        //atomStoreMemberPriceRuleVo.setDeleteFlag(DeleteFlagEnum.NO.getCode()); sql里
        List<AtomStoreMemberPriceRuleVo> atomStoreMemberPriceRuleVos = promotionStoreRelationDao.storeMenberPricePromotion(atomStoreMemberPriceRuleVo);
        for (AtomStoreMemberPriceRuleVo storeMemberPriceRuleVo : atomStoreMemberPriceRuleVos) {
            //退出共享店铺,DisableFlag != 1
            if (!DeleteFlagEnum.NO.getCode().equals(storeMemberPriceRuleVo.getDisableFlag())) {
                //此时活动状态为已过期
                storeMemberPriceRuleVo.setStatus(PromotionStatusEnum.EXPIRED.getCode());
            }
        }
        List<ResStoreMemberPriceRuleDTO> resStoreMemberPriceRuleDTOS = BeanCopierUtil.copyList(atomStoreMemberPriceRuleVos, ResStoreMemberPriceRuleDTO.class);
        return ExecuteDTO.success(new ExecutePageDTO<>(pages.getTotal(), resStoreMemberPriceRuleDTOS));
    }

    @Override
    public ExecuteDTO<ResStoreMemberPriceRuleDTO> storeMenberPricePromotionInfo(ReqStoreMemberPriceRuleDTO req) {
        AtomStoreMemberPriceRuleVo atomStoreMemberPriceRuleVo = new AtomStoreMemberPriceRuleVo();
        //店铺参与的所有会员价活动
        atomStoreMemberPriceRuleVo.setStoreNo(req.getStoreNo());
        atomStoreMemberPriceRuleVo.setPromotionType(PromotionTypeEnum.MEMBER_PRICE.getCode());
        atomStoreMemberPriceRuleVo.setSourceType(SourceTypeEnum.SOURCE_TYPE_1002.getCode());
        atomStoreMemberPriceRuleVo.setPromotionNo(req.getPromotionNo());
        //atomStoreMemberPriceRuleVo.setDeleteFlag(DeleteFlagEnum.NO.getCode()); sql里
        List<AtomStoreMemberPriceRuleVo> atomStoreMemberPriceRuleVos = promotionStoreRelationDao.storeMenberPricePromotion(atomStoreMemberPriceRuleVo);
        if (CollectionUtils.isEmpty(atomStoreMemberPriceRuleVos)) {
            return ExecuteDTO.error(CommonCode.CODE_10000002, "查询结果为空");
        }

        AtomStoreMemberPriceRuleVo priceRuleVo = atomStoreMemberPriceRuleVos.get(NumConstant.ZERO);
        //退出共享店铺,DisableFlag != 1
        if (!DeleteFlagEnum.NO.getCode().equals(priceRuleVo.getDisableFlag())) {
            //此时活动状态为已过期
            priceRuleVo.setStatus(PromotionStatusEnum.EXPIRED.getCode());
        }

        ResStoreMemberPriceRuleDTO copy = BeanCopierUtil.copy(priceRuleVo, ResStoreMemberPriceRuleDTO.class);

        return ExecuteDTO.success(copy);
    }

    /**
     * 千橙PC查询礼品卡活动详情
     *
     * @param queryDTO queryDTO
     * @return 活动详情
     * <AUTHOR>
     * @date 2023-04-11
     */
    @Override
    public ExecuteDTO<ResGiftCardDetailDTO> getGiftCardDetailByPromotionNo(ReqPromotionQueryDTO queryDTO) {
        log.info("getGiftCardDetailByPromotionNo-入参：{}", queryDTO);
        final String promotionNo = queryDTO.getPromotionNo();

        // 查询促销活动的活动规则以及促销规则信息
        AtomReqPromotionInfoDTO atomReqPromotionInfoDTO = new AtomReqPromotionInfoDTO();
        atomReqPromotionInfoDTO.setPromotionNo(promotionNo);
        ExecuteDTO<AtomResGoodsPromotionRuleDTO> ruleInfoExecuteDTO = atomGoodsPromotionRuleAnalysisService.getGoodsPromotionRuleByPromotionNo(atomReqPromotionInfoDTO);
        if (null == ruleInfoExecuteDTO) {
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        }
        if (!ruleInfoExecuteDTO.successFlag()) {
            return new ExecuteDTO<>(ruleInfoExecuteDTO.getStatus(), ruleInfoExecuteDTO.getMsg(), null);
        }
        if (null == ruleInfoExecuteDTO.getData()) {
            return ExecuteDTO.ok();
        }

        // 出参转换
        ResGiftCardDetailDTO resGiftCardDetailDTO = BeanCopierUtil.copy(ruleInfoExecuteDTO.getData(), ResGiftCardDetailDTO.class);

        // 根据活动编号查询该店铺活动参与的所有商品数据
        AtomReqGoodsPromotionGoodsRelationDTO reqPromotionGoods = new AtomReqGoodsPromotionGoodsRelationDTO();
        reqPromotionGoods.setPromotionNo(promotionNo);
        ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> executeDTO = atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList(reqPromotionGoods);
        if (null == executeDTO) {
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getInMsg(), null);
        }
        if (!executeDTO.successFlag()) {
            return new ExecuteDTO<>(executeDTO.getStatus(), executeDTO.getMsg(), null);
        }
        List<AtomResGoodsPromotionGoodsRelationDTO> promotionGoodsList = executeDTO.getData();
        if (CollectionUtils.isNotEmpty(promotionGoodsList)) {
            List<ResGiftCardGoodsDTO> goodsRelationDTOS = BeanCopierUtil.copyList(promotionGoodsList, ResGiftCardGoodsDTO.class);
            this.converPromotionGoodsInfo(goodsRelationDTOS);
            resGiftCardDetailDTO.setGoodsList(goodsRelationDTOS);
        }

        // 活动图片
        List<PromotionImageDTO> promotionImageDTOS = goodsPromotionImageService.getImagesByPromotionNo(promotionNo);
        resGiftCardDetailDTO.setPromotionImages(BeanCopierUtil.copyList(promotionImageDTOS, PromotionImageDTO.class));

        // 返回结果
        return ExecuteDTO.ok(resGiftCardDetailDTO);
    }

    /**
     * 社群接龙活动商品列表
     *
     * <AUTHOR>
     * @date 2022-07-22
     */
    private void converPromotionGoodsInfo(List<ResGiftCardGoodsDTO> goodsRelationDTOS) {
        List<String> goodsNoList = goodsRelationDTOS.stream().map(ResGiftCardGoodsDTO::getGoodsNo).collect(Collectors.toList());
        Map<String, ResGoodsDTO> goodsDTOMap = this.getGoodsDTOMap(goodsNoList);
        goodsRelationDTOS.forEach(goods -> {
            ResGoodsDTO resGoodsDTO = goodsDTOMap.get(goods.getGoodsNo());
            if (null != resGoodsDTO) {
                goods.setGoodsName(resGoodsDTO.getGoodsName());
                goods.setCanSaleStockNum(resGoodsDTO.getCanSaleStockNum());
                goods.setCalculationUnitNo(resGoodsDTO.getCalculationUnitNo());
                goods.setCalculationUnitName(resGoodsDTO.getCalculationUnitName());
                goods.setFirstAttributeName(resGoodsDTO.getFirstAttributeName());
                goods.setFirstAttributeValueName(resGoodsDTO.getFirstAttributeValueName());
                goods.setSecondAttributeName(resGoodsDTO.getSecondAttributeName());
                goods.setSecondAttributeValueName(resGoodsDTO.getSecondAttributeValueName());
                goods.setThirdAttributeName(resGoodsDTO.getThirdAttributeName());
                goods.setThirdAttributeValueName(resGoodsDTO.getThirdAttributeValueName());
                goods.setMainPictureUrl(resGoodsDTO.getMainPictureUrl());
                goods.setRetailPrice(resGoodsDTO.getRetailPrice());
                goods.setMarketPrice(resGoodsDTO.getMarketPrice());
                goods.setGoodsStatus(resGoodsDTO.getGoodsStatus());
                goods.setGoodsDeleteFlag(resGoodsDTO.getDeleteFlag());
                goods.setGoodsForm(resGoodsDTO.getGoodsForm());
                // 20230928蛋品 lixiang  商品管理 多单位商品
                goods.setMultiUnitType(resGoodsDTO.getMultiUnitType());
                goods.setMultiUnitGoodsNo(resGoodsDTO.getMultiUnitGoodsNo());
                if (MultiUnitTypeEnum.MAIN_UNIT.getCode().equals(goods.getMultiUnitType())) {
                    goods.setGoodsName(goods.getGoodsName() + "(" + goods.getCalculationUnitName() + ")");
                }

                // 规格属性
                StringBuilder attributeNames = new StringBuilder();
                if (StringUtils.isNotBlank(resGoodsDTO.getFirstAttributeValueName())) {
                    attributeNames.append(resGoodsDTO.getFirstAttributeValueName());
                }
                if (StringUtils.isNotBlank(resGoodsDTO.getSecondAttributeValueName())) {
                    if (StringUtils.isNotBlank(attributeNames.toString())) {
                        attributeNames.append("-");
                    }
                    attributeNames.append(resGoodsDTO.getSecondAttributeValueName());
                }
                if (StringUtils.isNotBlank(resGoodsDTO.getThirdAttributeValueName())) {
                    if (StringUtils.isNotBlank(attributeNames.toString())) {
                        attributeNames.append("-");
                    }
                    attributeNames.append(resGoodsDTO.getThirdAttributeValueName());
                }
                if (StringUtils.isNotBlank(attributeNames)) {
                    goods.setAttributeNames(attributeNames.toString());
                }
            }
        });
    }

    /**
     * @param promotionInfoDTO
     * @Description : pc端社群接龙活动列表查询
     * <AUTHOR> 吴鑫鑫
     * @date : 2023/6/7
     * 202503 东启
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResGoodsPromotionRuleDTO>> getPcGoodsPromotionRuleList(ReqPromotionInfoDTO promotionInfoDTO) {
        //条件判断
        this.promotionInfoAssert.dtoAssert(promotionInfoDTO);
        log.info("--GoodsPromotionRuleAnalysisServiceImpl.getPcGoodsPromotionRuleList-入参-promotionInfoDTO-{}", promotionInfoDTO);
        AtomReqPromotionInfoDTO infoDTO = BeanCopierUtil.copy(promotionInfoDTO, AtomReqPromotionInfoDTO.class);
        if (CollectionUtils.isNotEmpty(promotionInfoDTO.getPromotionTypes())) {
            infoDTO.setPromotionTypeList(promotionInfoDTO.getPromotionTypes());
        }
        ExecuteDTO<ExecutePageDTO<AtomResGoodsPromotionRuleDTO>> executeDTO = atomGoodsPromotionRuleAnalysisService.getPcGoodsPromotionRuleList(infoDTO);
        if (executeDTO.successFlag()) {
            //店铺查询判断活动上下架
            if (executeDTO.getData() != null && CollectionUtils.isNotEmpty(executeDTO.getData().getRows())) {
                executeDTO.getData().getRows().forEach(promotion -> {
                    if (StringUtils.isNotEmpty(promotionInfoDTO.getStoreNo())) {
                        //商家上下架接龙活动状态记录
                        promotion.setMerchantUpDownFlag(promotion.getUpDownFlag());
                        if (PromotionTypeEnum.COMMUNITY_SOLITAIRE_SJ.getCode().equals(promotion.getPromotionType())) {
                            if (StoreUpDownEnum.UP.getCode().equals(promotion.getUpDownFlag())
                                    && StoreUpDownEnum.UP.getCode().equals(promotion.getStoreRelationUpDownFlag())
                                    && StoreUseEnum.ENABLE.getCode().equals(promotion.getStoreRelationDisableFlag())) {
                                promotion.setUpDownFlag(StoreUpDownEnum.UP.getCode());
                            } else {
                                promotion.setUpDownFlag(StoreUpDownEnum.DOWN.getCode());
                            }
                        }
                    }
                });
            }
            return ExecuteDTO.success(new ExecutePageDTO<>(executeDTO.getData().getTotal(), BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResGoodsPromotionRuleDTO.class)));
        }
        return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
    }
}