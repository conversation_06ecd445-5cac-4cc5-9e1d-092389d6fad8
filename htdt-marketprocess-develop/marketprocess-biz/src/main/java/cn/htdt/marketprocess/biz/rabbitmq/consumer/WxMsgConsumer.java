package cn.htdt.marketprocess.biz.rabbitmq.consumer;

import cn.htdt.marketprocess.api.operat.WxPushMessageOperateService;
import cn.htdt.marketprocess.biz.rabbitmq.config.DirectDeplayConfig;
import cn.htdt.marketprocess.dto.request.ReqWxMsgUnpaidDTO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 对象消费者-微信消息推送
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WxMsgConsumer {

    @Resource
    private WxPushMessageOperateService wxPushMessageOperateService;

    /**
     * 消费死信队列内容，微信消息推送（未支付提醒）
     *
     * @param reqWxMsgUnpaidDTO 消息内容
     */
    @RabbitHandler
    @RabbitListener(queues = DirectDeplayConfig.DEAD_LETTER_QUEUE_WX_MSG_UNPAID_NAME)
    public void mqUnpaidMsgByDeadLetter(ReqWxMsgUnpaidDTO reqWxMsgUnpaidDTO) {
        log.info("---消费MQ---mqUnpaidMsgByDeadLetter---queueName：{}，入参:：{}", DirectDeplayConfig.DEAD_LETTER_QUEUE_WX_MSG_UNPAID_NAME, JSON.toJSONString(reqWxMsgUnpaidDTO));
        wxPushMessageOperateService.unpaidMsgPush(reqWxMsgUnpaidDTO);
    }
}
