package cn.htdt.marketprocess.biz.operat;

import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.enums.MarketErrorCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.goods.SeriesTypeEnum;
import cn.htdt.common.enums.goods.SourceTypeEnum;
import cn.htdt.common.enums.market.PromotionStatusEnum;
import cn.htdt.common.enums.market.PromotionTypeEnum;
import cn.htdt.common.enums.market.StoreTypeEnum;
import cn.htdt.common.enums.market.StoreUpDownEnum;
import cn.htdt.common.enums.user.DeleteFlagEnum;
import cn.htdt.common.generator.MarketFormGenerator;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.CollectionUtils;
import cn.htdt.marketcenter.dto.request.AtomReqLotteryRuleDTO;
import cn.htdt.marketcenter.dto.request.AtomReqPromotionStoreRelationDTO;
import cn.htdt.marketcenter.dto.request.AtomReqPromotionStoreRuleDTO;
import cn.htdt.marketcenter.dto.response.AtomResLotteryRuleDTO;
import cn.htdt.marketcenter.dto.response.AtomResPromotionStoreRuleDTO;
import cn.htdt.marketprocess.api.operat.PromotionStoreRelationOperatService;
import cn.htdt.marketprocess.biz.conversion.PromotionStoreRelationAssert;
import cn.htdt.marketprocess.dao.PromotionInfoDao;
import cn.htdt.marketprocess.dao.PromotionStoreRelationDao;
import cn.htdt.marketprocess.domain.PromotionInfoDomain;
import cn.htdt.marketprocess.domain.PromotionStoreRelationDomain;
import cn.htdt.marketprocess.dto.request.ReqPromotionStoreRelationDTO;
import cn.htdt.marketprocess.dto.request.ReqSharedMembershipStoreDTO;
import cn.htdt.marketprocess.dto.request.ReqSharedStoreDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomLotteryRuleAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomPromotionStoreRuleAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomPromotionStoreRelationOperatService;
import cn.htdt.marketprocess.vo.PromotionInfoVO;
import cn.htdt.marketprocess.vo.PromotionStoreRelationVo;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 活动店铺关联上下架表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-18
 */
@Slf4j
@DubboService
public class PromotionStoreRelationOperatServiceImpl implements PromotionStoreRelationOperatService {

    /**
     * 活动店铺规则表
     */
    @Resource
    private AtomPromotionStoreRelationOperatService atomPromotionStoreRelationOperatService;

    @Resource
    private AtomPromotionStoreRuleAnalysisService atomPromotionStoreRuleAnalysisService;

    @Resource
    private AtomLotteryRuleAnalysisService atomLotteryRuleAnalysisService;

    @Resource
    private PromotionInfoDao promotionInfoDao;

    @Resource
    private PromotionStoreRelationDao promotionStoreRelationDao;

    /**
     * 校验
     */
    @Autowired
    private PromotionStoreRelationAssert promotionStoreRelationAssert;

    @Override
    public ExecuteDTO modifyStoreShelves(ReqPromotionStoreRelationDTO reqPromotionStoreRelationDTO) {
        log.info("-PromotionStoreRelationOperatServiceImpl-modifyStoreShelves-param={}", JSON.toJSONString(reqPromotionStoreRelationDTO));
        this.promotionStoreRelationAssert.modifyStoreShelvesAssert(reqPromotionStoreRelationDTO);
        // 查询活动编码查询平台抽奖活动
        AtomReqLotteryRuleDTO atomReqLotteryRuleDTO = new AtomReqLotteryRuleDTO();
        atomReqLotteryRuleDTO.setPromotionNo(reqPromotionStoreRelationDTO.getPromotionNo());
        ExecuteDTO<AtomResLotteryRuleDTO> lotteryExecuteDTO = atomLotteryRuleAnalysisService.viewLotteryInfo(atomReqLotteryRuleDTO);
        if (null == lotteryExecuteDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!lotteryExecuteDTO.successFlag()) {
            return ExecuteDTO.error(lotteryExecuteDTO.getStatus(), lotteryExecuteDTO.getMsg());
        }
        // 平台抽奖活动不存在了，直接退出
        if (lotteryExecuteDTO.getData() == null) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000100);
        }
        // 平台抽奖活动已经下架了，店铺则不允许操作上下架，直接退出
        if (WhetherEnum.NO.getCode().equals(lotteryExecuteDTO.getData().getUpDownFlag())) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000603);
        }
        // 店铺上下架平台活动之前，校验店铺是否在设置的参与店铺范围之内
        AtomReqPromotionStoreRuleDTO reqDto = new AtomReqPromotionStoreRuleDTO();
        reqDto.setPromotionNo(reqPromotionStoreRelationDTO.getPromotionNo());
        reqDto.setStoreNo(reqPromotionStoreRelationDTO.getStoreNo());
        ExecuteDTO<AtomResPromotionStoreRuleDTO> storeRuleAndDetailExecuteDTO =
                atomPromotionStoreRuleAnalysisService.selectPromotionStoreRuleAndDetail(reqDto);
        if (null == storeRuleAndDetailExecuteDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!storeRuleAndDetailExecuteDTO.successFlag()) {
            return ExecuteDTO.error(storeRuleAndDetailExecuteDTO.getStatus(), storeRuleAndDetailExecuteDTO.getMsg());
        }
        AtomResPromotionStoreRuleDTO storeRuleAndDetail = storeRuleAndDetailExecuteDTO.getData();
        // 店铺不在设置的参与店铺内，不允许做上下架操作，直接退出
        if (null == storeRuleAndDetail || (StoreTypeEnum.STORE_TYPE_TWO.getCode().equals(storeRuleAndDetail.getStoreType()) && StringUtils.isBlank(storeRuleAndDetail.getEnrollNo()))) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000302);
        }
        PromotionStoreRelationVo storeRelationVo = new PromotionStoreRelationVo();
        storeRelationVo.setPromotionNo(reqPromotionStoreRelationDTO.getPromotionNo());
        storeRelationVo.setMerchantNo(reqPromotionStoreRelationDTO.getMerchantNo());
        storeRelationVo.setStoreNo(reqPromotionStoreRelationDTO.getStoreNo());
        List<PromotionStoreRelationVo> storeRelationVoList = this.promotionStoreRelationDao.selectPromotionStoreRelationList(storeRelationVo);
        if (CollectionUtils.isNotEmpty(storeRelationVoList) && WhetherEnum.YES.getCode().equals(storeRelationVoList.get(NumConstant.ZERO).getDisableFlag())) {
            return ExecuteDTO.error(CommonCode.CODE_10000003.getCode(), "失效店铺不能上下架");
        }
        AtomReqPromotionStoreRelationDTO atomReqPromotionStoreRelationDTO = BeanCopierUtil.copy(reqPromotionStoreRelationDTO, AtomReqPromotionStoreRelationDTO.class);
        atomPromotionStoreRelationOperatService.modifyStoreShelves(atomReqPromotionStoreRelationDTO);
        log.info("-PromotionStoreRelationOperatServiceImpl-modifyStoreShelves-end");
        return ExecuteDTO.success();
    }

    @Override
    public ExecuteDTO modifyPromotionStoreRelation(ReqPromotionStoreRelationDTO reqPromotionStoreRelationDTO) {
        log.info("-PromotionStoreRelationOperatServiceImpl-modifyPromotionStoreRelation-param={}", JSON.toJSONString(reqPromotionStoreRelationDTO));
        this.promotionStoreRelationAssert.modifyStoreShelvesAssert(reqPromotionStoreRelationDTO);
        PromotionInfoDomain promotionInfoDomain = new PromotionInfoDomain();
        promotionInfoDomain.setPromotionNo(reqPromotionStoreRelationDTO.getPromotionNo());
        promotionInfoDomain.setDeleteFlag(DeleteFlagEnum.NO.getCode());
        PromotionInfoDomain infoDomain = promotionInfoDao.selectPromotionInfo(promotionInfoDomain);
        //主活动未上架,店铺对应状态只能是下架
        if (WhetherEnum.YES.getCode().equals(reqPromotionStoreRelationDTO.getUpDownFlag())){
            if (WhetherEnum.NO.getCode().equals(infoDomain.getUpDownFlag())){
                return ExecuteDTO.error(MarketErrorCode.CODE_17001013.getCode(),"主活动未上架");
            }
            //最新上架时间
            reqPromotionStoreRelationDTO.setLatestUpDownTime(LocalDateTime.now());
        }

        //查询是新增还是编辑
        PromotionStoreRelationVo promotionNo = new PromotionStoreRelationVo();
        promotionNo.setDeleteFlag(DeleteFlagEnum.NO.getCode());
        promotionNo.setPromotionNo(reqPromotionStoreRelationDTO.getPromotionNo());
        promotionNo.setStoreNo(reqPromotionStoreRelationDTO.getStoreNo());
        PromotionStoreRelationVo promotionStoreRelationVo = promotionStoreRelationDao.selectPromotionStoreRelation(promotionNo);

        if (null == promotionStoreRelationVo){
            PromotionStoreRelationDomain promotionStoreRelationDomain = BeanCopierUtil.copy(reqPromotionStoreRelationDTO, PromotionStoreRelationDomain.class);
            promotionStoreRelationDomain.setRuleNo(MarketFormGenerator.getStoreRuleNo());
            promotionStoreRelationDomain.setPromotionNo(reqPromotionStoreRelationDTO.getPromotionNo());
            promotionStoreRelationDomain.setStoreSourceType(SourceTypeEnum.SOURCE_TYPE_1002.getCode());
            promotionStoreRelationDomain.setUpDownFlag(WhetherEnum.NO.getCode());
            this.promotionStoreRelationDao.insert(promotionStoreRelationDomain);
        } else {
            //主活动为上架状态,店铺可操作上下架
            AtomReqPromotionStoreRelationDTO atomReqPromotionStoreRelationDTO = BeanCopierUtil.copy(reqPromotionStoreRelationDTO, AtomReqPromotionStoreRelationDTO.class);
            atomPromotionStoreRelationOperatService.modifyStoreShelves(atomReqPromotionStoreRelationDTO);
        }
        return ExecuteDTO.success();
    }

    @Override
    public ExecuteDTO operatePromotionStoreRelation(ReqSharedMembershipStoreDTO reqSharedMembershipStoreDTO) {
        log.info("-PromotionStoreRelationOperatServiceImpl-modifyPromotionStoreRelation-param={}", JSON.toJSONString(reqSharedMembershipStoreDTO));

        //共享会员信息
        if (CollectionUtils.isEmpty(reqSharedMembershipStoreDTO.getSharedStoreDTOS())){
            log.info("-PromotionStoreRelationOperatServiceImpl-getSharedStoreDTOS-isEmpty,{}",reqSharedMembershipStoreDTO.getMerchantNo());
            return ExecuteDTO.success();
        }

        PromotionInfoVO promotionInfoVO = new PromotionInfoVO();
        promotionInfoVO.setMerchantNo(reqSharedMembershipStoreDTO.getMerchantNo());
        promotionInfoVO.setPromotionType(PromotionTypeEnum.MEMBER_PRICE.getCode());
        promotionInfoVO.setSourceType(SourceTypeEnum.SOURCE_TYPE_1002.getCode());
        promotionInfoVO.setDeleteFlag(DeleteFlagEnum.NO.getCode());
        List<PromotionInfoVO> promotionInfoVOS = promotionInfoDao.selectPromotionInfoList(promotionInfoVO);
        if (CollectionUtils.isEmpty(promotionInfoVOS)){
            log.info("-PromotionStoreRelationOperatServiceImpl-operatePromotionStoreRelation-isEmpty,{}",reqSharedMembershipStoreDTO.getMerchantNo());
            return ExecuteDTO.success();
        }

        //商家创建的所有非草稿会员价活动
        List<String> promotionNos = promotionInfoVOS.stream().filter(promotions -> !PromotionStatusEnum.ONLY_SAVED.getCode().equals(promotions.getStatus())).map(PromotionInfoVO::getPromotionNo).collect(Collectors.toList());

        //通过对应的商家会员价活动,查询店铺下的所有会员价活动
        PromotionStoreRelationVo promotionStoreRelationVo = new PromotionStoreRelationVo();
        promotionStoreRelationVo.setPromotionNoList(promotionNos);
        promotionStoreRelationVo.setStoreSourceType(SourceTypeEnum.SOURCE_TYPE_1002.getCode());
        List<PromotionStoreRelationVo> promotionStoreRelationVos = promotionStoreRelationDao.selectPromotionStoreRelationList(promotionStoreRelationVo);

        //会员店存在的数据更新,不存在的数据新增
        ReqPromotionStoreRelationDTO req;
        List<ReqPromotionStoreRelationDTO> insertList = new ArrayList<>();
        List<ReqPromotionStoreRelationDTO> updateList = new ArrayList<>();
        boolean isinsert;

        //需要分发的商家会员价活动
        for (PromotionInfoVO infoVO : promotionInfoVOS) {
            //过滤草稿 过滤没有首次上架过的
            if (PromotionStatusEnum.ONLY_SAVED.getCode().equals(infoVO.getStatus()) || null == infoVO.getFirstUpTime()){
                continue;
            }

            //商家操作的会员店
            for (ReqSharedStoreDTO sharedStoreDTO : reqSharedMembershipStoreDTO.getSharedStoreDTOS()) {

                if (null == sharedStoreDTO.getIsShare()){
                    continue;
                }

                isinsert = true;
                req = new ReqPromotionStoreRelationDTO();
                //新增更新通用参数
                req.setPromotionNo(infoVO.getPromotionNo());
                req.setStoreNo(sharedStoreDTO.getStoreNo());
                //活动编号+店铺编号 为唯一数据
                if (CollectionUtils.isNotEmpty(promotionStoreRelationVos)){
                    for (PromotionStoreRelationVo vo : promotionStoreRelationVos) {
                        //共享店铺
                        if (StringUtils.equals(vo.getPromotionNo(),infoVO.getPromotionNo()) && StringUtils.equals(vo.getStoreNo(),sharedStoreDTO.getStoreNo())){
                            isinsert = false;
                            break;
                        }
                    }
                }

                //店铺关联表不存在
                if (isinsert){
                    //店铺表不存在老数据 不是共享店铺不新增
                    if (WhetherEnum.NO.getCode().equals(sharedStoreDTO.getIsShare())){
                        continue;
                    } else {
                        req.setRuleNo(MarketFormGenerator.getStoreRuleNo());
                        req.setStoreSourceType(SourceTypeEnum.SOURCE_TYPE_1002.getCode());
                        req.setMerchantNo(infoVO.getMerchantNo());
                        req.setStoreNo(sharedStoreDTO.getStoreNo());
                        req.setUpDownFlag(infoVO.getUpDownFlag());
                        //如果是上架,需要增加最新上架时间
                        if (WhetherEnum.YES.getCode().equals(infoVO.getUpDownFlag())){
                            req.setLatestUpDownTime(LocalDateTime.now());
                        }
                        insertList.add(req);
                    }
                }
                //店铺关联表存在数据
                else {
                    //店铺存在老数据 不是共享店铺更新dis字段
                    if (WhetherEnum.NO.getCode().equals(sharedStoreDTO.getIsShare())){
                        //不可用
                        req.setDisableFlag(DeleteFlagEnum.YES.getCode());
                        req.setUpDownFlag(StoreUpDownEnum.DOWN.getCode());
                    } else {
                        //是共享店铺更新需要的字段
                        req.setDisableFlag(DeleteFlagEnum.NO.getCode());

                    }
                    updateList.add(req);
                }
            }
        }


        //批量新增 批量更新
        return atomPromotionStoreRelationOperatService.batchOperate(insertList,updateList);
    }

}
