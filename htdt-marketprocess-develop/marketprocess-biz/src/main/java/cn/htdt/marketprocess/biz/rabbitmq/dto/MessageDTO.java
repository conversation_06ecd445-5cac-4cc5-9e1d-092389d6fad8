package cn.htdt.marketprocess.biz.rabbitmq.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 消息DTO
 * <AUTHOR>
 */
@Data
public class MessageDTO implements Serializable{
    /**
     * 消息唯一值
     */
    private String messageId;
    /**
     * 消息数据
     */
    private String messageData;
    /**
     * 消息创建时间
     */
    private String createTime;
    /**
     * 消息ttl
     */
    private Long messageTTL;
}
