package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqExpandHelpRecordDTO;
import cn.htdt.marketcenter.dto.request.AtomReqExpandRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomResExpandHelpRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomResExpandRecordDTO;
import cn.htdt.marketprocess.api.analysis.ExpandHelpRecordAnalysisService;
import cn.htdt.marketprocess.dto.request.ReqExpandHelpRecordDTO;
import cn.htdt.marketprocess.dto.response.ResExpandHelpCountDTO;
import cn.htdt.marketprocess.dto.response.ResExpandHelpRecordDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomExpandHelpRecordAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomExpandRecordAnalysisService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 膨胀红包助力记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Slf4j
@DubboService
public class ExpandHelpRecordAnalysisServiceImpl implements ExpandHelpRecordAnalysisService {

    /**
     * 膨胀红包发起助力记录
     */
    @Resource
    private AtomExpandHelpRecordAnalysisService atomExpandHelpRecordAnalysisService;

    @Resource
    private AtomExpandRecordAnalysisService atomExpandRecordAnalysisService;

    @Override
    public ExecuteDTO<ExecutePageDTO<ResExpandHelpRecordDTO>> getExpandHelpRecordPage(ReqExpandHelpRecordDTO reqExpandHelpRecordDTO) {
        log.info("-ExpandHelpRecordAnalysisServiceImpl-getExpandHelpRecordPage-param={}", JSON.toJSONString(reqExpandHelpRecordDTO));
        ExecutePageDTO<ResExpandHelpRecordDTO> executePageDTO = new ExecutePageDTO<>();
        AtomReqExpandHelpRecordDTO atomReqExpandHelpRecordDTO = BeanCopierUtil.copy(reqExpandHelpRecordDTO, AtomReqExpandHelpRecordDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResExpandHelpRecordDTO>> executeDTO = atomExpandHelpRecordAnalysisService.getExpandHelpRecordPage(atomReqExpandHelpRecordDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        List<ResExpandHelpRecordDTO> resList = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResExpandHelpRecordDTO.class);
        log.info("-ExpandHelpRecordAnalysisServiceImpl-getExpandHelpRecordPage-return={}", JSON.toJSONString(resList));
        executePageDTO.setRows(resList);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    @Override
    public ExecuteDTO<List<ResExpandHelpRecordDTO>> getExpandHelpRecordList(ReqExpandHelpRecordDTO reqExpandHelpRecordDTO) {
        log.info("-ExpandHelpRecordAnalysisServiceImpl-getExpandHelpRecordList-param={}", JSON.toJSONString(reqExpandHelpRecordDTO));
        AtomReqExpandHelpRecordDTO atomReqExpandHelpRecordDTO = BeanCopierUtil.copy(reqExpandHelpRecordDTO, AtomReqExpandHelpRecordDTO.class);
        ExecuteDTO<List<AtomResExpandHelpRecordDTO>> executeDTO = atomExpandHelpRecordAnalysisService.getExpandHelpRecordList(atomReqExpandHelpRecordDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        List<ResExpandHelpRecordDTO> resList = BeanCopierUtil.copyList(executeDTO.getData(), ResExpandHelpRecordDTO.class);
        log.info("-ExpandHelpRecordAnalysisServiceImpl-getExpandHelpRecordList-return={}", JSON.toJSONString(resList));
        return ExecuteDTO.success(resList);
    }

    @Override
    public ExecuteDTO<ResExpandHelpCountDTO> getHxgExpandHelpCount(ReqExpandHelpRecordDTO reqExpandHelpRecordDTO) {
        log.info("-ExpandHelpRecordAnalysisServiceImpl-getHxgExpandHelpCount-param={}", JSON.toJSONString(reqExpandHelpRecordDTO));
        ResExpandHelpCountDTO helpCountDTO = new ResExpandHelpCountDTO();
        // 已发起总数
        AtomReqExpandRecordDTO expandRecordDTO = new AtomReqExpandRecordDTO();
        expandRecordDTO.setPromotionNo(reqExpandHelpRecordDTO.getPromotionNo());
        ExecuteDTO<Integer> expandRecordTotalCount = atomExpandRecordAnalysisService.getExpandRecordTotalCount(expandRecordDTO);
        if (!expandRecordTotalCount.successFlag()) {
            return ExecuteDTO.error(expandRecordTotalCount.getStatus(), expandRecordTotalCount.getMsg());
        }
        if (expandRecordTotalCount.getData() != null) {
            // 已助力数量
            helpCountDTO.setExpandTotal(expandRecordTotalCount.getData());
        }
        // 已助力数量
        AtomReqExpandHelpRecordDTO helpRecordDTO = new AtomReqExpandHelpRecordDTO();
        helpRecordDTO.setPromotionNo(reqExpandHelpRecordDTO.getPromotionNo());
        ExecuteDTO<Integer> recordCountDto = atomExpandHelpRecordAnalysisService.getExpandHelpRecordCount(helpRecordDTO);
        if (!recordCountDto.successFlag()) {
            return ExecuteDTO.error(recordCountDto.getStatus(), recordCountDto.getMsg());
        }
        if (recordCountDto.getData() != null) {
            // 已助力数量
            helpCountDTO.setHelpTotal(recordCountDto.getData());
        }
        // 领取量 使用量
        AtomReqExpandRecordDTO atomReqExpandRecordDTO = new AtomReqExpandRecordDTO();
        atomReqExpandRecordDTO.setPromotionNo(reqExpandHelpRecordDTO.getPromotionNo());
        ExecuteDTO<AtomResExpandRecordDTO> expandRecordUseCount = this.atomExpandRecordAnalysisService.getExpandRecordUseCount(atomReqExpandRecordDTO);
        if (!expandRecordUseCount.successFlag()) {
            return ExecuteDTO.error(expandRecordUseCount.getStatus(), expandRecordUseCount.getMsg());
        }
        if (expandRecordUseCount.getData() != null) {
            helpCountDTO.setUseCount(expandRecordUseCount.getData().getUseCount());
            helpCountDTO.setExchangeCount(expandRecordUseCount.getData().getExchangeCount());
        }

        return ExecuteDTO.success(helpCountDTO);
    }

    @Override
    public ExecuteDTO<BigDecimal> getExpandHelpHelpMoney(ReqExpandHelpRecordDTO reqExpandHelpRecordDTO) {
        log.info("-ExpandHelpRecordAnalysisServiceImpl-getExpandHelpHelpMoney-param={}", JSON.toJSONString(reqExpandHelpRecordDTO));
        AtomReqExpandHelpRecordDTO atomReqExpandHelpRecordDTO = BeanCopierUtil.copy(reqExpandHelpRecordDTO, AtomReqExpandHelpRecordDTO.class);
        ExecuteDTO<BigDecimal> executeDTO = atomExpandHelpRecordAnalysisService.getExpandHelpHelpMoney(atomReqExpandHelpRecordDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        log.info("-ExpandHelpRecordAnalysisServiceImpl-getExpandHelpHelpMoney-return={}", JSON.toJSONString(executeDTO));
        return ExecuteDTO.success(executeDTO.getData());
    }

}
