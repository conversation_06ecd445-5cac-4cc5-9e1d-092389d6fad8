package cn.htdt.marketprocess.biz.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.market.PointsOperateTypeEnum;
import cn.htdt.common.generator.IdGenerator;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqUserPointsDTO;
import cn.htdt.marketcenter.dto.request.AtomReqUserPointsRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomResUserPointsDTO;
import cn.htdt.marketprocess.api.operat.UserPointsRecordOperateService;
import cn.htdt.marketprocess.dto.request.ReqUserPointsRecordDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomUserPointsAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomUserPointsRecordOperateService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

@DubboService
@Slf4j
public class UserPointsRecordOperateServiceImpl implements UserPointsRecordOperateService {
    @Resource
    AtomUserPointsAnalysisService atomUserPointsAnalysisService;

    @Resource
    AtomUserPointsRecordOperateService atomUserPointsRecordOperateService;

    /**
     * @see UserPointsRecordOperateService#addUserPointsRecord(ReqUserPointsRecordDTO)
     */
    @Override
    public ExecuteDTO<String> addUserPointsRecord(ReqUserPointsRecordDTO recordDTO) {
        log.info("UserPointsRecordOperateServiceImpl.addUserPointsRecord----param----{}", JSON.toJSONString(recordDTO));
        AtomReqUserPointsRecordDTO atomReqUserPointsRecordDTO = BeanCopierUtil.copy(recordDTO, AtomReqUserPointsRecordDTO.class);
        String recordNo = IdGenerator.getDid();
        atomReqUserPointsRecordDTO.setRecordNo(recordNo);
        AtomReqUserPointsDTO atomReqUserPointsDTO = BeanCopierUtil.copy(recordDTO, AtomReqUserPointsDTO.class);
        //查询用户最新的账户积分余额
        //20230928蛋品-wh-商家-判断类型查询是商家积分还是门店积分
        ExecuteDTO<AtomResUserPointsDTO> executeDTO = null;
        if(StringUtils.isEmpty(recordDTO.getType()) || String.valueOf(NumConstant.ONE).equals(recordDTO.getType())){
            //查询门店的
            atomReqUserPointsDTO.setRuleType(String.valueOf(NumConstant.ONE));
            executeDTO = atomUserPointsAnalysisService.getSimpleUserPoints(atomReqUserPointsDTO);
            atomReqUserPointsRecordDTO.setRuleType(String.valueOf(NumConstant.ONE));
        }else {
            //查询商家
            atomReqUserPointsDTO.setRuleType(String.valueOf(NumConstant.TWO));
            executeDTO = atomUserPointsAnalysisService.getMerchantNoSimpleUserPoints(atomReqUserPointsDTO);
            atomReqUserPointsRecordDTO.setRuleType(String.valueOf(NumConstant.TWO));
        }

        if (executeDTO.successFlag() && executeDTO.getData()!=null) {

            Integer accountRemainPoints = executeDTO.getData().getAccountRemainPoints();
            atomReqUserPointsRecordDTO.setAfterChangeNum(accountRemainPoints);
            atomReqUserPointsRecordDTO.setBeforeChangeNum(accountRemainPoints - atomReqUserPointsRecordDTO.getChangeNum());
        }
        if (atomReqUserPointsRecordDTO.getChangeNum() >= 0) {
            atomReqUserPointsRecordDTO.setOperateType(PointsOperateTypeEnum.POINTS_GAIN.getCode());
        } else {
            atomReqUserPointsRecordDTO.setOperateType(PointsOperateTypeEnum.POINTS_DEDUCT.getCode());
        }
        atomUserPointsRecordOperateService.addUserPointsRecord(atomReqUserPointsRecordDTO);
        return ExecuteDTO.success(recordNo);
    }
}
