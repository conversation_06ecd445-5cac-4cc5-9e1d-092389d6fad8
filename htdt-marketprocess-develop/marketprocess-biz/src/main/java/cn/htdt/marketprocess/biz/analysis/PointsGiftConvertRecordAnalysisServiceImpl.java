package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.DateUtil;
import cn.htdt.marketcenter.dto.request.AtomReqPointsGiftConvertRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomResPointsGiftConvertRecordDTO;
import cn.htdt.marketprocess.api.analysis.PointsGiftConvertRecordAnalysisService;
import cn.htdt.marketprocess.biz.conversion.PointsGiftAssert;
import cn.htdt.marketprocess.biz.utils.PointsUtil;
import cn.htdt.marketprocess.dto.request.ReqPointsGiftConvertRecordDTO;
import cn.htdt.marketprocess.dto.response.ResPointsGiftConvertRecordDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomPointsGiftConvertRecoredAnalysisService;
import cn.htdt.userprocess.dto.response.StoreInfoResponse;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 * @see PointsGiftConvertRecordAnalysisService
 */
@DubboService
@Slf4j
public class PointsGiftConvertRecordAnalysisServiceImpl implements PointsGiftConvertRecordAnalysisService {
    @Resource
    PointsGiftAssert pointsGiftAssert;

    @Resource
    AtomPointsGiftConvertRecoredAnalysisService recoredAnalysisService;

    @Resource
    PointsUtil pointsUtil;

    /**
     * @see PointsGiftConvertRecordAnalysisService#getConvertRecords(ReqPointsGiftConvertRecordDTO)
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResPointsGiftConvertRecordDTO>> getConvertRecords(ReqPointsGiftConvertRecordDTO recordDTO) {
        log.info("PointsGiftConvertRecordAnalysisServiceImpl.getConvertRecords----param----{}", JSON.toJSONString(recordDTO));
        AtomReqPointsGiftConvertRecordDTO reqPointsGiftDTO = BeanCopierUtil.copy(recordDTO, AtomReqPointsGiftConvertRecordDTO.class);
        if(NumConstant.TWO == recordDTO.getLoginIdentity()){
            //身份是商家的，直接查询商家的信息
            reqPointsGiftDTO.setPointsGiftType(String.valueOf(NumConstant.TWO));
        }else {
            pointsGiftAssert.getConvertRecords(recordDTO);
            //20230928蛋品-wh-查询店铺是否是共享店铺
            StoreInfoResponse storeInfoResponse = pointsUtil.queryStore(recordDTO.getStoreNo());
            if (storeInfoResponse.getMemberSharing() == NumConstant.ONE) {
                reqPointsGiftDTO.setPointsGiftType(String.valueOf(NumConstant.ONE));
            } else {
                reqPointsGiftDTO.setPointsGiftType(String.valueOf(NumConstant.TWO));
            }
        }
        ExecuteDTO<ExecutePageDTO<AtomResPointsGiftConvertRecordDTO>> executeDTO = recoredAnalysisService.getConvertRecords(reqPointsGiftDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }

        //参数转换
        ExecutePageDTO<ResPointsGiftConvertRecordDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResPointsGiftConvertRecordDTO> recordDTOS = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResPointsGiftConvertRecordDTO.class);
        recordDTOS.forEach(s->s.setCreateTimeStr(DateUtil.format(s.getCreateTime(),DateUtil.YYDDMMHHMMSS)));
        executePageDTO.setRows(recordDTOS);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        log.info("PointsGiftConvertRecordAnalysisServiceImpl.getConvertRecords----end----");
        return ExecuteDTO.success(executePageDTO);
    }
    /**
     * @see PointsGiftConvertRecordAnalysisService#getConvertRecordsWithPictureUrl(ReqPointsGiftConvertRecordDTO) (ReqPointsGiftConvertRecordDTO)
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResPointsGiftConvertRecordDTO>> getConvertRecordsWithPictureUrl(ReqPointsGiftConvertRecordDTO recordDTO) {
        log.info("PointsGiftConvertRecordAnalysisServiceImpl.getConvertRecordsWithPictureUrl----param----{}", JSON.toJSONString(recordDTO));
        pointsGiftAssert.getConvertRecords(recordDTO);
        AtomReqPointsGiftConvertRecordDTO reqPointsGiftDTO = BeanCopierUtil.copy(recordDTO, AtomReqPointsGiftConvertRecordDTO.class);
        //20230928蛋品-wh-查询兑换记录信息
        StoreInfoResponse storeInfoResponse = pointsUtil.queryStore(recordDTO.getStoreNo());
        if(storeInfoResponse.getMemberSharing() == NumConstant.ONE){
            reqPointsGiftDTO.setPointsGiftType(String.valueOf(NumConstant.ONE));
        }else {
            reqPointsGiftDTO.setPointsGiftType(String.valueOf(NumConstant.TWO));
        }

        ExecuteDTO<ExecutePageDTO<AtomResPointsGiftConvertRecordDTO>> executeDTO = recoredAnalysisService.getConvertRecordsWithPictureUrl(reqPointsGiftDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        //参数转换
        ExecutePageDTO<ResPointsGiftConvertRecordDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResPointsGiftConvertRecordDTO> recordDTOS = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResPointsGiftConvertRecordDTO.class);
        recordDTOS.forEach(s->s.setCreateTimeStr(DateUtil.format(s.getCreateTime(),DateUtil.YYDDMMHHMMSS)));
        executePageDTO.setRows(recordDTOS);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        log.info("PointsGiftConvertRecordAnalysisServiceImpl.getConvertRecordsWithPictureUrl----end----");
        return ExecuteDTO.success(executePageDTO);
    }
}
