package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.base.exception.BaseException;
import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.enums.MarketErrorCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.PlatformTypeEnum;
import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.constants.PromotionKeyConstant;
import cn.htdt.common.enums.goods.*;
import cn.htdt.common.enums.market.*;
import cn.htdt.common.enums.user.IdentityEnum;
import cn.htdt.common.redis.utils.RedisBaseUtil;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.BigDecimalUtil;
import cn.htdt.common.utils.DateUtil;
import cn.htdt.common.utils.ListUtil;
import cn.htdt.goodscenter.dto.request.AtomReqGoodsDTO;
import cn.htdt.goodscenter.dto.response.AtomResGoodsDTO;
import cn.htdt.goodsprocess.api.analysis.CloudPoolApplyAnalysisService;
import cn.htdt.goodsprocess.api.analysis.GoodsAnalysisService;
import cn.htdt.goodsprocess.api.analysis.GoodsTagAnalysisService;
import cn.htdt.goodsprocess.api.operat.LegacyGoodsCenterService;
import cn.htdt.goodsprocess.dto.request.cloudpool.ReqCloudPoolApplyComPageDTO;
import cn.htdt.goodsprocess.dto.request.goods.ReqGoodsDTO;
import cn.htdt.goodsprocess.dto.request.goodstag.ReqGoodsTagDTO;
import cn.htdt.goodsprocess.dto.response.goods.ResGoodsDTO;
import cn.htdt.goodsprocess.dto.response.goodstag.ResGoodsTagDTO;
import cn.htdt.marketcenter.dto.request.*;
import cn.htdt.marketcenter.dto.response.*;
import cn.htdt.marketprocess.api.analysis.GoodsPromotionGoodsRelationAnalysisService;
import cn.htdt.marketprocess.api.analysis.GoodsPromotionRuleAnalysisService;
import cn.htdt.marketprocess.api.analysis.PreDeterminedCheckService;
import cn.htdt.marketprocess.biz.conversion.GoodsPromotionAssert;
import cn.htdt.marketprocess.biz.utils.CurrencyUtil;
import cn.htdt.marketprocess.dao.GoodsPromotionGoodsRelationDao;
import cn.htdt.marketprocess.dao.GoodsPromotionRuleDao;
import cn.htdt.marketprocess.domain.GoodsPromotionGoodsRelationDomain;
import cn.htdt.marketprocess.dto.request.*;
import cn.htdt.marketprocess.dto.response.*;
import cn.htdt.marketprocess.legacycenter.api.analysis.*;
import cn.htdt.marketprocess.vo.AtomGoodsPromotionGoodsRelationVo;
import cn.htdt.marketprocess.vo.AtomGoodsPromotionRuleVo;
import cn.htdt.marketprocess.vo.AtomPromotionGoodsInfoVo;
import cn.htdt.userprocess.dto.request.ReqUcDisplayConfigurationDTO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 详细说明.促销活动商品
 * <p>
 * Copyright: Copyright (c) 2021/6/29 14:48
 * <p>
 * Company: htd
 * <p>
 * deleteGoodsPromotion
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@DubboService
@Slf4j
public class GoodsPromotionGoodsRelationAnalysisServiceImpl extends CurrencyUtil implements GoodsPromotionGoodsRelationAnalysisService {

    @Resource
    private AtomGoodsPromotionGoodsRelationAnalysisService atomGoodsPromotionGoodsRelationAnalysisService;

    @Resource
    private AtomGoodsPromotionRuleAnalysisService atomGoodsPromotionRuleAnalysisService;

    @Resource
    private AtomGoodsPromotionPeriodAnalysisService atomGoodsPromotionPeriodAnalysisService;

    @DubboReference
    private GoodsAnalysisService goodsAnalysisService;

    @DubboReference
    private CloudPoolApplyAnalysisService cloudPoolApplyAnalysisService;

    @Resource
    private AtomAgentMarketRewardsetAnalysisService atomAgentMarketRewardsetAnalysisService;

    @Resource
    private AtomGoodsPromotionCategoryRelationAnalysisService atomGoodsPromotionCategoryRelationAnalysisService;

    @DubboReference
    private LegacyGoodsCenterService legacyGoodsCenterService;

    @DubboReference
    private GoodsTagAnalysisService goodsTagAnalysisService;

    @Autowired
    private GoodsPromotionAssert goodsPromotionAssert;

    @Autowired
    private GoodsPromotionRuleAnalysisService goodsPromotionRuleAnalysisService;

    @Autowired
    private RedisBaseUtil redisBaseUtil;

    @Autowired
    private PreDeterminedCheckService preDeterminedCheckService;

    @Autowired
    private LimitTimePromotionAnalysisServiceImpl limitTimePromotionAnalysisService;

    @Resource
    private GoodsPromotionRuleDao goodsPromotionRuleDao;

    @Resource
    private GoodsPromotionGoodsRelationDao goodsPromotionGoodsRelationDao;

    @Value("${media.default-picture-url}")
    private String DEFAULT_PICTURE_URL;


    /**
     * @param
     * @Description : 根据零售价修改折扣价
     * <AUTHOR>
     * @date : 20250616
     */
    @Override
    public ExecuteDTO promotionGoodsPriectindex(AtomReqGoodsPromotionGoodsRelationDTO atomReqGoodsPromotionGoodsRelationDTO) {
        log.info("**根据零售价修改折扣价--promotionGoodsStockReduce入参：{}", JSON.toJSONString(atomReqGoodsPromotionGoodsRelationDTO));
        AtomGoodsPromotionGoodsRelationVo vo_new = new AtomGoodsPromotionGoodsRelationVo();
        vo_new.setPromotionNo(atomReqGoodsPromotionGoodsRelationDTO.getPromotionNo());
        vo_new.setPeriodNo(atomReqGoodsPromotionGoodsRelationDTO.getPeriodNo());
        vo_new.setGoodsNo(atomReqGoodsPromotionGoodsRelationDTO.getGoodsNo());
      List<GoodsPromotionGoodsRelationDomain> goodsRelationDomainList =  goodsPromotionGoodsRelationDao.selectPromotionPeriodGoodsListgoodsNo(vo_new);
        if(null != goodsRelationDomainList && goodsRelationDomainList.size() > 0){
            GoodsPromotionGoodsRelationDomain goodsPromotionGoodsRelationDomain =  goodsRelationDomainList.get(0);
            AtomGoodsPromotionGoodsRelationVo atomGoodsPromotionGoodsRelationVo_new = new AtomGoodsPromotionGoodsRelationVo();
            atomGoodsPromotionGoodsRelationVo_new.setPromotionPrice(atomReqGoodsPromotionGoodsRelationDTO.getPromotionPrice());
            LocalDateTime ldt = LocalDateTime.now();
            atomGoodsPromotionGoodsRelationVo_new.setModifyTime(ldt);
            atomGoodsPromotionGoodsRelationVo_new.setId(goodsPromotionGoodsRelationDomain.getId());
            goodsPromotionGoodsRelationDao.updatePromotionGoodsRelationPeic(atomGoodsPromotionGoodsRelationVo_new);
        }
        log.info("根据零售价修改折扣价");
        return ExecuteDTO.success();
    }


    /**
     * @param reqGoodsPromotionGoodsRelationDTO
     * @Description : 查询促销活动设置商品列表
     * <AUTHOR> 张宇
     * @date : 2021/6/29 14:59
     */
    @Override
    public ExecuteDTO<List<ResGoodsPromotionGoodsRelationDTO>> getGoodsRelationList(ReqGoodsPromotionGoodsRelationDTO reqGoodsPromotionGoodsRelationDTO) {
        this.goodsPromotionAssert.dtoAssert(reqGoodsPromotionGoodsRelationDTO);
        log.info("--GoodsPromotionGoodsRelationAnalysisServiceImpl.getGoodsRelationList-入参-{}", String.valueOf(reqGoodsPromotionGoodsRelationDTO));
        AtomReqGoodsPromotionGoodsRelationDTO goodsRelationDTO = BeanCopierUtil.copy(reqGoodsPromotionGoodsRelationDTO, AtomReqGoodsPromotionGoodsRelationDTO.class);
        ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> executeDTO = atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList(goodsRelationDTO);
        if (executeDTO.successFlag()) {
            return ExecuteDTO.success(BeanCopierUtil.copyList(executeDTO.getData(), ResGoodsPromotionGoodsRelationDTO.class));
        }
        return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
    }

    /**
     * @param reqDTO
     * @Description : 秒杀商品列表聚合页查询（hxg）
     * <AUTHOR> 张宇
     * @date : 2021/6/29 14:59
     */
    @Override
    public ExecuteDTO<ResSecKillGoodsPageDTO> getSecKillGoodsPage(ReqPromotionInfoDTO reqDTO) {
        this.goodsPromotionAssert.getSecKillGoodsPageAssert(reqDTO);
        AtomReqPromotionInfoDTO promotionInfoDTO = BeanCopierUtil.copy(reqDTO, AtomReqPromotionInfoDTO.class);
        log.info("--atomGoodsPromotionRuleAnalysisService.getGoodsPromotionRuleByPromotionNo-start-promotionInfoDTO-{}", String.valueOf(promotionInfoDTO));
        //查询商品促销活动详情
        ExecuteDTO<AtomResGoodsPromotionRuleDTO> executeDTO = atomGoodsPromotionRuleAnalysisService.getGoodsPromotionRuleByPromotionNo(promotionInfoDTO);
        log.info("--atomGoodsPromotionRuleAnalysisService.getGoodsPromotionRuleByPromotionNo-end");
        if (executeDTO.successFlag()) {
            if (executeDTO.getData() != null) {
                ResSecKillGoodsPageDTO secKillGoodsPageDTO = this.getResSecKillGoodsPageDTO(reqDTO, executeDTO);
                return ExecuteDTO.success(secKillGoodsPageDTO);
            }
            return ExecuteDTO.error(MarketErrorCode.CODE_17000100);
        }
        return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
    }

    /**
     * 功能描述:获取商品列表以及活动信息
     *
     * @param reqDTO,executeDTO
     * @return ResSecKillGoodsPageDTO
     * @author: 张宇
     * @date: 2021/7/4 20:24
     */
    private ResSecKillGoodsPageDTO getResSecKillGoodsPageDTO(ReqPromotionInfoDTO reqDTO, ExecuteDTO<AtomResGoodsPromotionRuleDTO> executeDTO) {
        ResSecKillGoodsPageDTO secKillGoodsPageDTO = new ResSecKillGoodsPageDTO();
        ResGoodsPromotionRuleDTO secKillPromotion = BeanCopierUtil.copy(executeDTO.getData(), ResGoodsPromotionRuleDTO.class);
        //查询秒杀活动时间
        this.getPromotionPeriodInfo(secKillPromotion);
        //活动信息
        secKillGoodsPageDTO.setSecKillPromotion(secKillPromotion);
        List<ResSecKillGoodsDTO> secKillGoodsDTOList = new ArrayList<>();
        //查询活动下的商品列表
        AtomReqGoodsPromotionGoodsRelationDTO goodsRelationDTO = BeanCopierUtil.copy(reqDTO, AtomReqGoodsPromotionGoodsRelationDTO.class);
        log.info("--atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList-start-goodsRelationDTO-{}", String.valueOf(goodsRelationDTO));
        ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> goodsExecuteDTO = atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList(goodsRelationDTO);
        log.info("--atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList-end");
        if (goodsExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(goodsExecuteDTO.getData())) {
            List<ResGoodsPromotionGoodsRelationDTO> relationDTOList = BeanCopierUtil.copyList(goodsExecuteDTO.getData(), ResGoodsPromotionGoodsRelationDTO.class);
            //筛选出非子品的商品，包括主品与普通商品
            List<ResGoodsPromotionGoodsRelationDTO> notSubGoodsList = relationDTOList.stream().filter(goodsDTO ->
                    WhetherEnum.NO.getCode().equals(goodsDTO.getSubGoodsFlag())).collect(Collectors.toList());
            //筛选出子品的商品
            List<ResGoodsPromotionGoodsRelationDTO> subGoodsList = relationDTOList.stream().filter(goodsDTO ->
                    WhetherEnum.YES.getCode().equals(goodsDTO.getSubGoodsFlag())).collect(Collectors.toList());
            //列表查询排除子品，秒杀商品聚合页调用
            if (!WhetherEnum.YES.getCode().equals(reqDTO.getQuerySubGoodsFlag())) {
                this.exchangeGoodsPromotionGoodsInfo(reqDTO, secKillPromotion, secKillGoodsDTOList, notSubGoodsList, subGoodsList);
            } else {
                //指定商品编号查询不排除子品，
                this.exchangeGoodsPromotionGoodsInfo(reqDTO, secKillPromotion, secKillGoodsDTOList, relationDTOList, subGoodsList);
            }
        }
        secKillGoodsPageDTO.setGoodsList(secKillGoodsDTOList);
        return secKillGoodsPageDTO;
    }

    /**
     * 更加查询结果查询对应的数据
     *
     * @param reqDTO
     * @param secKillPromotion
     * @param secKillGoodsDTOList
     * @param goodsList
     * @param subGoodsList
     */
    private void exchangeGoodsPromotionGoodsInfo(ReqPromotionInfoDTO reqDTO, ResGoodsPromotionRuleDTO secKillPromotion, List<ResSecKillGoodsDTO> secKillGoodsDTOList,
                                                 List<ResGoodsPromotionGoodsRelationDTO> goodsList, List<ResGoodsPromotionGoodsRelationDTO> subGoodsList) {
        ReqGoodsDTO reqGoodsDTO = new ReqGoodsDTO();
        //平台秒杀活动即云池商品，需要根据cloudPoolgoodsNo查询
        if (SourceTypeEnum.SOURCE_TYPE_1001.getCode().equals(secKillPromotion.getSourceType())) {
            reqGoodsDTO.setCloudPoolGoodsNos(goodsList.stream().map(ResGoodsPromotionGoodsRelationDTO::getGoodsNo).collect(Collectors.toList()));
        } else {
            //店铺活动则根据goodsNo查询
            reqGoodsDTO.setGoodsNos(goodsList.stream().map(ResGoodsPromotionGoodsRelationDTO::getGoodsNo).collect(Collectors.toList()));
        }
        reqGoodsDTO.setNoPage();
        reqGoodsDTO.setStoreNo(reqDTO.getStoreNo());
        // 可用的商品
        reqGoodsDTO.setDisableFlag(NumConstant.TWO);
        // 如果是汇赚钱进入秒杀商品聚合页列表，店铺活动，只查询分销商品
        if (SourceTypeEnum.SOURCE_TYPE_1003.getCode().equals(secKillPromotion.getSourceType())
                && SecKillLocationEnum.H_Z_Q_SECKILL.getCode().equals(reqDTO.getSecKillLocation())) {
            reqGoodsDTO.setDistributeGoodsFlag(WhetherEnum.YES.getCode());
        }
        //查询商品信息
        log.info("--goodsAnalysisService.getGoodsPage-start-reqGoodsDTO-{}", String.valueOf(reqGoodsDTO));
        ExecuteDTO<ExecutePageDTO<ResGoodsDTO>> goodsExecute = goodsAnalysisService.getGoodsPage(reqGoodsDTO);
        log.info("--goodsAnalysisService.getGoodsPage-end");
        if (goodsExecute.successFlag() && goodsExecute.getData() != null && CollectionUtils.isNotEmpty(goodsExecute.getData().getRows())) {
            List<ResGoodsDTO> resGoodsDTOList = goodsExecute.getData().getRows();
            // 查询出所有子品的商品数据，用于计算佣金
            if (CollectionUtils.isNotEmpty(subGoodsList)) {
                this.getSubGoodsInfo(reqDTO, subGoodsList, reqGoodsDTO);
            }
            //将商品信息遍历赋值到秒杀商品列表下
            goodsList.forEach(goodsDTO -> {
                //如果是云池商品则拿查询出来的cloudPoolGoodsNo与goodsNo比较，反正则直接用goodsno
                Optional<ResGoodsDTO> optional = resGoodsDTOList.stream().filter(goods -> goodsDTO.getGoodsNo().equals(goods.getGoodsNo()) || goodsDTO.getGoodsNo().equals(goods.getCloudPoolGoodsNo())).findFirst();
                if (optional.isPresent()) {
                    ResGoodsDTO resGoodsDTO = optional.get();
                    ResSecKillGoodsDTO secKillGoodsDTO = BeanCopierUtil.copy(resGoodsDTO, ResSecKillGoodsDTO.class);
                    // 20230928 蛋品 多单位商品
                    if (MultiUnitTypeEnum.MAIN_UNIT.getCode().equals(resGoodsDTO.getMultiUnitType()) && StringUtils.isNotBlank(resGoodsDTO.getCalculationUnitName())) {
                        secKillGoodsDTO.setGoodsName(resGoodsDTO.getGoodsName() + "(" + resGoodsDTO.getCalculationUnitName() + ")");
                    }
                    secKillGoodsDTO.setLimitBuyNum(goodsDTO.getLimitBuyNum());
                    secKillGoodsDTO.setPromotionPrice(goodsDTO.getPromotionPrice());    //秒杀价格
                    secKillGoodsDTO.setActivityExpenses(goodsDTO.getActivityExpenses());   //活动费用
                    secKillGoodsDTO.setAgentCommissionRatio(goodsDTO.getAgentCommissionRatio());    //佣金比例
                    secKillGoodsDTO.setUserLimitNum(secKillPromotion.getUserLimitNum());
                    secKillGoodsDTO.setPromotionNo(secKillPromotion.getPromotionNo());
                    secKillGoodsDTO.setPromotionName(secKillPromotion.getPromotionName());
                    secKillGoodsDTO.setStoreTotalNum(secKillPromotion.getStoreTotalNum());
                    secKillGoodsDTO.setEffectiveTime(secKillPromotion.getEffectiveTime());
                    secKillGoodsDTO.setInvalidTime(secKillPromotion.getInvalidTime());
                    secKillGoodsDTO.setPeriodNo(goodsDTO.getPeriodNo());    //活动场次时间段编号
                    secKillGoodsDTO.setDailyStartTime(secKillPromotion.getDailyStartTime());
                    secKillGoodsDTO.setDailyEndTime(secKillPromotion.getDailyEndTime());
                    secKillGoodsDTO.setRemainStockNum(goodsDTO.getRemainStockNum());    //活动商品剩余促销库存
                    secKillGoodsDTO.setSourceType(secKillPromotion.getSourceType());
                    secKillGoodsDTO.setSettingStockNum(goodsDTO.getSettingStockNum());
                    secKillGoodsDTO.setPromotionType(secKillPromotion.getPromotionType());
                    secKillGoodsDTO.setUserScope(secKillPromotion.getUserScope());
                    secKillGoodsDTO.setUpDownFlag(secKillPromotion.getUpDownFlag());    //活动上下架状态
                    secKillGoodsDTO.setDeliveryFlag(secKillPromotion.getDeliveryFlag());    //配送方式是否以商品自身配置为主
                    secKillGoodsDTO.setDeliveryWay(secKillPromotion.getDeliveryWay());  //活动设置的配送方式
                    //如果是主品，则需要查询出子品的最小秒杀价
                    if (SeriesTypeEnum.PARENT_GOODS.getCode().equals(resGoodsDTO.getSeriesType()) && CollectionUtils.isNotEmpty(subGoodsList)) {
                        // 找出对应主品下的子品商品
                        List<ResGoodsPromotionGoodsRelationDTO> currentParentSubList = subGoodsList.stream()
                                .filter(subGoods -> subGoods.getParentGoodsNo().equals(goodsDTO.getGoodsNo())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(currentParentSubList)) {
                            // 最小秒杀价格
                            Optional<ResGoodsPromotionGoodsRelationDTO> minOptional = subGoodsList.stream().filter(
                                    subGoods -> subGoods.getParentGoodsNo().equals(goodsDTO.getGoodsNo()))
                                    .min(Comparator.comparing(ResGoodsPromotionGoodsRelationDTO::getPromotionPrice));
                            //设置最小秒杀价
                            if (minOptional.isPresent()) {
                                ResGoodsPromotionGoodsRelationDTO goodsRelationDTO = minOptional.get();
                                secKillGoodsDTO.setMinPromotionPrice(goodsRelationDTO.getPromotionPrice());
                                // 个人限购数取最低秒杀价格商品个人限购数
                                secKillGoodsDTO.setLimitBuyNum(goodsRelationDTO.getLimitBuyNum());
                            }
                            // 取最大活动价格
                            Optional<ResGoodsPromotionGoodsRelationDTO> maxOptional = subGoodsList.stream().filter(
                                    subGoods -> subGoods.getParentGoodsNo().equals(goodsDTO.getGoodsNo()))
                                    .max(Comparator.comparing(ResGoodsPromotionGoodsRelationDTO::getPromotionPrice));
                            maxOptional.ifPresent(resGoodsPromotionGoodsRelationDTO -> secKillGoodsDTO.setMaxPromotionPrice(resGoodsPromotionGoodsRelationDTO.getPromotionPrice()));

                            // 汇总子品中所有的活动剩余库存以及设置的活动库存
                            Integer remainStockTotalNum = 0;
                            Integer settingStockTotalNum = 0;
                            for (ResGoodsPromotionGoodsRelationDTO goodsRelationDTO : currentParentSubList) {
                                if (null != goodsRelationDTO && null != goodsRelationDTO.getRemainStockNum()) {
                                    remainStockTotalNum = remainStockTotalNum + goodsRelationDTO.getRemainStockNum();
                                }
                                if (null != goodsRelationDTO && null != goodsRelationDTO.getSettingStockNum()) {
                                    settingStockTotalNum = settingStockTotalNum + goodsRelationDTO.getSettingStockNum();
                                }
                            }
                            secKillGoodsDTO.setRemainStockNum(remainStockTotalNum);    //所有子品的活动商品剩余促销库存
                            secKillGoodsDTO.setSettingStockNum(settingStockTotalNum);   //所有子品的设置活动库存
                            // 计算酬劳
                            this.calculateReward(secKillGoodsDTO, currentParentSubList);
                        }
                    } else {
                        // 非系列商品
                        List<ResGoodsPromotionGoodsRelationDTO> goods = new ArrayList<>();
                        goodsDTO.setCloudPoolSupplyPrice(resGoodsDTO.getCloudPoolSupplyPrice());
                        goods.add(goodsDTO);
                        // 计算酬劳
                        this.calculateReward(secKillGoodsDTO, goods);
                    }
                    secKillGoodsDTOList.add(secKillGoodsDTO);
                }
            });
        }
    }

    /**
     * 功能描述: 查询秒杀活动时间
     *
     * @param secKillPromotion
     * @author: 张宇
     * @date: 2021/7/6 20:12
     */
    private void getPromotionPeriodInfo(ResGoodsPromotionRuleDTO secKillPromotion) {
        AtomReqGoodsPromotionPeriodDTO periodDTO = new AtomReqGoodsPromotionPeriodDTO();
        periodDTO.setPromotionNo(secKillPromotion.getPromotionNo());
        ExecuteDTO<List<AtomResGoodsPromotionPeriodDTO>> executePeriodDTO = atomGoodsPromotionPeriodAnalysisService.getGoodsPromotionPeriodByPromotionNo(periodDTO);
        if (executePeriodDTO.successFlag() && CollectionUtils.isNotEmpty(executePeriodDTO.getData())) {
            AtomResGoodsPromotionPeriodDTO atomResGoodsPromotionPeriodDTO = executePeriodDTO.getData().get(NumConstant.ZERO);
            secKillPromotion.setDailyStartTime(atomResGoodsPromotionPeriodDTO.getStartTime());
            secKillPromotion.setDailyEndTime(atomResGoodsPromotionPeriodDTO.getEndTime());
            secKillPromotion.setPeriodNo(atomResGoodsPromotionPeriodDTO.getPeriodNo());
        }
    }


    /**
     * @param reqDTO
     * @Description : 首页获取店铺秒杀商品列表
     * <AUTHOR> 张宇
     * @date : 2021/7/05 14:59
     */
    @Override
    public ExecuteDTO<ResIndexSecKillPromotionDTO> getIndexSecKillGoodsList(ReqPromotionInfoDTO reqDTO) {
        log.info("getIndexSecKillGoodsList----->首页获取店铺秒杀商品列表, 入参: {}", JSON.toJSONString(reqDTO));
        this.goodsPromotionAssert.getIndexSecKillGoodsListAssert(reqDTO);
        //查询秒杀活动
        ExecuteDTO<List<ResGoodsPromotionRuleDTO>> executeDTO = goodsPromotionRuleAnalysisService.getGoodsPromotionRuleListForHxg(reqDTO);
        if (executeDTO.successFlag() && CollectionUtils.isNotEmpty(executeDTO.getData())) {
            ResIndexSecKillPromotionDTO indexSecKillPromotionDTO = new ResIndexSecKillPromotionDTO();
            List<ResSecKillGoodsDTO> secKillGoodsDTOList = new ArrayList<>();
            List<ResGoodsPromotionRuleDTO> promotionRuleDTOList = executeDTO.getData();
            // 按活动场次开始时间 排序
            List<AtomResGoodsPromotionRuleDTO> promotionList = BeanCopierUtil.copyList(promotionRuleDTOList, AtomResGoodsPromotionRuleDTO.class);
            List<AtomResGoodsPromotionRuleDTO> atomResGoodsPromotionRuleDTOList = limitTimePromotionAnalysisService.sortPromotionData(promotionList);
            if (CollectionUtils.isNotEmpty(atomResGoodsPromotionRuleDTOList)) {
                //秒杀活动数量
                Set<String> promotionNoSet = new HashSet<>();
                atomResGoodsPromotionRuleDTOList.forEach(promotion -> {
                    promotionNoSet.add(promotion.getPromotionNo());
                });
                indexSecKillPromotionDTO.setPromotionSize(promotionNoSet.size());
                //如果活动等于1，则取第一个活动的promotionNo返回给前台
                if (promotionNoSet.size() == NumConstant.ONE) {
                    indexSecKillPromotionDTO.setPromotionNo(new ArrayList<>(promotionNoSet).get(NumConstant.ZERO));
                }
                // 保证取得活动来自不同的活动
                for (AtomResGoodsPromotionRuleDTO goodsPromotionRule : atomResGoodsPromotionRuleDTOList) {
                    // 找到5个商品则退出
                    if (secKillGoodsDTOList.size() > NumConstant.FIVE) {
                        break;
                    }
                    //查询商品详情时，区分使用 cloudPoolGoodsNo 或者是 goodsNo
                    reqDTO.setSourceType(goodsPromotionRule.getSourceType());
                    //查询活动下的商品列表
                    AtomReqGoodsPromotionGoodsRelationDTO goodsRelationDTO =
                            BeanCopierUtil.copy(goodsPromotionRule, AtomReqGoodsPromotionGoodsRelationDTO.class);
                    goodsRelationDTO.setPromotionNo(goodsPromotionRule.getPromotionNo());
                    log.info("--atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList-start-goodsRelationDTO-{}", goodsRelationDTO);
                    ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> goodsExecuteDTO =
                            atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList(goodsRelationDTO);
                    log.info("--atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList-end");
                    if (goodsExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(goodsExecuteDTO.getData())) {
                        List<ResGoodsPromotionGoodsRelationDTO> relationDTOList = BeanCopierUtil.copyList(goodsExecuteDTO.getData(), ResGoodsPromotionGoodsRelationDTO.class);
                        relationDTOList.forEach(relationDTO -> {
                            relationDTO.setEffectiveTime(goodsPromotionRule.getEffectiveTime());  //活动开始时间
                            relationDTO.setInvalidTime(goodsPromotionRule.getInvalidTime());  //活动结束时间
                            relationDTO.setPromotionDate(goodsPromotionRule.getPromotionDate());    //活动场次具体对应的日期
                            relationDTO.setDailyStartTime(goodsPromotionRule.getDailyStartTime());    //活动时间段的开始时间
                            relationDTO.setDailyEndTime(goodsPromotionRule.getDailyEndTime());    //活动时间段的结束时间
                            relationDTO.setUserLimitNum(goodsPromotionRule.getUserLimitNum());    //用户限制参与活动次数
                            relationDTO.setStoreTotalNum(goodsPromotionRule.getStoreTotalNum());  //单店总限购数量
                            relationDTO.setPromotionName(goodsPromotionRule.getPromotionName());  //活动名称
                            relationDTO.setSourceType(goodsPromotionRule.getSourceType());    //活动来源
                            relationDTO.setPromotionType(goodsPromotionRule.getPromotionType());  //活动类型
                            relationDTO.setUserScope(goodsPromotionRule.getUserScope());// 活动对象
                        });
                        //获取商品信息
                        this.getIndexSecKillGoodsDetailList(reqDTO, secKillGoodsDTOList, relationDTOList);
                    }
                }
            }
            // 秒杀商品出参数据扩展
            if (CollectionUtils.isNotEmpty(secKillGoodsDTOList)) {
                this.extendPromotionGoodsData(secKillGoodsDTOList);
            }
            indexSecKillPromotionDTO.setGoodsList(secKillGoodsDTOList);
            return ExecuteDTO.success(indexSecKillPromotionDTO);
        }
        return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());

    }

    /**
     * 店铺装修获取秒杀活动商品
     *
     * @param reqDTO 查询参数
     * <AUTHOR>
     * @date 2021-11-12
     */
    @Override
    public ExecuteDTO<ResIndexSecKillPromotionDTO> getSecKillGoodsForShopDesc(ReqPromotionInfoDTO reqDTO) {
        this.goodsPromotionAssert.getIndexSecKillGoodsListAssert(reqDTO);
        //查询秒杀活动
        ExecuteDTO<List<ResGoodsPromotionRuleDTO>> executeDTO = goodsPromotionRuleAnalysisService.getGoodsPromotionRuleListForHxg(reqDTO);
        if (executeDTO.successFlag() && CollectionUtils.isNotEmpty(executeDTO.getData())) {
            ResIndexSecKillPromotionDTO indexSecKillPromotionDTO = new ResIndexSecKillPromotionDTO();
            List<ResSecKillGoodsDTO> secKillGoodsDTOList = new ArrayList();
            List<ResGoodsPromotionRuleDTO> promotionRuleDTOList = executeDTO.getData();
            // 按活动场次开始时间 排序
            List<AtomResGoodsPromotionRuleDTO> promotionList = BeanCopierUtil.copyList(promotionRuleDTOList, AtomResGoodsPromotionRuleDTO.class);
            List<AtomResGoodsPromotionRuleDTO> atomResGoodsPromotionRuleDTOList = limitTimePromotionAnalysisService.sortPromotionData(promotionList);
            if (CollectionUtils.isNotEmpty(atomResGoodsPromotionRuleDTOList)) {
                //秒杀活动数量
                Set<String> promotionNoSet = new HashSet<>();
                atomResGoodsPromotionRuleDTOList.forEach(promotion -> {
                    promotionNoSet.add(promotion.getPromotionNo());
                });
                indexSecKillPromotionDTO.setPromotionSize(promotionNoSet.size());
                //如果活动等于1，则取第一个活动的promotionNo返回给前台
                if (promotionNoSet.size() == NumConstant.ONE) {
                    indexSecKillPromotionDTO.setPromotionNo(atomResGoodsPromotionRuleDTOList.get(NumConstant.ZERO).getPromotionNo());
                }
                // 保证取得活动来自不同的活动
                for (AtomResGoodsPromotionRuleDTO goodsPromotionRule : atomResGoodsPromotionRuleDTOList) {
                    // 找到5个商品则退出
                    if (secKillGoodsDTOList.size() > NumConstant.FIVE) {
                        break;
                    }
                    //查询商品详情时，区分使用 cloudPoolGoodsNo 或者是 goodsNo
                    reqDTO.setSourceType(goodsPromotionRule.getSourceType());
                    //查询活动下的商品列表
                    AtomReqGoodsPromotionGoodsRelationDTO goodsRelationDTO =
                            BeanCopierUtil.copy(goodsPromotionRule, AtomReqGoodsPromotionGoodsRelationDTO.class);
                    goodsRelationDTO.setPromotionNo(goodsPromotionRule.getPromotionNo());
                    log.info("--atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList-start-goodsRelationDTO-{}", goodsRelationDTO);
                    ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> goodsExecuteDTO =
                            atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList(goodsRelationDTO);
                    log.info("--atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList-end");
                    if (goodsExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(goodsExecuteDTO.getData())) {
                        List<ResGoodsPromotionGoodsRelationDTO> relationDTOList = BeanCopierUtil.copyList(goodsExecuteDTO.getData(), ResGoodsPromotionGoodsRelationDTO.class);
                        relationDTOList.forEach(relationDTO -> {
                            relationDTO.setEffectiveTime(goodsPromotionRule.getEffectiveTime());  //活动开始时间
                            relationDTO.setInvalidTime(goodsPromotionRule.getInvalidTime());  //活动结束时间
                            relationDTO.setPromotionDate(goodsPromotionRule.getPromotionDate());    //活动场次具体对应的日期
                            relationDTO.setDailyStartTime(goodsPromotionRule.getDailyStartTime());    //活动时间段的开始时间
                            relationDTO.setDailyEndTime(goodsPromotionRule.getDailyEndTime());    //活动时间段的结束时间
                            relationDTO.setUserLimitNum(goodsPromotionRule.getUserLimitNum());    //用户限制参与活动次数
                            relationDTO.setStoreTotalNum(goodsPromotionRule.getStoreTotalNum());  //单店总限购数量
                            relationDTO.setPromotionName(goodsPromotionRule.getPromotionName());  //活动名称
                            relationDTO.setSourceType(goodsPromotionRule.getSourceType());    //活动来源
                            relationDTO.setPromotionType(goodsPromotionRule.getPromotionType());  //活动类型
                            relationDTO.setUserScope(goodsPromotionRule.getUserScope());// 活动对象
                        });
                        //获取商品信息
                        this.getIndexSecKillGoodsDetailList(reqDTO, secKillGoodsDTOList, relationDTOList);
                    }
                }
            }
            // 秒杀商品出参数据扩展
            if (CollectionUtils.isNotEmpty(secKillGoodsDTOList)) {
                this.extendPromotionGoodsData(secKillGoodsDTOList);
            }
            indexSecKillPromotionDTO.setGoodsList(secKillGoodsDTOList);
            return ExecuteDTO.success(indexSecKillPromotionDTO);
        }
        return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
    }

    /**
     * 秒杀商品出参数据扩展
     *
     * @param secKillGoodsList 待返回的秒杀数据
     * <AUTHOR>
     * @date 2021-11-10
     */
    private void extendPromotionGoodsData(List<ResSecKillGoodsDTO> secKillGoodsList) {
        // 查询出所有的促销活动标签
        List<ResGoodsTagDTO> goodsPromotionTagList = this.getGoodsPromotionTagName();
        for (ResSecKillGoodsDTO resSecKillGoodsDTO : secKillGoodsList) {
            // 根据商品对应的参与的促销活动，找到对应的活动标签
            List<String> tagNameList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(goodsPromotionTagList)) {
                for (ResGoodsTagDTO resGoodsTagDTO : goodsPromotionTagList) {
                    if (StringUtils.isNotBlank(resGoodsTagDTO.getTagPictureUrl())
                            && resSecKillGoodsDTO.getPromotionType().equals(resGoodsTagDTO.getPromotionType())) {
                        tagNameList.add(resGoodsTagDTO.getTagPictureUrl());
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(tagNameList)) {
                resSecKillGoodsDTO.setActiveTagNames(tagNameList);
            }
            // 计算活动倒计时和按钮状态
            this.getCountdownTimeStampAndButton(resSecKillGoodsDTO);
        }
    }

    /**
     * 查询商品促销活动标签信息
     *
     * <AUTHOR>
     * @date 2021-11-10
     */
    public List<ResGoodsTagDTO> getGoodsPromotionTagName() {
        // 活动标签 查询
        ReqGoodsTagDTO activeReqGoodsTagDTO = new ReqGoodsTagDTO();
        activeReqGoodsTagDTO.setTagType(GoodsTagTypeEnum.PROMOTION_LABEL.getCode());
        List<ResGoodsTagDTO> goodsTagRelationList = new ArrayList<>();
        activeReqGoodsTagDTO.setNoPage();
        ExecuteDTO<ExecutePageDTO<ResGoodsTagDTO>> goodsTagListForPage = goodsTagAnalysisService.getGoodsTagListForPage(activeReqGoodsTagDTO);

        if (goodsTagListForPage != null && CommonCode.SUCCESS.getCode().equals(goodsTagListForPage.getStatus())
                && goodsTagListForPage.getData() != null) {
            goodsTagRelationList = goodsTagListForPage.getData().getRows();
        }

        return goodsTagRelationList;
    }

    /**
     * 计算秒杀商品活动的倒计时和按钮状态
     *
     * @param resSecKillGoodsDTO 秒杀商品
     * <AUTHOR>
     * @date 2021-11-10
     */
    private void getCountdownTimeStampAndButton(ResSecKillGoodsDTO resSecKillGoodsDTO) {
        // 先根据活动的开始和结束时间，计算倒计时和按钮的未开始、进行中、已结束
        // 活动的开始时间和结束时间
        LocalDateTime startTime = LocalDateTime.of(resSecKillGoodsDTO.getPromotionDate(), resSecKillGoodsDTO.getDailyStartTime());
        LocalDateTime endTime = LocalDateTime.of(resSecKillGoodsDTO.getPromotionDate(), resSecKillGoodsDTO.getDailyEndTime());
        // 当前时间
        LocalDateTime nowTime = DateUtil.getLocalDateTime();
        // 如果当前时间在活动最开始之前，则取当前时间到活动场次开始时间的秒数差
        if (nowTime.isBefore(startTime)) {
            // 计算距开始的倒计时
            resSecKillGoodsDTO.setCountdownTimeWords(PlatformCountDownWordsEnum.FROM_START.getType());
            resSecKillGoodsDTO.setCountdownTimeStamp(DateUtil.getLocalDateTimeDiffSecond(nowTime, startTime));
            // 按钮展示未开始
            resSecKillGoodsDTO.setButton(SecKillGoodsStatusEnum.NOT_START.getCode());
        } else if (!nowTime.isAfter(endTime)) {
            // 如果当前时间不大于活动的结束时间，说明活动处于进行中，则取当前时间和活动结束时间差的秒数
            // 计算距结束的倒计时
            resSecKillGoodsDTO.setCountdownTimeWords(PlatformCountDownWordsEnum.FROM_END.getType());
            resSecKillGoodsDTO.setCountdownTimeStamp(DateUtil.getLocalDateTimeDiffSecond(nowTime, endTime));
            // 设置成进行中
            resSecKillGoodsDTO.setButton(SecKillGoodsStatusEnum.STARTING.getCode());
        } else {
            // 当前时间大于活动的结束时间，说明当前场次的活动已经结束，活动倒计时设置为0
            resSecKillGoodsDTO.setCountdownTimeStamp(0L);
            // 按钮设置成已结束
            resSecKillGoodsDTO.setButton(SecKillGoodsStatusEnum.END.getCode());
        }

        // 如果活动已结束，则直接退出
        if (SecKillGoodsStatusEnum.END.getCode().equals(resSecKillGoodsDTO.getButton())) {
            return;
        }

        // 活动没有结束，看商品是否被抢光，如果已经抢光的话，则按钮设置成 已抢光，然后直接退出
        // 超过单店总限购数量（平台活动判断） 或者 超过秒杀商品促销库存，按钮设置成已抢光
        String storeBuyNumKey = String.format(PromotionKeyConstant.PROMOTION_STORE_BUYGOODSNUM_PREFIX,
                resSecKillGoodsDTO.getPromotionNo(), resSecKillGoodsDTO.getStoreNo());
        Object storeBuyNum = redisBaseUtil.get(storeBuyNumKey); //店铺已购买的秒杀商品数
        if ((PlatformTypeEnum.PLATFORM_COMPANY.getCode().equals(resSecKillGoodsDTO.getSourceType())
                && storeBuyNum != null && resSecKillGoodsDTO.getStoreTotalNum() != null
                && resSecKillGoodsDTO.getStoreTotalNum().compareTo(Integer.valueOf(storeBuyNum.toString())) <= 0)
                || (resSecKillGoodsDTO.getRemainStockNum() != null && resSecKillGoodsDTO.getRemainStockNum() <= 0)) {
            resSecKillGoodsDTO.setButton(SecKillGoodsStatusEnum.NOT_HAVING.getCode());
            return;
        }

        // 商品已下架，按钮设置成已下架，然后退出
        if (GoodsStatusEnum.OFF_SHELF.getCode().equals(resSecKillGoodsDTO.getGoodsShowStatus())) {
            resSecKillGoodsDTO.setButton(SecKillGoodsStatusEnum.REMOVED.getCode());    //已下架
            return;
        }

        // 可售库存为0，按钮设置成已售罄，然后退出
        if (resSecKillGoodsDTO.getCanSaleStockNum() != null
                && resSecKillGoodsDTO.getCanSaleStockNum().compareTo(BigDecimal.valueOf(NumConstant.ZERO)) <= NumConstant.ZERO) {
            resSecKillGoodsDTO.setButton(SecKillGoodsStatusEnum.SOLD_OUT.getCode());
        }
    }

    /**
     * @param reqGoodsPromotionGoodsRelationDTO
     * @Description : 查询活动下商品促销信息
     * <AUTHOR> 高繁
     * @date : 2021/7/6 14:41
     */
    @Override
    public ExecuteDTO<List<ResGoodsPromotionGoodsRelationDTO>> getPromotionGoodsInfoByGoods(ReqGoodsPromotionGoodsRelationDTO reqGoodsPromotionGoodsRelationDTO) {
        log.info(String.format("getPromotionGoodsInfoByGoods 入参:%s", JSON.toJSONString(reqGoodsPromotionGoodsRelationDTO)));
        this.goodsPromotionAssert.getPromotionGoodsInfoByGoods(reqGoodsPromotionGoodsRelationDTO);
        //
        AtomReqGoodsPromotionGoodsRelationDTO relationDTO = BeanCopierUtil.copy(reqGoodsPromotionGoodsRelationDTO, AtomReqGoodsPromotionGoodsRelationDTO.class);
        ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> executeDTO = atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList(relationDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        if (CollectionUtils.isNotEmpty(executeDTO.getData())) {
            return ExecuteDTO.success(BeanCopierUtil.copyList(executeDTO.getData(), ResGoodsPromotionGoodsRelationDTO.class));
        }
        return ExecuteDTO.success();
    }

    /**
     * 获取商品详情列表
     *
     * @param reqDTO
     * @param secKillGoodsDTOList
     * @param relationDTOList
     */
    private void getIndexSecKillGoodsDetailList(ReqPromotionInfoDTO reqDTO, List<ResSecKillGoodsDTO> secKillGoodsDTOList, List<ResGoodsPromotionGoodsRelationDTO> relationDTOList) {
        log.info("getIndexSecKillGoodsDetailList----->reqDTO: {}", JSON.toJSONString(reqDTO));
        //筛选出非子品的商品，包括主品与普通商品
        List<ResGoodsPromotionGoodsRelationDTO> notSubGoodsList = relationDTOList.stream().filter(goodsDTO ->
                WhetherEnum.NO.getCode().equals(goodsDTO.getSubGoodsFlag())).collect(Collectors.toList());
        //筛选出子品的商品
        List<ResGoodsPromotionGoodsRelationDTO> subGoodsList = relationDTOList.stream().filter(goodsDTO ->
                WhetherEnum.YES.getCode().equals(goodsDTO.getSubGoodsFlag())).collect(Collectors.toList());
        // 没有主品+普通商品的话，直接退出
        if (CollectionUtils.isEmpty(notSubGoodsList)) {
            return;
        }
        ReqGoodsDTO reqGoodsDTO = new ReqGoodsDTO();
        reqGoodsDTO.setStoreNo(reqDTO.getStoreNo());
        reqGoodsDTO.setNoPage();
        // 汇赚钱进入时，对于店铺活动，只查询分销商品
        if (SourceTypeEnum.SOURCE_TYPE_1003.getCode().equals(reqDTO.getSourceType())
                && SecKillLocationEnum.H_Z_Q_SECKILL.getCode().equals(reqDTO.getSecKillLocation())) {
            reqGoodsDTO.setDistributeGoodsFlag(WhetherEnum.YES.getCode());
        }
        // 可用的商品
        reqGoodsDTO.setDisableFlag(NumConstant.TWO);
        if (SourceTypeEnum.SOURCE_TYPE_1003.getCode().equals(reqDTO.getSourceType())) {
            reqGoodsDTO.setGoodsNos(notSubGoodsList.stream().map(ResGoodsPromotionGoodsRelationDTO::getGoodsNo).collect(Collectors.toList()));
        } else {
            // 云池商品上下架优化, 如果为平台创建的活动
            if (SourceTypeEnum.SOURCE_TYPE_1001.getCode().equals(reqDTO.getSourceType())) {
                // 汇赚钱秒杀, 查询上架类型为仅上架至活动区域的商品
                if (SecKillLocationEnum.H_Z_Q_SECKILL.getCode().equals(reqDTO.getSecKillLocation())) {
                    reqGoodsDTO.setShelfType(CloudPoolGoodsShelfTypeEnum.PROMOTION_SHELF.getCode());
                    log.info("getIndexSecKillGoodsDetailList----->汇赚钱秒杀查询上架类型为仅上架至活动区域和正常上架的的商品");
                } else {
                    // 其他情况, 以传过来的参数为准
                    if (StringUtils.isNotBlank(reqDTO.getShelfType())) {
                        log.info("getIndexSecKillGoodsDetailList----->shelfType :{}", reqDTO.getShelfType());
                        reqGoodsDTO.setShelfType(reqDTO.getShelfType());
                    }
                }
            }
            reqGoodsDTO.setCloudPoolGoodsNos(notSubGoodsList.stream().map(ResGoodsPromotionGoodsRelationDTO::getGoodsNo).collect(Collectors.toList()));
        }
        //查询商品信息主品+普通商品
        log.info("--goodsAnalysisService.getGoodsPage-start, 入参: reqGoodsDTO-{}", JSON.toJSONString(reqGoodsDTO));
        ExecuteDTO<ExecutePageDTO<ResGoodsDTO>> goodsExecute = goodsAnalysisService.getGoodsPage(reqGoodsDTO);
        log.info("--goodsAnalysisService.getGoodsPage-end");
        if (goodsExecute.successFlag() && goodsExecute.getData() != null && CollectionUtils.isNotEmpty(goodsExecute.getData().getRows())) {
            // 汇赚钱页面，需要查询出所有子品的商品数据，用于计算佣金
            if (CollectionUtils.isNotEmpty(subGoodsList)
                    && SecKillLocationEnum.H_Z_Q_SECKILL.getCode().equals(reqDTO.getSecKillLocation())) {
                this.getSubGoodsInfo(reqDTO, subGoodsList, reqGoodsDTO);
            }

            List<ResGoodsDTO> resGoodsDTOList = goodsExecute.getData().getRows();
            //将商品信息遍历赋值到秒杀商品列表下
            for (ResGoodsPromotionGoodsRelationDTO notSubGoods : notSubGoodsList) {
                //如果是云池商品则拿查询出来的cloudPoolGoodsNo与goodsNo比较，反正则直接用goodsno
                Optional<ResGoodsDTO> optional = resGoodsDTOList.stream().
                        filter(goods -> (notSubGoods.getGoodsNo().equals(goods.getGoodsNo()) || notSubGoods.getGoodsNo().equals(goods.getCloudPoolGoodsNo()))
                                && GoodsStatusEnum.ON_SHELF.getCode().equals(goods.getGoodsStatus()))
                        .findFirst();
                if (optional.isPresent()) {
                    ResGoodsDTO resGoodsDTO = optional.get();
                    ResSecKillGoodsDTO secKillGoodsDTO = BeanCopierUtil.copy(resGoodsDTO, ResSecKillGoodsDTO.class);
                    // 20230928 蛋品 多单位商品
                    if (MultiUnitTypeEnum.MAIN_UNIT.getCode().equals(resGoodsDTO.getMultiUnitType()) && StringUtils.isNotBlank(resGoodsDTO.getCalculationUnitName())) {
                        secKillGoodsDTO.setGoodsName(resGoodsDTO.getGoodsName() + "(" + resGoodsDTO.getCalculationUnitName() + ")");
                    }
                    // 取出活动商品配置的信息
                    secKillGoodsDTO.setLimitBuyNum(notSubGoods.getLimitBuyNum());   //商品个人限购数
                    secKillGoodsDTO.setPromotionPrice(notSubGoods.getPromotionPrice()); //活动价格
                    secKillGoodsDTO.setUserLimitNum(notSubGoods.getUserLimitNum()); //用户限制参与活动次数
                    secKillGoodsDTO.setUserScope(notSubGoods.getUserScope());//活动对象
                    secKillGoodsDTO.setPromotionNo(notSubGoods.getPromotionNo());   //活动编号
                    secKillGoodsDTO.setPromotionName(notSubGoods.getPromotionName());   //活动名称
                    secKillGoodsDTO.setStoreTotalNum(notSubGoods.getStoreTotalNum());   //单店限购总数
                    secKillGoodsDTO.setPeriodNo(notSubGoods.getPeriodNo()); //活动场次时间段编号
                    secKillGoodsDTO.setEffectiveTime(notSubGoods.getEffectiveTime());   //活动开始时间
                    secKillGoodsDTO.setInvalidTime(notSubGoods.getInvalidTime());   //活动结束时间
                    secKillGoodsDTO.setPromotionDate(notSubGoods.getPromotionDate());    //活动场次具体对应的日期
                    secKillGoodsDTO.setDailyStartTime(notSubGoods.getDailyStartTime()); //商品活动时间段开始时间
                    secKillGoodsDTO.setDailyEndTime(notSubGoods.getDailyEndTime()); //商品活动时间段结束时间
                    secKillGoodsDTO.setRemainStockNum(notSubGoods.getRemainStockNum()); //商品促销活动剩余库存
                    secKillGoodsDTO.setSourceType(notSubGoods.getSourceType()); //活动来源
                    secKillGoodsDTO.setPromotionType(notSubGoods.getPromotionType());   //活动类型
                    //如果是主品，则需要查询出子品的最小秒杀价
                    if (SeriesTypeEnum.PARENT_GOODS.getCode().equals(resGoodsDTO.getSeriesType())) {
                        if (CollectionUtils.isNotEmpty(subGoodsList)) {
                            // 找出对应主品以及对应活动下的子品集合
                            List<ResGoodsPromotionGoodsRelationDTO> currentParentSubList = subGoodsList.stream()
                                    .filter(subGoods -> subGoods.getParentGoodsNo().equals(notSubGoods.getGoodsNo())
                                            && subGoods.getPromotionNo().equals(notSubGoods.getPromotionNo())).collect(Collectors.toList());
                            // 计算对应主品下的所有子品中最大和最小的秒杀价格
                            if (CollectionUtils.isNotEmpty(currentParentSubList)) {
                                // 最小秒杀价格
                                Optional<ResGoodsPromotionGoodsRelationDTO> min = currentParentSubList.stream()
                                        .min(Comparator.comparing(ResGoodsPromotionGoodsRelationDTO::getPromotionPrice));
                                if (min.isPresent()) {
                                    ResGoodsPromotionGoodsRelationDTO goodsRelationDTO = min.get();
                                    secKillGoodsDTO.setMinPromotionPrice(goodsRelationDTO.getPromotionPrice());
                                    // 个人限购数取最小秒杀价格商品的个人限购数
                                    secKillGoodsDTO.setLimitBuyNum(goodsRelationDTO.getLimitBuyNum());
                                }
                                // 最大秒杀价格
                                Optional<ResGoodsPromotionGoodsRelationDTO> max = currentParentSubList.stream()
                                        .max(Comparator.comparing(ResGoodsPromotionGoodsRelationDTO::getPromotionPrice));
                                max.ifPresent(maxGoodsRelationDTO -> secKillGoodsDTO.setMaxPromotionPrice(maxGoodsRelationDTO.getPromotionPrice()));
                                // 所有子品中的活动剩余库存值汇总，用于页面展示主品的状态是不是已抢光
                                Integer remainStockTotalNum = 0;
                                for (ResGoodsPromotionGoodsRelationDTO goodsRelationDTO : currentParentSubList) {
                                    if (null != goodsRelationDTO && null != goodsRelationDTO.getRemainStockNum()) {
                                        remainStockTotalNum = remainStockTotalNum + goodsRelationDTO.getRemainStockNum();
                                    }
                                }
                                secKillGoodsDTO.setRemainStockNum(remainStockTotalNum); //商品促销活动剩余库存
                                // 计算酬劳
                                this.calculateReward(secKillGoodsDTO, currentParentSubList);
                            }
                        }
                    } else {
                        // 非系列商品
                        List<ResGoodsPromotionGoodsRelationDTO> goodsList = new ArrayList<>();
                        notSubGoods.setCloudPoolSupplyPrice(resGoodsDTO.getCloudPoolSupplyPrice());
                        goodsList.add(notSubGoods);
                        // 计算酬劳
                        this.calculateReward(secKillGoodsDTO, goodsList);
                    }

                    secKillGoodsDTOList.add(secKillGoodsDTO);
                    //秒杀商品等于5结束逻辑判断
                    if (secKillGoodsDTOList.size() > NumConstant.FIVE) {
                        return;
                    }
                }
            }
        }
    }

    /**
     * 计算酬劳
     *
     * @param secKillGoodsDTO
     * @param goodsList
     */
    private void calculateReward(ResSecKillGoodsDTO secKillGoodsDTO, List<ResGoodsPromotionGoodsRelationDTO> goodsList) {
        // 获取云池商品的主品佣金信息
        if (GoodsSourceTypeEnum.CLOUD_POOL_DISTRIBUTE.getCode().equals(secKillGoodsDTO.getGoodsSourceType())) {
            List<BigDecimal> calculateList = new ArrayList<>();
            for (ResGoodsPromotionGoodsRelationDTO goodsRelationDTO : goodsList) {
                // 活动费用
                BigDecimal activityExpenses = goodsRelationDTO.getActivityExpenses();
                // 秒杀价格
                BigDecimal promotionPrice = goodsRelationDTO.getPromotionPrice();
                // 供货价格
                BigDecimal cloudPoolSupplyPrice = goodsRelationDTO.getCloudPoolSupplyPrice();
                // 佣金比率
                BigDecimal agentCommissionRatio = goodsRelationDTO.getAgentCommissionRatio();
                if (null == activityExpenses || null == promotionPrice || null == cloudPoolSupplyPrice || null == agentCommissionRatio) {
                    continue;
                }
                // 计算佣金
                BigDecimal oneHundred = new BigDecimal(NumConstant.ONE_HUNDRED);
                // 按比例计算佣金 (活动费用+秒杀价格-供货价)*百分比
                BigDecimal calculate = BigDecimalUtil.setScale(agentCommissionRatio.divide(oneHundred)
                        .multiply(activityExpenses.add(promotionPrice).subtract(cloudPoolSupplyPrice)));
                calculateList.add(calculate);
            }
            // 求最大值
            if (CollectionUtils.isNotEmpty(calculateList)) {
                BigDecimal calculateMax = Collections.max(calculateList);
                log.info("******************云池商品最大佣金计算值：{}", calculateMax);
                calculateMax = BigDecimalUtil.setScale(calculateMax);
                secKillGoodsDTO.setReward("￥" + String.valueOf(calculateMax));
            }
        }

        // 分销商品
        if (NumConstant.TWO == secKillGoodsDTO.getDistributeGoodsFlag()) {
            // 返回的酬劳信息
            StringBuilder result = new StringBuilder();
            // 分销酬劳设置
            AtomReqAgentMarketRewardsetDTO atomReqAgentMarketRewardsetDTO = new AtomReqAgentMarketRewardsetDTO();
            // secKillGoodsDTO是非子品，所以直接取goodsNo
            atomReqAgentMarketRewardsetDTO.setTaskOrGoodsNo(secKillGoodsDTO.getGoodsNo());
            atomReqAgentMarketRewardsetDTO.setMarketType(MarketTypeEnum.StoreDistribution.getCode());
            ExecuteDTO<List<AtomResAgentMarketRewardsetDTO>> listExecuteDTO =
                    atomAgentMarketRewardsetAnalysisService.getList(atomReqAgentMarketRewardsetDTO);
            if (null == listExecuteDTO || !listExecuteDTO.successFlag()) {
                throw new BaseException(CommonCode.CODE_10000003);
            }
            // 取最大的秒杀价格
            BigDecimal maxPromotionPrice = null;
            Optional<ResGoodsPromotionGoodsRelationDTO> realSalePriceOptional = goodsList.stream()
                    .max(Comparator.comparing(ResGoodsPromotionGoodsRelationDTO::getPromotionPrice));
            if (realSalePriceOptional.isPresent()) {
                maxPromotionPrice = realSalePriceOptional.get().getPromotionPrice();
            }
            List<AtomResAgentMarketRewardsetDTO> rewardLists = listExecuteDTO.getData();
            if (CollectionUtils.isNotEmpty(rewardLists)) {
                AtomResAgentMarketRewardsetDTO atomResAgentMarketRewardsetDTO = rewardLists.get(0);
                if (null != atomResAgentMarketRewardsetDTO) {
                    // 佣金类型
                    switch (atomResAgentMarketRewardsetDTO.getRewardType()) {
                        case NumConstant.ONE:
                            // 佣金
                            BigDecimal oneHundred = new BigDecimal(NumConstant.ONE_HUNDRED);
                            if (null != atomResAgentMarketRewardsetDTO.getYjOrHjb() && maxPromotionPrice != null) {
                                // 按比例计算佣金
                                BigDecimal calculateResult = BigDecimalUtil.setScale(atomResAgentMarketRewardsetDTO.getYjOrHjb().divide(oneHundred)
                                        .multiply(maxPromotionPrice));
                                // 四舍五入保留2位小数
                                calculateResult = BigDecimalUtil.setScale(calculateResult);
                                result.append("￥").append(String.valueOf(calculateResult));
                            } else {
                                log.info("*********佣金接口返回空值，请后台查看问题*********************");
                                result.append("");
                            }
                            break;
                        case NumConstant.TWO:
                            if (null != atomResAgentMarketRewardsetDTO.getYjOrHjb()) {
                                // 汇金币
                                result.append("汇金币").append(String.valueOf(atomResAgentMarketRewardsetDTO.getYjOrHjb())).append("个");
                            } else {
                                log.info("*********汇金币接口返回空值，请后台查看问题*********************");
                                result.append("");
                            }
                            break;
                        case NumConstant.THREE:
                            // 礼品
                            result.append(atomResAgentMarketRewardsetDTO.getRelatedName());
                            break;
                        case NumConstant.FOUR:
                            // 现金券
                            result.append("现金券(满￥").append(atomResAgentMarketRewardsetDTO.getRelatedUp().intValue()).append("减")
                                    .append(atomResAgentMarketRewardsetDTO.getRelatedLess().intValue()).append(")");
                            break;
                        case NumConstant.FIVE:
                            // 服务劵
                            result.append(atomResAgentMarketRewardsetDTO.getRelatedName());
                            break;
                        default:
                            log.info("*********酬劳设置未匹配到类型，请后台查看问题*********************");
                            result.append("");
                    }
                }
            }
            if (StringUtils.isNotBlank(result.toString())) {
                secKillGoodsDTO.setReward(result.toString());
            }
        }
    }

    /**
     * 查询子品信息，用于计算酬劳
     *
     * @param reqDTO
     * @param subGoodsList
     * @param reqGoodsDTO
     * <AUTHOR>
     * @date 2021-07-27
     */
    private void getSubGoodsInfo(ReqPromotionInfoDTO reqDTO, List<ResGoodsPromotionGoodsRelationDTO> subGoodsList, ReqGoodsDTO reqGoodsDTO) {
        // 店铺
        if (SourceTypeEnum.SOURCE_TYPE_1003.getCode().equals(reqDTO.getSourceType())) {
            reqGoodsDTO.setGoodsNos(subGoodsList.stream().map(ResGoodsPromotionGoodsRelationDTO::getGoodsNo).collect(Collectors.toList()));
        } else {    // 云池
            reqGoodsDTO.setCloudPoolGoodsNos(subGoodsList.stream().map(ResGoodsPromotionGoodsRelationDTO::getGoodsNo).collect(Collectors.toList()));
        }
        ExecuteDTO<ExecutePageDTO<ResGoodsDTO>> subGoodsExecuteDTO = goodsAnalysisService.getGoodsPage(reqGoodsDTO);
        log.info("查询子品的商品信息结果出参：{}", subGoodsExecuteDTO);
        if (subGoodsExecuteDTO == null || !subGoodsExecuteDTO.successFlag()) {
            throw new BaseException(CommonCode.CODE_10000003);
        }
        // 取出子品相关信息
        if (subGoodsExecuteDTO.getData() != null && CollectionUtils.isNotEmpty(subGoodsExecuteDTO.getData().getRows())) {
            subGoodsList.forEach(subGoods -> {
                //如果是云池商品则拿查询出来的cloudPoolGoodsNo与goodsNo比较，反正则直接用goodsno
                Optional<ResGoodsDTO> optional = subGoodsExecuteDTO.getData().getRows().stream()
                        .filter(goods -> subGoods.getGoodsNo().equals(goods.getGoodsNo()) || subGoods.getGoodsNo().equals(goods.getCloudPoolGoodsNo()))
                        .findFirst();
                if (optional.isPresent()) {
                    ResGoodsDTO goodsDTO = optional.get();
                    // 取零售价格，云池供货价，用于计算佣金
                    subGoods.setRetailPrice(goodsDTO.getRetailPrice()); //子品的零售价
                    subGoods.setCloudPoolSupplyPrice(goodsDTO.getCloudPoolSupplyPrice());   //子品的云池供货价
                }
            });
        }
    }

    /**
     * 平台查询平台促销活动的参与商品信息
     *
     * <AUTHOR>
     * @date 2021-07-06
     */
    @Override
    public ExecuteDTO<List<ResGoodsPromotionGoodsRelationDTO>> platformGetPromotionGoods(ReqPromotionInfoDTO reqDTO) {
        log.info("**GoodsPromotionGoodsRelationAnalysisServiceImpl.platformGetPromotionGoods-查询平台活动的参与商品信息-start**");
        log.info("**GoodsPromotionGoodsRelationAnalysisServiceImpl.platformGetPromotionGoods-入参：{}", reqDTO);
        // 入参校验
        goodsPromotionAssert.getPromotionGoodsAssert(reqDTO);

        // 根据活动编号查询该平台活动参与的所有商品数据
        AtomReqGoodsPromotionGoodsRelationDTO reqPromotionGoods = new AtomReqGoodsPromotionGoodsRelationDTO();
        List<String> promotionNoList = new ArrayList<>();
        promotionNoList.add(reqDTO.getPromotionNo());
        reqPromotionGoods.setPromotionNoList(promotionNoList);
        // 排除主品
        reqPromotionGoods.setChildFlag(WhetherEnum.YES.getCode());
        ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> executeDTO = atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList(reqPromotionGoods);
        if (null == executeDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        List<AtomResGoodsPromotionGoodsRelationDTO> promotionGoodsList = executeDTO.getData();
        if (CollectionUtils.isEmpty(promotionGoodsList)) {
            return ExecuteDTO.success();
        }

        // 出参转换
        List<ResGoodsPromotionGoodsRelationDTO> resultList = BeanCopierUtil.copyList(promotionGoodsList, ResGoodsPromotionGoodsRelationDTO.class);

        // 从查询出来平台促销活动商品中提取出商品编码，用于查询这些商品的基础信息
        List<String> goodsNoList = promotionGoodsList.stream().filter(goods -> StringUtils.isNotBlank(goods.getGoodsNo()))
                .map(AtomResGoodsPromotionGoodsRelationDTO::getGoodsNo).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(goodsNoList)) {
            return ExecuteDTO.success(resultList);
        }
        ReqCloudPoolApplyComPageDTO cloudPoolApplyComPageDTO = new ReqCloudPoolApplyComPageDTO();
        cloudPoolApplyComPageDTO.setNoPage();
        cloudPoolApplyComPageDTO.setGoodsNos(goodsNoList);
        // 设置促销活动查询的标识
        cloudPoolApplyComPageDTO.setGoodsPromotionFlag(WhetherEnum.YES.getCode());
        ExecuteDTO<ExecutePageDTO<ResGoodsDTO>> cloudPoolGoodsList = cloudPoolApplyAnalysisService.getCloudPoolGoodsList(cloudPoolApplyComPageDTO);
        if (null == cloudPoolGoodsList) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!cloudPoolGoodsList.successFlag()) {
            return ExecuteDTO.error(cloudPoolGoodsList.getStatus(), cloudPoolGoodsList.getMsg());
        }

        // 活动参与的商品信息匹配
        if (null != cloudPoolGoodsList.getData() && CollectionUtils.isNotEmpty(cloudPoolGoodsList.getData().getRows())) {
            BigDecimal oneHundred = new BigDecimal(NumConstant.ONE_HUNDRED);    //计算佣金的公共参数
            for (ResGoodsPromotionGoodsRelationDTO goodsRelationDTO : resultList) {
                Optional<ResGoodsDTO> goodsDTOOptional = cloudPoolGoodsList.getData().getRows().stream()
                        .filter(resGoodsDTO -> StringUtils.equals(goodsRelationDTO.getGoodsNo(), resGoodsDTO.getGoodsNo()))
                        .findFirst();
                if (goodsDTOOptional.isPresent()) {
                    ResGoodsDTO goodsDTO = goodsDTOOptional.get();
                    // 商品形式
                    goodsRelationDTO.setGoodsForm(goodsDTO.getGoodsForm());
                    // 商品名称
                    goodsRelationDTO.setGoodsName(goodsDTO.getGoodsName());
                    // 规格属性
                    StringBuilder attributeNames = new StringBuilder();
                    if (StringUtils.isNotBlank(goodsDTO.getFirstAttributeValueName())) {
                        attributeNames.append(goodsDTO.getFirstAttributeValueName());
                    }
                    if (StringUtils.isNotBlank(goodsDTO.getSecondAttributeValueName())) {
                        if (StringUtils.isNotBlank(attributeNames.toString())) {
                            attributeNames.append("-");
                        }
                        attributeNames.append(goodsDTO.getSecondAttributeValueName());
                    }
                    if (StringUtils.isNotBlank(goodsDTO.getThirdAttributeValueName())) {
                        if (StringUtils.isNotBlank(attributeNames.toString())) {
                            attributeNames.append("-");
                        }
                        attributeNames.append(goodsDTO.getThirdAttributeValueName());
                    }
                    if (StringUtils.isNotBlank(attributeNames)) {

                        goodsRelationDTO.setAttributeNames(attributeNames.toString());
                    }
                    // 供货价
                    goodsRelationDTO.setCloudPoolSupplyPrice(goodsDTO.getCloudPoolSupplyPrice());
                    // 云池售价
                    goodsRelationDTO.setCloudPoolRetailPrice(goodsDTO.getCloudPoolRetailPrice());
                    // 云池可售库存
                    goodsRelationDTO.setCanSaleStockNum(goodsDTO.getCanSaleStockNum());
                    // 计算佣金的值：活动费用+秒杀价格-供货价
                    BigDecimal calculate = goodsRelationDTO.getActivityExpenses().add(goodsRelationDTO.getPromotionPrice())
                            .subtract(goodsRelationDTO.getCloudPoolSupplyPrice());
                    // 代理人佣金：（活动费用+秒杀价格-供货价）*百分比
                    BigDecimal agentCommission = BigDecimalUtil.setScale(
                            goodsRelationDTO.getAgentCommissionRatio().divide(oneHundred).multiply(calculate)
                    );
                    goodsRelationDTO.setAgentCommission(agentCommission);
                    // 分销店佣金：（活动费用+秒杀价格-供货价）*百分比
                    BigDecimal distributionStoreCommission = BigDecimalUtil.setScale(
                            goodsRelationDTO.getDistributionStoreCommissionRatio().divide(oneHundred).multiply(calculate)
                    );
                    goodsRelationDTO.setDistributionStoreCommission(distributionStoreCommission);
                    // 平台佣金比例*（秒杀价+活动费用-供货价）
                    BigDecimal platformServiceCommission = BigDecimalUtil.setScale(
                            goodsRelationDTO.getPlatformServiceCommissionRatio().divide(oneHundred).multiply(calculate));
                    goodsRelationDTO.setPlatformServiceCommission(platformServiceCommission);
                    // 平台净利润：云池秒杀价-云池供货价-代理人佣金-分销店佣金-平台服务费
                    goodsRelationDTO.setGrossProfit(BigDecimalUtil.setScale(
                            goodsRelationDTO.getPromotionPrice()
                                    .subtract(goodsRelationDTO.getCloudPoolSupplyPrice())
                                    .subtract(goodsRelationDTO.getAgentCommission())
                                    .subtract(goodsRelationDTO.getDistributionStoreCommission())
                                    .subtract(goodsRelationDTO.getPlatformServiceCommission())
                            )
                    );
                    // 店铺编号
                    goodsRelationDTO.setStoreNo(goodsDTO.getStoreNo());
                    // 店铺名称
                    goodsRelationDTO.setStoreName(goodsDTO.getStoreName());
                }
            }
        }

        log.info("**GoodsPromotionGoodsRelationAnalysisServiceImpl.platformGetPromotionGoods-查询平台活动的参与商品信息-end**");

        return ExecuteDTO.success(resultList);
    }

    /**
     * 查询店铺促销活动的参与商品信息
     *
     * <AUTHOR>
     * @date 2021-07-08
     */
    @Override
    public ExecuteDTO<List<ResGoodsPromotionGoodsRelationDTO>> getStorePromotionGoods(ReqPromotionInfoDTO reqDTO) {
        log.info("**GoodsPromotionGoodsRelationAnalysisServiceImpl-getStorePromotionGoods-查询店铺活动参与的商品信息-start");
        log.info("**GoodsPromotionGoodsRelationAnalysisServiceImpl-getStorePromotionGoods-入参：{}", reqDTO);
        // 入参校验
        goodsPromotionAssert.getPromotionGoodsAssert(reqDTO);

        // 根据活动编号查询该店铺活动参与的所有商品数据
        AtomReqGoodsPromotionGoodsRelationDTO reqPromotionGoods = new AtomReqGoodsPromotionGoodsRelationDTO();
        reqPromotionGoods.setPromotionNo(reqDTO.getPromotionNo());
        //促销活动查询时，会传活动场次编号，用于查询对应活动场次的商品数据
        reqPromotionGoods.setPeriodNo(reqDTO.getPeriodNo());
        // 排除主品
        reqPromotionGoods.setChildFlag(WhetherEnum.YES.getCode());
        ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> executeDTO = atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList(reqPromotionGoods);
        if (null == executeDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        List<AtomResGoodsPromotionGoodsRelationDTO> promotionGoodsList = executeDTO.getData();
        if (CollectionUtils.isEmpty(promotionGoodsList)) {
            return ExecuteDTO.success();
        }

        // 校验商品是否是互斥商品，先查询出和当前活动场次互斥的商品
        List<String> noChoiceGoodsNos = null;
        if (WhetherEnum.YES.getCode().equals(reqDTO.getCheckGoodsFlag())) {
            // 首先根据活动编号和时间段编号查询活动场次信息
            AtomReqGoodsPromotionPeriodDTO reqPromotionPeriodDTO = new AtomReqGoodsPromotionPeriodDTO();
            reqPromotionPeriodDTO.setPromotionNo(reqDTO.getPromotionNo());
            reqPromotionPeriodDTO.setPeriodNo(reqDTO.getPeriodNo());
            ExecuteDTO<AtomResGoodsPromotionPeriodDTO> promotionPeriodDTOExecuteDTO =
                    atomGoodsPromotionPeriodAnalysisService.selectPromotionPeriodInfo(reqPromotionPeriodDTO);
            if (null == promotionPeriodDTOExecuteDTO) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            if (!promotionPeriodDTOExecuteDTO.successFlag()) {
                return ExecuteDTO.error(promotionPeriodDTOExecuteDTO.getStatus(), promotionPeriodDTOExecuteDTO.getMsg());
            }
            // 查询互斥的商品
            // 先查询出和当前活动存在互斥的商品
            AtomReqGoodsPromotionGoodsRelationDTO reqGoodsDTO = BeanCopierUtil.copy(promotionPeriodDTOExecuteDTO.getData()
                    , AtomReqGoodsPromotionGoodsRelationDTO.class);
            reqGoodsDTO.setStoreNo(reqDTO.getStoreNo());
            reqGoodsDTO.setQueryType(NumConstant.TWO);
            ExecuteDTO<List<String>> noChoiceGoodsNoList =
                    atomGoodsPromotionGoodsRelationAnalysisService.getNoChoiceGoodsNoList(reqGoodsDTO);
            if (null == noChoiceGoodsNoList) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            if (!noChoiceGoodsNoList.successFlag()) {
                return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
            }
            noChoiceGoodsNos = noChoiceGoodsNoList.getData();
        }

        // 出参转换
        List<ResGoodsPromotionGoodsRelationDTO> resultList = BeanCopierUtil.copyList(promotionGoodsList, ResGoodsPromotionGoodsRelationDTO.class);

        // 从查询出来店铺促销活动商品中提取出商品编码，用于查询这些商品的基础信息
        List<String> goodsNoList = promotionGoodsList.stream().filter(goods -> StringUtils.isNotBlank(goods.getGoodsNo()))
                .map(AtomResGoodsPromotionGoodsRelationDTO::getGoodsNo).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(goodsNoList)) {
            return ExecuteDTO.success(resultList);
        }
        // 查询商品基础信息
        ReqGoodsDTO reqGoodsDTO = new ReqGoodsDTO();
        // 设置要查询的商品
        reqGoodsDTO.setGoodsNos(goodsNoList);
        // 不分页查询
        reqGoodsDTO.setNoPage();
        ExecuteDTO<ExecutePageDTO<ResGoodsDTO>> goodsPage = goodsAnalysisService.getGoodsPage(reqGoodsDTO);
        if (null == goodsPage) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!goodsPage.successFlag()) {
            return ExecuteDTO.error(goodsPage.getStatus(), goodsPage.getMsg());
        }

        // 查询分销酬劳酬劳类型数据
        // 活动设置的商品，设置的是非主品，查询店铺分销商品的酬劳信息用parentGoodsNo来查，普通商品的goodsNo和parentGoodsNo一样
        List<String> parentGoodsNoList = promotionGoodsList.stream().filter(goods -> StringUtils.isNotBlank(goods.getParentGoodsNo()))
                .map(AtomResGoodsPromotionGoodsRelationDTO::getParentGoodsNo).collect(Collectors.toList());
        ExecuteDTO<List<AtomResAgentMarketRewardsetDTO>> rewardList = atomAgentMarketRewardsetAnalysisService.batchGetRewardSetByTaskNos(parentGoodsNoList);
        if (null == rewardList) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!rewardList.successFlag()) {
            return ExecuteDTO.error(rewardList.getStatus(), rewardList.getMsg());
        }

        // 活动参与的商品信息以及分销信息匹配
        if ((null != goodsPage.getData() && CollectionUtils.isNotEmpty(goodsPage.getData().getRows())) ||
                CollectionUtils.isNotEmpty(rewardList.getData())) {
            for (ResGoodsPromotionGoodsRelationDTO goodsRelationDTO : resultList) {
                // 是否分销商品标识
                Integer distributeGoodsFlag = WhetherEnum.NO.getCode();
                // 商品信息匹配
                if (null != goodsPage.getData() && CollectionUtils.isNotEmpty(goodsPage.getData().getRows())) {
                    Optional<ResGoodsDTO> goodsDTOOptional = goodsPage.getData().getRows().stream()
                            .filter(resGoodsDTO -> StringUtils.equals(goodsRelationDTO.getGoodsNo(), resGoodsDTO.getGoodsNo()))
                            .findFirst();
                    if (goodsDTOOptional.isPresent()) {
                        ResGoodsDTO goodsDTO = goodsDTOOptional.get();
                        distributeGoodsFlag = goodsDTO.getDistributeGoodsFlag();
                        // 商品形式
                        goodsRelationDTO.setGoodsForm(goodsDTO.getGoodsForm());
                        // 商品名称
                        goodsRelationDTO.setGoodsName(goodsDTO.getGoodsName());
                        // 规格属性
                        StringBuilder attributeNames = new StringBuilder();
                        if (StringUtils.isNotBlank(goodsDTO.getFirstAttributeValueName())) {
                            attributeNames.append(goodsDTO.getFirstAttributeValueName());
                        }
                        if (StringUtils.isNotBlank(goodsDTO.getSecondAttributeValueName())) {
                            if (StringUtils.isNotBlank(attributeNames.toString())) {
                                attributeNames.append("-");
                            }
                            attributeNames.append(goodsDTO.getSecondAttributeValueName());
                        }
                        if (StringUtils.isNotBlank(goodsDTO.getThirdAttributeValueName())) {
                            if (StringUtils.isNotBlank(attributeNames.toString())) {
                                attributeNames.append("-");
                            }
                            attributeNames.append(goodsDTO.getThirdAttributeValueName());
                        }
                        if (StringUtils.isNotBlank(attributeNames)) {
                            goodsRelationDTO.setAttributeNames(attributeNames.toString());
                        }
                        // 零售价
                        goodsRelationDTO.setRetailPrice(goodsDTO.getRetailPrice());
                        // 可售库存
                        goodsRelationDTO.setCanSaleStockNum(goodsDTO.getCanSaleStockNum());
                        // 店铺编号
                        goodsRelationDTO.setStoreNo(goodsDTO.getStoreNo());
                        // 店铺名称
                        goodsRelationDTO.setStoreName(goodsDTO.getStoreName());
                        // 商品展示状态
                        goodsRelationDTO.setGoodsShowStatusValue(goodsDTO.getGoodsShowStatusValue());
                        if (StringUtils.isNotBlank(goodsDTO.getMainPictureUrl())) {
                            goodsRelationDTO.setMainPictureUrl(goodsDTO.getMainPictureUrl());
                        } else {
                            goodsRelationDTO.setMainPictureUrl(this.getPictureUrl(new ReqUcDisplayConfigurationDTO(IdentityEnum.MERCHANT.getCode(),goodsDTO.getMerchantNo())));
                        }
                        // 20230928蛋品 lixiang  商品管理 多单位商品
                        goodsRelationDTO.setMultiUnitType(goodsDTO.getMultiUnitType());
                        goodsRelationDTO.setMultiUnitGoodsNo(goodsDTO.getMultiUnitGoodsNo());
                        goodsRelationDTO.setCalculationUnitNo(goodsDTO.getCalculationUnitNo());
                        goodsRelationDTO.setCalculationUnitName(goodsDTO.getCalculationUnitName());
                    }
                }
                // 商品是分销商品时，进行分销信息匹配
                if (CollectionUtils.isNotEmpty(rewardList.getData()) && WhetherEnum.YES.getCode().equals(distributeGoodsFlag)) {
                    Optional<AtomResAgentMarketRewardsetDTO> rewardOptional = rewardList.getData().stream()
                            .filter(rewardDTO -> StringUtils.equals(goodsRelationDTO.getParentGoodsNo(), rewardDTO.getTaskOrGoodsNo()))
                            .findFirst();
                    if (rewardOptional.isPresent()) {
                        AtomResAgentMarketRewardsetDTO resRewardSetDTO = rewardOptional.get();
                        // 酬劳类型
                        goodsRelationDTO.setRewardType(resRewardSetDTO.getRewardType());
                        // 分销酬劳
                        StringBuilder reward = new StringBuilder();
                        Integer rewardType = resRewardSetDTO.getRewardType();
                        // 酬劳类型
                        if (RewardTypeEnum.getByCode(rewardType) != null) {
                            goodsRelationDTO.setRewardTypeValue(RewardTypeEnum.getByCode(rewardType).getType());
                        }
                        switch (rewardType) {
                            case NumConstant.ONE:
                                // 佣金
                                BigDecimal oneHundred = new BigDecimal(NumConstant.ONE_HUNDRED);
                                if (null != resRewardSetDTO.getYjOrHjb()) {
                                    // 活动价格
                                    BigDecimal promotionPrice = goodsRelationDTO.getPromotionPrice();
                                    if (null == promotionPrice) {
                                        promotionPrice = BigDecimal.valueOf(0);
                                    }
                                    BigDecimal yjOrHjb = resRewardSetDTO.getYjOrHjb();
                                    goodsRelationDTO.setYjPercent(BigDecimalUtil.setScale(yjOrHjb));
                                    // 按比例计算佣金
                                    BigDecimal calculateResult = yjOrHjb.divide(oneHundred)
                                            .multiply(promotionPrice);
                                    // 四舍五入保留2位小数
                                    calculateResult = BigDecimalUtil.setScale(calculateResult);
                                    reward.append("￥").append(calculateResult);
                                } else {
                                    log.info("*********佣金接口返回空值，请后台查看问题*********************");
                                    reward.append("");
                                }
                                break;
                            case NumConstant.TWO:
                                if (null != resRewardSetDTO.getYjOrHjb()) {
                                    // 汇金币
                                    reward.append("汇金币").append(resRewardSetDTO.getYjOrHjb()).append("个");
                                } else {
                                    log.info("*********汇金币接口返回空值，请后台查看问题*********************");
                                    reward.append("");
                                }
                                break;
                            case NumConstant.THREE:
                                // 礼品
                                reward.append(resRewardSetDTO.getRelatedName());
                                break;
                            case NumConstant.FOUR:
                                // 现金券
                                reward.append("现金券(满￥").append(resRewardSetDTO.getRelatedUp().intValue()).append("减")
                                        .append(resRewardSetDTO.getRelatedLess().intValue()).append(")");
                                break;
                            case NumConstant.FIVE:
                                // 服务劵
                                reward.append(resRewardSetDTO.getRelatedName());
                                break;
                            default:
                                log.info("*********酬劳设置未匹配到类型，请后台查看问题*********************");
                                reward.append("");
                        }
                        goodsRelationDTO.setReward(reward.toString());
                    }
                }
                // 校验是否为互斥商品
                if (CollectionUtils.isNotEmpty(noChoiceGoodsNos)) {
                    if (noChoiceGoodsNos.contains(goodsRelationDTO.getGoodsNo())) {
                        goodsRelationDTO.setUnAvailableFlag(WhetherEnum.YES.getCode());
                    } else {
                        goodsRelationDTO.setUnAvailableFlag(WhetherEnum.NO.getCode());
                    }
                } else {
                    goodsRelationDTO.setUnAvailableFlag(WhetherEnum.NO.getCode());
                }
            }
        }

        log.info("**GoodsPromotionGoodsRelationAnalysisServiceImpl-getStorePromotionGoods-查询店铺活动参与的商品信息-end");

        // 返回结果
        return ExecuteDTO.success(resultList);
    }

    /**
     * 查询要添加的云池商品列表用于平台活动设置参与商品
     *
     * <AUTHOR>
     * @date 2021-07-07
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResGoodsPromotionGoodsRelationDTO>> getCloudGoodsForPromotion(ReqPromotionInfoDTO reqDTO) {
        log.info("**GoodsPromotionGoodsRelationAnalysisServiceImpl-getCloudGoodsForPromotion-查询要添加的云池商品列表-start");
        log.info("**GoodsPromotionGoodsRelationAnalysisServiceImpl-getCloudGoodsForPromotion-入参：{}", reqDTO);
        // 入参校验
        goodsPromotionAssert.getCloudGoodsForPromotionAssert(reqDTO);

        // 首先根据活动编号和时间段编号查询活动场次信息
        /* 去除促销活动商品互斥校验逻辑
        AtomReqGoodsPromotionPeriodDTO reqPromotionPeriodDTO = BeanCopierUtil.copy(reqDTO, AtomReqGoodsPromotionPeriodDTO.class);
        ExecuteDTO<AtomResGoodsPromotionPeriodDTO> promotionPeriodDTOExecuteDTO = atomGoodsPromotionPeriodAnalysisService.selectPromotionPeriodInfo(reqPromotionPeriodDTO);
        log.info("**查询活动场次信息出参：{}", promotionPeriodDTOExecuteDTO);
        if (null == promotionPeriodDTOExecuteDTO || !promotionPeriodDTOExecuteDTO.successFlag()) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        AtomResGoodsPromotionPeriodDTO promotionPeriodDTO = promotionPeriodDTOExecuteDTO.getData();
        if (null == promotionPeriodDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000002.getCode(), "没有找到活动信息");
        }

        // 查询互斥不可选择的商品编号（当前活动的当前时间段已经选择的商品 + 其他过期活动或者未过期但场次重合的商品）
        AtomReqGoodsPromotionGoodsRelationDTO reqGoodsDTO = BeanCopierUtil.copy(promotionPeriodDTO, AtomReqGoodsPromotionGoodsRelationDTO.class);
        reqGoodsDTO.setSourceType(reqDTO.getSourceType());
        // 添加弹窗，互斥的商品包括当前活动选择或者其他活动有时间重合的选择
        reqGoodsDTO.setQueryType(NumConstant.ONE);
        ExecuteDTO<List<String>> noChoiceGoodsNoList = atomGoodsPromotionGoodsRelationAnalysisService.getNoChoiceGoodsNoList(reqGoodsDTO);
        if (null == noChoiceGoodsNoList) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!noChoiceGoodsNoList.successFlag()) {
            return ExecuteDTO.error(noChoiceGoodsNoList.getStatus(), noChoiceGoodsNoList.getMsg());
        }
        */
        // 分页查询要添加的商品列表
        ReqCloudPoolApplyComPageDTO cloudPoolApplyComPageDTO = BeanCopierUtil.copy(reqDTO, ReqCloudPoolApplyComPageDTO.class);
        // if (CollectionUtils.isNotEmpty(noChoiceGoodsNoList.getData())) {
        //     cloudPoolApplyComPageDTO.setNotExistGoods(noChoiceGoodsNoList.getData());
        // }
        // 设置促销活动查询的标识
        cloudPoolApplyComPageDTO.setGoodsPromotionFlag(WhetherEnum.YES.getCode());
        // 设置只查询有库存的商品
        // cloudPoolApplyComPageDTO.setStockDisableFlag(WhetherEnum.NO.getCode());
        ExecuteDTO<ExecutePageDTO<ResGoodsDTO>> cloudPoolGoodsList = cloudPoolApplyAnalysisService.getCloudPoolGoodsList(cloudPoolApplyComPageDTO);
        if (null == cloudPoolGoodsList) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!cloudPoolGoodsList.successFlag()) {
            return ExecuteDTO.error(cloudPoolGoodsList.getStatus(), cloudPoolGoodsList.getMsg());
        }

        // 出参转换
        ExecutePageDTO<ResGoodsPromotionGoodsRelationDTO> executePageDTO = new ExecutePageDTO<>();
        if (null != cloudPoolGoodsList.getData()) {
            List<ResGoodsPromotionGoodsRelationDTO> list = BeanCopierUtil.copyList(cloudPoolGoodsList.getData().getRows(), ResGoodsPromotionGoodsRelationDTO.class);
            // 数据格式化
            if (CollectionUtils.isNotEmpty(list)) {
                for (ResGoodsPromotionGoodsRelationDTO goodsRelationDTO : list) {
                    // 规格属性
                    StringBuilder attributeNames = new StringBuilder();
                    if (StringUtils.isNotBlank(goodsRelationDTO.getFirstAttributeValueName())) {
                        attributeNames.append(goodsRelationDTO.getFirstAttributeValueName());
                    }
                    if (StringUtils.isNotBlank(goodsRelationDTO.getSecondAttributeValueName())) {
                        if (StringUtils.isNotBlank(attributeNames.toString())) {
                            attributeNames.append("-");
                        }
                        attributeNames.append(goodsRelationDTO.getSecondAttributeValueName());
                    }
                    if (StringUtils.isNotBlank(goodsRelationDTO.getThirdAttributeValueName())) {
                        if (StringUtils.isNotBlank(attributeNames.toString())) {
                            attributeNames.append("-");
                        }
                        attributeNames.append(goodsRelationDTO.getThirdAttributeValueName());
                    }
                    if (StringUtils.isNotBlank(attributeNames)) {
                        goodsRelationDTO.setAttributeNames(attributeNames.toString());
                    }
                    // 子品
                    if (SeriesTypeEnum.SUB_GOODS.getCode().equals(goodsRelationDTO.getSeriesType())) {
                        goodsRelationDTO.setSubGoodsFlag(WhetherEnum.YES.getCode());
                    } else {
                        // 普通商品
                        goodsRelationDTO.setSubGoodsFlag(WhetherEnum.NO.getCode());
                        goodsRelationDTO.setParentGoodsNo(goodsRelationDTO.getGoodsNo());
                    }
                }
            }
            executePageDTO.setRows(list);
            executePageDTO.setTotal(cloudPoolGoodsList.getData().getTotal());
        }

        log.info("**GoodsPromotionGoodsRelationAnalysisServiceImpl-getCloudGoodsForPromotion-查询要添加的云池商品列表-end");

        return ExecuteDTO.success(executePageDTO);
    }

    /**
     * 查询要添加的店铺自建+商家分发的商品列表数据，用于店铺活动设置参与商品
     *
     * <AUTHOR>
     * @date 2021-07-08
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResGoodsPromotionGoodsRelationDTO>> getStoreGoodsForPromotion(ReqPromotionInfoDTO reqDTO) {
        log.info("**GoodsPromotionGoodsRelationAnalysisServiceImpl-getStoreGoodsForPromotion-查询店铺活动可以参与的商品-start");
        log.info("**GoodsPromotionGoodsRelationAnalysisServiceImpl-getStoreGoodsForPromotion-入参：{}", reqDTO);
        // 入参校验
        goodsPromotionAssert.getStoreGoodsForPromotionAssert(reqDTO);

        // 分页查询要添加的商品信息
        ReqGoodsDTO goodsDTO = BeanCopierUtil.copy(reqDTO, ReqGoodsDTO.class);

        if (StringUtils.isNotBlank(reqDTO.getPromotionNo())) {
            // 首先根据活动编号和时间段编号查询活动场次信息
            AtomReqGoodsPromotionPeriodDTO reqPromotionPeriodDTO = BeanCopierUtil.copy(reqDTO, AtomReqGoodsPromotionPeriodDTO.class);
            ExecuteDTO<AtomResGoodsPromotionPeriodDTO> promotionPeriodDTOExecuteDTO = atomGoodsPromotionPeriodAnalysisService.selectPromotionPeriodInfo(reqPromotionPeriodDTO);
            log.info("**查询活动场次信息出参：{}", promotionPeriodDTOExecuteDTO);
            if (null == promotionPeriodDTOExecuteDTO || !promotionPeriodDTOExecuteDTO.successFlag()) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            AtomResGoodsPromotionPeriodDTO promotionPeriodDTO = promotionPeriodDTOExecuteDTO.getData();
            if (null == promotionPeriodDTO) {
                return ExecuteDTO.error(CommonCode.CODE_10000002.getCode(), "没有找到活动信息");
            }

            // 查询互斥不可选择的商品编号（当前活动的当前时间段已经选择的商品 + 其他过期活动或者未过期但场次不重合的商品）
            /*
            AtomReqGoodsPromotionGoodsRelationDTO reqGoodsDTO = BeanCopierUtil.copy(promotionPeriodDTO, AtomReqGoodsPromotionGoodsRelationDTO.class);
            reqGoodsDTO.setSourceType(reqDTO.getSourceType());
            reqGoodsDTO.setStoreNo(reqDTO.getStoreNo());
            // 添加弹窗，互斥的商品包括当前活动选择或者其他活动有时间重合的选择
            reqGoodsDTO.setQueryType(NumConstant.ONE);
            ExecuteDTO<List<String>> noChoiceGoodsNoList = atomGoodsPromotionGoodsRelationAnalysisService.getNoChoiceGoodsNoList(reqGoodsDTO);
            if (null == noChoiceGoodsNoList) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            if (!noChoiceGoodsNoList.successFlag()) {
                return ExecuteDTO.error(noChoiceGoodsNoList.getStatus(), noChoiceGoodsNoList.getMsg());
            }
            // 设置过滤掉的商品
            if (CollectionUtils.isNotEmpty(noChoiceGoodsNoList.getData())) {
                goodsDTO.setNotExistGoods(noChoiceGoodsNoList.getData());
            }
             */
            // 商品不再做活动互斥，因此此处仅需要排除当前活动阶段已选商品即可
            AtomReqGoodsPromotionGoodsRelationDTO relationDTO = new AtomReqGoodsPromotionGoodsRelationDTO();
            relationDTO.setPromotionNo(reqDTO.getPromotionNo());
            if (StringUtils.isNotBlank(reqDTO.getPeriodNo())) {
                relationDTO.setPeriodNo(reqDTO.getPeriodNo());
            }
            ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> promotionPeriodGoodsListEO = atomGoodsPromotionGoodsRelationAnalysisService.getPromotionPeriodGoodsList(relationDTO);
            if (promotionPeriodGoodsListEO != null && promotionPeriodGoodsListEO.successFlag() && CollectionUtils.isNotEmpty(promotionPeriodGoodsListEO.getData())) {
                List<String> noChoiceGoodsNos = new ArrayList<>();
                promotionPeriodGoodsListEO.getData().forEach(promotionGoodsRelationDTO -> {
                    // parentGoodsNo 非空的数据表示 是 非主品的数据，即活动设置的商品数据
                    if (StringUtils.isNotBlank(promotionGoodsRelationDTO.getParentGoodsNo())) {
                        noChoiceGoodsNos.add(promotionGoodsRelationDTO.getGoodsNo());
                    }
                });
                if (CollectionUtils.isNotEmpty(noChoiceGoodsNos)) {
                    goodsDTO.setNotExistGoods(noChoiceGoodsNos);
                }
            }
        }

        // 设置商品审核状态为通过-2003
        goodsDTO.setAuditStatus(GoodsAuditStatusEnum.AUDIT_SUCCESS.getCode());
        // 设置排除主品
        goodsDTO.setChildFlag(SeriesTypeEnum.SUB_GOODS.getCode());
        // 设置店铺身份查询列表（包括商家分发、店铺自建）
        goodsDTO.setQueryType(GoodsQueryTypeEnum.STORE.getCode());
        // 设置非门店开单标识，避免center中的sql 在 queryType = 1006 and storeBillingFlag != 2时排除了子品
        goodsDTO.setStoreBillingFlag(WhetherEnum.YES.getCode().toString());
        // 非商品预售活动时，设置只查询有库存的商品
        if (!PromotionTypeEnum.PRE_SALE.getCode().equals(reqDTO.getPromotionType())
                && !PromotionTypeEnum.GIFT_CARD.getCode().equals(reqDTO.getPromotionType())) {
            goodsDTO.setStockDisableFlag(WhetherEnum.NO.getCode());
        }
        // 20230419 by lixiang 礼品卡活动不校验库存
        if (PromotionTypeEnum.GIFT_CARD.getCode().equals(reqDTO.getPromotionType())) {
            goodsDTO.setStockDisableFlag(null);
        }

        // 设置店铺编号
        goodsDTO.setStoreNo(reqDTO.getStoreNo());
        ExecuteDTO<ExecutePageDTO<ResGoodsDTO>> goodsPage = goodsAnalysisService.getGoodsPage(goodsDTO);
        if (null == goodsPage) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!goodsPage.successFlag()) {
            return ExecuteDTO.error(goodsPage.getStatus(), goodsPage.getMsg());
        }

        // 出参转换
        ExecutePageDTO<ResGoodsPromotionGoodsRelationDTO> resultPage = new ExecutePageDTO<>();
        if (null != goodsPage.getData()) {
            List<ResGoodsPromotionGoodsRelationDTO> list = BeanCopierUtil.copyList(goodsPage.getData().getRows(), ResGoodsPromotionGoodsRelationDTO.class);
            // 数据格式化
            if (CollectionUtils.isNotEmpty(list)) {
                // 查询出来的是非主品数据，所以不管是子品还是普通商品，都取主品编码来查询分销酬劳酬劳类型数据
                List<String> goodsNoList = list.stream().map(ResGoodsPromotionGoodsRelationDTO::getParentGoodsNo).collect(Collectors.toList());
                ExecuteDTO<List<AtomResAgentMarketRewardsetDTO>> rewardList = atomAgentMarketRewardsetAnalysisService.batchGetRewardSetByTaskNos(goodsNoList);
                if (null == rewardList) {
                    return ExecuteDTO.error(CommonCode.CODE_10000003);
                }
                if (!rewardList.successFlag()) {
                    return ExecuteDTO.error(rewardList.getStatus(), rewardList.getMsg());
                }
                for (ResGoodsPromotionGoodsRelationDTO goodsRelationDTO : list) {
                    // 规格属性
                    StringBuilder attributeNames = new StringBuilder();
                    if (StringUtils.isNotBlank(goodsRelationDTO.getFirstAttributeValueName())) {
                        attributeNames.append(goodsRelationDTO.getFirstAttributeValueName());
                    }
                    if (StringUtils.isNotBlank(goodsRelationDTO.getSecondAttributeValueName())) {
                        if (StringUtils.isNotBlank(attributeNames.toString())) {
                            attributeNames.append("-");
                        }
                        attributeNames.append(goodsRelationDTO.getSecondAttributeValueName());
                    }
                    if (StringUtils.isNotBlank(goodsRelationDTO.getThirdAttributeValueName())) {
                        if (StringUtils.isNotBlank(attributeNames.toString())) {
                            attributeNames.append("-");
                        }
                        attributeNames.append(goodsRelationDTO.getThirdAttributeValueName());
                    }
                    if (StringUtils.isNotBlank(attributeNames)) {
                        goodsRelationDTO.setAttributeNames(attributeNames.toString());
                    }
                    // 子品
                    if (SeriesTypeEnum.SUB_GOODS.getCode().equals(goodsRelationDTO.getSeriesType())) {
                        goodsRelationDTO.setSubGoodsFlag(WhetherEnum.YES.getCode());
                    } else {
                        // 普通商品
                        goodsRelationDTO.setSubGoodsFlag(WhetherEnum.NO.getCode());
                        goodsRelationDTO.setParentGoodsNo(goodsRelationDTO.getGoodsNo());
                    }

                    // 分销信息匹配
                    if (CollectionUtils.isNotEmpty(rewardList.getData())) {
                        Optional<AtomResAgentMarketRewardsetDTO> rewardOptional = rewardList.getData().stream()
                                .filter(rewardDTO -> StringUtils.equals(goodsRelationDTO.getParentGoodsNo(), rewardDTO.getTaskOrGoodsNo()))
                                .findFirst();
                        if (rewardOptional.isPresent()) {
                            AtomResAgentMarketRewardsetDTO resRewardSetDTO = rewardOptional.get();
                            // 酬劳类型
                            goodsRelationDTO.setRewardType(resRewardSetDTO.getRewardType());
                            // 分销酬劳
                            StringBuilder reward = new StringBuilder();
                            switch (resRewardSetDTO.getRewardType()) {
                                case NumConstant.ONE:
                                    // 佣金
                                    BigDecimal oneHundred = new BigDecimal(NumConstant.ONE_HUNDRED);
                                    if (null != resRewardSetDTO.getYjOrHjb()) {
                                        // 零售价
                                        BigDecimal retailPrice = goodsRelationDTO.getRetailPrice();
                                        if (null == retailPrice) {
                                            retailPrice = BigDecimal.valueOf(0);
                                        }
                                        BigDecimal yjOrHjb = resRewardSetDTO.getYjOrHjb();
                                        goodsRelationDTO.setYjPercent(BigDecimalUtil.setScale(yjOrHjb));
                                        // 按比例计算佣金
                                        BigDecimal calculateResult = yjOrHjb.divide(oneHundred)
                                                .multiply(retailPrice);
                                        // 四舍五入保留2位小数
                                        calculateResult = BigDecimalUtil.setScale(calculateResult);
                                        reward.append("￥").append(calculateResult);
                                    } else {
                                        log.info("*********佣金接口返回空值，请后台查看问题*********************");
                                        reward.append("");
                                    }
                                    break;
                                case NumConstant.TWO:
                                    if (null != resRewardSetDTO.getYjOrHjb()) {
                                        // 汇金币
                                        reward.append("汇金币").append(resRewardSetDTO.getYjOrHjb()).append("个");
                                    } else {
                                        log.info("*********汇金币接口返回空值，请后台查看问题*********************");
                                        reward.append("");
                                    }
                                    break;
                                case NumConstant.THREE:
                                    // 礼品
                                    reward.append(resRewardSetDTO.getRelatedName());
                                    break;
                                case NumConstant.FOUR:
                                    // 现金券
                                    reward.append("现金券(满￥").append(resRewardSetDTO.getRelatedUp().intValue()).append("减")
                                            .append(resRewardSetDTO.getRelatedLess().intValue()).append(")");
                                    break;
                                case NumConstant.FIVE:
                                    // 服务劵
                                    reward.append(resRewardSetDTO.getRelatedName());
                                    break;
                                default:
                                    log.info("*********酬劳设置未匹配到类型，请后台查看问题*********************");
                                    reward.append("");
                            }
                            goodsRelationDTO.setReward(reward.toString());
                        }
                    }
                }
            }
            resultPage.setRows(list);
            resultPage.setTotal(goodsPage.getData().getTotal());
        }


        log.info("**GoodsPromotionGoodsRelationAnalysisServiceImpl-getStoreGoodsForPromotion-查询店铺活动可以参与的商品-end");

        return ExecuteDTO.success(resultPage);
    }

    /**
     * @param reqGoodsPromotionGoodsOrderDTO
     * @Description : 查询活动下商品的基本配置信息
     * <AUTHOR> 高繁
     * @date : 2021/7/26 16:58
     */
    @Override
    public ExecuteDTO<List<ResGoodsPromotionGoodsRelationDTO>> getGoodsInfoList(ReqGoodsPromotionGoodsOrderDTO reqGoodsPromotionGoodsOrderDTO) {
        log.info(String.format("**GoodsPromotionGoodsRelationAnalysisServiceImpl-getGoodsInfoList-查询活动下商品的基本配置信息入参：%s", JSON.toJSONString(reqGoodsPromotionGoodsOrderDTO)));
        goodsPromotionAssert.getGoodsInfoList(reqGoodsPromotionGoodsOrderDTO);
        //查询商品数据
        AtomReqGoodsPromotionGoodsRelationDTO atomReqGoodsPromotionGoodsRelationDTO = BeanCopierUtil.copy(reqGoodsPromotionGoodsOrderDTO, AtomReqGoodsPromotionGoodsRelationDTO.class);
        ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> executeDTO = atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList(atomReqGoodsPromotionGoodsRelationDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        if (CollectionUtils.isNotEmpty(reqGoodsPromotionGoodsOrderDTO.getGoodsNoList()) && (CollectionUtils.isEmpty(executeDTO.getData()) || executeDTO.getData().size() != reqGoodsPromotionGoodsOrderDTO.getGoodsNoList().size())) {
            //商品数量不一致
            return ExecuteDTO.error(MarketErrorCode.CODE_17000616);
        }
        return ExecuteDTO.success(BeanCopierUtil.copyList(executeDTO.getData(), ResGoodsPromotionGoodsRelationDTO.class));
    }

    /**
     * 查询添加促销活动商品时，不可选择的商品编码集合
     *
     * @param reqDTO
     * @return
     * <AUTHOR> @date 2021-07-29
     */
    @Override
    public ExecuteDTO<List<String>> getNoChoiceGoodsNoList(ReqGoodsPromotionGoodsRelationDTO reqDTO) {
        log.info(String.format("**GoodsPromotionGoodsRelationAnalysisServiceImpl-getNoChoiceGoodsNoList- 查询添加促销活动商品时，不可选择的商品编码集合：%s", JSON.toJSONString(reqDTO)));
        goodsPromotionAssert.getNoChoiceGoodsNoList(reqDTO);
        //查询不可选择的商品编码集合
        AtomReqGoodsPromotionGoodsRelationDTO atomReqGoodsPromotionGoodsRelationDTO = BeanCopierUtil.copy(reqDTO, AtomReqGoodsPromotionGoodsRelationDTO.class);
        ExecuteDTO<List<String>> executeDTO = atomGoodsPromotionGoodsRelationAnalysisService.getNoChoiceGoodsNoList(atomReqGoodsPromotionGoodsRelationDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        return ExecuteDTO.success(executeDTO.getData());
    }

    /**
     * 秒杀商品下单校验
     *
     * @param reqDTO
     * @return
     * <AUTHOR>
     * @date 2021-08-02
     */
    @Override
    public ExecuteDTO secKillGoodsPlacingOrderCheck(ReqSkiSecGoodsOrderCheckDTO reqDTO) {
        log.info("**GoodsPromotionGoodsRelationAnalysisServiceImpl.secKillGoodsPlacingOrderCheck-入参：{}", reqDTO);
        // 入参校验
        goodsPromotionAssert.secKillGoodsPlacingOrderCheckAssert(reqDTO);

        // 根据商品编号查询商品信息
        ReqGoodsDTO goodsDto = new ReqGoodsDTO();
        goodsDto.setDisableFlag(NumConstant.TWO);   // 查询有效的商品
        goodsDto.setGoodsNo(reqDTO.getGoodsNo());
        ExecuteDTO<ResGoodsDTO> goodsInfoExecuteDTO = goodsAnalysisService.getGoods(goodsDto);
        if (null == goodsInfoExecuteDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!goodsInfoExecuteDTO.successFlag()) {
            return ExecuteDTO.error(goodsInfoExecuteDTO.getStatus(), goodsInfoExecuteDTO.getMsg());
        }
        if (null == goodsInfoExecuteDTO.getData()) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000616);
        }
        ResGoodsDTO goodsInfo = goodsInfoExecuteDTO.getData();
        BigDecimal canSaleStockNum = BigDecimal.valueOf(0);
        if (goodsInfo.getCanSaleStockNum() != null) {
            canSaleStockNum = goodsInfo.getCanSaleStockNum();
        }
        if ((canSaleStockNum.subtract(BigDecimal.valueOf(reqDTO.getBuyNum()))).compareTo(BigDecimal.valueOf(0)) < 0) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000619);
        }

        // 云池商品时，取cloudPoolGoodsNo，否则取 goodsNo
        String goodsNo;
        if (GoodsSourceTypeEnum.CLOUD_POOL_DISTRIBUTE.getCode().equals(goodsInfo.getGoodsSourceType())) {
            goodsNo = goodsInfo.getCloudPoolGoodsNo();
        } else {
            goodsNo = goodsInfo.getGoodsNo();
        }

        // 查询秒杀商品配置的活动信息
        AtomReqGoodsPromotionGoodsRelationDTO goodsRelationDTO = new AtomReqGoodsPromotionGoodsRelationDTO();
        goodsRelationDTO.setPromotionNo(reqDTO.getPromotionNo());
        goodsRelationDTO.setPeriodNo(reqDTO.getPeriodNo());
        goodsRelationDTO.setGoodsNo(goodsNo);
        ExecuteDTO<AtomResGoodsPromotionGoodsRelationDTO> goodsAndRuleExecuteDTO = atomGoodsPromotionGoodsRelationAnalysisService.getGoodsAndRule(goodsRelationDTO);
        if (null == goodsAndRuleExecuteDTO || !goodsAndRuleExecuteDTO.successFlag()) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (null == goodsAndRuleExecuteDTO.getData()) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000616);
        }
        AtomResGoodsPromotionGoodsRelationDTO goodsAndRule = goodsAndRuleExecuteDTO.getData();
        // 如果活动配置限制新用户参与时，校验用户是否为新用户
        if (UserScopeEnum.NEW_FANS.getCode().equals(goodsAndRule.getUserScope())) {
            // 判断是否限制新老用户，如果是云池商品，表示平台，store传 null
            ExecuteDTO<Boolean> checkNewFansExecuteDTO = preDeterminedCheckService.checkNewFans(reqDTO.getFanNo(),
                    GoodsSourceTypeEnum.CLOUD_POOL_DISTRIBUTE.getCode().equals(goodsInfo.getGoodsSourceType()) ? null : reqDTO.getStoreNo());
            if (!checkNewFansExecuteDTO.successFlag()) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            // 活动设置了限制新用户，但该用户已经下过单不是新用户了，提示信息给前端
            if (!checkNewFansExecuteDTO.getData()) {
                return ExecuteDTO.error(MarketErrorCode.CODE_17000620);
            }
        } else if (UserScopeEnum.OLD_FANS.getCode().equals(goodsAndRule.getUserScope())) {
            // 如果活动配置限制老用户参与时，校验用户是否为老用户
            // 判断是否限制新老用户，如果是云池商品，表示平台，store传 null
            ExecuteDTO<Boolean> checkNewFansExecuteDTO = preDeterminedCheckService.checkNewFans(reqDTO.getFanNo(),
                    GoodsSourceTypeEnum.CLOUD_POOL_DISTRIBUTE.getCode().equals(goodsInfo.getGoodsSourceType()) ? null : reqDTO.getStoreNo());
            if (!checkNewFansExecuteDTO.successFlag()) {
                return ExecuteDTO.error(CommonCode.CODE_10000003);
            }
            // 活动设置了限制老用户，但该用户没有下过单，是新用户，提示信息给前端
            if (checkNewFansExecuteDTO.getData()) {
                return ExecuteDTO.error(MarketErrorCode.CODE_17000623);
            }
        }

        // 如果是平台活动的云池商品，则查询根据商品编码查询商品数据，替换成cloudPoolGoodsNo取缓存数据
        if (SourceTypeEnum.SOURCE_TYPE_1001.getCode().equals(goodsAndRule.getSourceType())) {
            reqDTO.setGoodsNo(goodsNo);
        }

        // 单店总限购数
        Integer storeTotalNum = goodsAndRule.getStoreTotalNum() == null ? 0 : goodsAndRule.getStoreTotalNum();
        // 单店已经购买的秒杀商品数
        String storeBuyGoodsNumKey = String.format(PromotionKeyConstant.PROMOTION_STORE_BUYGOODSNUM_PREFIX, reqDTO.getPromotionNo(), reqDTO.getStoreNo());
        Object storeBuyGoodsNumObject = redisBaseUtil.get(storeBuyGoodsNumKey);
        Integer storeBuyGoodsTotalNum = (storeBuyGoodsNumObject == null || StringUtils.isBlank(storeBuyGoodsNumObject.toString())) ?
                reqDTO.getBuyNum() : (Integer.parseInt(storeBuyGoodsNumObject.toString()) + reqDTO.getBuyNum());
        // 活动中每人秒杀总限购数
        Integer limitBuyTotalNum = goodsAndRule.getLimitBuyTotalNum() == null ? 0 : goodsAndRule.getLimitBuyTotalNum();
        // 个人已购买商品总数
        String userBuyGoodsTotalNumKey = String.format(PromotionKeyConstant.PROMOTION_USER_BUYGOODSNUM_PREFIX, reqDTO.getPromotionNo(), reqDTO.getFanNo());
        Object userBuyGoodsTotalNumObject = redisBaseUtil.get(userBuyGoodsTotalNumKey);
        Integer userBuyGoodsTotalNum = (userBuyGoodsTotalNumObject == null || StringUtils.isBlank(userBuyGoodsTotalNumObject.toString())) ?
                reqDTO.getBuyNum() : (Integer.parseInt(userBuyGoodsTotalNumObject.toString()) + reqDTO.getBuyNum());
        // 商品个人限购数
        Integer limitBuyNum = goodsAndRule.getLimitBuyNum() == null ? 0 : goodsAndRule.getLimitBuyNum();
        // 个人已购买商品数
        String userBuyGoodsNumKey = String.format(PromotionKeyConstant.PROMOTION_USER_GOODS_BUYGOODSNUM_PREFIX
                , reqDTO.getPromotionNo(), reqDTO.getPeriodNo(), reqDTO.getGoodsNo(), reqDTO.getFanNo());
        Object userBuyGoodsNumObject = redisBaseUtil.get(userBuyGoodsNumKey);
        Integer userBuyGoodsNum = (userBuyGoodsNumObject == null || StringUtils.isBlank(userBuyGoodsNumObject.toString())) ?
                reqDTO.getBuyNum() : (Integer.parseInt(userBuyGoodsNumObject.toString()) + reqDTO.getBuyNum());

        // 平台活动
        if (SourceTypeEnum.SOURCE_TYPE_1001.getCode().equals(goodsAndRule.getSourceType())) {
            // 如果 小于单店总限购数量  并且 小于活动中每人秒杀总限购数，但超过个人限购数，则提示 抱歉，已超秒杀限制数量，请修改数量重新下单！
            if ((storeBuyGoodsTotalNum.compareTo(storeTotalNum) < 0 || userBuyGoodsTotalNum.compareTo(limitBuyTotalNum) < 0)
                    && userBuyGoodsNum.compareTo(limitBuyNum) > 0) {
                return ExecuteDTO.error(MarketErrorCode.CODE_17000621);
            }
            // 如果 大于单店总限购数量  或者 大于活动中每人秒杀总限购数 或者 大于个人限购数，则提示 抱歉，当前购买数量已超秒杀优惠限制，不支持继续购买！
            if (storeBuyGoodsTotalNum.compareTo(storeTotalNum) > 0 || userBuyGoodsTotalNum.compareTo(limitBuyTotalNum) > 0
                    || userBuyGoodsNum.compareTo(limitBuyNum) > 0) {
                return ExecuteDTO.error(MarketErrorCode.CODE_17000622);
            }
        } else {
            // 店铺活动
            // 如果 小于活动中每人秒杀总限购数，但超过个人限购数，则提示 抱歉，已超秒杀限制数量，请修改数量重新下单！
            if (userBuyGoodsTotalNum.compareTo(limitBuyTotalNum) < 0 && userBuyGoodsNum.compareTo(limitBuyNum) > 0) {
                return ExecuteDTO.error(MarketErrorCode.CODE_17000621);
            }
            // 如果 大于活动中每人秒杀总限购数 或者 大于个人限购数，则提示 抱歉，当前购买数量已超秒杀优惠限制，不支持继续购买！
            if (userBuyGoodsTotalNum.compareTo(limitBuyTotalNum) > 0 || userBuyGoodsNum.compareTo(limitBuyNum) > 0) {
                return ExecuteDTO.error(MarketErrorCode.CODE_17000622);
            }
        }

        return ExecuteDTO.success();
    }

    /**
     * @param reqGoodsPromotionPeriodDTO
     * @Description : 获取商品配置时间段信息
     * <AUTHOR> 高繁
     * @date : 2021/8/9 15:31
     */
    @Override
    public ExecuteDTO<ResGoodsPromotionPeriodDTO> getGoodsPeriod(ReqGoodsPromotionPeriodDTO reqGoodsPromotionPeriodDTO) {
        // 首先根据活动编号和时间段编号查询活动场次信息
        AtomReqGoodsPromotionPeriodDTO reqPromotionPeriodDTO = BeanCopierUtil.copy(reqGoodsPromotionPeriodDTO, AtomReqGoodsPromotionPeriodDTO.class);
        ExecuteDTO<AtomResGoodsPromotionPeriodDTO> promotionPeriodDTOExecuteDTO = atomGoodsPromotionPeriodAnalysisService.selectPromotionPeriodInfo(reqPromotionPeriodDTO);
        log.info("**查询活动场次信息出参：{}", promotionPeriodDTOExecuteDTO);
        if (null == promotionPeriodDTOExecuteDTO || !promotionPeriodDTOExecuteDTO.successFlag()) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        AtomResGoodsPromotionPeriodDTO promotionPeriodDTO = promotionPeriodDTOExecuteDTO.getData();
        if (null == promotionPeriodDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000002.getCode(), "没有找到活动信息");
        }
        return ExecuteDTO.success(BeanCopierUtil.copy(promotionPeriodDTO, ResGoodsPromotionPeriodDTO.class));
    }

    /**
     * 获取活动下关联的商品数
     *
     * @param relationDTO 查询参数
     * @return 活动下关联的商品数
     * <AUTHOR>
     * @date 2021-10-12
     */
    @Override
    public ExecuteDTO<List<ResGoodsPromotionGoodsRelationDTO>> getPromotionGoodsNum(ReqGoodsPromotionGoodsRelationDTO relationDTO) {
        log.info("getPromotionGoodsNum-入参：{}", relationDTO);

        // 查询活动关联的商品数
        AtomReqGoodsPromotionGoodsRelationDTO reqDTO = BeanCopierUtil.copy(relationDTO, AtomReqGoodsPromotionGoodsRelationDTO.class);
        ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> executeDTO = atomGoodsPromotionGoodsRelationAnalysisService.getPromotionGoodsNum(reqDTO);
        log.info("getPromotionGoodsNum-出参：{}", executeDTO);
        if (null == executeDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }

        // 返回结果
        return ExecuteDTO.success(BeanCopierUtil.copyList(executeDTO.getData(), ResGoodsPromotionGoodsRelationDTO.class));
    }

    /**
     * 查询商品参与的促销活动信息（普通商详页）
     *
     * @param relationDTO 查询参数
     * <AUTHOR>
     * @date 2021-11-23
     */
    @Override
    public ExecuteDTO<List<ResGoodsPromotionGoodsRelationDTO>> getGoodsPromotionInfo(ReqGoodsPromotionGoodsRelationDTO relationDTO) {
        log.info("getGoodsPromotionInfo-入参：{}", relationDTO);
        // 入参校验
        goodsPromotionAssert.getGoodsPromotionInfoAssert(relationDTO);

        // 查询商品参与的活动信息
        AtomReqGoodsPromotionGoodsRelationDTO reqAtomDTO = BeanCopierUtil.copy(relationDTO, AtomReqGoodsPromotionGoodsRelationDTO.class);
        ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> executeDTO =
                atomGoodsPromotionGoodsRelationAnalysisService.getGoodsPromotionInfo(reqAtomDTO);
        if (null == executeDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }

        // 返回结果
        return ExecuteDTO.success(BeanCopierUtil.copyList(executeDTO.getData(), ResGoodsPromotionGoodsRelationDTO.class));
    }

    /**
     * 查询指定活动场次下设置的商品数据
     *
     * @param reqDTO 查询参数
     * <AUTHOR>
     * @date 2021-12-08
     */
    @Override
    public ExecuteDTO<List<ResGoodsPromotionGoodsRelationDTO>> getPromotionPeriodGoodsList(ReqGoodsPromotionGoodsRelationDTO reqDTO) {
        log.info("getPromotionPeriodGoodsList-入参：{}", reqDTO);
        // 入参校验
        goodsPromotionAssert.getPromotionPeriodGoodsListAssert(reqDTO);

        // 查询数据
        AtomReqGoodsPromotionGoodsRelationDTO relationDTO = BeanCopierUtil.copy(reqDTO, AtomReqGoodsPromotionGoodsRelationDTO.class);
        ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> executeDTO =
                atomGoodsPromotionGoodsRelationAnalysisService.getPromotionPeriodGoodsList(relationDTO);
        if (null == executeDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000003);
        }
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }

        // 返回结果
        return ExecuteDTO.success(BeanCopierUtil.copyList(executeDTO.getData(), ResGoodsPromotionGoodsRelationDTO.class));
    }

    /**
     * 查询活动下各个场次中不设置成活动商品的数据
     *
     * @param reqDTO 查询参数
     * <AUTHOR>
     * @date 2021-12-13
     */
    @Override
    public ExecuteDTO<List<ResGoodsPromotionGoodsRelationDTO>> getNoChoiceGoodsNosForBatchPeriod(ReqGoodsPromotionInfoDTO reqDTO) {
        log.info("getNoChoiceGoodsNosForBatchPeriod-入参：{}", reqDTO);
        // 入参校验
        goodsPromotionAssert.getNoChoiceGoodsNosForBatchPeriodAssert(reqDTO);

        // 查询不可设置成活动商品的互斥商品数据
        AtomReqGoodsPromotionGoodsRelationDTO reqNoChoiceGoods = BeanCopierUtil.copy(reqDTO, AtomReqGoodsPromotionGoodsRelationDTO.class);
        reqNoChoiceGoods.setPeriodList(BeanCopierUtil.copyList(reqDTO.getGoodsPromotionPeriodDTOList(), AtomReqLimitTimePeriodDTO.class));
        ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> noChoiceGoodsNoList =
                atomGoodsPromotionGoodsRelationAnalysisService.getNoChoiceGoodsNosForBatchPeriod(reqNoChoiceGoods);
        if (noChoiceGoodsNoList == null || !noChoiceGoodsNoList.successFlag()) {
            return ExecuteDTO.error(CommonCode.CODE_10000003.getCode(), "查询互斥商品时异常");
        }

        // 返回结果
        return ExecuteDTO.success(BeanCopierUtil.copyList(noChoiceGoodsNoList.getData(), ResGoodsPromotionGoodsRelationDTO.class));
    }

    /**
     * 检查是否同时间段活动
     *
     * @param reqDTO 查询参数
     * <AUTHOR>
     * @date 2022-02-18
     */
    @Override
    public ExecuteDTO<Integer> checkPromotionGoods(ReqFullDiscountInfoAddDTO reqDTO) {
        log.info("checkPromotionGoods-入参：{}", reqDTO);
        // 入参校验
        this.goodsPromotionAssert.checkPromotionGoods(reqDTO);
        Integer resDto = NumConstant.ZERO;
        // 根据店铺编码和时间段查询已存在的活动
        AtomReqPromotionInfoDTO infoDTO = BeanCopierUtil.copy(reqDTO, AtomReqPromotionInfoDTO.class);
        infoDTO.setDailyStartTime(reqDTO.getGoodsPromotionPeriodDTOList().get(NumConstant.ZERO).getStartTime());
        infoDTO.setDailyEndTime(reqDTO.getGoodsPromotionPeriodDTOList().get(NumConstant.ZERO).getEndTime());
        ExecuteDTO<List<AtomResGoodsPromotionRuleDTO>> fullDisCount = atomGoodsPromotionRuleAnalysisService.getFullDiscountByParam(infoDTO);
        if (fullDisCount == null || !fullDisCount.successFlag()) {
            return ExecuteDTO.error(CommonCode.CODE_10000003.getCode(), "查询满减满折活动信息异常");
        }
        if (CollectionUtils.isEmpty(fullDisCount.getData())) {
            return ExecuteDTO.success(resDto);
        }
        // 判断当前活动的参与商品是否与之前活动有交叉
        Optional<AtomResGoodsPromotionRuleDTO> ruleDTO = fullDisCount.getData().stream().filter(fullDisCountInfo ->
                GoodsPromotionScopeEnum.SCOPE_TYPE_ALL.getCode().equals(fullDisCountInfo.getGoodsPromotionScope())
        ).findFirst();
        if (ruleDTO.isPresent()) {
            // 有全部商品直接返回
            return ExecuteDTO.success(NumConstant.ONE);
        }
        // 指定商品商品集
        List<AtomResGoodsPromotionGoodsRelationDTO> goodsRelationList = new ArrayList<>();
        // 指定类目类目集
        List<AtomResGoodsPromotionCategoryRelationDTO> goodsCategoryList = new ArrayList<>();
        // 指定商品
        List<AtomResGoodsPromotionRuleDTO> goodsPromotionList = fullDisCount.getData().stream().filter(fullDisCountInfo ->
                GoodsPromotionScopeEnum.SCOPE_TYPE_GOODS.getCode().equals(fullDisCountInfo.getGoodsPromotionScope())
        ).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(goodsPromotionList)) {
            List<String> goodsPromotionNoList = goodsPromotionList.stream().map(AtomResGoodsPromotionRuleDTO::getPromotionNo).collect(Collectors.toList());
            AtomReqGoodsPromotionGoodsRelationDTO goodsRelationDTO = new AtomReqGoodsPromotionGoodsRelationDTO();
            goodsRelationDTO.setPromotionNoList(goodsPromotionNoList);
            ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> goodsRelationListData = atomGoodsPromotionGoodsRelationAnalysisService.getGoodsRelationList(goodsRelationDTO);
            if (!goodsRelationListData.successFlag()) {
                return ExecuteDTO.error(CommonCode.CODE_10000003.getCode(), "查询活动下所以参与商品异常");
            }
            goodsRelationList = goodsRelationListData.getData();
        }
        // 指定类目
        List<AtomResGoodsPromotionRuleDTO> categoryPromotionList = fullDisCount.getData().stream().filter(fullDisCountInfo ->
                GoodsPromotionScopeEnum.SCOPE_TYPE_CATEGORY.getCode().equals(fullDisCountInfo.getGoodsPromotionScope())
        ).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(categoryPromotionList)) {
            List<String> categoryPromotionNoNoList = categoryPromotionList.stream().map(AtomResGoodsPromotionRuleDTO::getPromotionNo).collect(Collectors.toList());
            // 根据活动编码查询类目数据
            AtomReqGoodsPromotionCategoryRelationDTO relationDTO = new AtomReqGoodsPromotionCategoryRelationDTO();
            relationDTO.setPromotionNoList(categoryPromotionNoNoList);
            ExecuteDTO<List<AtomResGoodsPromotionCategoryRelationDTO>> goodsCategoryListData = atomGoodsPromotionCategoryRelationAnalysisService.getGoodsPromotionCategoryByParam(relationDTO);
            goodsCategoryList = goodsCategoryListData.getData();
        }
        // 类目直接对比新增类目
        if (CollectionUtils.isNotEmpty(reqDTO.getReqCouponCategoryRelationDTOS()) && CollectionUtils.isNotEmpty(goodsCategoryList)) {
            List<String> reqCategoryList = reqDTO.getReqCouponCategoryRelationDTOS().stream().map(ReqCouponCategoryRelationDTO::getCategoryNo).collect(Collectors.toList());
            Optional<AtomResGoodsPromotionCategoryRelationDTO> relationDTO = goodsCategoryList.stream().filter(goodsCategory -> reqCategoryList.contains(goodsCategory.getCategoryNo())).findFirst();
            if (relationDTO.isPresent()) {
                return ExecuteDTO.success(NumConstant.ONE);
            }
        }

        if (CollectionUtils.isNotEmpty(reqDTO.getReqCouponGoodsRelationDTOS()) && CollectionUtils.isNotEmpty(goodsRelationList)) {
            // 指定商品对比新增商品、并且对比类目
            List<String> reqGoodsNoList = reqDTO.getReqCouponGoodsRelationDTOS().stream().map(ReqCouponGoodsRelationDTO::getGoodsNo).collect(Collectors.toList());
            Optional<AtomResGoodsPromotionGoodsRelationDTO> goodsRelationDTO = goodsRelationList.stream().filter(goodsCategory -> reqGoodsNoList.contains(goodsCategory.getGoodsNo())).findFirst();
            if (goodsRelationDTO.isPresent()) {
                return ExecuteDTO.success(NumConstant.ONE);
            }
            // 历史类目不为空时校验(选指定商品的时候 要关注之前指定类目活动，当前的商品是否在类目里存在 提示)
            if (CollectionUtils.isNotEmpty(goodsCategoryList)) {
                // 历史类目编码
                List<String> categoryList = goodsCategoryList.stream().map(AtomResGoodsPromotionCategoryRelationDTO::getCategoryNo).collect(Collectors.toList());
                // 查指定商品的类目 与历史类目比较是否有重叠
                AtomReqGoodsDTO goodsDTO = new AtomReqGoodsDTO();
                goodsDTO.setGoodsNos(reqGoodsNoList);
                ExecuteDTO<List<AtomResGoodsDTO>> goodsByParams = legacyGoodsCenterService.getGoodsByParams(goodsDTO);
                if (!goodsByParams.successFlag()) {
                    return ExecuteDTO.error(CommonCode.CODE_10000003.getCode(), "查询活动下所以参与商品异常");
                }
                List<AtomResGoodsDTO> goodsByParamsData = goodsByParams.getData();
                if (CollectionUtils.isNotEmpty(goodsByParamsData)) {
                    Optional<AtomResGoodsDTO> resGoodsDTO = goodsByParamsData.stream().filter(goodsInfo -> categoryList.contains(goodsInfo.getCategoryNo())).findFirst();
                    if (resGoodsDTO.isPresent()){
                        return ExecuteDTO.success(NumConstant.ONE);
                    }

                }
            }
        }
        return ExecuteDTO.success(resDto);
    }

    /**
     * 检查特惠促销活动商品是否重复
     *
     * @param reqDTO 查询参数
     * <AUTHOR>
     * @date 2022-07-23
     */
    @Override
    public ExecuteDTO<List<ResGoodsPromotionGoodsRelationDTO>> checkVipPricePromotionGoods(ReqVipPriceAddDTO reqDTO) {
        log.info("checkVipPricePromotionGoods-入参：{}", reqDTO);
        AtomGoodsPromotionRuleVo vo = new AtomGoodsPromotionRuleVo();
        // 活动时间
        vo.setEffectiveTime(reqDTO.getEffectiveTime());
        vo.setInvalidTime(reqDTO.getInvalidTime());
        vo.setDailyStartTime(ListUtil.getFirst(reqDTO.getGoodsPromotionPeriodDTOList()).getStartTime());
        vo.setDailyEndTime(ListUtil.getFirst(reqDTO.getGoodsPromotionPeriodDTOList()).getEndTime());
        // 商品编码列表
        List<String> goodsNos = reqDTO.getGoodsList().stream().map(ReqVipPriceGoodsDTO::getGoodsNo).collect(Collectors.toList());
        List<AtomGoodsPromotionGoodsRelationVo> goodsVoList = goodsPromotionRuleDao.selectVipPriceByParam(vo, goodsNos);
        if (ListUtil.isEmpty(goodsVoList)) {
            return ExecuteDTO.ok(Collections.emptyList());
        } else {
            List<ResGoodsPromotionGoodsRelationDTO> resultList = new ArrayList<>(goodsVoList.size());
            for (AtomGoodsPromotionGoodsRelationVo atomGoodsPromotionGoodsRelationVo : goodsVoList) {
                if (StringUtils.isBlank(reqDTO.getPromotionNo()) || !reqDTO.getPromotionNo().equals(atomGoodsPromotionGoodsRelationVo.getPromotionNo())) {
                    resultList.add(BeanCopierUtil.copy(atomGoodsPromotionGoodsRelationVo, ResGoodsPromotionGoodsRelationDTO.class));
                }
            }
            return ExecuteDTO.ok(resultList);
        }
    }

    /**
     * 根据商品编码查询生效中的特惠促销
     * @param reqDTO reqDTO
     * @return ExecuteDTO<List<ResGoodsPromotionGoodsRelationDTO>>
     */
    @Override
    public ExecuteDTO<List<ResGoodsPromotionGoodsRelationDTO>> getActiveVipPriceByGoodsNos(ReqPromotionQueryDTO reqDTO) {
        log.info("getActiveVipPriceByGoodsNos-入参：{}", reqDTO);
        // 商品编码列表
        List<String> goodsNos = reqDTO.getGoodsNos();
        List<AtomPromotionGoodsInfoVo> goodsVoList = goodsPromotionGoodsRelationDao.selectActiveVipPriceByGoodsNos(goodsNos);
        if (ListUtil.isEmpty(goodsVoList)) {
            return ExecuteDTO.ok(Collections.emptyList());
        } else {
            return ExecuteDTO.ok(BeanCopierUtil.copyList(goodsVoList, ResGoodsPromotionGoodsRelationDTO.class));
        }
    }

    /**
     * 根据商品编码查询生效中的会员价
     *
     * @param reqDTO reqDTO
     * @return ExecuteDTO<List < ResGoodsPromotionGoodsRelationDTO>>
     */
    @Override
    public ExecuteDTO<List<ResGoodsPromotionGoodsRelationDTO>> getActiveMemberPriceByGoodsNos(ReqPromotionQueryDTO reqDTO) {
        log.info("getActiveMemberPriceByGoodsNos-入参：{}", JSON.toJSONString(reqDTO));
        // 商品编码列表
//        List<String> goodsNos = reqDTO.getGoodsNos();
        List<AtomPromotionGoodsInfoVo> goodsVoList = goodsPromotionGoodsRelationDao.selectActiveMemberPriceByGoodsNos(reqDTO);
        if (ListUtil.isEmpty(goodsVoList)) {
            return ExecuteDTO.ok(Collections.emptyList());
        } else {
            return ExecuteDTO.ok(BeanCopierUtil.copyList(goodsVoList, ResGoodsPromotionGoodsRelationDTO.class));
        }
    }

    /**
     * 查询当前生效的折扣最低的全场通用的会员价活动
     * @return ExecuteDTO<List<ResGoodsPromotionGoodsRelationDTO>>
     */
    @Override
    public ExecuteDTO<ResGoodsPromotionGoodsRelationDTO> selectMinActiveMemberPriceAllGoods(ReqPromotionQueryDTO reqDTO) {
        log.info("selectActiveMemberPriceAllGoods-start");
        AtomPromotionGoodsInfoVo promotionGoodsInfoVo = goodsPromotionGoodsRelationDao.selectMinActiveMemberPriceAllGoods(reqDTO);
        ResGoodsPromotionGoodsRelationDTO resultDto = BeanCopierUtil.copy(promotionGoodsInfoVo, ResGoodsPromotionGoodsRelationDTO.class);
        log.info("selectActiveMemberPriceAllGoods-result:{}", resultDto);
        return ExecuteDTO.ok(resultDto);
    }

    /**
     * 根据商品编码查询生效中的会员活动信息
     * @param reqDTO reqDTO goodsNo或goodsNoList
     * @return ExecuteDTO<List<ResGoodsPromotionGoodsRelationDTO>>
     */
    @Override
    public ExecuteDTO<List<ResGoodsPromotionGoodsRelationDTO>> getMemberPromotionInfoByGoods(ReqPromotionQueryDTO reqDTO) {
        log.info("getMemberPromotionInfoByGoods-入参：{}", reqDTO);
        AtomGoodsPromotionRuleVo atomGoodsPromotionRuleVo = BeanCopierUtil.copy(reqDTO, AtomGoodsPromotionRuleVo.class);
        List<AtomGoodsPromotionGoodsRelationVo> goodsVoList = goodsPromotionRuleDao.selectMemberPriceByTerm(atomGoodsPromotionRuleVo);
        if (ListUtil.isEmpty(goodsVoList)) {
            return ExecuteDTO.ok(Collections.emptyList());
        } else {
            return ExecuteDTO.ok(BeanCopierUtil.copyList(goodsVoList, ResGoodsPromotionGoodsRelationDTO.class));
        }
    }

    /**
     * 查询指定活动下设置的商品数据
     *
     * @param reqDTO 查询参数
     * <AUTHOR>
     * @date 2022-08-15
     */
    @Override
    public ExecuteDTO<List<ResGoodsPromotionGoodsRelationDTO>> getRelationGoodsList(ReqGoodsPromotionGoodsRelationDTO reqDTO) {
        log.info("getRelationGoodsList-入参：{}", reqDTO);
        // 查询数据
        AtomReqGoodsPromotionGoodsRelationDTO relationDTO = BeanCopierUtil.copy(reqDTO, AtomReqGoodsPromotionGoodsRelationDTO.class);
        ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> executeDTO = atomGoodsPromotionGoodsRelationAnalysisService.getPromotionPeriodGoodsList(relationDTO);
        if (null == executeDTO) {
            return new ExecuteDTO<>(CommonCode.CODE_10000003.getCode(), CommonCode.CODE_10000003.getShowMsg(), null);
        }
        if (!executeDTO.successFlag()) {
            return new ExecuteDTO<>(executeDTO.getStatus(), executeDTO.getMsg(), null);
        }
        // 返回结果
        return ExecuteDTO.ok(BeanCopierUtil.copyList(executeDTO.getData(), ResGoodsPromotionGoodsRelationDTO.class));
    }

    @Override
    public ExecuteDTO<Integer> checkActivePromotionGoodsRelation(ReqGoodsPromotionGoodsRelationDTO reqDTO) {
        AtomGoodsPromotionGoodsRelationVo vo = BeanCopierUtil.copy(reqDTO, AtomGoodsPromotionGoodsRelationVo.class);
        Integer integer = goodsPromotionGoodsRelationDao.checkActivePromotionGoodsRelation(vo);
        return ExecuteDTO.ok(integer == null ? NumConstant.ZERO : integer);
    }
}
