package cn.htdt.marketprocess.biz.rabbitmq.config;

import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.CustomExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.QueueBuilder;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * direct模式配置-延迟队列配置
 *
 * <AUTHOR>
 */
@Configuration
public class DelayDeplayConfig {

    @Value("${global.env:null}")
    private String env;

    /* mtodo 暂时注释掉
    @Resource
    private RabbitAdmin secondRabbitAdmin;
     */

    /**
     * 正常业务交换机
     **/
    public static final String NORMAL_DELAYED_EXCHANGE_NAME = "marketprocess_normal_delayed_exchange";

    /**
     * 短信发送消息队列
     */
    public final static String NORMAL_QUEUE_SMS_TASK_NAME = "marketprocess_normal_queue_sms_task";
    public final static String NORMAL_QUEUE_SMS_TASK_ROUTING_KEY = "marketprocess_normal_queue_sms_task_routingkey";


    @PostConstruct
    public void RabbitInit() {
        /* mtodo 暂时注释掉
        Map<String, Object> args = new HashMap<>();
        args.put("x-delayed-type", "direct");
        CustomExchange normalExchange = new CustomExchange(NORMAL_DELAYED_EXCHANGE_NAME + env, "x-delayed-message", true, false, args);
        secondRabbitAdmin.declareExchange(normalExchange);
        Queue normalQueueSmsTaskQueue = QueueBuilder.durable(NORMAL_QUEUE_SMS_TASK_NAME + env).build();
        secondRabbitAdmin.declareQueue(normalQueueSmsTaskQueue);

        secondRabbitAdmin.declareBinding(BindingBuilder.bind(normalQueueSmsTaskQueue)
                .to(normalExchange)
                .with(NORMAL_QUEUE_SMS_TASK_ROUTING_KEY + env).and(Collections.emptyMap()));
         */

    }

}
