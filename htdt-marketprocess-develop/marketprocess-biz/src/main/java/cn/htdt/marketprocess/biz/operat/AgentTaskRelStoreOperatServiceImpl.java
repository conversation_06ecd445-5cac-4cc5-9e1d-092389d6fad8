package cn.htdt.marketprocess.biz.operat;

import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.enums.constants.AgentKeyConstant;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.constants.RedisKeyTTLConstant;
import cn.htdt.common.enums.market.TaskStatusEnum;
import cn.htdt.common.enums.user.StoreStatusEnum;
import cn.htdt.common.generator.MarketFormGenerator;
import cn.htdt.common.redis.utils.RedisBaseUtil;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqAgentTaskRelStoreDTO;
import cn.htdt.marketprocess.api.operat.AgentTaskRelStoreOperatService;
import cn.htdt.marketprocess.dto.request.ReqAgentTaskRelStoreDTO;
import cn.htdt.marketprocess.dto.response.ResImportErrorStoreDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomAgentTaskAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomAgentTaskRelStoreAnalysisService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomAgentTaskOperatService;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomAgentTaskRelStoreOperatService;
import cn.htdt.userprocess.api.UserPublicService;
import cn.htdt.userprocess.dto.publicDto.response.GetStoreListResponse;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021-01-15
 * @Description 任务和店铺关系原子操作服务
 */
@Slf4j
@DubboService
public class AgentTaskRelStoreOperatServiceImpl implements AgentTaskRelStoreOperatService {

    @Resource
    private AtomAgentTaskOperatService atomAgentTaskOperatService;

    @Resource
    private AtomAgentTaskAnalysisService atomAgentTaskAnalysisService;

    @Resource
    private AtomAgentTaskRelStoreOperatService atomAgentTaskRelStoreOperatService;

    @Resource
    private AtomAgentTaskRelStoreAnalysisService atomAgentTaskRelStoreAnalysisService;

    @DubboReference
    private UserPublicService userPublicService;

    @Autowired
    private RedisBaseUtil redisBaseUtil;

    @Override
    public ExecuteDTO saveBatch(List<ReqAgentTaskRelStoreDTO> reqAgentTaskRelStoreDTOList) {
        List<AtomReqAgentTaskRelStoreDTO> atomReqAgentTaskRelStoreDTOList = BeanCopierUtil.copyList(reqAgentTaskRelStoreDTOList, AtomReqAgentTaskRelStoreDTO.class);
        ExecuteDTO executeDTO = atomAgentTaskRelStoreOperatService.saveBatch(atomReqAgentTaskRelStoreDTOList);
        return executeDTO;
    }

    @Override
    public ExecuteDTO deleteBatchByTaskRelStoreNo(ReqAgentTaskRelStoreDTO reqAgentTaskRelStoreDTO) {
        AtomReqAgentTaskRelStoreDTO atomReqAgentTaskRelStoreDTO = BeanCopierUtil.copy(reqAgentTaskRelStoreDTO, AtomReqAgentTaskRelStoreDTO.class);
        ExecuteDTO executeDTO = atomAgentTaskRelStoreOperatService.deleteBatchByTaskRelStoreNo(atomReqAgentTaskRelStoreDTO);
        return executeDTO;
    }

    @Override
    public ExecuteDTO improtStore(ReqAgentTaskRelStoreDTO reqAgentTaskRelStoreDTO) {
        String storeJson = "";
        Boolean isHas = redisBaseUtil.hasKey(AgentKeyConstant.STORE_IMPORT_LIST);
        log.info("*AgentTaskRelStoreOperatServiceImpl-improtStore-isHas：{}", isHas);
        if (isHas) {
            storeJson = (String)redisBaseUtil.get(AgentKeyConstant.STORE_IMPORT_LIST);
        }
        List<String> merchantNolist = JSON.parseArray(storeJson, String.class);
        if (CollectionUtils.isEmpty(merchantNolist)) {
            return ExecuteDTO.error(CommonCode.CODE_10000001, "商家编号信息");
        }
        ExecuteDTO<List<GetStoreListResponse>> storeExecuteDTO = userPublicService.queryMerchantStoreList(merchantNolist);
        if (!storeExecuteDTO.successFlag()) {
            return ExecuteDTO.error(storeExecuteDTO.getStatus(), storeExecuteDTO.getMsg());
        }
        //停用店铺
        List<ResImportErrorStoreDTO> storeCloseList = new ArrayList<>();
        //未停用店铺编号
        List<String> storeNoOpenList = new ArrayList<>();
        //商家编号
        Map<String,String> merchantNoMap = new HashMap<>();
        //商家名称
        Map<String,String> merchantNameMap = new HashMap<>();
        for (GetStoreListResponse getStoreListResponse : storeExecuteDTO.getData()) {
            merchantNolist.remove(getStoreListResponse.getMerchantNo());
            merchantNoMap.put(getStoreListResponse.getStoreNo(), getStoreListResponse.getMerchantNo());
            merchantNameMap.put(getStoreListResponse.getStoreNo(), getStoreListResponse.getMerchantName());
            if (StoreStatusEnum.STOP.getCode().equals(getStoreListResponse.getStoreStatus())){
                ResImportErrorStoreDTO resImportErrorStoreDTO = BeanCopierUtil.copy(getStoreListResponse, ResImportErrorStoreDTO.class);
                resImportErrorStoreDTO.setErrorReason("店铺已禁用");
                storeCloseList.add(resImportErrorStoreDTO);
             } else {
                storeNoOpenList.add(getStoreListResponse.getStoreNo());
            }
        }
        if (CollectionUtils.isNotEmpty(merchantNolist)) {
            for (String merchantNo : merchantNolist) {
                ResImportErrorStoreDTO resImportErrorStoreDTO = new ResImportErrorStoreDTO();
                resImportErrorStoreDTO.setMerchantNo(merchantNo);
                resImportErrorStoreDTO.setErrorReason("商家编号不存在");
                storeCloseList.add(resImportErrorStoreDTO);
            }
        }
        log.info("*AgentTaskRelStoreOperatServiceImpl-improtStore-storeCloseList：{}", storeCloseList);
        Integer isHasErrorStore = NumConstant.ONE;
        if (CollectionUtils.isNotEmpty(storeCloseList)) {
            isHasErrorStore = NumConstant.TWO;
            String storeErrorList = JSON.toJSONString(storeCloseList);
            String key = AgentKeyConstant.STORE_ERROR_LIST + reqAgentTaskRelStoreDTO.getTaskNo();
            redisBaseUtil.set(key, storeErrorList, RedisKeyTTLConstant.EXPIRE_TIME_ONE_HOURS);
        }
        //查看是否有重复的店铺
        AtomReqAgentTaskRelStoreDTO atomReqAgentTaskRelStoreDTO = new AtomReqAgentTaskRelStoreDTO();
        atomReqAgentTaskRelStoreDTO.setTaskNo(reqAgentTaskRelStoreDTO.getTaskNo());
        atomReqAgentTaskRelStoreDTO.setStoreNoList(storeNoOpenList);
        ExecuteDTO<List<String>> storeNoSameExecuteDTO = atomAgentTaskRelStoreAnalysisService.checkSameAgentTaskRelStoreList(atomReqAgentTaskRelStoreDTO);
        if (!storeNoSameExecuteDTO.successFlag()) {
            return ExecuteDTO.error(storeNoSameExecuteDTO.getStatus(), storeNoSameExecuteDTO.getMsg());
        }
        //去除重复的店铺
        List<String> storeNoNewList = null;
        if (CollectionUtils.isNotEmpty(storeNoSameExecuteDTO.getData())) {
            storeNoNewList = storeNoOpenList.stream().filter(item -> !storeNoSameExecuteDTO.getData().contains(item)).collect(Collectors.toList());
        } else {
            storeNoNewList = storeNoOpenList;
        }
        log.info("*AgentTaskRelStoreOperatServiceImpl-improtStore-storeNoNewList：{}", storeNoNewList);
        //插入新的店铺
        if (CollectionUtils.isNotEmpty(storeNoNewList)) {
            List<AtomReqAgentTaskRelStoreDTO> atomReqAgentTaskRelStoreNewDTOList = new ArrayList<>();
            for (String storeNo : storeNoNewList) {
                AtomReqAgentTaskRelStoreDTO atomReqAgentTaskRelStoreNewDTO = new AtomReqAgentTaskRelStoreDTO();
                atomReqAgentTaskRelStoreNewDTO.setTaskRelStoreNo(MarketFormGenerator.genMarketBillNo());
                atomReqAgentTaskRelStoreNewDTO.setTaskNo(reqAgentTaskRelStoreDTO.getTaskNo());
                atomReqAgentTaskRelStoreNewDTO.setMerchantNo(merchantNoMap.get(storeNo));
                atomReqAgentTaskRelStoreNewDTO.setMerchantName(merchantNameMap.get(storeNo));
                atomReqAgentTaskRelStoreNewDTO.setStoreNo(storeNo);
                atomReqAgentTaskRelStoreNewDTO.setStatus(TaskStatusEnum.NO.getCode());
                atomReqAgentTaskRelStoreNewDTOList.add(atomReqAgentTaskRelStoreNewDTO);
            }
            log.info("*AgentTaskRelStoreOperatServiceImpl-improtStore-saveBatch：{}", atomReqAgentTaskRelStoreNewDTOList);
            if (atomReqAgentTaskRelStoreNewDTOList.size() >= NumConstant.ONE) {
                ExecuteDTO saveBatchExecuteDTO = atomAgentTaskRelStoreOperatService.saveBatch(atomReqAgentTaskRelStoreNewDTOList);
                if (!saveBatchExecuteDTO.successFlag()) {
                    return ExecuteDTO.error(saveBatchExecuteDTO.getStatus(), saveBatchExecuteDTO.getMsg());
                }
            }
        }
        return ExecuteDTO.success((Object)isHasErrorStore);
    }

}
