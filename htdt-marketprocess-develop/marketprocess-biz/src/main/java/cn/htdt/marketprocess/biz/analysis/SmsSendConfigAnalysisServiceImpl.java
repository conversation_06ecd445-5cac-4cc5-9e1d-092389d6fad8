package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.dto.utils.ExecuteDTOUtil;
import cn.htdt.common.enums.FestivalEnum;
import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.market.SmsTypeEnum;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.CollectionUtils;
import cn.htdt.common.utils.DateUtil;
import cn.htdt.common.utils.LunarCalendar;
import cn.htdt.common.utils.encry.dto.ResLunarCalendarDTO;
import cn.htdt.marketcenter.dto.request.AtomReqSmsTrafficDTO;
import cn.htdt.marketcenter.dto.response.AtomResSmsTrafficDTO;
import cn.htdt.marketcenter.dto.response.ResDebtSmsDTO;
import cn.htdt.marketprocess.api.analysis.DebtSmsAnalysisService;
import cn.htdt.marketprocess.api.analysis.SmsSendConfigAnalysisService;
import cn.htdt.marketprocess.dao.SmsSendConfigDao;
import cn.htdt.marketprocess.dao.SmsSendConfigRelationDao;
import cn.htdt.marketprocess.domain.SmsSendConfigDomain;
import cn.htdt.marketprocess.dto.request.ReqDebtSmsDTO;
import cn.htdt.marketprocess.dto.request.ReqSmsSendConfigCalculationDaysDTO;
import cn.htdt.marketprocess.dto.request.ReqSmsSendConfigDTO;
import cn.htdt.marketprocess.dto.response.ResSMSLunarCalendarDTO;
import cn.htdt.marketprocess.dto.response.ResSmsSendConfigDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomSmsTrafficAnalysisService;
import cn.htdt.marketprocess.vo.SmsSendConfigVO;
import cn.htdt.usercenter.dto.request.ReqFansFollowDTO;
import cn.htdt.userprocess.api.StoreProcessService;
import cn.htdt.userprocess.api.UcFansProcessService;
import cn.htdt.userprocess.dto.response.StoreInfoResponse;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 短信发送配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
@DubboService
@Slf4j
public class SmsSendConfigAnalysisServiceImpl implements SmsSendConfigAnalysisService {

    @Resource
    private SmsSendConfigDao smsSendConfigDao;

    @Resource
    private SmsSendConfigRelationDao smsSendConfigRelationDao;

    @DubboReference
    private UcFansProcessService ucFansProcessService;

    @DubboReference
    private StoreProcessService storeProcessService;

    @Resource
    private DebtSmsAnalysisService debtSmsAnalysisService;

    @Resource
    private AtomSmsTrafficAnalysisService atomSmsTrafficAnalysisService;

    @Override
    public ExecuteDTO<ExecutePageDTO<ResSmsSendConfigDTO>> selectSmsSendConfigs(ReqSmsSendConfigDTO reqSmsSendConfigDTO) {
        SmsSendConfigDomain copy = BeanCopierUtil.copy(reqSmsSendConfigDTO, SmsSendConfigDomain.class);
        reqSmsSendConfigDTO.setNoPage();
        Page pages = PageHelper.startPage(reqSmsSendConfigDTO.getPageNum(),reqSmsSendConfigDTO.getPageSize(),false);
        List<SmsSendConfigVO> smsSendConfigVOS = smsSendConfigDao.selectSmsSendConfigs(copy);
        List<ResSmsSendConfigDTO> resSmsSendConfigDTOS = JSON.parseArray(JSON.toJSONString(smsSendConfigVOS), ResSmsSendConfigDTO.class);
        return ExecuteDTO.success(new ExecutePageDTO<>(pages.getTotal(),resSmsSendConfigDTOS));
    }

    /**
     * 查询短信营销配置详情
     * @Date 2023/5/27
     * <AUTHOR>
     * @param reqSmsSendConfigDTO
     * @return ExecuteDTO<ResSmsSendConfigDTO>
     **/
    @Override
    public ExecuteDTO<ResSmsSendConfigDTO> getSmsSendConfigInfo(ReqSmsSendConfigDTO reqSmsSendConfigDTO){
        ResSmsSendConfigDTO resSmsSendConfigDTO = new ResSmsSendConfigDTO();
        SmsSendConfigDomain copy = BeanCopierUtil.copy(reqSmsSendConfigDTO, SmsSendConfigDomain.class);
        List<SmsSendConfigDomain> smsSendConfigDomains = smsSendConfigDao.selectByParams(copy);
        //查询配置并且弹框不等于未保存告警配置设置不再提醒情况返回数据
        if(CollectionUtils.isNotEmpty(smsSendConfigDomains) && StringUtils.isNotBlank(smsSendConfigDomains.get(NumConstant.ZERO).getSmsContent())){
            resSmsSendConfigDTO = BeanCopierUtil.copy(smsSendConfigDomains.get(NumConstant.ZERO),ResSmsSendConfigDTO.class);
        }else{
            //默认值
            resSmsSendConfigDTO.setSendCycle(NumConstant.FOUR);
            resSmsSendConfigDTO.setSendTarget(NumConstant.ONE);
            resSmsSendConfigDTO.setSendTargetValue(NumConstant.ONE_HUNDRED);
            resSmsSendConfigDTO.setSmsContentType(NumConstant.ONE);
            resSmsSendConfigDTO.setUseFlag(NumConstant.ONE);
        }
        //如果短信内容为模板，则短信内容要实时查询
        if(resSmsSendConfigDTO.getSmsContentType()==NumConstant.ONE){
            ReqDebtSmsDTO reqDebtSmsDTO = new ReqDebtSmsDTO();
            reqDebtSmsDTO.setStoreNo(reqSmsSendConfigDTO.getStoreNo());
            reqDebtSmsDTO.setSendTarget(resSmsSendConfigDTO.getSendTarget());
            reqDebtSmsDTO.setSendTargetValue(resSmsSendConfigDTO.getSendTargetValue());
            ExecuteDTO<ResDebtSmsDTO> dtoExecuteDTO = debtSmsAnalysisService.getDebtSmsContent(reqDebtSmsDTO);
            resSmsSendConfigDTO.setSmsContent(dtoExecuteDTO.getData().getSmsContent());
            resSmsSendConfigDTO.setSmsPreviewContent(dtoExecuteDTO.getData().getSmsPreviewContent());
        }
        //查询店铺短信余额信息
        AtomReqSmsTrafficDTO atomReqSmsTrafficDTO = new AtomReqSmsTrafficDTO();
        atomReqSmsTrafficDTO.setStoreNo(reqSmsSendConfigDTO.getStoreNo());
        ExecuteDTO<AtomResSmsTrafficDTO> resSmsTrafficDTOExecuteDTO = atomSmsTrafficAnalysisService.getSmsTraffic(atomReqSmsTrafficDTO);
        if(resSmsTrafficDTOExecuteDTO.successFlag() && resSmsTrafficDTOExecuteDTO.getData()!=null){
            resSmsSendConfigDTO.setSmsRemainingNum(resSmsTrafficDTOExecuteDTO.getData().getSmsRemainingNum());
        }
        return ExecuteDTO.success(resSmsSendConfigDTO);
    }

    @Override
    public ExecuteDTO<ResSmsSendConfigDTO> selectSmsSendConfigInfo(ReqSmsSendConfigDTO reqSmsSendConfigDTO) {
        if (StringUtils.isBlank(reqSmsSendConfigDTO.getStoreNo())){
            //通过店铺编号查询生日配置
            return ExecuteDTO.error(CommonCode.CODE_10000001,"店铺编号");
        }

        if (StringUtils.equals(SmsTypeEnum.FESTIVAL_FANS.getCode(),reqSmsSendConfigDTO.getSmsType()) && StringUtils.isBlank(reqSmsSendConfigDTO.getConfigNo())){
            //节日短信需要传短信配置编号
            return ExecuteDTO.error(CommonCode.CODE_10000001,"短信配置编号");
        }

        SmsSendConfigDomain copy = BeanCopierUtil.copy(reqSmsSendConfigDTO, SmsSendConfigDomain.class);
        SmsSendConfigVO smsSendConfigVO = smsSendConfigDao.selectSmsSendConfigInfo(copy);
        ResSmsSendConfigDTO resSmsSendConfigDTO = BeanCopierUtil.copy(smsSendConfigVO, ResSmsSendConfigDTO.class);
        if (StringUtils.equals(SmsTypeEnum.BIRTHDAY_CARE.getCode(),reqSmsSendConfigDTO.getSmsType())){

            //查询店铺维护生日粉丝数
            ReqFansFollowDTO reqFansFollowDTO = new ReqFansFollowDTO();
            reqFansFollowDTO.setStoreNumber(reqSmsSendConfigDTO.getStoreNo());
            ExecuteDTO<Integer> integerExecuteDTO = ucFansProcessService.selectFansBirthdayCount(reqFansFollowDTO);
            if (integerExecuteDTO.successFlag()){
                if (null == resSmsSendConfigDTO){
                    resSmsSendConfigDTO = new ResSmsSendConfigDTO();
                }
                resSmsSendConfigDTO.setBirthdayCount(integerExecuteDTO.getData());
            }
        }

        //查询店铺对外展示名
        /*ExecuteDTO<StoreInfoResponse> storeInfoByStoreNo = storeProcessService.getStoreInfoByStoreNo(reqSmsSendConfigDTO.getStoreNo());
        if (storeInfoByStoreNo.successFlag() && null != storeInfoByStoreNo.getData()){
            resSmsSendConfigDTO.setStoreName(storeInfoByStoreNo.getData().getStoreName());
        }*/

        return ExecuteDTO.success(resSmsSendConfigDTO);
    }

    @Override
    public ExecuteDTO<List<ResSMSLunarCalendarDTO>> smsSendConfigCalculationDays(ReqSmsSendConfigCalculationDaysDTO reqDTO) {
        //返回公历农历和距离天数
        List<ResSMSLunarCalendarDTO> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(reqDTO.getFestivalTypes())){
            return ExecuteDTOUtil.error(CommonCode.CODE_10000001,"节日类型");
        }

        if (null == reqDTO.getSendTime()){
            //reqDTO.setSendTime(LocalTime.of(8,30));
            return ExecuteDTOUtil.error(CommonCode.CODE_10000001,"发送时间");
        }

        for (String key : reqDTO.getFestivalTypes()) {

            FestivalEnum valueByKey = FestivalEnum.getNameByCode(key);
            if (null == valueByKey){
                return ExecuteDTOUtil.error(CommonCode.CODE_10000002,"节日类型");
            }
            //返回值处理
            ResLunarCalendarDTO resLunarCalendarDTO = new ResLunarCalendarDTO();

            String[] valueSplit = valueByKey.getDate().split("-");
            switch (WhetherEnum.getByCode(valueByKey.getType())) {
                case NO:
                    //公历
                    LocalDateTime sol = LocalDateTime.of(LocalDate.now().getYear(), Integer.parseInt(valueSplit[0]), Integer.parseInt(valueSplit[1]),reqDTO.getSendTime().getHour(),reqDTO.getSendTime().getMinute());
                    //如果今年的节日已过,取明年的节日 30分钟执行一次,预留当天等待执行时间 带上提前发送天数
                    //LocalDateTime sendDateTime = sol.plusMinutes(NumConstant.THIRTY).plusDays(reqDTO.getDistanceDay());
                    sol = sol.plusDays(reqDTO.getDistanceDay());

                    if (sol.isBefore(LocalDateTime.now())){
                        //公历年份加一年
                        sol = sol.plusYears(NumConstant.ONE);
                    }

                    //公历发送日期值
                    resLunarCalendarDTO.setSolarDate(sol);
                    //计算公历距离天数
                    resLunarCalendarDTO.setDistanceDays(DateUtil.getInterval(LocalDate.now(), sol.toLocalDate()));
                    break;
                case YES:
                    //农历
                    //先查询今天的农历年份
                    resLunarCalendarDTO = LunarCalendar.convertCalendar(LocalDateTime.now(), WhetherEnum.NO.getCode());
                    //将农历的年拿出来,拼接成农历节日 后续查公历的时间
                    LocalDate lun = LocalDate.of(resLunarCalendarDTO.getLunarDate().getYear(), Integer.parseInt(valueSplit[0]), Integer.parseInt(valueSplit[1]));
                    //查公历的时间
                    resLunarCalendarDTO = LunarCalendar.convertCalendar(lun.atStartOfDay(), WhetherEnum.YES.getCode());
                    //查今天农历节日对应公历时间 of
                    LocalDateTime solarDateComp = resLunarCalendarDTO.getSolarDate();
                    LocalDateTime of = LocalDateTime.of(solarDateComp.getYear(), solarDateComp.getMonthValue(), solarDateComp.getDayOfMonth(), reqDTO.getSendTime().getHour(), reqDTO.getSendTime().getMinute());
                    // 如果今天的可处理时间不在应处理时间之前 则需要再查询此节日对应下一年的公历日期 30分钟执行一次,预留当天等待执行时间 带上提前发送天数
                    //LocalDateTime sendDateTime1 = of.plusMinutes(NumConstant.THIRTY).plusDays(reqDTO.getDistanceDay());
                    of = of.plusDays(reqDTO.getDistanceDay());
                    if (of.isBefore(LocalDateTime.now())) {
                        resLunarCalendarDTO = LunarCalendar.convertCalendar(lun.atStartOfDay(), WhetherEnum.YES.getCode(), NumConstant.ONE);
                    }
                    LocalDateTime dis = resLunarCalendarDTO.getSolarDate().plusDays(reqDTO.getDistanceDay());
                    LocalDateTime solarDate = LocalDateTime.of(dis.getYear(), dis.getMonthValue(), dis.getDayOfMonth(), reqDTO.getSendTime().getHour(), reqDTO.getSendTime().getMinute());
                    resLunarCalendarDTO.setSolarDate(solarDate);
                    resLunarCalendarDTO.setDistanceDays(resLunarCalendarDTO.getDistanceDays()+reqDTO.getDistanceDay());
                    break;
                default:
                    break;
            }

            //增加节日类型和名称
            ResSMSLunarCalendarDTO copy = BeanCopierUtil.copy(resLunarCalendarDTO, ResSMSLunarCalendarDTO.class);
            copy.setFestivalType(valueByKey.getCode());
            copy.setFestivalName(valueByKey.getName());

            list.add(copy);
        }
        return ExecuteDTO.success(list);
    }
}
