package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.base.exception.BaseException;
import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.goods.GoodsFormEnum;
import cn.htdt.common.enums.goods.SeriesTypeEnum;
import cn.htdt.common.enums.goods.SourceTypeEnum;
import cn.htdt.common.enums.market.*;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.ListUtil;
import cn.htdt.goodscenter.dto.request.AtomReqGoodsRealStockDTO;
import cn.htdt.goodscenter.dto.response.AtomResGoodsRealStockDTO;
import cn.htdt.goodsprocess.api.analysis.GoodsAnalysisService;
import cn.htdt.goodsprocess.api.operat.LegacyGoodsCenterService;
import cn.htdt.goodsprocess.dto.request.goods.ReqGoodsDTO;
import cn.htdt.goodsprocess.dto.response.goods.ResGoodsDTO;
import cn.htdt.marketcenter.dto.request.*;
import cn.htdt.marketcenter.dto.response.*;
import cn.htdt.marketprocess.api.analysis.LotteryRuleAnalysisService;
import cn.htdt.marketprocess.biz.utils.CouponInfoUtil;
import cn.htdt.marketprocess.dto.request.ReqLotteryRewardInfoDTO;
import cn.htdt.marketprocess.dto.request.ReqLotteryRuleDTO;
import cn.htdt.marketprocess.dto.request.ReqLotteryStoreRuleDTO;
import cn.htdt.marketprocess.dto.response.ResLotteryRewardGoodsRelationDTO;
import cn.htdt.marketprocess.dto.response.ResLotteryRewardInfoDTO;
import cn.htdt.marketprocess.dto.response.ResLotteryRuleDTO;
import cn.htdt.marketprocess.dto.response.ResLotteryStoreRuleDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.*;
import cn.htdt.marketprocess.legacycenter.api.operat.AtomPromotionStoreRelationOperatService;
import cn.htdt.userprocess.api.UserPublicService;
import cn.htdt.userprocess.dto.publicDto.response.GetStoreListResponse;
import cn.htdt.userprocess.dto.request.MerchantStoreRequest;
import cn.htdt.userprocess.dto.response.MerchantStoreResponse;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@DubboService
public class LotteryRuleAnalysisServiceImpl implements LotteryRuleAnalysisService {
    /**
     * 抽奖活动
     */
    @Resource
    private AtomLotteryRuleAnalysisService atomLotteryRuleAnalysisService;
    /**
     * 活动店铺关联
     */
    @Resource
    private AtomPromotionStoreRelationOperatService atomPromotionStoreRelationOperatService;
    /**
     * 奖品商品关联
     */
    @Resource
    private AtomPromotionRewardGoodsRelationAnalysisService atomPromotionRewardGoodsRelationAnalysisService;
    /**
     * 活动中奖纪录
     */
    @Resource
    private AtomLotteryDrawRecordAnalysisService atomLotteryDrawRecordAnalysisService;
    /**
     * 活动奖品信息
     */
    @Resource
    private AtomPromotionRewardInfoAnalysisService atomPromotionRewardInfoAnalysisService;
    /**
     * 活动店铺规则
     */
    @Resource
    private AtomPromotionStoreRuleAnalysisService atomPromotionStoreRuleAnalysisService;
    /**
     * 活动店铺规则店铺详情
     */
    @Resource
    private AtomPromotionStoreDetailAnalysisService atomPromotionStoreDetailAnalysisService;
    /**
     * 获取所有店铺用
     */
    @DubboReference
    private UserPublicService userPublicService;
    /**
     * 商品库存center
     */
    @DubboReference
    private LegacyGoodsCenterService legacyGoodsCenterService;

    @DubboReference
    private GoodsAnalysisService goodsAnalysisService;

    @Autowired
    private CouponInfoUtil couponInfoUtil;


    @Override
    public ExecuteDTO<ResLotteryRuleDTO> viewLotteryInfo(ReqLotteryRuleDTO reqLotteryRuleDTO) {
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-viewLotteryInfo-params-start");
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-viewLotteryInfo-params={}", JSON.toJSONString(reqLotteryRuleDTO));
        AtomReqLotteryRuleDTO atomReqLotteryRuleDTO = BeanCopierUtil.copy(reqLotteryRuleDTO, AtomReqLotteryRuleDTO.class);
        ExecuteDTO<AtomResLotteryRuleDTO> executeDTO = atomLotteryRuleAnalysisService.viewLotteryInfo(atomReqLotteryRuleDTO);
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-viewLotteryInfo-return={}", JSON.toJSONString(executeDTO));
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-viewLotteryInfo-end");
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResLotteryRuleDTO resLotteryRuleDTO = BeanCopierUtil.copy(executeDTO.getData(), ResLotteryRuleDTO.class);
        return ExecuteDTO.success(resLotteryRuleDTO);
    }

//    202503 东启
    @Override
    public ExecuteDTO<ExecutePageDTO<ResLotteryRuleDTO>> getLotteryInfoPage(ReqLotteryRuleDTO reqLotteryRuleDTO) {
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getLotteryInfoPage-params-start");
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getLotteryInfoPage-params={}", JSON.toJSONString(reqLotteryRuleDTO));
        AtomReqLotteryRuleDTO atomReqLotteryRuleDTO = BeanCopierUtil.copy(reqLotteryRuleDTO, AtomReqLotteryRuleDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResLotteryRuleDTO>> executeDTO = atomLotteryRuleAnalysisService.getLotteryInfoPage(atomReqLotteryRuleDTO);
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getLotteryInfoPage-return={}", JSON.toJSONString(executeDTO));
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getLotteryInfoPage-end");
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        //参数转换
        ExecutePageDTO<ResLotteryRuleDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResLotteryRuleDTO> resLotteryRuleDTOS = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResLotteryRuleDTO.class);
        // 赋值中奖纪录数
        this.setLotteryRecordCount(resLotteryRuleDTOS);
        // 设置活动状态
        this.setLotteryStatus(resLotteryRuleDTOS);
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getLotteryInfoPage-设置活动状态-return={}", JSON.toJSONString(resLotteryRuleDTOS));

        executePageDTO.setRows(resLotteryRuleDTOS);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    private void setLotteryStatus(List<ResLotteryRuleDTO> resLotteryRuleDTOS) {
        if (CollectionUtils.isNotEmpty(resLotteryRuleDTOS)) {
            resLotteryRuleDTOS.forEach(ruleDto -> {
                // 草稿状态
                if (StringUtils.isNotEmpty(ruleDto.getStatus()) && PromotionStatusEnum.ONLY_SAVED.getCode().equals(ruleDto.getStatus())) {
                    ruleDto.setStatus(PromotionStatusEnum.ONLY_SAVED.getCode());
                    // 其他状态
                } else if (ruleDto.getEffectiveTime() != null && ruleDto.getInvalidTime() != null) {
                    //未开始
                    if (ruleDto.getEffectiveTime().isAfter(LocalDateTime.now())) {
                        ruleDto.setStatus(PromotionStatusEnum.NOT_STARTED.getCode());
//                        this.setEffectivePeriodTypeLotteryStatus(ruleDto);
                        //进行中
                    } else if ((ruleDto.getEffectiveTime().isBefore(LocalDateTime.now()) || ruleDto.getEffectiveTime().equals(LocalDateTime.now()))
                            && (ruleDto.getInvalidTime().equals(LocalDateTime.now()) || ruleDto.getInvalidTime().isAfter(LocalDateTime.now()))) {
                        ruleDto.setStatus(PromotionStatusEnum.IN_PROGRESS.getCode());
//                        this.setEffectivePeriodTypeLotteryStatus(ruleDto);
                        //已结束
                    } else if (ruleDto.getInvalidTime().isBefore(LocalDateTime.now())) {
                        ruleDto.setStatus(PromotionStatusEnum.EXPIRED.getCode());
//                        this.setEffectivePeriodTypeLotteryStatus(ruleDto);
                    }
                }
            });
        }
    }

    private void setOneLotteryStatus(ResLotteryRuleDTO ruleDto) {
        if (ruleDto != null) {
            // 草稿状态
            if (StringUtils.isNotEmpty(ruleDto.getStatus()) && PromotionStatusEnum.ONLY_SAVED.getCode().equals(ruleDto.getStatus())) {
                ruleDto.setStatus(PromotionStatusEnum.ONLY_SAVED.getCode());
                // 其他状态
            } else if (ruleDto.getEffectiveTime() != null && ruleDto.getInvalidTime() != null) {
                //未开始
                if (ruleDto.getEffectiveTime().isAfter(LocalDateTime.now())) {
                    ruleDto.setStatus(PromotionStatusEnum.NOT_STARTED.getCode());
//                    this.setEffectivePeriodTypeLotteryStatus(ruleDto);
                    //进行中
                } else if ((ruleDto.getEffectiveTime().isBefore(LocalDateTime.now()) || ruleDto.getEffectiveTime().equals(LocalDateTime.now()))
                        && (ruleDto.getInvalidTime().equals(LocalDateTime.now()) || ruleDto.getInvalidTime().isAfter(LocalDateTime.now()))) {
                    ruleDto.setStatus(PromotionStatusEnum.IN_PROGRESS.getCode());
//                    this.setEffectivePeriodTypeLotteryStatus(ruleDto);
                    //已结束
                } else if (ruleDto.getInvalidTime().isBefore(LocalDateTime.now())) {
                    ruleDto.setStatus(PromotionStatusEnum.EXPIRED.getCode());
//                    this.setEffectivePeriodTypeLotteryStatus(ruleDto);
                }
            }
        }
    }

    private void setEffectivePeriodTypeLotteryStatus(ResLotteryRuleDTO ruleDto) {
        // 若是时间段
        if (StringUtils.isNotBlank(ruleDto.getEffectivePeriodType()) && EffectivePeriodTypeEnum.EFFECTIVE_PERIOD_TYPE_TWO.getCode().equals(ruleDto.getEffectivePeriodType())) {
            if (ruleDto.getDailyStartTime().isAfter(LocalTime.now())) {
                ruleDto.setStatus(PromotionStatusEnum.NOT_STARTED.getCode());
                //进行中
            } else if ((ruleDto.getDailyStartTime().isBefore(LocalTime.now()) || ruleDto.getDailyStartTime().equals(LocalTime.now()))
                    && (ruleDto.getDailyEndTime().equals(LocalTime.now()) || ruleDto.getDailyEndTime().isAfter(LocalTime.now()))) {
                ruleDto.setStatus(PromotionStatusEnum.IN_PROGRESS.getCode());
                //已结束
            } else if (ruleDto.getDailyEndTime().isBefore(LocalTime.now())) {
                ruleDto.setStatus(PromotionStatusEnum.EXPIRED.getCode());
            }
        }
    }


    public ExecuteDTO setStatus(List<ResLotteryRuleDTO> resLotteryRuleDTOS) {
        this.setLotteryStatus(resLotteryRuleDTOS);
        return ExecuteDTO.success();
    }

    private void setLotteryRecordCount(List<ResLotteryRuleDTO> resLotteryRuleDTOS) {
        if (CollectionUtils.isNotEmpty(resLotteryRuleDTOS)) {
            AtomReqLotteryDrawRecordDTO dto = new AtomReqLotteryDrawRecordDTO();
            Set<String> promotionNos = resLotteryRuleDTOS.stream().map(ResLotteryRuleDTO::getPromotionNo).collect(Collectors.toSet());
            dto.setPromotionNos(new ArrayList(promotionNos));
            ExecuteDTO<List<AtomResLotteryDrawRecordDTO>> recordExecute = atomLotteryDrawRecordAnalysisService.batchGetLotteryDrawRecordCount(dto);
            log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getLotteryInfoPage-recordExecute={}", JSON.toJSONString(recordExecute));
            if (recordExecute != null && recordExecute.successFlag() && CollectionUtils.isNotEmpty(recordExecute.getData())) {
                List<AtomResLotteryDrawRecordDTO> list = recordExecute.getData();
                Map<String, AtomResLotteryDrawRecordDTO> recordDTOMap = list.stream().collect(
                        Collectors.toMap(AtomResLotteryDrawRecordDTO::getPromotionNo, (p) -> p));
                resLotteryRuleDTOS.forEach(ruleDto -> {
                    AtomResLotteryDrawRecordDTO lotteryDrawRecordDTO = recordDTOMap.get(ruleDto.getPromotionNo());
                    if (lotteryDrawRecordDTO != null) {
                        ruleDto.setRecordCount(lotteryDrawRecordDTO.getRecordCount());
                    }
                });
            }
        }
    }

    // 赋值是否显示上下架
    private void setLotteryShowShelf(List<ResLotteryRuleDTO> resLotteryRuleDTOS, String storeNo) {
        if (CollectionUtils.isNotEmpty(resLotteryRuleDTOS)) {
            AtomReqPromotionStoreRuleDTO dto = new AtomReqPromotionStoreRuleDTO();
            Set<String> promotionNos = resLotteryRuleDTOS.stream().map(ResLotteryRuleDTO::getPromotionNo).collect(Collectors.toSet());
            dto.setPromotionNoList(new ArrayList(promotionNos));
            // 获取活动类型
//            dto.setStoreType(StoreTypeEnum.STORE_TYPE_TWO.getCode());
            ExecuteDTO<List<AtomResPromotionStoreRuleDTO>> ruleExecute = atomPromotionStoreRuleAnalysisService.getPromotionStoreRuleList(dto);
            log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getLotteryInfoPage-setLotteryShowShelf={}", JSON.toJSONString(ruleExecute));
            if (ruleExecute != null && ruleExecute.successFlag() && CollectionUtils.isNotEmpty(ruleExecute.getData())) {
                List<AtomResPromotionStoreRuleDTO> list = ruleExecute.getData();
                if (CollectionUtils.isNotEmpty(list) && StringUtils.isNotBlank(storeNo)) {
                    // 根据storeType分组
                    Map<String, List<AtomResPromotionStoreRuleDTO>> ruleMap = list.stream().collect(Collectors.groupingBy(AtomResPromotionStoreRuleDTO::getStoreType));
                    List<AtomResPromotionStoreRuleDTO> list1 = ruleMap.get(StoreTypeEnum.STORE_TYPE_ONE.getCode());
                    List<AtomResPromotionStoreRuleDTO> list2 = ruleMap.get(StoreTypeEnum.STORE_TYPE_TWO.getCode());
                    // 活动类型为报名活动
                    if (CollectionUtils.isNotEmpty(list2)) {
                        for (AtomResPromotionStoreRuleDTO ruleDTO : list2) {
                            AtomReqPromotionStoreDetailDTO detailDTO = new AtomReqPromotionStoreDetailDTO();
                            detailDTO.setPromotionNo(ruleDTO.getEnrollNo());
                            detailDTO.setStoreNo(storeNo);
                            log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getLotteryInfoPage-setLotteryShowShelf-入参={}", JSON.toJSONString(detailDTO));
                            ExecuteDTO<Integer> countExecute = atomPromotionStoreDetailAnalysisService.getPromotionStoreDetailCount(detailDTO);
                            log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getLotteryInfoPage-setLotteryShowShelf-countExecute={}", JSON.toJSONString(countExecute));
                            if (countExecute.successFlag()) {
                                for (ResLotteryRuleDTO resLotteryRuleDTO : resLotteryRuleDTOS) {
                                    if (resLotteryRuleDTO.getPromotionNo().equals(ruleDTO.getPromotionNo())
                                            && storeNo.equals(resLotteryRuleDTO.getStoreNo())) {
                                        resLotteryRuleDTO.setShowShelf(countExecute.getData() == null ? NumConstant.ZERO : countExecute.getData());
                                        resLotteryRuleDTO.setStoreType(StoreTypeEnum.STORE_TYPE_TWO.getCode());
                                    }
                                }
                            }
                        }
                    }
                    // 活动类型为全部店铺
                    if (CollectionUtils.isNotEmpty(list1)) {
                        for (AtomResPromotionStoreRuleDTO ruleDTO : list1) {
                            for (ResLotteryRuleDTO resLotteryRuleDTO : resLotteryRuleDTOS) {
                                if (resLotteryRuleDTO.getPromotionNo().equals(ruleDTO.getPromotionNo())) {
                                    resLotteryRuleDTO.setStoreType(StoreTypeEnum.STORE_TYPE_ONE.getCode());
                                }
                            }
                        }
                    }
                }
            }
        }
    }


    @Override
    public ExecuteDTO<ExecutePageDTO<ResLotteryRuleDTO>> getLotteryInfoMerchantStorePage(ReqLotteryRuleDTO reqLotteryRuleDTO) {
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getLotteryInfoMerchantStorePage-params-start");
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getLotteryInfoMerchantStorePage-params={}", JSON.toJSONString(reqLotteryRuleDTO));
        // 判断是商家还是店铺，需要入关联

        AtomReqLotteryRuleDTO atomReqLotteryRuleDTO = BeanCopierUtil.copy(reqLotteryRuleDTO, AtomReqLotteryRuleDTO.class);

        if (SourceTypeEnum.SOURCE_TYPE_1002.getCode().equals(reqLotteryRuleDTO.getSourceType())) {//1002  202503东启
            if(StringUtils.isNotEmpty(reqLotteryRuleDTO.getStoreNo())){

            }else{
                atomReqLotteryRuleDTO.setStoreNo(StringUtils.EMPTY);
            }

        }
        ExecuteDTO<ExecutePageDTO<AtomResLotteryRuleDTO>> executeDTO = atomLotteryRuleAnalysisService.getLotteryInfoMerchantStorePage(atomReqLotteryRuleDTO);
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getLotteryInfoMerchantStorePage-return={}", JSON.toJSONString(executeDTO));
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getLotteryInfoMerchantStorePage-end");
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        //参数转换
        ExecutePageDTO<ResLotteryRuleDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResLotteryRuleDTO> resLotteryRuleDTOS = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResLotteryRuleDTO.class);
        if (PlatformSourceEnum.PLATFORM_APP.getCode().equals(reqLotteryRuleDTO.getCreateSource()) && (PromotionStatusEnum.IN_PROGRESS.getCode().equals(reqLotteryRuleDTO.getStatus()) || PromotionStatusEnum.EXPIRED.getCode().equals(reqLotteryRuleDTO.getStatus()))) {
            // bossApp 列表添加了抽奖总数
            this.setLotteryDrawRecord(resLotteryRuleDTOS);
        }
        // 赋值中奖纪录数
        this.setLotteryRecordCount(resLotteryRuleDTOS);
        // 设置活动状态
        this.setLotteryStatus(resLotteryRuleDTOS);
        // 设置店铺名称
        this.setStoreNames(resLotteryRuleDTOS);
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getLotteryInfoMerchantStorePage-设置活动状态-return={}", JSON.toJSONString(resLotteryRuleDTOS));

        executePageDTO.setRows(resLotteryRuleDTOS);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    /**
     * 塞入该活动抽奖次数 bossApp需求
     */
    private void setLotteryDrawRecord(List<ResLotteryRuleDTO> resLotteryRuleDTOS) {
        if (CollectionUtils.isNotEmpty(resLotteryRuleDTOS)) {
            resLotteryRuleDTOS.forEach(resLotteryRule -> {
                AtomReqLotteryDrawNumDTO reqLotteryDrawNumDTO = BeanCopierUtil.copy(resLotteryRule, AtomReqLotteryDrawNumDTO.class);
                ExecuteDTO<AtomResLotteryDrawNumDTO> atomResLotteryDrawNumDTOExecuteDTO = atomLotteryDrawRecordAnalysisService.getDrawNumByPromotion(reqLotteryDrawNumDTO);
                if (!atomResLotteryDrawNumDTOExecuteDTO.successFlag() || atomResLotteryDrawNumDTOExecuteDTO.getData() == null) {
                    throw new BaseException(CommonCode.CODE_10000003);
                }
                resLotteryRule.setAllLotteryNum(atomResLotteryDrawNumDTOExecuteDTO.getData().getAllLotteryNum());
            });
        }
    }

    private void setStoreNames(List<ResLotteryRuleDTO> resLotteryRuleDTOS) {
        if (CollectionUtils.isNotEmpty(resLotteryRuleDTOS)) {
            List<String> storeNos = new ArrayList<>();
            resLotteryRuleDTOS.forEach(ruleDto -> {
                if (StringUtils.isNotEmpty(ruleDto.getStoreNo())) {
                    storeNos.add(ruleDto.getStoreNo());
                }
            });
            if (CollectionUtils.isNotEmpty(storeNos)) {
                log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getLotteryInfoMerchantStorePage-setStoreNames-storeNos-入参={}", JSON.toJSONString(storeNos));
                ExecuteDTO<List<GetStoreListResponse>> storeExecuteDTO = userPublicService.queryStoreList(storeNos);
                log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getLotteryInfoMerchantStorePage-setStoreNames-storeExecuteDTO={}", JSON.toJSONString(storeExecuteDTO));
                if (storeExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(storeExecuteDTO.getData())) {
                    for (ResLotteryRuleDTO resLotteryRuleDTO : resLotteryRuleDTOS) {
                        if (StringUtils.isNotEmpty(resLotteryRuleDTO.getStoreNo())) {
                            for (GetStoreListResponse getStoreListResponse : storeExecuteDTO.getData()) {
                                if (StringUtils.isNotEmpty(getStoreListResponse.getStoreNo()) && resLotteryRuleDTO.getStoreNo().equals(getStoreListResponse.getStoreNo())) {
                                    resLotteryRuleDTO.setStoreName(getStoreListResponse.getStoreName());
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public ExecuteDTO<ExecutePageDTO<ResLotteryRuleDTO>> getMerchantJoinLotteryInfoPage(ReqLotteryRuleDTO reqLotteryRuleDTO) {
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getMerchantJoinLotteryInfoPage-params-start");
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getMerchantJoinLotteryInfoPage-params={}", JSON.toJSONString(reqLotteryRuleDTO));
        // @TODO获取商家下所有店铺信息
        //商家角色，先查询商家下的店铺
        MerchantStoreRequest merchantStoreRequest = new MerchantStoreRequest();
        merchantStoreRequest.setMerchantNo(reqLotteryRuleDTO.getMerchantNo());
        List<String> storeNoList = new ArrayList<>();
        log.info(String.format("userPublicService.queryMerchantStore merchantStoreRequest入参:%s", JSON.toJSONString(merchantStoreRequest)));
        ExecuteDTO<List<MerchantStoreResponse>> merchantStoreDto = userPublicService.queryMerchantStore(merchantStoreRequest);
        log.info(String.format("merchantStoreDto关系结果:%s", JSON.toJSONString(merchantStoreDto)));
        if (merchantStoreDto.successFlag() && CollectionUtils.isNotEmpty(merchantStoreDto.getData())) {
            merchantStoreDto.getData().forEach(agentTaskDTO -> {
                storeNoList.add(agentTaskDTO.getStoreNo());
            });
        }
        ExecutePageDTO<ResLotteryRuleDTO> executePageDTO = new ExecutePageDTO<>();
        if (CollectionUtils.isNotEmpty(storeNoList)) {
            // 判断查询类型是全部店铺的平台活动是否存在，不存在直接保存关联-商家
            AtomReqLotteryStoreRuleDTO atomReqLotteryStoreRuleDTO = BeanCopierUtil.copy(reqLotteryRuleDTO, AtomReqLotteryStoreRuleDTO.class);
            atomReqLotteryStoreRuleDTO.setStoreNoList(storeNoList);
            atomReqLotteryStoreRuleDTO.setSourceType(SourceTypeEnum.SOURCE_TYPE_1001.getCode());
            log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getMerchantJoinLotteryInfoPage-判断查询类型是全部店铺的平台活动是否存在，不存在直接保存关联-商家={}", JSON.toJSONString(atomReqLotteryStoreRuleDTO));
            ExecuteDTO lotteryExecuteDTO = atomPromotionStoreRelationOperatService.boolLotteryStoreRelationMerchant(atomReqLotteryStoreRuleDTO);
            if (!lotteryExecuteDTO.successFlag()) {
                return ExecuteDTO.error(lotteryExecuteDTO.getStatus(), lotteryExecuteDTO.getMsg());
            }
        }
        AtomReqLotteryRuleDTO atomReqLotteryRuleDTO = BeanCopierUtil.copy(reqLotteryRuleDTO, AtomReqLotteryRuleDTO.class);
        atomReqLotteryRuleDTO.setStoreNos(storeNoList);
        ExecuteDTO<ExecutePageDTO<AtomResLotteryRuleDTO>> executeDTO = atomLotteryRuleAnalysisService.getMerchantJoinByParams(atomReqLotteryRuleDTO);
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getMerchantJoinLotteryInfoPage-return={}", JSON.toJSONString(executeDTO));
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getMerchantJoinLotteryInfoPage-end");
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        //参数转换
        List<ResLotteryRuleDTO> resLotteryRuleDTOS = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResLotteryRuleDTO.class);
        // 赋值中奖纪录数
        this.setLotteryRecordCount(resLotteryRuleDTOS);
        // 设置活动状态
        this.setLotteryStatus(resLotteryRuleDTOS);
        // 赋值是否显示山下架
//        if (CollectionUtils.isNotEmpty(storeNoList)) {
//            for(String storeNo: storeNoList) {
//                this.setLotteryShowShelf(resLotteryRuleDTOS, storeNo);
//            }
//        }
        executePageDTO.setRows(resLotteryRuleDTOS);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    @Override
    public ExecuteDTO<ExecutePageDTO<ResLotteryRuleDTO>> getStoreJoinLotteryInfoPage(ReqLotteryRuleDTO reqLotteryRuleDTO) {
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getStoreJoinLotteryInfoPage-params-start");
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getStoreJoinLotteryInfoPage-params={}", JSON.toJSONString(reqLotteryRuleDTO));
        // 判断查询类型是全部店铺的平台活动是否存在，不存在直接保存关联-商家
        AtomReqLotteryStoreRuleDTO atomReqLotteryStoreRuleDTO = BeanCopierUtil.copy(reqLotteryRuleDTO, AtomReqLotteryStoreRuleDTO.class);
        atomReqLotteryStoreRuleDTO.setSourceType(SourceTypeEnum.SOURCE_TYPE_1001.getCode());
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getStoreJoinLotteryInfoPage-判断查询类型是全部店铺的平台活动是否存在，不存在直接保存关联-店铺={}", JSON.toJSONString(atomReqLotteryStoreRuleDTO));
        ExecuteDTO lotteryExecuteDTO = atomPromotionStoreRelationOperatService.boolLotteryStoreRelationStore(atomReqLotteryStoreRuleDTO);
        if (!lotteryExecuteDTO.successFlag()) {
            return ExecuteDTO.error(lotteryExecuteDTO.getStatus(), lotteryExecuteDTO.getMsg());
        }
        AtomReqLotteryRuleDTO atomReqLotteryRuleDTO = BeanCopierUtil.copy(reqLotteryRuleDTO, AtomReqLotteryRuleDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResLotteryRuleDTO>> executeDTO = atomLotteryRuleAnalysisService.getStoreJoinByParams(atomReqLotteryRuleDTO);
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getStoreJoinLotteryInfoPage-return={}", JSON.toJSONString(executeDTO));
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getStoreJoinLotteryInfoPage-end");
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        //参数转换
        ExecutePageDTO<ResLotteryRuleDTO> executePageDTO = new ExecutePageDTO<>();
        List<ResLotteryRuleDTO> resLotteryRuleDTOS = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResLotteryRuleDTO.class);
        // 赋值中奖纪录数
        this.setLotteryRecordCount(resLotteryRuleDTOS);
        // 设置活动状态
        this.setLotteryStatus(resLotteryRuleDTOS);
        // 赋值是否显示山下架
        if (StringUtils.isNotEmpty(reqLotteryRuleDTO.getStoreNo())) {
            this.setLotteryShowShelf(resLotteryRuleDTOS, reqLotteryRuleDTO.getStoreNo());
        }

        executePageDTO.setRows(resLotteryRuleDTOS);
        executePageDTO.setTotal(executeDTO.getData().getTotal());
        return ExecuteDTO.success(executePageDTO);
    }

    @Override
    public ExecuteDTO<ResLotteryRuleDTO> getLotteryRule(ReqLotteryRuleDTO reqLotteryRuleDTO) {
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getLotteryRule-params-start");
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getLotteryRule-params={}", JSON.toJSONString(reqLotteryRuleDTO));
        AtomReqLotteryRuleDTO atomReqLotteryRuleDTO = BeanCopierUtil.copy(reqLotteryRuleDTO, AtomReqLotteryRuleDTO.class);
        ExecuteDTO<AtomResLotteryRuleDTO> executeDTO = atomLotteryRuleAnalysisService.getLotteryRule(atomReqLotteryRuleDTO);
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getLotteryRule-return={}", JSON.toJSONString(executeDTO));
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getLotteryRule-end");
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResLotteryRuleDTO resLotteryRuleDTO = BeanCopierUtil.copy(executeDTO.getData(), ResLotteryRuleDTO.class);
        // 设置活动状态
        this.setOneLotteryStatus(resLotteryRuleDTO);
        return ExecuteDTO.success(resLotteryRuleDTO);
    }

    @Override
    public ExecuteDTO<ResLotteryStoreRuleDTO> getLotteryStore(ReqLotteryStoreRuleDTO reqLotteryStoreRuleDTO) {
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getLotteryStore-params-start");
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getLotteryStore-params={}", JSON.toJSONString(reqLotteryStoreRuleDTO));
        AtomReqLotteryStoreRuleDTO atomReqLotteryStoreRuleDTO = BeanCopierUtil.copy(reqLotteryStoreRuleDTO, AtomReqLotteryStoreRuleDTO.class);
        ExecuteDTO<AtomResLotteryStoreRuleDTO> executeDTO = atomLotteryRuleAnalysisService.getLotteryStore(atomReqLotteryStoreRuleDTO);
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getLotteryStore-return={}", JSON.toJSONString(executeDTO));
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getLotteryStore-end");
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResLotteryStoreRuleDTO resLotteryStoreRuleDTO = BeanCopierUtil.copy(executeDTO.getData(), ResLotteryStoreRuleDTO.class);
        return ExecuteDTO.success(resLotteryStoreRuleDTO);
    }

    @Override
    public ExecuteDTO<List<ResLotteryRewardInfoDTO>> getLotteryReward(ReqLotteryRewardInfoDTO reqLotteryRewardInfoDTO) {
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getLotteryReward-params-start");
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getLotteryReward-params={}", JSON.toJSONString(reqLotteryRewardInfoDTO));
        AtomReqLotteryRewardInfoDTO atomReqLotteryRewardInfoDTO = BeanCopierUtil.copy(reqLotteryRewardInfoDTO, AtomReqLotteryRewardInfoDTO.class);
        ExecuteDTO<List<AtomResLotteryRewardInfoDTO>> executeDTO = atomLotteryRuleAnalysisService.getLotteryReward(atomReqLotteryRewardInfoDTO);
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getLotteryReward-return={}", JSON.toJSONString(executeDTO));
        log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-getLotteryReward-end");
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        // 获取关联商品信息和自建商品库存信息
        List<AtomResLotteryRewardInfoDTO> list = executeDTO.getData();
        List<ResLotteryRewardInfoDTO> resultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            resultList = BeanCopierUtil.copyList(list, ResLotteryRewardInfoDTO.class);
            for (ResLotteryRewardInfoDTO dto : resultList) {
                // 实物礼物、优惠券、话费-查询关联商品
                if (LotteryRewardTypeEnum.REWARD_TYPE_ONE.getCode().equals(dto.getRewardType())
                        || LotteryRewardTypeEnum.REWARD_TYPE_TWO.getCode().equals(dto.getRewardType())
                        || LotteryRewardTypeEnum.REWARD_TYPE_FOUR.getCode().equals(dto.getRewardType())) {
                    AtomReqLotteryRewardGoodsRelationDTO goodsRelationDTO = new AtomReqLotteryRewardGoodsRelationDTO();
                    goodsRelationDTO.setRewardNo(dto.getRewardNo());
                    ExecuteDTO<List<AtomResLotteryRewardGoodsRelationDTO>> goodsRelationExecute = atomPromotionRewardGoodsRelationAnalysisService.listPromotionRewardGoodsRelation(goodsRelationDTO);
                    if (goodsRelationExecute.successFlag() && goodsRelationExecute.getData() != null) {
                        List<ResLotteryRewardGoodsRelationDTO> resLotteryRewardGoodsRelationDTOS = BeanCopierUtil.copyList(goodsRelationExecute.getData(), ResLotteryRewardGoodsRelationDTO.class);
                        //查询商品信息
                        List<String> goodsNoList = resLotteryRewardGoodsRelationDTOS.stream().map(atomResLotteryRewardGoodsRelationDTO -> atomResLotteryRewardGoodsRelationDTO.getGoodsNo()).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(goodsNoList)) {
                            ReqGoodsDTO goodsDTO = new ReqGoodsDTO();
                            goodsDTO.setNoPage();
                            goodsDTO.setGoodsNos(goodsNoList);
                            ExecuteDTO<ExecutePageDTO<ResGoodsDTO>> goodsExecuteDTO = goodsAnalysisService.getGoodsPage(goodsDTO);
                            if (goodsExecuteDTO.successFlag()) {
                                List<ResGoodsDTO> goodsList = goodsExecuteDTO.getData().getRows();
                                if (CollectionUtils.isNotEmpty(goodsList)) {
                                    List<ResGoodsDTO> resGoodsDTOList = BeanCopierUtil.copyList(goodsList, ResGoodsDTO.class);
                                    List<ResGoodsDTO> parentGoodsList = resGoodsDTOList.stream().filter(goods -> SeriesTypeEnum.PARENT_GOODS.getCode().equals(goods.getSeriesType())).collect(Collectors.toList());
                                    //获取主品商品库存集合（取子品库存的区间）
                                    couponInfoUtil.getSeriesGoodsStockList(parentGoodsList, false);
                                    // 商品map
                                    Map<String, ResGoodsDTO> goodsDTOMap = resGoodsDTOList.stream().collect(Collectors.toMap(ResGoodsDTO::getGoodsNo, ResGoodsDTO -> ResGoodsDTO));
                                    // 指定商品
                                    //组装商品数据
                                    resLotteryRewardGoodsRelationDTOS = resLotteryRewardGoodsRelationDTOS.stream().map(resLotteryRewardGoodsRelationDTO -> {
                                        ResGoodsDTO resGoodsDTO = goodsDTOMap.get(resLotteryRewardGoodsRelationDTO.getGoodsNo());
                                        if (resGoodsDTO != null) {
                                            resLotteryRewardGoodsRelationDTO.setGoodsForm(resGoodsDTO.getGoodsForm());
                                            resLotteryRewardGoodsRelationDTO.setGoodsName(resGoodsDTO.getGoodsName());
                                            resLotteryRewardGoodsRelationDTO.setGoodsFormValue(GoodsFormEnum.getByCode(resGoodsDTO.getGoodsForm()).getName());
                                            resLotteryRewardGoodsRelationDTO.setRetailPrice(resGoodsDTO.getRetailPrice());
                                            resLotteryRewardGoodsRelationDTO.setMinRetailPrice(resGoodsDTO.getMinRetailPrice());
                                            resLotteryRewardGoodsRelationDTO.setMaxRetailPrice(resGoodsDTO.getMaxRetailPrice());
                                            resLotteryRewardGoodsRelationDTO.setCategoryName(resGoodsDTO.getCategoryName());
                                            resLotteryRewardGoodsRelationDTO.setBrandName(resGoodsDTO.getBrandName());
                                            resLotteryRewardGoodsRelationDTO.setSupplierCode(resGoodsDTO.getSupplierCode());
                                            resLotteryRewardGoodsRelationDTO.setSupplierName(resGoodsDTO.getSupplierName());
                                            resLotteryRewardGoodsRelationDTO.setCanSaleStockNum(resGoodsDTO.getCanSaleStockNum());
                                            resLotteryRewardGoodsRelationDTO.setMainPictureUrl(resGoodsDTO.getMainPictureUrl());
                                            resLotteryRewardGoodsRelationDTO.setSalesVolume(resGoodsDTO.getSalesVolume());
                                        }
                                        return resLotteryRewardGoodsRelationDTO;
                                    }).collect(Collectors.toList());

                                }
                            }
                        }
                        dto.setRelationDTOList(resLotteryRewardGoodsRelationDTOS);
                    }
                    // 实物礼物、话费-查询可售库存
                    if (LotteryRewardTypeEnum.REWARD_TYPE_ONE.getCode().equals(dto.getRewardType())
                            || LotteryRewardTypeEnum.REWARD_TYPE_FOUR.getCode().equals(dto.getRewardType())) {
                        // 20230928蛋品  lixiang 多单位商品管理 优化
                        // if (CollectionUtils.isNotEmpty(goodsRelationExecute.getData())) {
                        //     AtomReqGoodsRealStockDTO goodsRealStockDTO = new AtomReqGoodsRealStockDTO();
                        //     goodsRealStockDTO.setGoodsNo(goodsRelationExecute.getData().get(NumConstant.ZERO).getGoodsNo());
                        //     ExecuteDTO<AtomResGoodsRealStockDTO> goodsRealStockExecute = legacyGoodsCenterService.getGoodsRealStock(goodsRealStockDTO);
                        //     if (goodsRealStockExecute.successFlag() && goodsRealStockExecute.getData() != null) {
                        //         dto.setAvailableStockNum(goodsRealStockExecute.getData().getAvailableStockNum());
                        //     }
                        // }
                        if (ListUtil.isNotEmpty(dto.getRelationDTOList())) {
                            BigDecimal totalCanSaleStockNum = dto.getRelationDTOList().stream().map(ResLotteryRewardGoodsRelationDTO::getCanSaleStockNum).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                            dto.setAvailableStockNum(totalCanSaleStockNum);
                        }
                    }
                }
                // 赋值总共抽取
                this.setDrawNumByPromotion(dto);
            }

        }
        return ExecuteDTO.success(resultList);
    }

    @Override
    public ExecuteDTO<List<ResLotteryRewardInfoDTO>> listLotteryRewardInfo(ReqLotteryRewardInfoDTO reqLotteryRewardInfoDTO) {
        if (StringUtils.isBlank(reqLotteryRewardInfoDTO.getPromotionNo())) {
            return ExecuteDTO.error(CommonCode.CODE_10000001, "promotionNo");
        }
        ExecuteDTO<List<AtomResLotteryRewardInfoDTO>> listExecuteDTO = atomLotteryRuleAnalysisService.listLotteryRewardInfo(BeanCopierUtil.copy(reqLotteryRewardInfoDTO, AtomReqLotteryRewardInfoDTO.class));
        return ExecuteDTO.success(BeanCopierUtil.copyList(listExecuteDTO.getData(), ResLotteryRewardInfoDTO.class));
    }

    @Override
    public ExecuteDTO<Integer> getSameLottery(ReqLotteryRuleDTO reqLotteryRuleDTO) {
        return atomLotteryRuleAnalysisService.getSameLottery(BeanCopierUtil.copy(reqLotteryRuleDTO, AtomReqLotteryRuleDTO.class));
    }

    @Override
    public ExecuteDTO<Integer> countResidueLotteryNum(ReqLotteryRewardInfoDTO reqLotteryRewardInfoDTO) {
        if (StringUtils.isBlank(reqLotteryRewardInfoDTO.getPromotionNo())) {
            return ExecuteDTO.error(CommonCode.CODE_10000001, "promotionNo");
        }
        ExecuteDTO<Integer> executeDTO = atomPromotionRewardInfoAnalysisService.countResidueLotteryNum(BeanCopierUtil.copy(reqLotteryRewardInfoDTO, AtomReqLotteryRewardInfoDTO.class));
        if (!executeDTO.successFlag()) {
            log.info("统计剩余奖品数异常，原因：{}", executeDTO.getMsg());
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        return ExecuteDTO.success(executeDTO.getData());
    }

    private void setDrawNumByPromotion(ResLotteryRewardInfoDTO resLotteryRuleDTO) {
        if (resLotteryRuleDTO != null) {
            AtomReqLotteryDrawNumDTO dto = new AtomReqLotteryDrawNumDTO();
            dto.setRewardNo(resLotteryRuleDTO.getRewardNo());
            dto.setPromotionNo(resLotteryRuleDTO.getPromotionNo());
            ExecuteDTO<AtomResLotteryDrawNumDTO> recordExecute = atomLotteryDrawRecordAnalysisService.getRecordCountByTerm(dto);
            log.info("MarketProcess-LotteryRuleAnalysisServiceImpl-setDrawNumByPromotion-recordExecute={}", JSON.toJSONString(recordExecute));
            if (recordExecute != null && recordExecute.successFlag() && recordExecute.getData() != null) {
                AtomResLotteryDrawNumDTO recordDto = recordExecute.getData();
                Integer recordTotalCount = recordDto.getAllDrawNum() == null ? NumConstant.ZERO : recordDto.getAllDrawNum();
                Integer recordDailyCount = recordDto.getTodayDrawNum() == null ? NumConstant.ZERO : recordDto.getTodayDrawNum();
                // 已抽奖品总库存、已抽每日可抽库存
                resLotteryRuleDTO.setHasTotalCount(recordTotalCount);
                resLotteryRuleDTO.setHasDailyCount(recordDailyCount);

                Integer totalCount = resLotteryRuleDTO.getTotalCount() == null ? NumConstant.ZERO : resLotteryRuleDTO.getTotalCount();
                Integer dailyCount = resLotteryRuleDTO.getDailyCount() == null ? NumConstant.ZERO : resLotteryRuleDTO.getDailyCount();
                // 剩余奖品总库存、剩余每日可抽库存
                resLotteryRuleDTO.setSurplusTotalCount(totalCount - recordTotalCount);
                resLotteryRuleDTO.setSurplusDailyCount(dailyCount - recordDailyCount);
            } /**else {
             resLotteryRuleDTO.setSurplusTotalCount(NumConstant.ZERO);
             resLotteryRuleDTO.setSurplusDailyCount(NumConstant.ZERO);
             }**/
        }
    }

}
