package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.base.exception.BaseException;
import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.market.PointChangeEventEnum;
import cn.htdt.common.enums.market.PointsOperateTypeEnum;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.DateUtil;
import cn.htdt.common.utils.ListUtil;
import cn.htdt.marketcenter.dto.request.AtomReqUserPointsRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomResUserPointsRecordDTO;
import cn.htdt.marketprocess.api.analysis.UserPointsRecordAnalysisService;
import cn.htdt.marketprocess.biz.conversion.UserPointsRecordAssert;
import cn.htdt.marketprocess.biz.utils.PointsUtil;
import cn.htdt.marketprocess.dao.UserPointsRecordDao;
import cn.htdt.marketprocess.domain.UserPointsRecordDomain;
import cn.htdt.marketprocess.dto.request.ReqUserPointsRecordDTO;
import cn.htdt.marketprocess.dto.response.ResUserPointsRecordDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomUserPointsRecordAnalysisService;
import cn.htdt.marketprocess.vo.UserPointsRecordVO;
import cn.htdt.userprocess.dto.response.StoreInfoResponse;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户积分记录查询服务
 * <AUTHOR>
 * @date 2021/9/29 14:15
 */
@Slf4j
@DubboService
public class UserPointsRecordAnalysisServiceImpl implements UserPointsRecordAnalysisService {

    @Autowired
    private UserPointsRecordAssert userPointsRecordAssert;

    @Resource
    private UserPointsRecordAnalysisService userPointsRecordAnalysisService;

    @Resource
    private AtomUserPointsRecordAnalysisService atomUserPointsRecordAnalysisService;

    @Autowired
    private UserPointsRecordDao userPointsRecordDao;

    @Resource
    private PointsUtil pointsUtil;

    /**
     * @param reqUserPointsRecordDTO
     * @Description 查询用户指定日期已获得积分数量
     * <AUTHOR> 高繁
     * @date : 2021/9/28 10:30
     */
    @Override
    public ExecuteDTO<Integer> getUserPointsByDay(ReqUserPointsRecordDTO reqUserPointsRecordDTO) {
        log.info("-UserPointsRecordAnalysisServiceImpl-getUserPointsByDay-param={}", JSON.toJSONString(reqUserPointsRecordDTO));
        userPointsRecordAssert.getUserPointsByDay(reqUserPointsRecordDTO);
        AtomReqUserPointsRecordDTO atomReqUserPointsRecordDTO = BeanCopierUtil.copy(reqUserPointsRecordDTO,AtomReqUserPointsRecordDTO.class);
        ExecuteDTO<List<AtomResUserPointsRecordDTO>> executeDTO = atomUserPointsRecordAnalysisService.getUserPointsRecordList(atomReqUserPointsRecordDTO);
        if (!executeDTO.successFlag() || CollectionUtils.isEmpty(executeDTO.getData())){
            return ExecuteDTO.success(NumConstant.ZERO);
        }
        Integer userPoints =  executeDTO.getData().stream().mapToInt(AtomResUserPointsRecordDTO::getChangeNum).sum();
        log.info("查询用户指定日期已获得积分数量结果:{}",userPoints);
        return  ExecuteDTO.success(userPoints);
    }

    /**
     * @see UserPointsRecordAnalysisService#getUserPointsRecordDetail(ReqUserPointsRecordDTO)
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResUserPointsRecordDTO>> getUserPointsRecordDetail(ReqUserPointsRecordDTO reqUserPointsRecordDTO) {
        log.info("UserPointsRecordAnalysisServiceImpl.getUserPointsRecordDetail----param----{}", JSON.toJSONString(reqUserPointsRecordDTO));
        userPointsRecordAssert.getUserPointsDetail(reqUserPointsRecordDTO);
        AtomReqUserPointsRecordDTO atomResUserPointsRecordDTO = BeanCopierUtil.copy(reqUserPointsRecordDTO, AtomReqUserPointsRecordDTO.class);
        //20230928蛋品-wh-查询店铺是否是共享店铺
        StoreInfoResponse storeInfoResponse = pointsUtil.queryStore(reqUserPointsRecordDTO.getStoreNo());
        if(storeInfoResponse.getMemberSharing() == NumConstant.ONE){
            atomResUserPointsRecordDTO.setMerchantNo("");
            atomResUserPointsRecordDTO.setRuleType(String.valueOf(NumConstant.ONE));
        }else {
            atomResUserPointsRecordDTO.setStoreNo("");
            atomResUserPointsRecordDTO.setRuleType(String.valueOf(NumConstant.TWO));
            atomResUserPointsRecordDTO.setMerchantNo(storeInfoResponse.getMerchantNo());
        }
        ExecuteDTO<ExecutePageDTO<AtomResUserPointsRecordDTO>> executeDTO = atomUserPointsRecordAnalysisService.getUserPointsRecordDetail(atomResUserPointsRecordDTO);
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        //参数转换
        List<ResUserPointsRecordDTO> userPointsRecordDTOS = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResUserPointsRecordDTO.class);
        userPointsRecordDTOS.forEach(s->s.setCreateTimeStr(DateUtil.format(s.getCreateTime(),DateUtil.YYDDMMHHMMSS)));
        log.info("UserPointsRecordAnalysisServiceImpl.getUserPointsDetail----end----");
        return ExecuteDTO.success(new ExecutePageDTO<>(executeDTO.getData().getTotal(),userPointsRecordDTOS));
    }

    /**
     * @param reqUserPointsRecordDTO
     * @Description : 查询粉丝当前订单是否存在积分扣减记录
     * <AUTHOR> 高繁
     * @date : 2021/12/1 10:33
     *
     * 20230928蛋品-赵翔宇-商家积分-退单扣减积分, orderprocess调用
     */
    @Override
    public ExecuteDTO<Integer> getSoReturnPointsCount(ReqUserPointsRecordDTO reqUserPointsRecordDTO) {
        log.info("UserPointsRecordAnalysisServiceImpl.getSoReturnPointsCount----param----{}", JSON.toJSONString(reqUserPointsRecordDTO));
        userPointsRecordAssert.getSoReturnPointsCountAssert(reqUserPointsRecordDTO);
        AtomReqUserPointsRecordDTO atomReqUserPointsRecordDTO = BeanCopierUtil.copy(reqUserPointsRecordDTO,AtomReqUserPointsRecordDTO.class);
        atomReqUserPointsRecordDTO.setOperateType(PointsOperateTypeEnum.POINTS_DEDUCT.getCode());
        // 20230928蛋品-赵翔宇-商家积分-退单扣减积分, orderprocess调用,
        // {"fansNo":"123","first":1,"last":0,"orderNo":"03ff3","pageNum":0,"pageSize":0,"pointChangeEvent":"2005"}
        if (StringUtils.isBlank(reqUserPointsRecordDTO.getOrderNo())) {
            //20230928蛋品-wh-查询店铺是否是共享店铺
            StoreInfoResponse storeInfoResponse = pointsUtil.queryStore(reqUserPointsRecordDTO.getStoreNo());
            if(storeInfoResponse.getMemberSharing() == NumConstant.ONE){
                atomReqUserPointsRecordDTO.setRuleType(String.valueOf(NumConstant.ONE));
            }else {
                atomReqUserPointsRecordDTO.setRuleType(String.valueOf(NumConstant.TWO));
            }
        }

        ExecuteDTO<Integer> executeDTO = atomUserPointsRecordAnalysisService.getSoReturnPointsCount(atomReqUserPointsRecordDTO);
        if (!executeDTO.successFlag() || executeDTO.getData() == null){
            return ExecuteDTO.success(NumConstant.ZERO);
        }
        log.info("UserPointsRecordAnalysisServiceImpl.getSoReturnPointsCount----结束----{}", JSON.toJSONString(executeDTO));
        return  executeDTO;
    }

    /**
     * @param reqUserPointsRecordDTO 查询参数
     * @Description : 查询粉丝在当前订单是否存在积分操作记录
     * <AUTHOR> wl
     * @date : 2022/11/02
     *
     * 20230928蛋品-赵翔宇-商家积分, orderprocess会调用,
     * 比如下单, 订单是否已经扣减过积分;
     * 订单提交异常, 退积分, 判断是否存在积分抵扣优惠；
     * 售后单, 订单存在获得积分，进行积分扣减
     */
    @Override
    public ExecuteDTO<Integer> getSoFansPointsCount(ReqUserPointsRecordDTO reqUserPointsRecordDTO) {
        log.info("UserPointsRecordAnalysisServiceImpl.getSoFansPointsCount----param----{}", JSON.toJSONString(reqUserPointsRecordDTO));
        userPointsRecordAssert.getSoFansPointsCountAssert(reqUserPointsRecordDTO);
        UserPointsRecordDomain userPointsRecordDomain = BeanCopierUtil.copy(reqUserPointsRecordDTO,UserPointsRecordDomain.class);

        // 20230928蛋品-赵翔宇-商家积分, 如果为积分抵扣订单退款退回等, 则需要查询用户积分记录表, 判断是商家积分还是店铺积分, orderprocess调用
        ExecuteDTO<ResUserPointsRecordDTO> userPointsChangeRuleTypeExecute = userPointsRecordAnalysisService.getUserPointsChangeRuleType(reqUserPointsRecordDTO);
        String ruleType = "";
        if (null != userPointsChangeRuleTypeExecute
                && null != userPointsChangeRuleTypeExecute.getData()
                && StringUtils.isNotBlank(userPointsChangeRuleTypeExecute.getData().getRuleType())) {
            // 积分类型, 目前分为店铺积分还是商家积分
            ruleType = userPointsChangeRuleTypeExecute.getData().getRuleType();
            log.info("getSoFansPointsCount--->ruleType: {}", ruleType);
        }

        // 20230928蛋品-赵翔宇-商家积分, orderprocess会根据订单编号和粉丝编号判断有没有过对应的积分抵扣记录, 入参没有店铺编号
        if (StringUtils.isBlank(ruleType) && StringUtils.isBlank(reqUserPointsRecordDTO.getOrderNo())) {
            //20230928蛋品-wh-查询店铺是否是共享店铺
            StoreInfoResponse storeInfoResponse = pointsUtil.queryStore(reqUserPointsRecordDTO.getStoreNo());
            // 是否会员共享：1否 2是
            if(WhetherEnum.NO.getCode().equals(storeInfoResponse.getMemberSharing())){
                userPointsRecordDomain.setRuleType(String.valueOf(NumConstant.ONE));
            }else {
                userPointsRecordDomain.setRuleType(String.valueOf(NumConstant.TWO));
            }
        }
        Integer count = userPointsRecordDao.selectSoFansPointsCount(userPointsRecordDomain);
        log.info("UserPointsRecordAnalysisServiceImpl.getSoFansPointsCount----结束----{}", count);
        return ExecuteDTO.success(count);
    }

    @Override
    public ExecuteDTO<ExecutePageDTO<ResUserPointsRecordDTO>> getMerchantUserPointsRecordDetail(ReqUserPointsRecordDTO reqUserPointsRecordDTO) {
        log.info("UserPointsRecordAnalysisServiceImpl.getMerchantUserPointsRecordDetail----param----{}", JSON.toJSONString(reqUserPointsRecordDTO));
        userPointsRecordAssert.getUserPointsMemberDetail(reqUserPointsRecordDTO);
        AtomReqUserPointsRecordDTO atomResUserPointsRecordDTO = BeanCopierUtil.copy(reqUserPointsRecordDTO, AtomReqUserPointsRecordDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResUserPointsRecordDTO>> executeDTO = atomUserPointsRecordAnalysisService.getMerchantUserPointsRecordDetail(atomResUserPointsRecordDTO);
        //判断状态
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        //参数转换
        List<ResUserPointsRecordDTO> userPointsRecordDTOS = BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResUserPointsRecordDTO.class);
        userPointsRecordDTOS.forEach(s->s.setCreateTimeStr(DateUtil.format(s.getCreateTime(),DateUtil.YYDDMMHHMMSS)));
        log.info("UserPointsRecordAnalysisServiceImpl.getMerchantUserPointsRecordDetail----end----");
        return ExecuteDTO.success(new ExecutePageDTO<>(executeDTO.getData().getTotal(),userPointsRecordDTOS));
    }

    /**
     * 20230928蛋品-赵翔宇-商家积分, 如果为积分抵扣订单退款退回, 则需要查询用户积分记录表, 是商家积分还是店铺积分
     *
     * @param reqUserPointsRecordDTO 查询参数
     * @return 结果
     */
    @Override
    public ExecuteDTO<ResUserPointsRecordDTO> getUserPointsChangeRuleType(ReqUserPointsRecordDTO reqUserPointsRecordDTO) {

        String pointChangeEvent = reqUserPointsRecordDTO.getPointChangeEvent();

        // 20230928蛋品-赵翔宇-商家积分, 如果为积分抵扣订单退款退回, 则需要查询用户积分记录表, 是商家积分还是店铺积分
        if (StringUtils.isNotBlank(reqUserPointsRecordDTO.getOrderNo())
                && (PointChangeEventEnum.CHANGE_BACK.getCode().equals(pointChangeEvent)
                || PointChangeEventEnum.FANS_DEDUCTION_POINT_RETURN.getCode().equals(pointChangeEvent)
                || PointChangeEventEnum.FANS_DEDUCTION_EXCEPTION_RETURN.getCode().equals(pointChangeEvent))) {
            log.info("getUserPointsChangeRuleType, gain points record, pointChangeEvent: {}, orderNo: {}", pointChangeEvent, reqUserPointsRecordDTO.getOrderNo());

            UserPointsRecordVO userPointsRecordVO = new UserPointsRecordVO();
            userPointsRecordVO.setOrderNo(reqUserPointsRecordDTO.getOrderNo());

            List<String> pointChangeEventList = new ArrayList<>();
            // 20230928蛋品-赵翔宇-商家积分, 售后订单退积分, 要查询下单送积分的记录
            if (PointChangeEventEnum.CHANGE_BACK.getCode().equals(pointChangeEvent)) {
                pointChangeEventList.add(PointChangeEventEnum.FANS_SHOP_ORDER.getCode());
                pointChangeEventList.add(PointChangeEventEnum.FANS_ONLINE_ORDER.getCode());
                userPointsRecordVO.setPointChangeEventList(pointChangeEventList);
            } else {
                // 20230928蛋品-赵翔宇-商家积分, 积分抵扣订单退款退回(正常退单)或者积分抵扣订单异常退回(提交订单失败), 要查询积分抵扣优惠的记录
                userPointsRecordVO.setPointChangeEvent(PointChangeEventEnum.FANS_DEDUCTION_POINT.getCode());
            }
            log.info("getUserPointsChangeRuleType, gain points record, param: {}", JSON.toJSONString(userPointsRecordVO));
            List<UserPointsRecordDomain> userGainPointsRecordList = userPointsRecordDao.selectUserPointsGainPointsRecordInfo(userPointsRecordVO);
            log.info("getUserPointsChangeRuleType, gain points record, result: {}", JSON.toJSONString(userGainPointsRecordList));
            if (CollectionUtils.isEmpty(userGainPointsRecordList)) {
                log.error("getUserPointsChangeRuleType, query record error, orderNo: {}", reqUserPointsRecordDTO.getOrderNo());
                throw new BaseException(CommonCode.CODE_10000003, "查询用户积分记录异常");
            }
            UserPointsRecordDomain userGainPointsRecordDomain = ListUtil.getFirst(userGainPointsRecordList);
            ResUserPointsRecordDTO userPointsRecordDTO = BeanCopierUtil.copy(userGainPointsRecordDomain, ResUserPointsRecordDTO.class);
            return ExecuteDTO.ok(userPointsRecordDTO);
        }
        return ExecuteDTO.ok();
    }
}
