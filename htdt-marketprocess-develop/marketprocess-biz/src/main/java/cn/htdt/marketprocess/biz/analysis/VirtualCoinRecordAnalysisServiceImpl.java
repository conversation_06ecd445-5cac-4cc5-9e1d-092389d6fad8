package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.base.exception.BaseException;
import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.market.VirtualCoinRuleTypeEnum;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.BigDecimalUtil;
import cn.htdt.common.utils.CollectionUtils;
import cn.htdt.common.utils.DataBaseUtil;
import cn.htdt.marketcenter.dto.request.AtomReqVirtualCoinRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomResVirtualCoinRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomVirtualCoinRecord4WorkChangeDto;
import cn.htdt.marketcenter.dto.response.AtomVirtualCoinRecordStatisticsDTO;
import cn.htdt.marketprocess.api.analysis.VirtualCoinRecordAnalysisService;
import cn.htdt.marketprocess.biz.conversion.VirtualCoinRecordAssert;
import cn.htdt.marketprocess.biz.utils.VirtualCoinUtil;
import cn.htdt.marketprocess.dao.UserVirtualCoinDao;
import cn.htdt.marketprocess.dao.VirtualCoinRecordDao;
import cn.htdt.marketprocess.dao.VirtualCoinRecordEffectDao;
import cn.htdt.marketprocess.domain.VirtualCoinRecordDomain;
import cn.htdt.marketprocess.dto.request.ReqVirtualCoinRecordDTO;
import cn.htdt.marketprocess.dto.response.*;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomVirtualCoinRecordAnalysisService;
import cn.htdt.marketprocess.vo.ReqMarketTouchingEffectVO;
import cn.htdt.marketprocess.vo.VirtualCoinRechargeRecordVo;
import cn.htdt.marketprocess.vo.VirtualCoinRecordStatistics;
import cn.htdt.marketprocess.vo.VirtualCoinRecordVO;
import cn.htdt.usercenter.dto.request.ReqFancDTO;
import cn.htdt.usercenter.dto.response.ResFancDTO;
import cn.htdt.userprocess.api.LegacyUserCenterService;
import cn.htdt.userprocess.api.UcFansProcessService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/1/27
 */
@Slf4j
@RefreshScope
@DubboService
public class VirtualCoinRecordAnalysisServiceImpl implements VirtualCoinRecordAnalysisService {
    @Resource
    private AtomVirtualCoinRecordAnalysisService recordAnalysisService;

    @DubboReference
    private LegacyUserCenterService legacyUserCenterService;

    @DubboReference
    private UcFansProcessService ucFansProcessService;

    @Resource
    private VirtualCoinRecordDao virtualCoinRecordDao;

    @Resource
    private UserVirtualCoinDao userVirtualCoinDao;

    @Resource
    private VirtualCoinRecordEffectDao virtualCoinRecordEffectDao;

    @Resource
    private VirtualCoinRecordAssert virtualCoinRecordAssert;

    @Resource
    private DataBaseUtil dataBaseUtil;

    @Resource
    private VirtualCoinUtil virtualCoinUtil;

    /**
     * 根据订单查询橙豆记录
     * 这个方法, orderprocess会调用
     *
     * @param recordDTO
     * @return
     */
    @Override
    public ExecuteDTO<List<ResVirtualCoinRecordDTO>> queryVirtualCoinRecordListByOrder(ReqVirtualCoinRecordDTO recordDTO) {
        AtomReqVirtualCoinRecordDTO atomReqVirtualCoinRecordDTO = BeanCopierUtil.copy(recordDTO, AtomReqVirtualCoinRecordDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResVirtualCoinRecordDTO>> executePageDTOExecuteDTO = recordAnalysisService.queryVirtualCoinRecordList(atomReqVirtualCoinRecordDTO);
        ExecutePageDTO<AtomResVirtualCoinRecordDTO> data = executePageDTOExecuteDTO.getData();
        return ExecuteDTO.ok(BeanCopierUtil.copyList(data.getRows(), ResVirtualCoinRecordDTO.class));
    }

    /**
     * 查询橙豆收入消费列表
     * <p>
     * 20230928蛋品-赵翔宇-商家橙豆-橙豆流水, 需要区分是店铺的橙豆流水还是商家的橙豆流水
     * 202503 东启
     *
     * @param recordDTO
     * @return
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResVirtualCoinRecordDTO>> queryVirtualCoinRecordList(ReqVirtualCoinRecordDTO recordDTO) {
        log.info("-VirtualCoinRecordAnalysisServiceImpl-queryVirtualCoinRecordList-param-start,{}", JSON.toJSONString(recordDTO));
        List<String> fansNoList = new ArrayList<>();
        List<ResVirtualCoinRecordDTO> resVirtualCoinRecordDTOS = new ArrayList<>();


        // orderprocess不少地方会调用这个方法, 传入订单号和交易类型(tradeType)
        // 20230928蛋品-赵翔宇-商家橙豆-橙豆流水, 商家可以查看共享店铺的橙豆流水, 若会员店A为共享会员的店铺，之后商家将其从共享会员店铺删除后，商家平台还是可以看到此店铺发生的历史商家橙豆流水数据
        String virtualCoinRuleType = virtualCoinUtil.getVirtualCoinRuleType(recordDTO.getStoreNo(),
                recordDTO.getMerchantNo(),
                recordDTO.getLoginIdentity());

        if (StringUtils.isNotBlank(recordDTO.getMobile()) || StringUtils.isNotBlank(recordDTO.getStoreFanName())) {
            fansNoList = getFanNoListByInput(recordDTO, virtualCoinRuleType);
            // 20230928蛋品-赵翔宇-商家橙豆-橙豆流水, 粉丝只关注了一个共享店铺, 店铺退出共享店铺后, 根据手机号查询共享店铺粉丝查不到,
            // 但是流水是存在的, 需要根据手机号取查询粉丝编号, 再根据粉丝编号去查询流水
            if (StringUtils.isNotBlank(recordDTO.getMobile()) && CollectionUtils.isEmpty(fansNoList)) {
                log.info("queryVirtualCoinRecordList-->get fanInfo by mobile, result is null");

                ReqFancDTO reqFancDTO = new ReqFancDTO();
                reqFancDTO.setPhone(recordDTO.getMobile());
                ExecuteDTO<List<ResFancDTO>> fanInfoByPhoneExecute = ucFansProcessService.selectFanInfoByPhone(reqFancDTO);
                log.info("queryVirtualCoinRecordList-->fanInfoByPhoneExecute: {}", JSON.toJSONString(fanInfoByPhoneExecute));
                if (null != fanInfoByPhoneExecute && fanInfoByPhoneExecute.successFlag() && CollectionUtils.isNotEmpty(fanInfoByPhoneExecute.getData())) {
                    fansNoList = fanInfoByPhoneExecute.getData().stream().map(ResFancDTO::getFanNo).distinct().collect(Collectors.toList());
                }
            }
            if (CollectionUtils.isEmpty(fansNoList)) {
                return ExecuteDTO.ok(new ExecutePageDTO<>(NumConstant.ZERO, resVirtualCoinRecordDTOS));
            }
        }

        AtomReqVirtualCoinRecordDTO atomReqVirtualCoinRecordDTO = BeanCopierUtil.copy(recordDTO, AtomReqVirtualCoinRecordDTO.class);
        atomReqVirtualCoinRecordDTO.setFansNoList(fansNoList);
        // 20230928蛋品-赵翔宇-商家橙豆-橙豆流水, 需要根据橙豆类型来区分流水
        atomReqVirtualCoinRecordDTO.setRuleType(virtualCoinRuleType);
        atomReqVirtualCoinRecordDTO.setFanNo(null);
        ExecuteDTO<ExecutePageDTO<AtomResVirtualCoinRecordDTO>> executePageDTOExecuteDTO =
                recordAnalysisService.queryVirtualCoinRecordList(atomReqVirtualCoinRecordDTO);
        ExecutePageDTO<AtomResVirtualCoinRecordDTO> data = executePageDTOExecuteDTO.getData();
        if (data == null) {
            return ExecuteDTO.ok(new ExecutePageDTO<>(NumConstant.ZERO, resVirtualCoinRecordDTOS));
        }

        resVirtualCoinRecordDTOS = BeanCopierUtil.copyList(data.getRows(), ResVirtualCoinRecordDTO.class);

        // 橙豆流水里的粉丝编号
        List<String> virtualCoinRecordFanNoList = resVirtualCoinRecordDTOS.stream().map(ResVirtualCoinRecordDTO::getFanNo).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(virtualCoinRecordFanNoList)) {
            // 查询粉丝手机号
            ReqFancDTO getFanInfoDTO = new ReqFancDTO();
            getFanInfoDTO.setFansNoList(virtualCoinRecordFanNoList);

            ExecuteDTO<List<ResFancDTO>> fanInfoExecuteDTO;
            // 20230928蛋品-赵翔宇-商家橙豆-橙豆流水, 该店铺为共享店铺, 则需要根据店铺编号去查询数据
            if (VirtualCoinRuleTypeEnum.STORE_RULE.getCode().equals(virtualCoinRuleType)) {
                // 20230928蛋品-赵翔宇-商家橙豆-橙豆流水, 店铺未共享店铺等
                getFanInfoDTO.setStoreNo(recordDTO.getStoreNo());
                fanInfoExecuteDTO = legacyUserCenterService.getFansInfoByNos(getFanInfoDTO);
            } else {
                // 20230928蛋品-赵翔宇-商家橙豆-橙豆流水, 店铺为共享店铺, 需要根据商家编号去查询
                getFanInfoDTO.setMerchantNo(recordDTO.getMerchantNo());
                fanInfoExecuteDTO = ucFansProcessService.getMerchantFanInfo(getFanInfoDTO);
            }

            if (null != fanInfoExecuteDTO && fanInfoExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(fanInfoExecuteDTO.getData())) {
                List<ResFancDTO> resFancDTOList = fanInfoExecuteDTO.getData();
                resVirtualCoinRecordDTOS.forEach(resVirtualCoinRecordDTO -> {

                    Optional<ResFancDTO> optionalResFancDTO = resFancDTOList
                            .stream()
                            .filter(resFancDTO -> StringUtils.equals(resFancDTO.getFanNo(), resVirtualCoinRecordDTO.getFanNo()))
                            .findFirst();

                    if (optionalResFancDTO.isPresent()) {
                        ResFancDTO resFancDTO = optionalResFancDTO.get();
                        resVirtualCoinRecordDTO.setMobile(resFancDTO.getPhone());
                        resVirtualCoinRecordDTO.setStoreFanName(resFancDTO.getStoreFanName());
                    }
                });
            }

            if (VirtualCoinRuleTypeEnum.MEMBER_SHARING_RULE.getCode().equals(virtualCoinRuleType)) {
                // 查询粉丝在当前店铺的备注名
                ReqFancDTO fanStoreNameDTO = new ReqFancDTO();
                fanStoreNameDTO.setStoreNo(recordDTO.getStoreNo());
                fanStoreNameDTO.setFansNoList(virtualCoinRecordFanNoList);
                ExecuteDTO<List<ResFancDTO>> fanStoreNameExecuteDTO = ucFansProcessService.getFanStoreName(fanStoreNameDTO);
                if (null != fanStoreNameExecuteDTO && fanStoreNameExecuteDTO.successFlag() && CollectionUtils.isNotEmpty(fanStoreNameExecuteDTO.getData())) {
                    List<ResFancDTO> fanStoreNameList = fanStoreNameExecuteDTO.getData();
                    resVirtualCoinRecordDTOS.forEach(resVirtualCoinRecordDTO -> {

                        Optional<ResFancDTO> fanNameOptional = fanStoreNameList
                                .stream()
                                .filter(resFancDTO -> StringUtils.equals(resFancDTO.getFanNo(), resVirtualCoinRecordDTO.getFanNo()))
                                .findFirst();

                        if (fanNameOptional.isPresent()) {
                            ResFancDTO resFancDTO = fanNameOptional.get();
                            resVirtualCoinRecordDTO.setStoreFanName(resFancDTO.getStoreFanName());
                        }
                    });
                }
            }
        }

        if (CollectionUtils.isNotEmpty(resVirtualCoinRecordDTOS)) {
            // 手机号为空的数据
            List<ResVirtualCoinRecordDTO> blankPhoneFanInfoList =
                    resVirtualCoinRecordDTOS.stream()
                            .filter(resVirtualCoinRecordDTO -> StringUtils.isBlank(resVirtualCoinRecordDTO.getMobile()))
                            .collect(Collectors.toList());
            // 20230928单品-商家储值-橙豆流水, 店铺退出共享店铺, 结果里粉丝手机号会为空, 根据粉丝编号再去查询一边
            if (CollectionUtils.isNotEmpty(blankPhoneFanInfoList)) {
                List<String> blankPhoneFanNoList = blankPhoneFanInfoList
                        .stream()
                        .map(ResVirtualCoinRecordDTO::getFanNo)
                        .distinct()
                        .collect(Collectors.toList());

                ReqFancDTO reqFancDTO = new ReqFancDTO();
                reqFancDTO.setFansNoList(blankPhoneFanNoList);
                log.info("queryVirtualCoinRecordList--->get fanPhone info, reqFancDTO: {}", JSON.toJSONString(reqFancDTO));
                ExecuteDTO<List<ResFancDTO>> fanPhoneInfoExecute = ucFansProcessService.selectFanPhoneInfo(reqFancDTO);
                log.info("queryVirtualCoinRecordList--->get fanPhone info, fanPhoneInfoExecute: {}", JSON.toJSONString(fanPhoneInfoExecute));
                if (null != fanPhoneInfoExecute && fanPhoneInfoExecute.successFlag() && CollectionUtils.isNotEmpty(fanPhoneInfoExecute.getData())) {
                    List<ResFancDTO> phoneInfoList = fanPhoneInfoExecute.getData();
                    for (ResVirtualCoinRecordDTO resVirtualCoinRecordDTO : blankPhoneFanInfoList) {
                        Optional<ResFancDTO> fanPhoneInfoOp = phoneInfoList.stream().
                                filter(fanPhoneInfo -> fanPhoneInfo.getFanNo()
                                        .equals(resVirtualCoinRecordDTO.getFanNo())).findFirst();

                        // 设置手机号
                        if (fanPhoneInfoOp.isPresent()) {
                            ResFancDTO fanPhoneInfo = fanPhoneInfoOp.get();
                            resVirtualCoinRecordDTO.setMobile(fanPhoneInfo.getPhone());
                            resVirtualCoinRecordDTO.setMobileCipher(fanPhoneInfo.getDsPhone());
                        }
                    }
                }
            }
        }

        return ExecuteDTO.ok(new ExecutePageDTO<>(data.getTotal(), resVirtualCoinRecordDTOS));
    }


    /**
     * 根据手机号或者店铺备注名, 查询粉丝信息
     *
     * @param recordDTO           查询参数
     * @param virtualCoinRuleType 橙豆规则
     * @return 粉丝编号集合
     */
    private List<String> getFanNoListByInput(ReqVirtualCoinRecordDTO recordDTO, String virtualCoinRuleType) {
        log.info("getFanNoListByInput--->,查询指定粉丝信息, 橙豆规则类型: {}", virtualCoinRuleType);
        ReqFancDTO reqFancDTO = new ReqFancDTO();
        reqFancDTO.setPhone(recordDTO.getMobile());
        reqFancDTO.setMerchantNo(recordDTO.getMerchantNo());
        // 备注名, 需要根据店铺编号去查
        if (StringUtils.isNotBlank(recordDTO.getStoreFanName())) {
            reqFancDTO.setStoreFanName(recordDTO.getStoreFanName());
            reqFancDTO.setStoreNo(recordDTO.getStoreNo());
        }

        ExecuteDTO<List<ResFancDTO>> executeDTO;

        // 20230928蛋品-赵翔宇-商家橙豆-橙豆流水, 该店铺为共享店铺
        if (VirtualCoinRuleTypeEnum.MEMBER_SHARING_RULE.getCode().equals(virtualCoinRuleType)) {
            // 20230928蛋品-赵翔宇-商家橙豆-橙豆流水, 店铺加入共享店铺等同于商家身份查询粉丝信息
            executeDTO = ucFansProcessService.getMerchantFanInfo(reqFancDTO);
        } else {
            // 20230928蛋品-赵翔宇-商家橙豆-橙豆流水, 该店铺不为共享店铺
            executeDTO = legacyUserCenterService.getFanBySelect(reqFancDTO);
        }

        if (!executeDTO.successFlag()) {
            throw new BaseException(CommonCode.CODE_10000002, "查询粉丝数据");
        }

        List<ResFancDTO> fancDTOList = executeDTO.getData();
        if (CollectionUtils.isEmpty(fancDTOList)) {
            return Collections.emptyList();
        }
        return fancDTOList.stream().map(ResFancDTO::getFanNo).collect(Collectors.toList());
    }

    /**
     * 查询橙豆统计
     * 202503 东启
     *
     * @param recordDTO
     * @return
     */
    @Override
    public ExecuteDTO<ResVirtualCoinRecordStatisticsDTO> queryVirtualCoinRecordStatistics(ReqVirtualCoinRecordDTO recordDTO) {
        log.info("-VirtualCoinRecordAnalysisServiceImpl-queryVirtualCoinRecordStatistics-param-start,{}", JSON.toJSONString(recordDTO));
        List<String> fansNoList = new ArrayList<>();
        ResVirtualCoinRecordStatisticsDTO statisticsDTO = new ResVirtualCoinRecordStatisticsDTO();

        // 20230928蛋品-赵翔宇-商家橙豆-橙豆流水, 商家可以查看共享店铺的橙豆流水, 若会员店A为共享会员的店铺，之后商家将其从共享会员店铺删除后，商家平台还是可以看到此店铺发生的历史商家橙豆流水数据
        String virtualCoinRuleType = virtualCoinUtil.getVirtualCoinRuleType(recordDTO.getStoreNo(),
                recordDTO.getMerchantNo(),
                recordDTO.getLoginIdentity());

        if (StringUtils.isNotBlank(recordDTO.getMobile()) || StringUtils.isNotBlank(recordDTO.getStoreFanName())) {
            fansNoList = getFanNoListByInput(recordDTO, virtualCoinRuleType);
            if (CollectionUtils.isEmpty(fansNoList)) {
                return ExecuteDTO.ok(statisticsDTO);
            }
        }

        AtomReqVirtualCoinRecordDTO atomReqVirtualCoinRecordDTO = BeanCopierUtil.copy(recordDTO, AtomReqVirtualCoinRecordDTO.class);
        atomReqVirtualCoinRecordDTO.setFansNoList(fansNoList);
        atomReqVirtualCoinRecordDTO.setRuleType(virtualCoinRuleType);
        ExecuteDTO<AtomVirtualCoinRecordStatisticsDTO> executeDTO =
                recordAnalysisService.queryVirtualCoinRecordStatistics(atomReqVirtualCoinRecordDTO);
        if (!executeDTO.successFlag()) {
            throw new BaseException(CommonCode.CODE_10000002, "查询橙豆统计");
        }
        AtomVirtualCoinRecordStatisticsDTO data = executeDTO.getData();
        statisticsDTO = BeanCopierUtil.copy(data, ResVirtualCoinRecordStatisticsDTO.class);
        log.info("-VirtualCoinRecordAnalysisServiceImpl-queryVirtualCoinRecordStatistics-param-end,{}", JSON.toJSONString(recordDTO));
        return ExecuteDTO.ok(statisticsDTO);
    }

    /**
     * 一体机交接班-查询橙豆统计
     *
     * @param queryDto
     * @return
     */
    @Override
    public ExecuteDTO<ResVirtualCoinRecord4WorkChangeDto> queryVirtualCoinRecord4WorkChange(ReqVirtualCoinRecordDTO queryDto) {
        log.info("-VirtualCoinRecordAnalysisServiceImpl-queryVirtualCoinRecord4WorkChange-param-start,{}", JSON.toJSONString(queryDto));
        AtomReqVirtualCoinRecordDTO queryAtomDto = BeanCopierUtil.copy(queryDto, AtomReqVirtualCoinRecordDTO.class);
        ExecuteDTO<AtomVirtualCoinRecord4WorkChangeDto> atomResultEo = recordAnalysisService.queryVirtualCoinRecord4WorkChange(queryAtomDto);
        if (!atomResultEo.successFlag()) {
            throw new BaseException(CommonCode.CODE_10000002, "查询交接班橙豆统计");
        }
        ResVirtualCoinRecord4WorkChangeDto resultDto = BeanCopierUtil.copy(atomResultEo.getData(), ResVirtualCoinRecord4WorkChangeDto.class);
        log.info("-VirtualCoinRecordAnalysisServiceImpl-queryVirtualCoinRecord4WorkChange-param-end,{}", JSON.toJSONString(resultDto));
        return ExecuteDTO.ok(resultDto);
    }

    /**
     * BossApp，店铺&单店角色 -> 工作台 -> 数据报表 -> 营销权益效果 -> 橙豆权益触达效果
     * 或者 -> 卖货 -> 我要看数据 -> 营销权益触达
     *
     * @param recordDTO 请求参数
     * @return 响应参数
     */
    @Override
    public ExecuteDTO<ResVirtualCoinRecordEffectDTO> queryVirtualCoinRecordEffectStatistics(ReqVirtualCoinRecordDTO recordDTO) {
        log.info("----->queryVirtualCoinRecordEffectStatistics-param-start, {}", JSON.toJSONString(recordDTO));

        virtualCoinRecordAssert.virtualCoinRecordEffectAssert(recordDTO);

        VirtualCoinRecordDomain virtualCoinRecordDomain = BeanCopierUtil.copy(recordDTO, VirtualCoinRecordDomain.class);
        // 20230928蛋品-赵翔宇-商家橙豆
        virtualCoinRecordDomain.setRuleType(VirtualCoinRuleTypeEnum.STORE_RULE.getCode());
        // 返回的数据
        ResVirtualCoinRecordEffectDTO resVirtualCoinRecordEffectDTO = new ResVirtualCoinRecordEffectDTO();

        VirtualCoinRechargeRecordVo virtualCoinRechargeRecordVo = virtualCoinRecordDao.queryVirtualCoinRecordEffectStatistics(virtualCoinRecordDomain);

        if (null == virtualCoinRechargeRecordVo) {
            log.error("------>queryVirtualCoinRecordEffectStatistics------->virtualCoinRechargeRecordVo is null");
            virtualCoinRechargeRecordVo = new VirtualCoinRechargeRecordVo();
            virtualCoinRechargeRecordVo.setTotalPaymentAmount(BigDecimal.ZERO);
            virtualCoinRechargeRecordVo.setTotalVirtualCoinChange(BigDecimal.ZERO);
        }

        resVirtualCoinRecordEffectDTO.setOrangeBeanRechargeTotal(virtualCoinRechargeRecordVo.getTotalPaymentAmount());
        resVirtualCoinRecordEffectDTO.setOrangeBeanConsumeTotal(virtualCoinRechargeRecordVo.getTotalVirtualCoinChange());

        // 账户剩余橙豆数>=0的店铺粉丝人数, 已排除无效粉丝
        ReqMarketTouchingEffectVO reqMarketTouchingEffectVO = new ReqMarketTouchingEffectVO();
        reqMarketTouchingEffectVO.setStoreNo(recordDTO.getStoreNo());
        reqMarketTouchingEffectVO.setMarketDatabaseName(dataBaseUtil.getMarketDataBaseName());
        reqMarketTouchingEffectVO.setUserDatabaseName(dataBaseUtil.getUserDataBaseName());
        int hasVirtualCoinFansCount = virtualCoinRecordEffectDao.getHasVirtualCoinFansCount(reqMarketTouchingEffectVO);

        resVirtualCoinRecordEffectDTO.setOrangeBeanFansNum(hasVirtualCoinFansCount);
        if (null == resVirtualCoinRecordEffectDTO.getOrangeBeanRechargeTotal()) {
            resVirtualCoinRecordEffectDTO.setOrangeBeanRechargeTotal(BigDecimalUtil.setScale(BigDecimal.ZERO));
        }

        if (null == resVirtualCoinRecordEffectDTO.getOrangeBeanConsumeTotal()) {
            resVirtualCoinRecordEffectDTO.setOrangeBeanConsumeTotal(BigDecimalUtil.setScale(BigDecimal.ZERO));
        }

        log.info("------>queryVirtualCoinRecordEffectStatistics-resVirtualCoinRecordEffectDTO-end, {}", resVirtualCoinRecordEffectDTO);
        return ExecuteDTO.ok(resVirtualCoinRecordEffectDTO);
    }

    @Override
    public ExecuteDTO<ResVirtualCoinRecordStatisticsDTO> getTotalPaymentAmount(ReqVirtualCoinRecordDTO recordDTO) {
        log.info("-VirtualCoinRecordAnalysisServiceImpl-getTotalPaymentAmount-param-start,{}", JSON.toJSONString(recordDTO));
        VirtualCoinRecordVO getVirtualCoinRecordVO = BeanCopierUtil.copy(recordDTO, VirtualCoinRecordVO.class);
        VirtualCoinRecordStatistics virtualCoinRecordStatistics = virtualCoinRecordDao.selectTotalPaymentAmount(getVirtualCoinRecordVO);
        ResVirtualCoinRecordStatisticsDTO resVirtualCoinRecordStatisticsDTO = BeanCopierUtil.copy(virtualCoinRecordStatistics, ResVirtualCoinRecordStatisticsDTO.class);
        log.info("-VirtualCoinRecordAnalysisServiceImpl-getTotalPaymentAmount-param-end,{}", JSON.toJSONString(recordDTO));
        return ExecuteDTO.ok(resVirtualCoinRecordStatisticsDTO);
    }

}
