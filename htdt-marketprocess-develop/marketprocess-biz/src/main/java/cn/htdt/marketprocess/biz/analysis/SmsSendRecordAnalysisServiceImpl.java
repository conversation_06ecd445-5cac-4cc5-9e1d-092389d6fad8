package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.base.exception.BaseException;
import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.market.SmsSendObjectEnum;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.marketcenter.dto.request.AtomReqSmsSendRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomResSmsSendRecordDTO;
import cn.htdt.marketprocess.api.analysis.SmsSendRecordAnalysisService;
import cn.htdt.marketprocess.biz.conversion.SmsSendRecordAssert;
import cn.htdt.marketprocess.dao.SmsSendRecordDao;
import cn.htdt.marketprocess.domain.SmsSendRecordDomain;
import cn.htdt.marketprocess.dto.request.ReqSmsSendRecordDTO;
import cn.htdt.marketprocess.dto.response.ResSmsSendObjectDTO;
import cn.htdt.marketprocess.dto.response.ResSmsSendRecordDTO;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomSmsSendRecordAnalysisService;
import cn.htdt.marketprocess.vo.SmsSendRecordStaticVO;
import cn.htdt.ordercenter.dto.request.AtomReqSoDTO;
import cn.htdt.ordercenter.dto.request.AtomReqSoShoppingCartDTO;
import cn.htdt.orderprocess.api.LegacyOrderCenterService;
import cn.htdt.usercenter.dto.request.ReqFancDTO;
import cn.htdt.usercenter.dto.response.ResFancDTO;
import cn.htdt.userprocess.api.LegacyUserCenterService;
import cn.htdt.userprocess.api.UcFansProcessService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 短信发送记录 数据查询服务接口实现类
 *
 * <AUTHOR>
 * @date 2021/7/5
 **/
@DubboService
@Slf4j
public class SmsSendRecordAnalysisServiceImpl implements SmsSendRecordAnalysisService {
    @Resource
    private AtomSmsSendRecordAnalysisService atomSmsSendRecordAnalysisService;
    @DubboReference
    private LegacyOrderCenterService legacyOrderCenterService;
    @DubboReference
    private LegacyUserCenterService legacyUserCenterService;

    @DubboReference
    private UcFansProcessService ucFansProcessService;

    @Resource
    private SmsSendRecordAssert smsSendRecordAssert;

    @Resource
    private SmsSendRecordDao smsSendRecordDao;

    /**
     * 综合服务平台 -> 营销管理 -> 短信营销活动 -> 获取短信发送记录列表，支持分页查询
     * 超级老板APP -> 会员 -> 短信营销 -> 获取短信发送记录列表，支持分页查询
     *
     * @param reqDTO 查询参数
     * @return ExecuteDTO<ExecutePageDTO < ResSmsSendRecordDTO>>
     * <AUTHOR>
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResSmsSendRecordDTO>> getSmsSendRecordPageList(ReqSmsSendRecordDTO reqDTO) {
        log.info("-------SmsSendRecordAnalysisServiceImpl-->getSmsSendRecordPageList,获取短信发送记录列表，支持分页查询--start----");

        smsSendRecordAssert.getSmsSendRecordPageListAssert(reqDTO);

        log.info("-----getSmsSendRecordPageList---->,查询参数:{}", reqDTO);

        ExecutePageDTO<ResSmsSendRecordDTO> executePageDTO = new ExecutePageDTO<>();

        AtomReqSmsSendRecordDTO atomReqDTO = BeanCopierUtil.copy(reqDTO, AtomReqSmsSendRecordDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResSmsSendRecordDTO>> executeDTO = atomSmsSendRecordAnalysisService.getSmsSendRecordPageList(atomReqDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }

        if (executeDTO.getData() != null) {
            executePageDTO.setTotal(executeDTO.getData().getTotal());
            executePageDTO.setRows(BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResSmsSendRecordDTO.class));
        }

        log.info("-------SmsSendRecordAnalysisServiceImpl-->getSmsSendRecordPageList,获取短信发送记录列表，支持分页查询----");
        return ExecuteDTO.success(executePageDTO);
    }
    /**
     * 综合服务平台 -> 营销管理 -> 短信营销活动 -> 获取短信发送记录列表，支持分页查询
     * 超级老板APP -> 会员 -> 短信营销 -> 获取短信发送记录列表，支持分页查询
     *
     * @param reqDTO 查询参数
     * @return ExecuteDTO<ExecutePageDTO < ResSmsSendRecordDTO>>
     * <AUTHOR>
     */
    @Override
    public ExecuteDTO<ExecutePageDTO<ResSmsSendRecordDTO>> getStoreSmsSendRecordPageList(ReqSmsSendRecordDTO reqDTO) {
        log.info("-------SmsSendRecordAnalysisServiceImpl-->getStoreSmsSendRecordPageList,获取短信发送记录列表，支持分页查询--start----");

        log.info("-----getSmsSendRecordPageList---->,查询参数:{}", reqDTO);

        ExecutePageDTO<ResSmsSendRecordDTO> executePageDTO = new ExecutePageDTO<>();

        AtomReqSmsSendRecordDTO atomReqDTO = BeanCopierUtil.copy(reqDTO, AtomReqSmsSendRecordDTO.class);
        ExecuteDTO<ExecutePageDTO<AtomResSmsSendRecordDTO>> executeDTO = atomSmsSendRecordAnalysisService.getSmsSendRecordPageList(atomReqDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }

        if (executeDTO.getData() != null) {
            executePageDTO.setTotal(executeDTO.getData().getTotal());
            executePageDTO.setRows(BeanCopierUtil.copyList(executeDTO.getData().getRows(), ResSmsSendRecordDTO.class));
        }

        log.info("-------SmsSendRecordAnalysisServiceImpl-->getSmsSendRecordPageList,获取短信发送记录列表，支持分页查询----");
        return ExecuteDTO.success(executePageDTO);
    }
    /**
     * 综合服务平台 -> 营销管理 -> 短信营销活动 -> 获取短信发送对象
     * 超级老板APP -> 会员 -> 短信营销 -> 获取短信发送对象数量
     *
     * @param reqDTO 查询参数
     * @return ExecuteDTO<ResSmsSendObjectDTO>
     * <AUTHOR>
     */
    @Override
    public ExecuteDTO<ResSmsSendObjectDTO> getSmsSendObject(ReqSmsSendRecordDTO reqDTO) {
        log.info("-------SmsSendRecordAnalysisServiceImpl-->getSmsSendObject,获取短信发送对象--start----");

        smsSendRecordAssert.getSmsSendObjectAssert(reqDTO);

        log.info("-----getSmsSendRecordPageList---->,查询参数:{}", reqDTO);

        ResSmsSendObjectDTO resSmsSendObjectDTO = new ResSmsSendObjectDTO();

        if (StringUtils.isBlank(reqDTO.getSendObject())){
            return ExecuteDTO.error(CommonCode.CODE_10000001,"发送对象");
        }

        switch (SmsSendObjectEnum.getByCode(reqDTO.getSendObject())) {
            case STORE_ALL_FANS:
                //店内所有粉丝 (也可作为店铺粉丝节日发送)
                this.getStoreAllFans(reqDTO.getStoreNo(), resSmsSendObjectDTO);
                break;
            case PURCHASED_FANS:
                //有购买行为的粉丝
                this.getPurchasedFans(reqDTO.getStoreNo(), reqDTO.getSendObjectParam(), resSmsSendObjectDTO);
                break;
            case ADD_SHOPPING_CART_FANS:
                //加入购物车的粉丝
                this.getAddShoppingCartFans(reqDTO.getStoreNo(), reqDTO.getSendObjectParam(), resSmsSendObjectDTO);
                break;
            case GROUPING_FANS:
                //分组粉丝
                this.getGroupingFans(reqDTO.getGroupNoList(), resSmsSendObjectDTO);
                break;
            case CUSTOMIZE_FANS:
                break;
            case BIRTHDAY_FANS:
                //维护了生日的粉丝
                this.getStoreFanBirthday(reqDTO.getStoreNo(), resSmsSendObjectDTO);
                break;
        }

        //获取发送人数
        resSmsSendObjectDTO.setSendPeopleNum(CollectionUtils.isEmpty(resSmsSendObjectDTO.getTelephoneList())
                ? NumConstant.ZERO : resSmsSendObjectDTO.getTelephoneList().size());
        log.info("-------SmsSendRecordAnalysisServiceImpl-->getSmsSendObject,获取短信发送对象----");

        return ExecuteDTO.success(resSmsSendObjectDTO);
    }

    /**
     * BossApp，店铺&单店角色 -> 工作台 -> 数据报表 -> 营销权益效果 -> 营销短信权益触达效果
     * 或者 -> 卖货 -> 我要看数据 -> 营销短信权益触达
     * @param reqDTO 请求参数
     * @return 响应参数
     */
    @Override
    public ExecuteDTO<ResSmsSendRecordDTO> getMessageRecordStatic(ReqSmsSendRecordDTO reqDTO) {
        log.info("-------SmsSendRecordAnalysisServiceImpl-->getMessageRecordStatic, 营销短信触达效果--start----");

        smsSendRecordAssert.getMessageRecordStaticAssert(reqDTO);

        log.info("-----getMessageRecordStatic---->, 查询参数:{}", reqDTO);

        SmsSendRecordDomain smsSendRecordDomain = new SmsSendRecordDomain();
        smsSendRecordDomain.setStoreNo(reqDTO.getStoreNo());
        SmsSendRecordStaticVO messageRecordStatic = smsSendRecordDao.getMessageRecordStatic(smsSendRecordDomain);
        log.info("-----getMessageRecordStatic---->, messageRecordStatic:{}", messageRecordStatic);
        if (null == messageRecordStatic) {
            messageRecordStatic = new SmsSendRecordStaticVO();
            messageRecordStatic.setSmsSendingTimes(NumConstant.ZERO);
            messageRecordStatic.setSendSuccessNum(NumConstant.ZERO);
        }

        ResSmsSendRecordDTO resSmsSendRecordDTO = new ResSmsSendRecordDTO();
        resSmsSendRecordDTO.setSmsSendingTimes(messageRecordStatic.getSmsSendingTimes());
        resSmsSendRecordDTO.setSendSuccessNum(messageRecordStatic.getSendSuccessNum());
        log.info("-------SmsSendRecordAnalysisServiceImpl-->getMessageRecordStatic, 营销短信触达效果--end----");
        return ExecuteDTO.ok(resSmsSendRecordDTO);
    }

    /**
     * 获取店铺所有粉丝
     *
     * @param storeNo             店铺编号
     * @param resSmsSendObjectDTO 结果集
     */
    private void getStoreAllFans(String storeNo, ResSmsSendObjectDTO resSmsSendObjectDTO) {
        ReqFancDTO reqFancDTO = new ReqFancDTO();
        reqFancDTO.setStoreNo(storeNo);
        reqFancDTO.setPageSize(NumConstant.ONE_THOUSAND);
        //获取店铺所有粉丝
        ExecuteDTO<ExecutePageDTO<ResFancDTO>> fansDTOExecuteDTO = legacyUserCenterService.getPageStoreFanc(reqFancDTO);
        if (!fansDTOExecuteDTO.successFlag()) {
            throw new BaseException(fansDTOExecuteDTO.getStatus(), fansDTOExecuteDTO.getMsg());
        }
        if (fansDTOExecuteDTO.getData() == null || CollectionUtils.isEmpty(fansDTOExecuteDTO.getData().getRows())) {
            return;
        }

        ExecutePageDTO<ResFancDTO> fansDTOExecutePageDTO = fansDTOExecuteDTO.getData();
        int fansTotal = (int) fansDTOExecutePageDTO.getTotal();
        //发送手机号集合
        List<String> telephoneList;
        List<String> telephoneCipherList;
        if (fansTotal > NumConstant.ONE_THOUSAND) {
            telephoneList = new ArrayList<>(fansTotal);
            telephoneCipherList = new ArrayList<>(fansTotal);
            telephoneList.addAll(fansDTOExecuteDTO.getData().getRows().stream().map(ResFancDTO::getPhone).collect(Collectors.toList()));
            telephoneCipherList.addAll(fansDTOExecuteDTO.getData().getRows().stream().map(ResFancDTO::getDsPhone).collect(Collectors.toList()));
            for (int i = NumConstant.ONE_THOUSAND; i < fansTotal; i += NumConstant.ONE_THOUSAND) {
                reqFancDTO.setPageNum(reqFancDTO.getPageNum() + NumConstant.ONE);
                fansDTOExecuteDTO = legacyUserCenterService.getPageStoreFanc(reqFancDTO);
                telephoneList.addAll(fansDTOExecuteDTO.getData().getRows().stream().map(ResFancDTO::getPhone).collect(Collectors.toList()));
                telephoneCipherList.addAll(fansDTOExecuteDTO.getData().getRows().stream().map(ResFancDTO::getDsPhone).collect(Collectors.toList()));
            }
        } else {
            telephoneList = fansDTOExecuteDTO.getData().getRows().stream().map(ResFancDTO::getPhone).collect(Collectors.toList());
            telephoneCipherList = fansDTOExecuteDTO.getData().getRows().stream().map(ResFancDTO::getDsPhone).collect(Collectors.toList());
        }
        resSmsSendObjectDTO.setTelephoneList(telephoneList);
        resSmsSendObjectDTO.setTelephoneCipherList(telephoneCipherList);
    }

    /**
     * 获取店铺有购买行为的粉丝
     *
     * @param storeNo             店铺编号
     * @param sendObjectParam     发送对象查询参数
     * @param resSmsSendObjectDTO 结果集
     */
    private void getPurchasedFans(String storeNo, String sendObjectParam, ResSmsSendObjectDTO resSmsSendObjectDTO) {
        AtomReqSoDTO atomReqSoDTO = new AtomReqSoDTO();
        atomReqSoDTO.setStoreNo(storeNo);
        atomReqSoDTO.setOrderCreateStartTime(this.getSmsSendObjectStartTime(sendObjectParam));
        atomReqSoDTO.setOrderCreateEndTime(LocalDateTime.now());
        atomReqSoDTO.setPageSize(NumConstant.ONE_THOUSAND);
        //获取有购买行为的粉丝
        ExecuteDTO<ExecutePageDTO<String>> fansNoListExecuteDTO = legacyOrderCenterService.getBuyingFans(atomReqSoDTO);
        if (!fansNoListExecuteDTO.successFlag()) {
            throw new BaseException(fansNoListExecuteDTO.getStatus(), fansNoListExecuteDTO.getMsg());
        }
        if (fansNoListExecuteDTO.getData() == null || CollectionUtils.isEmpty(fansNoListExecuteDTO.getData().getRows())) {
            return;
        }

        ExecutePageDTO<String> fansNoExecutePageDTO = fansNoListExecuteDTO.getData();
        int fansTotal = (int) fansNoExecutePageDTO.getTotal();
        //获取粉丝手机号集合
        List<String> telephoneList;
        List<String> telephoneCipherList;
        //判断粉丝数是否大于1000，大于1000分批次获取剩余粉丝，并获取粉丝手机号，每次1000条
        if (fansTotal > NumConstant.ONE_THOUSAND) {
            telephoneList = new ArrayList<>(fansTotal);
            telephoneCipherList = new ArrayList<>(fansTotal);
            List<ResFancDTO> fansPhone = this.getFansPhone(fansNoExecutePageDTO.getRows());
            telephoneList.addAll(fansPhone.stream().map(ResFancDTO::getPhone).collect(Collectors.toList()));
            telephoneCipherList.addAll(fansPhone.stream().map(ResFancDTO::getDsPhone).collect(Collectors.toList()));
            for (int i = NumConstant.ONE_THOUSAND; i < fansTotal; i += NumConstant.ONE_THOUSAND) {
                atomReqSoDTO.setPageNum(atomReqSoDTO.getPageNum() + NumConstant.ONE);
                fansNoListExecuteDTO = legacyOrderCenterService.getBuyingFans(atomReqSoDTO);
                List<ResFancDTO> fansPhones = this.getFansPhone(fansNoListExecuteDTO.getData().getRows());
                telephoneList.addAll(fansPhones.stream().map(ResFancDTO::getPhone).collect(Collectors.toList()));
                telephoneCipherList.addAll(fansPhones.stream().map(ResFancDTO::getDsPhone).collect(Collectors.toList()));
            }
        } else {
            List<ResFancDTO> fansPhones = this.getFansPhone(fansNoExecutePageDTO.getRows());
            telephoneList = fansPhones.stream().map(ResFancDTO::getPhone).collect(Collectors.toList());
            telephoneCipherList = fansPhones.stream().map(ResFancDTO::getDsPhone).collect(Collectors.toList());
        }

        resSmsSendObjectDTO.setTelephoneList(telephoneList);
        resSmsSendObjectDTO.setTelephoneCipherList(telephoneCipherList);
    }

    /**
     * 获取店铺加入购物车的粉丝
     *
     * @param storeNo             店铺编号
     * @param sendObjectParam     发送对象查询参数
     * @param resSmsSendObjectDTO 结果集
     */
    private void getAddShoppingCartFans(String storeNo, String sendObjectParam, ResSmsSendObjectDTO resSmsSendObjectDTO) {
        AtomReqSoShoppingCartDTO soShoppingCartDTO = new AtomReqSoShoppingCartDTO();
        soShoppingCartDTO.setStoreNo(storeNo);
        soShoppingCartDTO.setCreateStartTime(this.getSmsSendObjectStartTime(sendObjectParam));
        soShoppingCartDTO.setCreateEndTime(LocalDateTime.now());
        soShoppingCartDTO.setPageSize(NumConstant.ONE_THOUSAND);
        //获取加入购物车的粉丝
        ExecuteDTO<ExecutePageDTO<String>> fansNoListExecuteDTO = legacyOrderCenterService.getCartFans(soShoppingCartDTO);
        if (!fansNoListExecuteDTO.successFlag()) {
            throw new BaseException(fansNoListExecuteDTO.getStatus(), fansNoListExecuteDTO.getMsg());
        }
        if (fansNoListExecuteDTO.getData() == null || CollectionUtils.isEmpty(fansNoListExecuteDTO.getData().getRows())) {
            return;
        }

        ExecutePageDTO<String> fansNoExecutePageDTO = fansNoListExecuteDTO.getData();
        int fansTotal = (int) fansNoExecutePageDTO.getTotal();
        //获取粉丝手机号集合
        List<String> telephoneList;
        List<String> telephoneCipherList;
        //判断粉丝数是否大于1000，大于1000分批次获取剩余粉丝，并获取粉丝手机号，每次1000条
        if (fansTotal > NumConstant.ONE_THOUSAND) {
            telephoneList = new ArrayList<>(fansTotal);
            telephoneCipherList = new ArrayList<>(fansTotal);
            List<ResFancDTO> fansPhone = this.getFansPhone(fansNoExecutePageDTO.getRows());
            telephoneList.addAll(fansPhone.stream().map(ResFancDTO::getPhone).collect(Collectors.toList()));
            telephoneCipherList.addAll(fansPhone.stream().map(ResFancDTO::getDsPhone).collect(Collectors.toList()));
            for (int i = NumConstant.ONE_THOUSAND; i < fansTotal; i += NumConstant.ONE_THOUSAND) {
                soShoppingCartDTO.setPageNum(soShoppingCartDTO.getPageNum() + NumConstant.ONE);
                fansNoListExecuteDTO = legacyOrderCenterService.getCartFans(soShoppingCartDTO);
                List<ResFancDTO> fansPhones = this.getFansPhone(fansNoExecutePageDTO.getRows());
                telephoneList.addAll(fansPhones.stream().map(ResFancDTO::getPhone).collect(Collectors.toList()));
                telephoneCipherList.addAll(fansPhones.stream().map(ResFancDTO::getDsPhone).collect(Collectors.toList()));
            }
        } else {
            List<ResFancDTO> fansPhones = this.getFansPhone(fansNoExecutePageDTO.getRows());
            telephoneList = fansPhones.stream().map(ResFancDTO::getPhone).collect(Collectors.toList());
            telephoneCipherList = fansPhones.stream().map(ResFancDTO::getDsPhone).collect(Collectors.toList());
        }
        resSmsSendObjectDTO.setTelephoneList(telephoneList);
        resSmsSendObjectDTO.setTelephoneCipherList(telephoneCipherList);
    }

    /**
     * 获取店铺分组粉丝
     *
     * @param groupNoList         粉丝分组编号集合
     * @param resSmsSendObjectDTO 结果集
     */
    private void getGroupingFans(List<String> groupNoList, ResSmsSendObjectDTO resSmsSendObjectDTO) {
        ReqFancDTO reqFancDTO = new ReqFancDTO();
        reqFancDTO.setGroupNoList(groupNoList);
        reqFancDTO.setNoPage();
        //获取分组粉丝
        ExecuteDTO<ExecutePageDTO<ResFancDTO>> fansDTOExecuteDTO = legacyUserCenterService.getPageGroupFanc(reqFancDTO);
        if (!fansDTOExecuteDTO.successFlag()) {
            throw new BaseException(fansDTOExecuteDTO.getStatus(), fansDTOExecuteDTO.getMsg());
        }
        if (fansDTOExecuteDTO.getData() == null || CollectionUtils.isEmpty(fansDTOExecuteDTO.getData().getRows())) {
            return;
        }
        resSmsSendObjectDTO.setTelephoneList(fansDTOExecuteDTO.getData().getRows().stream().map(ResFancDTO::getPhone).collect(Collectors.toList()));
        resSmsSendObjectDTO.setTelephoneCipherList(fansDTOExecuteDTO.getData().getRows().stream().map(ResFancDTO::getDsPhone).collect(Collectors.toList()));
    }

    /**
     * 获取店铺当月生日粉丝
     *
     * @param storeNo             店铺编号
     * @param resSmsSendObjectDTO 结果集
     */
    private void getStoreFanBirthday(String storeNo, ResSmsSendObjectDTO resSmsSendObjectDTO) {
        ReqFancDTO reqFancDTO = new ReqFancDTO();
        reqFancDTO.setStoreNo(storeNo);
        reqFancDTO.setPageSize(NumConstant.ONE_THOUSAND);
        //获取店铺所有粉丝
        ExecuteDTO<ExecutePageDTO<ResFancDTO>> fansDTOExecuteDTO = ucFansProcessService.selectStoreFanBirthdayByStoreNo(reqFancDTO);
        if (!fansDTOExecuteDTO.successFlag()) {
            throw new BaseException(fansDTOExecuteDTO.getStatus(), fansDTOExecuteDTO.getMsg());
        }
        if (fansDTOExecuteDTO.getData() == null || CollectionUtils.isEmpty(fansDTOExecuteDTO.getData().getRows())) {
            return;
        }

        ExecutePageDTO<ResFancDTO> fansDTOExecutePageDTO = fansDTOExecuteDTO.getData();
        int fansTotal = (int) fansDTOExecutePageDTO.getTotal();
        //发送手机号集合
        List<String> telephoneList;
        List<String> telephoneCipherList;
        if (fansTotal > NumConstant.ONE_THOUSAND) {
            telephoneList = new ArrayList<>(fansTotal);
            telephoneCipherList = new ArrayList<>(fansTotal);
            telephoneList.addAll(fansDTOExecuteDTO.getData().getRows().stream().map(ResFancDTO::getPhone).collect(Collectors.toList()));
            telephoneCipherList.addAll(fansDTOExecuteDTO.getData().getRows().stream().map(ResFancDTO::getDsPhone).collect(Collectors.toList()));
            for (int i = NumConstant.ONE_THOUSAND; i < fansTotal; i += NumConstant.ONE_THOUSAND) {
                reqFancDTO.setPageNum(reqFancDTO.getPageNum() + NumConstant.ONE);
                fansDTOExecuteDTO = legacyUserCenterService.getPageStoreFanc(reqFancDTO);
                telephoneList.addAll(fansDTOExecuteDTO.getData().getRows().stream().map(ResFancDTO::getPhone).collect(Collectors.toList()));
                telephoneCipherList.addAll(fansDTOExecuteDTO.getData().getRows().stream().map(ResFancDTO::getDsPhone).collect(Collectors.toList()));
            }
        } else {
            telephoneList = fansDTOExecuteDTO.getData().getRows().stream().map(ResFancDTO::getPhone).collect(Collectors.toList());
            telephoneCipherList = fansDTOExecuteDTO.getData().getRows().stream().map(ResFancDTO::getDsPhone).collect(Collectors.toList());
        }
        resSmsSendObjectDTO.setTelephoneList(telephoneList);
        resSmsSendObjectDTO.setTelephoneCipherList(telephoneCipherList);
    }

    /**
     * 获取短信发送对象查询开始时间
     *
     * @param sendObjectParam 查询参数
     */
    private LocalDateTime getSmsSendObjectStartTime(String sendObjectParam) {
        //参数不可为空
        if (StringUtils.isBlank(sendObjectParam)) {
            throw new BaseException(CommonCode.CODE_10000001, "sendObjectParam");
        }
        int month;
        try {
            //字符串转整数
            month = Integer.parseInt(sendObjectParam);
        } catch (Exception e) {
            throw new BaseException(CommonCode.CODE_10000002, "sendObjectParam");
        }
        switch (month) {
            case NumConstant.ONE:
            case NumConstant.TWO:
            case NumConstant.THREE:
            case NumConstant.SIX:
            case NumConstant.TWELVE:
                return LocalDateTime.now().minusMonths(month);
            default:
                throw new BaseException(CommonCode.CODE_10000002, "sendObjectParam");
        }
    }

    /**
     * 获取粉丝手机号
     *
     * @param fansNoList 粉丝编号集合
     * @return List<String>
     */
    private List<ResFancDTO> getFansPhone(List<String> fansNoList) {
        //获取粉丝信息
        ExecuteDTO<List<ResFancDTO>> fansDTOExecuteDTO = legacyUserCenterService.getFanByFanNos(fansNoList);
        if (!fansDTOExecuteDTO.successFlag()) {
            throw new BaseException(fansDTOExecuteDTO.getStatus(), fansDTOExecuteDTO.getMsg());
        }
        if (CollectionUtils.isEmpty(fansDTOExecuteDTO.getData())) {
            return Lists.newArrayList();
        }

        return fansDTOExecuteDTO.getData();
    }
}
