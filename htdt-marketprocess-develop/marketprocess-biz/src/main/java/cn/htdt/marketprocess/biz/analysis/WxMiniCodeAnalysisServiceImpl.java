package cn.htdt.marketprocess.biz.analysis;

import cn.htdt.common.dto.enums.CommonCode;
import cn.htdt.common.dto.enums.GoodsErrorCode;
import cn.htdt.common.dto.enums.MarketErrorCode;
import cn.htdt.common.dto.enums.UserErrorCode;
import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.enums.OrderChannelSourceEnum;
import cn.htdt.common.enums.PlatformTypeEnum;
import cn.htdt.common.enums.ShareSceneEnum;
import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.goods.GoodsFormEnum;
import cn.htdt.common.enums.goods.GoodsSourceTypeEnum;
import cn.htdt.common.enums.goods.GoodsTagTypeEnum;
import cn.htdt.common.enums.goods.SeriesTypeEnum;
import cn.htdt.common.enums.market.MarketTypeEnum;
import cn.htdt.common.enums.market.PromotionTypeEnum;
import cn.htdt.common.enums.market.ShareAgentUrlEnum;
import cn.htdt.common.enums.market.ShareUrlEnum;
import cn.htdt.common.enums.user.AppletTypeEnum;
import cn.htdt.common.enums.user.FanTypeEnum;
import cn.htdt.common.enums.user.IdentityEnum;
import cn.htdt.common.generator.MarketFormGenerator;
import cn.htdt.common.utils.BeanCopierUtil;
import cn.htdt.common.utils.StandaloneAppletUtil;
import cn.htdt.goodsprocess.api.analysis.GoodsAnalysisService;
import cn.htdt.goodsprocess.api.analysis.GoodsTagAnalysisService;
import cn.htdt.goodsprocess.dto.request.goods.ReqGoodsDTO;
import cn.htdt.goodsprocess.dto.request.goodstag.ReqGoodsTagDTO;
import cn.htdt.goodsprocess.dto.response.goods.ResGoodsDTO;
import cn.htdt.goodsprocess.dto.response.goodstag.ResGoodsTagDTO;
import cn.htdt.marketcenter.dto.request.AtomReqWxMiniCodeDTO;
import cn.htdt.marketcenter.dto.response.AtomResWxMiniCodeDTO;
import cn.htdt.marketprocess.api.analysis.AgentTaskAnalysisService;
import cn.htdt.marketprocess.api.analysis.PromotionInfoAnalysisService;
import cn.htdt.marketprocess.api.operat.AgentMarketBillOperatService;
import cn.htdt.marketprocess.api.operat.WxMiniCodeAnalysisService;
import cn.htdt.marketprocess.api.operat.WxMiniCodeOperatService;
import cn.htdt.marketprocess.biz.conversion.CommonAssert;
import cn.htdt.marketprocess.biz.conversion.PromotionInfoAssert;
import cn.htdt.marketprocess.biz.utils.CurrencyUtil;
import cn.htdt.marketprocess.biz.utils.UserInfoUtil;
import cn.htdt.marketprocess.dto.request.*;
import cn.htdt.marketprocess.dto.response.*;
import cn.htdt.marketprocess.legacycenter.api.analysis.AtomWxMiniCodeAnalysisService;
import cn.htdt.userprocess.api.AgentApiProcessService;
import cn.htdt.userprocess.api.FansApiProcessService;
import cn.htdt.userprocess.api.UserPublicService;
import cn.htdt.userprocess.dto.publicDto.request.GetStoreInfoRequest;
import cn.htdt.userprocess.dto.publicDto.response.GetMerchantResponse;
import cn.htdt.userprocess.dto.publicDto.response.GetStoreInfoResponse;
import cn.htdt.userprocess.dto.request.AgentInfoApiRequest;
import cn.htdt.userprocess.dto.request.GetFancInfoRequest;
import cn.htdt.userprocess.dto.request.ReqUcDisplayConfigurationDTO;
import cn.htdt.userprocess.dto.response.AgentInfoApiResponse;
import cn.htdt.userprocess.dto.response.FanInfoResponse;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 微信小程序二维码生成记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-27
 */
@Slf4j
@DubboService
public class WxMiniCodeAnalysisServiceImpl extends CurrencyUtil implements WxMiniCodeAnalysisService {

    @Resource
    private AtomWxMiniCodeAnalysisService atomWxMiniCodeAnalysisService;

    @Resource
    CommonAssert commonAssert;
    @Autowired
    PromotionInfoAssert promotionInfoAssert;

    @Value("${share.agent-content}")
    private String agentShareContent;

    @Value("${share.fans-content}")
    private String fansShareContent;

    @Value("${share.share-content}")
    private String shareContent;

    @Value("${share.fans-tip}")
    private String fansShareTip;

    @Value("${share.coupon-title}")
    private String couponTitle;

    @Value("${share.coupon-content}")
    private String couponContent;

    @Value("${url.hxg}")
    private String hxgFrontUrl;

    @Value("${media.default-picture-url}")
    private String DEFAULT_PICTURE_URL;

    /**
     * 大转盘
     */
    @Value("${share.promotion.url.turn-table}")
    private String turnTableUrl;

    /**
     * 摇奖机
     */
    @Value("${share.promotion.url.lottery-wheel}")
    private String lotteryWheelUrl;

    /**
     * 盲盒
     */
    @Value("${share.promotion.url.blind-box}")
    private String blindBoxUrl;

    /**
     * 套圈圈
     */
    @Value("${share.promotion.url.snare}")
    private String snareUrl;

    /**
     * 小猫钓鱼
     */
    @Value("${share.promotion.url.cat-fishing}")
    private String catFishingUrl;

    /**
     * 以下是pc端活动二维码地址
     */
    // 大转盘
    @Value(value = "${promotion.turntable-draw-url}")
    private String turntableDrawUrl;
    // 摇奖机
    @Value(value = "${promotion.lotterywheel-draw-url}")
    private String lotterywheelDrawUrl;
    // 盲盒
    @Value(value = "${promotion.blindbox-draw-url}")
    private String blindboxDrawUrl;
    // 套圈圈
    @Value(value = "${promotion.snare-draw-url}")
    private String snareDrawUrl;
    // 小猫钓鱼
    @Value(value = "${promotion.catfishing-draw-url}")
    private String catfishingDrawUrl;

    public static final String SHARE_PROMOTION_START_ONE = "SOF001";
    public static final String SHARE_PROMOTION_START_TWO = "SOF002";
    public static final String SHARE_PROMOTION_START_THREE = "SOF003";
    public static final String SHARE_PROMOTION_START_FOUR = "SOF004";
    public static final String SHARE_PROMOTION_START_FIVE = "SOF005";
    public static final String SHARE_PROMOTION_START_SIX = "SOF006";
    public static final String SHARE_PROMOTION_START_SEVEN = "SOF007";
    public static final String SHARE_PROMOTION_START_EIGHT = "SOF008";

    /**
     * 券分享
     */
    @Value("${share.promotion.url.coupon}")
    private String couponUrl;

    @Resource
    private AgentTaskAnalysisService agentTaskAnalysisService;

    @DubboReference
    private AgentApiProcessService agentApiProcessService;

    @DubboReference
    private GoodsAnalysisService goodsAnalysisService;

    @Resource
    private AgentMarketBillOperatService agentMarketBillOperatService;

    @Resource
    private PromotionInfoAnalysisService promotionInfoAnalysisService;

    @Resource
    private WxMiniCodeOperatService wxMiniCodeOperatService;

    @DubboReference
    private UserPublicService userPublicService;

    @DubboReference
    private GoodsTagAnalysisService goodsTagAnalysisService;


    @DubboReference
    private FansApiProcessService ucFansService;

    @Resource
    private UserInfoUtil userInfoUtil;

    @Autowired
    StandaloneAppletUtil standaloneAppletUtil;

    /**
     * 查询微信小程序二维码生成操作记录
     *
     * @param reqWxMiniCodeDTO 请求参数
     * <AUTHOR>
     */
    public ExecuteDTO<ResWxMiniCodeDTO> getWxMiniCode(ReqWxMiniCodeDTO reqWxMiniCodeDTO) {
        log.info("-WxMiniCodeAnalysisServiceImpl-getWxMiniCode-param={}", JSON.toJSONString(reqWxMiniCodeDTO));
        AtomReqWxMiniCodeDTO atomReqWxMiniCodeDTO = BeanCopierUtil.copy(reqWxMiniCodeDTO, AtomReqWxMiniCodeDTO.class);
        ExecuteDTO<AtomResWxMiniCodeDTO> executeDTO = atomWxMiniCodeAnalysisService.getWxMiniCode(atomReqWxMiniCodeDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResWxMiniCodeDTO resWxMiniCodeDTO = BeanCopierUtil.copy(executeDTO.getData(), ResWxMiniCodeDTO.class);
        log.info("-WxMiniCodeAnalysisServiceImpl-getWxMiniCode-return={}", JSON.toJSONString(resWxMiniCodeDTO));
        return ExecuteDTO.success(resWxMiniCodeDTO);
    }

    /**
     * 分享
     * 就汇享购用
     * @param reqHxgShareDTO
     * @returnWxMiniCodeAnalysisService
     */
    @Override
    public ExecuteDTO<ResShareDTO> share(ReqCommonShareDTO reqHxgShareDTO) {
        // 参数校验
        commonAssert.shareAssert(reqHxgShareDTO);
        ResShareDTO hxgShareDTO = null;
        ReqAgentMarketBillDTO reqAgentMarketBillDTO = null;
        ReqGoodsDTO reqGoodsDTO;
        // 如果是普通用户分享或者代理人分享
        if (ShareSceneEnum.NORMAL_SHARE.getCode().equals(reqHxgShareDTO.getShareScene()) || ShareSceneEnum.AGENT_SHARE.getCode().equals(reqHxgShareDTO.getShareScene())) {
            // 代理人信息校验
            if (ShareSceneEnum.AGENT_SHARE.getCode().equals(reqHxgShareDTO.getShareScene())) {
                ExecuteDTO<AgentInfoApiResponse> apiResponseResAppComDTO = this.getAgentInfo(reqHxgShareDTO.getAgentNo());
                if (!apiResponseResAppComDTO.successFlag()) {
                    return ExecuteDTO.error(apiResponseResAppComDTO.getStatus(), apiResponseResAppComDTO.getMsg());
                }
            }

            ExecuteDTO<ResGoodsDTO> resAppComDTO = this.getGoodsInfo(reqHxgShareDTO.getGoodsNo());
            if (!resAppComDTO.successFlag()) {
                return ExecuteDTO.error(resAppComDTO.getStatus(), resAppComDTO.getMsg());
            }

            // 查询店铺信息
            ExecuteDTO<GetStoreInfoResponse> storeResAppComDTO = this.getStoreInfo(resAppComDTO.getData().getStoreNo());
            if (!storeResAppComDTO.successFlag()) {
                return ExecuteDTO.error(storeResAppComDTO.getStatus(), storeResAppComDTO.getMsg());
            }
            // 组装返回参数
            hxgShareDTO = new ResShareDTO();
            hxgShareDTO.setShareScene(reqHxgShareDTO.getShareScene());
            hxgShareDTO.setShareTitle(resAppComDTO.getData().getGoodsName());
            hxgShareDTO.setShareContent(fansShareContent);
            StringBuilder stringBuilder = new StringBuilder();
            // 如果是代理人分享,agentNo不为空 需要加上agentNo 2021-3-24
            if (StringUtils.isNotBlank(reqHxgShareDTO.getAgentNo())) {
                hxgShareDTO.setShareUrl(stringBuilder.append(hxgFrontUrl).append(String.format(ShareUrlEnum.AGENT_SHARE_GOODS.getShareUrl(), reqHxgShareDTO.getGoodsNo(), reqHxgShareDTO.getAgentNo())).toString());
                // 小程序
                hxgShareDTO.setMiniUrl(String.format(ShareUrlEnum.AGENT_SHARE_GOODS.getMiniShareUrl(), reqHxgShareDTO.getGoodsNo(), reqHxgShareDTO.getAgentNo()));
            } else {
                hxgShareDTO.setShareUrl(stringBuilder.append(hxgFrontUrl).append(String.format(ShareUrlEnum.NORMAL_SHARE_GOODS.getShareUrl(), resAppComDTO.getData().getGoodsNo())).toString());
                hxgShareDTO.setMiniUrl(String.format(ShareUrlEnum.NORMAL_SHARE_GOODS.getMiniShareUrl(), resAppComDTO.getData().getGoodsNo()));
            }
            // 商品主图
            hxgShareDTO.setGoodsName(resAppComDTO.getData().getGoodsName());
            hxgShareDTO.setSeriesFlag(GoodsFormEnum.SERIES_GOODS.getCode().equals(resAppComDTO.getData().getGoodsForm()) ? WhetherEnum.YES.getCode() : WhetherEnum.NO.getCode());
            hxgShareDTO.setShareThumb(resAppComDTO.getData().getMainPictureUrl());
            hxgShareDTO.setStoreName(storeResAppComDTO.getData().getStoreName());
            hxgShareDTO.setStoreNo(storeResAppComDTO.getData().getStoreNo());
            hxgShareDTO.setMarketPrice(Optional.ofNullable(resAppComDTO.getData().getMaxMarketPrice()).orElse(resAppComDTO.getData().getMarketPrice()));
            hxgShareDTO.setFloorRetailPrice(Optional.ofNullable(resAppComDTO.getData().getMinRetailPrice()).orElse(resAppComDTO.getData().getRetailPrice()));
            hxgShareDTO.setCeilingRetailPrice(Optional.ofNullable(resAppComDTO.getData().getMaxRetailPrice()).orElse(resAppComDTO.getData().getRetailPrice()));
            // 服务标签 只有一个
            hxgShareDTO.setServiceTagNames(Lists.newArrayList(resAppComDTO.getData().getServiceTagName()));
            // 活动标签
            hxgShareDTO.setActiveTagNames(getActiveTagNames(reqHxgShareDTO.getGoodsNo()));
            hxgShareDTO.setSuperscriptPictureUrl(resAppComDTO.getData().getSuperscriptPictureUrl());
            hxgShareDTO.setShareTip(fansShareTip);
            hxgShareDTO.setSnapTitle(fansShareContent);
            // TODO 不需要分享记录
            // (怎么会呢?↓)
            // 代理人分享分销商品记录
            if (StringUtils.equals(ShareSceneEnum.AGENT_SHARE.getCode(),reqHxgShareDTO.getShareScene())) {
                //查询是否是分销商品
                reqGoodsDTO = new ReqGoodsDTO();
                reqGoodsDTO.setGoodsNo(reqHxgShareDTO.getGoodsNo());
                ExecuteDTO<ResGoodsDTO> goodsByNo = goodsAnalysisService.getGoodsByNo(reqGoodsDTO);
                if (goodsByNo.successFlag()){
                    String marketType = null;
                    if(WhetherEnum.YES.getCode().equals(goodsByNo.getData().getDistributeGoodsFlag())){ //店铺分销
                        marketType = MarketTypeEnum.StoreDistribution.getCode();
                    } else if(GoodsSourceTypeEnum.CLOUD_POOL_DISTRIBUTE.getCode().equals(goodsByNo.getData().getGoodsSourceType())){ //云池分销
                        marketType = MarketTypeEnum.CloudDistribution.getCode();
                    }
                    if (StringUtils.isNotBlank(marketType)){
                        reqAgentMarketBillDTO = new ReqAgentMarketBillDTO();
                        BeanCopierUtil.copy(reqHxgShareDTO, reqAgentMarketBillDTO);
                        reqAgentMarketBillDTO.setMarketType(marketType);
                        reqAgentMarketBillDTO.setMarketBillNo(MarketFormGenerator.genMarketBillNo());
                        reqAgentMarketBillDTO.setAgentNo(reqHxgShareDTO.getAgentNo());
                        reqAgentMarketBillDTO.setStoreNo(storeResAppComDTO.getData().getStoreNo());
                        reqAgentMarketBillDTO.setTaskOrGoodsNo(reqHxgShareDTO.getGoodsNo());
                        reqAgentMarketBillDTO.setTaskOrGoodsName(goodsByNo.getData().getGoodsName());
                    }
                }
            }
        }
        // 代理人任务分享
        else if (ShareSceneEnum.AGENT_TASK_SHARE.getCode().equals(reqHxgShareDTO.getShareScene())) {
            ExecuteDTO<AgentInfoApiResponse> apiResponseResAppComDTO = this.getAgentInfo(reqHxgShareDTO.getAgentNo());
            if (!apiResponseResAppComDTO.successFlag()) {
                return ExecuteDTO.error(apiResponseResAppComDTO.getStatus(), apiResponseResAppComDTO.getMsg());
            }
            // 查询店铺信息
            ExecuteDTO<GetStoreInfoResponse> storeResAppComDTO = this.getStoreInfo(apiResponseResAppComDTO.getData().getStoreNo());
            if (!storeResAppComDTO.successFlag()) {
                return ExecuteDTO.error(storeResAppComDTO.getStatus(), storeResAppComDTO.getMsg());
            }
            //根据代理人任务编号查询任务信息
            ReqAgentTaskDTO reqAgentTaskDTO = new ReqAgentTaskDTO();
            reqAgentTaskDTO.setTaskNo(reqHxgShareDTO.getTaskNo());
            // 查询代理人任务详情
            ExecuteDTO<ResAgentTaskDTO> taskExecuteDTO = agentTaskAnalysisService.getAgentTaskDetail(reqAgentTaskDTO);
            if (!taskExecuteDTO.successFlag()) {
                return ExecuteDTO.error(taskExecuteDTO.getStatus(), taskExecuteDTO.getMsg());
            }

            // 组装响应信息
            hxgShareDTO = new ResShareDTO();
            hxgShareDTO.setShareScene(reqHxgShareDTO.getShareScene());
            hxgShareDTO.setShareTitle(taskExecuteDTO.getData().getTaskName());
            // 分享缩略图
            hxgShareDTO.setShareThumb(taskExecuteDTO.getData().getTaskImageUrl());
            // 分享内容信息
            hxgShareDTO.setShareContent(String.format(agentShareContent, taskExecuteDTO.getData().getTaskName()));
            // TODO 目前只有拉新任务
            if (ShareUrlEnum.getByCode(taskExecuteDTO.getData().getTaskType()) != null) {
                StringBuilder stringBuilder = new StringBuilder();
                if (ShareUrlEnum.TASK_PULL_NEW.getCode().equals(taskExecuteDTO.getData().getTaskType())) {
                    hxgShareDTO.setShareUrl(stringBuilder.append(hxgFrontUrl).append(String.format(ShareUrlEnum.TASK_PULL_NEW.getShareUrl(), reqHxgShareDTO.getAgentNo(), taskExecuteDTO.getData().getTaskNo(), storeResAppComDTO.getData().getStoreNo())).toString());
                    hxgShareDTO.setMiniUrl(String.format(ShareUrlEnum.TASK_PULL_NEW.getShareUrl(), reqHxgShareDTO.getAgentNo(), taskExecuteDTO.getData().getTaskNo(), storeResAppComDTO.getData().getStoreNo()));
                }
            }
            hxgShareDTO.setStoreName(storeResAppComDTO.getData().getStoreName());
            hxgShareDTO.setStoreNo(storeResAppComDTO.getData().getStoreNo());
            hxgShareDTO.setSnapTitle(String.format(agentShareContent, taskExecuteDTO.getData().getTaskName()));

            // 分享记录
            reqAgentMarketBillDTO = new ReqAgentMarketBillDTO();
            BeanCopierUtil.copy(reqHxgShareDTO, reqAgentMarketBillDTO);
            reqAgentMarketBillDTO.setMarketBillNo(MarketFormGenerator.genMarketBillNo());
            reqAgentMarketBillDTO.setAgentNo(reqHxgShareDTO.getAgentNo());
            reqAgentMarketBillDTO.setStoreNo(storeResAppComDTO.getData().getStoreNo());
            reqAgentMarketBillDTO.setTaskOrGoodsNo(reqHxgShareDTO.getTaskNo());
            reqAgentMarketBillDTO.setTaskOrGoodsName(taskExecuteDTO.getData().getTaskName());
            reqAgentMarketBillDTO.setMarketType(taskExecuteDTO.getData().getTaskType());
        }
        // 活动分享
        else if(ShareSceneEnum.PROMOTION_SHARE.getCode().equals(reqHxgShareDTO.getShareScene())) {
            hxgShareDTO = new ResShareDTO();
            ExecuteDTO resAppComDTO = sharePromotion(hxgShareDTO, reqHxgShareDTO);
            hxgShareDTO.setShareScene(reqHxgShareDTO.getShareScene());
            if(!resAppComDTO.successFlag()) {
                return resAppComDTO;
            }
        } else {
            // 其他分享（领取代理人优惠券、分享商品、拉新任务、推荐有奖）

        }
        // 场景不存在
        if (null == hxgShareDTO) {
            return ExecuteDTO.error(CommonCode.CODE_10000002, "分享场景");
        }
        //保存分享记录
        if (null != reqAgentMarketBillDTO) {
            agentMarketBillOperatService.saveAgentMarketBill(reqAgentMarketBillDTO);
        }
        //如果token不为空，则查询粉丝信息
        if(StringUtils.isNotBlank(reqHxgShareDTO.getToken())){
            GetFancInfoRequest request = new GetFancInfoRequest();
            request.setToken(reqHxgShareDTO.getToken());
            ExecuteDTO<FanInfoResponse> fanInfoResponseExecuteDTO = ucFansService.getFanInfo(request);
            if(fanInfoResponseExecuteDTO.successFlag() && fanInfoResponseExecuteDTO.getData()!=null){
                hxgShareDTO.setNickName(fanInfoResponseExecuteDTO.getData().getNickName());
                hxgShareDTO.setHeadImg(fanInfoResponseExecuteDTO.getData().getHeadImg());
            }
        }
        return ExecuteDTO.success(hxgShareDTO);
    }

    @Override
    public ExecuteDTO<ResShareInfoDTO> shareUrl(ReqCommonShareDTO reqDTO) {
        // 参数校验
        commonAssert.shareUrlAssert(reqDTO);
        ResShareInfoDTO resShareInfoDTO = new ResShareInfoDTO();
        log.info("CommonServiceImpl-shareUrl-params={}", JSON.toJSONString(reqDTO));
        resShareInfoDTO.setShareScene(reqDTO.getShareScene());
        //todo 行业版修改
        if(StringUtils.isBlank(reqDTO.getTrade()) && StringUtils.isNotBlank(reqDTO.getStoreNo())){
            try{
                GetMerchantResponse getMerchantResponse = this.userInfoUtil.getMerchantInfo(null, reqDTO.getStoreNo());
                reqDTO.setTrade(getMerchantResponse.getIndustryExpand());
            }catch (Exception e){
                log.error("userInfoUtil.getMerchantInfo-error",e);
            }
        }
        log.info("CommonServiceImpl-setResShareInfo-params1={}, params2={}", JSON.toJSONString(resShareInfoDTO), JSON.toJSONString(reqDTO));
        ExecuteDTO<ResShareInfoDTO> resAppComDTO = setResShareInfo(resShareInfoDTO, reqDTO);
        log.info("CommonServiceImpl-setResShareInfo-response={}", JSON.toJSONString(resAppComDTO));
        if (resAppComDTO == null) {
            return ExecuteDTO.error(CommonCode.CODE_10000002, "分享场景");
        }
        if (!resAppComDTO.successFlag()) {
            return resAppComDTO;
        }
        // 场景不存在
//        if (null == resShareInfoDTO) {
//            return ExecuteDTO.error(CommonCode.CODE_10000002, "分享场景");
//        }
        return resAppComDTO;
    }

    /**
     * 最终还是调用shareUrl
     * @param reqPromotionInfoDTO
     * @return
     */
    @Override
    public ExecuteDTO<ResShareInfoDTO> sharePcUrl(ReqPromotionInfoDTO reqPromotionInfoDTO) {
        // 参数校验
        this.promotionInfoAssert.modifyPromotionShortLinkAssert(reqPromotionInfoDTO);
        Map<String, String> returnMap = this.getShareUrl(reqPromotionInfoDTO);
        ReqCommonShareDTO reqCommonShareDTO = new ReqCommonShareDTO();
        reqCommonShareDTO.setPromotionNo(reqPromotionInfoDTO.getPromotionNo());
        reqCommonShareDTO.setShareScene(reqPromotionInfoDTO.getShareScene() == null ? FanTypeEnum.CJHD.getCode() : reqPromotionInfoDTO.getShareScene());
        reqCommonShareDTO.setStoreNo(reqPromotionInfoDTO.getStoreNo());
        reqCommonShareDTO.setPage(reqPromotionInfoDTO.getPage() == null ? returnMap.get("page") : reqPromotionInfoDTO.getPage());
        reqCommonShareDTO.setScene(reqPromotionInfoDTO.getScene() == null ? returnMap.get("scene") : reqPromotionInfoDTO.getScene());
        reqCommonShareDTO.setPlatformType(reqPromotionInfoDTO.getPlatformType() == null ? "1003" : reqPromotionInfoDTO.getPlatformType());
        reqCommonShareDTO.setCodeFlag(reqPromotionInfoDTO.getCodeFlag() == null ? WhetherEnum.YES.getCode() : reqPromotionInfoDTO.getCodeFlag());
        reqCommonShareDTO.setAppId(reqPromotionInfoDTO.getAppId());
        log.info("CommonServiceImpl-sharePcUrl-params={}", JSON.toJSONString(reqCommonShareDTO));
        ExecuteDTO<ResShareInfoDTO> executeDTO = this.shareUrl(reqCommonShareDTO);
        if (executeDTO == null) {
            return ExecuteDTO.error(CommonCode.CODE_10000002, "分享场景");
        }
        if (!executeDTO.successFlag()) {
            return executeDTO;
        }
        return executeDTO;
    }

    /**
     * 获取H5活动页链接地址
     *
     * @param reqPromotionInfoDTO
     * @return
     */
    private Map<String, String> getShareUrl(ReqPromotionInfoDTO reqPromotionInfoDTO) {
        Map<String, String> returnMap = new HashedMap();
        String promotionUrl;
        switch (PromotionTypeEnum.getByCode(reqPromotionInfoDTO.getPromotionType())) {
            case LOTTERYWHEEL_DRAW:
                promotionUrl = lotterywheelDrawUrl;
                break;
            case BLINDBOX_DRAW:
                promotionUrl = blindboxDrawUrl;
                break;
            case SNARE_DRAW:
                promotionUrl = snareDrawUrl;
                break;
            case CATFISHING_DRAW:
                promotionUrl = catfishingDrawUrl;
                break;
            default:
                promotionUrl = turntableDrawUrl;
                break;
        }
        returnMap.put("page", promotionUrl);
        returnMap.put("scene", "promotionNo=" + reqPromotionInfoDTO.getPromotionNo());
        return returnMap;

    }

    /**
     * 赋值返回URL-NEW
     */
    private ExecuteDTO<ResShareInfoDTO> setResShareInfo(ResShareInfoDTO resShareInfoDTO, ReqCommonShareDTO reqDTO) {
        ExecuteDTO<ResShareInfoDTO> resAppComDTO = null;
        // 商品分享-普通粉丝
        if (FanTypeEnum.PTSPFX.getCode().equals(reqDTO.getShareScene())
                || FanTypeEnum.YCSPFX.getCode().equals(reqDTO.getShareScene())) {
            resAppComDTO = this.getShareGoodsInfo(resShareInfoDTO, reqDTO);
        }
        // 商品分享-代理人
        if (FanTypeEnum.DLYLX.getCode().equals(reqDTO.getShareScene())) {
            resAppComDTO = this.getShareGoodsInfo(resShareInfoDTO, reqDTO);
        }
        // 代理人拉新任务分享-代理人
        if (FanTypeEnum.LXRW.getCode().equals(reqDTO.getShareScene())) {
            resAppComDTO = this.shareTask(resShareInfoDTO, reqDTO);
        }
        // 代理人招募-我要做分销
        if (FanTypeEnum.DLRZM.getCode().equals(reqDTO.getShareScene())){
            resAppComDTO = this.recruitAgent(resShareInfoDTO, reqDTO);
        }
        // 活动分享
        if (StringUtils.isNotEmpty(reqDTO.getShareScene()) && reqDTO.getShareScene().startsWith(SHARE_PROMOTION_START_TWO)) {
            resAppComDTO = this.shareAllPromotion(resShareInfoDTO, reqDTO);
        }
        // 领取代理人优惠券
        if (FanTypeEnum.LQDLRYHQ.getCode().equals(reqDTO.getShareScene())) {
            resAppComDTO = this.getShareAgentCoupon(resShareInfoDTO, reqDTO);
        }

        if(resAppComDTO != null && resAppComDTO.successFlag() && resAppComDTO.getData()!=null){
            //如果token不为空，则查询粉丝信息
            if(StringUtils.isNotBlank(reqDTO.getToken())){
                GetFancInfoRequest request = new GetFancInfoRequest();
                request.setToken(reqDTO.getToken());
                ExecuteDTO<FanInfoResponse> fanInfoResponseExecuteDTO = ucFansService.getFanInfo(request);
                if(fanInfoResponseExecuteDTO.successFlag() && fanInfoResponseExecuteDTO.getData()!=null){
                    resAppComDTO.getData().setNickName(fanInfoResponseExecuteDTO.getData().getNickName());
                    resAppComDTO.getData().setHeadImg(fanInfoResponseExecuteDTO.getData().getHeadImg());
                }
            }
        }
        // 推荐有奖
        // @TODO 其他分享在此新加即可
        return resAppComDTO;
    }

    /**
     * 获取分享商品信息-NEW
     *
     * @param reqHxgShareDTO goodsNo和shareScene,代理人时agentNo
     * @return
     */
    private ExecuteDTO<ResShareInfoDTO> getShareGoodsInfo(ResShareInfoDTO resShareInfoDTO, ReqCommonShareDTO reqHxgShareDTO) {
        ReqGoodsDTO reqGoodsDTO = new ReqGoodsDTO();
        reqGoodsDTO.setGoodsNo(reqHxgShareDTO.getGoodsNo());
        // 查询商品信息
        ExecuteDTO<ResGoodsDTO> executeDTO = goodsAnalysisService.getGoods(reqGoodsDTO);
        if (!executeDTO.successFlag() || null == executeDTO.getData()) {
            return ExecuteDTO.error(GoodsErrorCode.CODE_12000001);
        }
        String url = "";
        String shareUrl = "";
        ReqAgentMarketBillDTO reqAgentMarketBillDTO = null;

        ExecuteDTO<ResGoodsDTO> resAppComDTO = this.getGoodsInfo(reqHxgShareDTO.getGoodsNo());
        if (!resAppComDTO.successFlag()) {
            return ExecuteDTO.error(resAppComDTO.getStatus(), resAppComDTO.getMsg());
        }

        // 查询店铺信息
        ExecuteDTO<GetStoreInfoResponse> storeExecuteDTO = this.getStoreInfo(resAppComDTO.getData().getStoreNo());
        if (!storeExecuteDTO.successFlag()) {
            return ExecuteDTO.error(storeExecuteDTO.getStatus(), storeExecuteDTO.getMsg());
        }
        // 商品分享-代理人
        if (FanTypeEnum.DLYLX.getCode().equals(reqHxgShareDTO.getShareScene())) {
            // 代理人信息校验
            ExecuteDTO<AgentInfoApiResponse> apiResponseExecuteDTO = this.getAgentInfo(reqHxgShareDTO.getAgentNo());
            if (!apiResponseExecuteDTO.successFlag()) {
                return ExecuteDTO.error(apiResponseExecuteDTO.getStatus(), apiResponseExecuteDTO.getMsg());
            }
            if (StringUtils.isNotEmpty(reqHxgShareDTO.getPage())) {
                url = reqHxgShareDTO.getPage();
            } else {
                url = String.format(ShareAgentUrlEnum.AGENT_SHARE_GOODS.getMiniShareUrl(), reqHxgShareDTO.getGoodsNo(), reqHxgShareDTO.getAgentNo());
            }
            // 查询是否是分销商品
            String marketType = null;
            if (WhetherEnum.YES.getCode().equals(executeDTO.getData().getDistributeGoodsFlag())) { //店铺分销
                marketType = MarketTypeEnum.StoreDistribution.getCode();
            } else if (GoodsSourceTypeEnum.CLOUD_POOL_DISTRIBUTE.getCode().equals(executeDTO.getData().getGoodsSourceType())) { //云池分销
                marketType = MarketTypeEnum.CloudDistribution.getCode();
            }
            if (StringUtils.isNotBlank(marketType)) {
                reqAgentMarketBillDTO = new ReqAgentMarketBillDTO();
                BeanCopierUtil.copy(reqHxgShareDTO, reqAgentMarketBillDTO);
                reqAgentMarketBillDTO.setMarketType(marketType);
                reqAgentMarketBillDTO.setMarketBillNo(MarketFormGenerator.genMarketBillNo());
                reqAgentMarketBillDTO.setAgentNo(reqHxgShareDTO.getAgentNo());
                reqAgentMarketBillDTO.setStoreNo(executeDTO.getData().getStoreNo());
                reqAgentMarketBillDTO.setTaskOrGoodsNo(reqHxgShareDTO.getGoodsNo());
                reqAgentMarketBillDTO.setTaskOrGoodsName(executeDTO.getData().getGoodsName());
            }
        } else {
            // 商品分享-普通粉丝
            if (StringUtils.isNotEmpty(reqHxgShareDTO.getPage())) {
                url = reqHxgShareDTO.getPage();
            } else {
                url = String.format(ShareAgentUrlEnum.NORMAL_SHARE_GOODS.getMiniShareUrl(), reqHxgShareDTO.getGoodsNo());
            }
        }
        //保存分享记录
        if (null != reqAgentMarketBillDTO) {
            agentMarketBillOperatService.saveAgentMarketBill(reqAgentMarketBillDTO);
        }
        shareUrl = hxgFrontUrl + url;
        resShareInfoDTO.setShareUrl(shareUrl);
        resShareInfoDTO.setMiniUrl(url);
        // 若小程序
        this.setMiniUrl(reqHxgShareDTO, resShareInfoDTO);
        return ExecuteDTO.success(resShareInfoDTO);
    }

    /**
     * 统一小程序二维码url
     */
    private void setMiniUrl(ReqCommonShareDTO reqHxgShareDTO, ResShareInfoDTO resShareInfoDTO) {
        // 判断若需要小程序生成二维码时
    	log.info("***WxMiniCodeAnalysisServiceImpl.setMiniUrl***param1:{}, param2:{}", reqHxgShareDTO, resShareInfoDTO);
        if (OrderChannelSourceEnum.HXG_MINI.getCode().equals(reqHxgShareDTO.getPlatformType())) {
            if (WhetherEnum.YES.getCode().equals(reqHxgShareDTO.getCodeFlag())) {

                ReqShareDTO reqShareDTO = new ReqShareDTO();
                reqShareDTO.setPage(reqHxgShareDTO.getPage());
                reqShareDTO.setShareScene(reqHxgShareDTO.getShareScene());
                reqShareDTO.setScene(reqHxgShareDTO.getScene());
                reqShareDTO.setAppId(reqHxgShareDTO.getAppId());

                ExecuteDTO<ResShareInfoDTO> shareExecuteDTO;
                if (AppletTypeEnum.HXG_INDEPENDENT.equals(standaloneAppletUtil.queryAppletType(reqHxgShareDTO.getAppId()))) {
                    shareExecuteDTO = wxMiniCodeOperatService.createAndSaveWxMiniCode4Applet(reqShareDTO);
                    log.info("***WxMiniCodeAnalysisServiceImpl.setMiniUrl***shareExecuteDTO1:{}", shareExecuteDTO);
                } else {
                    //todo 行业版修改
                    reqShareDTO.setTrade(reqHxgShareDTO.getTrade());
                    shareExecuteDTO = wxMiniCodeOperatService.createAndSaveWxMiniCode(reqShareDTO);
                    log.info("***WxMiniCodeAnalysisServiceImpl.setMiniUrl***shareExecuteDTO2:{}", shareExecuteDTO);
                }
                if (shareExecuteDTO.successFlag()) {
                    resShareInfoDTO.setCodeNo(shareExecuteDTO.getData().getCodeNo());
                    resShareInfoDTO.setMiniUrl(shareExecuteDTO.getData().getMiniUrl());
                }
            }
        }
    }

    /**
     * 代理人拉新任务分享-NEW
     *
     * @param reqHxgShareDTO
     */
    private ExecuteDTO<ResShareInfoDTO> recruitAgent(ResShareInfoDTO resShareInfoDTO, ReqCommonShareDTO reqHxgShareDTO) {
        // 代理人招募
        if (StringUtils.isNotBlank(reqHxgShareDTO.getStoreNo())) {
            StringBuilder stringBuilder = new StringBuilder();
            String url = String.format(ShareAgentUrlEnum.RECRUIT_AGENT.getShareUrl(), reqHxgShareDTO.getStoreNo());
//            if (StringUtils.isNotEmpty(reqHxgShareDTO.getPage())) {
//                url = reqHxgShareDTO.getPage();
//            }
            resShareInfoDTO.setShareUrl(stringBuilder.append(hxgFrontUrl).append(url).toString());
//            resShareInfoDTO.setMiniUrl(url);
            // 若小程序
            this.setMiniUrl(reqHxgShareDTO, resShareInfoDTO);

        }
        return ExecuteDTO.success(resShareInfoDTO);
    }

    /**
     * 代理人拉新任务分享-NEW
     *
     * @param reqHxgShareDTO
     */
    private ExecuteDTO<ResShareInfoDTO> shareTask(ResShareInfoDTO resShareInfoDTO, ReqCommonShareDTO reqHxgShareDTO) {
        ExecuteDTO<AgentInfoApiResponse> apiResponseExecuteDTO = this.getAgentInfo(reqHxgShareDTO.getAgentNo());
        if (!apiResponseExecuteDTO.successFlag()) {
            return ExecuteDTO.error(apiResponseExecuteDTO.getStatus(), apiResponseExecuteDTO.getMsg());
        }
        // 查询店铺信息
        ExecuteDTO<GetStoreInfoResponse> storeExecuteDTO = this.getStoreInfo(apiResponseExecuteDTO.getData().getStoreNo());
        if (!storeExecuteDTO.successFlag()) {
            return ExecuteDTO.error(storeExecuteDTO.getStatus(), storeExecuteDTO.getMsg());
        }
        //根据代理人任务编号查询任务信息
        ReqAgentTaskDTO reqAgentTaskDTO = new ReqAgentTaskDTO();
        reqAgentTaskDTO.setTaskNo(reqHxgShareDTO.getTaskNo());
        // 查询代理人任务详情
        ExecuteDTO<ResAgentTaskDTO> taskExecuteDTO = agentTaskAnalysisService.getAgentTaskDetail(reqAgentTaskDTO);
        if (!taskExecuteDTO.successFlag()) {
            return ExecuteDTO.error(taskExecuteDTO.getStatus(), taskExecuteDTO.getMsg());
        }

        // TODO 目前只有拉新任务
        if (ShareUrlEnum.getByCode(taskExecuteDTO.getData().getTaskType()) != null) {
            StringBuilder stringBuilder = new StringBuilder();
            String url = String.format(ShareAgentUrlEnum.TASK_PULL_NEW_MINI.getShareUrl(), reqHxgShareDTO.getAgentNo(), taskExecuteDTO.getData().getTaskNo(), storeExecuteDTO.getData().getStoreNo());
            if (StringUtils.isNotEmpty(reqHxgShareDTO.getPage())) {
                url = reqHxgShareDTO.getPage();
            }
            resShareInfoDTO.setShareUrl(stringBuilder.append(hxgFrontUrl).append(url).toString());
            resShareInfoDTO.setMiniUrl(url);
            // 若小程序
            this.setMiniUrl(reqHxgShareDTO, resShareInfoDTO);


            // 分享记录
            ReqAgentMarketBillDTO reqAgentMarketBillDTO = new ReqAgentMarketBillDTO();
            BeanCopierUtil.copy(reqHxgShareDTO, reqAgentMarketBillDTO);
            reqAgentMarketBillDTO.setMarketBillNo(MarketFormGenerator.genMarketBillNo());
            reqAgentMarketBillDTO.setAgentNo(reqHxgShareDTO.getAgentNo());
            reqAgentMarketBillDTO.setStoreNo(storeExecuteDTO.getData().getStoreNo());
            reqAgentMarketBillDTO.setTaskOrGoodsNo(reqHxgShareDTO.getTaskNo());
            reqAgentMarketBillDTO.setTaskOrGoodsName(taskExecuteDTO.getData().getTaskName());
            reqAgentMarketBillDTO.setMarketType(taskExecuteDTO.getData().getTaskType());
            //保存分享记录
            if (null != reqAgentMarketBillDTO) {
                agentMarketBillOperatService.saveAgentMarketBill(reqAgentMarketBillDTO);
            }
        }
        return ExecuteDTO.success(resShareInfoDTO);
    }

    /**
     * 获取分享活动信息-NEW
     *
     * @param reqHxgShareDTO
     */
    private ExecuteDTO<ResShareInfoDTO> shareAllPromotion(ResShareInfoDTO resShareInfoDTO, ReqCommonShareDTO reqHxgShareDTO) {
        if(StringUtils.isBlank(reqHxgShareDTO.getPromotionNo())) {
            log.info("reqHxgShareDTO.getPromotionNo()={}", reqHxgShareDTO.getPromotionNo());
            return ExecuteDTO.error(CommonCode.CODE_10000001.getCode(), "promotionNo不能为空");
        }
        // 查询活动信息
        ReqPromotionInfoDTO reqPromotionInfoDTO = new ReqPromotionInfoDTO();
        reqPromotionInfoDTO.setPromotionNo(reqHxgShareDTO.getPromotionNo());
        ExecuteDTO<ResPromotionInfoDTO> executeDTO =
                promotionInfoAnalysisService.getAllPromotionInfo(reqPromotionInfoDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        ResPromotionInfoDTO resPromotionInfoDTO = executeDTO.getData();
        if (resPromotionInfoDTO == null) {
            return ExecuteDTO.error(MarketErrorCode.CODE_17000100.getCode(), MarketErrorCode.CODE_17000100.getInMsg());
        }
        // 出参组装
        this.convertShareInfo(resShareInfoDTO, PromotionTypeEnum.getByCode(resPromotionInfoDTO.getPromotionType()), reqHxgShareDTO);
        return ExecuteDTO.success(resShareInfoDTO);
    }

    /**
     * 获取分享领取代理人优惠券信息-NEW
     *
     * @param reqHxgShareDTO goodsNo和userCouponNo
     * @return
     */
    private ExecuteDTO<ResShareInfoDTO> getShareAgentCoupon(ResShareInfoDTO resShareInfoDTO, ReqCommonShareDTO reqHxgShareDTO) {
        String url = String.format(ShareAgentUrlEnum.SHARE_PROMOTION_COUPON.getMiniShareUrl(), reqHxgShareDTO.getAgentNo(), reqHxgShareDTO.getUserCouponNo());
        if (StringUtils.isNotEmpty(reqHxgShareDTO.getPage())) {
            url = reqHxgShareDTO.getPage();
        }
        String shareUrl = hxgFrontUrl + url;
        resShareInfoDTO.setShareUrl(shareUrl);
        resShareInfoDTO.setMiniUrl(url);
        // 若小程序
        this.setMiniUrl(reqHxgShareDTO, resShareInfoDTO);
        return ExecuteDTO.success(resShareInfoDTO);
    }

    /**
     * 获取分享图片
     *
     * @param promotionTypeEnum
     * @param reqHxgShareDTO
     * @return
     */
    private void convertShareInfo(ResShareInfoDTO resShareInfoDTO, PromotionTypeEnum promotionTypeEnum, ReqCommonShareDTO reqHxgShareDTO) {
    	log.info("***WxMiniCodeAnalysisServiceImpl.convertShareInfo***, param1{}, param2{}, param3{},", 
    			resShareInfoDTO, promotionTypeEnum, reqHxgShareDTO);
        StringBuilder stringBuilder = new StringBuilder();
        String url = "";
        switch (promotionTypeEnum) {
            case TURNTABLE_DRAW:
                // 大转盘抽奖
                url = String.format(ShareAgentUrlEnum.SHARE_PROMOTION_TURNTABLE.getShareUrl(), reqHxgShareDTO.getStoreNo(), reqHxgShareDTO.getPromotionNo(),reqHxgShareDTO.getAgentNo());
                if (StringUtils.isNotEmpty(reqHxgShareDTO.getPage())) {
                    url = reqHxgShareDTO.getPage();
                }
                resShareInfoDTO.setShareUrl(stringBuilder.append(hxgFrontUrl).append(url).toString());
                resShareInfoDTO.setMiniUrl(url);
                // 若小程序
                this.setMiniUrl(reqHxgShareDTO, resShareInfoDTO);
                break;
            case LOTTERYWHEEL_DRAW:
                // 摇奖机抽奖
                // bossApp 老板分享活动 没有代理人
                if (StringUtils.isNotEmpty(reqHxgShareDTO.getAgentNo())) {
                    url = String.format(ShareAgentUrlEnum.SHARE_PROMOTION_LOTTERYWHEEL.getMiniShareUrl(), reqHxgShareDTO.getStoreNo(), reqHxgShareDTO.getPromotionNo(), reqHxgShareDTO.getAgentNo());
                } else {
                    url = String.format(ShareAgentUrlEnum.SHARE_BOSS_PROMOTION_LOTTERYWHEEL.getMiniShareUrl(), reqHxgShareDTO.getStoreNo(), reqHxgShareDTO.getPromotionNo());
                }

                if (StringUtils.isNotEmpty(reqHxgShareDTO.getPage())) {
                    url = reqHxgShareDTO.getPage();
                }
                resShareInfoDTO.setShareUrl(stringBuilder.append(hxgFrontUrl).append(url).toString());
                resShareInfoDTO.setMiniUrl(url);
                // 若小程序
                this.setMiniUrl(reqHxgShareDTO, resShareInfoDTO);
                break;
            case BLINDBOX_DRAW:
                // 拆盲盒抽奖
                url = String.format(ShareAgentUrlEnum.SHARE_PROMOTION_BLINDBOX.getMiniShareUrl(), reqHxgShareDTO.getStoreNo(), reqHxgShareDTO.getPromotionNo(),reqHxgShareDTO.getAgentNo());
                if (StringUtils.isNotEmpty(reqHxgShareDTO.getPage())) {
                    url = reqHxgShareDTO.getPage();
                }
                resShareInfoDTO.setShareUrl(stringBuilder.append(hxgFrontUrl).append(url).toString());
                resShareInfoDTO.setMiniUrl(url);
                // 若小程序
                this.setMiniUrl(reqHxgShareDTO, resShareInfoDTO);
                break;
            case SNARE_DRAW:
                // 套圈圈抽奖
                url = String.format(ShareAgentUrlEnum.SHARE_PROMOTION_SNARE.getMiniShareUrl(), reqHxgShareDTO.getStoreNo(), reqHxgShareDTO.getPromotionNo(),reqHxgShareDTO.getAgentNo());
                if (StringUtils.isNotEmpty(reqHxgShareDTO.getPage())) {
                    url = reqHxgShareDTO.getPage();
                }
                resShareInfoDTO.setShareUrl(stringBuilder.append(hxgFrontUrl).append(url).toString());
                resShareInfoDTO.setMiniUrl(url);
                // 若小程序
                this.setMiniUrl(reqHxgShareDTO, resShareInfoDTO);
                break;
            case CATFISHING_DRAW:
                // 小猫钓鱼抽奖
                url = String.format(ShareAgentUrlEnum.SHARE_PROMOTION_CATFISHING.getMiniShareUrl(), reqHxgShareDTO.getStoreNo(), reqHxgShareDTO.getPromotionNo(),reqHxgShareDTO.getAgentNo());
                if (StringUtils.isNotEmpty(reqHxgShareDTO.getPage())) {
                    url = reqHxgShareDTO.getPage();
                }
                resShareInfoDTO.setShareUrl(stringBuilder.append(hxgFrontUrl).append(url).toString());
                resShareInfoDTO.setMiniUrl(url);
                // 若小程序
                this.setMiniUrl(reqHxgShareDTO, resShareInfoDTO);
                break;
            case PLATFORM_MANUAL_AGENT_COUPON:
                // 平台-手动发代理人券
                url = String.format(ShareAgentUrlEnum.SHARE_PROMOTION_COUPON.getShareUrl(),reqHxgShareDTO.getAgentNo(), reqHxgShareDTO.getUserCouponNo());
                if (StringUtils.isNotEmpty(reqHxgShareDTO.getPage())) {
                    url = reqHxgShareDTO.getPage();
                }
                resShareInfoDTO.setShareUrl(stringBuilder.append(hxgFrontUrl).append(url).toString());
                resShareInfoDTO.setMiniUrl(url);
                // 若小程序
                this.setMiniUrl(reqHxgShareDTO, resShareInfoDTO);
                break;
            case SENCOND_KILL:
                // 秒杀
                url = String.format(ShareAgentUrlEnum.SHARE_SENCOND_KILL.getShareUrl(), reqHxgShareDTO.getAgentNo(), reqHxgShareDTO.getGoodsNo(), reqHxgShareDTO.getPromotionNo());
                if (StringUtils.isNotEmpty(reqHxgShareDTO.getPage())) {
                    url = reqHxgShareDTO.getPage();
                }
                resShareInfoDTO.setShareUrl(stringBuilder.append(hxgFrontUrl).append(url).toString());
                resShareInfoDTO.setMiniUrl(url);
                // 若小程序
                this.setMiniUrl(reqHxgShareDTO, resShareInfoDTO);
                break;
            case LIMIT_TIME:
                // 限时购
//                url = String.format(ShareAgentUrlEnum.SHARE_SENCOND_KILL.getShareUrl(), reqHxgShareDTO.getAgentNo(), reqHxgShareDTO.getGoodsNo(), reqHxgShareDTO.getPromotionNo());
//                if (StringUtils.isNotEmpty(reqHxgShareDTO.getPage())) {
                    url = reqHxgShareDTO.getPage();
//                }
//                resShareInfoDTO.setShareUrl(stringBuilder.append(hxgFrontUrl).append(url).toString());
//                resShareInfoDTO.setMiniUrl(url);
                // 若小程序
                this.setMiniUrl(reqHxgShareDTO, resShareInfoDTO);
                break;
            case PRE_SALE:
                // 预售
                // 小程序分享，直接根据小程序分享传参组装数据
                this.setMiniUrl(reqHxgShareDTO, resShareInfoDTO);
                break;
            case COMMUNITY_SOLITAIRE:
                // 社群接龙
                // 小程序分享，直接根据小程序分享传参组装数据
                this.setMiniUrl(reqHxgShareDTO, resShareInfoDTO);
                break;
            case COMMUNITY_SOLITAIRE_SJ:
                // 商家社群接龙
                // 小程序分享，直接根据小程序分享传参组装数据
                this.setMiniUrl(reqHxgShareDTO, resShareInfoDTO);
                break;
            default:
                break;
        }
    }


    /**
     * 查询代理人信息
     *
     * @param agentNo
     * @return
     */
    private ExecuteDTO<AgentInfoApiResponse> getAgentInfo(String agentNo) {
        AgentInfoApiRequest agentInfoApiRequest = new AgentInfoApiRequest();
        agentInfoApiRequest.setAgentNo(agentNo);
        // 查询代理人信息
        ExecuteDTO<AgentInfoApiResponse> agentExecuteDTO = agentApiProcessService.getAgentInfo(agentInfoApiRequest);
        if (!agentExecuteDTO.successFlag() || null == agentExecuteDTO.getData()) {
            return ExecuteDTO.error(UserErrorCode.CODE_13000009);
        }
        return ExecuteDTO.success(agentExecuteDTO.getData());
    }


    /**
     * 获取商品信息
     *
     * @param goodsNo
     * @return
     */
    private ExecuteDTO<ResGoodsDTO> getGoodsInfo(String goodsNo) {
        ReqGoodsDTO reqGoodsDTO = new ReqGoodsDTO();
        reqGoodsDTO.setGoodsNo(goodsNo);
        // 查询商品信息
        ExecuteDTO<ResGoodsDTO> executeDTO = goodsAnalysisService.getGoods(reqGoodsDTO);
        if (!executeDTO.successFlag() || null == executeDTO.getData()) {
            return ExecuteDTO.error(GoodsErrorCode.CODE_12000001);
        }
        // 商品无主图添加默认商品图片
        if (StringUtils.isBlank(executeDTO.getData().getMainPictureUrl())) {
            executeDTO.getData().setMainPictureUrl(this.getPictureUrl(new ReqUcDisplayConfigurationDTO(IdentityEnum.MERCHANT.getCode(),executeDTO.getData().getMerchantNo())));
        }

        // 参数传入的是子品商品，查询出主表的价格区间
        if (SeriesTypeEnum.SUB_GOODS.getCode().equals(executeDTO.getData().getSeriesType())) {
            ReqGoodsDTO parentGoodsDto = new ReqGoodsDTO();
            // 主品商品编号查询
            parentGoodsDto.setGoodsNo(executeDTO.getData().getParentGoodsNo());
            ExecuteDTO<ResGoodsDTO> executeParentDTO = goodsAnalysisService.getGoods(parentGoodsDto);

            if (!executeParentDTO.successFlag()) {
                return BeanCopierUtil.copy(executeParentDTO, ExecuteDTO.class);
            }
            if (null != executeParentDTO.getData()) {
                executeDTO.getData().setMinMarketPrice(executeParentDTO.getData().getMinMarketPrice());
                executeDTO.getData().setMaxMarketPrice(executeParentDTO.getData().getMaxMarketPrice());
                executeDTO.getData().setMinRetailPrice(executeParentDTO.getData().getMinRetailPrice());
                executeDTO.getData().setMaxRetailPrice(executeParentDTO.getData().getMaxRetailPrice());
            }
        }
        return ExecuteDTO.success(executeDTO.getData());
    }

    /**
     * 查询店铺信息
     *
     * @param storeNo
     * @return
     */
    private ExecuteDTO<GetStoreInfoResponse> getStoreInfo(String storeNo) {
        //所属店铺
        GetStoreInfoRequest getStoreInfoRequest = new GetStoreInfoRequest();
        getStoreInfoRequest.setStoreNo(storeNo);
        ExecuteDTO<GetStoreInfoResponse> storeExecuteDTO = userPublicService.queryStoreInfo(getStoreInfoRequest);
        if (!storeExecuteDTO.successFlag() || null == storeExecuteDTO.getData()) {
            return ExecuteDTO.error(storeExecuteDTO.getStatus(), storeExecuteDTO.getMsg());
        }
        return ExecuteDTO.success(storeExecuteDTO.getData());
    }


    /**
     * 获取分享图片
     *
     * @param promotionTypeEnum
     * @param reqHxgShareDTO
     * @return
     */
    private void convertShareInfo(ResShareDTO resHxgShareDTO, PromotionTypeEnum promotionTypeEnum, ReqCommonShareDTO reqHxgShareDTO) {
        StringBuilder stringBuilder = new StringBuilder();
        switch (promotionTypeEnum) {
            case TURNTABLE_DRAW:
                resHxgShareDTO.setShareThumb(turnTableUrl);
                if (!PlatformTypeEnum.PLATFORM_COMPANY.getCode().equals(reqHxgShareDTO.getPlatformType())) {
                    resHxgShareDTO.setShareUrl(stringBuilder.append(hxgFrontUrl).append(String.format(ShareUrlEnum.SHARE_PROMOTION_TURNTABLE.getShareUrl(), reqHxgShareDTO.getStoreNo(), reqHxgShareDTO.getPromotionNo())).toString());
                    resHxgShareDTO.setShareContent(shareContent);
                } else {
                    resHxgShareDTO.setMiniUrl(String.format(ShareUrlEnum.SHARE_PROMOTION_TURNTABLE.getMiniShareUrl(), reqHxgShareDTO.getStoreNo(), reqHxgShareDTO.getPromotionNo()));
                }
                break;
            case LOTTERYWHEEL_DRAW:
                resHxgShareDTO.setShareThumb(lotteryWheelUrl);
                if (!PlatformTypeEnum.PLATFORM_COMPANY.getCode().equals(reqHxgShareDTO.getPlatformType())) {
                    resHxgShareDTO.setShareUrl(stringBuilder.append(hxgFrontUrl).append(String.format(ShareUrlEnum.SHARE_PROMOTION_LOTTERYWHEEL.getShareUrl(), reqHxgShareDTO.getStoreNo(), reqHxgShareDTO.getPromotionNo())).toString());
                } else {
                    resHxgShareDTO.setMiniUrl(String.format(ShareUrlEnum.SHARE_PROMOTION_LOTTERYWHEEL.getMiniShareUrl(), reqHxgShareDTO.getStoreNo(), reqHxgShareDTO.getPromotionNo()));
                    resHxgShareDTO.setShareContent(shareContent);
                }
                break;
            case BLINDBOX_DRAW:
                resHxgShareDTO.setShareThumb(blindBoxUrl);
                if (!PlatformTypeEnum.PLATFORM_COMPANY.getCode().equals(reqHxgShareDTO.getPlatformType())) {
                    resHxgShareDTO.setShareUrl(stringBuilder.append(hxgFrontUrl).append(String.format(ShareUrlEnum.SHARE_PROMOTION_BLINDBOX.getShareUrl(), reqHxgShareDTO.getStoreNo(), reqHxgShareDTO.getPromotionNo())).toString());
                    resHxgShareDTO.setShareContent(shareContent);
                } else {
                    resHxgShareDTO.setMiniUrl(String.format(ShareUrlEnum.SHARE_PROMOTION_BLINDBOX.getMiniShareUrl(), reqHxgShareDTO.getStoreNo(), reqHxgShareDTO.getPromotionNo()));
                }
                break;
            case SNARE_DRAW:
                resHxgShareDTO.setShareThumb(snareUrl);
                if (!PlatformTypeEnum.PLATFORM_COMPANY.getCode().equals(reqHxgShareDTO.getPlatformType())) {
                    resHxgShareDTO.setShareUrl(stringBuilder.append(hxgFrontUrl).append(String.format(ShareUrlEnum.SHARE_PROMOTION_SNARE.getShareUrl(), reqHxgShareDTO.getStoreNo(), reqHxgShareDTO.getPromotionNo())).toString());
                    resHxgShareDTO.setShareContent(shareContent);
                } else {
                    resHxgShareDTO.setMiniUrl(String.format(ShareUrlEnum.SHARE_PROMOTION_SNARE.getMiniShareUrl(), reqHxgShareDTO.getStoreNo(), reqHxgShareDTO.getPromotionNo()));
                }
                break;
            case CATFISHING_DRAW:
                resHxgShareDTO.setShareThumb(catFishingUrl);
                if (!PlatformTypeEnum.PLATFORM_COMPANY.getCode().equals(reqHxgShareDTO.getPlatformType())) {
                    resHxgShareDTO.setShareUrl(stringBuilder.append(hxgFrontUrl).append(String.format(ShareUrlEnum.SHARE_PROMOTION_CATFISHING.getShareUrl(), reqHxgShareDTO.getStoreNo(), reqHxgShareDTO.getPromotionNo())).toString());
                    resHxgShareDTO.setShareContent(shareContent);
                } else {
                    resHxgShareDTO.setMiniUrl(String.format(ShareUrlEnum.SHARE_PROMOTION_CATFISHING.getMiniShareUrl(), reqHxgShareDTO.getStoreNo(), reqHxgShareDTO.getPromotionNo()));
                }
                break;
            case PLATFORM_MANUAL_AGENT_COUPON:
                resHxgShareDTO.setShareThumb(couponUrl);
                resHxgShareDTO.setShareTitle(couponTitle);
                resHxgShareDTO.setShareContent(couponContent);
                resHxgShareDTO.setShareUrl(String.format(ShareUrlEnum.SHARE_PROMOTION_COUPON.getShareUrl(), reqHxgShareDTO.getStoreNo(), reqHxgShareDTO.getUserCouponNo()));
                resHxgShareDTO.setMiniUrl(String.format(ShareUrlEnum.SHARE_PROMOTION_COUPON.getMiniShareUrl(), reqHxgShareDTO.getStoreNo(), reqHxgShareDTO.getUserCouponNo()));
                break;
            default:
                break;
        }
    }

    /**
     * 获取活动标签列表
     *
     * @param goodsNo
     * @return
     */
    private List<String> getActiveTagNames(String goodsNo) {
        ReqGoodsTagDTO activeReqGoodsTagDTO = new ReqGoodsTagDTO();
        // 商品编号
        activeReqGoodsTagDTO.setGoodsNo(goodsNo);
        // 活动标签
        activeReqGoodsTagDTO.setTagType(GoodsTagTypeEnum.PROMOTION_LABEL.getCode());
        ExecuteDTO<List<ResGoodsTagDTO>> executeDTO = goodsTagAnalysisService.getTagNamesByGoodsNo(activeReqGoodsTagDTO);
        if (executeDTO.successFlag()) {
            return executeDTO.getData().stream().map(ResGoodsTagDTO::getTagName).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    /**
     * 活动分享
     *
     * @param resHxgShareDTO
     * @param reqHxgShareDTO
     */
    private ExecuteDTO sharePromotion(ResShareDTO resHxgShareDTO, ReqCommonShareDTO reqHxgShareDTO) {

        // 查询活动信息
        ReqPromotionInfoDTO reqPromotionInfoDTO = new ReqPromotionInfoDTO();
        reqPromotionInfoDTO.setPromotionNo(reqHxgShareDTO.getPromotionNo());
        ExecuteDTO<ResPromotionInfoDTO> executeDTO =
                promotionInfoAnalysisService.getPromotionInfo(reqPromotionInfoDTO);
        if (!executeDTO.successFlag()) {
            return ExecuteDTO.error(executeDTO.getStatus(), executeDTO.getMsg());
        }
        // 出参组装
        resHxgShareDTO.setShareTitle(executeDTO.getData().getPromotionName());
        convertShareInfo(resHxgShareDTO, PromotionTypeEnum.getByCode(executeDTO.getData().getPromotionType()), reqHxgShareDTO);
        return ExecuteDTO.success();
    }

}
