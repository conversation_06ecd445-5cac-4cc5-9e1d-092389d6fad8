package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqSmsTrafficDTO;
import cn.htdt.marketcenter.dto.response.AtomResSmsTrafficDTO;
import cn.htdt.marketcenter.dto.response.AtomResStoreSmsTrafficDTO;
import cn.htdt.marketprocess.dto.request.ReqSmsTrafficDTO;

import java.util.List;

/**
 * 短信流量表 数据查询服务接口
 *
 * <AUTHOR>
 * @date 2021-06-25
 */
public interface AtomSmsTrafficAnalysisService {

    /**
     * 获取店铺短信流量信息
     *
     * @param atomReqDTO 查询参数
     * @return ExecuteDTO<AtomResSmsTrafficDTO>
     * <AUTHOR>
     */
    ExecuteDTO<AtomResSmsTrafficDTO> getSmsTraffic(AtomReqSmsTrafficDTO atomReqDTO);
    /**
     * 平台根据参数统计店铺总剩余短信条数
     *
     * @param atomReqDTO 查询参数
     * @return ExecuteDTO<Integer>
     * <AUTHOR>
     */
    ExecuteDTO<Integer> countStoreSurplus(AtomReqSmsTrafficDTO atomReqDTO);
    /**
     * 获取店铺剩余短信大于0的店铺数
     *
     * @param storeNoList 店铺编码列表
     * @return ExecuteDTO<Integer>
     * <AUTHOR>
     */
    ExecuteDTO<Integer> getSmsRemainingCount(List<String> storeNoList);

    /**
     * 根据参数分页查询所有店铺短信账户上的剩余短信条数信息
     *
     * @param reqSmsTrafficDTO 查询参数
     * @return List<SmsTrafficDomain>
     */
    ExecuteDTO<List<AtomResStoreSmsTrafficDTO>> getStoreSmsRemainingInfo(ReqSmsTrafficDTO reqSmsTrafficDTO);
}
