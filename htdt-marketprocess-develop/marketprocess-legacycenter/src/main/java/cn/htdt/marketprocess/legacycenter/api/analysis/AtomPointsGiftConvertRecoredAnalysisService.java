package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.AtomReqPointsGiftConvertRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomResPointsGiftConvertRecordDTO;

/**
 * 积分商城礼品兑换记录类
 *
 * <AUTHOR>
 * @since 2021-10-12
 */
public interface AtomPointsGiftConvertRecoredAnalysisService {
    /**
     * 根据条件查礼品兑换记录
     *
     * @param atomReqPointsGiftConvertRecordDTO 店铺编号/粉丝姓名/手机号/兑换起止时间
     * @return 礼品兑换记录分页数据
     */
    ExecuteDTO<ExecutePageDTO<AtomResPointsGiftConvertRecordDTO>> getConvertRecords(AtomReqPointsGiftConvertRecordDTO atomReqPointsGiftConvertRecordDTO);

    /**
     * 根据条件查礼品兑换记录 包括主图地址
     *
     * @param atomReqPointsGiftConvertRecordDTO 店铺编号/粉丝姓名/手机号/兑换起止时间
     * @return 礼品兑换记录分页数据
     */
    ExecuteDTO<ExecutePageDTO<AtomResPointsGiftConvertRecordDTO>> getConvertRecordsWithPictureUrl(AtomReqPointsGiftConvertRecordDTO atomReqPointsGiftConvertRecordDTO);

}
