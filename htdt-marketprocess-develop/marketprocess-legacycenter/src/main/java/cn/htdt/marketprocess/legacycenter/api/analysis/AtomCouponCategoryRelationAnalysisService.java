package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqCouponCategoryRelationDTO;
import cn.htdt.marketcenter.dto.response.AtomResCouponCategoryRelationDTO;

import java.util.List;

/**
 * <p>
 * 活动品牌表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-02
 */
public interface AtomCouponCategoryRelationAnalysisService {

    /**
     * @param atomReqCouponCategoryRelationDTO
     * @Description : 根据条件查询活动品牌关联表列表
     * <AUTHOR> wanglei
     */
    ExecuteDTO<List<AtomResCouponCategoryRelationDTO>> getCouponCategoryRelationList(AtomReqCouponCategoryRelationDTO atomReqCouponCategoryRelationDTO);

}
