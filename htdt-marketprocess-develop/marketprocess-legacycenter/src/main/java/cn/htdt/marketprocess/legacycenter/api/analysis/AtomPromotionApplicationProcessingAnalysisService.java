package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.ApplyPromoteRecordDto;
import cn.htdt.marketcenter.dto.request.AtomPromotionApplicationProcessingDTO;
import cn.htdt.marketcenter.dto.response.PromotionApplicationProcessingDTO;

import java.util.List;

/**
 * <p>
 * 推广申请处理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-12
 */
public interface AtomPromotionApplicationProcessingAnalysisService {

    /**
     * 查询推广信息
     * @param processingDTO
     * @return
     */
    ExecuteDTO<ExecutePageDTO<PromotionApplicationProcessingDTO>> getPromotionApplicationProcessing(AtomPromotionApplicationProcessingDTO processingDTO);

    ExecuteDTO<Object> updateRecordInfoList(List<ApplyPromoteRecordDto> list);

}
