package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqLotteryRewardGoodsRelationDTO;
import cn.htdt.marketcenter.dto.response.AtomResLotteryRewardGoodsRelationDTO;

import java.util.List;

/**
 * <p>
 * 活动奖品商品关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-02
 */
public interface AtomPromotionRewardGoodsRelationAnalysisService {

    ExecuteDTO<List<AtomResLotteryRewardGoodsRelationDTO>> listPromotionRewardGoodsRelation(AtomReqLotteryRewardGoodsRelationDTO atomReqLotteryRewardGoodsRelationDTO);


    /**
     * 通过奖品编码查询信息-单条
     * @param atomReqLotteryRewardGoodsRelationDTO
     * @return
     */
    ExecuteDTO<AtomResLotteryRewardGoodsRelationDTO> getLotteryRewardGoodsRelation(AtomReqLotteryRewardGoodsRelationDTO atomReqLotteryRewardGoodsRelationDTO);

    /**
     * 通过奖品编码查询信息-批量
     * @param atomReqLotteryRewardGoodsRelationDTO
     * @return
     */
    ExecuteDTO<List<AtomResLotteryRewardGoodsRelationDTO>> batchGetLotteryRewardGoodsRelation(AtomReqLotteryRewardGoodsRelationDTO atomReqLotteryRewardGoodsRelationDTO);

}
