package cn.htdt.marketprocess.legacycenter.biz.operat;

import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import org.apache.ibatis.session.SqlSession;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;

/**
 * <AUTHOR>
 * @Date 2021-01-15
 * @Description
 **/
public class BaseServiceImpl<T> {

    /**
     * 批量操作 SqlSession
     */
    protected SqlSession sqlSessionBatch(Class<T> entity) {
        return SqlHelper.sqlSessionBatch(entity);
    }

    /**
     * 获取 SqlStatement
     *
     * @param sqlMethod ignore
     * @return ignore
     */
    protected String sqlStatement(Class<T> entity, SqlMethod sqlMethod) {
        return SqlHelper.table(entity).getSqlStatement(sqlMethod.getMethod());
    }

    /**
     * 批量插入
     *
     * @param entityList ignore
     * @param batchSize  ignore
     * @return ignore
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatch(Collection<T> entityList, int batchSize) {
        Assert.notEmpty(entityList, "error: entityList must not be empty");

        Class<T> classEntity = null;
        for (T anEntityList : entityList) {
            classEntity = (Class<T>) anEntityList.getClass();
            if(classEntity != null){
                break;
            }
        }
        if (classEntity == null) {
            return false;
        }
        String sqlStatement = sqlStatement(classEntity, SqlMethod.INSERT_ONE);
        try (SqlSession batchSqlSession = sqlSessionBatch(classEntity)) {
            int i = 0;
            for (T anEntityList : entityList) {
                batchSqlSession.insert(sqlStatement, anEntityList);
                if (i >= 1 && i % batchSize == 0) {
                    batchSqlSession.flushStatements();
                }
                i++;
            }
            batchSqlSession.flushStatements();
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatch(Collection<T> entityList) {
        Assert.notEmpty(entityList, "error: entityList must not be empty");

        Class<T> classEntity = null;
        for (T anEntityList : entityList) {
            classEntity = (Class<T>) anEntityList.getClass();
            if(classEntity != null){
                break;
            }
        }
        if (classEntity == null) {
            return false;
        }
        String sqlStatement = sqlStatement(classEntity, SqlMethod.INSERT_ONE);
        int batchSize = 1000;
        if(entityList.size() < 1000){
            batchSize = entityList.size();
        }
        try (SqlSession batchSqlSession = sqlSessionBatch(classEntity)) {
            int i = 0;
            for (T anEntityList : entityList) {
                batchSqlSession.insert(sqlStatement, anEntityList);
                if (i >= 1 && i % batchSize == 0) {
                    batchSqlSession.flushStatements();
                }
                i++;
            }
            batchSqlSession.flushStatements();
        }
        return true;
    }
}