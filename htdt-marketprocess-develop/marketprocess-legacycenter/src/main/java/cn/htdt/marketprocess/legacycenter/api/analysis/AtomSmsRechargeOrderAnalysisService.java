package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.AtomReqSmsRechargeOrderDTO;
import cn.htdt.marketcenter.dto.response.AtomResSmsRechargeOrderDTO;

import java.util.List;

/**
 * 短信充值订单服务接口
 *
 * <AUTHOR>
 * @date 2021-07-01
 **/
public interface AtomSmsRechargeOrderAnalysisService {

    /**
     * 分页查询短信充值订单列表
     *
     * @param atomReqSmsRechargeOrderDTO 查询参数
     * @return ExecuteDTO<ExecutePageDTO < AtomResSmsRechargeOrderDTO>>
     * <AUTHOR>
     * @date 2021-07-01 09:50:58
     */
    ExecuteDTO<ExecutePageDTO<AtomResSmsRechargeOrderDTO>> getSmsRechargeOrderListByPage(AtomReqSmsRechargeOrderDTO atomReqSmsRechargeOrderDTO);

    /**
     * @description 短信充值月度统计
     * <AUTHOR>
     * @date 2021-12-21 16:01:00
     * @param atomReqSmsRechargeOrderDTO
     * @return
     */
    ExecuteDTO<List<AtomResSmsRechargeOrderDTO>> getSmsRechargeOrderMonthCount(AtomReqSmsRechargeOrderDTO atomReqSmsRechargeOrderDTO);

}
