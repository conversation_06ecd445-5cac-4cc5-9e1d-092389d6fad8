package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.AtomReqLotteryDrawNumDTO;
import cn.htdt.marketcenter.dto.request.AtomReqLotteryDrawRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomResLotteryDrawNumDTO;
import cn.htdt.marketcenter.dto.response.AtomResLotteryDrawRecordCountVO;
import cn.htdt.marketcenter.dto.response.AtomResLotteryDrawRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomResStatisticsLotteryStockDTO;

import java.util.List;

/**
 * <p>
 * 活动中奖记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-02
 */
public interface AtomLotteryDrawRecordAnalysisService {

    /**
     * 查看中奖记录分页列表
     * @param atomReqLotteryDrawRecordDTO
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResLotteryDrawRecordDTO>> getLotteryDrawRecordPage(AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO);

    /**
     * @param atomReqLotteryDrawRecordDTO
     * @Description : BossApp->抽奖列表->中奖记录统计
     * <AUTHOR> 卜金隆
     * @date : 2021/7/06 19:02
     */
    ExecuteDTO<List<AtomResLotteryDrawRecordCountVO>> getLotteryDrawRecordCountList(AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO);


    /**
     * 查看中奖记录数量
     * @param atomReqLotteryDrawRecordDTO
     * @return
     */
    ExecuteDTO<Integer> getLotteryDrawRecordCount(AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO);

    /**
     * 批量查看中奖记录数量
     * @param atomReqLotteryDrawRecordDTO
     * @return
     */
    ExecuteDTO<List<AtomResLotteryDrawRecordDTO>> batchGetLotteryDrawRecordCount(AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO);

    /**
     * @param atomReqLotteryDrawRecordDTO
     * @Description : 批量获取中奖记录
     * <AUTHOR> 王磊
     * @date : 2021/4/26 14:28
     */
    ExecuteDTO<List<AtomResLotteryDrawRecordDTO>> batchGetLotteryDrawRecord(AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO);

    /**
     * 统计抽奖数量
     *
     * @param atomReqLotteryDrawRecordDTO
     * @return
     */
    ExecuteDTO<List<AtomResStatisticsLotteryStockDTO>> statisticsLotteryStock(AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO);

    /**
     * 查询抽奖记录
     *
     * @param atomReqLotteryDrawRecordDTO
     * @return
     */
    ExecuteDTO<List<AtomResLotteryDrawRecordDTO>> listLotteryDrawRecord(AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO);

    /**
     * 查询我的抽奖记录
     *
     * @param atomReqLotteryDrawRecordDTO
     * @return
     */
    ExecuteDTO<List<AtomResLotteryDrawRecordDTO>> getMyLotteryDrawRecord(AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO);

    /**
      * @param atomReqLotteryDrawRecordDTO
      * @Description : 查询核销列表
      * <AUTHOR> 高繁
      * @date : 2021/3/19 14:01
     */
    ExecuteDTO<ExecutePageDTO<AtomResLotteryDrawRecordDTO>> getWriteOffList(AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO);

    /**
     * @param atomReqLotteryDrawRecordDTO
     * @Description : 根据单号批量查询-批量
     * <AUTHOR> 王磊
     * @date : 2021/3/29 14:01
     */
    ExecuteDTO<List<AtomResLotteryDrawRecordDTO>> getWriteOffListByOrderNos(AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO);

    /**
     * @param atomReqLotteryDrawRecordDTO
     * @Description : 根据单号批量查询-单条
     * <AUTHOR> 王磊
     * @date : 2021/3/29 14:01
     */
    ExecuteDTO<AtomResLotteryDrawRecordDTO> getWriteOffListByOrderNo(AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO);

    /**
     * @param atomReqLotteryDrawNumDTO
     * @Description : 查询活动下中奖次数
     * <AUTHOR> 高繁
     * @date : 2021/4/9 15:27
     */
    ExecuteDTO<AtomResLotteryDrawNumDTO> getDrawNumByPromotion(AtomReqLotteryDrawNumDTO atomReqLotteryDrawNumDTO);

    /**
     * @param atomReqLotteryDrawNumDTO
     * @Description : 查询某奖品下中奖次数
     * <AUTHOR> 王磊
     * @date : 2021/4/9 15:27
     */
    ExecuteDTO<AtomResLotteryDrawNumDTO> getRecordCountByTerm(AtomReqLotteryDrawNumDTO atomReqLotteryDrawNumDTO);

    /**
     * @Description 查询核销码在当前会员店是否存在
     * <AUTHOR>
     * @Date 2021/4/13 11:09
     * @Param 
     * @Return 
    */
    ExecuteDTO<AtomResLotteryDrawRecordDTO> getWriteOffCodeIsExist(AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO);

    /**
      * @param recordNoList
      * @Description : 根据中奖记录编号集合查询订单集合
      * <AUTHOR> 高繁
      * @date : 2021/5/18 13:49
     */
    ExecuteDTO<List<String>> getOrderNoListByRecordNos(List<String> recordNoList);

    /**
      * @param atomReqLotteryDrawRecordDTO
      * @Description : 根据中奖记录编号集合或者订单编号集合查询中奖记录
      * <AUTHOR> 高繁
      * @date : 2021/9/8 11:21
     */
    ExecuteDTO<List<AtomResLotteryDrawRecordDTO>> getRewardRecordList(AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO);

    /**
     * 查询某粉丝未使用的优惠券数量
     *
     * @param fansNo 粉丝编号
     * @return 未使用的优惠券数量
     */
    ExecuteDTO<Integer> getFansUnusedCoupons(String fansNo);

    /**
     * 查询某粉丝未兑换的礼品 包含实物礼品和话费券
     *
     * @param fansNo 粉丝编号
     * @return 未兑换的礼品数量
     */
    ExecuteDTO<Integer> getUnconvertedGift(String fansNo);

    /**
     * 根据原始订单的订单号查出该订单号产生的中奖记录
     *
     * @param sourceOrderNo 原始下单的订单号
     * @return 中奖记录
     */
    ExecuteDTO<List<AtomResLotteryDrawRecordDTO>> getRewardRecordListBySourceOrderNo(String sourceOrderNo);
}
