package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.AtomReqLotteryRewardInfoDTO;
import cn.htdt.marketcenter.dto.request.AtomReqLotteryRuleDTO;
import cn.htdt.marketcenter.dto.request.AtomReqLotteryStoreRuleDTO;
import cn.htdt.marketcenter.dto.response.AtomResLotteryRewardInfoDTO;
import cn.htdt.marketcenter.dto.response.AtomResLotteryRuleDTO;
import cn.htdt.marketcenter.dto.response.AtomResLotteryStoreRuleDTO;
import cn.htdt.marketcenter.dto.response.AtomResPromotionInfoDTO;

import java.util.List;

/**
 * <p>
 * 抽奖活动表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-02
 */
public interface AtomLotteryRuleAnalysisService {

    /**
     * 查看单条抽奖活动
     * @param atomReqLotteryRuleDTO
     * @return
     */
    ExecuteDTO<AtomResLotteryRuleDTO> viewLotteryInfo(AtomReqLotteryRuleDTO atomReqLotteryRuleDTO);

    /**
     * 查看抽奖活动分页列表-用于抽奖列表
     * @param atomReqLotteryRuleDTO
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResLotteryRuleDTO>> getLotteryInfoPage(AtomReqLotteryRuleDTO atomReqLotteryRuleDTO);

    /**
     * 查看抽奖活动分页列表-用于抽奖列表-商家下店铺抽奖查询
     * @param atomReqLotteryRuleDTO
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResLotteryRuleDTO>> getLotteryInfoMerchantStorePage(AtomReqLotteryRuleDTO atomReqLotteryRuleDTO);

    /**
     * 查看抽奖活动分页列表-用于商家参与的平台抽奖列表
     * @param atomReqLotteryRuleDTO
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResLotteryRuleDTO>> getMerchantJoinByParams(AtomReqLotteryRuleDTO atomReqLotteryRuleDTO);

    /**
     * 查看抽奖活动分页列表-用于店铺参与的平台抽奖列表
     * @param atomReqLotteryRuleDTO
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResLotteryRuleDTO>> getStoreJoinByParams(AtomReqLotteryRuleDTO atomReqLotteryRuleDTO);

    /**
     * 获取抽奖活动-抽奖规则
     * @param atomReqLotteryRuleDTO
     * @return
     */
    ExecuteDTO<AtomResLotteryRuleDTO> getLotteryRule(AtomReqLotteryRuleDTO atomReqLotteryRuleDTO);

    /**
     * 获取抽奖活动-活动店铺
     * @param atomReqLotteryStoreRuleDTO
     * @return
     */
    ExecuteDTO<AtomResLotteryStoreRuleDTO> getLotteryStore(AtomReqLotteryStoreRuleDTO atomReqLotteryStoreRuleDTO);

    /**
     * 获取抽奖活动-参与奖品
     * @param atomReqLotteryRewardInfoDTO
     * @return
     */
    ExecuteDTO<List<AtomResLotteryRewardInfoDTO>> getLotteryReward(AtomReqLotteryRewardInfoDTO atomReqLotteryRewardInfoDTO);

    /**
     * 获取抽奖活动-参与奖品列表
     * @param atomReqLotteryRewardInfoDTO
     * @return
     */
    ExecuteDTO<List<AtomResLotteryRewardInfoDTO>> listLotteryRewardInfo(AtomReqLotteryRewardInfoDTO atomReqLotteryRewardInfoDTO);

    /**
     * 获取活动规则列表
     * 不分页
     *
     * @param atomReqLotteryRuleDTO
     * @return
     */
    ExecuteDTO<List<AtomResLotteryRuleDTO>> listLotteryInfo(AtomReqLotteryRuleDTO atomReqLotteryRuleDTO);

    /**
     * 查询活动信息
     * 关联奖品信息
     *
     * @param atomReqLotteryRuleDTO
     * @return
     */
    ExecuteDTO<AtomResPromotionInfoDTO> getLotteryInfo(AtomReqLotteryRuleDTO atomReqLotteryRuleDTO);

    /**
     * 查询同一时间段内相同抽奖活动的个数
     *
     * @param atomReqLotteryRuleDTO atomReqLotteryRuleDTO
     * @return 同一时间段内相同抽奖活动的个数
     */
    ExecuteDTO<Integer> getSameLottery(AtomReqLotteryRuleDTO atomReqLotteryRuleDTO);
}
