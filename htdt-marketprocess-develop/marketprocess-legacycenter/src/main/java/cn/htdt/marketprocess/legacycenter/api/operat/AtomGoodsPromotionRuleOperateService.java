package cn.htdt.marketprocess.legacycenter.api.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqGoodsPromotionRuleDTO;

/**
 * 商品促销活动规则操作类
 *
 * <AUTHOR>
 * @date 2021-06-28
 */
public interface AtomGoodsPromotionRuleOperateService {

    /**
     * 新增商品促销活动规则
     *
     * @param goodsPromotionRuleDTO
     * @return
     * <AUTHOR>
     * @date 2021-06-28
     */
    ExecuteDTO saveGoodsPromotionRule(AtomReqGoodsPromotionRuleDTO goodsPromotionRuleDTO);

    /**
     * 更新商品促销活动规则
     *
     * @param goodsPromotionRuleDTO
     * @return
     * <AUTHOR>
     * @date 2021-06-28
     */
    ExecuteDTO updateGoodsPromotionRule(AtomReqGoodsPromotionRuleDTO goodsPromotionRuleDTO);

    /**
     * 删除商品促销活动规则
     *
     * @param promotionNo
     * @return
     * <AUTHOR>
     * @date 2021-07-05
     */
    ExecuteDTO deleteGoodsPromotionRule(String promotionNo);
}
