package cn.htdt.marketprocess.legacycenter.api.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqGoodsPromotionCategoryRelationDTO;

import java.util.List;

/**
 * 商品促销活动参与类目的操作类
 *
 * <AUTHOR>
 * @date 2022/2/18 9:00
 */
public interface AtomGoodsPromotionCategoryRelationOperateService {

    /**
     * 新增参与促销活动的类目关联
     *
     * @param reqDTO
     * @return
     * <AUTHOR>
     * @date 2022/2/18 9:00
     */
    ExecuteDTO savePromotionCategoryRelation(List<AtomReqGoodsPromotionCategoryRelationDTO> reqDTO);

    /**
     * 根据促销活动编号，删除类目关联
     *
     * @param promotionNo
     * @return
     * <AUTHOR>
     * @date 2022/2/18 9:00
     */
    ExecuteDTO delPromotionCategoryRelation(String promotionNo);

}
