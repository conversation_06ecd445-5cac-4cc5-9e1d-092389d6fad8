package cn.htdt.marketprocess.legacycenter.api.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqPromotionStoreRuleDTO;

/**
 * 促销活动店铺规则操作类
 *
 * <AUTHOR>
 * @date 2021-06-28
 */
public interface AtomPromotionStoreRuleOperateService {

    /**
     * 商品促销新增活动店铺规则信息
     *
     * @param storeRuleDTO
     * @return
     * <AUTHOR>
     * @date 2021-06-28
     */
    ExecuteDTO savePromotionStoreRule(AtomReqPromotionStoreRuleDTO storeRuleDTO);

    /**
     * 修改商品促销活动店铺规则信息
     *
     * @param storeRuleDTO
     * @return
     * <AUTHOR>
     * @date 2021-06-28
     */
    ExecuteDTO updatePromotionStoreRule(AtomReqPromotionStoreRuleDTO storeRuleDTO);

    /**
     * 删除促销活动设置的店铺规则信息
     *
     * @param promotionNo
     * @return
     * <AUTHOR>
     * @date 2021-07-05
     */
    ExecuteDTO deletePromotionStoreRule(String promotionNo);
}
