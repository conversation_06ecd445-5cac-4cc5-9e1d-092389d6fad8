package cn.htdt.marketprocess.legacycenter.api.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqGoodsPromotionBookingDTO;

/**
 * 活动商品预约类
 * <AUTHOR>
 * @date 2021/7/20 17:00
 */
public interface AtomGoodsPromotionBookingOperateService {

    /**
     * @param
     * @Description : 新增活动商品预约信息
     * <AUTHOR> 卜金隆
     * @date : 2021/7/20 17:59
     */
    ExecuteDTO saveGoodsPromotionBookingInfo(AtomReqGoodsPromotionBookingDTO atomReqGoodsPromotionBookingDTO);

    /**
     * @param
     * @Description : 修改活动商品预约信息
     * <AUTHOR> 卜金隆
     * @date : 2021/7/20 17:59
     */
    ExecuteDTO modifyGoodsPromotionBookingInfo(AtomReqGoodsPromotionBookingDTO atomReqGoodsPromotionBookingDTO);
}

