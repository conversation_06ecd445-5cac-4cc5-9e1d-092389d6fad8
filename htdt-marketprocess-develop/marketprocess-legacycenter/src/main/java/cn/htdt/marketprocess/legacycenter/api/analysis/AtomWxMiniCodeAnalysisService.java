package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqWxMiniCodeDTO;
import cn.htdt.marketcenter.dto.response.AtomResWxMiniCodeDTO;

/**
 * <p>
 * 微信小程序二维码生成记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-27
 */
public interface AtomWxMiniCodeAnalysisService {

    /**
     * 查询微信小程序二维码生成操作记录
     * @param atomReqWxMiniCodeDTO 请求参数
     * <AUTHOR>
     */
    public ExecuteDTO<AtomResWxMiniCodeDTO> getWxMiniCode(AtomReqWxMiniCodeDTO atomReqWxMiniCodeDTO);

}
