package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqUserVirtualCoinDTO;
import cn.htdt.marketcenter.dto.response.AtomResUserVirtualCoinDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/20
 */
public interface AtomUserVirtualCoinAnalysisService {
    /**
     * 查询用户橙豆列表
     * @param reqUserVirtualCoinDTO
     * @return
     */
    ExecuteDTO<List<AtomResUserVirtualCoinDTO>> selectUserVirtualCoinList(AtomReqUserVirtualCoinDTO reqUserVirtualCoinDTO);

}
