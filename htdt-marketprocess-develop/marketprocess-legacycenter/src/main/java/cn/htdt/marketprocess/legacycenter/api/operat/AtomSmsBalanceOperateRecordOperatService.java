package cn.htdt.marketprocess.legacycenter.api.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqSmsBalanceOperateRecordDTO;

/**
 * 短信余额操作记录表 操作服务接口
 *
 * <AUTHOR>
 * @date 2021-7-8
 */
public interface AtomSmsBalanceOperateRecordOperatService {

    /**
     * 新增短信余额操作记录
     *
     * @param atomReqDTO 请求参数
     * @return ExecuteDTO
     * <AUTHOR>
     */
    ExecuteDTO addSmsBalanceOperateRecord(AtomReqSmsBalanceOperateRecordDTO atomReqDTO);

}
