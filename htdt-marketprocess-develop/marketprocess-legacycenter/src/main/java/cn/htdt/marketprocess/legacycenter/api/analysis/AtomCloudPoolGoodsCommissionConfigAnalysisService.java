package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqCloudPoolGoodsCommissionConfigDTO;
import cn.htdt.marketcenter.dto.response.AtomResCloudPoolGoodsCommissionConfigDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-01-21
 * @Description 云池商品佣金配置查询
 **/
public interface AtomCloudPoolGoodsCommissionConfigAnalysisService {
    /**
     * 根据goodsNo，肯定是单条，包含所有信息
     * goodsNo，必须要存在，不然就是查询集合用 getListConfigInfoByParam
     * @param
     * @return
     * <AUTHOR>
     */
    ExecuteDTO<AtomResCloudPoolGoodsCommissionConfigDTO> getConfigInfoByGoodsNo(AtomReqCloudPoolGoodsCommissionConfigDTO configDTO);
    /**
     * 根据goodsNo，肯定是单条，包含所有信息
     * 查询集合塞 goodsNoList
     * @param
     * @return
     * <AUTHOR>
     */
    ExecuteDTO<List<AtomResCloudPoolGoodsCommissionConfigDTO>> getListConfigInfoByParam(AtomReqCloudPoolGoodsCommissionConfigDTO configDTO);

}


