package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqPromotionStoreRuleDTO;
import cn.htdt.marketcenter.dto.response.AtomResPromotionStoreRuleDTO;

import java.util.List;

/**
 * <p>
 * 活动店铺规则表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-02
 */
public interface AtomPromotionStoreRuleAnalysisService {

    /**
     * @Description : 查询报名活动是否被其他的活动引用
     * <AUTHOR>
     * @Date 2021/4/19 14:01
     * @Param
     * @Return
     */
    ExecuteDTO<List<AtomResPromotionStoreRuleDTO>> getEnrollIsUsedByOthersList(AtomReqPromotionStoreRuleDTO reqDto);

    /**
     * @Description : 批量获取活动店铺类型
     * <AUTHOR>
     * @Param
     * @Return
     */
    ExecuteDTO<List<AtomResPromotionStoreRuleDTO>> getPromotionStoreRuleList(AtomReqPromotionStoreRuleDTO reqDto);

    /**
     * 根据活动编码获得全部店铺关系
     */
    ExecuteDTO<Integer> countRuleNum(AtomReqPromotionStoreRuleDTO reqDto);

    /**
     * 查询促销活动设置的店铺规则以及店铺报名详情
     * <p>
     * 用于判断店铺是否还在报名活动列表中
     *
     * @param reqDto
     * @return
     * <AUTHOR>
     * @date 2021-07-02
     */
    ExecuteDTO<AtomResPromotionStoreRuleDTO> selectPromotionStoreRuleAndDetail(AtomReqPromotionStoreRuleDTO reqDto);

    /**
     * 根据活动编号查询设置的参与店铺的规则
     *
     * @param promotionNo
     * @return
     * <AUTHOR>
     * @date 2021-07-08
     */
    ExecuteDTO<AtomResPromotionStoreRuleDTO> selectPromotionStoreRuleByPromotionNo(String promotionNo);

}
