package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketprocess.dto.request.ReqSmsRechargeAdjustRecordDTO;
import cn.htdt.marketprocess.dto.response.ResSmsRechargeAdjustRecordDTO;

/**
 * 短信充值记录调整记录表
 *
 * <AUTHOR>
 * @date 2022/9/19
 **/
public interface AtomSmsRechargeAdjustRecordAnalysisService {
    /**
     * 平台运营人员登录千橙掌柜PC，营销管理-会员短信账户,点击操作记录
     *
     * @param reqDTO 查询参数
     * @return ExecuteDTO<ExecutePageDTO < ResSmsRechargeOrderDTO>>
     * <AUTHOR>
     * @date 2021-09-19 09:50:58
     */
    ExecuteDTO<ExecutePageDTO<ResSmsRechargeAdjustRecordDTO>> getSmsRechargeAdjustRecordListByParam(ReqSmsRechargeAdjustRecordDTO reqDTO);
}
