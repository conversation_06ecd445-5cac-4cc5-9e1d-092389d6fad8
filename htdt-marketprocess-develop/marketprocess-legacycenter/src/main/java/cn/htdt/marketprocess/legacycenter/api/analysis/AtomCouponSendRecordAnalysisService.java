package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.AtomReqCouponSendRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomResCouponSendRecordDTO;

/**
 * <p>
 * 优惠券发送记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-02
 */
public interface AtomCouponSendRecordAnalysisService {

    /**
     * 查看发券记录分页列表
     *
     * @param atomReqCouponSettingDTO
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResCouponSendRecordDTO>> getCouponSendRecordListPage(AtomReqCouponSendRecordDTO atomReqCouponSettingDTO);

}
