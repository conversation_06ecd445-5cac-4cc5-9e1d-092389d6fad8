package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.AtomReqEnrollRuleListDTO;
import cn.htdt.marketcenter.dto.response.AtomResEnrollRuleListDTO;
import cn.htdt.marketcenter.dto.response.AtomResEnrollRuleShopNumDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/1 15:51
 */
public interface AtomEnrollRuleAnalysisService {


    /**
      * @param reqEnrollRuleListDTO
      * @Description : 报名活动列表查询
      * <AUTHOR> 高繁
      * @date : 2021/4/1 16:59
     */
    ExecuteDTO<ExecutePageDTO<AtomResEnrollRuleListDTO>> getEnrollPromotionList(AtomReqEnrollRuleListDTO reqEnrollRuleListDTO);


    /**
      * @param promotionNoList
      * @Description : 根据活动编号查询参与店铺数
      * <AUTHOR> 高繁
      * @date : 2021/4/6 10:37
     */
    ExecuteDTO<List<AtomResEnrollRuleShopNumDTO>> getEnrollShopNumList(List<String> promotionNoList);
    /**
     * @param reqEnrollRuleListDTO
     * @Description : 根据活动编号查询活动明细
     * <AUTHOR> 高繁
     * @date : 2021/4/6 10:37
     */
    ExecuteDTO<AtomResEnrollRuleListDTO> getEnrollPromotionInfo(AtomReqEnrollRuleListDTO reqEnrollRuleListDTO);


}
