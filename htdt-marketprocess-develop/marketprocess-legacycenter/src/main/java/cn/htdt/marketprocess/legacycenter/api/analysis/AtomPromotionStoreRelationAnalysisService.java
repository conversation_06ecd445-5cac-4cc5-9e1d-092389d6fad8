package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqPromotionStoreRelationDTO;
import cn.htdt.marketcenter.dto.response.AtomResPromotionStoreRelationDTO;

import java.util.List;

/**
 * <p>
 * 活动店铺关联上下架表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-18
 */
public interface AtomPromotionStoreRelationAnalysisService {

    /**
     * 获取活动店铺关联关系
     *
     * @param atomReqPromotionStoreRelationDTO
     * @return
     */
    ExecuteDTO<AtomResPromotionStoreRelationDTO> getLotteryStoreRelation(AtomReqPromotionStoreRelationDTO atomReqPromotionStoreRelationDTO);

    /**
     * 判断并返回关联关系
     *
     * @param atomReqPromotionStoreRelationDTO
     * @return
     */
    ExecuteDTO<AtomResPromotionStoreRelationDTO> boolLotteryStoreRelation(AtomReqPromotionStoreRelationDTO atomReqPromotionStoreRelationDTO);

    /**
     * 批量判断并返回关联关系
     *
     * @param atomReqPromotionStoreRelationDTOs
     * @return
     */
    ExecuteDTO<List<AtomResPromotionStoreRelationDTO>> boolLotteryStoreRelationList(List<AtomReqPromotionStoreRelationDTO> atomReqPromotionStoreRelationDTOs);

    /**
     * 获取商品平台促销活动店铺关联上下架关系
     *
     * @param atomReqPromotionStoreRelationDTO
     * @return
     * <AUTHOR>
     * @date 2021-07-02
     */
    ExecuteDTO<AtomResPromotionStoreRelationDTO> getGoodsPromotionStoreRelation(AtomReqPromotionStoreRelationDTO atomReqPromotionStoreRelationDTO);
    
    /**
     * 获取商家店铺信息, 即店铺编码为空, 记录分派方式是全部店铺还是指定店铺的记录
     *
     * @param atomReqPromotionStoreRelationDTO
     * @return
     * <AUTHOR>
     * @date 2023-06-03
     */
    ExecuteDTO<AtomResPromotionStoreRelationDTO> getSjPromotionStoreRelation(AtomReqPromotionStoreRelationDTO atomReqPromotionStoreRelationDTO);
    
    /**
     * 获取商家社群接龙关联店铺信息
     * @param atomReqPromotionStoreRelationDTO
     * @return
     * <AUTHOR>
     * @date 2023-06-03
     */
    ExecuteDTO<List<AtomResPromotionStoreRelationDTO>> getStoreRelationList(AtomReqPromotionStoreRelationDTO atomReqPromotionStoreRelationDTO);
    
}
