package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.AtomReqPointsConfigLogDTO;
import cn.htdt.marketcenter.dto.response.AtomResPointsConfigLogDTO;


/**
 * <p>
 * 积分配置记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
public interface AtomPointsConfigLogAnalysisService {
    /**
     * 根据条件查询积分配置的操作记录
     *
     * @param configLogDTO 查询条件
     * @return 积分配置的操作记录列表
     */
    ExecuteDTO<ExecutePageDTO<AtomResPointsConfigLogDTO>> getPointsConfigLog(AtomReqPointsConfigLogDTO configLogDTO);
}
