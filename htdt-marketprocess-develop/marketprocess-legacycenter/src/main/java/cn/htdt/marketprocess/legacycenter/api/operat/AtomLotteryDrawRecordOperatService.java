package cn.htdt.marketprocess.legacycenter.api.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqLotteryDrawRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomResLotteryDrawRecordDTO;
import cn.htdt.marketprocess.domain.LotteryDrawRecordDomain;

import java.util.List;

/**
 * <p>
 * 活动中奖记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-02
 */
public interface AtomLotteryDrawRecordOperatService {

    /**
     * 作废中奖记录
     * @param atomReqLotteryDrawRecordDTO
     * @return
     */
    ExecuteDTO invalidLotteryDrawRecord(AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO);

    /**
     * 添加中奖记录
     *
     * @param atomReqLotteryDrawRecordDTO
     * @return
     */
    ExecuteDTO addLotteryDrawRecord(AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO);


    /**
     * 中奖记录-中奖核销-单条
     *
     * @param atomReqLotteryDrawRecordDTO
     * @return
     */
    ExecuteDTO<Integer> modifyLotteryDrawRecordWriteOff(AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO);


    /**
     * 批量更新抽奖记录
     *
     * @param atomReqLotteryDrawRecordDTO
     * @return
     */
    ExecuteDTO<Integer> batchModifyLotteryDrawRecord(List<AtomReqLotteryDrawRecordDTO> atomReqLotteryDrawRecordDTO);

    /**
     * 中奖记录-中奖核销-批量
     *
     * @param atomReqLotteryDrawRecordDTOs
     * @return
     */
    ExecuteDTO<Integer> batchModifyLotteryDrawRecordWriteOff(List<AtomReqLotteryDrawRecordDTO> atomReqLotteryDrawRecordDTOs);

    /**
     * 更新中奖记录中的优惠券的使用状态
     *
     * @param atomReqLotteryDrawRecordDTO
     * <AUTHOR>
     * @date 2022-01-28
     */
    ExecuteDTO<Integer> updateLotteryDrawRecordCoupon(AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO);

    /**
     * 查询核销码
     *
     * @param atomReqLotteryDrawRecordDTO
     * <AUTHOR>
     * @date 2022-01-28
     */
    ExecuteDTO<AtomResLotteryDrawRecordDTO> getWriteOffCode(AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO);
}
