package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.AtomReqCouponGoodsDTO;
import cn.htdt.marketcenter.dto.request.AtomReqExpandRuleDTO;
import cn.htdt.marketcenter.dto.response.AtomResCouponGoodsDTO;
import cn.htdt.marketcenter.dto.response.AtomResExpandRuleDTO;

/**
 * <p>
 * 膨胀红包规则 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
public interface AtomExpandRuleAnalysisService {

    /**
     * 查看膨胀红包活动分页列表-用于膨胀红包列表
     *
     * @param atomReqExpandRuleDTO
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResExpandRuleDTO>> getExpandPage(AtomReqExpandRuleDTO atomReqExpandRuleDTO);

    /**
     * 获取店铺膨胀红包列表
     *
     * @param atomReqExpandRuleDTO
     * @return
     */
//    ExecuteDTO<List<AtomResExpandRuleDTO>> listExpandForStore(AtomReqExpandRuleDTO atomReqExpandRuleDTO);

    /**
     * 获取膨胀红包活动-膨胀红包规则
     *
     * @param atomReqExpandRuleDTO
     * @return
     */
    ExecuteDTO<AtomResExpandRuleDTO> getExpandRule(AtomReqExpandRuleDTO atomReqExpandRuleDTO);

    /**
     * 获取膨胀红包活动-膨胀红包参与商品
     *
     * @param atomReqCouponGoodsDTO
     * @return
     */
    ExecuteDTO<AtomResCouponGoodsDTO> getExpandGoodsRelation(AtomReqCouponGoodsDTO atomReqCouponGoodsDTO);

}
