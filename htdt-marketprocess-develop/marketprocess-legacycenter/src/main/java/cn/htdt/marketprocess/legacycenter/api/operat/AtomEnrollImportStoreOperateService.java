package cn.htdt.marketprocess.legacycenter.api.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqEnrollImportStoreDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/22 15:51
 */
public interface AtomEnrollImportStoreOperateService {

    /**
      * @param reqDTO
      * @Description : 插入导入报名店铺
      * <AUTHOR> 卜金隆
      * @date : 2022/3/22 10:37
     */
    ExecuteDTO saveEnrollImportStore(AtomReqEnrollImportStoreDTO reqDTO);
    /**
     * 批量插入导入报名店铺
     *
     * @param reqDTO
     * @return
     */
    ExecuteDTO batchSaveEnrollImportStore(List<AtomReqEnrollImportStoreDTO> reqDTO);

    /**
     * @param reqDTO
     * @Description : 一个活动下根据店铺编码集合批量删除报名导入店铺
     * <AUTHOR> 卜金隆
     * @date :  2022/3/22 10:37
     */
    ExecuteDTO deleteEnrollImportStore(AtomReqEnrollImportStoreDTO reqDTO);
}
