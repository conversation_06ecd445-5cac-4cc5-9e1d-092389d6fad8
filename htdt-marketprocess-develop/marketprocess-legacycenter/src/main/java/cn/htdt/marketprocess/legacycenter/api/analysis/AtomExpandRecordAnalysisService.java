package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.AtomReqExpandRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomResExpandRecordDTO;

/**
 * <p>
 * 膨胀红包发起记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
public interface AtomExpandRecordAnalysisService {

    /**
     * 用于膨胀红包发起列表
     *
     * @param atomReqExpandRecordDTO
     * @return
     */
    ExecuteDTO<AtomResExpandRecordDTO> getOneExpandRecord(AtomReqExpandRecordDTO atomReqExpandRecordDTO);

    /**
     * 用于膨胀红包发起列表
     *
     * @param atomReqExpandRecordDTO
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResExpandRecordDTO>> getExpandRecordPage(AtomReqExpandRecordDTO atomReqExpandRecordDTO);

    /**
     * 查询当日发起的数量
     * @param atomReqExpandRecordDTO
     * @return
     */
    ExecuteDTO<Integer> getCurrentExpandRecordCount(AtomReqExpandRecordDTO atomReqExpandRecordDTO);

    /**
     * 查询某活动累计发起的数量
     * @param atomReqExpandRecordDTO
     * @return
     */
    ExecuteDTO<Integer> getExpandRecordTotalCount(AtomReqExpandRecordDTO atomReqExpandRecordDTO);
    /**
     * 查询某活动累计领取量、使用量
     * @param atomReqExpandRecordDTO
     * @return
     */
    ExecuteDTO<AtomResExpandRecordDTO> getExpandRecordUseCount(AtomReqExpandRecordDTO atomReqExpandRecordDTO);
}
