package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.AtomReqPointsGiftDTO;
import cn.htdt.marketcenter.dto.response.AtomResPointsGiftDTO;

/**
 * 积分商城礼品操作类
 *
 * <AUTHOR>
 * @since 2021-10-12
 */
public interface AtomPointsGiftAnalysisService {
    /**
     * 根据条件查询单条商城礼品的信息
     *
     * @param atomReqPointsGiftDTO giftNo
     * @return 礼品信息
     */
    ExecuteDTO<AtomResPointsGiftDTO> getSimpleGift(AtomReqPointsGiftDTO atomReqPointsGiftDTO);

    /**
     * 根据条件查礼品列表
     *
     * @param atomReqPointsGiftDTO 礼品名称/上下架状态/店铺编号/更细起止时间
     * @return 符合条件的礼品列表分页数据
     */
    ExecuteDTO<ExecutePageDTO<AtomResPointsGiftDTO>> getMultiGift(AtomReqPointsGiftDTO atomReqPointsGiftDTO);

    /**
     * 查询粉丝可以兑换的礼品列表
     *
     * @param atomReqPointsGiftDTO 手机号/店铺编号
     * @return 粉丝可以兑换的礼品列表
     */
    ExecuteDTO<ExecutePageDTO<AtomResPointsGiftDTO>> getCanConvert(AtomReqPointsGiftDTO atomReqPointsGiftDTO);

    /**
     * //20230928蛋品-wh-礼品-根据条件查商家礼品列表
     * 根据条件查商家礼品列表
     *
     * @param atomReqPointsGiftDTO 礼品名称/上下架状态/店铺编号/更细起止时间
     * @return 符合条件的礼品列表分页数据
     */
    ExecuteDTO<ExecutePageDTO<AtomResPointsGiftDTO>> getMerchantMultiGift(AtomReqPointsGiftDTO atomReqPointsGiftDTO);

    /**
     * //20230928蛋品-wh-礼品-根据条件查询单条商家共享商城礼品的信息
     * 根据条件查询单条商家共享商城礼品的信息
     *
     * @param atomReqPointsGiftDTO giftNo
     * @return 礼品信息
     */
    ExecuteDTO<AtomResPointsGiftDTO> getSharingSimpleGift(AtomReqPointsGiftDTO atomReqPointsGiftDTO);

    /**
     * //20230928蛋品-wh-积分商城礼品-门店共享店铺查询商城礼品信息
     * 门店共享店铺查询商城礼品信息
     *
     * @param atomReqPointsGiftDTO 礼品名称/上下架状态/店铺编号/更细起止时间
     * @return 符合条件的礼品列表分页数据
     */
    ExecuteDTO<ExecutePageDTO<AtomResPointsGiftDTO>> getSharingMultiGift(AtomReqPointsGiftDTO atomReqPointsGiftDTO);

    /**
     * //20230928蛋品-wh-门店-门店共享店铺查询门店下礼品信息
     * 门店共享店铺查询门店下礼品信息
     *
     * @param atomReqPointsGiftDTO giftNo
     * @return 礼品信息
     */
    ExecuteDTO<AtomResPointsGiftDTO> getSharingStoreSimpleGift(AtomReqPointsGiftDTO atomReqPointsGiftDTO);


    /**
     * //20230928蛋品-wh-礼品-查询商家共享店铺礼品的信息(单个)
     * 查询商家共享店铺礼品的信息(单个)
     *
     * @param atomReqPointsGiftDTO giftNo
     * @return 礼品信息
     */
    ExecuteDTO<AtomResPointsGiftDTO> selMerchantPointsGiftDetails(AtomReqPointsGiftDTO atomReqPointsGiftDTO);

}
