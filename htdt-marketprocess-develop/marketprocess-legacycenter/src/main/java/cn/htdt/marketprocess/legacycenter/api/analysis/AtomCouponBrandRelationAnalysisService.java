package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqCouponBrandRelationDTO;
import cn.htdt.marketcenter.dto.response.AtomResCouponBrandRelationDTO;

import java.util.List;

/**
 * <p>
 * 活动品牌表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-02
 */
public interface AtomCouponBrandRelationAnalysisService {

    /**
     * @param atomReqCouponBrandRelationDTO
     * @Description : 根据条件查询活动品牌关联表列表
     * <AUTHOR> wanglei
     */
    ExecuteDTO<List<AtomResCouponBrandRelationDTO>> getCouponBrandRelationList(AtomReqCouponBrandRelationDTO atomReqCouponBrandRelationDTO);

}
