package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqSmsRechargePackageDTO;
import cn.htdt.marketcenter.dto.response.AtomResSmsRechargePackageDTO;

import java.util.List;

/**
 * 短信充值套餐表 数据查询服务接口
 *
 * <AUTHOR>
 * @date 2021-09-26
 **/
public interface AtomSmsRechargePackageAnalysisService {

    /**
     * 获取短信充值套餐列表
     *
     * @param atomReqDTO 查询参数
     * @return ExecuteDTO<List<AtomResSmsRechargePackageDTO>>
     * <AUTHOR>
     */
    ExecuteDTO<List<AtomResSmsRechargePackageDTO>> getSmsRechargePackageList(AtomReqSmsRechargePackageDTO atomReqDTO);

}
