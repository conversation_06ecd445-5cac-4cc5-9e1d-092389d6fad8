package cn.htdt.marketprocess.legacycenter.api.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqGoodsPromotionPeriodDTO;

import java.util.List;

/**
 * 商品促销活动的时间段操作service
 *
 * <AUTHOR>
 * @date 2021-06-28
 */
public interface AtomGoodsPromotionPeriodOperateService {

    /**
     * 新增商品促销活动的时间段信息
     *
     * @param periodDTO
     * @return
     * <AUTHOR>
     * @date 2021-09-23
     */
    ExecuteDTO saveGoodsPromotionPeriod(AtomReqGoodsPromotionPeriodDTO periodDTO);

    /**
     * 批量新增商品促销活动的时间段信息
     *
     * @param periodDTOList
     * @return
     * <AUTHOR>
     * @date 2021-06-28
     */
    ExecuteDTO batchSaveGoodsPromotionPeriod(List<AtomReqGoodsPromotionPeriodDTO> periodDTOList);

    /**
     * 根据活动编号删除促销活动时间段数据
     *
     * @param promotionNo
     * @return
     * <AUTHOR>
     * @date 2021-06-28
     */
    ExecuteDTO deletePeriodByPromotionNo(String promotionNo);

    /**
     * 批量更新商品促销活动的时间段信息
     *
     * @param periodDTOList
     * @return
     * <AUTHOR>
     * @date 2021-07-19
     */
    ExecuteDTO batchUpdateGoodsPromotionPeriod(List<AtomReqGoodsPromotionPeriodDTO> periodDTOList);

    /**
     * 根据活动编号时间段编号，删除促销活动时间段数据
     *
     * @param periodDTO
     * @return
     * <AUTHOR>
     * @date 2021-09-23
     */
    ExecuteDTO deletePeriodByNo(AtomReqGoodsPromotionPeriodDTO periodDTO);

}
