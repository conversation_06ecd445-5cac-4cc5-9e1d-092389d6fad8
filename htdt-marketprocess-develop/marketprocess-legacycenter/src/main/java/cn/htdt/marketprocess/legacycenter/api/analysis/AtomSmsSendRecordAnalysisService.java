package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.AtomReqSmsSendRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomResSmsSendRecordDTO;

/**
 * 短信发送记录表 数据查询服务接口
 *
 * <AUTHOR>
 * @date 2021-06-25
 */
public interface AtomSmsSendRecordAnalysisService {

    /**
     * 获取短信发送记录列表，支持分页查询
     *
     * @param atomReqDTO 查询参数
     * @return ExecuteDTO<ExecutePageDTO < AtomResSmsSendRecordDTO>>
     * <AUTHOR>
     */
    ExecuteDTO<ExecutePageDTO<AtomResSmsSendRecordDTO>> getSmsSendRecordPageList(AtomReqSmsSendRecordDTO atomReqDTO);

}
