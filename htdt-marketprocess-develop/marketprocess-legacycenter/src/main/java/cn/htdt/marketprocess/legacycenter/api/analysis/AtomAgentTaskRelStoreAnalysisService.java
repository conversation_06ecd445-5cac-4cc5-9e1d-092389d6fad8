package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.AtomReqAgentTaskRelStoreDTO;
import cn.htdt.marketcenter.dto.request.AtomReqJoinStoreDTO;
import cn.htdt.marketcenter.dto.response.AtomResAgentTaskRelStoreDTO;
import cn.htdt.marketcenter.dto.response.AtomResJoinStoreDTO;

import java.util.List;

/**
 * 代理人平台任务和店铺关系查询service
 * <AUTHOR>
 * @date 2021/1/26 19:43
 */
public interface AtomAgentTaskRelStoreAnalysisService {

    /**
      * @Description : 根据商家编号查询参与店铺列表
      * <AUTHOR> 高繁
      * @date : 2021/1/26 19:48
     */
    ExecuteDTO<ExecutePageDTO<AtomResJoinStoreDTO>> getJoinStoreListByParams(AtomReqJoinStoreDTO reqJoinStoreDTO);

    /**
     * @Description 分页查询任务和营销清单列表
     * <AUTHOR>
     * @param reqAgentTaskRelStoreDTO
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResAgentTaskRelStoreDTO>> getAgentTaskRelStoreListByPage(AtomReqAgentTaskRelStoreDTO reqAgentTaskRelStoreDTO);

    /**
     * @Description 查看是否有重复的店铺
     * <AUTHOR>
     * @param reqAgentTaskRelStoreDTO
     * @return
     */
    ExecuteDTO<List<String>> checkSameAgentTaskRelStoreList(AtomReqAgentTaskRelStoreDTO reqAgentTaskRelStoreDTO);

    /**
      * @param reqAgentTaskRelStoreDTO
      * @Description : 查询该商家或店铺是否已初始化关系数据
      * <AUTHOR> 高繁
      * @date : 2021/2/22 15:26
     */
    ExecuteDTO<Integer> checkTaskStoreExists(AtomReqAgentTaskRelStoreDTO reqAgentTaskRelStoreDTO);
}
