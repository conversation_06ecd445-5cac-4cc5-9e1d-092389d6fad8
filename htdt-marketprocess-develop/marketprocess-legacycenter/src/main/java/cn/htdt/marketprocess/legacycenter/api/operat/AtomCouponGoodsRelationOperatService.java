package cn.htdt.marketprocess.legacycenter.api.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqCouponGoodsRelationDTO;

import java.util.List;

/**
 * <p>
 * 活动关联商品表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-02
 */
public interface AtomCouponGoodsRelationOperatService {


    /**
     * 批量删除活动的商品
     * @param promotionNo
     */
    ExecuteDTO deleteCouponGoodsRelation(String promotionNo);

    /**
      * @param atomReqCouponGoodsRelationDTOS
      * @Description : 批量添加活动商品关联
      * <AUTHOR> 高繁
      * @date : 2021/9/15 18:32
     */
    ExecuteDTO batchAddCouponGoodsRelation(List<AtomReqCouponGoodsRelationDTO> atomReqCouponGoodsRelationDTOS);

}
