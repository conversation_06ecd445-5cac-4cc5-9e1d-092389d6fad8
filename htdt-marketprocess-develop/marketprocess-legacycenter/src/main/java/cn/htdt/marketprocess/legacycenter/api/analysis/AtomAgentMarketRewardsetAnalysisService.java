package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqAgentMarketRewardsetDTO;
import cn.htdt.marketcenter.dto.request.AtomReqAgentMarketRewardsetRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomResAgentMarketRewardsetDTO;
import cn.htdt.marketcenter.dto.response.AtomResAgentMarketRewardsetRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomResRewardSetListDto;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-01-13
 * @Description 代理人酬劳设置查询
 **/
public interface AtomAgentMarketRewardsetAnalysisService {
    /**
     * 根据taskOrGoodsNo和marketType查询，可能是单条也可能是集合
     * taskOrGoodsNo和marketType必须有值
     * @param atomAgentMarketRewardsetDTO
     * @return
     */
    ExecuteDTO<List<AtomResAgentMarketRewardsetDTO>> getList(AtomReqAgentMarketRewardsetDTO atomAgentMarketRewardsetDTO);

    /**
     * 根据平台任务编码批量查询酬劳设置聚合数据
     * @param taskNoList 任务编码集合
     * @return
     */
    ExecuteDTO<List<AtomResRewardSetListDto>> getRewardSetByTaskNos(List<String> taskNoList);

    /**
     * 根据平台任务编码批量查询酬劳设置数据
     * @param taskNoList 任务编码集合
     * @return
     */
    ExecuteDTO<List<AtomResAgentMarketRewardsetDTO>> batchGetRewardSetByTaskNos(List<String> taskNoList);

    /**
     * 查询单个分销商品酬劳历史修改记录
     * @auth hxj
     * @param recordDTO
     * @return
     */
    ExecuteDTO<List<AtomResAgentMarketRewardsetRecordDTO>> selectRewardSetRecordList(
            AtomReqAgentMarketRewardsetRecordDTO recordDTO);

}