package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqLotteryRecordDTO;

/**
 * <p>
 * 抽奖记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-09
 */
public interface AtomLotteryRecordAnalysisService {

    /**
     * 查询某个抽奖活动下已经发生抽奖的订单数
     *
     * @param atomReqLotteryRecordDTO 活动编号
     * @return 某个抽奖活动下已经发生抽奖的订单数
     */
    ExecuteDTO<Integer> getPromotionLotteryCount(AtomReqLotteryRecordDTO atomReqLotteryRecordDTO);

    /**
     * 查询订单参与某个抽奖活动的次数
     *
     * @param atomReqLotteryRecordDTO 订单编号/活动编号
     * @return 订单参与某个抽奖活动的次数
     */
    ExecuteDTO<Integer> getOrderLotteryCountOfPromotion(AtomReqLotteryRecordDTO atomReqLotteryRecordDTO);
}
