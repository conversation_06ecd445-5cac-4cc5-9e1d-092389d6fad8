package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.AtomReqCouponCanUseRecordDTO;
import cn.htdt.marketcenter.dto.request.AtomReqCouponUserRecordDTO;
import cn.htdt.marketcenter.dto.response.*;

import java.util.List;
import java.util.Map;

/**
 * 优惠券领发记录表
 *
 * <AUTHOR>
 */
public interface AtomCouponUserRecordAnalysisService {

    /**
     * 获取领发券记录-分页
     *
     * @param atomReqCouponUserRecordDTO
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResCouponUserRecordDTO>> getCouponUserRecordPage(AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO);

    /**
     * @param atomReqCouponUserRecordDTO
     * @Description : 查询单条
     * <AUTHOR> 王磊
     */
    ExecuteDTO<AtomResCouponUserRecordDTO> getOneCouponUserRecord(AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO);

    /**
     * @param atomReqCouponCanUseRecordDTO
     * @Description : 查询用户+商品可用的优惠券(未排除品牌品类等限制)
     * <AUTHOR> 高繁
     * @date : 2021/4/27 10:43
     */
    ExecuteDTO<List<AtomResCouponCanUserRecordDTO>> getCouponCanUse(AtomReqCouponCanUseRecordDTO atomReqCouponCanUseRecordDTO);

    /**
     * 获取某优惠券的以下信息：
     * 单个粉丝总共可获得券数
     * 店铺粉丝总共可获得券
     * 店铺粉丝总共可获得券
     * 店铺粉丝总共可获得券
     */
    ExecuteDTO<AtomResCouponRecordDTO> getCouponUserRecordCounts(AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO);

    /**
     * 获取某优惠券的以下信息：
     * 店铺粉丝总共可获得券
     * 店铺粉丝单日总共可获得券
     * 不包含粉丝信息
     *
     * <AUTHOR> 卜金隆
     * @date : 2021/6/30 17:13
     */
    ExecuteDTO<AtomResCouponRecordDTO> getCouponStoreRecordCounts(AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO);

    /**
     * 获取发行总数
     */
    ExecuteDTO<Integer> getCouponTotalCount(AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO);

    /**
     * @param atomReqCouponUserRecordDTO
     * @Description : 查询优惠券配置的商品信息
     * <AUTHOR> 高繁
     * @date : 2021/4/28 17:13
     */
    ExecuteDTO<List<String>> getCouponGoodsList(AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO);

    /**
     * @param atomReqCouponUserRecordDTO
     * @Description : 查询优惠券配置的分类信息
     * <AUTHOR> 高繁
     * @date : 2021/4/28 17:13
     */
    ExecuteDTO<List<String>> getCouponCategoryList(AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO);

    /**
     * @param atomReqCouponUserRecordDTO
     * @Description : 查询优惠券配置的店铺分类信息
     * <AUTHOR> 高宁
     */
    ExecuteDTO<List<String>> getCouponStoreCategoryList(AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO);

    /**
     * @param atomReqCouponUserRecordDTO
     * @Description : 查询优惠券配置的品牌信息
     * <AUTHOR> 高繁
     * @date : 2021/4/28 17:13
     */
    ExecuteDTO<List<String>> getCouponBrandList(AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO);

    /**
     * @param atomReqCouponUserRecordDTO
     * @Description : 批量查询优惠券配置的商品信息 -列表用
     * <AUTHOR> 卜金隆
     * @date : 2021/4/28 17:13
     */
    ExecuteDTO<Map<String, List<String>>> getBatchCouponGoodsList(AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO);

    /**
     * @param atomReqCouponUserRecordDTO
     * @Description : 批量查询优惠券配置的分类信息 -列表用
     * <AUTHOR> 卜金隆
     * @date : 2021/4/28 17:13
     */
    ExecuteDTO<Map<String, List<String>>> getBatchCouponCategoryList(AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO);

    /**
     * @param atomReqCouponUserRecordDTO
     * @Description : 批量查询优惠券配置的品牌信息 -列表用
     * <AUTHOR> 卜金隆
     * @date : 2021/6/6 17:13
     */
    ExecuteDTO<Map<String, List<String>>> getBatchCouponBrandList(AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO);

    /**
     * @param atomReqCouponUserRecordDTO
     * @Description : 查询用户券编号对应的券基本信息
     * <AUTHOR> 高繁
     * @date : 2021/5/11 14:29
     */
    ExecuteDTO<AtomResCouponOrderRecordDTO> getCouponInfoByUser(AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO);

    /**
     * @param atomReqCouponUserRecordDTO
     * @Description : 查询用户券编号对应的券基本信息
     * <AUTHOR> 卜金隆
     * @date : 2021/6/1 21:29
     */
    ExecuteDTO<AtomResCouponUserRecordDTO> getCouponDetailInfoByUser(AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO);

    /**
     * @param atomReqCouponUserRecordDTO
     * @Description : 我的优惠券列表
     * <AUTHOR> 卜金隆
     * @date : 2021/5/11 17:01
     */
    ExecuteDTO<List<AtomResCouponUserRecordDTO>> getMyCouponList(AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO);

    /**
     * 粉丝优惠券带列表分页
     * <AUTHOR> hxj
     * @date : 2021/8/12 15:32
     * @param atomReqCouponUserRecordDTO
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResCouponUserRecordDTO>> getMyCouponListWithPage(AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO);

    /**
     * @param atomReqCouponUserRecordDTO
     * @Description : 我的优惠券列表数量
     * <AUTHOR> 卜金隆
     * @date : 2021/5/19 19:01
     */
    ExecuteDTO<Integer> getMyCouponNum(AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO);

    /**
     * @param atomReqCouponUserRecordDTO
     * @Description : 我的优惠券每个店铺的张数
     * <AUTHOR> 卜金隆
     * @date : 2021/10/15 19:01
     */
    ExecuteDTO<ExecutePageDTO<AtomResCouponStoreNumDTO>> getMyCouponStoreNo(AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO);

  /**
    * @param atomReqCouponUserRecordDTO
    * @Description : 粉丝可用优惠券数量
    * <AUTHOR> 高繁
    * @date : 2021/11/5 17:33
   */
    ExecuteDTO<Integer> getFansCanUseCouponNum(AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO);

    /**
     * 粉丝在该商家未使用的优惠券的数量
     *
     * @param atomReqCouponUserRecordDTO 商家编码
     * @return 数量
     */
    ExecuteDTO<Integer> getUnusedCouponCount(AtomReqCouponUserRecordDTO atomReqCouponUserRecordDTO);

}

