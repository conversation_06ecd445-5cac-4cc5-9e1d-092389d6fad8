package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqSmsTemplateDTO;
import cn.htdt.marketcenter.dto.response.AtomResSmsTemplateDTO;

import java.util.List;

/**
 * 短信模板表 数据查询服务接口
 *
 * <AUTHOR>
 * @date 2021-06-25
 */
public interface AtomSmsTemplateAnalysisService {

    /**
     * 获取短信模板列表
     *
     * @param atomReqDTO 查询参数
     * @return ExecuteDTO<List < AtomResSmsTemplateDTO>>
     * <AUTHOR>
     */
    ExecuteDTO<List<AtomResSmsTemplateDTO>> getSmsTemplateList(AtomReqSmsTemplateDTO atomReqDTO);


    ExecuteDTO<List<AtomResSmsTemplateDTO>> getInitSmsTemplateList(AtomReqSmsTemplateDTO atomReqDTO);

}
