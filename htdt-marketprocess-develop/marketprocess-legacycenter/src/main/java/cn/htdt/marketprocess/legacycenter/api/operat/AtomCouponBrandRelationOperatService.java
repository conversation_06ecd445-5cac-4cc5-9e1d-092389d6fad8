package cn.htdt.marketprocess.legacycenter.api.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqCouponBrandRelationDTO;

import java.util.List;

/**
 * <p>
 * 活动品牌关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-02
 */
public interface AtomCouponBrandRelationOperatService {


    /**
     * 批量删除活动的品牌
     * @param promotionNo
     * <AUTHOR> 王磊
     */
    ExecuteDTO deleteCouponBrandRelation(String promotionNo);

    /**
     * @param atomReqCouponBrandRelationDTOS
     * @Description : 批量添加活动品牌关联
     * <AUTHOR> 王磊
     */
    ExecuteDTO batchAddCouponBrandRelation(List<AtomReqCouponBrandRelationDTO> atomReqCouponBrandRelationDTOS);

}
