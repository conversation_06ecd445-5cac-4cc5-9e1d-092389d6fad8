package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.AtomReqGoodsPromotionGoodsRelationDTO;
import cn.htdt.marketcenter.dto.response.AtomResCloudPoolGoodsCommissionConfigDTO;
import cn.htdt.marketcenter.dto.response.AtomResGoodsPromotionGoodsRelationDTO;

import java.util.List;

/**
 * 详细说明.商品促销商品查询接口
 * <p>
 * Copyright: Copyright (c) 2021/6/22 18:47
 * <p>
 * Company: htd
 * <p>
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public interface AtomGoodsPromotionGoodsRelationAnalysisService {


    /**
     * @param atomReqGoodsPromotionGoodsRelationDTO
     * @Description : 查询促销活动设置商品列表
     * <AUTHOR> 张宇
     * @date : 2021/6/28 19:59
     */
    ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> getGoodsRelationList(AtomReqGoodsPromotionGoodsRelationDTO atomReqGoodsPromotionGoodsRelationDTO);

    /**
     * @param atomReqGoodsPromotionGoodsRelationDTO
     * @Description : 查询秒杀商品以及对应的活动规则配置信息
     * <AUTHOR> 杨子建
     * @date : 2021-08-02
     */
    ExecuteDTO<AtomResGoodsPromotionGoodsRelationDTO> getGoodsAndRule(AtomReqGoodsPromotionGoodsRelationDTO atomReqGoodsPromotionGoodsRelationDTO);

    /**
     * 查询添加促销活动商品时，不可选择的商品编码集合
     *
     * @param reqDTO
     * @return
     * <AUTHOR>
     * @date 2021-07-07
     */
    ExecuteDTO<List<String>> getNoChoiceGoodsNoList(AtomReqGoodsPromotionGoodsRelationDTO reqDTO);

    /**
     * 查询活动场次下的商品哪些是互斥的商品
     *
     * @param reqDTO 查询参数
     * <AUTHOR>
     * @date 2021-11-29
     */
    ExecuteDTO<List<String>> getPromotionUnAvailableGoods(AtomReqGoodsPromotionGoodsRelationDTO reqDTO);

    /**
     * 促销活动，各场次批量保存场次下的商品时，排除非当前活动互斥的商品
     *
     * @param reqDTO 请求参数
     * @return
     * <AUTHOR>
     * @date 2021-11-25
     */
    ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> getNoChoiceGoodsNosForBatchPeriod(AtomReqGoodsPromotionGoodsRelationDTO reqDTO);

    /**
     * 查询促销活动的商品活动价以及对应的活动信息
     *
     * @param reqDTO
     * @return
     * <AUTHOR>
     * @date 2021-07-13
     */
    ExecuteDTO<AtomResGoodsPromotionGoodsRelationDTO> getPromotionAndGoodsInfo(AtomReqGoodsPromotionGoodsRelationDTO reqDTO);

    /**
     * @param atomReqGoodsPromotionGoodsRelationDTO
     * @Description : 根据活动编码，商品编码查询商品佣金配置
     * <AUTHOR> 高繁
     * @date : 2021/7/28 15:47
     */
    ExecuteDTO<AtomResCloudPoolGoodsCommissionConfigDTO> getPromotionGoodsCommissionConfig(AtomReqGoodsPromotionGoodsRelationDTO atomReqGoodsPromotionGoodsRelationDTO);

    /**
     * 获取活动下关联的商品数
     *
     * @param relationDTO 查询参数
     * @return 活动下关联的商品数
     * <AUTHOR>
     * @date 2021-10-12
     */
    ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> getPromotionGoodsNum(AtomReqGoodsPromotionGoodsRelationDTO relationDTO);

    /**
     * 分页查询促销活动商品
     *
     * <AUTHOR>
     * @date 2021-11-04
     */
    ExecuteDTO<ExecutePageDTO<AtomResGoodsPromotionGoodsRelationDTO>> getGoodsRelationPage(AtomReqGoodsPromotionGoodsRelationDTO reqDTO);

    /**
     * 查询商品参与的活动基本信息（普通商详页）
     *
     * @param relationDTO 查询参数
     * <AUTHOR>
     * @date 2021-11-23
     */
    ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> getGoodsPromotionInfo(AtomReqGoodsPromotionGoodsRelationDTO relationDTO);

    /**
     * 查询指定活动场次下设置的商品数据
     *
     * @param relationDTO 查询参数
     * <AUTHOR>
     * @date 2021-12-08
     */
    ExecuteDTO<List<AtomResGoodsPromotionGoodsRelationDTO>> getPromotionPeriodGoodsList(AtomReqGoodsPromotionGoodsRelationDTO relationDTO);
}
