package cn.htdt.marketprocess.legacycenter.api.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomPromotionApplicationProcessingDTO;

/**
 * <p>
 * 推广申请处理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-12
 */
public interface AtomPromotionApplicationProcessingOperatService {

    /**
     * 新增推广申请
     * @param applicationProcessingDTO
     * @return
     */
    ExecuteDTO addPromotionApplication(AtomPromotionApplicationProcessingDTO applicationProcessingDTO);

    /**
     * 推广审核
     * @param applicationProcessingDTO
     * @return
     */
    ExecuteDTO modifyPromotionApplication(AtomPromotionApplicationProcessingDTO applicationProcessingDTO);

}
