package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.AtomReqVirtualCoinOrderDTO;
import cn.htdt.marketcenter.dto.request.AtomReqVirtualCoinOrderFlowDTO;
import cn.htdt.marketcenter.dto.response.AtomResVirtualCoinOrderDTO;
import cn.htdt.marketcenter.dto.response.AtomResVirtualCoinOrderFlowDTO;

import java.math.BigDecimal;

/**
 * 橙豆充值订单
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
public interface AtomVirtualCoinOrderAnalysisService {

    ExecuteDTO<AtomResVirtualCoinOrderFlowDTO> selectOrderFlow(AtomReqVirtualCoinOrderFlowDTO flowDto);

    ExecuteDTO<AtomResVirtualCoinOrderFlowDTO> selectOrderFlowByOutTradeNo(String outTradeNo);

    ExecuteDTO<AtomResVirtualCoinOrderDTO> selectOrderByOrderNo(String virtualOrderNo);

    ExecuteDTO<ExecutePageDTO<AtomResVirtualCoinOrderDTO>> selectOrderByStoreNoByPage(AtomReqVirtualCoinOrderDTO queryOrderDto);

    ExecuteDTO<BigDecimal> getTotalPayment(AtomReqVirtualCoinOrderDTO queryOrderDto);
}
