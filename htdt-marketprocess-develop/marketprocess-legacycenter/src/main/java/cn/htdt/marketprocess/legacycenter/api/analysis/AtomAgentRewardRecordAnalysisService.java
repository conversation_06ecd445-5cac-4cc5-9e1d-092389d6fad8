package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.AtomReqAgentRewardRecordDTO;
import cn.htdt.marketcenter.dto.request.AtomReqJoinTaskNumDTO;
import cn.htdt.marketcenter.dto.response.AtomResAgentRewardRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomResJoinTaskNumDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-01-14
 * @Description 代理人酬劳过程信息原子查询服务
 */
public interface AtomAgentRewardRecordAnalysisService {

    /**
     * @Description 分页查询代理人酬劳过程信息列表
     * <AUTHOR>
     * @param reqAgentRewardRecordDTO
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResAgentRewardRecordDTO>> getAgentRewardRecordListByPage(AtomReqAgentRewardRecordDTO reqAgentRewardRecordDTO);

    /**
     * 每推荐个数
     * @param reqAgentRewardRecordDTO
     * @return
     */
    ExecuteDTO<List<String>> countByEach(AtomReqAgentRewardRecordDTO reqAgentRewardRecordDTO);

    /**
     * 累计推荐个数 | 用于判断是不是新粉丝
     * @param reqAgentRewardRecordDTO
     * @return
     */
    ExecuteDTO<List<String>> countByGrandTotal(AtomReqAgentRewardRecordDTO reqAgentRewardRecordDTO);

    /**
     * @param reqJoinTaskNumDTO
     * @Description : 根据店铺编码+代理人编码/任务编码 查询拉新任务总数
     * <AUTHOR> 高繁
     * @date : 2021/2/20 9:59
     */
    ExecuteDTO<List<AtomResJoinTaskNumDTO>> getStorePullNewTaskFansNum(AtomReqJoinTaskNumDTO reqJoinTaskNumDTO);

    /**
     * @description 查询代理人任务拉新粉丝数
     * <AUTHOR>
     * @date 2021-03-11 19:38:43
     * @param reqAgentRewardRecordDTO
     * @return
     */
    ExecuteDTO<Integer> getFanTotalStatistics(AtomReqAgentRewardRecordDTO reqAgentRewardRecordDTO);

    /**
      * @param orderNo 订单编号
      * @Description : 根据订单编号查询酬劳过程记录
      * <AUTHOR> 高繁
      * @date : 2021/6/23 19:12
     */
    ExecuteDTO<List<AtomResAgentRewardRecordDTO>> getAgentRewardRecordByOrder(String orderNo);
}
