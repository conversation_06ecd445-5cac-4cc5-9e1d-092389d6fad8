package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqLotteryRewardInfoDTO;
import cn.htdt.marketcenter.dto.request.AtomReqPromotionInfoDTO;
import cn.htdt.marketcenter.dto.response.AtomResPromotionInfoDTO;

/**
 * <p>
 * 活动奖品信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-02
 */
public interface AtomPromotionRewardInfoAnalysisService {

    /**
     * 查询活动信息
     * 关联奖品信息
     *
     * @param atomReqPromotionInfoDTO
     * @return
     */
    ExecuteDTO<AtomResPromotionInfoDTO> getPromotionRewardInfo(AtomReqPromotionInfoDTO atomReqPromotionInfoDTO);

    /**
     * 根据活动编码判断奖品是否设置
     * 单个获取
     *
     * @param reqLotteryRewardInfoDTO
     * @return
     */
    ExecuteDTO<Integer> countLotteryGoodsNum(AtomReqLotteryRewardInfoDTO reqLotteryRewardInfoDTO);

    /**
     * 根据活动编码查询剩余奖品库存总数
     * @param reqLotteryRewardInfoDTO
     * @return
     */
    ExecuteDTO<Integer> countResidueLotteryNum(AtomReqLotteryRewardInfoDTO reqLotteryRewardInfoDTO);
}
