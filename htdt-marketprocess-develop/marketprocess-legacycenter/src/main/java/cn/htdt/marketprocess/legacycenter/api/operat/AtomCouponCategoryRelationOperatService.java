package cn.htdt.marketprocess.legacycenter.api.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqCouponCategoryRelationDTO;

import java.util.List;

/**
 * <p>
 * 活动类目关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-02
 */
public interface AtomCouponCategoryRelationOperatService {


    /**
     * 批量删除活动的类目
     * @param promotionNo
     * <AUTHOR> 王磊
     */
    ExecuteDTO deleteCouponCategoryRelation(String promotionNo);

    /**
     * @param atomReqCouponCategoryRelationDTOS
     * @Description : 批量添加活动类目关联
     * <AUTHOR> 王磊
     */
    ExecuteDTO batchAddCouponCategoryRelation(List<AtomReqCouponCategoryRelationDTO> atomReqCouponCategoryRelationDTOS);

}
