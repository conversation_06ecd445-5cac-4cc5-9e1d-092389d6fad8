package cn.htdt.marketprocess.legacycenter.api.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqAgentMarketRewardsetDTO;
import cn.htdt.marketcenter.dto.response.AtomResAgentMarketRewardsetDTO;
import cn.htdt.marketcenter.dto.response.GoodsNoByCommissionDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-01-13
 * @Description 代理人酬劳设置操作
 **/
public interface AtomAgentMarketRewardsetOperatService {
    /**
     * 保存并修改
     * @param reqAgentMarketRewardsetDTO
     * @return
     */
    ExecuteDTO save(AtomReqAgentMarketRewardsetDTO reqAgentMarketRewardsetDTO);

    /**
     * 批量情况下肯定是新增
     * @param listReqAgentMarketRewardsetDTO
     * @return
     */
    ExecuteDTO saveList(List<AtomReqAgentMarketRewardsetDTO> listReqAgentMarketRewardsetDTO);

    /**
     * <AUTHOR>
     * @Description 通过商品编号查询新上架的店铺分销 获取最近3条
     * @Date 2021/7/1
     * @Param [atomReqAgentMarketRewardsetDTO]
     * @return cn.htdt.common.dto.response.ExecuteDTO<cn.htdt.marketcenter.dto.response.GoodsNoByCommissionDTO>
     **/
    ExecuteDTO<AtomResAgentMarketRewardsetDTO> selectGoodsNoByGoodsNo(AtomReqAgentMarketRewardsetDTO atomReqAgentMarketRewardsetDTO);

    /**
     * <AUTHOR>
     * @Description 通过佣金比率高低查询新上架的店铺分销 获取最近3条
     * @Date 2021/7/1
     * @Param [atomReqAgentMarketRewardsetDTO]
     * @return cn.htdt.common.dto.response.ExecuteDTO<cn.htdt.marketcenter.dto.response.GoodsNoByCommissionDTO>
     **/
    ExecuteDTO<List<GoodsNoByCommissionDTO>> selectGoodsNoByCommission(AtomReqAgentMarketRewardsetDTO atomReqAgentMarketRewardsetDTO);
}