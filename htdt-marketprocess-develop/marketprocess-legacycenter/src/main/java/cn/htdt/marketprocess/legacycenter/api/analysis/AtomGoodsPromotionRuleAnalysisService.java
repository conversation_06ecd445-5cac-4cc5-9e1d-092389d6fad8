package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.AtomReqPromotionInfoDTO;
import cn.htdt.marketcenter.dto.response.AtomResGoodsPromotionRuleDTO;

import java.util.List;

/**
 * 详细说明.商品促销规则查询接口
 * <p>
 * Copyright: Copyright (c) 2021/6/22 18:47
 * <p>
 * Company: htd
 * <p>
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public interface AtomGoodsPromotionRuleAnalysisService {

    /**
     * @param promotionInfoDTO
     * @Description : 商品促销活动列表查询
     * <AUTHOR> 张宇
     * @date : 2021/6/26 16:59
     */
    ExecuteDTO<ExecutePageDTO<AtomResGoodsPromotionRuleDTO>> getGoodsPromotionRuleList(AtomReqPromotionInfoDTO promotionInfoDTO);

    /**
     * @param promotionInfoDTO
     * @Description : 商品促销活动列表查询-根据活动编号list批量获取
     * <AUTHOR> 张宇
     * @date : 2021/6/26 16:59
     */
    ExecuteDTO<List<AtomResGoodsPromotionRuleDTO>> batchGetGoodsPromotionRuleList(AtomReqPromotionInfoDTO promotionInfoDTO);

    /**
     * @param promotionInfoDTO
     * @Description : 查询商品促销活动详情
     * <AUTHOR> 张宇
     * @date : 2021/6/26 16:59
     */
    ExecuteDTO<AtomResGoodsPromotionRuleDTO> getGoodsPromotionRuleByPromotionNo(AtomReqPromotionInfoDTO promotionInfoDTO);

    /**
     * @param promotionInfoDTO
     * @Description : 根据店铺编号查询有效促销活动列表-汇享购
     * <AUTHOR> 张宇
     * @date : 2021/6/28 16:59
     */
    ExecuteDTO<List<AtomResGoodsPromotionRuleDTO>> getGoodsPromotionRuleListForHxg(AtomReqPromotionInfoDTO promotionInfoDTO);

    /**
     * 商家查询平台的促销活动
     *
     * @param promotionInfoDTO
     * @return
     * <AUTHOR>
     * @date 2021-07-01
     */
    ExecuteDTO<ExecutePageDTO<AtomResGoodsPromotionRuleDTO>> merchantGetPlatformPromotion(AtomReqPromotionInfoDTO promotionInfoDTO);

    /**
     * 店铺查询平台的促销活动
     *
     * @param promotionInfoDTO
     * @return
     * <AUTHOR>
     * @date 2021-07-01
     */
    ExecuteDTO<ExecutePageDTO<AtomResGoodsPromotionRuleDTO>> storeGetPlatformPromotion(AtomReqPromotionInfoDTO promotionInfoDTO);

    /**
     * 根据店铺编号查询有效限时购活动列表-汇享购
     *
     * @param promotionInfoDTO 查询参数
     * @return 限时购活动列表
     * <AUTHOR>
     * @date 2021-09-26
     */
    ExecuteDTO<List<AtomResGoodsPromotionRuleDTO>> getLimitTimePromotionForHxg(AtomReqPromotionInfoDTO promotionInfoDTO);

    /**
     * 汇享购首页查询限时购活动信息
     *
     * @param promotionInfoDTO 查询参数
     * @return 限时购活动列表
     * <AUTHOR>
     * @date 2021-09-28
     */
    ExecuteDTO<List<AtomResGoodsPromotionRuleDTO>> getLimitTimeForHxgIndex(AtomReqPromotionInfoDTO promotionInfoDTO);

    /**
     * 千橙掌柜收银-商品列表-查询进行中的满减满折活动
     *
     * @param reqDTO 查询参数
     * @return 进行中的满减满折活动列表
     * <AUTHOR>
     * @date 2022-02-17
     */
    ExecuteDTO<List<AtomResGoodsPromotionRuleDTO>> getFullDiscountForSmartCashier(AtomReqPromotionInfoDTO reqDTO);

    /**
     * 查询满减满折活动列表
     *
     * @param reqDTO 查询参数
     * @return 进行中的满减满折活动列表
     * <AUTHOR>
     * @date 2022-02-21
     */
    ExecuteDTO<List<AtomResGoodsPromotionRuleDTO>> getFullDiscountList(AtomReqPromotionInfoDTO reqDTO);

    /**
     * 查询满减满折活动基本信息
     *
     * <AUTHOR>
     * @date 2022-02-21
     */
    ExecuteDTO<List<AtomResGoodsPromotionRuleDTO>> getFullDiscountByPromotionNo(AtomReqPromotionInfoDTO reqDTO);

    /**
     * 店铺根据条件查询满减满折活动数组 时间段范围内的活动
     *
     * <AUTHOR>
     * @date 2022-02-22
     */
    ExecuteDTO<List<AtomResGoodsPromotionRuleDTO>> getFullDiscountByParam(AtomReqPromotionInfoDTO reqDTO);
    
    /**
     * @param promotionInfoDTO
     * @Description : pc端社群接龙活动列表查询
     * <AUTHOR> 吴鑫鑫
     * @date : 2023/6/7
     */
    ExecuteDTO<ExecutePageDTO<AtomResGoodsPromotionRuleDTO>> getPcGoodsPromotionRuleList(AtomReqPromotionInfoDTO promotionInfoDTO);


}
