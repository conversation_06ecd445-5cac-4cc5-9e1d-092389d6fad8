package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.AtomReqVirtualCoinRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomResVirtualCoinRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomVirtualCoinRecord4WorkChangeDto;
import cn.htdt.marketcenter.dto.response.AtomVirtualCoinRecordStatisticsDTO;

/**
 * 橙豆收入消费记录
 * <AUTHOR>
 * @date 2022/1/19
 */
public interface AtomVirtualCoinRecordAnalysisService {
    /**
     * 查询橙豆收入消费列表
     * @param recordDTO
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResVirtualCoinRecordDTO>> queryVirtualCoinRecordList(AtomReqVirtualCoinRecordDTO recordDTO);

    /**
     * 查询橙豆统计信息
     * @param recordDTO
     * @return
     */
    ExecuteDTO<AtomVirtualCoinRecordStatisticsDTO> queryVirtualCoinRecordStatistics(AtomReqVirtualCoinRecordDTO recordDTO);

    /**
     * 插叙橙豆消费记录不分页
     * @param recordDTO
     * @return
     */
    ExecuteDTO<AtomResVirtualCoinRecordDTO> queryVirtualCoinRecordByOrderNo(AtomReqVirtualCoinRecordDTO recordDTO);

    ExecuteDTO<AtomVirtualCoinRecord4WorkChangeDto> queryVirtualCoinRecord4WorkChange(AtomReqVirtualCoinRecordDTO recordDTO);

}
