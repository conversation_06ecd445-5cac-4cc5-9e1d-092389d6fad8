package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.*;
import cn.htdt.marketcenter.dto.response.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2021-01-14
 * @Description 代理人酬劳信息原子查询服务
 */
public interface AtomAgentRewardAnalysisService {

    /**
     * 代理人任务酬劳上限判断
     * @param reqAgentRewardDTO
     * @return
     */
    ExecuteDTO<List<AtomResAgentRewardSumDTO>> getSumByCeiling(AtomReqAgentRewardDTO reqAgentRewardDTO);

    /**
     * @Description 分页查询代理人酬劳信息列表
     * <AUTHOR>
     * @param reqAgentRewardDTO
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResAgentRewardDTO>> getAgentRewardListByPage(AtomReqAgentRewardDTO reqAgentRewardDTO);

    /**
     * @Description 查询店铺任务最新推广信息
     * <AUTHOR>
     * @param reqAgentRewardDTO
     * @return
     */
    ExecuteDTO<AtomResAgentRewardDTO> getNewAgentReward(AtomReqAgentRewardDTO reqAgentRewardDTO);

    /**
     * @Description 酬劳明细汇总
     * <AUTHOR>
     * @param reqAgentRewardDTO
     * @return
     */
    ExecuteDTO<AtomResAgentRewardDTO> getAgentRewardStatistics(AtomReqAgentRewardDTO reqAgentRewardDTO);

    /**
     * @Description 酬劳明细月度汇总
     * <AUTHOR>
     * @param reqAgentRewardDTO
     * @return
     */
    ExecuteDTO<List<AtomResAgentRewardDTO>> getAgentRewardMouthStatistics(AtomReqAgentRewardDTO reqAgentRewardDTO);

    /**
     * @Description 按天数统计商品和任务
     * <AUTHOR>
     * @param reqAgentRewardDTO
     * @return
     */
    ExecuteDTO<AtomResAgentRewardDTO> getGoodsOrTaskDayStatistics(AtomReqAgentRewardDTO reqAgentRewardDTO);

    /**
     * @description 统计分销订单数量
     * <AUTHOR>
     * @date 2021-07-05 16:49:33
     * @param reqAgentRewardDTO
     * @return
     */
    ExecuteDTO<AtomResAgentRewardDTO> getDistributeOrderCount(AtomReqAgentRewardDTO reqAgentRewardDTO);

    /**
     * 查询店铺下任务 代理人酬劳汇总
     * @param reqStoreAgentRewardDTO
     * @return
     */
    ExecuteDTO<List<AtomResAgentTaskRewardDTO>> getAgentRewardListByTaskNo(AtomReqStoreAgentRewardDTO reqStoreAgentRewardDTO);

    /**
     * @Description 代理人分销收益
     * <AUTHOR>
     * @param reqAgentRewardDTO
     * @return
     */
    ExecuteDTO<AtomResAgentRewardDTO>  getAgentOrderStatistics(AtomReqAgentRewardDTO reqAgentRewardDTO);

    /**
     * @Description 代理人任务收益
     * <AUTHOR>
     * @param reqAgentRewardDTO
     * @return
     */
    ExecuteDTO<AtomResAgentRewardDTO>  getAgentTaskStatistics(AtomReqAgentRewardDTO reqAgentRewardDTO);

    /**
     * @Description 代理人佣金资产
     * <AUTHOR>
     * @param reqAgentRewardDTO
     * @return
     */
    ExecuteDTO<AtomResAgentRewardDTO> getAgentYjStatistics(AtomReqAgentRewardDTO reqAgentRewardDTO);

    /**
     * @Description 代理人其他资产
     * <AUTHOR>
     * @param reqAgentRewardDTO
     * @return
     */
    ExecuteDTO<AtomResAgentRewardDTO> getAgentOtherStatistics(AtomReqAgentRewardDTO reqAgentRewardDTO);

    /**
     * @Description 代理人累计成果
     * <AUTHOR>
     * @param reqAgentRewardDTO
     * @return
     */
    ExecuteDTO<AtomResAgentRewardDTO>  getAgentTotalStatistics(AtomReqAgentRewardDTO reqAgentRewardDTO);

    /**
     * 查询代理人总酬劳接口（不分页）
     * @auth hxj
     * @param reqAgentRewardDTO
     * @return
     */
    ExecuteDTO<List<AtomResAgentRewardDTO>> selectAgentRewardList(AtomReqAgentRewardDTO reqAgentRewardDTO);

    /**
     * 查询代理人分销酬劳(分页)
     * @auth hxj
     * @param reqAgentRecordDTO
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResAgentRewardDTO>> selectAgentRewardDetails(AtomReqAgentRewardDTO reqAgentRecordDTO);

    /**
     * 汇赚钱-首页-代理人获得酬劳滚动信息
     * @auth hxj
     * @param reqAgentRecordDTO
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResAgentRewardDTO>> selectAgentRewardCircularDisplay(AtomReqAgentRewardDTO reqAgentRecordDTO);

    /**
      * @Description : 代理人酬劳管理列表查询
      * <AUTHOR> 高繁
      * @date : 2021/1/27 16:00
     */
    ExecuteDTO<ExecutePageDTO<AtomResAgentRewardManageDTO>> getAgentRewardManageByParams(AtomReqAgentRewardManageDTO reqAgentRewardManageDTO);

    /**
     * 查询代理人酬劳核销(分页)
     * @auth 邢会强
     * @param reqAgentRecordDTO
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResAgentRewardDTO>> getWriteOffGoodsList(AtomReqAgentRewardDTO reqAgentRecordDTO);


    /**
     * @description 查询代理人酬劳核销统计信息
     * <AUTHOR>
     * @date 2021-09-27 18:23:59
     * @param reqAgentRecordDTO
     * @return
     */
    ExecuteDTO<Integer> getWriteOffGoodsCount(AtomReqAgentRewardDTO reqAgentRecordDTO);

    /**
     * 查询核销码
     *
     * @param reqAgentRecordDTO
     * @return
     * <AUTHOR>
     */
    ExecuteDTO<AtomResAgentRewardDTO> getWriteOffCode(AtomReqAgentRewardDTO reqAgentRecordDTO);

    /**
      * @param reqAgentRewardGoodsNumDTO
      * @Description : 查询代理人所在店铺分销商品数
      * <AUTHOR> 高繁
      * @date : 2021/2/1 17:40
     */
    ExecuteDTO<List<AtomResAgentRewardGoodsNumDTO>> getAgentRewardGoodsNum(AtomReqAgentRewardGoodsNumDTO reqAgentRewardGoodsNumDTO);

    /**
      * @param reqStoreAgentRewardSumDTO
      * @Description : 查询代理人在此店铺下的酬劳汇总
      * <AUTHOR> 高繁
      * @date : 2021/2/3 10:05
     */
    ExecuteDTO<List<AtomResStoreAgentRewardSumDTO>> getStoreAgentRewardSum(AtomReqStoreAgentRewardSumDTO reqStoreAgentRewardSumDTO);

    /**
     * 根据订单编号或者商品编号获取酬劳信息，主要用于云池商品或者分销商品
     * @param reqAgentRewardDTO
     * @return
     */
    ExecuteDTO<List<AtomResAgentRewardDTO>> getByOrderNo(AtomReqAgentRewardDTO reqAgentRewardDTO);

    /**
     * @param reqStoreAgentRewardDetailDTO
     * @Description : 查询代理人在此店铺下的酬劳明细列表
     * <AUTHOR> 高繁
     * @date : 2021/2/17 15:57
     */
    ExecuteDTO<ExecutePageDTO<AtomResStoreAgentRewardDetailListDTO>> getAgentRewardDetailList(AtomReqStoreAgentRewardDetailDTO reqStoreAgentRewardDetailDTO);

    /**
     * @param reqAgentRewardDTO
     * @Description : 查询任务或代理人酬劳总数
     * <AUTHOR> 桑伟杰
     * @date : 2021-02-22
     */
    ExecuteDTO<AtomResAgentRewardDTO> getTotalAgentReward(AtomReqAgentRewardDTO reqAgentRewardDTO);

    /**
      * @param reqAgentRewardGoodsNumDTO
      * @Description : 查询代理人在店铺下的佣金集合
      * <AUTHOR> 高繁
      * @date : 2021/2/26 11:14
     */
    ExecuteDTO<List<AtomResAgentRewardCommissionDto>> getAgentRewardCommissionList(AtomReqAgentRewardGoodsNumDTO reqAgentRewardGoodsNumDTO);


    /**
      * @param reqAgentRewardGoodsNumDTO
      * @Description : 查询代理人在店铺下的礼品，券集合
      * <AUTHOR> 高繁
      * @date : 2021/2/26 15:14
     */
    ExecuteDTO<List<AtomResAgentRewardGiftDto>> getAgentRewardGiftList(AtomReqAgentRewardGoodsNumDTO reqAgentRewardGoodsNumDTO);

    /**
      * @param reqStoreAgentRewardSumDTO
      * @Description : 根据代理人店铺查询酬劳汇总集合
      * <AUTHOR> 高繁
      * @date : 2021/3/1 19:14
     */
    ExecuteDTO<List<AtomResStoreAgentRewardSumDTO>> getStoreAgentRewardValueSum(AtomReqStoreAgentRewardSumDTO reqStoreAgentRewardSumDTO);

    /**
     * 暂不适用，但请勿删
     * 代理人维度
     * platform -> 店铺分销订单（供货店云池订单）-> 订单代理人总佣金（按订单维度、按店铺维度、按商家维度）传 storeNo | merchantNo
     * platform -> 赚佣金 ->（分销店总部分销订单） -> 订单代理人总佣金（按订单维度、按店铺维度、按商家维度）传 distributionStoreNo | distributionMerchantNo
     * 获取酬劳状态一般为：1:预估 2:冻结 3:解冻
     * @param reqAgentRewardDTO
     * <AUTHOR> 徐辉
     * @date : 2021/3/4 15:11
     * 铁血四兄弟
     */
    //ExecuteDTO<BigDecimal> getAgentRewardValueSum(AtomReqAgentRewardDTO reqAgentRewardDTO);

    /**
     * 暂不适用，但请勿删
     * 供货店维度
     * platform -> 供货店云池订单 -> 待返金额（供货价-供货店佣金 按订单维度）
     * 获取酬劳状态一般为：1:预估 2:冻结 3:解冻
     * @param reqAgentRewardDTO
     * <AUTHOR> 徐辉
     * @date : 2021/3/7 16:10
     * 铁血四兄弟
     */
    //ExecuteDTO<BigDecimal> getStoreToBeReturnRewardValueSum(AtomReqAgentRewardDTO reqAgentRewardDTO);

    /**
     * 暂不适用，但请勿删
     * 供货店维度
     * platform -> 供货店云池订单 -> 总佣金（四方分账之和 按订单维度）
     * 获取酬劳状态一般为：1:预估 2:冻结 3:解冻
     * @param reqAgentRewardDTO
     * <AUTHOR> 徐辉
     * @date : 2021/3/4 19:40
     * 铁血四兄弟
     */
    //ExecuteDTO<BigDecimal> getStoreRewardValueSum(AtomReqAgentRewardDTO reqAgentRewardDTO);

    /**
     * 暂不适用，但请勿删
     * 分销店维度
     * platform ->（分销店）总部分销订单 -> 本店佣金（累计分销佣金 预估佣金 已入账佣金）
     * @param reqAgentRewardDTO
     * <AUTHOR> 徐辉
     * @date : 2021/3/5 18:30
     * 铁血四兄弟
     */
    //ExecuteDTO<BigDecimal> getDisStoreRewardValueSum(AtomReqAgentRewardDTO reqAgentRewardDTO);

    /**
     * 云池 platform 综合查询
     * 代理人佣金 - 云池 - 汇推广 - 店铺分销订单（商家、店铺）
     * 订单总佣金 - 云池 - 汇推广 - （供货店）云池供货订单（商家、店铺）
     * 待返金额 - 云池 - 汇推广 - （供货店）云池供货订单（商家、店铺）
     * 本店获得佣金 - 云池 - 赚佣金 - (分销店)总部分销订单（商家、店铺）
     * @param reqAgentRewardDTO
     * <AUTHOR> 徐辉
     * @date : 2021/3/8 10:00
     * @return
     */
    ExecuteDTO<AtomResPlanAsAWholeRewardDTO> getPlanAsAWholeReward(AtomReqAgentRewardDTO reqAgentRewardDTO);

    /**
     * 获取分润集合
     * @param reqAgentRewardDTO
     * @return
     */
    ExecuteDTO<List<AtomResAgentRewardProfitDTO>> getProfitByOrderNo(AtomReqAgentRewardDTO reqAgentRewardDTO);

    /**
      * @param orderNo
      * @Description : 根据订单查询酬劳
      * <AUTHOR> 高繁
      * @date : 2021/6/25 11:11
     */
    ExecuteDTO<List<AtomResAgentRewardDTO>> getAgentRewardOrder(String orderNo);

    /**
     * <AUTHOR>
     * @Description 查询店铺一段时间内的分销订单数和分销金额
     * @Date 2021/6/29
     * @Param [agentRewardStoreNoDTO]
     * @return cn.htdt.common.dto.response.ExecuteDTO<cn.htdt.marketcenter.dto.response.AgentRewardOrderMoneyDTO>
     **/
    ExecuteDTO<AgentRewardOrderMoneyDTO> selectAgentRewardOrderMoney(AgentRewardStoreNoDTO agentRewardStoreNoDTO);

    /**
     * @description 分页查询酬劳信息列表
     * <AUTHOR>
     * @date 2021-06-30 09:44:38
     * @param atomReqAgentRewardDTO
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResAgentRewardDTO>> getRewardListByPage(AtomReqAgentRewardDTO atomReqAgentRewardDTO);

    /**
     * @description 酬劳月度汇总
     * <AUTHOR>
     * @date 2021-12-21 16:40:19
     * @param atomReqAgentRewardDTO
     * @return
     */
    ExecuteDTO<List<AtomResAgentRewardDTO>> getRewardMonthCount(AtomReqAgentRewardDTO atomReqAgentRewardDTO);

    /**
     * @description 总部分销成交订单数
     * <AUTHOR>
     * @date 2021-07-28 11:08:11
     * @param atomReqAgentRewardDTO
     * @return
     */
    ExecuteDTO<Integer> getOrderCount(AtomReqAgentRewardDTO atomReqAgentRewardDTO);

    /**
     * @description 总部分销获得佣金
     * <AUTHOR>
     * @date 2021-07-28 11:09:05
     * @param atomReqAgentRewardDTO
     * @return
     */
    ExecuteDTO<BigDecimal> getYjCount(AtomReqAgentRewardDTO atomReqAgentRewardDTO);

    /**
     * @description 根据订单号查询佣金
     * <AUTHOR>
     * @date 2021-08-09 11:30:11
     * @param atomReqAgentRewardDTO
     * @return
     */
    ExecuteDTO<BigDecimal> getYjCountByOrderNo(AtomReqAgentRewardDTO atomReqAgentRewardDTO);

    /**
     * 根据订单号批量查询佣金(导出优化专用sql)
     * @Date 2021/11/8
     * <AUTHOR>
     * @param orderNos
     * @return ExecuteDTO<List<AtomYjCountDTO>>
     **/
    ExecuteDTO<List<AtomYjCountDTO>> selectYjCountByOrderNos(Set<String> orderNos);

    /**
     * 根据订单号批量查询分销店佣金-解冻
     * <AUTHOR>
     * @param orderNos
     * @return ExecuteDTO<List<AtomYjCountDTO>>
     **/
    ExecuteDTO<List<AtomYjCountDTO>> selectStoreYjByOrderNos(Set<String> orderNos);

    /**
     * 根据订单号批量查询分销店佣金-冻结
     * <AUTHOR>
     * @param orderNos
     * @return ExecuteDTO<List<AtomYjCountDTO>>
     **/
    ExecuteDTO<List<AtomYjCountDTO>> selectStoreDjYjByOrderNos(Set<String> orderNos);

    /**
     * @description 今日任务达人榜
     * <AUTHOR>
     * @date 2021-08-26 09:23:57
     * @param atomReqAgentRewardDTO
     * @return
     */
    ExecuteDTO<List<AtomResAgentRewardDTO>> getBestRewardAgentList(AtomReqAgentRewardDTO atomReqAgentRewardDTO);

    /**
     * 商家未付给的拥金数量
     *
     * @param atomReqAgentRewardDTO 商家编码
     * @return 数量
     */
    ExecuteDTO<Integer> getUnpaidRewardCount(AtomReqAgentRewardDTO atomReqAgentRewardDTO);

    /**
     * 查询可提现佣金（预估+冻结+解冻）
     * @param agentNo 代理人编号
     * @return 可提现佣金（预估+冻结+解冻）
     */
    ExecuteDTO<BigDecimal> getYjrewardTotal(String agentNo);

    /**
     * 查询未使用的服务券
     * @param agentNo 代理人编号
     * @return 未使用的服务券
     */
    ExecuteDTO<BigDecimal> getUnusedAgentServiceCoupon(String agentNo);

    /**
     * 查询未使用的现金券
     * @param agentNo 代理人编号
     * @return 未使用的现金券
     */
    ExecuteDTO<BigDecimal> getUnusedAgentExclusiveCoupon(String agentNo);

    /**
     * 查询未使用的代理人酬劳（排除佣金，现金券，服务券）
     * @param agentNo 代理人编号
     * @return 未使用的代理人酬劳（排除佣金，现金券，服务券）
     */
    ExecuteDTO<BigDecimal> getUnusedAgentOtherReward(String agentNo);
}
