package cn.htdt.marketprocess.legacycenter.api.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqCloudPoolGoodsCommissionConfigDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-01-21
 * @Description 云池商品佣金配置操作
 **/
public interface AtomCloudPoolGoodsCommissionConfigOperatService {
    /**
     * @Description 新增云池商品佣金配置信息
     * <AUTHOR>
     * @param configDTO
     * @return
     */
    ExecuteDTO save(AtomReqCloudPoolGoodsCommissionConfigDTO configDTO);

    /**
     * @Description 批量新增云池商品佣金配置信息
     * <AUTHOR>
     * @param configDTOList
     * @return
     */
    ExecuteDTO batchSave(List<AtomReqCloudPoolGoodsCommissionConfigDTO> configDTOList);
    /**
     * @Description 修改云池商品佣金配置信息
     * <AUTHOR>
     * @param configDTO
     * @return
     */
    ExecuteDTO updateByParam(AtomReqCloudPoolGoodsCommissionConfigDTO configDTO);
    /**
     * @Description 修改云池商品佣金配置信息
     * <AUTHOR>
     * @param configDTO
     * @return
     */
    ExecuteDTO batchUpdateByParam(List<AtomReqCloudPoolGoodsCommissionConfigDTO> configDTO);
    /**
     * @Description 批量删除云池商品佣金配置信息
     * <AUTHOR>
     * @param configDTO
     * @return
     */
    ExecuteDTO batchDeleteByParam(List<AtomReqCloudPoolGoodsCommissionConfigDTO> configDTO);

}
