package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.AtomReqDistributeGoodsRewardDTO;
import cn.htdt.marketcenter.dto.response.AtomResDistributeGoodsRewardDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-01-25
 * @Description 分销商品酬劳查询
 **/
public interface AtomDistributeGoodsRewardAnalysisService {

    /**
     * 根据商品编码集合查询商品佣金列表
     *
     * @param reqDistributeGoodsRewardDTO
     * @return
     * <AUTHOR>
     */
    ExecuteDTO<List<AtomResDistributeGoodsRewardDTO>> getAgentRewardByGoodsNoList(AtomReqDistributeGoodsRewardDTO reqDistributeGoodsRewardDTO);

    /**
     * 通过商品编号以及代理人编号获取分销数量
     *
     * @param reqDistributeGoodsRewardDTO
     * @return
     * <AUTHOR>
     */
    ExecuteDTO<List<AtomResDistributeGoodsRewardDTO>> getAgentDistributeEffectByGoodsNo(AtomReqDistributeGoodsRewardDTO reqDistributeGoodsRewardDTO);

    /**
     * 查询商品粉丝自购次数或统计商品代理人分销次数列表，分页查询，支持代理人名称模糊查询，代理人手机号精确查询
     *
     * @param reqDistributeGoodsRewardDTO 查询参数
     * @return ExecuteDTO<ExecutePageDTO < AtomResDistributeGoodsRewardDTO>>
     * <AUTHOR>
     */
    ExecuteDTO<ExecutePageDTO<AtomResDistributeGoodsRewardDTO>> getAgentDistributeTimesByGoodsNo(AtomReqDistributeGoodsRewardDTO reqDistributeGoodsRewardDTO);
}
