package cn.htdt.marketprocess.legacycenter.api.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqGoodsPromotionGoodsRelationDTO;

import java.util.List;

/**
 * 商品促销活动参与商品的操作类
 *
 * <AUTHOR>
 * @date 2021-06-28
 */
public interface AtomGoodsPromotionGoodsRelationOperateService {


    /**
     * 新增参与促销活动的商品
     *
     * @param goodsRelationDTOList
     * @return
     * <AUTHOR>
     * @date 2021-06-28
     */
    ExecuteDTO savePromotionGoodsRelation(List<AtomReqGoodsPromotionGoodsRelationDTO> goodsRelationDTOList);

    /**
     * 根据促销活动编号，删除关联的参与商品数据
     *
     * @param promotionNo
     * @return
     * <AUTHOR>
     * @date 2021-06-29
     */
    ExecuteDTO deletePromotionGoodsRelation(String promotionNo);

    /**
     * @param atomReqGoodsPromotionGoodsRelationDTO
     * @Description : 促销商品库存扣减
     * <AUTHOR> 高繁
     * @date : 2021/7/7 10:33
     */
    ExecuteDTO promotionGoodsStockReduce(AtomReqGoodsPromotionGoodsRelationDTO atomReqGoodsPromotionGoodsRelationDTO);

    /**
     * 批量删除指定活动下指定时间段内的商品
     *
     * @param goodsRelationDTOList
     * <AUTHOR>
     * @date 2021-07-20
     */
    ExecuteDTO batchDeletePromotionGoodsNoRelation(List<AtomReqGoodsPromotionGoodsRelationDTO> goodsRelationDTOList);


    /**
     * @param atomReqGoodsPromotionGoodsRelationDTO
     * @Description : 促销商品库存增加
     * <AUTHOR> 高繁
     * @date : 2021/8/2 10:38
     */
    ExecuteDTO promotionGoodsStockAdd(AtomReqGoodsPromotionGoodsRelationDTO atomReqGoodsPromotionGoodsRelationDTO);

    /**
     * 根据促销活动编号和时间段编号，删除关联的参与商品数据
     *
     * @param relationDTO
     * @return
     * <AUTHOR>
     * @date 2021-09-23
     */
    ExecuteDTO deletePromotionGoodsByNo(AtomReqGoodsPromotionGoodsRelationDTO relationDTO);

}
