package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.AtomReqExpandHelpRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomResExpandHelpRecordDTO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 膨胀红包助力记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
public interface AtomExpandHelpRecordAnalysisService {

    /**
     * 用于膨胀红包发起助力列表
     *
     * @param atomReqExpandHelpRecordDTO
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResExpandHelpRecordDTO>> getExpandHelpRecordPage(AtomReqExpandHelpRecordDTO atomReqExpandHelpRecordDTO);

    /**
     * 用于膨胀红包发起助力列表
     *
     * @param atomReqExpandHelpRecordDTO
     * @return
     */
    ExecuteDTO<List<AtomResExpandHelpRecordDTO>> getExpandHelpRecordList(AtomReqExpandHelpRecordDTO atomReqExpandHelpRecordDTO);

    /**
     * 膨胀红包助力次数
     *
     * @param atomReqExpandHelpRecordDTO
     * @return
     */
    ExecuteDTO<Integer> getExpandHelpRecordCount(AtomReqExpandHelpRecordDTO atomReqExpandHelpRecordDTO);

    /**
     * 获取已助力金额
     *
     * @param atomReqExpandHelpRecordDTO
     * @return
     */
    ExecuteDTO<BigDecimal> getExpandHelpHelpMoney(AtomReqExpandHelpRecordDTO atomReqExpandHelpRecordDTO);

}
