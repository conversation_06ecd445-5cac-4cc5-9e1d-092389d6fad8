package cn.htdt.marketprocess.legacycenter.api.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqLotteryStoreRuleDTO;
import cn.htdt.marketcenter.dto.request.AtomReqPromotionStoreRelationDTO;
import cn.htdt.marketcenter.dto.response.AtomResPromotionStoreRelationDTO;
import cn.htdt.marketprocess.domain.PromotionStoreRelationDomain;
import cn.htdt.marketprocess.dto.request.ReqPromotionStoreRelationDTO;

import java.util.List;

/**
 * <p>
 * 活动店铺关联上下架表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-18
 */
public interface AtomPromotionStoreRelationOperatService {

    /**
     * 判断查询类型是全部店铺和报名活动的平台活动是否存在，不存在直接保存关联-商家
     *
     * @param atomReqLotteryStoreRuleDTO
     * @return
     */
    ExecuteDTO<AtomResPromotionStoreRelationDTO> boolLotteryStoreRelationMerchant(AtomReqLotteryStoreRuleDTO atomReqLotteryStoreRuleDTO);

    /**
     * 判断查询类型是全部店铺和报名活动的平台活动是否存在，不存在直接保存关联-店铺
     *
     * @param atomReqLotteryStoreRuleDTO
     * @return
     */
    ExecuteDTO<AtomResPromotionStoreRelationDTO> boolLotteryStoreRelationStore(AtomReqLotteryStoreRuleDTO atomReqLotteryStoreRuleDTO);

    /**
     * 判断并返回关联关系
     *
     * @param atomReqPromotionStoreRelationDTO
     * @return
     */
    ExecuteDTO<AtomResPromotionStoreRelationDTO> saveLotteryStoreRelation(AtomReqPromotionStoreRelationDTO atomReqPromotionStoreRelationDTO);

    /**
     * @param atomReqPromotionStoreRelationDTO
     * @Description : 店铺活动上下架
     * <AUTHOR> 王磊
     * @date : 2021/3/9 11:19
     */
    ExecuteDTO modifyStoreShelves(AtomReqPromotionStoreRelationDTO atomReqPromotionStoreRelationDTO);

    /**
     * 保存平台活动，店铺上下架数据
     *
     * <AUTHOR>
     * @date 2021-07-02
     */
    ExecuteDTO saveStoreRelation(AtomReqPromotionStoreRelationDTO atomReqPromotionStoreRelationDTO);

    /**
     * 批量操作新增/更新
     * @Date 2023/5/29
     * <AUTHOR>
     * @param insertList
     * @param updateList
     * @return ExecuteDTO
     **/
    ExecuteDTO batchOperate(List<ReqPromotionStoreRelationDTO> insertList,List<ReqPromotionStoreRelationDTO>  updateList);
    
    /**
     * 批量保存社群接龙关联店铺
     * @param promotionStoreRelationList
     * @param editFlag 0新增 1更新
     * <AUTHOR>
     * @date 2023-06-03
     * @return
     */
	ExecuteDTO batchSavePromotionStoreRelation(List<AtomReqPromotionStoreRelationDTO> promotionStoreRelationList, Integer editFlag);
	
	/**
	 * 商家社群接龙关联全部店铺，所有店铺可用
	 * @param atomReqPromotionStoreRelationDTO
	 * @return
	 */
	ExecuteDTO enableAllStoreRelation(AtomReqPromotionStoreRelationDTO atomReqPromotionStoreRelationDTO);
	
	/**
	 * 商家社群接龙已经存在，即将取消关联的店铺
	 * @param atomReqPromotionStoreRelationDTO
	 * @param disableStoreNos
	 * <AUTHOR>
	 * @date 2023-06-03
	 * @return
	 */
	ExecuteDTO disableExistStoreRelation(AtomReqPromotionStoreRelationDTO atomReqPromotionStoreRelationDTO, List<String> disableStoreNos);

	/**
	 * 商家社群接龙已经存在，继续关联的店铺
	 * @param enableStoreRelationDTO
	 * @param oldEnableStoreList
	 * <AUTHOR>
	 * @date 2023-06-03
	 * @return
	 */
	ExecuteDTO enableExistStoreRelation(AtomReqPromotionStoreRelationDTO enableStoreRelationDTO,
			List<String> oldEnableStoreList);
	
	/**
	 * 商家社群接龙分发店铺商家设置上下架
	 * @param atomReqPromotionStoreRelationDTO
	 * <AUTHOR>
	 * @date 2023-06-06
	 * @return
	 */
	ExecuteDTO upDownStoreRelation(AtomReqPromotionStoreRelationDTO atomReqPromotionStoreRelationDTO);

	/**
	 * 删除商家社群接龙分发店铺
	 * @param promotionNo
	 * <AUTHOR>
	 * @date 2023-06-06
	 * @return
	 */
	ExecuteDTO deletePromotionStoreRelation(String promotionNo);

	/**
	 * 更新商家关联店铺的分发店铺方式以及上下架状态等信息
	 * @param atomReqPromotionStoreRelationDTO
	 * <AUTHOR>
	 * @date 2023-06-30
	 * @return
	 */
	ExecuteDTO updateSjStoreRelation(AtomReqPromotionStoreRelationDTO atomReqPromotionStoreRelationDTO);

	/**
	 * 蛋品, 复制商家拆盲盒活动, 批量保存活动的店铺规则和参与活动的店铺信息
	 *
	 * @param promotionStoreRelationDomainList 保存的数据
	 * @return 保存结果
	 */
	ExecuteDTO<Boolean> batchAddMerchantLotteryDrawInfo(List<PromotionStoreRelationDomain> promotionStoreRelationDomainList);
	
}
