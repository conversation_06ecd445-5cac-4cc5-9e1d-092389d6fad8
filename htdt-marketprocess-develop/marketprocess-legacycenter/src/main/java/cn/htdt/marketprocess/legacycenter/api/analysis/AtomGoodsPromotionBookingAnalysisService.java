package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqGoodsPromotionBookingDTO;
import cn.htdt.marketcenter.dto.response.AtomResGoodsPromotionBookingDTO;

import java.util.List;

/**
 * 活动商品预约类
 *
 * <AUTHOR>
 * @date 2021/7/20 17:00
 */
public interface AtomGoodsPromotionBookingAnalysisService {

    /**
     * @param
     * @Description : 查询活动商品预约信息
     * <AUTHOR> 卜金隆
     * @date : 2021/7/20 17:30
     */
    ExecuteDTO<List<AtomResGoodsPromotionBookingDTO>> getGoodsPromotionBookingInfoList(AtomReqGoodsPromotionBookingDTO atomReqGoodsPromotionBookingDTO);

    ExecuteDTO<List<AtomResGoodsPromotionBookingDTO>> getFansBookingInfoList(AtomReqGoodsPromotionBookingDTO atomReqGoodsPromotionBookingDTO);

    /**
     * 查询促销活动场次下的商品预约数
     *
     * @param bookingDTO 查询参数
     * @return 预约数
     * <AUTHOR>
     * @date 2021-10-09
     */
    ExecuteDTO<Integer> getPromotionGoodsBookingNum(AtomReqGoodsPromotionBookingDTO bookingDTO);

}


