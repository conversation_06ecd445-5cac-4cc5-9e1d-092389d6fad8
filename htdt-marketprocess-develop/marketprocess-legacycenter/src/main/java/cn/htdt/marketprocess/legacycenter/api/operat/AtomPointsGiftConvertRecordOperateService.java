package cn.htdt.marketprocess.legacycenter.api.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqPointsGiftConvertRecordDTO;

/**
 * 积分兑换类
 *
 * <AUTHOR>
 * @since 2021-10-13
 */
public interface AtomPointsGiftConvertRecordOperateService {
    /**
     * 新增积分兑换礼品流水记录
     *
     * @param convertRecordDTO 记录信息
     * @return void
     */
    ExecuteDTO addConvertRecord(AtomReqPointsGiftConvertRecordDTO convertRecordDTO);
}
