package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.AtomReqUserPointsDTO;
import cn.htdt.marketcenter.dto.response.AtomResUserPointsDTO;

import java.util.List;

/**
 * 粉丝积分
 *
 * <AUTHOR>
 * @since 2021-09-30
 */
public interface AtomUserPointsAnalysisService {
    /**
     * 根据一定条件查单条粉丝积分数据
     *
     * @param userPointsDTO 手机号/名字/店铺编号
     * @return 粉丝积分
     */
    ExecuteDTO<AtomResUserPointsDTO> getSimpleUserPoints(AtomReqUserPointsDTO userPointsDTO);

    /**
     * 查询一定条件下的分页数据
     *
     * @param userPointsDTO 手机号/名字/店铺编号
     * @return 粉丝积分
     */
    ExecuteDTO<ExecutePageDTO<AtomResUserPointsDTO>> getUserPointsPage(AtomReqUserPointsDTO userPointsDTO);

    /**
     * 根据手机号查粉丝积分以及兑换的礼品数量
     *
     * @param userPointsDTO 手机号/店铺号
     * @return粉丝积分以及兑换的礼品数量
     */
    ExecuteDTO<AtomResUserPointsDTO> getUserConvert(AtomReqUserPointsDTO userPointsDTO);

    /**
     * 查询店铺累计发放和消费的积分 bossApp
     *
     * @param atomReqUserPointsDTO 店铺编号
     * @return 店铺累计发放和消费的积分
     */
    ExecuteDTO<AtomResUserPointsDTO> getStoreTotalPoints(AtomReqUserPointsDTO atomReqUserPointsDTO);


    /**
      * @param userPointsDTO
      * @Description : 批量查询粉丝积分信息
      * <AUTHOR> 高繁
      * @date : 2021/11/5 15:16
     */
    ExecuteDTO<List<AtomResUserPointsDTO>> getUserPointsList(AtomReqUserPointsDTO userPointsDTO);


    /**
     * 查询商家名下所有用户剩余的积分
     *
     * @param atomReqUserPointsDTO 商家编号
     * @return 总的剩余积分数
     * <AUTHOR>
     */
    ExecuteDTO<Integer> getMerchantRemainPoints(AtomReqUserPointsDTO atomReqUserPointsDTO);

    /**
     * 查询某粉丝所有未使用的积分总和
     *
     * @param atomReqUserPointsDTO 粉丝编号
     * @return 未使用的积分总和
     */
    ExecuteDTO<Integer> getFansRemainPoints(AtomReqUserPointsDTO atomReqUserPointsDTO);

    /**
     * 20230928蛋品-wh-门店-查询共享商家下所有门店粉丝积分信息
     * 查询共享商家下所有门店粉丝积分信息
     *
     * @param userPointsDTO 手机号/名字/店铺编号
     * @return 粉丝积分
     */
    ExecuteDTO<ExecutePageDTO<AtomResUserPointsDTO>> getSharingStoreUserPointsPage(AtomReqUserPointsDTO userPointsDTO);


    /**
     * 20230928蛋品-wh-门店-查询共享店铺下根据店铺编号和粉丝编号查询粉丝积分(单个)
     * 查询共享店铺下根据店铺编号和粉丝编号查询粉丝积分(单个)
     *
     * @param userPointsDTO 手机号/名字/店铺编号
     * @return 粉丝积分
     */
    ExecuteDTO<AtomResUserPointsDTO> getMerchantNoSimpleUserPoints(AtomReqUserPointsDTO userPointsDTO);
}
