package cn.htdt.marketprocess.legacycenter.api.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqVirtualCoinRecordDTO;

/**
 * 橙豆收入消费记录
 * <AUTHOR>
 * @date 2022/1/19
 */
public interface AtomVirtualCoinRecordOperatService {

    /**
     * 插入收入消费记录
     * @param recordDTO
     * @return
     */
    ExecuteDTO<Boolean> insertVirtualCoinRecord(AtomReqVirtualCoinRecordDTO recordDTO);
}
