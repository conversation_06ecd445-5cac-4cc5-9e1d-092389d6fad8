package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.AtomReqCouponGoodsDTO;
import cn.htdt.marketcenter.dto.request.AtomReqCouponSettingDTO;
import cn.htdt.marketcenter.dto.response.AtomResCouponGoodsDTO;
import cn.htdt.marketcenter.dto.response.AtomResCouponSettingDTO;

import java.util.List;

/**
 * <p>
 * 优惠券活动表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-02
 */
public interface AtomCouponSettingAnalysisService {

    /**
     * 查看优惠券活动分页列表-用于优惠券列表
     *
     * @param atomReqCouponSettingDTO
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResCouponSettingDTO>> getCouponSettingPage(AtomReqCouponSettingDTO atomReqCouponSettingDTO);

    /**
     * 查看优惠券活动分页列表-用于优惠券列表-店铺优惠券列表
     *
     * @param atomReqCouponSettingDTO
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResCouponSettingDTO>> getCouponSetting4StoreByPage(AtomReqCouponSettingDTO atomReqCouponSettingDTO);

    /**
     * 获取店铺优惠券列表
     *
     * @param atomReqCouponSettingDTO
     * @return
     */
    ExecuteDTO<List<AtomResCouponSettingDTO>> listCouponSetting4Store(AtomReqCouponSettingDTO atomReqCouponSettingDTO);

    /**
     * 获取优惠券活动-优惠券规则
     *
     * @param atomReqCouponSettingDTO
     * @return
     */
    ExecuteDTO<AtomResCouponSettingDTO> getCouponSetting(AtomReqCouponSettingDTO atomReqCouponSettingDTO);

    /**
     * 获取优惠券活动-优惠券参与商品
     *
     * @param atomReqCouponGoodsDTO
     * @return
     */
    ExecuteDTO<AtomResCouponGoodsDTO> getCouponGoodsRelation(AtomReqCouponGoodsDTO atomReqCouponGoodsDTO);

    /**
     *  hxg-通用获取店铺全部活动
     * @param atomReqCouponSettingDTO
     * @return
     * <AUTHOR>
     */
    ExecuteDTO<List<AtomResCouponSettingDTO>> selectHxgCouponInfoList(AtomReqCouponSettingDTO atomReqCouponSettingDTO);
    /**
     *  hxg-店铺3-10张券
     * @param atomReqCouponSettingDTO
     * @return
     * <AUTHOR>
     */
    ExecuteDTO<ExecutePageDTO<AtomResCouponSettingDTO>> getHomePageCouponsList(AtomReqCouponSettingDTO atomReqCouponSettingDTO);

    /**
     * 根据活动List获取优惠券活动
     * @param atomReqCouponSettingDTO
     * @return
     */
    ExecuteDTO<List<AtomResCouponSettingDTO>> getCouponSettingList(AtomReqCouponSettingDTO atomReqCouponSettingDTO);

}
