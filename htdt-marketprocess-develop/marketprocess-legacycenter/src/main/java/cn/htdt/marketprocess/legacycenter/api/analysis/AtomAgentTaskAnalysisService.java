package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.AtomReqAgentTaskDTO;
import cn.htdt.marketcenter.dto.request.AtomReqStoreAgentRewardDTO;
import cn.htdt.marketcenter.dto.request.AtomReqStoreAgentTaskDTO;
import cn.htdt.marketcenter.dto.response.AtomResAgentTaskDTO;
import cn.htdt.marketcenter.dto.response.AtomResStoreAgentTaskDTO;
import cn.htdt.marketcenter.dto.response.AtomResStoreTaskCollectDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-01-19
 * @Description 代理人任务查询
 **/
public interface AtomAgentTaskAnalysisService {

    /**
     * 根据taskNo，肯定是单条，包含所有信息
     * taskno，必须要存在，不然就是查询集合用 getListByPage
     * @param
     * @return
     * <AUTHOR>
     */
    ExecuteDTO<AtomResAgentTaskDTO> get(AtomReqAgentTaskDTO atomAgentTaskDTO);

    /**
     * 获取合法的代理人任务
     * @param atomAgentTaskDTO
     * @return
     */
    ExecuteDTO<AtomResAgentTaskDTO> getValidTask(AtomReqAgentTaskDTO atomAgentTaskDTO);

    /**
     * 分页查询，只包含主信息
     * @param atomAgentTaskDTO 查询请求对象
     * @return 分页查询订单数据
     */
    ExecuteDTO<ExecutePageDTO<AtomResAgentTaskDTO>> getListByPage(AtomReqAgentTaskDTO atomAgentTaskDTO);

    /**
     * 商家店铺平台任务分页查询，只包含主信息
     * @param reqStoreAgentTaskDTO 查询请求对象
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResStoreAgentTaskDTO>> getStoreAgentTaskListByParams(AtomReqStoreAgentTaskDTO reqStoreAgentTaskDTO);

    /**
     * 分页查询，只包含主信息
     * @param atomAgentTaskDTO 查询请求对象
     * @return 分页查询店铺代理人任务
     * <AUTHOR>
     */
    ExecuteDTO<ExecutePageDTO<AtomResAgentTaskDTO>> getStoreAgentTaskByPage(AtomReqAgentTaskDTO atomAgentTaskDTO);

    /**
      * @param :
      * @Description : 根据店铺编码，任务编码查询平台任务信息
      * <AUTHOR> 高繁
      * @date : 2021/1/26 11:34
     */
    ExecuteDTO<AtomResStoreAgentTaskDTO> getStoreAgentTaskByStoreNo(AtomReqStoreAgentTaskDTO reqStoreAgentTaskDTO);

    /**
      * @param reqStoreAgentTaskDTO
      * @Description : 根据店铺编号或商家编号查询未初始化的任务
      * <AUTHOR> 高繁
      * @date : 2021/2/22 16:04
     */
    ExecuteDTO<List<String>> getNotInitTaskListByStoreOrMerchant(AtomReqStoreAgentTaskDTO reqStoreAgentTaskDTO);

    /**
      * @param reqStoreAgentRewardDTO
      * @Description : 根据店铺编号+任务编号查询任务参与代理人数
      * <AUTHOR> 高繁
      * @date : 2021/2/25 10:31
     */
    ExecuteDTO<List<AtomResStoreTaskCollectDTO>> getStoreTaskJoinAgentNum(AtomReqStoreAgentRewardDTO reqStoreAgentRewardDTO);

    /**
     * @param merchantNo 商家编码
     * @Description : 根据商家编号查询已初始化的店铺
     * <AUTHOR> 高繁
     * @date : 2021/3/3 10:42
     */
    ExecuteDTO<List<String>> getInitStoreListByMerchant(String merchantNo);

    /**
     * @description 查询任务酬劳
     * <AUTHOR>
     * @date 2021-03-10 10:28:56
     * @param reqAgentTaskDTO
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResAgentTaskDTO>> getTaskRewardListByParams(AtomReqAgentTaskDTO reqAgentTaskDTO);

}