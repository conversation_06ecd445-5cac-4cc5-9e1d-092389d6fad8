package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.AtomReqPromotionInfoDTO;
import cn.htdt.marketcenter.dto.response.AtomResPromotionInfoDTO;

import java.util.List;

/**
 * 促销活动 服务类
 *
 * <AUTHOR>
 */
public interface AtomPromotionInfoAnalysisService {


    /**
     * 获取抽奖活动详情
     * 单个获取
     *
     * @param atomReqPromotionInfoDTO
     * @return
     */
    ExecuteDTO<AtomResPromotionInfoDTO> getPromotionInfo(AtomReqPromotionInfoDTO atomReqPromotionInfoDTO);

    /**
     * 获取所有有效的活动列表
     * @param atomReqPromotionInfoDTO
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResPromotionInfoDTO>> getPromotionInfoPage(AtomReqPromotionInfoDTO atomReqPromotionInfoDTO);

    /**
     * 获取对应活动标签信息
     * @param atomReqPromotionInfoDTO
     * @return
     */
    ExecuteDTO<List<AtomResPromotionInfoDTO>> getPromotionTypeList(AtomReqPromotionInfoDTO atomReqPromotionInfoDTO);

    /**
     * 获取活动数量
     * @param atomReqPromotionInfoDTO
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResPromotionInfoDTO>> selectPromotionNum(AtomReqPromotionInfoDTO atomReqPromotionInfoDTO);

    /**
     * 获取过期活动列表
     * @param atomReqPromotionInfoDTO
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResPromotionInfoDTO>> getPromotionShelvesPage(AtomReqPromotionInfoDTO atomReqPromotionInfoDTO);

    /**
     * 获取过期活动总数
     * @param atomReqPromotionInfoDTO
     * @return
     */
    ExecuteDTO<Integer> getPromotionShelvesCount(AtomReqPromotionInfoDTO atomReqPromotionInfoDTO);


    /**
     * 查询商家名下当前时间进行中的营销活动
     *
     * @param atomReqPromotionInfoDTO 商家编号
     * @return 营销活动个数
     */
    ExecuteDTO<Integer> getMerchantOnGoingPromotionCount(AtomReqPromotionInfoDTO atomReqPromotionInfoDTO);


    /**
     * 店铺装修活动列表
     * @param atomReqPromotionInfoDTO
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResPromotionInfoDTO>> getPromotionInfoForDecorate(AtomReqPromotionInfoDTO atomReqPromotionInfoDTO);

    /**
     * @description 统计创建活动数量
     * <AUTHOR>
     * @date 2022-06-21 14:13:36
     * @param atomReqPromotionInfoDTO
     * @return
     */
    ExecuteDTO<AtomResPromotionInfoDTO> getNewPromotionCount(AtomReqPromotionInfoDTO atomReqPromotionInfoDTO);

}
