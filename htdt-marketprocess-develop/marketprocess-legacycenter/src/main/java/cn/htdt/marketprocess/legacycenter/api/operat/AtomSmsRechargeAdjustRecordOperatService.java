package cn.htdt.marketprocess.legacycenter.api.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomSmsRechargeAdjustRecordDTO;

/**
 * 短信充值记录调整记录 service
 *
 * <AUTHOR>
 * @date 2022/9/19
 **/
public interface AtomSmsRechargeAdjustRecordOperatService {

    /**
     * 短信条数调整记录
     *
     * @param atomReqDTO 请求参数
     * @return ExecuteDTO
     * <AUTHOR>
     */
    ExecuteDTO addSmsRechargeAdjustRecord(AtomSmsRechargeAdjustRecordDTO atomReqDTO);
}
