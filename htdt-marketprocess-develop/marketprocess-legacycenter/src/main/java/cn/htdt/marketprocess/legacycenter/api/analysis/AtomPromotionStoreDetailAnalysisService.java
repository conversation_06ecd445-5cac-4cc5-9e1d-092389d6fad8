package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqPromotionStoreDetailDTO;
import cn.htdt.marketcenter.dto.response.AtomResPromotionStoreDetailDTO;

import java.util.List;

/**
 * <p>
 * 活动店铺详情表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-02
 */
public interface AtomPromotionStoreDetailAnalysisService {

    ExecuteDTO<List<AtomResPromotionStoreDetailDTO>> selectPromotionStoreDetailList(AtomReqPromotionStoreDetailDTO promotionStoreDetailVo);

    /**
     * 报名活动参与店铺数查询
     * @param promotionNoList
     * @return
     */
    ExecuteDTO<List<AtomResPromotionStoreDetailDTO>> getPromotionEnrollShopNumList(List<String> promotionNoList);

    /**
     * 报名活动是否参与店铺的查询
     * @param dto
     * @return
     */
    ExecuteDTO<Integer> getPromotionStoreDetailCount(AtomReqPromotionStoreDetailDTO dto);

}
