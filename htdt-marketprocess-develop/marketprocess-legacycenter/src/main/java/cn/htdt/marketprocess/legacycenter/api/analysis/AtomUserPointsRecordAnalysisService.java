package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.AtomReqFollowPointsDTO;
import cn.htdt.marketcenter.dto.request.AtomReqUserPointsRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomResUserPointsRecordDTO;

import java.util.List;

/**
 * <p>
 * 用户积分记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-28
 */
public interface AtomUserPointsRecordAnalysisService {
    /**
     * 查询用户积分记录
     *
     * @param atomReqUserPointsRecordDTO 请求参数
     * @return 店铺配置信息
     */
    ExecuteDTO<List<AtomResUserPointsRecordDTO>> getUserPointsRecordList(AtomReqUserPointsRecordDTO atomReqUserPointsRecordDTO);

    /**
     * 查用户积分明细
     * @param atomReqUserPointsRecordDTO 粉丝号/店铺编号/加分操作类型/积分操作时间范围
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResUserPointsRecordDTO>> getUserPointsRecordDetail(AtomReqUserPointsRecordDTO atomReqUserPointsRecordDTO);

    /**
      * @param atomReqUserPointsRecordDTO
      * @Description : 查询粉丝当前订单是否存在积分扣减记录
      * <AUTHOR> 高繁
      * @date : 2021/12/1 11:21
     */
    ExecuteDTO<Integer> getSoReturnPointsCount(AtomReqUserPointsRecordDTO atomReqUserPointsRecordDTO);

    /**
     * 查询粉丝在某个店铺是否产生过关注得积分的记录
     *
     * @param atomReqFollowPointsDTO 手机列表/店铺编号
     * @return 产生过关注得积分的手机号列表
     */
    ExecuteDTO<List<String>> getHasFollowRecordPhones(AtomReqFollowPointsDTO atomReqFollowPointsDTO);

    ExecuteDTO<List<String>> getExistPhones(AtomReqFollowPointsDTO atomReqFollowPointsDTO);

    /**
     * 20230928蛋品-wh-商家-查询商家积分明细
     * 查询商家积分明细
     * @param atomReqUserPointsRecordDTO 粉丝号/店铺编号/加分操作类型/积分操作时间范围
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResUserPointsRecordDTO>> getMerchantUserPointsRecordDetail(AtomReqUserPointsRecordDTO atomReqUserPointsRecordDTO);
}
