package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqPointsConfigDTO;
import cn.htdt.marketcenter.dto.response.AtomResPointsConfigDTO;

import java.util.List;

/**
 * <p>
 * 积分配置
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
public interface AtomPointsConfigAnalysisService {
    /**
     * 查询多条店铺配置信息
     *
     * @param atomReqPointsConfigDTO 请求参数
     * @return 店铺配置信息
     */
    ExecuteDTO<List<AtomResPointsConfigDTO>> getStorePointsConfigs(AtomReqPointsConfigDTO atomReqPointsConfigDTO);

    /**
     * 根据条件查询单条积分配置记录
     *
     * @param atomReqPointsConfigDTO 配置编号
     * @return 积分配置信息
     */
    ExecuteDTO<AtomResPointsConfigDTO> getPointsConfigByConfigNo(AtomReqPointsConfigDTO atomReqPointsConfigDTO);

    /**
     * 查询多条商家配置信息
     *
     * @param atomReqPointsConfigDTO 请求参数
     * @return 店铺配置信息
     */
    ExecuteDTO<List<AtomResPointsConfigDTO>> getMerchantPointsConfigs(AtomReqPointsConfigDTO atomReqPointsConfigDTO);
}
