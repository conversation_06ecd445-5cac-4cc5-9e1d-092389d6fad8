package cn.htdt.marketprocess.legacycenter.api.operat;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqUserPointsRecordDTO;

import java.util.List;

/**
 * 粉丝积分记录操作类
 *
 * <AUTHOR>
 * @since 2021-10-08
 */
public interface AtomUserPointsRecordOperateService {
    /**
     * 新增用户积分记录
     *
     * @param userPointsRecordDTO 粉丝号/店铺号/调整积分数/调整原因
     * @return 通用返回结果
     */
    ExecuteDTO addUserPointsRecord(AtomReqUserPointsRecordDTO userPointsRecordDTO);

    /**
     * 批量新增用户积分记录
     *
     * @param userPointsRecordDTOS 粉丝号/店铺号/调整积分数/调整原因
     * @return 通用返回结果
     */
    ExecuteDTO batchAddUserPointsRecord(List<AtomReqUserPointsRecordDTO> userPointsRecordDTOS);
}
