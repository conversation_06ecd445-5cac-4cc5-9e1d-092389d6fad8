package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqEnrollImportStoreDTO;
import cn.htdt.marketcenter.dto.response.AtomResEnrollImportStoreDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/22 15:51
 */
public interface AtomEnrollImportStoreAnalysisService {

    /**
      * @param reqDTO
      * @Description : 根据条件查询报名店铺
      * <AUTHOR> 卜金隆
      * @date : 2022/3/22 10:37
     */
    ExecuteDTO<List<AtomResEnrollImportStoreDTO>> getEnrollImportStoreList(AtomReqEnrollImportStoreDTO reqDTO);

}
