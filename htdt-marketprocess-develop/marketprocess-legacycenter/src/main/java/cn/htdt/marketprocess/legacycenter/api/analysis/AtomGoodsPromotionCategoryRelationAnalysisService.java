package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqGoodsPromotionCategoryRelationDTO;
import cn.htdt.marketcenter.dto.response.AtomResGoodsPromotionCategoryRelationDTO;

import java.util.List;

/**
 * 促销活动 类目关联查询类
 *
 * <AUTHOR>
 * @date 2022-02-21
 */
public interface AtomGoodsPromotionCategoryRelationAnalysisService {

    /**
     * 根据活动编号查询活动的关联类目
     *
     * @param reqDTO
     * @return
     * <AUTHOR>
     * @date 2022-02-21
     */
    ExecuteDTO<List<AtomResGoodsPromotionCategoryRelationDTO>> getGoodsPromotionCategoryByParam(AtomReqGoodsPromotionCategoryRelationDTO reqDTO);
}
