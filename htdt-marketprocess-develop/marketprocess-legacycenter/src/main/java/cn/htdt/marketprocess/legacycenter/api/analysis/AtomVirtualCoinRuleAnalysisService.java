package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.AtomReqVirtualCoinRuleDTO;
import cn.htdt.marketcenter.dto.response.AtomResVirtualCoinRuleDTO;

import java.util.List;

/**
 * 橙豆规则接口
 * <AUTHOR>
 * @date 2022/1/19
 */
public interface AtomVirtualCoinRuleAnalysisService {

    /**
     * 查询橙豆规则列表
     * @param virtualCoinRuleDTO
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResVirtualCoinRuleDTO>> selectVirtualCoinRuleList(AtomReqVirtualCoinRuleDTO virtualCoinRuleDTO);

    /**
     * 查询橙豆规则
     * @param virtualCoinRuleDTO
     * @return
     */
    ExecuteDTO<AtomResVirtualCoinRuleDTO> selectVirtualCoinRule(AtomReqVirtualCoinRuleDTO virtualCoinRuleDTO);

    /**
     * 查询橙豆规则
     * @param virtualCoinRuleDTO
     * @return
     */
    ExecuteDTO<AtomResVirtualCoinRuleDTO> selectVirtualCoinRuleWithOutSelf(AtomReqVirtualCoinRuleDTO virtualCoinRuleDTO);

    /**
     * 查询购物币规则
     * @param virtualCoinRuleDTO
     * @return
     */
    ExecuteDTO<List<AtomResVirtualCoinRuleDTO>> selectVirtualCoinRules(AtomReqVirtualCoinRuleDTO virtualCoinRuleDTO);

    /**
     * 查询购物币规则
     * @param virtualCoinRuleDTO
     * @return
     */
    ExecuteDTO<List<AtomResVirtualCoinRuleDTO>> selectVirtualCoinRuleWithOutSelfs(AtomReqVirtualCoinRuleDTO virtualCoinRuleDTO);
}
