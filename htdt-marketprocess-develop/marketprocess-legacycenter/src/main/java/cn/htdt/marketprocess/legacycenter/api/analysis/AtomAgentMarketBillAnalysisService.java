package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqAgentMarketBillDTO;
import cn.htdt.marketcenter.dto.response.AgentNumShareSumDTO;
import cn.htdt.marketcenter.dto.response.AtomResAgentMarketBillDTO;

/**
 * <AUTHOR>
 * @Date 2021-01-13
 * @Description 任务和营销清单原子查询服务
 */
public interface AtomAgentMarketBillAnalysisService {

    /**
     * @Description 查询任务和营销清单
     * <AUTHOR>
     * @param reqAgentMarketBillDTO
     * @return
     */
    ExecuteDTO<AtomResAgentMarketBillDTO> getAgentMarketBill(AtomReqAgentMarketBillDTO reqAgentMarketBillDTO);

    /**
     * <AUTHOR>
     * @Description 统计当天店铺代理人分享次数
     * @Date 2021/6/30
     * @Param [reqAgentMarketBillDTO]
     * @return cn.htdt.common.dto.response.ExecuteDTO<java.lang.Integer>
     **/
    ExecuteDTO<Integer> selectshareSumByStoreNo(AtomReqAgentMarketBillDTO reqAgentMarketBillDTO);

    /**
     * <AUTHOR>
     * @Description 近30天分享的代理人数和分享次数
     * @Date 2021/6/30
     * @Param [reqAgentMarketBillDTO]
     * @return cn.htdt.common.dto.response.ExecuteDTO<java.lang.Integer>
     **/
    ExecuteDTO<AgentNumShareSumDTO> selectAgentNumShareSumByStoreNo(AtomReqAgentMarketBillDTO reqAgentMarketBillDTO);
}
