package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketcenter.dto.request.AtomReqGoodsPromotionPeriodDTO;
import cn.htdt.marketcenter.dto.response.AtomResGoodsPromotionPeriodDTO;

import java.util.List;

/**
 * 商品促销活动时间段查询接口
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
public interface AtomGoodsPromotionPeriodAnalysisService {

    /**
     * 根据活动编号查询活动的时间段信息
     *
     * @param reqDTO
     * @return
     * <AUTHOR>
     * @date 2021-07-05
     */
    ExecuteDTO<List<AtomResGoodsPromotionPeriodDTO>> getGoodsPromotionPeriodByPromotionNo(AtomReqGoodsPromotionPeriodDTO reqDTO);

    /**
     * 根据活动编号和时间段编号查询对应场次的活动信息
     *
     * @param reqDTO
     * <AUTHOR>
     * @date 2021-08-06
     */
    ExecuteDTO<AtomResGoodsPromotionPeriodDTO> selectPromotionPeriodInfo(AtomReqGoodsPromotionPeriodDTO reqDTO);

    /**
     * 根据活动编号查询活动以及场次信息
     *
     * @param reqDTO
     * <AUTHOR>
     * @date 2021-11-09
     */
    ExecuteDTO<List<AtomResGoodsPromotionPeriodDTO>> selectPromotionPeriodList(AtomReqGoodsPromotionPeriodDTO reqDTO);

    /**
     * 根据活动编号和活动场次编号查询对应场次下的活动基本信息
     *
     * @param reqDTO 查询参数
     * <AUTHOR>
     * @date 2021-12-06
     */
    ExecuteDTO<AtomResGoodsPromotionPeriodDTO> getPromotionPeriodBaseInfo(AtomReqGoodsPromotionPeriodDTO reqDTO);
}
