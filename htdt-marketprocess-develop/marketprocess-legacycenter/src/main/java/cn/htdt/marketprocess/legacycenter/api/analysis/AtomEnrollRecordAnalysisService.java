package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.ApplyPromoteRecordDto;
import cn.htdt.marketcenter.dto.request.AtomReqEnrollRecordDTO;
import cn.htdt.marketcenter.dto.response.AtomResEnrollApplyCountDTO;
import cn.htdt.marketcenter.dto.response.AtomResEnrollDrawRecordDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/1 15:51
 */
public interface AtomEnrollRecordAnalysisService {

    /**
      * @param reqEnrollRecordDTO
      * @Description : 报名记录列表
      * <AUTHOR> 高繁
      * @date : 2021/4/6 16:22
     */
    ExecuteDTO<ExecutePageDTO<AtomResEnrollDrawRecordDTO>> getEnrollRecordList(AtomReqEnrollRecordDTO reqEnrollRecordDTO);

    /**
      * @param reqEnrollRecordDTO
      * @Description : 获取活动下所有的报名记录
      * <AUTHOR> 高繁
      * @date : 2021/4/8 17:43
     */
    ExecuteDTO<List<AtomResEnrollDrawRecordDTO>> getAllEnrollRecordList(AtomReqEnrollRecordDTO reqEnrollRecordDTO);

    /**
     * @Description : 查询是否需要店铺参与的报名活动信息是否存在
     * <AUTHOR>
     * @Date 2021/4/19 14:33
     * @Param reqDtO
     * @Return
    */
    ExecuteDTO<List<AtomResEnrollDrawRecordDTO>> getEnrollRecordNeedStoreList(AtomReqEnrollRecordDTO reqDtO);

    /**
     * @Description : 报名记录统计接口
     * <AUTHOR>
     * @Date 2022/3/24 17:33
     * @Param reqDtO
     * @Return
     */
    ExecuteDTO<AtomResEnrollApplyCountDTO> enrollApplyCount(AtomReqEnrollRecordDTO reqDtO);


    ExecuteDTO<Object> updateRecordInfoList(List<ApplyPromoteRecordDto> list);

}
