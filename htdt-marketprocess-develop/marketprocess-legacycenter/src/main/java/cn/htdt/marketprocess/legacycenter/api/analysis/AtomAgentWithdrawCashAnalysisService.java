package cn.htdt.marketprocess.legacycenter.api.analysis;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.common.dto.response.ExecutePageDTO;
import cn.htdt.marketcenter.dto.request.AtomReqAgentWithdrawCashDTO;
import cn.htdt.marketcenter.dto.response.AtomResAgentWithdrawCashDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-03-15
 * @description 提现记录查询
 **/
public interface AtomAgentWithdrawCashAnalysisService {
    
    /**
     * @description 提现记录查询（不分页）
     * <AUTHOR>
     * @date 2021-03-15 10:57:38
     * @param atomReqAgentWithdrawCashDTO
     * @return
     */
    ExecuteDTO<List<AtomResAgentWithdrawCashDTO>> getListByParams(AtomReqAgentWithdrawCashDTO atomReqAgentWithdrawCashDTO);

    /**
     * @description 提现记录分页查询
     * <AUTHOR>
     * @date 2021-03-15 10:57:38
     * @param atomReqAgentWithdrawCashDTO
     * @return
     */
    ExecuteDTO<ExecutePageDTO<AtomResAgentWithdrawCashDTO>> getListByPage(AtomReqAgentWithdrawCashDTO atomReqAgentWithdrawCashDTO);

    /**
     * @description 已提现佣金汇总
     * <AUTHOR>
     * @date 2021-03-22 14:19:42
     * @param atomReqAgentWithdrawCashDTO
     * @return
     */
    ExecuteDTO<AtomResAgentWithdrawCashDTO> getWithdrawStatistics(AtomReqAgentWithdrawCashDTO atomReqAgentWithdrawCashDTO);

    /**
     * @description 提现记录月度统计
     * <AUTHOR>
     * @date 2021-03-15 15:28:33
     * @param atomReqAgentWithdrawCashDTO
     * @return
     */
    ExecuteDTO<List<AtomResAgentWithdrawCashDTO>> getMouthStatistics(AtomReqAgentWithdrawCashDTO atomReqAgentWithdrawCashDTO);

}
