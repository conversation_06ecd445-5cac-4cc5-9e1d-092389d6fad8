package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.VirtualCoinRuleCouponRelationDomain;
import cn.htdt.marketprocess.vo.VirtualCoinRuleAndCouponInfoVo;
import cn.htdt.marketprocess.vo.VirtualCoinRuleCouponRelationVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 橙豆规则优惠券关联表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-01-20
 */
@Mapper
public interface VirtualCoinRuleCouponRelationDao extends BaseMapper<VirtualCoinRuleCouponRelationDomain> {

    /**
     *  20230928蛋品-橙豆规则-赵翔宇-查询店铺橙豆规则或者商家橙豆规则对应的优惠券
     *
     * @param virtualCoinRuleCouponRelationVO 查询参数
     * @return 橙豆规则对应的优惠券信息
     */
    List<VirtualCoinRuleAndCouponInfoVo> getVirtualCoinRuleAndCouponInfoList(VirtualCoinRuleCouponRelationVO virtualCoinRuleCouponRelationVO);

    /**
     * 更新接口
     * @param virtualCoinRuleCouponRelationDomain 入参
     * @return 更新数量
     */
    int updVirtualCoinRuleCouponRelation(VirtualCoinRuleCouponRelationDomain virtualCoinRuleCouponRelationDomain);
}
