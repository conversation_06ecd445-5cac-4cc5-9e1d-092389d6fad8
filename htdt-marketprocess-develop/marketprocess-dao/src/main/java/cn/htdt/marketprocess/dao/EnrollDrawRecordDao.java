package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.EnrollDrawRecordDomain;
import cn.htdt.marketprocess.vo.ApplyPromoteRecordVo;
import cn.htdt.marketprocess.vo.EnrollApplyCountVO;
import cn.htdt.marketprocess.vo.EnrollDrawRecordVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 报名活动记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-01
 */
@Mapper
public interface EnrollDrawRecordDao extends BaseMapper<EnrollDrawRecordDomain> {


    /**
      * @param enrollDrawRecordDomain
      * @Description : 根据参数查询报名记录
      * <AUTHOR> 高繁
      * @date : 2021/4/6 16:37
     */
    List<EnrollDrawRecordDomain> selectEnrollRecordList(EnrollDrawRecordVo enrollDrawRecordDomain);

    /**
     * 根据参数查询报名记录,按活动分组取最新一条
     * @param enrollDrawRecordDomain enrollDrawRecordDomain
     * @return List<EnrollDrawRecordDomain>
     */
    List<EnrollDrawRecordDomain> selectEnrollRecordListGroupByPromotionNo(EnrollDrawRecordVo enrollDrawRecordDomain);

    /**
      * @param promotionNo
      * @Description : 根据活动编码删除报名记录-物理删除
      * <AUTHOR> 高繁
      * @date : 2021/4/8 14:54
     */
    int deleteEnrollRecordByPromotion(@Param("promotionNo") String promotionNo);
    /**
     * @param promotionNo
     * @Description : 根据活动编码删除报名记录-逻辑删除
     * <AUTHOR> 高繁
     * @date : 2021/4/8 14:54
     */
    int updateDeleteFlag(@Param("promotionNo") String promotionNo);
    /**
     * @param enrollDrawRecordDomain
     * @Description : 批量修改报名活动 报名受理状态
     * <AUTHOR> 卜金隆
     * @date : 2022/3/23 19:54
     */
    int updateApplyStatus(EnrollDrawRecordVo enrollDrawRecordDomain);
    /**
     * @param enrollDrawRecordDomain
     * @Description : 根据商家编码删除报名记录-逻辑删除
     * <AUTHOR> 卜金隆
     * @date : 2021/5/11 14:54
     */
    int deleteFlagByMerchantNo(EnrollDrawRecordVo enrollDrawRecordDomain);
    /**
     * @Description ：查询是否需要店铺参与的报名活动信息是否存在
     * <AUTHOR>
     * @Date 2021/4/19 11:12
     * @Param enrollDrawRecordDomain
     * @Return
    */
    List<EnrollDrawRecordVo> selectEnrollRecordNeedStoreList(EnrollDrawRecordVo enrollDrawRecordVo);
    /**
     * @Description : 报名记录统计接口
     * <AUTHOR>
     * @Date 2022/3/24 17:33
     * @Param reqDtO
     * @Return
     */
    EnrollApplyCountVO enrollApplyCount(EnrollDrawRecordVo enrollDrawRecordVo);

    /**
     * 更改任务完成状态
     * @param applyPromoteRecordVoList
     * @return
     */
    Integer batchUpdatTaskStatuseByPrimaryKey(@Param("applyPromoteRecordVoList") List<ApplyPromoteRecordVo> applyPromoteRecordVoList);

    /**
     * 商家是否有已经通知成功的数据
     * @param enrollDrawRecordDomain
     * @return
     */
    List<EnrollDrawRecordDomain> selectAcceptEnrollRecordList(EnrollDrawRecordVo enrollDrawRecordDomain);

    /**
     * 更改任务接受状态
     * @param enrollDrawRecordDomainList
     * @return
     */
    Integer batchUpdateAcceptStatusByPrimaryKey(@Param("enrollDrawRecordDomainList") List<EnrollDrawRecordDomain> enrollDrawRecordDomainList);
}
