package cn.htdt.marketprocess.vo;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 *
 * 查询店铺下所有粉丝累积获得积分的总计和店铺下所有粉丝累积扣减积分的总计
 *
 * <AUTHOR>
 * @date 2022-12-16
 */
@Data
public class UserPointsRecordStaticVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 操作类型(1000:积分获得 2000:积分扣减)
     */
    private String operateType;

    /**
     * 变动数量总数量
     */
    private Integer totalChangeNum;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
