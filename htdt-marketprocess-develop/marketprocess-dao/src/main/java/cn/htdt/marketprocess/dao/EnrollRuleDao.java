package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.EnrollRuleDomain;
import cn.htdt.marketprocess.vo.EnrollPromotionVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 抽奖规则 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-01
 */
@Mapper
public interface EnrollRuleDao extends BaseMapper<EnrollRuleDomain> {


    /**
     * @param enrollPromotionVO
     * @Description : 报名活动列表查询
     * <AUTHOR> 高繁
     * @date : 2021/4/1 16:59
     */
    List<EnrollPromotionVO> selectEnrollPromotionList(EnrollPromotionVO enrollPromotionVO);

    /**
     * 店铺查询可报名活动列表
     * @param enrollPromotionVO enrollPromotionVO
     * @return List<EnrollPromotionVO>
     */
    List<EnrollPromotionVO> selectStoreCanEnrollPromotionList(EnrollPromotionVO enrollPromotionVO);

    /**
     * @param promotionNoList
     * @Description : 报名活动参与店铺数查询
     * <AUTHOR> 高繁
     * @date : 2021/4/1 16:59
     */
    List<EnrollPromotionVO> selectEnrollShopNumList(@Param("promotionNoList") List<String> promotionNoList);

    /**
      * @param enrollPromotionVO
      * @Description : 根据参数查询报名活动基本信息
      * <AUTHOR> 高繁
      * @date : 2021/4/6 14:43
     */
    EnrollPromotionVO selectEnrollPromotionInfo(EnrollPromotionVO enrollPromotionVO);

    /**
      * @param enrollRuleDomain
      * @Description : 修改主信息
      * <AUTHOR> 高繁
      * @date : 2021/4/8 15:30
     */
    int updateByParams(EnrollRuleDomain enrollRuleDomain);

    /**
      * @param promotionNo
      * @Description : 删除报名活动
      * <AUTHOR> 高繁
      * @date : 2021/4/8 20:08
     */
    int updateDeleteFlag(@Param("promotionNo") String promotionNo);
}
