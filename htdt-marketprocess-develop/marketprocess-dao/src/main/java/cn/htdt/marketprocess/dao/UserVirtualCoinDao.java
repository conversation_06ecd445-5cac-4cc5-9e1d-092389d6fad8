package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.UserVirtualCoinDomain;
import cn.htdt.marketprocess.vo.ReqVirtualCoinAndLatestRecordVo;
import cn.htdt.marketprocess.vo.UserVirtualCoinVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户橙豆表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-20
 */
@Mapper
public interface UserVirtualCoinDao extends BaseMapper<UserVirtualCoinDomain> {
    /**
     * 查询用户橙豆接口
     * @param virtualCoinDomain
     * @return
     */
    List<UserVirtualCoinDomain> selectUserVirtualCoinList(UserVirtualCoinDomain virtualCoinDomain);

    void updateUserVirtualCoin(UserVirtualCoinDomain virtualCoinDomain);

    /**
     * 账户剩余橙豆数>= 0的店铺粉丝人数
     * @param storeNo 店铺编号
     * @return 粉丝数量
     */
    int getHasVirtualCoinFansCount(@Param("storeNo") String storeNo);

    /**
     * 查询店铺粉丝橙豆接口
     * @param reqVirtualCoinVo 查询参数
     * @return 结果
     */
    List<UserVirtualCoinDomain> getStoreFansVirtualCoinList(ReqVirtualCoinAndLatestRecordVo reqVirtualCoinVo);

    /**
     * 账户剩余橙豆数大于0的店铺编号
     * @param userVirtualCoinVo 店铺编号和粉丝编号集合
     * @return 店铺编号集合
     */
    List<String> selectHasVirtualCoinStoreNoList(UserVirtualCoinVo userVirtualCoinVo);

    /**
     * 20230928蛋品-赵翔宇-商家橙豆, 查询粉丝橙豆余额
     *
     * @param virtualCoinDomain 查询参数
     * @return 结果
     */
    UserVirtualCoinDomain getUserVirtualCoin(UserVirtualCoinDomain virtualCoinDomain);

    /**
     * 20230928蛋品-赵翔宇-商家橙豆, 查询商家下商家橙豆大于0的粉丝数量
     *
     * @param userVirtualCoinVo 查询参数
     * @return 粉丝数量
     */
    int getMerchantHasVirtualCoinFanCount(UserVirtualCoinVo userVirtualCoinVo);

}