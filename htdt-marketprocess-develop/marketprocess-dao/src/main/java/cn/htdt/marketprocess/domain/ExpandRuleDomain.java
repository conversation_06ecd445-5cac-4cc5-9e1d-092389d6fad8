package cn.htdt.marketprocess.domain;

import cn.htdt.common.domain.BaseDomain;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 膨胀红包规则
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("expand_rule")
public class ExpandRuleDomain extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 活动编号
     */
    private String promotionNo;

    /**
     * 粉丝初始可领取金额
     */
    private BigDecimal firstMoney;

    /**
     * 邀请好友助力次数上限
     */
    private Integer inviteHelpTimes;

    /**
     * 最高可得红包金额
     */
    private BigDecimal maxMoney;

    /**
     * 优惠券类型 （1001:满减券 1002:折扣券 1003:现金券）
     */
    private String couponType;

    /**
     * 每个红包同一个人助力次数
     */
    private Integer userHelpLimitNum;

    /**
     * 使用门槛阈值（满减时表示满多少元）
     */
    private BigDecimal discountThreshold;

    /**
     * 兑换门槛阈值（满多少元可兑换）
     */
    private BigDecimal exchangeThreshold;

    /**
     * 券面额
     */
    private BigDecimal couponValue;

    /**
     * 券标识
     */
    private String couponIdentity;

    /**
     * 券有效期（1001：长期有效 1002：指定日期 1003:自领取后天数有效）
     */
    private String couponPeriodValidity;

    /**
     * 券有效天数（只有有效期为1003时使用）
     */
    private Integer couponEffectiveDay;

    /**
     * 券开始时间
     */
    private LocalDateTime couponEffectiveTime;

    /**
     * 券结束时间
     */
    private LocalDateTime couponInvalidTime;

    /**
     * 券使用渠道 (1001:汇享购下单 1002:门店下单)
     */
    private String couponUseChannel;

    /**
     * 券使用范围(1001:店铺全场通用 1002:云池商品通用 1003:指定云池商品 1004:店铺指定类目 1005:店铺指定品牌 1006:店铺指定商品 )
     */
    private String couponUseScope;

    /**
     * 券使用说明
     */
    private String couponUseExplain;

    /**
     * 用户每日可发起次数
     */
    private Integer userDailyTimes;

    /**
     * 用户累计可发起次数
     */
    private Integer userTotalTimes;

    /**
     * 总部每日可发起次数
     */
    private Integer dailyTimes;

    /**
     * 总部累计可发起次数
     */
    private Integer totalTimes;

    /**
     * 店铺每日可发起次数
     */
    private Integer storeDailyTimes;

    /**
     * 店铺累计可发起次数
     */
    private Integer storeTotalTimes;

    /**
     * 是否可用:默认1，1：可用，其余不可用
     */
    private Integer disableFlag;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;

    //20230818蛋品-盛守武-膨胀红包改动（start）
    /**
     * 是否给助力者发放优惠券 true:发放，false:不发放
     */
    private Boolean helperCouponIssueFlag;

    /**
     * 助力者优惠门槛
     */
    private BigDecimal helperCouponThreshold;

    /**
     * 助力者优惠金额
     */
    private BigDecimal helperCouponValue;
    //20230818蛋品-盛守武-膨胀红包改动（end）
}
