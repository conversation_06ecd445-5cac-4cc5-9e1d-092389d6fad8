package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.CardCountingConfigDomain;
import cn.htdt.marketprocess.vo.CardCountingConfigVO;
import cn.htdt.marketprocess.vo.ExpandRuleVO;
import cn.htdt.marketprocess.vo.VoucherSaleAndUsedCountVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 计次卡设置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-11
 */
@Mapper
public interface CardCountingConfigDao extends BaseMapper<CardCountingConfigDomain> {

    /**
     * 查询单条记录-仅仅包括rule表信息
     * @param cardCountingConfigVO
     * @return
     */
    CardCountingConfigVO selectCardCountingConfig(CardCountingConfigVO cardCountingConfigVO);

    /**
     * 查询单条记录-仅仅包括rule表信息-包含删除的活动
     * @param cardCountingConfigVO
     * @return
     */
    CardCountingConfigVO selectCardCountingConfigInfo(CardCountingConfigVO cardCountingConfigVO);


    List<CardCountingConfigVO> getCardCountingPage(CardCountingConfigVO cardCountingConfigVO);

    /**
     *  根据 card_counting_no 更新 card_counting_config 表
     * @param cardCountingConfigDomain cardCountingConfigDomain
     * @return int
     */
    int updateByParams(CardCountingConfigDomain cardCountingConfigDomain);

    int deleteByCardCountingNo(@Param("cardCountingNo") String cardCountingNo);

    List<VoucherSaleAndUsedCountVO> selectSaleAndUsedCount(@Param("cardCountingNoList") List<String> cardCountingNoList, @Param("ingoreDeleteFlag") Integer ingoreDeleteFlag);

}
