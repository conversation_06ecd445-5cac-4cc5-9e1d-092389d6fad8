package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.SmsSendConfigDomain;
import cn.htdt.marketprocess.vo.SmsSendConfigVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 短信发送配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
@Mapper
public interface SmsSendConfigDao extends BaseMapper<SmsSendConfigDomain> {

    /**
     * 查询短信营销配置表
     * @Date 2022/11/7
     * <AUTHOR>
     * @param smsSendConfigDomain
     * @return List<SmsSendConfigDomain>
     **/
    List<SmsSendConfigVO> selectSmsSendConfigs(SmsSendConfigDomain smsSendConfigDomain);


    /**
     * 查询短信营销配置表，不关联节日
     * @Date 2023/5/27
     * <AUTHOR>
     * @param smsSendConfigDomain
     * @return List<SmsSendConfigDomain>
     **/
    List<SmsSendConfigDomain> selectByParams(SmsSendConfigDomain smsSendConfigDomain);

    /**
     * 查询短信营销配置详情
     * @Date 2022/11/7
     * <AUTHOR>
     * @param smsSendConfigDomain
     * @return SmsSendConfigDomain
     **/
    SmsSendConfigVO selectSmsSendConfigInfo(SmsSendConfigDomain smsSendConfigDomain);

    /**
     * 更新短信营销配置表
     * @Date 2022/11/7
     * <AUTHOR>
     * @param smsSendConfigDomain
     * @return void
     **/
    void updateSmsSendConfig(SmsSendConfigDomain smsSendConfigDomain);

    /**
     * 删除短信营销配置表
     * @Date 2022/11/10
     * <AUTHOR>
     * @param smsSendConfigDomain
     * @return void
     **/
    void deleteSmsSendConfigByConfigNo(SmsSendConfigDomain smsSendConfigDomain);
}
