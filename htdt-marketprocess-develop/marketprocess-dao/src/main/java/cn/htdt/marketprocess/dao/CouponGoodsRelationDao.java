package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.CouponGoodsRelationDomain;
import cn.htdt.marketprocess.vo.CouponGoodsRelationVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 优惠券商品关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-01
 */
@Mapper
public interface CouponGoodsRelationDao extends BaseMapper<CouponGoodsRelationDomain> {

    /**
     * 通过条件查询信息
     * @param couponGoodsRelationVO
     * @return
     */
    List<CouponGoodsRelationVO> selectCouponGoodsRelation(CouponGoodsRelationVO couponGoodsRelationVO);

    /**
     * 批量删除优惠券关联商品
     * @param atomCouponGoodsRelationVos
     * @return
     */
    void batchDeleteCouponGoodsRelation(@Param("couponGoodsRelationList") List<CouponGoodsRelationVO> atomCouponGoodsRelationVos);

    /**
     * 删除优惠券关联商品
     * @param couponGoodsRelationVO
     */
    void deleteCouponGoodsRelationByNo(CouponGoodsRelationVO couponGoodsRelationVO);

    /**
     * 删除优惠券关联商品
     * @param couponNo
     */
    void deleteCouponGoodsRelation(@Param("couponNo") String couponNo);

}
