package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.SmsSendRecordDomain;
import cn.htdt.marketprocess.vo.SmsSendRecordStaticVO;
import cn.htdt.marketprocess.vo.SmsSendRecordVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 短信发送记录表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2021/07/05
 **/
@Mapper
public interface SmsSendRecordDao extends BaseMapper<SmsSendRecordDomain> {

    /**
     * 根据参数查询
     *
     * @param vo 查询参数
     * @return List<SmsSendRecordDomain>
     */
    List<SmsSendRecordDomain> selectByParam(SmsSendRecordVO vo);



    List<SmsSendRecordDomain> selectSendDetailBysonBatchNoList(@Param("sonBatchNoList") List<String> sonBatchNoList);

    /**
     * 根据参数修改
     *
     * @param domain 编辑参数
     * @return int
     */
    int updateByParams(SmsSendRecordDomain domain);


    /**
     * 批量更新
     * @param smsSendRecordDomainList
     * @return
     */
    int batchUpdateSmsSendRecord(@Param("smsSendRecordDomainList") List<SmsSendRecordDomain> smsSendRecordDomainList);


    /**
     * 只更新父批次数据
     * @param smsSendRecordDomainList
     * @return
     */
    int batchUpdateSmsSendRecordByBatchNo(@Param("smsSendRecordDomainList") List<SmsSendRecordDomain> smsSendRecordDomainList);

    /**
     * 查询店铺的短信发送成功条数的总计和短信发送次数的累计
     * @param smsSendRecordDomain
     * @return
     */
    SmsSendRecordStaticVO getMessageRecordStatic(SmsSendRecordDomain smsSendRecordDomain);
}
