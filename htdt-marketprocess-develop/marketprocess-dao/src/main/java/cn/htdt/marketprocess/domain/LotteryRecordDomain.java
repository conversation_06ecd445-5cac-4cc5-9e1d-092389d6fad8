package cn.htdt.marketprocess.domain;

import cn.htdt.common.domain.BaseDomain;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 抽奖记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("lottery_record")
@ApiModel(value="LotteryRecordDomain对象", description="订单抽奖记录表")
public class LotteryRecordDomain extends BaseDomain {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "活动类型")
    private String promotionType;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "是否有效的订单抽奖(1:有效;2:无效)")
    private Integer effective;

    @ApiModelProperty(value = "是否有效的埋点数据(1:是;2:否)")
    private Integer trackingFlag;
}
