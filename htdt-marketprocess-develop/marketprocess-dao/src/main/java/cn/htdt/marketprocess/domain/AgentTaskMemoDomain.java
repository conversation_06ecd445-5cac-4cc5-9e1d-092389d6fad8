package cn.htdt.marketprocess.domain;

import cn.htdt.common.domain.BaseDomain;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 代理人任务规则说明表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("agent_task_memo")
public class AgentTaskMemoDomain extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 任务活动唯一编号
     */
    private String taskNo;

    /**
     * 任务规则说明
     */
    private byte[] taskMemo;
}