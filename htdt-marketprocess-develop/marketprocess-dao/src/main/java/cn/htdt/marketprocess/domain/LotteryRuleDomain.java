package cn.htdt.marketprocess.domain;

import cn.htdt.common.domain.BaseDomain;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <p>
 * 抽奖规则
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("lottery_rule")
@ApiModel(value="LotteryRuleDomain对象", description="抽奖规则")
public class LotteryRuleDomain extends BaseDomain {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "抽奖类型 1001:直接抽奖 1002:订单抽奖")
    private String lotteryType;

    @ApiModelProperty(value = "抽奖上架地点 1001:网店抽奖活动专区 1002:专属链接")
    private String lotteryShowLocation;

    @ApiModelProperty(value = "用户每日抽奖次数")
    private Integer userDailyDrawTimes;

    @ApiModelProperty(value = "用户每日中奖次数")
    private Integer userDailyWinningTimes;

    @ApiModelProperty(value = "用户总抽奖次数")
    private Integer userTotalDrawTimes;

    @ApiModelProperty(value = "用户总中奖次数")
    private Integer userTotalWinningTimes;

    @ApiModelProperty(value = "店铺每日中奖次数")
    private Integer storeDailyWinningTimes;

    @ApiModelProperty(value = "店铺总中奖次数")
    private Integer storeTotalWinningTimes;

    @ApiModelProperty(value = "是否有分享次数（1:否 2:是）")
    private Integer shareTimesLimitFlag;

    @ApiModelProperty(value = "每次分享获得额外抽奖次数")
    private Integer shareGainDrawTimes;

    @ApiModelProperty(value = "分享最高可获抽奖次数")
    private Integer shareMaxGainDrawTimes;

    @ApiModelProperty(value = "是否转换成商城券（1:否 2:是）")
    private Integer changePurchaseCouponFlag;

    @ApiModelProperty(value = "是否有金币抽奖次数")
    private Integer goldTimesLimitFlag;

    @ApiModelProperty(value = "每次消耗金币数")
    private Integer goldNum;

    @ApiModelProperty(value = "金币最高可获抽奖次数")
    private Integer goldMaxGainDrawTimes;

    @ApiModelProperty(value = "单笔订单满额")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "单笔订单满额获得抽奖次数")
    private Integer orderNum;

    @ApiModelProperty(value = "是否有积分抽奖次数")
    private Integer scoreTimesLimitFlag;

    @ApiModelProperty(value = "每次消耗积分数")
    private Integer scoreNum;

    @ApiModelProperty(value = "积分最高可获抽奖次数")
    private Integer scoreMaxGainDrawTimes;

    @ApiModelProperty(value = "是否可用:默认1，1：可用，其余不可用")
    private Integer disableFlag;



}
