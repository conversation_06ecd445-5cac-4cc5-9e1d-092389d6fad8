package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.AgentRewardRecordDomain;
import cn.htdt.marketprocess.vo.AgentRewardRecordFansNumVO;
import cn.htdt.marketprocess.vo.AgentRewardRecordVO;
import cn.htdt.marketprocess.vo.AgentRewardVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 代理人酬劳过程信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-11
 */
@Mapper
public interface AgentRewardRecordDao extends BaseMapper<AgentRewardRecordDomain> {

    /**
     * @Description 代理人酬劳过程信息列表
     * <AUTHOR>
     * @param agentRewardRecordVO
     * @return
     */
    List<AgentRewardRecordVO> selectAgentRewardRecordListByPage(AgentRewardRecordVO agentRewardRecordVO);

    /**
     * 每推荐个数
     * @param agentRewardRecordDomain
     * @return
     */
    List<String> countByEach(AgentRewardRecordDomain agentRewardRecordDomain);

    /**
     * 累计推荐个数 | 用于判断是不是新粉丝
     * @param agentRewardRecordDomain
     * @return
     */
    List<String> countByGrandTotal(AgentRewardRecordDomain agentRewardRecordDomain);

    /**
      * @param agentRewardRecordFansNumVO
      * @Description : 根据店铺编码+代理人编码/任务编码 查询拉新任务总数
      * <AUTHOR> 高繁
      * @date : 2021/2/20 10:53
     */
    List<AgentRewardRecordFansNumVO> selectFansNumByAgentOrTask(AgentRewardRecordFansNumVO agentRewardRecordFansNumVO);

    /**
     * 一定是跟订单有关系的
     * 单独更新粉丝操作状态
     * @param agentRewardRecordDomain
     * @return
     */
    Integer modifyFansOperateStatus(AgentRewardRecordDomain agentRewardRecordDomain);

    /**
     * @description 查询代理人任务拉新粉丝数
     * <AUTHOR>
     * @date 2021-03-11 19:38:43
     * @param agentRewardVO
     * @return
     */
    Integer selectFanTotalStatistics(AgentRewardVO agentRewardVO);

    /**
      * @param orderNo
      * @Description : 根据订单编号查询酬劳过程记录
      * <AUTHOR> 高繁
      * @date : 2021/6/23 19:16
     */
    List<AgentRewardRecordDomain> queryAgentRewardRecordListByOrder(@Param("orderNo") String orderNo);
    /**
     * @param agentRewardRecordDomain
     * @Description : 修改商品实付金额
     * <AUTHOR> 高繁
     * @date : 2021/6/24 14:51
     */
    Integer modifyGoodsPrice(AgentRewardRecordDomain agentRewardRecordDomain);
}