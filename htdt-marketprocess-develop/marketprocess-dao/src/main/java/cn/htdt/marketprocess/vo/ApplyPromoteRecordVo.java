package cn.htdt.marketprocess.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/6/8 16:28
 */
@Data
public class ApplyPromoteRecordVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录编号
     */
    @ApiModelProperty("记录编号")
    private String recordNo;

    /**
     * 会员店账号
     */
    @ApiModelProperty("会员店账号")
    private String htdAccountNo;

    /**
     * 商家编号
     */
    @ApiModelProperty("商家编号")
    private String merchantNo;

    /**
     * 任务完成状态（1：已完成）
     */
    @ApiModelProperty("任务完成状态（1：已完成）")
    private String taskStatus;

    /**
     * 活动id
     */
    @ApiModelProperty("活动id")
    private String activityId;

}
