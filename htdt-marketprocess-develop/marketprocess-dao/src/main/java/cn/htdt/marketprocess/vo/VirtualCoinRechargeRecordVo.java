package cn.htdt.marketprocess.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022-07-18
 */
@Data
public class VirtualCoinRechargeRecordVo {

    /**
     * 支付渠道,字典paymentChannelEnum
     */
    private String paymentChannel;

    /**
     * 订单笔数
     */
    private Integer orderCount;

    /**
     * 橙豆总收费
     */
    private BigDecimal totalPaymentAmount;

    /**
     * 橙豆总变动数量量
     */
    private BigDecimal totalVirtualCoinChange;

}
