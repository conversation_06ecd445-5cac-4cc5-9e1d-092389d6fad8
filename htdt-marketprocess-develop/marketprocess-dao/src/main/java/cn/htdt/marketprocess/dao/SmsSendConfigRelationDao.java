package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.SmsSendConfigRelationDomain;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 短信营销配置节日关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
@Mapper
public interface SmsSendConfigRelationDao extends BaseMapper<SmsSendConfigRelationDomain> {

    /**
     * 查询生日节日对应的时间节点
     * @Date 2022/11/11
     * <AUTHOR>
     * @param smsSendConfigRelationDomain
     * @return List<SmsSendConfigRelationDomain>
     **/
    List<SmsSendConfigRelationDomain> selectSmsSendConfigRelations(SmsSendConfigRelationDomain smsSendConfigRelationDomain);

    /**
     * 编辑短信营销配置节日关联信息
     * @Date 2022/11/10
     * <AUTHOR>
     * @param smsSendConfigRelationDomains
     * @return void
     **/
    void updateSmsSendConfigRelations(@Param("list") List<SmsSendConfigRelationDomain> smsSendConfigRelationDomains);

    /**
     * 通过编号删除记录
     * @Date 2022/11/7
     * <AUTHOR>
     * @param configNo
     * @return void
     **/
    void deleteSmsSendConfigRelation(@Param("configNo") String configNo);
}
