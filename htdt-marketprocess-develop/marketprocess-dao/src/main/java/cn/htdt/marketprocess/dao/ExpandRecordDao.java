package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.ExpandRecordDomain;
import cn.htdt.marketprocess.vo.ExpandRecordVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 膨胀红包发起记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Mapper
public interface ExpandRecordDao extends BaseMapper<ExpandRecordDomain> {

    /**
     * 查询记录list
     *
     * @param expandRecordVO
     * @return
     */
    List<ExpandRecordVO> selectExpandRecordListByParams(ExpandRecordVO expandRecordVO);

    /**
     * 查询记录-one
     *
     * @param expandRecordVO
     * @return
     */
    ExpandRecordVO selectOneExpandRecordByParams(ExpandRecordVO expandRecordVO);

    /**
     * 查询当日发起的数量
     *
     * @param expandRecordVO
     * @return
     */
    Integer selectCurrentExpandRecordCount(ExpandRecordVO expandRecordVO);

    /**
     * 查询某活动累计发起的数量
     *
     * @param expandRecordVO
     * @return
     */
    Integer selectExpandRecordTotalCount(ExpandRecordVO expandRecordVO);

    /**
     * 查询某活动累计领取数量和使用总量
     */
    ExpandRecordVO selectExpandRecordUseCount(ExpandRecordVO expandRecordVO);

    /**
     * 更新-兑换、可用变更用
     *
     * @param expandRecordVO
     * @return
     */
    void updateExpandRecordByParams(ExpandRecordVO expandRecordVO);

    /**
     * 更新-膨胀金额变更用
     *
     * @param expandRecordVO
     * @return
     */
    void updateMoneyExpandRecordByParams(ExpandRecordVO expandRecordVO);

}
