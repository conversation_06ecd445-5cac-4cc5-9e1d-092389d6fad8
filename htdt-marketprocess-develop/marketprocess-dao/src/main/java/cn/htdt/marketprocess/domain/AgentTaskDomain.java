package cn.htdt.marketprocess.domain;

import cn.htdt.common.domain.BaseDomain;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <p>
 * 代理人任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("agent_task")
public class AgentTaskDomain extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 任务活动唯一编号
     */
    private String taskNo;

    /**
     * 任务活动类型，0001:拉新任务
     */
    private String taskType;

    /**
     * 参与会员店类型，1：部分店铺 2：全部店铺
     */
    private Integer taskStoreType;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务图片地址
     */
    private String taskImageUrl;

    /**
     * 任务开始时间
     */
    private LocalDate taskStartTime;

    /**
     * 任务结束时间
     */
    private LocalDate taskEndTime;

    /**
     * 总共酬劳上限-佣金
     */
    private BigDecimal totalYj;

    /**
     * 总共酬劳上限-汇金币
     */
    private BigDecimal totalHjb;

    /**
     * 每个代理人酬劳上限-佣金
     */
    private BigDecimal totalAgentYj;

    /**
     * 每个代理人酬劳上限-汇金币
     */
    private BigDecimal totalAgentHjb;

    /**
     * 前台展示酬劳
     */
    private String showMessage;

    /**
     * 任务上下架标识，1:未发布 2:上架 3:下架
     */
    private Integer statusFlag;

    /**
     * 任务复制标识，0:正常 1：复制
     */
    private Integer copyFlag;

}