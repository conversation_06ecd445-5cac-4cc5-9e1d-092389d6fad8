package cn.htdt.marketprocess.vo;

import cn.htdt.marketprocess.domain.GoodsPromotionGoodsRelationDomain;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Data
public class AtomGoodsPromotionGoodsRelationVo extends GoodsPromotionGoodsRelationDomain {

    private static final long serialVersionUID = -3779977151511914801L;

    /**
     * 活动编号集合
     */
    private List<String> promotionNoList;

    /**
     * 来源 1001：平台 1002：商家 1003：店铺")
     */
    private String sourceType;

    /**
     * 购买商品数
     */
    private Integer buyGoodsNum;

    /**
     * 每场开始时间字符串，格式为HH:mm:ss
     */
    private LocalTime startTime;

    /**
     * 每场结束时间字符串，格式为HH:mm:ss
     */
    private LocalTime endTime;

    /**
     * 查询子品标识：1-非子品、2-子品
     */
    private Integer childFlag;

    /**
     * 活动名称
     */
    private String promotionName;

    /**
     * 促销类型，2001:秒杀
     */
    private String promotionType;

    /**
     * 活动有效期的开始时间
     */
    private LocalDateTime effectiveTime;

    /**
     * 活动有效期的结束时间
     */
    private LocalDateTime invalidTime;

    /**
     * 商品编码集合
     */
    private List<String> goodsNoList;

    /**
     * 系列商品主品商品编号，非系列商品商品编号 集合
     */
    private List<String>  parentGoodsNoList;

    /**
     * 店铺编号
     */
    private String storeNo;

    /**
     * 活动状态，1000:草稿 1001:活动未开始 1002:活动进行中，1003:活动已结束
     */
    private String status;

    /**
     * 单店总限购数
     */
    private Integer storeTotalNum;

    /**
     * 个人总限购数
     */
    private Integer limitBuyTotalNum;

    /**
     * 活动对象 1001: 不限制 1002: 限制老用户 1003: 限制新用户
     */
    private String userScope;

    /**
     * 查询类型：1-当前活动和其他活动互斥；2-非当前活动的其他活动互斥；3-时间有重合的互斥
     */
    private Integer queryType;

    /**
     * 活动关联的商品数
     */
    private Integer goodsNum;


    /**
     * 限时购活动的多场次时间段数据
     */
    List<AtomLimitTimePeriodVo> periodList;

    /**
     * 活动场次编码集合
     */
    List<String> periodNoList;

    /**
     * 价格变化标签 1：大转小 2：小转大
     */
    private Integer priceChangeFlag;

    /**
     * 优惠折扣(满减时是金额，满折时是折扣)
     */
    private BigDecimal discountMoney;

}
