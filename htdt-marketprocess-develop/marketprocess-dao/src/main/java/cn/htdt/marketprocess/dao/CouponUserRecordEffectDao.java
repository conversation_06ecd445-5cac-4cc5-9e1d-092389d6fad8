package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.vo.CouponUserRecordStaticVO;
import cn.htdt.marketprocess.vo.ReqMarketTouchingEffectVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 优惠券权益触达Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-01-05
 */
@Mapper
public interface CouponUserRecordEffectDao extends BaseMapper {

    /**
     * 店铺下领券粉丝数和用券粉丝数
     * @param reqMarketTouchingEffectVO 查询参数
     * @return 店铺的领券粉丝数和用券粉丝数
     */
    CouponUserRecordStaticVO getStoreCouponStaticByParam(ReqMarketTouchingEffectVO reqMarketTouchingEffectVO);
}
