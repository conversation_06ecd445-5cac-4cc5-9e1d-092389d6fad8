package cn.htdt.marketprocess.domain;

import cn.htdt.common.domain.BaseDomain;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 短信流量表
 *
 * <AUTHOR>
 * @since 2021-07-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sms_traffic")
public class SmsTrafficDomain extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 短信剩余条数
     */
    private Long smsRemainingNum;

    /**
     * 发送成功条数
     */
    private Long sendSuccessNum;

    /**
     * 发送成功次数
     */
    private Long sendSuccessTimes;

    /**
     * 短信发送总次数
     */
    private Long smsSendingTimes;

    /**
     * 是否可用:默认2，1：不可用2:可用
     */
    private Integer disableFlag;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;

}
