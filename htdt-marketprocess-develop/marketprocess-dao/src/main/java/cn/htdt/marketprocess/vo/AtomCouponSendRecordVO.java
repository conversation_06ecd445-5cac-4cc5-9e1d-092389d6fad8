package cn.htdt.marketprocess.vo;

import cn.htdt.marketprocess.domain.CouponSendRecordDomain;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 优惠券发送记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CouponSendRecordDomain对象", description="优惠券发送记录表")
public class AtomCouponSendRecordVO  extends CouponSendRecordDomain {

    private static final long serialVersionUID = 1L;


}
