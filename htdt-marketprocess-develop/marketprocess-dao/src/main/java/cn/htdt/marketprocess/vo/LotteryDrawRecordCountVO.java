package cn.htdt.marketprocess.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 活动中奖记录统计VO
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="LotteryDrawRecordCountVO对象", description="活动中奖记录统计VO")
public class LotteryDrawRecordCountVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "奖品编号")
    private String rewardNo;

    @ApiModelProperty(value = "奖品类型（1001:实物礼品 1002:优惠券 1003:话费券 1004:话费 1005:汇金币 1006:积分  9999:谢谢惠顾）")
    private String rewardType;

    @ApiModelProperty(value = "奖品名称")
    private String rewardName;

    @ApiModelProperty(value = "奖品总中奖数量")
    private Integer recordCountNum;
}
