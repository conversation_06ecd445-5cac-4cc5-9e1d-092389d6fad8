package cn.htdt.marketprocess.vo;

import cn.htdt.marketprocess.domain.PointsGiftConvertRecordDomain;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class PointsGiftConvertRecordVO extends PointsGiftConvertRecordDomain implements Serializable {
    private static final long serialVersionUID = 1L;
    private String giftPictureUrl;
    private String namePhone;
    private String searchCondition;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
}
