package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.UserPointsDomain;
import cn.htdt.marketprocess.vo.SharingStoreUserPointsVO;
import cn.htdt.marketprocess.vo.UserPointsVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户积分表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-26
 */
@Mapper
public interface UserPointsDao extends BaseMapper<UserPointsDomain> {
    List<UserPointsVO> selectUserPoints(UserPointsVO userPointsVO);

    int updateUserPoints(UserPointsVO userPointsVO);

    void batchUpdateUserPoints(@Param("userPointsVOS") List<UserPointsVO> userPointsVOS);

    UserPointsVO selectUserConvert(UserPointsVO userPointsVO);

    UserPointsVO selectStoreTotalPoints(UserPointsVO userPointsVO);

    int seletMerchantRemainPoints(@Param("merchantNo")String merchantNo);

    int selectFansRemainPoints(@Param("fansNo") String fansNo);

    /**
     * 账户剩余橙豆数>= 0的店铺粉丝编号
     * @param storeNo 店铺编号
     * @return 粉丝编号集合
     */
    List<String> selectHasPointsFansCount(@Param("storeNo") String storeNo);

    /**
     * 账户剩余积分数不等于 0的店铺编号
     * @param userPointsVO 查询参数
     * @return 店铺编号集合
     */
    List<String> selectHasPointsStoreNoList(UserPointsVO userPointsVO);

    /**
     * 20230928蛋品-wh-门店-查询共享商家下所有门店粉丝积分大于0的信息
     * @param sharingStoreUserPointsVO
     * @return
     */
    List<UserPointsVO> selectSharingStoreUserPoints(SharingStoreUserPointsVO sharingStoreUserPointsVO);

    /**
     * 20230928蛋品-wh-门店-查询共享店铺下根据店铺编号和粉丝编号查询粉丝积分(单个)
     * @param userPointsVO
     * @return
     */
    UserPointsVO selectMerchantNoUserPoints(UserPointsVO userPointsVO);

    /**
     * 20230928蛋品-wh-门店-共享店铺下扣减积分是针对，门店下粉丝编号全部扣减
     * @param userPointsVO
     * @return
     */
    int updateMerchantNoUserPoints(UserPointsVO userPointsVO);

    /**
     * 20230928蛋品-wh-商家-商家批量导入用户积分，根据商家编号修改积分信息
     * @param userPointsVOS
     */
    void batchUpdateMemberUserPoints(@Param("userPointsVOS") List<UserPointsVO> userPointsVOS);

    /**
     * 20230928蛋品-赵翔宇-商家积分, 查询商家下商家积分不等于0的粉丝数量
     *
     * @param userPointsVO 查询参数
     * @return 粉丝数量
     */
    int getMerchantHasPointsFanCount(UserPointsVO userPointsVO);
}
