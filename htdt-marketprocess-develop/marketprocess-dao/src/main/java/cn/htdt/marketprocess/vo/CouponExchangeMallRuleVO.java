package cn.htdt.marketprocess.vo;

import cn.htdt.marketprocess.domain.CouponExchangeMallRuleDomain;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <p>
 * 优惠券转商城券规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CouponExchangeMallRuleVO对象", description="优惠券转商城券规则表")
public class CouponExchangeMallRuleVO extends CouponExchangeMallRuleDomain {

    private static final long serialVersionUID = 1L;

    private List<String> promotionNoList;

}
