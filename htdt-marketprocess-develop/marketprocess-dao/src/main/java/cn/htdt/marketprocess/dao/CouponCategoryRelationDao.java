package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.CouponCategoryRelationDomain;
import cn.htdt.marketprocess.vo.CouponCategoryRelationVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 优惠券类目关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-01
 */
@Mapper
public interface CouponCategoryRelationDao extends BaseMapper<CouponCategoryRelationDomain> {

    /**
      * @param couponCategoryRelationDomain
      * @Description : 根据参数优惠券配置的品类
      * <AUTHOR> 高繁
      * @date : 2021/4/28 19:55
     */
    List<CouponCategoryRelationDomain> selectCouponCategoryRelation(CouponCategoryRelationVO couponCategoryRelationDomain);

    /**
     * 批量删除优惠券分类活动
     *
     * @param categoryNos
     * @param couponNo
     */
    void batchDeleteCouponCategoryRelation(@Param("categoryNos") List<String> categoryNos, @Param("couponNo") String couponNo);
}
