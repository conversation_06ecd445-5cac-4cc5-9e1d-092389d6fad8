package cn.htdt.marketprocess.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * <p>
 * 优惠券用户接收记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-01
 */
@Data
public class VoucherSaleAndUsedCountVO {
    /**
     * 活动编号
     */
    private String promotionNo;

    /**
     * 已售数量(总数，包含已退款的)
     */
    private Integer salesVolumeTotal;

    /**
     * 已售数量(去除已退款的)
     */
    private Integer salesVolume;

    /**
     * 使用量
     */
    private Integer usedCount;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
