package cn.htdt.marketprocess.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigInteger;
import java.time.LocalDateTime;

/**
 * <p>
 * 小程序消息与事件接收表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("applet_msg_title")
public class AppletMsgTitleDomain implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 通知类型:1010 活动预约-秒杀 1011 活动预约-限时购 1012 活动预约-预售 1020 未支付提醒 1030 支出成功提醒 1040 订单发货提醒 1050 申请退款审核失败提醒 1060 退款成功提醒 1070 退款失败提醒 1080 分销佣金提醒
     */
    private String noticeType;

    /**
     * 模板id
     */
    private String titleId;

    /**
     * 模板标题
     */
    private String title;
    
    /**
     * 主键ID
     * 自增
     */
    @TableId(type = IdType.AUTO)
    private BigInteger id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(update = "now()")
    private LocalDateTime modifyTime;

    /**
     * 数据逻辑删除标识，默认1未删除，其余已删除
     */
    private Integer deleteFlag = 1;


}
