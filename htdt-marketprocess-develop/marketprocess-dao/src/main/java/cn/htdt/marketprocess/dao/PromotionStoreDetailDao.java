package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.PromotionStoreDetailDomain;
import cn.htdt.marketcenter.dto.request.AtomReqPromotionStoreDetailDTO;
import cn.htdt.marketprocess.vo.PromotionStoreDetailVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 活动店铺详情表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-08
 */
@Mapper
public interface PromotionStoreDetailDao extends BaseMapper<PromotionStoreDetailDomain> {

    /**
     * 通过ruleNo查询信息list
     * @param promotionStoreDetailVo
     * @return
     */
    List<PromotionStoreDetailVo> selectPromotionStoreDetailList(PromotionStoreDetailVo promotionStoreDetailVo);

    /**
     * 通过ruleNo查询信息count
     * @param dto
     * @return
     */
    Integer selectPromotionStoreDetailCount(AtomReqPromotionStoreDetailDTO dto);

    /**
     * 删除活动店铺详情
     * @param promotionNo
     * @return
     */
    int updateDeleteFlag(@Param("promotionNo") String promotionNo);

    /**
     * 删除活动店铺详情
     * @param promotionStoreDetailVo
     * @return
     */
    int delPromotionStoreDetail(PromotionStoreDetailVo promotionStoreDetailVo);

    /**
     * 报名活动参与店铺数查询
     * @param promotionNoList
     * @return
     */
    List<PromotionStoreDetailVo> selectPromotionEnrollShopNumList(@Param("promotionNoList") List<String> promotionNoList);

}
