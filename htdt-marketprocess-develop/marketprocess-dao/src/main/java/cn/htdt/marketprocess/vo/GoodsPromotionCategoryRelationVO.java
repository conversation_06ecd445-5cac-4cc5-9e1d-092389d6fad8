package cn.htdt.marketprocess.vo;

import cn.htdt.marketprocess.domain.GoodsPromotionCategoryRelationDomain;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 促销活动类目关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
public class GoodsPromotionCategoryRelationVO extends GoodsPromotionCategoryRelationDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 活动编码集合
     */
    List<String> promotionNoList;

}