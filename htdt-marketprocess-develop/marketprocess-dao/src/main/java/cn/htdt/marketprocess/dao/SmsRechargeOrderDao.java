package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.SmsRechargeOrderDomain;
import cn.htdt.marketprocess.vo.SmsRechargeOrderVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-07-01
 * @description 短信充值订单mapper
 **/
@Mapper
public interface SmsRechargeOrderDao extends BaseMapper<SmsRechargeOrderDomain> {

    /**
     * @param smsRechargeOrderDomain
     * @return List<SmsRechargeOrderDomain>
     * @description 根据参数查询
     * <AUTHOR>
     * @date 2021-07-01 09:46:52
     */
    List<SmsRechargeOrderDomain> selectByParam(SmsRechargeOrderVO smsRechargeOrderDomain);

    /**
     * @description 短信充值月度汇总
     * <AUTHOR>
     * @date 2021-12-21 17:12:24
     * @param smsRechargeOrderVO
     * @return
     */
    List<SmsRechargeOrderVO> selectSmsRechargeOrderMonthCount(SmsRechargeOrderVO smsRechargeOrderVO);

    /**
     * 根据参数修改
     *
     * @param smsRechargeOrderDomain 请求参数
     * @return int
     * <AUTHOR>
     */
    int updateByParams(SmsRechargeOrderDomain smsRechargeOrderDomain);

}
