package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.LotteryRewardGoodsRelationDomain;
import cn.htdt.marketprocess.vo.LotteryRewardGoodsRelationVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 抽奖奖品商品关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-08
 */
@Mapper
public interface LotteryRewardGoodsRelationDao extends BaseMapper<LotteryRewardGoodsRelationDomain> {

    /**
     * 通过奖品编码查询信息
     * @param lotteryRewardGoodsRelationVo
     * @return
     */
    LotteryRewardGoodsRelationVo selectLotteryRewardGoodsRelation(LotteryRewardGoodsRelationVo lotteryRewardGoodsRelationVo);

    /**
     * 通过奖品编码查询信息-多条
     * @param lotteryRewardGoodsRelationVo
     * @return
     */
    List<LotteryRewardGoodsRelationVo> selectLotteryRewardGoodsRelations(LotteryRewardGoodsRelationVo lotteryRewardGoodsRelationVo);

    /**
     * 通过奖品编码查询信息list
     * @param lotteryRewardGoodsRelationVo
     * @return
     */
    List<LotteryRewardGoodsRelationVo> selectLotteryRewardGoodsRelationList(LotteryRewardGoodsRelationVo lotteryRewardGoodsRelationVo);


    /**
     * 批量删除活动编码查询信息
     * @param lotteryRewardInfoVo
     * @return
     */
    void batchDeleteLotteryRewardGoodsRelation(LotteryRewardGoodsRelationVo lotteryRewardInfoVo);

    /**
     * 批量逻辑删除活动编码查询信息
     * @param lotteryRewardInfoVo
     * @return
     */
    void batchUpdateLotteryRewardGoodsRelation(LotteryRewardGoodsRelationVo lotteryRewardInfoVo);

}
