package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.PromotionBrowsingRecordDomain;
import cn.htdt.marketprocess.vo.PromotionInfoVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 活动浏览记录表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-05-18
 */
@Mapper
public interface PromotionBrowsingRecordDao extends BaseMapper<PromotionBrowsingRecordDomain> {

    /**
     * 查询浏览促销活动的粉丝数
     *
     * @param promotionInfoVO
     * @return
     */
    int selectBrowseFansCount(PromotionInfoVO promotionInfoVO);

    /**
     * 保存粉丝活动浏览记录，如活动、粉丝编号、日期重复则浏览数加1
     *
     * @param domain 请求参数
     * @return int
     */
    int savePromotionBrowsingRecord(PromotionBrowsingRecordDomain domain);

    /**
     * 查询促销活动浏览粉丝数
     *
     * @param promotionInfoVO 请求参数
     * @return List<PromotionBrowsingRecordDomain>
     */
    List<PromotionBrowsingRecordDomain> selectPromotionBrowseRecordCount(PromotionInfoVO promotionInfoVO);

}
