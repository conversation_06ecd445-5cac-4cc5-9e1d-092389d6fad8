package cn.htdt.marketprocess.vo;

import cn.htdt.marketprocess.domain.PromotionVirtualGoodsRelationDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <p>
 * 促销活动虚拟商品关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PromotionVirtualGoodsRelationVo extends PromotionVirtualGoodsRelationDomain {

    /**
     * 促销编码
     */
    private List<String> promotionNoList;

    /**
     * 商品编码
     */
    private List<String> goodsNoList;


}
