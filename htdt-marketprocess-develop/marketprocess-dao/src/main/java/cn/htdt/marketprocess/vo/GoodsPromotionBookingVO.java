package cn.htdt.marketprocess.vo;

import cn.htdt.marketprocess.domain.GoodsPromotionBookingDomain;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * <p>
 * 商品活动预约表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "GoodsPromotionBookingVO对象", description = "商品活动预约表")
public class GoodsPromotionBookingVO extends GoodsPromotionBookingDomain {
    private static final long serialVersionUID = 1L;
    /*
     * 秒杀活动每日开始时间
     * */
    private LocalTime startTime;

    /*
     * 活动开始时间
     * */
    private LocalDateTime effectiveTime;

    /**
     * 预约状态集合
     */
    private List<String> statusList;
}