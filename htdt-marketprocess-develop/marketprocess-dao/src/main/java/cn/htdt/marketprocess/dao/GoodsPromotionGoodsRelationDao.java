package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.GoodsPromotionGoodsRelationDomain;
import cn.htdt.marketcenter.dto.request.AtomReqGoodsPromotionGoodsRelationDTO;
import cn.htdt.marketprocess.dto.request.ReqPromotionQueryDTO;
import cn.htdt.marketprocess.vo.AtomGoodsPromotionGoodsRelationVo;
import cn.htdt.marketprocess.vo.AtomPromotionGoodsInfoVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 商品促销活动与商品关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-22
 */
@Mapper
public interface GoodsPromotionGoodsRelationDao extends BaseMapper<GoodsPromotionGoodsRelationDomain> {

    /**
     * 根据促销活动编号，删除关联的参与商品数据
     *
     * @param promotionNo
     * @return
     * <AUTHOR>
     * @date 2021-06-29
     */
    void deletePromotionGoodsRelation(String promotionNo);

    /**
     * 查询条件查询促销商品列表
     *
     * @param vo
     * @return
     */
    List<AtomGoodsPromotionGoodsRelationVo> selectListByParam(AtomGoodsPromotionGoodsRelationVo vo);

    /**
     * 查询秒杀商品以及对应的活动规则配置信息
     *
     * @param vo
     * @return
     * <AUTHOR>
     * @date 2021-08-02
     */
    AtomGoodsPromotionGoodsRelationVo selectGoodsAndRule(AtomGoodsPromotionGoodsRelationVo vo);

    /**
     * @param vo
     * @Description : 促销商品库存扣减
     * <AUTHOR> 高繁
     * @date : 2021/7/7 10:41
     */
    void updatePromotionGoodsStock(AtomGoodsPromotionGoodsRelationVo vo);

    /**
     * 查询不可选择的活动互斥商品编码
     *
     * @param vo
     * @return
     * <AUTHOR>
     * @date 2021-07-07
     */
    List<String> getNoChoiceGoodsNoList(AtomGoodsPromotionGoodsRelationVo vo);

    /**
     * 查询活动场次下的商品哪些是互斥的商品
     *
     * @param vo 查询参数
     * <AUTHOR>
     * @date 2021-11-29
     */
    List<String> selectPromotionUnAvailableGoods(AtomGoodsPromotionGoodsRelationVo vo);

    /**
     * 促销活动，各场次批量保存场次下的商品时，排除非当前活动互斥的商品
     *
     * @param vo 请求参数
     * @return
     * <AUTHOR>
     * @date 2021-11-25
     */
    List<AtomGoodsPromotionGoodsRelationVo> selectNoChoiceGoodsNosForBatchPeriod(AtomGoodsPromotionGoodsRelationVo vo);

    /**
     * 查询促销活动的商品活动价以及对应的活动信息
     *
     * @param vo
     * @return
     * <AUTHOR>
     * @date 2021-07-13
     */
    AtomGoodsPromotionGoodsRelationVo getPromotionAndGoodsInfo(AtomGoodsPromotionGoodsRelationVo vo);

    /**
     * 删除指定活动下指定时间段内的商品
     *
     * @param goodsRelationDTOList
     * <AUTHOR>
     * @date 2021-07-20
     */
    void deletePromotionGoodsNoRelation(@Param("goodsRelationDTOList") List<AtomReqGoodsPromotionGoodsRelationDTO> goodsRelationDTOList);

    /**
     * @param goodsPromotionGoodsRelationDomain
     * @Description : 根据活动编号，商品编号查询活动商品配置信息
     * <AUTHOR> 高繁
     * @date : 2021/7/28 15:22
     */
    GoodsPromotionGoodsRelationDomain getPromotionGoodsNoRelation(GoodsPromotionGoodsRelationDomain goodsPromotionGoodsRelationDomain);

    /**
     * 删除促销活动关联的参与商品数据
     *
     * @param vo
     * @return
     * <AUTHOR>
     * @date 2021-09-23
     */
    void deletePromotionGoodsByNo(AtomGoodsPromotionGoodsRelationVo vo);

    /**
     * 获取活动下关联的商品数
     *
     * @param vo 查询参数
     * @return 活动下关联的商品数
     * <AUTHOR>
     * @date 2021-10-12
     */
    List<AtomGoodsPromotionGoodsRelationVo> selectPromotionGoodsNum(AtomGoodsPromotionGoodsRelationVo vo);

    /**
     * 查询商品参与的活动基本信息（普通商详页）
     *
     * @param domain 查询参数
     * <AUTHOR>
     * @date 2021-11-23
     */
    List<AtomGoodsPromotionGoodsRelationVo> selectGoodsPromotionInfo(GoodsPromotionGoodsRelationDomain domain);

    /**
     * 查询指定活动场次下设置的商品数据
     *
     * @param vo 查询参数
     * <AUTHOR>
     * @date 2021-12-08
     */
    List<GoodsPromotionGoodsRelationDomain> selectPromotionPeriodGoodsList(AtomGoodsPromotionGoodsRelationVo vo);

    /**
     * 查询指定活动场次下设置的商品数据
     *
     * @param vo 查询参数
     * <AUTHOR>
     * @date 2021-12-08
     */
    List<GoodsPromotionGoodsRelationDomain> selectPromotionPeriodGoodsListgoodsNo(AtomGoodsPromotionGoodsRelationVo vo);


    List<AtomPromotionGoodsInfoVo> selectActiveVipPriceByGoodsNos(@Param("goodsNos") List<String> goodsNos);

    /**
     * 查询关联商品的会员价活动
     * @param reqDTO
     * @return
     */
    List<AtomPromotionGoodsInfoVo> selectActiveMemberPriceByGoodsNos(ReqPromotionQueryDTO reqDTO);

    /**
     * 查询当前生效的折扣最低的全场通用的会员价活动
     * @return
     */
    AtomPromotionGoodsInfoVo selectMinActiveMemberPriceAllGoods(ReqPromotionQueryDTO storeNo);

    /**
     * 因千橙掌柜收银改价单位换算（斤与公斤），修改特惠促销活动商品价格
     *
     * @param vo
     */
    void updatePromotionGoodsVipPrice(AtomGoodsPromotionGoodsRelationVo vo);

    /**
     *  根据ID 修改价格
     *
     * @param vo
     */
    void updatePromotionGoodsRelationPeic(AtomGoodsPromotionGoodsRelationVo vo);

    /**
     * 更新活动价格、活动库存、个人限购数设置
     * @param vo AtomGoodsPromotionGoodsRelationVo
     */
    void updatePromotionGoodsRelation(AtomGoodsPromotionGoodsRelationVo vo);

    Integer checkActivePromotionGoodsRelation(AtomGoodsPromotionGoodsRelationVo vo);

}
