package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.CardCountingFansUseRecordDomain;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 计次卡粉丝使用记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-29
 */
@Mapper
public interface CardCountingFansUseRecordDao extends BaseMapper<CardCountingFansUseRecordDomain> {

    /**
     * 根据条件查询
     *
     * @param domain 查询参数
     * @return List<CardCountingFansUseRecordDomain>
     */
    List<CardCountingFansUseRecordDomain> selectByParam(CardCountingFansUseRecordDomain domain);

//    /**
//     * 获取使用记录统计数据
//     *
//     * @param cardCountingFansUseRecordVO 查询参数
//     * @return CardCountingFansUseRecordVO
//     */
//    CardCountingFansUseRecordVO selectFansUseRecordStatisticsByParam(CardCountingFansUseRecordVO cardCountingFansUseRecordVO);

}
