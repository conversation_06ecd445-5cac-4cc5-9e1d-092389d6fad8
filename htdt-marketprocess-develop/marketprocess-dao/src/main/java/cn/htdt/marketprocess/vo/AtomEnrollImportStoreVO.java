package cn.htdt.marketprocess.vo;

import cn.htdt.marketprocess.domain.EnrollImportStoreDomain;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 报名导入店铺记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-22
 */
@Data
public class AtomEnrollImportStoreVO extends EnrollImportStoreDomain implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 门店编号，对应orgId
     */
    private List<String> storeNos;
}
