package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.CouponStoreCategoryRelationDomain;
import cn.htdt.marketprocess.vo.CouponCategoryRelationVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 优惠券类目关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-29
 */
@Mapper
public interface CouponStoreCategoryRelationDao extends BaseMapper<CouponStoreCategoryRelationDomain> {

    /**
     * @param couponCategoryRelationVO
     * @Description : 根据参数优惠券配置的店铺类目
     * <AUTHOR> lixiang
     * @date : 2022-09-29
     */
    List<CouponStoreCategoryRelationDomain> selectCouponStoreCategoryRelation(CouponCategoryRelationVO couponCategoryRelationVO);

    /**
     * 批量删除优惠券分类活动
     *
     * @param categoryNos
     * @param couponNo
     */
    void batchDeleteCouponStoreCategoryRelation(@Param("categoryNos") List<String> categoryNos, @Param("couponNo") String couponNo);
}
