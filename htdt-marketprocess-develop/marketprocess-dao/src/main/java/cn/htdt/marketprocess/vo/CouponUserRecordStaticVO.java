package cn.htdt.marketprocess.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 券类权益触达效果
 *
 * <AUTHOR>
 * @since 2022-12-16
 */
@Data
@ApiModel(value = "CouponUserRecordStaticVO对象", description = "优惠券用户接收记录表")
public class CouponUserRecordStaticVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "发放券总数")
    private Integer provideCouponTotal;

    @ApiModelProperty(value = "已使用券总数")
    private Integer useCouponTotal;

    @ApiModelProperty(value = "券使用率")
    private String couponUseRate;

    @ApiModelProperty(value = "领券粉丝数")
    private Integer collectCouponFansNum;

    @ApiModelProperty(value = "用券粉丝数")
    private Integer useCouponFansNum;

    @ApiModelProperty(value = "店铺编码")
    private String storeNo;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "活动类型")
    private String promotionType;

    @ApiModelProperty(value = "活动名称")
    private String promotionName;

    @ApiModelProperty(value = "活动开始时间")
    private LocalDateTime effectiveTime;

    @ApiModelProperty(value = "活动结束时间")
    private LocalDateTime invalidTime;

    /**
     * 活动有效期（1001：长期有效 1002：指定日期）
     */
    private String periodValidity;

    /**
     * 优惠券 编号
     */
    private String couponNo;

    /**
     * 优惠券 编号
     */
    private String promotionNo;

    @ApiModelProperty(value = "用券粉丝率")
    private String useCouponFansRate;

    @ApiModelProperty(value = "触达粉丝率")
    private String couponReachNum;

    private Integer couponEffectiveDay;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
