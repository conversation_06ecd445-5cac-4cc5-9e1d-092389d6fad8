package cn.htdt.marketprocess.domain;

import cn.htdt.common.domain.BaseDomain;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 优惠券配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("coupon_setting")
@ApiModel(value="CouponSettingDomain对象", description="优惠券配置表")
public class CouponSettingDomain extends BaseDomain {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "促销编码")
    private String promotionNo;

    @ApiModelProperty(value = "促销类型 3001:平台-手动发代理人券 3002::平台-手动发粉丝券 3003:平台-网店代理人领券 3004:平台-网店粉丝领券 3005:店铺-手动发粉丝券 3006:店铺-网店粉丝领券 3007:膨胀红包 3008:商家-手动发粉丝券 3009:商家-网店粉丝领券 3100:抽奖券")
    private String promotionCouponType;

    @ApiModelProperty(value = "券编号")
    private String couponNo;

    @ApiModelProperty(value = "活动名称")
    private String couponName;

    @ApiModelProperty(value = "优惠券类型 （1001:满减券 1002:折扣券 1003:现金券）")
    private String couponType;

    @ApiModelProperty(value = "使用门槛阈值（满减时表示满多少元）")
    private BigDecimal discountThreshold;

    //20230928蛋品-吴鑫鑫-优惠券增加折扣-优惠券活动创建
    @ApiModelProperty(value = "券面额或者折扣")
    private BigDecimal couponValue;

    @ApiModelProperty(value = "券标识")
    private String couponIdentity;

    @ApiModelProperty(value = "券有效期（1001：长期有效 1002：指定日期 1003:自领取后天数有效）")
    private String couponPeriodValidity;

    @ApiModelProperty(value = "券有效天数（只有有效期为1003时使用）")
    private Integer couponEffectiveDay;

    @ApiModelProperty(value = "券开始时间")
    private LocalDateTime couponEffectiveTime;

    @ApiModelProperty(value = "券结束时间")
    private LocalDateTime couponInvalidTime;

    @ApiModelProperty(value = "券使用渠道 (1001:汇享购下单 1002:门店下单)")
    private String couponUseChannel;

    @ApiModelProperty(value = "券使用范围(1001:店铺全场通用 1002:云池商品通用 1003:指定云池商品 1004:店铺指定类目 1005:店铺指定品牌 1006:店铺指定商品 )")
    private String couponUseScope;

    @ApiModelProperty(value = "券使用说明")
    private String couponUseExplain;

    @ApiModelProperty(value = "是否支持代理人使用(1:否 2:是)")
    private Integer agentLimitFlag;

    @ApiModelProperty(value = "是否转换成商城券（1:否 2:是）")
    private Integer changePurchaseCouponFlag;

    @ApiModelProperty(value = "单个用户总共可获得张数")
    private Integer userTotalNum;

    @ApiModelProperty(value = "单个用户每日可获得张数")
    private Integer userDailyNum;

    @ApiModelProperty(value = "店铺用户每日可获得张数")
    private Integer storeDailyNum;

    @ApiModelProperty(value = "店铺用户总共可获得张数")
    private Integer storeTotalNum;

    @ApiModelProperty(value = "是否限制发行量（1:否 2:是）")
    private Integer circulationLimitFlag;

    @ApiModelProperty(value = "总发行量")
    private Integer totalNum;

    @ApiModelProperty(value = "剩余张数")
    private Integer remainNum;

    @ApiModelProperty(value = "是否可用:默认1，1：可用，其余不可用")
    private Integer disableFlag;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "门店编号，对应orgId")
    private String storeNo;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    /**
     * 售价
     */
    private BigDecimal salePrice;
}
