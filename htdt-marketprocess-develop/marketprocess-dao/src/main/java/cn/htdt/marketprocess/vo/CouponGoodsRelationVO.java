package cn.htdt.marketprocess.vo;

import cn.htdt.marketprocess.domain.CouponGoodsRelationDomain;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <p>
 * 优惠券商品关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CouponGoodsRelationVO对象", description="优惠券商品关联表")
public class CouponGoodsRelationVO extends CouponGoodsRelationDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 批量优惠券编码
     */
    private List<String> couponNoList;

}
