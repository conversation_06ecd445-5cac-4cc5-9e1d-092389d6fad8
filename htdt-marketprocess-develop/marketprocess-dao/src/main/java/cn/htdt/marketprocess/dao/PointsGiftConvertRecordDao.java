package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.PointsGiftConvertRecordDomain;
import cn.htdt.marketprocess.vo.PointsGiftConvertRecordVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 积分礼品兑换表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-26
 */
@Mapper
public interface PointsGiftConvertRecordDao extends BaseMapper<PointsGiftConvertRecordDomain> {
    List<PointsGiftConvertRecordVO> selectConvertList(PointsGiftConvertRecordVO recordVO);

    List<PointsGiftConvertRecordVO> selectConvertListWithPictureUrl(PointsGiftConvertRecordVO recordVO);

}
