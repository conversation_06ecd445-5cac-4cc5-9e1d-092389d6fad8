package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.vo.ReqMarketTouchingEffectVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 用户橙豆权益触达 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-01-05
 */
@Mapper
public interface VirtualCoinRecordEffectDao extends BaseMapper {
    /**
     * 账户剩余橙豆数>= 0的店铺粉丝人数
     * @param reqMarketTouchingEffectVO 查询参数
     * @return 粉丝数量
     */
    int getHasVirtualCoinFansCount(ReqMarketTouchingEffectVO reqMarketTouchingEffectVO);
}
