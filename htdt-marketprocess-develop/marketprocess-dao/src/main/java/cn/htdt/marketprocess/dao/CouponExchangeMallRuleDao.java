package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.CouponExchangeMallRuleDomain;
import cn.htdt.marketprocess.vo.CouponExchangeMallRuleVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 优惠券转商城券规则表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-08
 */
@Mapper
public interface CouponExchangeMallRuleDao extends BaseMapper<CouponExchangeMallRuleDomain> {

    /**
     * 查询单条记录-仅仅包括优惠券转商城券规则表信息
     * @param domain
     * @return
     */
    CouponExchangeMallRuleDomain selectCouponExchangeMallRule(CouponExchangeMallRuleDomain domain);

    /**
     * 查看优惠券转商城券规则-批量
     * @param vo
     * @return
     */
    List<CouponExchangeMallRuleVO> selectCouponExchangeMallRuleList(CouponExchangeMallRuleVO vo);

    /**
     * 查询单条记录-仅仅包括rule表信息
     * @param domain
     * @return
     */
    void updateByParams(CouponExchangeMallRuleDomain domain);

}
