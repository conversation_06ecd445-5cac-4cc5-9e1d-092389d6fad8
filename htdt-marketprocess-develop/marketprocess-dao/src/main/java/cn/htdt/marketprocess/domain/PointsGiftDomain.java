package cn.htdt.marketprocess.domain;

import cn.htdt.common.domain.BaseDomain;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 积分礼品表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("points_gift")
public class PointsGiftDomain extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 礼品编号
     */
    private String giftNo;

    /**
     * 礼品名称
     */
    private String giftName;

    /**
     * 所需积分数（个）
     */
    private Integer points;

    /**
     * 礼品总库存（件）
     */
    private Integer giftStockNum;

    /**
     * 已兑换库存（件）
     */
    private Integer convertStockNum;

    /**
     * 礼品主图
     */
    private String giftPictureUrl;

    /**
     * 配送方式 （1001:自提）
     */
    private String deliveryType;

    /**
     * 兑换规则
     */
    private String convertRule;

    /**
     * 礼品上架状态（1:已上架 2:未上架）
     */
    private String giftStatus;

    /**
     * 最新上下架时间
     */
    private LocalDateTime latestUpDownTime;

    /**
     * 是否可用:默认1，1：可用，其余不可用
     */
    private Integer disableFlag;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 创建人ID
     */
    private String createNo;

    /**
     * 最后一次修改人ID
     */
    private String modifyNo;

    /**
     * 礼品归属（1.门店，2.商家）
     */
    private String pointsGiftType;

    /**
     * 商家礼品状态（1:已上架 2:未上架）
     */
    private String merchantGiftStatus;

    /**
     * 商家礼品是否首次上架（1.是，2.否）
     */
    private String merchantFirstStatus;
}
