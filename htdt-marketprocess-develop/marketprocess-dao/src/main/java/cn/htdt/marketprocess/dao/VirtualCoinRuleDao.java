package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.VirtualCoinRuleDomain;
import cn.htdt.marketprocess.vo.VirtualCoinRuleAndCouponInfoDetailVo;
import cn.htdt.marketprocess.vo.VirtualCoinRuleAndCouponInfoVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 橙豆规则表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-19
 */
@Mapper
public interface VirtualCoinRuleDao extends BaseMapper<VirtualCoinRuleDomain> {

    /**
     * 更新接口
     * @param virtualCoinRuleDomain
     */
    void updVirtualCoinRule(VirtualCoinRuleDomain virtualCoinRuleDomain);

    /**
     * 查询接口
     * @param virtualCoinRuleDomain
     * @return
     */
    List<VirtualCoinRuleDomain> queryVirtualCoinRuleList(VirtualCoinRuleDomain virtualCoinRuleDomain);

    /**
     * 查询店铺自己创建的橙豆规则或者商家橙豆规则
     *
     * 20230928蛋品-赵翔宇-商家橙豆-橙豆规则, 店铺查询店铺本身创建的橙豆规则
     *
     * @param virtualCoinRuleAndCouponInfoVo 查询参数
     * @return 结果
     */
    List<VirtualCoinRuleAndCouponInfoVo> getVirtualCoinRuleList(VirtualCoinRuleAndCouponInfoVo virtualCoinRuleAndCouponInfoVo);


    /**
     * 查询橙豆规则和对应的优惠券详情接口
     * @param virtualCoinRuleAndCouponInfoVo 查询参数
     * @return 结果
     */
    List<VirtualCoinRuleAndCouponInfoDetailVo> queryVirtualCoinRuleAndCouponInfoDetail(VirtualCoinRuleAndCouponInfoVo virtualCoinRuleAndCouponInfoVo);

    /**
     * 查询店铺橙豆规则或者商家橙豆规则数量
     * @param virtualCoinRuleAndCouponInfoVo 查询参数
     * @return 数量
     */
    int getVirtualCoinRuleCount(VirtualCoinRuleAndCouponInfoVo virtualCoinRuleAndCouponInfoVo);
}
