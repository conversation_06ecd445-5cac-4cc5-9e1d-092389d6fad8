package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.GoodsPromotionCategoryRelationDomain;
import cn.htdt.marketprocess.vo.GoodsPromotionCategoryRelationVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 满减满折促销关联类目表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-18
 */
@Mapper
public interface GoodsPromotionCategoryRelationDao extends BaseMapper<GoodsPromotionCategoryRelationDomain> {


    List<GoodsPromotionCategoryRelationDomain> selectGoodsPromotionCategoryRelation(GoodsPromotionCategoryRelationVO relationVO);
    /**
     * 根据促销活动编号，删除关联的活动类目
     *
     * @param promotionNo
     * @return
     * <AUTHOR>
     * @since 2022-02-18
     */
    void deleteGoodsPromotionCategoryRelation(String promotionNo);

    /**
     * 根据促销活动编号，修改
     *
     * @param relationVO
     * @return
     * <AUTHOR>
     * @since 2022-02-18
     */
    void updateByPromotion(GoodsPromotionCategoryRelationVO relationVO);
}