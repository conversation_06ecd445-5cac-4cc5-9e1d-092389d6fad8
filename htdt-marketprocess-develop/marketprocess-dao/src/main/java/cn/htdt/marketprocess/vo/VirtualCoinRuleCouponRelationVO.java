package cn.htdt.marketprocess.vo;

import cn.htdt.marketprocess.domain.VirtualCoinRuleCouponRelationDomain;
import lombok.Data;

import java.util.List;

/**
 * 20230928蛋品-赵翔宇-商家储值-橙豆规则, 橙豆规则优惠券关联VO
 *
 * <AUTHOR>
 * @since 2023-08-15
 */
@Data
public class VirtualCoinRuleCouponRelationVO extends VirtualCoinRuleCouponRelationDomain {

    /**
     * 橙豆规则编号
     */
    private List<String> virtualNoList;

    /**
     * 券使用渠道 (1001:汇享购下单 1002:门店下单)
     */
    private String couponUseChannel;

    /**
     * 促销类型 3001:平台-手动发代理人券 3002::平台-手动发粉丝券 3003:平台-网店代理人领券 3004:平台-网店粉丝领券 3005:店铺-手动发粉丝券 3006:店铺-网店粉丝领券 3007:膨胀红包 3100:抽奖券
     *
     * 参考枚举: PromotionTypeCouponEnum
     */
    private String promotionCouponType;

    /**
     * 券有效期, 1001：长期有效 1002：指定日期 1003:自领取后天数有效
     */
    private String couponPeriodValidity;
}
