package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.EnrollImportStoreDomain;
import cn.htdt.marketprocess.vo.AtomEnrollImportStoreVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 报名表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-22
 */
@Mapper
public interface EnrollImportStoreDao extends BaseMapper<EnrollImportStoreDomain> {

    /**
     * 查询记录list
     *
     * @param storeDomain
     * @return
     */
    List<EnrollImportStoreDomain> selectListByParams(EnrollImportStoreDomain storeDomain);

    void deleteEnrollImportStore(AtomEnrollImportStoreVO importStoreDomain);
}
