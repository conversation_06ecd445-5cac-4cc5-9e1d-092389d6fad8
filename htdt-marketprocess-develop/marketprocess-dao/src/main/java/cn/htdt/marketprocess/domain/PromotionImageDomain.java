package cn.htdt.marketprocess.domain;

import cn.htdt.common.domain.BaseDomain;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 促销活动图片
 *
 * <AUTHOR>
 * @since 2022-08-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("goods_promotion_image")
public class PromotionImageDomain extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 活动编号
     */
    private String promotionNo;

    /**
     * url
     */
    private String url;

    /**
     * 排序值
     */
    private Integer sortValue;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 是否可用:默认1，1：可用，其余不可用
     */
    private Integer disableFlag;

}
