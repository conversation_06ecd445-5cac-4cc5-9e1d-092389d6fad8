package cn.htdt.marketprocess.domain;

import cn.htdt.common.domain.BaseDomain;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 任务和营销清单表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("agent_market_bill")
public class AgentMarketBillDomain extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 清单表查询主键
     */
    private String marketBillNo;

    /**
     * 代理人编号
     */
    private String agentNo;

    /**
     * 店铺编号
     */
    private String storeNo;

    /**
     * 任务活动唯一编号或者是商品编号
     */
    private String taskOrGoodsNo;

    /**
     * 营销活动类型，对应枚举MarketTypeEnum
     */
    private String marketType;

    /**
     * 任务名称|商品名称
     */
    private String taskOrGoodsName;

    /**
     * 任务图片地址
     */
    private String taskImageUrl;

}