package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.PromotionVirtualGoodsRelationDomain;
import cn.htdt.marketprocess.vo.PromotionVirtualGoodsRelationVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 优惠券对应虚拟商品关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-01
 */
@Mapper
public interface PromotionVirtualGoodsRelationDao extends BaseMapper<PromotionVirtualGoodsRelationDomain> {

    List<PromotionVirtualGoodsRelationDomain> selectByParams(PromotionVirtualGoodsRelationVo queryVo);

    PromotionVirtualGoodsRelationDomain selectOneByParams(PromotionVirtualGoodsRelationVo queryVo);

    int updateTotalNum(PromotionVirtualGoodsRelationVo queryVo);

}
