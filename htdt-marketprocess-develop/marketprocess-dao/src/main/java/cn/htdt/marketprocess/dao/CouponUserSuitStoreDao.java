package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.CouponUserSuitStoreDomain;
import cn.htdt.marketprocess.vo.CouponUserSuitStoreVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户优惠券适用店铺表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@Mapper
public interface CouponUserSuitStoreDao extends BaseMapper<CouponUserSuitStoreDomain> {

    List<CouponUserSuitStoreVO> batchQueryCouponUserSuitStoreList(@Param("couponUserSuitStoreVOList") List<CouponUserSuitStoreVO> couponUserSuitStoreVOList);


    Integer batchInsert(@Param("dataList") List<CouponUserSuitStoreDomain> dataList);

    List<CouponUserSuitStoreVO> querySuitStoreList(@Param("userCouponNo") String userCouponNo);


}
