package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.AgentTaskDomain;
import cn.htdt.marketprocess.vo.AgentTaskVO;
import cn.htdt.marketprocess.vo.StoreAgentTaskVO;
import cn.htdt.marketprocess.vo.StoreTaskCollectVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-11
 */
@Mapper
public interface AgentTaskDao extends BaseMapper<AgentTaskDomain> {
    /**
     * 逻辑删除
     *
     * @param agentTaskDomain
     * @return
     */
    int logicDelete(AgentTaskDomain agentTaskDomain);

    AgentTaskDomain selectValidTask(AgentTaskDomain agentTaskDomain);

    AgentTaskVO selectByParams(AgentTaskDomain agentTaskDomain);

    List<AgentTaskVO> selectListByParams(AgentTaskVO agentTaskVO);

    int updateByTaskNo(AgentTaskDomain agentTaskDomain);

    List<StoreAgentTaskVO> selectStoreListByParams(StoreAgentTaskVO storeAgentTaskVO);

    List<AgentTaskDomain> selectStoreAgentTaskByPage(AgentTaskVO agentTaskVO);
    /**
     * @Description 店铺上下架平台拉新任务
     * <AUTHOR>
     * @param storeAgentTaskVO
     * @return
     */
    int updateStoreTaskByTaskNo(StoreAgentTaskVO storeAgentTaskVO);
    /**
     * @param storeAgentTaskVO
     * @Description : 根据店铺编号或商家编号查询未初始化的任务
     * <AUTHOR> 高繁
     * @date : 2021/2/22 16:04
     */
    List<String> getNotInitTaskListByStoreOrMerchant(StoreAgentTaskVO storeAgentTaskVO);
    /**
     * @param storeTaskCollectVO
     * @Description : 根据店铺编号+任务编号查询任务参与代理人数
     * <AUTHOR> 高繁
     * @date : 2021/2/25 10:31
     */
    List<StoreTaskCollectVO> selectStoreTaskJoinAgentNum(StoreTaskCollectVO storeTaskCollectVO);

    /**
      * @param merchantNo 商家编码
      * @Description : 根据商家编号查询已初始化的店铺
      * <AUTHOR> 高繁
      * @date : 2021/3/3 10:42
     */
    List<String> selectInitStoreListByMerchant(@Param("merchantNo") String merchantNo);

    /**
     * @description 查询任务酬劳
     * <AUTHOR>
     * @date 2021-03-10 10:26:38
     * @param agentTaskVO
     * @return
     */
    List<AgentTaskVO> selectTaskRewardListByParams(AgentTaskVO agentTaskVO);

    /**
     * @description 已过期任务超过48小时自动下架
     * <AUTHOR>
     * @date 2021-03-11 09:24:43
     * @param agentTaskVO
     * @return
     */
    Integer updateExpireTask(AgentTaskVO agentTaskVO);

}