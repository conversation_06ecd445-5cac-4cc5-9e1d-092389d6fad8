package cn.htdt.marketprocess.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/1/28
 */
@Data
public class VirtualCoinRecordStatistics {

    /**
     * 交易类型
     */
    private String tradeType;
    /**
     * 橙豆总收费
     */
    private BigDecimal totalPaymentAmount;

    /**
     * 橙豆总变动数量量
     */
    private BigDecimal totalVirtualCoinChange;

    /**
     * 橙豆总额
     */
    private BigDecimal totalCoin;

}
