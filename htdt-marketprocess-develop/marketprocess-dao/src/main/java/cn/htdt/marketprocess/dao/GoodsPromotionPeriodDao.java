package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.GoodsPromotionPeriodDomain;
import cn.htdt.marketcenter.dto.request.AtomReqGoodsPromotionPeriodDTO;
import cn.htdt.marketprocess.vo.AtomGoodsPromotionPeriodVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 商品促销活动时间段表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-22
 */
@Mapper
public interface GoodsPromotionPeriodDao extends BaseMapper<GoodsPromotionPeriodDomain> {

    /**
     * 根据活动编号删除促销活动时间段数据
     *
     * @param promotionNo
     * @return
     * <AUTHOR>
     * @date 2021-06-28
     */
    void deletePeriodByPromotionNo(String promotionNo);

    /**
     * 根据活动编号查询活动的时间段信息
     *
     * @param vo
     * @return
     * <AUTHOR>
     * @date 2021-07-05
     */
    List<GoodsPromotionPeriodDomain> selectPeriodByPromotionNo(AtomGoodsPromotionPeriodVo vo);

    /**
     * 批量更新活动时间段数据
     *
     * @param periodDTOList
     * <AUTHOR>
     * @date 2021-07-19
     */
    void batchUpdateGoodsPromotionPeriod(@Param("periodDTOList") List<AtomReqGoodsPromotionPeriodDTO> periodDTOList);

    /**
     * 根据活动编号和时间段编号查询对应场次的活动信息
     *
     * @param atomReqGoodsPromotionPeriodDTO 请求参数
     * <AUTHOR>
     * @date 2021-08-06
     */
    AtomGoodsPromotionPeriodVo selectPromotionPeriodInfo(AtomReqGoodsPromotionPeriodDTO atomReqGoodsPromotionPeriodDTO);

    /**
     * 根据活动编号查询活动以及场次信息
     *
     * @param atomReqGoodsPromotionPeriodDTO 请求参数
     * <AUTHOR>
     * @date 2021-11-09
     */
    List<AtomGoodsPromotionPeriodVo> selectPromotionPeriodList(AtomReqGoodsPromotionPeriodDTO atomReqGoodsPromotionPeriodDTO);

    /**
     * 根据活动编号时间段编号，删除促销活动时间段数据
     *
     * @param vo
     * @return
     * <AUTHOR>
     * @date 2021-09-23
     */
    void deletePeriodByNo(AtomGoodsPromotionPeriodVo vo);

    /**
     * 根据活动编号和活动场次编号查询对应场次下的活动基本信息
     *
     * @param reqDTO 查询参数
     * <AUTHOR>
     * @date 2021-12-06
     */
    AtomGoodsPromotionPeriodVo selectPromotionPeriodBaseInfo(AtomReqGoodsPromotionPeriodDTO reqDTO);

}
