package cn.htdt.marketprocess.vo;

import cn.htdt.marketprocess.domain.CardCountingConfigDomain;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * <p>
 * 计次卡设置表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-11
 */
@Data
public class CardCountingConfigVO extends CardCountingConfigDomain {

    private static final long serialVersionUID = 1L;

    /***活动基本信息**/
    /**
     * 活动名称
     */
    private String promotionName;
    /**
     * 活动短链接
     */
    private String shortLink;
    /**
     * 图片类型(1:默认图片 2:自定义图片)
     */
    private Integer promotionPictureType;
    /**
     * 图片url
     */
    private String promotionPictureUrl;
    /**
     * 活动对象 1001: 不限制 1002: 限制老用户 1003: 限制新用户
     */
    private String userScope;
    /**
     * 活动对象 1001: 不限制 1002: 限制老用户 1003: 限制新用户
     */
    private String joinUserScope;
    /**
     * 活动说明
     */
    private String promotionExplain;
    /**
     * 活动时间段类型 (1001:全天 1002:指定时间段)
     */
    private String effectivePeriodType;
    /**
     * 活动开始时间
     */
    private LocalDateTime effectiveTime;
    /**
     * 活动结束时间
     */
    private LocalDateTime invalidTime;
    /**
     * 每日开始时间
     */
    private LocalTime dailyStartTime;
    /**
     * 每日结束时间
     */
    private LocalTime dailyEndTime;
    /**
     * 来源 1001：平台 1002：商家 1003：店铺
     */
    private String sourceType;
    /**
     * 活动类型 1000:抽奖  2000:商品促销 3000:优惠券
     */
    private String activityType;
    /**
     * 促销类型 1001:大转盘 1002:套圈圈 1003:打地鼠 1004:扭蛋机 1005:刮刮乐 1006:膨胀红包 2001:秒杀 2002:拼团 2003:预售 2004:单品促销 2005:满额促销 2006:满量促销 2007:满额赠 2008:套餐 3001:平台-手动发代理人券 3002::平台-手动发粉丝券 3003:平台-网店代理人领券 3004:平台-网店粉丝领券 3005:店铺-手动发粉丝券 3006:店铺-网店粉丝领券 3007:膨胀红包 3100:抽奖券
     */
    private String promotionType;
    /**
     * 活动状态 1001:活动未开始 1002:活动进行中，1003:活动已结束
     */
    private String status;
    /**
     * 活动有效期（1001：长期有效 1002：指定日期）
     */
    private String periodValidity;
    /**
     * 上下架状态（1：未上架 2：上架）
     */
    private Integer upDownFlag;
    /**
     * 店铺上下架状态（1：未上架 2：上架）
     */
    private Integer storeUpDownFlag;
    /**
     * 创建来源1000：pc 2000：app
     */
    private String createSource;

    /**
     * 来源1000：一体机，为空无需条件
     */
    private String source;



    /**其他**/
    /**
     * 活动更新时间-开始时间
     */
    private LocalDateTime startTime;
    /**
     * 活动更新时间-结束时间
     */
    private LocalDateTime endTime;

    /**
     * 活动结束时间判断用
     */
    private LocalDateTime expandEndTime;

    /**
     * 活动对象集合 1001: 不限制 1002: 仅限老用户 1003: 仅限新用户
     */
    private List<String> userScopeList;

}
