package cn.htdt.marketprocess.dao;

import cn.htdt.marketcenter.dto.response.AtomResStoreSmsTrafficDTO;
import cn.htdt.marketprocess.domain.SmsTrafficDomain;
import cn.htdt.marketprocess.dto.request.ReqSmsTrafficDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 短信流量表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2021/07/05
 **/
@Mapper
public interface SmsTrafficDao extends BaseMapper<SmsTrafficDomain> {

    /**
     * 根据参数查询
     *
     * @param domain 查询参数
     * @return List<SmsTrafficDomain>
     */
    List<SmsTrafficDomain> selectByParam(SmsTrafficDomain domain);

    /**
     * 修改短信流量信息（增加）
     * 注：短信余额增加，发送成功条数、发送成功次数可能会减少
     *
     * @param domain 请求参数
     * @return int
     */
    int updateByIncr(SmsTrafficDomain domain);

    /**
     * 修改短信流量信息（减少）
     * 注：短信余额扣除，发送成功条数、发送成功、短信发送总次数次数会增加
     *
     * @param domain 请求参数
     * @return int
     */
    int updateByDecr(SmsTrafficDomain domain);

    /**
     * 平台根据参数统计店铺总剩余短信条数
     *
     * @param domain 查询参数
     * @return List<SmsTrafficDomain>
     */
    Integer countByParam(SmsTrafficDomain domain);

    /**
     * 根据参数分页查询所有店铺短信账户上的剩余短信条数相关信息
     *
     * @param reqSmsTrafficDTO 查询参数
     * @return List<SmsTrafficDomain>
     */
    List<SmsTrafficDomain> getStoreSmsRemainingInfo(ReqSmsTrafficDTO reqSmsTrafficDTO);

    /**
     * 修改短信条数
     *
     * @param domain 请求参数
     * @return int
     */
    int updateByParam(SmsTrafficDomain domain);
}
