package cn.htdt.marketprocess.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 权益触达效果请求参数
 * <AUTHOR>
 * @date 2023-01-05
 */
@Data
public class ReqMarketTouchingEffectVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 店铺编号
     */
    private List<String> storeNoList;

    /**
     * 店铺编号
     */
    private String storeNo;

    /**
     * 优惠券编号
     */
    private String couponNo;

    /**
     * market库名
     */
    private String marketDatabaseName;

    /**
     * user库名
     */
    private String userDatabaseName;
}
