package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.AgentRewardDomain;
import cn.htdt.marketcenter.dto.request.AgentRewardStoreNoDTO;
import cn.htdt.marketcenter.dto.request.AtomReqAgentRewardDTO;
import cn.htdt.marketcenter.dto.request.AtomReqModifyRewardStatusDTO;
import cn.htdt.marketcenter.dto.request.AtomReqStoreAgentRewardDTO;
import cn.htdt.marketcenter.dto.response.AtomResAgentRewardDTO;
import cn.htdt.marketcenter.dto.response.AtomYjCountDTO;
import cn.htdt.marketprocess.vo.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 代理人酬劳信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-11
 */
@Mapper
public interface AgentRewardDao extends BaseMapper<AgentRewardDomain> {

    /**
     * 代理人任务酬劳上限判断
     *
     * @param reqAgentRecordDTO
     * @return
     */
    List<AgentRewardSumVO> selectSumByCeiling(AtomReqAgentRewardDTO reqAgentRecordDTO);

    /**
     * @param agentRewardVO
     * @return
     * @Description 代理人酬劳信息列表
     * <AUTHOR>
     */
    List<AgentRewardVO> selectAgentRewardListByPage(AgentRewardVO agentRewardVO);

    /**
     * @param agentRewardDomain
     * @return
     * @Description 查询店铺任务最新推广信息
     * <AUTHOR>
     */
    AgentRewardVO selectNewAgentReward(AgentRewardDomain agentRewardDomain);

    /**
     * @param agentRewardDomain
     * @return
     * @Description 酬劳明细汇总
     * <AUTHOR>
     */
    AgentRewardVO selectAgentRewardStatistics(AgentRewardDomain agentRewardDomain);

    /**
     * @param agentRewardDomain
     * @return
     * @Description 酬劳明细月度汇总
     * <AUTHOR>
     */
    List<AgentRewardVO> selectAgentRewardMouthStatistics(AgentRewardDomain agentRewardDomain);

    /**
     * @param agentRewardVO
     * @return
     * @Description 按天数统计商品和任务
     * <AUTHOR>
     */
    AgentRewardVO selectGoodsOrTaskDayStatistics(AgentRewardVO agentRewardVO);

    /**
     * @description 统计分销订单数量
     * <AUTHOR>
     * @date 2021-07-05 16:46:53

     * @return
     */
    AgentRewardVO selectDistributeOrderCount(AgentRewardVO agentRewardVO);

    /**
     * 根据商品集合及各种状态查询佣金汇总
     *
     * @param agentRewardVO
     * @return
     * <AUTHOR>
     */
    List<AgentRewardDomain> selectAgentRewardByGoodsNoList(AgentRewardVO agentRewardVO);

    /**
     * 通过商品编号以及代理人编号获取分销数量
     *
     * @param agentRewardVO
     * @return
     * <AUTHOR>
     */
    List<AgentRewardDomain> selectAgentDistributeAmountByGoodsNo(AgentRewardVO agentRewardVO);

    /**
     * 查询商品粉丝自购次数或统计商品代理人分销次数列表，分页查询，支持代理人名称模糊查询，代理人手机号精确查询
     *
     * @param agentRewardVO 查询参数
     * @return List<AgentRewardDomain>
     * <AUTHOR>
     */
    List<AgentRewardVO> selectAgentDistributeTimeByGoodsNo(AgentRewardVO agentRewardVO);

    /**
     * @param : reqStoreAgentRewardDTO
     * @Description : 查询店铺下任务 代理人酬劳汇总
     * <AUTHOR> 高繁
     * @date : 2021/1/22 14:21
     */
    List<AgentTaskRewardVO> selectAgentRewardListByTaskNo(AtomReqStoreAgentRewardDTO reqStoreAgentRewardDTO);

    /**
     * @param agentRewardVO
     * @return
     * @Description 代理人分销收益
     * <AUTHOR>
     */
    AgentRewardVO selectAgentOrderStatistics(AgentRewardVO agentRewardVO);

    /**
     * @param agentRewardVO
     * @return
     * @Description 代理人任务收益
     * <AUTHOR>
     */
    AgentRewardVO selectAgentTaskStatistics(AgentRewardVO agentRewardVO);

    /**
     * @param agentRewardDomain
     * @return
     * @Description 代理人佣金资产
     * <AUTHOR>
     */
    AgentRewardVO selectAgentYjStatistics(AgentRewardVO agentRewardDomain);

    /**
     * @param agentRewardDomain
     * @return
     * @Description 代理人其他资产
     * <AUTHOR>
     */
    AgentRewardVO selectAgentOtherStatistics(AgentRewardDomain agentRewardDomain);

    /**
     * @param agentRewardDomain
     * @return
     * @Description 代理人累计成果
     * <AUTHOR>
     */
    AgentRewardVO selectAgentTotalStatistics(AgentRewardDomain agentRewardDomain);

    /**
     * 查询代理人总酬劳接口（不分页）
     * * @auth hxj
     *
     * @param agentRewardVO
     * @return
     */
    List<AgentRewardDomain> selectAgentRewardList(AgentRewardVO agentRewardVO);

    /**
     * 查询代理人分销酬劳
     *
     * @param reqAgentRecordDTO
     * @return
     * @auth hxj
     */
    List<AtomResAgentRewardDTO> selectAgentRewardDetails(AtomReqAgentRewardDTO reqAgentRecordDTO);

    /**
     * 汇赚钱-首页-代理人获得酬劳滚动信息
     *
     * @param reqAgentRecordDTO
     * @return
     * @auth hxj
     */
    List<AgentRewardDomain> selectAgentRewardCircularDisplay(AtomReqAgentRewardDTO reqAgentRecordDTO);

    /**
     * @param :
     * @Description : 代理人酬劳管理列表查询
     * <AUTHOR> 高繁
     * @date : 2021/1/27 16:11
     */
    List<AgentRewardDomain> selectAgentRewardManage(AgentRewardDomain agentRewardDomain);

    /**
     * 查询代理人酬劳核销信息
     *
     * @param agentRewardVO
     * @return
     * <AUTHOR> 邢会强
     */
    List<AgentRewardDomain> selectWriteOffGoodsList(AgentRewardVO agentRewardVO);

    /**
     * @description 查询代理人酬劳核销统计信息
     * <AUTHOR>
     * @date 2021-09-27 18:23:59
     * @param agentRewardVO
     * @return
     */
    int selectWriteOffGoodsCount(AgentRewardVO agentRewardVO);

    /**
     * 查询核销码
     *
     * @param agentRewardDomain
     * @return
     * <AUTHOR> 邢会强
     */
    AgentRewardDomain selectWriteOffCode(AgentRewardDomain agentRewardDomain);

    /**
     * 酬劳核销批量
     *
     * @param agentRewardDomainList
     * @return
     * <AUTHOR> 邢会强
     */
    int updateAgentRewardWriteOffList(@Param("agentRewardDomainList") List<AgentRewardDomain> agentRewardDomainList);

    /**
     * @param agentRewardGoodsNumVO
     * @Description : 查询代理人所在店铺分销商品数
     * <AUTHOR> 高繁
     * @date : 2021/2/1 18:21
     */
    List<AgentRewardDomain> selectAgentRewardGoodsNum(AgentRewardGoodsNumVO agentRewardGoodsNumVO);

    /**
     * @param agentRewardGoodsNumVO
     * @Description : 查询代理人在此店铺下的酬劳汇总
     * <AUTHOR> 高繁
     * @date : 2021/2/3 10:05
     */
    List<AgentRewardDomain> selectStoreAgentRewardSum(AgentRewardGoodsNumVO agentRewardGoodsNumVO);

    /**
     * 更新酬劳的状态，只用于更新酬劳状态
     *
     * @param modifyRewardStatusDTO
     * @return
     */
    int updateRewardStatus(AtomReqModifyRewardStatusDTO modifyRewardStatusDTO);

    /**
     * 根据订单编号或者商品编号获取酬劳信息，主要用于云池商品或者分销商品
     *
     * @param agentRewardDTO
     * @return
     */
    List<AgentRewardDomain> selectByOrderNo(AtomReqAgentRewardDTO agentRewardDTO);

    /**
     * @param agentRewardDetailVO
     * @Description : 查询代理人在此店铺下的酬劳明细列表
     * <AUTHOR> 高繁
     * @date : 2021/2/17 16:53
     */
    List<AgentRewardDetailVO> selectAgentRewardDetailList(AgentRewardDetailVO agentRewardDetailVO);

    /**
     * @param agentRewardDomain
     * @Description : 查询任务或代理人酬劳总数
     * <AUTHOR> 桑伟杰
     * @date : 2021-02-22
     */
    AgentRewardVO selectTotalAgentReward(AgentRewardDomain agentRewardDomain);

    /**
     * @param agentRewardGoodsNumVO
     * @Description : 查询代理人在店铺下的佣金集合(包含店铺分销 平台分销)
     * <AUTHOR> 高繁
     * @date : 2021/2/26 11:14
     */
    List<AgentRewardDomain> selectAgentRewardCommissionList(AgentRewardGoodsNumVO agentRewardGoodsNumVO);

    /**
     * @param agentRewardGoodsNumVO
     * @Description : 查询代理人在店铺下的礼品或券集合(包含店铺分销 平台分销)
     * <AUTHOR> 高繁
     * @date : 2021/2/26 11:14
     */
    List<AgentRewardDomain> selectAgentRewardGiftList(AgentRewardGoodsNumVO agentRewardGoodsNumVO);

    /**
     * @param agentRewardGoodsNumVO
     * @Description : 根据代理人店铺查询酬劳汇总集合
     * <AUTHOR> 高繁
     * @date : 2021/3/1 19:14
     */
    List<AgentRewardDomain> selectStoreAgentRewardValueSum(AgentRewardGoodsNumVO agentRewardGoodsNumVO);

    /**
     * 暂不适用，但请勿删
     * 代理人维度
     * platform -> 店铺分销订单（供货店云池订单）-> 订单代理人总佣金（按订单维度、按店铺维度、按商家维度）传 storeNo | merchantNo
     * platform -> 赚佣金 ->（分销店总部分销订单） -> 订单代理人总佣金（按订单维度、按店铺维度、按商家维度）传 distributionStoreNo | distributionMerchantNo
     * 获取酬劳状态一般为：1:预估 2:冻结 3:解冻
     * @param reqAgentRewardDTO
     * <AUTHOR> 徐辉
     * @date : 2021/3/4 15:11
     * 铁血四兄弟
     */
    //BigDecimal selectAgentRewardValueSum(AtomReqAgentRewardDTO reqAgentRewardDTO);
    /**
     * 暂不适用，但请勿删
     * 供货店维度
     * platform -> 供货店云池订单 -> 待返金额（供货价-供货店佣金 按订单维度）
     * 获取酬劳状态一般为：1:预估 2:冻结 3:解冻
     * @param reqAgentRewardDTO
     * <AUTHOR> 徐辉
     * @date : 2021/3/7 16:10
     * 铁血四兄弟
     */
    //BigDecimal selectStoreToBeReturnRewardValueSum(AtomReqAgentRewardDTO reqAgentRewardDTO);
    /**
     * 暂不适用，但请勿删
     * 供货店维度
     * platform -> 供货店云池订单 -> 总佣金（四方分账之和 按订单维度）
     * 获取酬劳状态一般为：1:预估 2:冻结 3:解冻
     * @param reqAgentRewardDTO
     * <AUTHOR> 徐辉
     * @date : 2021/3/4 19:40
     * 铁血四兄弟
     */
    //BigDecimal selectStoreRewardValueSum(AtomReqAgentRewardDTO reqAgentRewardDTO);
    /**
     * 暂不适用，但请勿删
     * 分销店维度
     * platform ->（分销店）总部分销订单 -> 本店佣金（累计分销佣金 预估佣金 已入账佣金）
     * @param reqAgentRewardDTO
     * <AUTHOR> 徐辉
     * @date : 2021/3/5 18:30
     * 铁血四兄弟
     */
    //BigDecimal selectDisStoreRewardValueSum(AtomReqAgentRewardDTO reqAgentRewardDTO);

    /**
     * 云池 platform查询用
     * 代理人佣金 - 云池 - 汇推广 - 店铺分销订单（商家、店铺）
     * 订单总佣金 - 云池 - 汇推广 - （供货店）云池供货订单（商家、店铺）
     * 待返金额 - 云池 - 汇推广 - （供货店）云池供货订单（商家、店铺）
     * 本店获得佣金 - 云池 - 赚佣金 - (分销店)总部分销订单（商家、店铺）
     * 综合查询
     *
     * @param reqAgentRewardDTO
     * @return
     */
    PlanAsAWholeRewardVO selectPlanAsAWholeReward(AtomReqAgentRewardDTO reqAgentRewardDTO);

    /**
     * 根据订单编号或者父订单号获取分润信息
     * @param reqAgentRewardDTO
     */
    List<AgentRewardProfitVO> selectProfitByOrderNo(AtomReqAgentRewardDTO reqAgentRewardDTO);
/**
      * @param agentRewardDomainList
      * @Description : 批量修改酬劳
      * <AUTHOR> 高繁
      * @date : 2021/6/24 16:57
     */
    void updateRewardValue(@Param("agentRewardList") List<AgentRewardDomain> agentRewardDomainList);

    /**
      * @param orderNo
      * @Description : 根据订单号查询酬劳信息
      * <AUTHOR> 高繁
      * @date : 2021/6/24 18:04
     */
    List<AgentRewardDomain> selectAgentRewardListByOrder(@Param("orderNo") String orderNo);

 /**
     * <AUTHOR>
     * @Description 查询时间段内分销订单数和分销金额
     * @Date 2021/6/29
     * @Param [agentRewardStoreNoDTO]
     * @return cn.htdt.marketprocess.vo.AgentRewardOrderMoneyVO
     **/
    AgentRewardOrderMoneyVO selectAgentRewardOrderMoney(AgentRewardStoreNoDTO agentRewardStoreNoDTO);

    /**
     * @description 分页查询酬劳信息列表
     * <AUTHOR>
     * @date 2021-06-29 18:06:40
     * @param agentRewardVO
     * @return
     */
    List<AgentRewardVO> selectRewardListByPage(AgentRewardVO agentRewardVO);

    /**
     * @description 酬劳月度汇总
     * <AUTHOR>
     * @date 2021-12-21 16:37:58
     * @param agentRewardVO
     * @return
     */
    List<AgentRewardVO> selectRewardMonthCount(AgentRewardVO agentRewardVO);

    /**
     * @description 总部分销成交订单数
     * <AUTHOR>
     * @date 2021-07-28 11:08:11
     * @param agentRewardVO
     * @return
     */
    Integer selectOrderCount(AgentRewardVO agentRewardVO);

    /**
     * @description 总部分销获得佣金
     * <AUTHOR>
     * @date 2021-07-28 11:09:05
     * @param agentRewardVO
     * @return
     */
    BigDecimal selectYjCount(AgentRewardVO agentRewardVO);

    /**
     * @description 根据订单号查询佣金
     * <AUTHOR>
     * @date 2021-08-09 11:30:11
     * @param agentRewardVO
     * @return
     */
    BigDecimal selectYjCountByOrderNo(AgentRewardVO agentRewardVO);

    /**
     * 通过订单编号集合查询佣金
     * @Date 2021/11/8
     * <AUTHOR>
     * @param orderNos
     * @return List<AtomYjCountDTO>
     **/
    List<AtomYjCountDTO> selectYjCountByOrderNos(@Param("orderNos") Set<String> orderNos);

    /**
     * 通过订单编号集合查询佣金总额
     * @Date 2023/3/6
     * <AUTHOR>
     * @param orderNos
     * @return AtomYjCountDTO
     **/
    BigDecimal selectYjCountSumByOrderNos(@Param("orderNos") Set<String> orderNos);

    /**
     * 通过订单编号集合查询分销店佣金-解冻
     * <AUTHOR>
     * @param orderNos
     * @return List<AtomYjCountDTO>
     **/
    List<AtomYjCountDTO> selectStoreYjByOrderNos(@Param("orderNos") Set<String> orderNos);

    /**
     * 通过订单编号集合查询分销店佣金-冻结
     * <AUTHOR>
     * @param orderNos
     * @return List<AtomYjCountDTO>
     **/
    List<AtomYjCountDTO> selectStoreDjYjByOrderNos(@Param("orderNos") Set<String> orderNos);

    /**
     * @description 今日任务达人榜
     * <AUTHOR>
     * @date 2021-08-25 20:16:26
     * @param agentRewardVO
     * @return
     */
    List<AgentRewardVO> selectBestRewardAgentList(AgentRewardVO agentRewardVO);

    int selectUnpaidRewardCount(@Param("merchantNo") String merchantNo);

    /**
     * 查询可提现佣金（预估+冻结+解冻）
     * @param agentNo 代理人编号
     * @return 查询可提现佣金（预估+冻结+解冻）
     */
    BigDecimal selectTotalUnpaidYjReward(@Param("agentNo") String agentNo);

    /**
     * 查询未使用的服务券
     * @param agentNo 代理人编号
     * @return 未使用的服务券
     */
    BigDecimal selectUnusedAgentServiceCoupon(@Param("agentNo") String agentNo);

    /**
     * 查询未使用的现金券
     * @param agentNo 代理人编号
     * @return 未使用的现金券
     */
    BigDecimal selectUnusedAgentExclusiveCoupon(@Param("agentNo") String agentNo);

    /**
     * 查询未使用的代理人酬劳（排除佣金，现金券，服务券）
     * @param agentNo 代理人编号
     * @return 未使用的代理人酬劳（排除佣金，现金券，服务券）
     */
    BigDecimal selectUnusedAgentOtherReward(@Param("agentNo") String agentNo);

}
