package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.CouponUserRecordDomain;
import cn.htdt.marketcenter.dto.response.AtomResCouponRecordDTO;
import cn.htdt.marketprocess.dto.request.ReqCouponUserRecordDTO;
import cn.htdt.marketprocess.vo.CouponStoreNumVO;
import cn.htdt.marketprocess.vo.CouponUserRecordStaticVO;
import cn.htdt.marketprocess.vo.CouponUserRecordVO;
import cn.htdt.marketprocess.vo.VoucherSaleAndUsedCountVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 优惠券用户接收记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-01
 */
@Mapper
public interface CouponUserRecordDao extends BaseMapper<CouponUserRecordDomain> {

    /**
     * 领券记录
     * @param couponUserRecordVO
     */
    List<CouponUserRecordVO> selectCouponUserRecords(CouponUserRecordVO couponUserRecordVO);

    /**
     * 领券记录-单条
     * @param couponUserRecordVO
     */
    CouponUserRecordVO selectOneCouponUserRecord(CouponUserRecordVO couponUserRecordVO);

    /**
     * 根据批量优惠券串码统计领取量
     * @param couponUserRecordVO
     */
    List<CouponUserRecordVO> selectNumListByTerm(CouponUserRecordVO couponUserRecordVO);

    /**
     * 根据批量优惠券串码统计使用量
     * @param couponUserRecordVO
     */
    List<CouponUserRecordVO> selectUseNumListByTerm(CouponUserRecordVO couponUserRecordVO);


    /**
      * @param couponUserRecordVO
      * @Description : 根据手机号+店铺查询可使用的优惠券-不分券使用场景
      * <AUTHOR> 高繁
      * @date : 2021/4/27 14:01
     */
    List<CouponUserRecordVO> selectCouponCanUse(CouponUserRecordVO couponUserRecordVO);

    /**
     * 获取某优惠券的以下信息：
     *      单个粉丝总共可获得券数
     *      店铺粉丝总共可获得券
     *      店铺粉丝总共可获得券
     *      店铺粉丝总共可获得券
     * @param couponUserRecordVO
     */
    AtomResCouponRecordDTO selectCouponUserRecordCounts(CouponUserRecordVO couponUserRecordVO);

    /**
     * 获取发行总数
     * @param couponUserRecordVO
     */
    Integer selectCouponTotalCount(CouponUserRecordVO couponUserRecordVO);


    /**
      * @param couponUserRecordVO
      * @Description : 根据用户券编码查询优惠券基本信息
      * <AUTHOR> 高繁
      * @date : 2021/5/11 9:44
     */
    CouponUserRecordVO selectCouponInfoByUserRecord(CouponUserRecordVO couponUserRecordVO);

    /**
     * @param couponUserRecordVO
     * @Description : 根据用户券编码查询优惠券基本信息
     * <AUTHOR>
     * @date : 2021/5/11 9:44
     */
    CouponUserRecordVO selectCouponDetailByUserRecord(CouponUserRecordVO couponUserRecordVO);

    /**
     * @param couponUserRecordVO
     * @Description : 我的优惠券列表
     * <AUTHOR> 卜金隆
     * @date : 2021/5/11 17:01
     */
    List<CouponUserRecordVO> selectMyCouponList(CouponUserRecordVO couponUserRecordVO);


    List<CouponStoreNumVO> selectMyCouponStoreNo(CouponUserRecordVO couponUserRecordVO);
    /**
     * @param couponUserRecordVO
     * @Description : 我的优惠券列表数量
     * <AUTHOR> 卜金隆
     * @date : 2021/5/19 19:49
     */
    int selectMyCouponNum(CouponUserRecordVO couponUserRecordVO);

    /**
     * 批量更新优惠券使用状态
     * 券使用及退回场景
     *
     * @param couponUserRecordVO
     * @return
     */
    int batchUpdateCouponUserRecord(CouponUserRecordVO couponUserRecordVO);

    /**
     * 变更优惠券领取记录-分享领取
     * @param couponUserRecordVO
     * @return
     */
    int changeCouponUserRecord(CouponUserRecordVO couponUserRecordVO);
    /**
     * 获取某优惠券的以下信息：
     *      店铺粉丝总共可获得券
     *      店铺粉丝单日总共可获得券
     * @param couponUserRecordVO
     * <AUTHOR> 卜金隆
     * @date : 2021/6/30 19:49
     */
    AtomResCouponRecordDTO selectCouponStoreRecordCounts(CouponUserRecordVO couponUserRecordVO);

    /**
     * @param couponNos
     * @Description : 批量作废优惠券
     * <AUTHOR> 高繁
     * @date : 2021/9/8 15:43
     */
    int disableCouponUserRecord(@Param("couponNos") List<String> couponNos);

    /**
      * @param couponUserRecordDomain
      * @Description : 粉丝可用优惠券数量
      * <AUTHOR> 高繁
      * @date : 2021/11/5 17:36
     */
    int selectFansCanUseCouponNum(CouponUserRecordDomain couponUserRecordDomain);

    /**
     * 按照券的类型统计每种券的数量
     *
     * @param couponUserRecordDomain 请求参数
     * @return 结果
     */
    List<CouponUserRecordVO> selectFansCanUseCouponCount(CouponUserRecordDomain couponUserRecordDomain);

    int selectUnusedCouponCount(@Param("merchantNo")String merchantNo);

    /**
     * 获取粉丝购买某券数量
     * @param couponUserRecordVO
     * @return
     */
    int selectCouponCountByTerm(CouponUserRecordVO couponUserRecordVO);

    /**
     * @param couponUserRecordVO
     * @Description : 逻辑删除券
     * <AUTHOR> wl
     * @date : 2022/9/29
     */
    int delCouponUserRecord(CouponUserRecordVO couponUserRecordVO);

    List<VoucherSaleAndUsedCountVO> selectVoucherSaleAndUsedCount(CouponUserRecordVO couponUserRecordVO);

    /**
     * 店铺下所有粉丝获得的券张数(不论券是否过期)和已使用券总数
     * @param domain 查询参数
     * @return 店铺的券的统计情况
     */
    CouponUserRecordStaticVO getStoreCouponStaticByParam(CouponUserRecordDomain domain);

    /**
     * 分页统计
     *
     * @param reqCouponUserRecordDTO
     * @return
     */
    List<CouponUserRecordStaticVO> getStoreCouponStaticByPage(ReqCouponUserRecordDTO reqCouponUserRecordDTO);

    /**
     * 蛋品, 根据券编号, 查询使用的券
     *
     * @param couponUserRecordVO 查询参数
     * @return 券编号集合
     */
    List<String> getUsedCoupon(CouponUserRecordVO couponUserRecordVO);
}
