package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.VirtualCoinRecordDomain;
import cn.htdt.marketprocess.vo.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 橙豆收入支出记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-19
 */
@Mapper
public interface VirtualCoinRecordDao extends BaseMapper<VirtualCoinRecordDomain> {

    /**
     * 查询橙豆流水列表
     * @param virtualCoinRecordDomain
     * @return
     */
    List<VirtualCoinRecordDomain> queryVirtualCoinRecordList(VirtualCoinRecordDomain virtualCoinRecordDomain);

    /**
     * 查询
     * @param virtualCoinRecordDomain
     * @return
     */
    List<VirtualCoinRecordStatistics> queryVirtualCoinRecordStatistics(VirtualCoinRecordVO virtualCoinRecordDomain);

    /**
     * 橙豆交接班信息-橙豆充值
     * @param virtualCoinRecordDomain
     * @return
     */
    List<VirtualCoinRechargeRecordVo> queryRechargeRecord4WorkChange(VirtualCoinRecordVO virtualCoinRecordDomain);

    /**
     * 橙豆交接班信息-橙豆订单消费与退单
     * @param virtualCoinRecordDomain
     * @return
     */
    List<VirtualCoinOrderRecordVo> queryOrderRecord4WorkChange(VirtualCoinRecordVO virtualCoinRecordDomain);


    /**
     * 当前店铺下，所有用户购买购物币的支付金额总计和购物币的消费金额统计
     * @param virtualCoinRecordDomain 查询参数
     * @return 结果
     */
    VirtualCoinRechargeRecordVo queryVirtualCoinRecordEffectStatistics(VirtualCoinRecordDomain virtualCoinRecordDomain);

    /**
     * 当前店铺下，用户最近充值橙豆的数额和时间，以及消费橙豆的数额和时间
     * 20230928蛋品-赵翔宇-商家橙豆-粉丝橙豆, 查询共享店铺或者商家下，用户最近充值橙豆的数额和时间，以及消费橙豆的数额和时间
     *
     * @param reqVirtualCoinAndLatestRecordVo 查询参数
     * @return 结果
     */
    List<ResVirtualCoinLatestRecordVo> getLatestVirtualCoinRecordData(ReqVirtualCoinAndLatestRecordVo reqVirtualCoinAndLatestRecordVo);

    /**
     * 20230928蛋品-赵翔宇-商家橙豆, 查询共享店铺或者商家下，用户最近充值橙豆的数额和时间，以及消费橙豆的数额和时间
     *
     * @param reqVirtualCoinAndLatestRecordVo 查询参数
     * @return 结果
     */
    List<ResVirtualCoinLatestRecordVo> getMerchantLatestVirtualCoinRecordData(ReqVirtualCoinAndLatestRecordVo reqVirtualCoinAndLatestRecordVo);

    /**
     * @description 20230928蛋品-桑伟杰-会员等级-查询橙豆支付金额
     * <AUTHOR>
     * @date 2023-08-16 14:39:12
     * @param virtualCoinRecordVO
     * @return
     */
    VirtualCoinRecordStatistics selectTotalPaymentAmount(VirtualCoinRecordVO virtualCoinRecordVO);

    /**
     * 20230928蛋品-赵翔宇-商家橙豆, 使用橙豆支付的订单进行整单退款后，会对此订单下的使用的橙豆进行退回，
     * 退回至商家橙豆余额还是店铺橙豆余额，需要查询当时橙豆消费记录的橙豆规则类型
     *
     * @param virtualCoinRecordVO 查询参数
     * @return 橙豆消费记录
     */
    List<VirtualCoinRecordDomain> selectVirtualCoinConsumeRecordInfo(VirtualCoinRecordVO virtualCoinRecordVO);
}
