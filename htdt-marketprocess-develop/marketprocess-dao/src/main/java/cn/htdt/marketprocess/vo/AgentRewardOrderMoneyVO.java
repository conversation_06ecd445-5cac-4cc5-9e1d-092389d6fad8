package cn.htdt.marketprocess.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 店铺分销订单数 分销金额
 */
@Data
public class AgentRewardOrderMoneyVO implements Serializable {
    /**
     * 序列化
     */
    private static final long serialVersionUID = 1L;

    /**
     * 分销订单数
     */
    private int distributionOrder;

    /**
     * 代理人酬劳和
     */
    private BigDecimal distributionAmount;
}