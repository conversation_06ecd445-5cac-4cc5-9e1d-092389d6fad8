package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.LotteryRecordDomain;
import cn.htdt.marketprocess.vo.LotteryRecordVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 抽奖记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-09
 */
@Mapper
public interface LotteryRecordDao extends BaseMapper<LotteryRecordDomain> {

    int selectPromotionLotteryCount(@Param("promotionNo")String promotionNo);

    int selectOrderLotteryCountOfPromotion(LotteryRecordVo lotteryRecordVo);

    int updateLotteryEffective(LotteryRecordVo lotteryRecordVo);

    List<LotteryRecordDomain> selectPromotionLottery(LotteryRecordVo lotteryRecordVo);
}
