package cn.htdt.marketprocess.vo;

import cn.htdt.marketprocess.domain.VirtualCoinRecordDomain;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/26
 */
@Data
public class VirtualCoinRecordVO extends VirtualCoinRecordDomain {
    /**
     * 1:今日 2:昨天 3近一周 4:近30天 5:自定义
     */
    private Integer dayType;

    /**
     * 5:自定义-开始时间
     * pattern = "yyyy-MM-dd"
     */
    private LocalDateTime countStartTime;

    /**
     * 5:自定义-结束时间
     * pattern = "yyyy-MM-dd"
     */
    private LocalDateTime countEndTime;

    /**
     * 0:全部 1000:汇享购 1001:超级老板PC 1002:超级老板APP 1003:一体机
     */
    private String appChannelSource;

    /**
     * 粉丝编号列表
     */
    private List<String> fansNoList;

    /**
     * 交易类型类型集合，参考字典：VirtualCoinTradeTypeEnum
     */
    private List<String> tradeTypeList;

    /**
     * 店铺编号集合
     */
    private List<String> storeNoList;

}
