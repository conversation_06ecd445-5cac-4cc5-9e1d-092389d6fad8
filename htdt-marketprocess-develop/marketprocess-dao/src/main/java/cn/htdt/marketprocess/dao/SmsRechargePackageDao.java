package cn.htdt.marketprocess.dao;

import cn.htdt.common.dto.response.ExecuteDTO;
import cn.htdt.marketprocess.domain.SmsRechargePackageDomain;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 短信充值套餐mapper
 *
 * <AUTHOR>
 * @date 2021-09-26
 **/
@Mapper
public interface SmsRechargePackageDao extends BaseMapper<SmsRechargePackageDomain> {

    /**
     * 根据参数查询
     *
     * @param domain
     * @return List<SmsRechargePackageDomain>
     * <AUTHOR>
     */
    List<SmsRechargePackageDomain> selectByParam(SmsRechargePackageDomain domain);

    int deleteByPackageNo(@Param("packageNo")String packageNo);

    int updateParam(SmsRechargePackageDomain domain);


    Integer selectMaxSort();
}
