package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.WxMiniCodeDomain;
import cn.htdt.marketprocess.vo.WxMiniCodeVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 微信小程序二维码生成记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-27
 */
@Mapper
public interface WxMiniCodeDao extends BaseMapper<WxMiniCodeDomain> {

    /**
     * 查询微信小程序二维码信息
     * @param wxMiniCodeVO
     * @return
     */
    WxMiniCodeVO selectWxMiniCode(WxMiniCodeVO wxMiniCodeVO);

    /**
     * 更新微信小程序二维码信息
     * @param wxMiniCodeVO
     * @return
     */
    void updateWxMiniCode(WxMiniCodeVO wxMiniCodeVO);

}
