package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.LotteryRuleDomain;
import cn.htdt.marketprocess.vo.AtomLotteryRuleVo;
import cn.htdt.marketprocess.vo.PromotionRewardInfoVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 抽奖规则 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-08
 */
@Mapper
public interface LotteryRuleDao extends BaseMapper<LotteryRuleDomain> {

    /**
     * 查询单条记录-包括2表信息
     * @param atomLotteryRuleVo
     * @return
     */
    AtomLotteryRuleVo selectOneByParams(AtomLotteryRuleVo atomLotteryRuleVo);

    /**
     * 查询记录list
     * @param atomLotteryRuleVo
     * @return
     */
    List<AtomLotteryRuleVo> selectByParams(AtomLotteryRuleVo atomLotteryRuleVo);

    /**
     * 查询记录list-商家下店铺抽奖查询
     * @param atomLotteryRuleVo
     * @return
     */
    List<AtomLotteryRuleVo> selectMerchantStoreByParams(AtomLotteryRuleVo atomLotteryRuleVo);

    /**
     * 查询记录list-用于商家参与的平台抽奖列表
     * @param atomLotteryRuleVo
     * @return
     */
    List<AtomLotteryRuleVo> selectMerchantJoinByParams(AtomLotteryRuleVo atomLotteryRuleVo);

    /**
     * 查询记录list-用于店铺参与的平台抽奖列表
     * @param atomLotteryRuleVo
     * @return
     */
    List<AtomLotteryRuleVo> selectStoreJoinByParams(AtomLotteryRuleVo atomLotteryRuleVo);

    /**
     * 查询单条记录-仅仅包括rule表信息
     * @param atomLotteryRuleVo
     * @return
     */
    AtomLotteryRuleVo selectLotteryRule(AtomLotteryRuleVo atomLotteryRuleVo);

    /**
     * 查询抽奖活动信息列表
     * @param atomLotteryRuleVo
     * @return
     */
    List<AtomLotteryRuleVo> selectLotteryRuleList(AtomLotteryRuleVo atomLotteryRuleVo);

    /**
     * 蛋品, 店铺查询商家抽奖活动信息列表, 目前只有拆盲盒抽奖
     * @param atomLotteryRuleVo 查询参数
     * @return 拆盲盒活动列表
     */
    List<AtomLotteryRuleVo> selectMerchantLotteryRuleList(AtomLotteryRuleVo atomLotteryRuleVo);

    /**
     * 蛋品, 店铺查询商家抽奖活动信息详情, 目前只有拆盲盒抽奖
     * 这里是以商家的上下架状态作为活动的上下架状态
     * @param atomLotteryRuleVo 查询参数
     * @return 拆盲盒活动列表
     */
    List<AtomLotteryRuleVo> selectMerchantLotteryRuleDetail(AtomLotteryRuleVo atomLotteryRuleVo);

    /**
     * 查询单条记录-仅仅包括rule表信息
     * @param atomLotteryRuleVo
     * @return
     */
    void updateByParams(AtomLotteryRuleVo atomLotteryRuleVo);

    /**
     * 根据promotionNo删除抽奖活动
     * @param atomLotteryRuleVo
     * @return
     */
    void deleteLotteryRuleByPromotionNo(AtomLotteryRuleVo atomLotteryRuleVo);

    /**
     * 获取抽奖活动详情
     *
     * @param atomLotteryRuleVo
     * @return
     */
    PromotionRewardInfoVO selectLotteryInfo(AtomLotteryRuleVo atomLotteryRuleVo);

    /**
     * 校验是否存在活动范围内的抽奖
     *
     * @param atomLotteryRuleVo
     * @return
     */
    Integer selectLotteryCount(AtomLotteryRuleVo atomLotteryRuleVo);

}
