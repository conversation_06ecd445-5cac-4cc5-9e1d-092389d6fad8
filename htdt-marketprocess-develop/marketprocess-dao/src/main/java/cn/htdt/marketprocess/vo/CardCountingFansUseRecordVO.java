package cn.htdt.marketprocess.vo;

import cn.htdt.marketprocess.domain.CardCountingFansUseRecordDomain;
import lombok.Data;

/**
 * <p>
 * 计次卡粉丝使用记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-29
 */
@Data
public class CardCountingFansUseRecordVO extends CardCountingFansUseRecordDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 总次数
     */
    private Integer totalCounts;

    /**
     * 已使用X次
     */
    private Integer hasUseCounts;

    /**
     * 剩余Y次
     */
    private Integer remainCounts;

}
