package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.SmsSendDetailDomain;
import cn.htdt.marketprocess.vo.SmsSendDetailVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 短信发送明细表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2021/07/05
 **/
@Mapper
public interface SmsSendDetailDao extends BaseMapper<SmsSendDetailDomain> {

    List<SmsSendDetailVo> selectByParam(SmsSendDetailDomain smsSendDetailDomain);

    List<SmsSendDetailDomain> selectByTelephone(SmsSendDetailDomain smsSendDetailDomain);

    List<SmsSendDetailDomain> selectSendDetailBysonBatchNoList(@Param("sonBatchNoList") List<String> sonBatchNoList);

    /**
     * 根据参数修改
     *
     * @param smsSendDetailDomainList 编辑参数
     * @return int
     */
    int batchUpdateSmsSendDetail(@Param("smsSendDetailDomainList") List<SmsSendDetailDomain> smsSendDetailDomainList);

    int updateSmsSendDetail(SmsSendDetailDomain smsSendDetailDomain);

}
