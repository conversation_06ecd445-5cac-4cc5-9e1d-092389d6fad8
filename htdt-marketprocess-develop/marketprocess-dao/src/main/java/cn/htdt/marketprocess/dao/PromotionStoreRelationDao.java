package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.PromotionStoreRelationDomain;
import cn.htdt.marketprocess.vo.AtomStoreMemberPriceRuleVo;
import cn.htdt.marketprocess.vo.PromotionStoreRelationVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 活动店铺关联上下架表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-18
 */
@Mapper
public interface PromotionStoreRelationDao extends BaseMapper<PromotionStoreRelationDomain> {

    /**
     * 通过条件查询信息
     *
     * @param promotionStoreRelationVo
     * @return
     */
    PromotionStoreRelationVo selectPromotionStoreRelation(PromotionStoreRelationVo promotionStoreRelationVo);

    PromotionStoreRelationVo selectPromotionMerchantNoRelation(PromotionStoreRelationVo promotionStoreRelationVo);

    /**
     * 通过条件查询信息
     *
     * @param promotionStoreRelationVo
     * @return
     */
    List<PromotionStoreRelationVo> selectPromotionStoreRelationList(PromotionStoreRelationVo promotionStoreRelationVo);


    /**
     * 更新记录-主要是上下架
     *
     * @param domain
     * @return
     */
    void updateByParams(PromotionStoreRelationVo domain);

    /**
     * 批量更新
     * @Date 2023/5/29
     * <AUTHOR>
     * @param promotionStoreRelationVos
     * @return void
     **/
    void batchUpdateByParams(@Param("list") List<PromotionStoreRelationVo> promotionStoreRelationVos);

    /**
     * 商家会员价店铺活动列表
     * @Date 2023/5/26
     * <AUTHOR>
     * @param vo
     * @return List<AtomStoreMemberPriceRuleVo>
     **/
    List<AtomStoreMemberPriceRuleVo> storeMenberPricePromotion(AtomStoreMemberPriceRuleVo vo);
    
    /**
     * 获取商家店铺信息, 即店铺编码为空, 记录分派方式是全部店铺还是指定店铺的记录
     *
     * @param atomReqPromotionStoreRelationDTO
     * @return
     * <AUTHOR>
     * @date 2023-06-03
     */
    PromotionStoreRelationVo selectSjPromotionStoreRelation(PromotionStoreRelationVo promotionStoreRelationVo);


    /**
     * 蛋品, 获取商家抽奖活动, 可以参加的店铺信息
     *
     * @param promotionStoreRelationVo 查询参数
     * @return 可以参加商家抽奖活动的店铺信息
     * <AUTHOR>
     * @date 2023-10-19
     */
    List<PromotionStoreRelationDomain> selectMerchantLotteryStoreRelationList(PromotionStoreRelationVo promotionStoreRelationVo);

    /**
     * 商家社群接龙已经存在，即将取消关联的店铺
     * @param relationVo
     */
	void disableExistStoreRelation(PromotionStoreRelationVo relationVo);

	/**
	 * 商家社群接龙已经存在，继续关联的店铺
	 * @param relationVo
	 */
	void enableExistStoreRelation(PromotionStoreRelationVo relationVo);

	/**
	 * 删除商家社群接龙分发店铺
	 * @param promotionNo
	 * <AUTHOR>
	 * @date 2023-06-06
	 */
	void deletePromotionStoreRelation(String promotionNo);

    /**
     * 更新商家关联店铺的分发店铺方式以及上下架状态等信息
     * @param relationVo
     * <AUTHOR>
     * @date 2023-06-30
     */
    void updateSjStoreRelation(PromotionStoreRelationVo relationVo);
	
}
