package cn.htdt.marketprocess.vo;

import cn.htdt.marketprocess.domain.CouponSettingDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 查询橙豆规则对应的优惠券信息详细
 * <AUTHOR>
 * @date 2023-01-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class VirtualCoinRuleAndCouponInfoDetailVo extends CouponSettingDomain {

    /**
     * 橙豆规则编号
     */
    private String virtualNo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
