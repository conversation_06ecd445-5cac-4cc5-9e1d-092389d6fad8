package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.AgentWithdrawCashDomain;
import cn.htdt.marketprocess.vo.AgentWithdrawCashVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface AgentWithdrawCashDao extends BaseMapper<AgentWithdrawCashDomain> {

    AgentWithdrawCashDomain selectByParams(AgentWithdrawCashDomain agentWithdrawCashDomain);

    List<AgentWithdrawCashDomain> selectListByParams(AgentWithdrawCashDomain agentWithdrawCashDomain);

    AgentWithdrawCashVO selectWithdrawStatistics(AgentWithdrawCashDomain agentWithdrawCashDomain);

    List<AgentWithdrawCashVO> selectMouthStatistics(AgentWithdrawCashDomain agentWithdrawCashDomain);

    int updateByCashNo(AgentWithdrawCashDomain agentWithdrawCashDomain);

}
