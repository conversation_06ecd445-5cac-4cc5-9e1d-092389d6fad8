package cn.htdt.marketprocess.vo;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalTime;

/**
 * 限时购活动时间段
 *
 * <AUTHOR>
 * @since 2021-11-09
 */
@Data
public class AtomLimitTimePeriodVo implements Serializable {

    private static final long serialVersionUID = 5991652309493405872L;

    /**
     * 每场开始时间
     */
    private LocalTime startTime;

    /**
     * 每场结束时间
     */
    private LocalTime endTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
