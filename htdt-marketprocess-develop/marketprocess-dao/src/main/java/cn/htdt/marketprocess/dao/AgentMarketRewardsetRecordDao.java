package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.AgentMarketRewardsetRecordDomain;
import cn.htdt.marketcenter.dto.response.AtomResAgentMarketRewardsetRecordDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 代理人酬劳设置记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-18
 */
@Mapper
public interface AgentMarketRewardsetRecordDao extends BaseMapper<AgentMarketRewardsetRecordDomain> {
    /**
     * 查询分销商品酬劳历史修改记录
     * @param recordDTO
     * @return
     */
    List<AtomResAgentMarketRewardsetRecordDTO> selectRewardSetRecordList(AgentMarketRewardsetRecordDomain recordDTO);
}