package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.SmsRechargeAdjustRecordDomain;
import cn.htdt.marketprocess.dto.request.ReqSmsRechargeAdjustRecordDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 短信条数调整操作记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@Mapper
public interface SmsRechargeAdjustRecordDao extends BaseMapper<SmsRechargeAdjustRecordDomain> {
    /**
     * 分页查询短信条数调整操作记录
     *
     * @param reqSmsRechargeAdjustRecordDTO 请求参数
     * <AUTHOR>
     */
    List<SmsRechargeAdjustRecordDomain> getSmsRechargeAdjustRecordListByParam(ReqSmsRechargeAdjustRecordDTO reqSmsRechargeAdjustRecordDTO);
}
