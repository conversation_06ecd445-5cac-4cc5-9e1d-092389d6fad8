package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.PromotionApplicationProcessingDomain;
import cn.htdt.marketprocess.vo.ApplyPromoteRecordVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 推广申请处理表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-12
 */
@Mapper
public interface PromotionApplicationProcessingDao extends BaseMapper<PromotionApplicationProcessingDomain> {

    /**
     * 推广信息审核
     * @param processingDomain
     * @return
     */
    int updatePromotionApplication(PromotionApplicationProcessingDomain processingDomain);

    /**
     * 查询申请记录
     * @param processingDomain
     * @return
     */
    List<PromotionApplicationProcessingDomain> selectPromotionApplicationProcessing(PromotionApplicationProcessingDomain processingDomain);


    /**
     * 更改任务完成状态
     * @param list
     * @return
     */
    Integer batchUpdateByPrimaryKey(@Param("list") List<ApplyPromoteRecordVo> list);
}
