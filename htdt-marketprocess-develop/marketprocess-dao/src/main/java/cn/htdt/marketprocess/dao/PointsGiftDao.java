package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.PointsGiftDomain;
import cn.htdt.marketprocess.vo.PointsGiftConvertRecordVO;
import cn.htdt.marketprocess.vo.PointsGiftVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 积分商城礼品Mapper 接口
 *
 * <AUTHOR>
 * @since 2021-10-12
 */
@Mapper
public interface PointsGiftDao extends BaseMapper<PointsGiftDomain> {

    void updateGiftStatus(PointsGiftVO pointsGiftVO);

    int undercarriageNullstock(PointsGiftVO pointsGiftVO);

    PointsGiftVO selectSimpleGift(PointsGiftVO pointsGiftVO);

    List<PointsGiftVO> selectGiftList(PointsGiftVO pointsGiftVO);

    void deleteByNo(PointsGiftVO pointsGiftVO);

    void updateGift(PointsGiftVO pointsGiftVO);

    List<PointsGiftVO> selectCanConvert(PointsGiftVO pointsGiftVO);

    void updateStockNum(PointsGiftConvertRecordVO recordVO);

    /**
     * //20230928蛋品-wh-礼品-修改商家礼品信息
     * 修改商家礼品信息
     * @param pointsGiftVO
     */
    void updateMemberGift(PointsGiftVO pointsGiftVO);

    /**
     * //20230928蛋品-wh-礼品-根据礼品编号查询这个商家下共享店铺礼品库存
     * 根据礼品编号查询这个商家下共享店铺礼品库存
     * @param giftNo
     * @return
     */
    Integer selGiftStockNum(String giftNo);

    /**
     * //20230928蛋品-wh-礼品-查询商家共享店铺下门店库存数量
     * 查询商家共享店铺下门店库存数量
     * @param vo
     * @return
     */
    List<PointsGiftVO> selMerchantPointsGiftList(PointsGiftVO vo);

    /**
     * //20230928蛋品-wh-礼品-查询商家下共享店铺礼品信息
     * 查询商家下共享店铺礼品信息
     * @param vo
     * @return
     */
    List<PointsGiftVO> selectMerchantGiftList(PointsGiftVO vo);

    /**
     * //20230928蛋品-wh-礼品-查询共享店铺下门店兑换礼品信息
     * 查询共享店铺下闷蛋兑换礼品信息
     * @param pointsGiftVO
     * @return
     */
    List<PointsGiftVO> selectSharingCanConvert(PointsGiftVO pointsGiftVO);

    /**
     * //20230928蛋品-wh-礼品-查询共享店铺下闷蛋兑换礼品信息
     * 根据条件查询单条商家共享商城礼品的信息
     * @param pointsGiftVO
     * @return
     */
    PointsGiftVO selectSharingSimpleGift(PointsGiftVO pointsGiftVO);

    /**
     * //20230928蛋品-wh-礼品-修改共享店铺下礼品库存
     * 修改共享店铺下礼品库存
     * @param recordVO
     */
    void updateSharingStockNum(PointsGiftConvertRecordVO recordVO);

    /**
     * //20230928蛋品-wh-礼品-修改共享店铺下库存为0改为下架状态
     * 修改共享店铺下库存为0改为下架状态
     * @param pointsGiftVO
     * @return
     */
    int sharingUndercarriageNullstock(PointsGiftVO pointsGiftVO);

    /**
     * //20230928蛋品-wh-积分商城礼品-门店共享店铺查询商城礼品信息
     * @param pointsGiftVO
     * @return
     */
    List<PointsGiftVO> selectSharingGiftList(PointsGiftVO pointsGiftVO);

    /**
     * //20230928蛋品-wh-门店-门店共享店铺查询门店下礼品信息
     * @param pointsGiftVO
     * @return
     */
    PointsGiftVO selSharingStoreSimpleGift(PointsGiftVO pointsGiftVO);

    /**
     * //20230928蛋品-wh-门店-门店共享店铺修改门店自己上下架状态
     * @param pointsGiftVO
     */
    void updateSharingStoreStatus(PointsGiftVO pointsGiftVO);

    /**
     * 20230928蛋品-赵翔宇-切换共享会员的配置, 关闭店铺的共享会员, 若操作的店铺下的商家礼品剩余库存>0, 则不允许关闭
     *
     * @param pointsGiftVO 查询参数
     * @return 数量
     */
    int getMerchantHasStockGiftCount(PointsGiftVO pointsGiftVO);

    /**
     * 20230928蛋品-赵翔宇-商家积分, 商家或者店铺, 开启会员共享时, 开启后店铺下自建的礼品状态设置为不可用并且下架
     *
     * @param pointsGiftVO 修改入参
     * @return 修改数量
     */
    int disableMemberSharingStorePointsGift(PointsGiftVO pointsGiftVO);


    /**
     * 20230928蛋品-赵翔宇-切换共享会员的配置, 一键全部开启会员共享时, 开启后, 批量设置店铺下自建的礼品状态设置为不可用并且下架
     *
     * @param pointsGiftVOList 请求参数
     * @return 修改结果
     */
    int batchDisableStorePointsGift(@Param("pointsGiftVOList") List<PointsGiftVO> pointsGiftVOList);

    /**
     * //20230928蛋品-wh-礼品-查询商家共享店铺礼品的信息(单个)
     * 查询商家共享店铺礼品的信息(单个)
     * @param vo
     * @return
     */
    PointsGiftVO selMerchantPointsGiftDetails(PointsGiftVO vo);

    /**
     * //20230928蛋品-wh-礼品-根据店铺编号和礼品编号查询礼品信息
     * 查询商家共享店铺礼品的信息(单个)
     * @param vo
     * @return
     */
    PointsGiftVO selMerchantPointsGift(PointsGiftVO vo);
}
