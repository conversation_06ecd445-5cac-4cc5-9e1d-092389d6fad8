package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.vo.ReqMarketTouchingEffectVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 积分权益触达 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-01-05
 */
@Mapper
public interface UserPointsEffectDao extends BaseMapper {
    /**
     * 账户剩余积分>= 0的店铺粉丝数量
     * @param reqUserPointsEffectVO 请求参数
     * @return 粉丝数量
     */
    int selectHasPointsFansCount(ReqMarketTouchingEffectVO reqUserPointsEffectVO);
}
