package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.CardCountingFansDomain;
import cn.htdt.marketprocess.vo.CardCountingFansVO;
import cn.htdt.marketprocess.vo.CardCountingStoreVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 计次卡粉丝表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-13
 */
@Mapper
public interface CardCountingFansDao extends BaseMapper<CardCountingFansDomain> {

    /**
     * 查询计次卡粉丝count
     *
     * @param cardCountingFansVO
     * @return
     */
    Integer selectCardCountingFansCount(CardCountingFansVO cardCountingFansVO);

    /**
     * 根据条件查询
     *
     * @param cardCountingFansVO 查询参数
     * @return List<CardCountingFansDomain>
     */
    List<CardCountingFansVO> selectByParam(CardCountingFansVO cardCountingFansVO);

    /**
     * 根据条件查询
     *
     * @param cardCountingFansVO 查询参数
     * @return List<CardCountingFansDomain>
     */
    CardCountingFansVO selectOneByParam(CardCountingFansVO cardCountingFansVO);

    /**
     * 根据条件查询一条记录-包含删除的（目前是订单号）
     *
     * @param cardCountingFansVO 查询参数
     * @return List<CardCountingFansDomain>
     */
    CardCountingFansVO selectOneCardByParam(CardCountingFansVO cardCountingFansVO);

    /**
     * 根据订单号查询计次卡粉丝List
     *
     * @param cardCountingFansVO
     * @return
     */
    List<CardCountingFansVO> selectCardCountingFansListByOrder(CardCountingFansVO cardCountingFansVO);

    /**
     * 根据订单号查询计次卡粉丝List
     *
     * @param cardCountingFansVO
     * @return
     */
    int deleteCardCountingFansListByOrder(CardCountingFansVO cardCountingFansVO);

    /**
     * 修改计次卡剩余可用次数
     *
     * @param cardCountingFansVO 请求参数
     * @return
     */
    int updateCardCountingCardRemainCounts(CardCountingFansVO cardCountingFansVO);

    /**
     * 查询粉丝有计次卡的店铺列表
     */
    List<CardCountingStoreVO> selectStoreListByFanNo(@Param("fanNo") String fanNo, @Param("merchantNo") String merchantNo);

}
