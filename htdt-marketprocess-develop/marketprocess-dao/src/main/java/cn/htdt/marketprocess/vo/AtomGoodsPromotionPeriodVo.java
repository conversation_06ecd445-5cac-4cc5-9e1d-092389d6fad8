package cn.htdt.marketprocess.vo;

import cn.htdt.marketprocess.domain.GoodsPromotionPeriodDomain;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 商品促销活动时间段表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-22
 */
@Data
public class AtomGoodsPromotionPeriodVo extends GoodsPromotionPeriodDomain {

    private static final long serialVersionUID = 9127723104482332467L;

    /**
     * 活动编号集合
     */
    private List<String> promotionNoList;

    /**
     * 活动有效期开始时间
     */
    private LocalDateTime effectiveTime;

    /**
     * 活动有效期结束时间
     */
    private LocalDateTime invalidTime;

    /**
     * 活动来源
     */
    private String sourceType;

    /**
     * 平台活动配置的单店限购总数
     */
    private Integer storeTotalNum;

    /**
     * 活动上下架状态
     */
    private Integer upDownFlag;

    /**
     * 活动场次编码集合
     */
    List<String> periodNoList;
}
