package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.PointsConfigDomain;
import cn.htdt.marketprocess.vo.PointsConfigVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 积分配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-26
 */
@Mapper
public interface PointsConfigDao extends BaseMapper<PointsConfigDomain> {

    int updatePointsConfig(PointsConfigDomain pointsConfigDomain);

    List<PointsConfigDomain> selectStorePointsConfig(PointsConfigDomain pointsConfigDomain);

    List<PointsConfigDomain> selectMerchantPointsConfig(PointsConfigDomain pointsConfigDomain);

    PointsConfigDomain selectPointsConfigByConfigNo(PointsConfigVO pointsConfigVO);

}
