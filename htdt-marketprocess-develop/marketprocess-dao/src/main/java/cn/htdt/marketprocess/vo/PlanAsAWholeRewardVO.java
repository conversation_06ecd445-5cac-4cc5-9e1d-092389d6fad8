package cn.htdt.marketprocess.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class PlanAsAWholeRewardVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 代理人佣金 -> 对应数据库sum(reward_value)
     * 云池 - 汇推广 - 店铺分销订单（商家、店铺）
     */
    private BigDecimal agentRewardValueSum;

    /**
     * 订单总佣金 -> 对应数据库sum(reward_value + distribution_reward_value + htdt_reward_value)
     * 云池 - 汇推广 - （供货店）云池供货订单（商家、店铺）
     */
    private BigDecimal storeTotalRewardValueSum;

    /**
     * 订单入账金额 -> 对应数据库sum(supply_shop_sharing_profit)
     * 云池 - 汇推广 - （供货店）云池供货订单（商家、店铺）
     */
    private BigDecimal storeToBeInRewardValueSum;

    /**
     * 订单待返金额 -> 对应数据库sum(return_supply_shop_money)
     * 云池 - 汇推广 - （供货店）云池供货订单（商家、店铺）
     */
    private BigDecimal storeToBeReturnRewardValueSum;

    /**
     * 本店获得佣金 -> 对应数据库sum(distribution_reward_value)
     * 云池 - 赚佣金 - (分销店)总部分销订单（商家、店铺）
     */
    private BigDecimal disStoreRewardValueSum;
}