package cn.htdt.marketprocess.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class GoodsNoByCommissionVO implements Serializable {

    /**
     * 商品编号
     */
    private String taskOrGoodsNo;

    /**
     * 酬劳类型，1佣金 2汇金币 3礼品 4专享现金券 5服务券 6话费券，对应枚举RewardTypeEnum
     */
    private Integer rewardType;

    /**
     * 佣金或汇金币 根据reward_type来取值
     */
    private BigDecimal yjOrHjb;
}
