package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.AgentMarketBillDomain;
import cn.htdt.marketprocess.vo.AgentNumShareSumVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 任务和营销清单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-11
 */
@Mapper
public interface AgentMarketBillDao extends BaseMapper<AgentMarketBillDomain> {

    AgentMarketBillDomain selectAgentMarketBill(AgentMarketBillDomain agentMarketBillDomain);

    /**
     * <AUTHOR>
     * @Description 统计当天店铺代理人分享次数
     * @Date 2021/6/30
     * @Param [agentMarketBillDomain]
     * @return int
     **/
    int selectshareSumByStoreNo(AgentMarketBillDomain agentMarketBillDomain);

    /**
     * <AUTHOR>
     * @Description 近30天分享的代理人数和分享次数
     * @Date 2021/6/30
     * @Param [agentMarketBillDomain]
     * @return int
     **/
    AgentNumShareSumVO selectAgentNumShareSumByStoreNo(AgentMarketBillDomain agentMarketBillDomain);
}