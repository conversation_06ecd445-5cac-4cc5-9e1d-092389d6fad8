package cn.htdt.marketprocess.domain;

import cn.htdt.common.domain.BaseDomain;
import cn.htdt.common.dto.annon.Encrypt;
import cn.htdt.common.dto.annon.EncryptField;
import cn.htdt.common.dto.enums.HandlerPolicy;
import cn.htdt.common.dto.enums.MaskType;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 膨胀红包发起记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Data
@Encrypt
@EqualsAndHashCode(callSuper = true)
@TableName("expand_record")
public class ExpandRecordDomain extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 膨胀编号
     */
    private String expandNo;

    /**
     * 活动编号
     */
    private String promotionNo;

    /**
     * 粉丝编号
     */
    private String fanNo;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    @EncryptField(handlerPolicy= HandlerPolicy.MASK, maskType = MaskType.MOBILE)
    private String phone;

    /**
     * 手机号
     */
    @EncryptField
    private String dsPhone;
    /**
     * 分享人头像
     */
    private String nickName;

    /**
     * 分享人昵称
     */
    private String headImg;
    /**
     * 使用门槛阈值（满减时表示满多少元）
     */
    private BigDecimal discountThreshold;

    /**
     * 膨胀金额
     */
    private BigDecimal expandMoney;

    /**
     * 兑换门槛阈值（满多少元可兑换）
     */
    private BigDecimal exchangeThreshold;

    /**
     * 兑换状态(1:未兑换 2:已兑换)
     */
    private Integer exchangeStatus;

    /**
     * 兑换时间
     */
    private LocalDateTime exchangeTime;

    /**
     * 券使用状态(1:未使用 2:已使用)
     */
    private Integer couponUseStatus;

    /**
     * 用户券编号
     */
    private String userCouponNo;

    /**
     * 是否可用:默认1，1：可用，其余不可用
     */
    private Integer disableFlag;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;


}
