package cn.htdt.marketprocess.vo;

import cn.htdt.marketprocess.domain.VirtualCoinRuleDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 查询橙豆规则和对应的优惠券信息
 * <AUTHOR>
 * @date 2023-01-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class VirtualCoinRuleAndCouponInfoVo extends VirtualCoinRuleDomain {

    /**
     * 券总数量
     */
    private Integer couponTotalCount;

    /**
     * 使用门槛阈值（满减时表示满多少元）
     */
    private BigDecimal discountThreshold;

    /**
     * 券总面额
     */
    private BigDecimal couponTotalValue;

    /**
     * 优惠券类型 （1001:满减券 1002:折扣券 1003:现金券）
     */
    private String couponType;

    /**
     * 券使用渠道 (1001:汇享购下单 1002:门店下单)
     */
    private String couponUseChannel;

    /**
     * 促销类型 3001:平台-手动发代理人券 3002::平台-手动发粉丝券 3003:平台-网店代理人领券 3004:平台-网店粉丝领券 3005:店铺-手动发粉丝券 3006:店铺-网店粉丝领券 3007:膨胀红包 3100:抽奖券
     *
     * 参考枚举: PromotionTypeCouponEnum
     */
    private String promotionCouponType;

    /**
     * 券有效期, 1001：长期有效 1002：指定日期 1003:自领取后天数有效
     */
    private String couponPeriodValidity;
}
