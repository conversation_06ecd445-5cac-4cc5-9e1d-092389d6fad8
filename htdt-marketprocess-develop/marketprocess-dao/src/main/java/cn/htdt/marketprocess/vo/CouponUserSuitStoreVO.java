package cn.htdt.marketprocess.vo;

import cn.htdt.marketprocess.domain.CouponUserSuitStoreDomain;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * <p>
 * 用户优惠券适用店铺表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CouponUserSuitStoreVO对象", description = "用户优惠券适用店铺表")
public class CouponUserSuitStoreVO extends CouponUserSuitStoreDomain {

    private static final long serialVersionUID = 1L;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
