package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.CouponBrandRelationDomain;
import cn.htdt.marketprocess.vo.CouponBrandRelationVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 优惠券品牌关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-01
 */
@Mapper
public interface CouponBrandRelationDao extends BaseMapper<CouponBrandRelationDomain> {

    /**
      * @param couponBrandRelationDomain
      * @Description : 根据条件查询优惠券品牌关联表列表
      * <AUTHOR> 高繁
      * @date : 2021/4/28 19:24
     */
    List<CouponBrandRelationDomain> selectCouponBrandRelationList(CouponBrandRelationVO couponBrandRelationDomain);


    /**
     * 批量删除优惠券品牌活动
     *
     * @param brandNos
     * @param couponNo
     */
    void batchDeleteCouponBrandRelation(@Param("brandNos") List<String> brandNos, @Param("couponNo") String couponNo);

}
