package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.CouponSendRecordDomain;
import cn.htdt.marketprocess.vo.AtomCouponSendRecordVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 优惠券发送记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-02
 */
@Mapper
public interface CouponSendRecordDao extends BaseMapper<CouponSendRecordDomain> {

    /**
     * 分页查询发券记录
     *
     * @auther 卜金隆
     */
    List<CouponSendRecordDomain> selectCouponSendRecordByParam(AtomCouponSendRecordVO couponSendRecordVO);
}
