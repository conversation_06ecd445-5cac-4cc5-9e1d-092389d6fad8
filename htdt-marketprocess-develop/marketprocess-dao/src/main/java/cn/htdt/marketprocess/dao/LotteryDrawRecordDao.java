package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.LotteryDrawRecordDomain;
import cn.htdt.marketcenter.dto.request.AtomReqLotteryDrawRecordDTO;
import cn.htdt.marketprocess.vo.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 活动中奖记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-08
 */
@Mapper
public interface LotteryDrawRecordDao extends BaseMapper<LotteryDrawRecordDomain> {

    /**
     * 统计抽奖记录
     *
     * @param atomReqLotteryDrawRecordDTO
     * @return
     */
    List<CountTotalRecordVO> countTotalRecord(AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO);

    /**
     * 统计抽奖记录
     * 关联奖品信息表
     *
     * @param atomReqLotteryDrawRecordDTO
     * @return
     */
    List<CountTotalRecordVO> countTotalAllRecord(AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO);


    /**
     * @param atomReqLotteryDrawRecordDTO
     * @Description : 中奖记录列表
     * <AUTHOR> 高繁
     * @date : 2021/3/11 14:28
     */
    List<LotteryRewardRecordVo> selectLotteryDrawRecordList(AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO);

    /**
     * @param atomReqLotteryDrawRecordDTO
     * @Description : BossApp->抽奖列表->中奖记录统计
     * <AUTHOR> 卜金隆
     * @date : 2021/7/06 19:02
     */
    List<LotteryDrawRecordCountVO> selectLotteryDrawRecordCountList(AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO);
    /**
     * @param atomReqLotteryDrawRecordDTO
     * @Description : 中奖记录数量
     * <AUTHOR> 王磊
     * @date : 2021/4/26 14:28
     */
    Integer selectLotteryDrawRecordCount(AtomReqLotteryDrawRecordDTO atomReqLotteryDrawRecordDTO);

    /**
     * @param lotteryDrawRecordVO
     * @Description : 根据活动编码List批量获取中奖记录数量
     * <AUTHOR> 王磊
     * @date : 2021/4/26 14:28
     */
    List<LotteryDrawRecordVO> batchSelectLotteryDrawRecordCount(LotteryDrawRecordVO lotteryDrawRecordVO);

    /**
     * @param lotteryDrawRecordVO
     * @Description : 批量获取中奖记录
     * <AUTHOR> 王磊
     * @date : 2021/4/26 14:28
     */
    List<LotteryDrawRecordVO> batchSelectLotteryDrawRecord(LotteryDrawRecordVO lotteryDrawRecordVO);

    /**
      * @param lotteryRewardInfoVo
      * @Description : 中奖记录作废
      * <AUTHOR> 高繁
      * @date : 2021/3/12 15:03
     */
    Integer updateLotteryDrawRecordStatus(LotteryRewardInfoVo lotteryRewardInfoVo);

    /**
      * @param lotteryRewardRecordVo
      * @Description : 查询核销实物奖品列表
      * <AUTHOR> 高繁
      * @date : 2021/3/19 10:41
     */
    List<LotteryRewardRecordVo> selectWriteOffList(LotteryRewardRecordVo lotteryRewardRecordVo);

    /**
     * 根据订单号查询核销实物奖品列表-批量
     * @param lotteryRewardRecordVo
     * <AUTHOR> 王磊
     * @date : 2021/3/29 10:41
     */
    List<LotteryRewardRecordVo> selectWriteOffListByOrderNos(LotteryRewardRecordVo lotteryRewardRecordVo);

    /**
     * 根据订单号查询核销实物奖品-单条
     * @param lotteryRewardRecordVo
     * <AUTHOR> 王磊
     * @date : 2021/3/29 10:41
     */
    LotteryRewardRecordVo selectWriteOffListByOrderNo(LotteryRewardRecordVo lotteryRewardRecordVo);

    /**
     * 中奖记录-中奖核销-单条
     * <AUTHOR> 王磊
     * @param lotteryRewardRecordVo
     * @return
     */
    int updateLotteryDrawRecordWriteOff(LotteryRewardRecordVo lotteryRewardRecordVo);

    /**
     * 中奖记录-中奖核销-批量
     * <AUTHOR> 王磊
     * @param lotteryRewardRecordVos
     * @return
     */
    int batchUpdateLotteryDrawRecordWriteOff(@Param("lotteryRewardRecordVos") List<LotteryRewardRecordVo> lotteryRewardRecordVos);

    /**
     * 批量更新抽奖记录
     *
     * @param atomReqLotteryDrawRecordDTOList
     * @return
     */
    int batchUpdateLotteryDrawRecord(@Param("atomReqLotteryDrawRecordDTOList") List<AtomReqLotteryDrawRecordDTO> atomReqLotteryDrawRecordDTOList);


    /**
     * @param reqRecordVO
     * @Description : 查询活动下中奖次数
     * <AUTHOR> 高繁
     * @date : 2021/4/9 15:29
     */
    LotteryRewardRecordVo selectDrawNumByPromotion(LotteryRewardRecordVo reqRecordVO);

    /**
     * @param reqRecordVO
     * @Description : 查询某奖品下中奖次数
     * <AUTHOR> 王磊
     * @date : 2021/4/9 15:29
     */
    LotteryRewardRecordVo selectRecordCountByTerm(LotteryRewardRecordVo reqRecordVO);

    /**
     * @Description 查询核销码在当前会员店是否存在
     * <AUTHOR>
     * @Date 2021/4/13 11:02
     * @Param lotteryRewardRecordVo
     * @Return
    */
    LotteryRewardRecordVo selectWriteOffCodeIsExist(LotteryRewardRecordVo lotteryRewardRecordVo);

    /**
      * @param recordNoList
      * @Description : 根据recordNo集合查询订单编号集合
      * <AUTHOR> 高繁
      * @date : 2021/5/18 11:33
     */
    List<String> selectOrderNoByRecordNoList(@Param("recordNoList") List<String> recordNoList);

    /**
      * @param lotteryRewardRecordVo
      * @Description : 根据中奖记录编号集合或者订单编号集合查询中奖记录
      * <AUTHOR> 高繁
      * @date : 2021/9/8 11:32
     */
    List<LotteryDrawRecordDomain> selectRewardRecordList(LotteryRewardRecordVo lotteryRewardRecordVo);

    int selectFansUnusedCoupons(@Param("fansNo") String fansNo);

    int selectUnconvertedGift(@Param("fansNo") String fansNo);

    List<LotteryDrawRecordDomain> selectRewardRecordListBySourceOrderNo(@Param("sourceOrderNo")String sourceOrderNo);

    /**
     * 蛋品, 查询商家抽奖订单中奖记录
     *
     * @param lotteryRewardRecordVo 查询参数
     * @return 商家抽奖, 中奖记录
     */
    List<LotteryDrawRecordDomain> selectMerchantLotteryDrawRecord(LotteryRewardRecordVo lotteryRewardRecordVo);

    /**
     * 更新中奖记录中的优惠券的使用状态
     *
     * @param lotteryRewardRecordVo
     * <AUTHOR>
     * @date 2022-01-28
     */
    int updateLotteryDrawRecordCoupon(LotteryRewardRecordVo lotteryRewardRecordVo);

    LotteryDrawRecordDomain selectRewardInfoWriteOff(LotteryRewardRecordVo lotteryRewardRecordVo);
}
