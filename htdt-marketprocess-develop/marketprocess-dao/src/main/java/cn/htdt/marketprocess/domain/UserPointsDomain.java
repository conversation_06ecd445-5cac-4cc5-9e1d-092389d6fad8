package cn.htdt.marketprocess.domain;

import cn.htdt.common.domain.BaseDomain;
import cn.htdt.common.dto.annon.Encrypt;
import cn.htdt.common.dto.annon.EncryptField;
import cn.htdt.common.dto.enums.HandlerPolicy;
import cn.htdt.common.dto.enums.MaskType;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 用户积分表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-26
 */
@Encrypt
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_points")
public class UserPointsDomain extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 粉丝编号
     */
    private String fansNo;

    /**
     * 手机号
     */
    @EncryptField(handlerPolicy= HandlerPolicy.MASK, maskType = MaskType.MOBILE)
    private String phone;

    /**
     * 手机号
     */
    @EncryptField
    private String dsPhone;

    /**
     * 姓名
     */
    private String name;

    /**
     * 累计获取积分
     */
    private Integer accumulateGainPoints;

    /**
     * 累计扣减积分
     */
    private Integer accumulateReducePoints;

    /**
     * 账户剩余积分
     */
    private Integer accountRemainPoints;

    /**
     * 是否可用:默认1，1：可用，其余不可用
     */
    private Integer disableFlag;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 创建人ID
     */
    private String createNo;

    /**
     * 最后一次修改人ID
     */
    private String modifyNo;

    /**
     * 积分类型, 1-店铺, 2-商家
     */
    private String ruleType;
}
