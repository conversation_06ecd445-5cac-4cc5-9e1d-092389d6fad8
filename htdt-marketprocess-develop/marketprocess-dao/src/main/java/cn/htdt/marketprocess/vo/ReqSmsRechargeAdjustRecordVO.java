package cn.htdt.marketprocess.vo;

import cn.htdt.marketprocess.domain.SmsRechargeAdjustRecordDomain;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 短信条数调整操作记录表 VO
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="ReqSmsRechargeAdjustRecordVO对象", description="短信条数调整操作记录")
public class ReqSmsRechargeAdjustRecordVO extends SmsRechargeAdjustRecordDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 店铺编号
     */
    private String storeNo;

    /**
     * 调整起始时间
     */
    private String adjustTimeStart;

    /**
     * 调整截止时间
     */
    private String adjustTimeEnd;
}
