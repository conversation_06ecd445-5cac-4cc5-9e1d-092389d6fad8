package cn.htdt.marketprocess.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 橙豆列表，响应参数
 * <AUTHOR>
 * @date 2023-01-16
 */
@Data
public class ResVirtualCoinAndLatestRecordVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 粉丝编号
     */
    private String fanNo;

    /**
     * 账户剩余橙豆
     */
    private BigDecimal accountRemainCoins;

    /**
     * 上次充值橙豆数额
     */
    private BigDecimal lastedRechargeVirtualCoin;

    /**
     * 上次充值时间
     */
    private LocalDateTime lastedRechargeVirtualCoinTime;

    /**
     * 上次消费橙豆数额
     */
    private BigDecimal lastedConsumeVirtualCoin;

    /**
     * 上次消费时间
     */
    private LocalDateTime lastedConsumeVirtualCoinTime;
}
