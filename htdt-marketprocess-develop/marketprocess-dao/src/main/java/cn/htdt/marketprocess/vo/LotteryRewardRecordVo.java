package cn.htdt.marketprocess.vo;

import cn.htdt.marketprocess.domain.LotteryDrawRecordDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 活动中奖信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="LotteryRewardRecordVo", description="活动中奖信息表")
public class LotteryRewardRecordVo extends LotteryDrawRecordDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 活动类型 1000:抽奖  2000:商品促销 3000:优惠券
     */
    private String activityType;

    /**
     * 订单编号集合
     */
    private List<String> orderNos;
    /**
     * 今日中奖次数
     */
    private Integer todayDrawNum;
    /**
     * 总中奖次数
     */
    private Integer allDrawNum;
    /**
     * 总抽奖次数
     */
    private Integer allLotteryNum;
    /**
     * 总抽奖人数
     */
    private Integer allLotteryUserNum;

    /**
     * 中奖开始时间
     */
    private LocalDateTime startTime;
    /**
     * 中奖结束时间
     */
    private LocalDateTime endTime;

    /**
     * 促销类型 1001:大转盘 1002:摇奖机1003:拆盲盒1004:套圈圈 1005:小猫钓鱼 1006:膨胀红包 2001:秒杀 2002:拼团 2003:预售 2004:单品促销 2005:满额促销 2006:满量促销 2007:满额赠 2008:套餐 3001:平台-手动发代理人券 3002::平台-手动发粉丝券 3003:平台-网店代理人领券 3004:平台-网店粉丝领券 3005:店铺-手动发粉丝券 3006:店铺-网店粉丝领券 3007:膨胀红包 3100:抽奖券 4001:报名活动
     */
    private String promotionType;

    /**
     * 中奖记录集合
     */
    private List<String> recordNos;

    /**
     * 不过滤已作废奖品 1：过滤 2：不过滤
     */
    private Integer filterFlag;

    @ApiModelProperty("订单号或者商品名称综合字段")
    private String orderNoOrGoodsName;

    @ApiModelProperty("核销码")
    private String writeOffCode;

    @ApiModelProperty(value = "来源 1001：平台  1003：店铺")
    private List<String> sourceTypes;
}
