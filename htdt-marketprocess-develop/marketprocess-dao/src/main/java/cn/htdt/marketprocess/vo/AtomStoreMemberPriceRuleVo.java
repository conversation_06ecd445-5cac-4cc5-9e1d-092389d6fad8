package cn.htdt.marketprocess.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Data
public class AtomStoreMemberPriceRuleVo implements Serializable {

    /***活动基本信息**/
    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "活动名称")
    private String promotionName;

    @ApiModelProperty(value = "来源 1001：平台 1002：商家 1003：店铺")
    private String sourceType;

    @ApiModelProperty(value = "上下架状态（1：未上架 2：上架）")
    private Integer upDownFlag;

    @ApiModelProperty(value = "活动状态 1001:活动未开始 1002:活动进行中，1003:活动已结束")
    private String status;

    @ApiModelProperty(value = "活动开始时间")
    private LocalDateTime effectiveTime;

    @ApiModelProperty(value = "活动结束时间")
    private LocalDateTime invalidTime;

    @ApiModelProperty(value = "店铺上下架状态（1：未上架 2：上架）")
    private Integer storeUpDownFlag;

    @ApiModelProperty(value = "每场开始时间")
    private LocalTime startTime;

    @ApiModelProperty(value = "每场结束时间")
    private LocalTime endTime;

    @ApiModelProperty(value = "周期模式:1001每天重复,1002每周重复,1003每月重复")
    private String repectType;

    @ApiModelProperty(value = "周期值:W1-W7周日-周六;M1-M31每月1-31日")
    private String repectVal;

    @ApiModelProperty(value = "商品范围(1001:自定义配置商品 1002:复用其他时间段商品)，默认是1001")
    private String goodsScope;

    @ApiModelProperty(value = "商品复用时段编号")
    String copyGoodsPeriodNo;

    @ApiModelProperty(value = "是否可用:默认1，1：可用(未退出共享会员店)，其余不可用(退出共享)")
    private Integer disableFlag;













    @ApiModelProperty(value = "活动短链接")
    private String shortLink;

    @ApiModelProperty(value = "图片类型(1:默认图片 2:自定义图片)")
    private Integer promotionPictureType;

    @ApiModelProperty(value = "图片url")
    private String promotionPictureUrl;

    @ApiModelProperty(value = "图片活动名称")
    private String promotionActityName;

    @ApiModelProperty(value = "活动对象 1001: 不限制 1002: 仅限老用户 1003: 仅限新用户")
    private String userScope;

    @ApiModelProperty(value = "活动对象 1001: 不限制 1002: 仅限老用户 1003: 仅限新用户")
    private String joinUserScope;

    @ApiModelProperty(value = "活动说明")
    private String promotionExplain;

    @ApiModelProperty(value = "活动时间段类型 (1001:全天 1002:指定时间段)")
    private String effectivePeriodType;



    @ApiModelProperty(value = "每日开始时间")
    private LocalTime dailyStartTime;

    @ApiModelProperty(value = "每日结束时间")
    private LocalTime dailyEndTime;



    @ApiModelProperty(value = "活动类型 1000:抽奖  2000:商品促销 3000:优惠券")
    private String activityType;

    @ApiModelProperty(value = "促销类型 1001:大转盘 1002:套圈圈 1003:打地鼠 1004:扭蛋机 1005:刮刮乐 1006:膨胀红包 2001:秒杀 2002:拼团 2003:预售 2004:单品促销 2005:满额促销 2006:满量促销 2007:满额赠 2008:套餐 3001:平台-手动发代理人券 3002::平台-手动发粉丝券 3003:平台-网店代理人领券 3004:平台-网店粉丝领券 3005:店铺-手动发粉丝券 3006:店铺-网店粉丝领券 3007:膨胀红包 3100:抽奖券 4001:报名活动")
    private String promotionType;



    @ApiModelProperty(value = "活动有效期（1001：长期有效 1002：指定日期）")
    private String periodValidity;


    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "门店编号，对应orgId")
    private String storeNo;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;



    @ApiModelProperty(value = "有效期-开始时间")
    private String effectiveTimeStr;

    @ApiModelProperty(value = "有效期-结束时间")
    private String invalidTimeStr;

    @ApiModelProperty(value = "商家下所有店铺编号")
    private List<String> storeNos;

    @ApiModelProperty(value = "活动店铺类型(1001:全部店铺 1002:报名活动)")
    private String storeType;

    @ApiModelProperty(value = "活动类型集合")
    private List<String> promotionTypeList;

    @ApiModelProperty(value = "活动编码集合")
    private List<String> promotionNoList;

    @ApiModelProperty(value = "创建来源 1000:PC  2000:APP")
    private String createSource;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime modifyTime;

    @ApiModelProperty(value = "参与店铺的活动是否被平台删除标识，1:未删除，2:删除")
    private Integer storeDeleteFlag;

    @ApiModelProperty(value = "活动对象集合 1001: 不限制 1002: 仅限老用户 1003: 仅限新用户")
    private List<String> userScopeList;

    /**
     * 限时购活动场次编号
     */
    private String periodNo;

    @ApiModelProperty(value = "搜索来源 1001：秒杀首页 1002：活动列表页")
    private String searchType;

    /**
     * 类目编号
     */
    private String categoryNo;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 查询状态时是否精确匹配时间，1否2是，精确时匹配到时分秒
     */
    private Integer accurateTimeFlag;

    @ApiModelProperty(value = "礼品卡类型:1001实体卡;1002电子卡")
    private String cardType;

    /**
     * 忽略deleteflag,2是，其他否，查询被删除数据
     */
    private Integer ignoreDeleteFlag;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 商品编码List
     */
    private List<String> goodsNos;

}
