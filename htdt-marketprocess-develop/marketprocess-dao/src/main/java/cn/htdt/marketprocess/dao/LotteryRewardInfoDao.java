package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.LotteryRewardInfoDomain;
import cn.htdt.marketprocess.vo.LotteryRewardInfoVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 活动奖品信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-08
 */
@Mapper
public interface LotteryRewardInfoDao extends BaseMapper<LotteryRewardInfoDomain> {

    /**
     * 通过活动编码查询信息
     * @param lotteryRewardInfoVo
     * @return
     */
    List<LotteryRewardInfoVo> selectLotteryRewardInfo(LotteryRewardInfoVo lotteryRewardInfoVo);

    /**
     * 通过活动编码查询信息-单条
     * @param lotteryRewardInfoVo
     * @return
     */
    LotteryRewardInfoVo selectOneLotteryRewardInfo(LotteryRewardInfoVo lotteryRewardInfoVo);

    /**
     * 通过活动编码查询列表信息
     * @param lotteryRewardInfoDomain
     * @return
     */
    List<LotteryRewardInfoDomain> selectLotteryRewardInfoList(LotteryRewardInfoDomain lotteryRewardInfoDomain);
    /**
     * 根据活动编码是否存在商品配置
     * @param lotteryRewardGoodsRelationVo
     * @return
     */
    Integer countLotteryGoodsNum(LotteryRewardInfoVo lotteryRewardGoodsRelationVo);
    /**
     * 批量删除活动编码查询信息
     * @param lotteryRewardInfoVo
     * @return
     */
    void batchDeleteLotteryRewardInfo(LotteryRewardInfoVo lotteryRewardInfoVo);

    /**
     * 批量删除活动编码查询信息
     * @param lotteryRewardInfoVo
     * @return
     */
    void batchUpdateLotteryRewardInfo(LotteryRewardInfoVo lotteryRewardInfoVo);

    /**
     * 更新活动奖品信息
     * @param lotteryRewardInfoVo
     * @return
     */
    void updateRewardInfo(LotteryRewardInfoVo lotteryRewardInfoVo);

    /**
      * @param lotteryRewardInfoList
      * @Description : 批量修改奖项设置
      * <AUTHOR> 高繁
      * @date : 2021/9/15 18:08
     */
    void batchUpdateLotteryReward(@Param("lotteryRewardInfoList") List<LotteryRewardInfoDomain> lotteryRewardInfoList);

    /**
     * 根据活动编码查询剩余奖品库存总数
     * @param lotteryRewardGoodsRelationVo
     * @return
     */
    Integer countResidueLotteryNum(LotteryRewardInfoVo lotteryRewardGoodsRelationVo);
}
