package cn.htdt.marketprocess.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 橙豆列表最新充值和消费的橙豆数量和时间，响应参数
 * <AUTHOR>
 * @date 2023-01-16
 */
@Data
public class ResVirtualCoinLatestRecordVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 粉丝编号
     */
    private String fanNo;

    /**
     * 上次充值橙豆数额
     */
    private BigDecimal latestRechargeVirtualCoin;

    /**
     * 上次充值时间
     */
    private LocalDateTime latestRechargeVirtualCoinTime;

    /**
     * 上次消费橙豆数额
     */
    private BigDecimal latestConsumeVirtualCoin;

    /**
     * 上次消费时间
     */
    private LocalDateTime latestConsumeVirtualCoinTime;
}
