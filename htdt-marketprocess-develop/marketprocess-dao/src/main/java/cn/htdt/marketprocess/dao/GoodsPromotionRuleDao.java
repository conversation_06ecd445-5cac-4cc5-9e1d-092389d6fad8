package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.GoodsPromotionRuleDomain;
import cn.htdt.marketprocess.vo.AtomGoodsPromotionGoodsRelationVo;
import cn.htdt.marketprocess.vo.AtomGoodsPromotionRuleVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 签到活动表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-22
 */
@Mapper
public interface GoodsPromotionRuleDao extends BaseMapper<GoodsPromotionRuleDomain> {

    /**
     * 查询商品促销活动列表
     *
     * @param vo
     * @return
     */
    List<AtomGoodsPromotionRuleVo> selectByParams(AtomGoodsPromotionRuleVo vo);

    /**
     * 查询商品促销活动列表-根据活动编号list批量获取
     *
     * @param vo
     * @return
     */
    List<AtomGoodsPromotionRuleVo> batchSelectByParams(AtomGoodsPromotionRuleVo vo);

    /**
     * 功能描述: 查询商品促销活动详情
     *
     * @param vo
     * @return AtomGoodsPromotionRuleVo
     * @author: 张宇
     * @date: 2021/7/4 15:35
     */
    AtomGoodsPromotionRuleVo selectByPromotionNo(AtomGoodsPromotionRuleVo vo);

    /**
     * 根据店铺编号查询有效促销活动列表-汇享购
     *
     * @param vo
     * @return
     */
    List<AtomGoodsPromotionRuleVo> selectListForHxg(AtomGoodsPromotionRuleVo vo);

    /**
     * 根据活动编号，更新促销活动规则信息
     *
     * @param goodsPromotionRuleDomain
     * @return
     * <AUTHOR> 杨子建
     * @date 2021-06-28
     */
    void updateRuleByPromotionNo(GoodsPromotionRuleDomain goodsPromotionRuleDomain);

    /**
     * 商家查询平台的促销活动
     *
     * @param vo
     * @return
     * <AUTHOR>
     * @date 2021-07-01
     */
    List<AtomGoodsPromotionRuleVo> merchantGetPlatformPromotion(AtomGoodsPromotionRuleVo vo);

    /**
     * 商家查询平台的促销活动
     *
     * @param vo
     * @return
     * <AUTHOR>
     * @date 2021-07-01
     */
    List<AtomGoodsPromotionRuleVo> storeGetPlatformPromotion(AtomGoodsPromotionRuleVo vo);

    /**
     * 删除商品促销活动规则
     *
     * @param promotionNo
     * @return
     * <AUTHOR>
     * @date 2021-07-05
     */
    void deleteGoodsPromotionRule(String promotionNo);

    /**
     * 根据店铺编号查询有效限时购活动列表-汇享购
     * <p>
     * 商品预售活动共用
     *
     * @param vo 查询参数
     * @return 限时购活动列表
     * <AUTHOR>
     * @date 2021-09-26
     */
    List<AtomGoodsPromotionRuleVo> selectLimitTimePromotionForHxg(AtomGoodsPromotionRuleVo vo);

    /**
     * 汇享购首页查询限时购活动信息
     *
     * @param vo 查询参数
     * @return 限时购活动列表
     * <AUTHOR>
     * @date 2021-09-28
     */
    List<AtomGoodsPromotionRuleVo> selectLimitTimeForHxgIndex(AtomGoodsPromotionRuleVo vo);

    /**
     * 千橙掌柜收银-商品列表-查询进行中的满减满折活动
     *
     * @param vo 查询参数
     * @return 进行中的满减满折活动列表
     * <AUTHOR>
     * @date 2022-02-17
     */
    List<AtomGoodsPromotionRuleVo> selectFullDiscountForSmartCashier(AtomGoodsPromotionRuleVo vo);

    /**
     * 查询满减满折活动列表
     *
     * @param vo 查询参数
     * @return 进行中的满减满折活动列表
     * <AUTHOR>
     * @date 2022-02-21
     */
    List<AtomGoodsPromotionRuleVo> selectFullDiscountList(AtomGoodsPromotionRuleVo vo);

    /**
     * 查询满减满折活动基本信息
     *
     * <AUTHOR>
     * @date 2022-02-21
     */
    List<AtomGoodsPromotionRuleVo> selectFullDiscountByPromotionNo(AtomGoodsPromotionRuleVo vo);

    /**
     * 店铺根据条件查询满减满折活动数组 时间段范围内的活动
     *
     * <AUTHOR>
     * @date 2022-02-22
     */
    List<AtomGoodsPromotionRuleVo> selectFullDiscountByParam(AtomGoodsPromotionRuleVo vo);

    List<AtomGoodsPromotionGoodsRelationVo> selectVipPriceByParam(@Param("ruleVo") AtomGoodsPromotionRuleVo vo, @Param("goodsNos") List<String> goodsNos);

    /**
     * 根据商品查询存在商品活动信息
     * @param vo
     * @return
     */
    List<AtomGoodsPromotionGoodsRelationVo> selectMemberPriceByTerm(AtomGoodsPromotionRuleVo vo);

    /**
     * 查询商家接龙活动
     * @param vo
     * @return
     */
    List<AtomGoodsPromotionRuleVo> selectSJPromotionList(AtomGoodsPromotionRuleVo vo);
    
    /**
     * 查询商家社群接龙活动列表
     * @param vo
     * @return
     */
    List<AtomGoodsPromotionRuleVo> selectPromotionByParams(AtomGoodsPromotionRuleVo vo);

}
