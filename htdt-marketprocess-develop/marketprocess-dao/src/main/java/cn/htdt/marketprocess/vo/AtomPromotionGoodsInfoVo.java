package cn.htdt.marketprocess.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;

@Data
public class AtomPromotionGoodsInfoVo {
    /**
     * 折扣率
     */
    private String discountRate;
    /**
     * 活动编号
     */
    private String promotionNo;

    /**
     * 每场开始时间字符串，格式为HH:mm:ss
     */
    private LocalTime startTime;

    /**
     * 每场结束时间字符串，格式为HH:mm:ss
     */
    private LocalTime endTime;

    /**
     * 活动名称
     */
    private String promotionName;

    /**
     * 促销类型，2001:秒杀
     */
    private String promotionType;

    /**
     * 活动有效期的开始时间
     */
    private LocalDateTime effectiveTime;

    /**
     * 活动有效期的结束时间
     */
    private LocalDateTime invalidTime;

    /**
     * 上下架状态（1：未上架 2：上架）
     */
    private Integer upDownFlag;

    /**
     * 时间段编号
     */
    private String periodNo;

    /**
     * 商品编号
     */
    private String goodsNo;

    /**
     * 活动价格（秒杀价、拼团价、预售价、搭配价、特惠促销）
     */
    private BigDecimal promotionPrice;

    /**
     * 优惠折扣(满减时是金额，满折时是折扣，全场会员价是折扣)
     */
    private BigDecimal discountMoney;

}
