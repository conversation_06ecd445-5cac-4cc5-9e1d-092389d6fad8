package cn.htdt.marketprocess.domain;

import cn.htdt.common.domain.BaseDomain;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <p>
 * 积分配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("points_config")
public class PointsConfigDomain extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 配置编号
     */
    private String configNo;

    /**
     * 配置类型(1001:积分获得上限 1002:粉丝下单配置 1003:关注店铺赠积分  2001:积分兑换礼品 2002:积分兑换抽奖机会,2003:积分支付抵扣)
     */
    private String configType;

    /**
     * 积分上限
     */
    private Integer pointsLimit;

    /**
     * 订单实付金额每满（元）
     */
    private BigDecimal orderAmount;

    /**
     * 赠送积分数（个）
     */
    private Integer presentPoints;

    /**
     * 积分抵扣比例默认抵扣1分钱
     */
    private Integer deductionScale;
    /**
     * 订单金额门槛：1001-不限制，1002-限制
     */
    private String orderAmountLimit;
    /**
     * 订单应付金额（元）
     */
    private BigDecimal orderHandleAmount;
    /**
     * 抵扣金额门槛：1001-不限制，1002-限制
     */
    private String deductionAmountLimit;
    /**
     * 积分抵扣上限
     */
    private Integer pointsDeductionLimit;
    /**
     * 积分适用渠道(1001:一体机开单 1002:app及pc开单 1003:汇享购网店订单)
     */
    private String useChannel;
    /**
     * 是否可用:默认1，1：启用，2：禁用
     */
    private Integer disableFlag;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 创建人ID
     */
    private String createNo;

    /**
     * 最后一次修改人ID
     */
    private String modifyNo;

    /**
     * 积分配置类型（1.门店，2.商家）
     */
    private String pointsType;
}
