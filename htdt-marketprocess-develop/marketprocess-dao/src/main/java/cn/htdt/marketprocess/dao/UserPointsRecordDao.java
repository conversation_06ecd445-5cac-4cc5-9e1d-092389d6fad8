package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.UserPointsDomain;
import cn.htdt.marketprocess.domain.UserPointsRecordDomain;
import cn.htdt.marketprocess.vo.UserPointsRecordStaticVO;
import cn.htdt.marketprocess.vo.UserPointsRecordVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户积分记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-26
 */
@Mapper
public interface UserPointsRecordDao extends BaseMapper<UserPointsRecordDomain> {


    /**
     * @param userPointsRecordVO
     * @Description : 查询用户积分记录列表
     * <AUTHOR> 高繁
     * @date : 2021/9/29 10:48
     */
    List<UserPointsDomain> selectUserPointsRecordList(UserPointsRecordVO userPointsRecordVO);

    /**
     * 查询用户积分明细
     *
     * @param userPointsRecordVO 粉丝号/店铺编号/操作类型/开始、结束时间
     * @return 用户积分明细列表
     */
    List<UserPointsRecordDao> selectUserPointsDetail(UserPointsRecordVO userPointsRecordVO);

    /**
     * @param userPointsRecordDomain
     * @Description : 查询粉丝当前订单是否存在积分扣减记录
     * <AUTHOR> 高繁
     * @date : 2021/12/1 14:35
     */
    Integer selectSoReturnPointsCount(UserPointsRecordDomain userPointsRecordDomain);

    /**
     * @param userPointsRecordDomain
     * @Description : 查询粉丝在当前订单是否存在积分操作记录
     * <AUTHOR> wl
     * @date : 2022/11/02
     */
    Integer selectSoFansPointsCount(UserPointsRecordDomain userPointsRecordDomain);

    /**
     * 查询粉丝在某个店铺是否产生过关注得积分的记录
     *
     * @param phoneList  要查询的手机列表
     * @param storeNo 店铺编号
     * @return 记录条数
     */
    List<String> selectHasFollowRecordPhones(@Param("phoneList") List<String> phoneList, @Param("storeNo") String storeNo);

    List<String> selectExistPhones(@Param("phoneList") List<String> phoneList, @Param("storeNo") String storeNo,@Param("ruleType") String ruleType);

    /**
     * 查询店铺下所有粉丝累积获得积分的总计和店铺下所有粉丝累积扣减积分的总计
     * @param userPointsRecordDomain 查询参数
     * @return 结果
     */
    List<UserPointsRecordStaticVO> selectStoreTotalChangeNum(UserPointsRecordDomain userPointsRecordDomain);

    /**
     * 20230928蛋品-wh-商家-查询商家积分明细
     * 查询商家积分明细
     *
     * @param userPointsRecordVO 粉丝号/商家编号/操作类型/开始、结束时间
     * @return 用户积分明细列表
     */
    List<UserPointsRecordDao> selectMerchantUserPointsDetail(UserPointsRecordVO userPointsRecordVO);


    /**
     * 20230928蛋品-赵翔宇-商家积分-积分退回, 使用积分抵扣, 订单退款后，会对积分退回，
     * 退回至商家积分还是店铺积分，需要根据订单号查询当时获得积分的记录对应的积分类型
     *
     * @param userPointsRecordVO 查询参数
     * @return 积分下单的记录
     */
    List<UserPointsRecordDomain> selectUserPointsGainPointsRecordInfo(UserPointsRecordVO userPointsRecordVO);

    /**
     * 20230928蛋品-wh-门店APP查询积分管理页面，查询累计发放和累计使用
     * @param userPointsRecordVO
     * @return
     */
    Integer selectChangePoints(UserPointsRecordVO userPointsRecordVO);

}
