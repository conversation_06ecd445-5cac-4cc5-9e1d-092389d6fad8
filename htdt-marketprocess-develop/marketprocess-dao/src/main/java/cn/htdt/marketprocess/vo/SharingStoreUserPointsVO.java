package cn.htdt.marketprocess.vo;

import cn.htdt.marketprocess.domain.UserPointsDomain;
import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 */
@Data
public class SharingStoreUserPointsVO extends UserPointsDomain {
    private static final long serialVersionUID = 1L;

    private Integer changeNum;

    /**
     * 兑换礼品数
     */
    private Integer convertNum;

    /**
     * 扣减的积分数量
     */

    private Integer reducePoints;

    /**
     * 粉丝名字或手机号
     */
    private String namePhone;

    /**
     * 累计发放积分
     */
    private Integer totalGain;

    /**
     * 累计使用积分
     */
    private Integer totalReduce;

    /**
     * 粉丝编号集合
     */
    private List<String> fansNoList;

    /**
     * 是否共享兑换类型（1.否，2.是）
     */
    private String sharingType;

    /**
     * 店铺编号集合
     */
    private List<String> storeNoList;

    //商家编号
    private String merchantNo;
}
