package cn.htdt.marketprocess.domain;

import cn.htdt.common.domain.BaseDomain;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 抽奖规则
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("enroll_rule")
public class EnrollRuleDomain  extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 活动编号
     */
    private String promotionNo;

    /**
     * 活动店铺类型(1001:需店铺报名参与 1002:指定店铺直接参与)
     */
    private String storeType;

    /**
     * 店铺报名范围（1001:全部店铺 1002：指定区域 1003：导入店铺）
     */
    private String applyType ;
    /**
     * APP图片类型(1:默认图片 2:自定义图片)
     */
    private Integer appPictureType;

    /**
     * APP图片url
     */
    private String appPictureUrl;

    /**
     * 报名协议编码
     */
    private String enrollAgreementCode;

    /**
     * 报名协议url
     */
    private String enrollAgreementUrl;

    /**
     * 报名区域范围
     */
    private String applyArea;

    /**
     * 报名区域范围
     */
    private String applyAreaName;

    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件大小
     */
    private String fileSize;
    /**
     * 报名说明
     */
    private String enrollExplain;
}
