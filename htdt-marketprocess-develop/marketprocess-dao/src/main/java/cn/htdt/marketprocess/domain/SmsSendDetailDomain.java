package cn.htdt.marketprocess.domain;

import cn.htdt.common.domain.BaseDomain;
import cn.htdt.common.dto.annon.Encrypt;
import cn.htdt.common.dto.annon.EncryptField;
import cn.htdt.common.dto.enums.HandlerPolicy;
import cn.htdt.common.dto.enums.MaskType;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 短信发送明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-05
 */
@Encrypt
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sms_send_detail")
public class SmsSendDetailDomain extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 批次编号
     */
    private String batchNo;

    /**
     * 批次编号
     */
    private String sonBatchNo;

    /**
     * 手机号
     */
    @EncryptField(handlerPolicy= HandlerPolicy.MASK, maskType = MaskType.MOBILE)
    private String telephone;

    /**
     * 手机号-加密
     */
    @EncryptField
    private String dsTelephone;

    /**
     * 是否可用:默认2，1：不可用2:可用
     */
    private Integer disableFlag;

    /**
     * 状态值（2发送中,0:成功;其他:失败）
     */
    private Integer msgStatus;

    /**
     * 详情错误原因（或上行信息）
     */
    private String detailInfo;

    /**
     * 错误原因详情
     */
    private String detailMsg;

    /**
     * 短信发送状态，与msg_status对应， 0成功，1：异常
     */
    private Integer isSuccess;

    /**
     * 卡密
     */
    private String secretKey;
}
