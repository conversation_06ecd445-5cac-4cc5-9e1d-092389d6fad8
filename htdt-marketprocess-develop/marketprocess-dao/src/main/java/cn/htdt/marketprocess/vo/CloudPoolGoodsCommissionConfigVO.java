package cn.htdt.marketprocess.vo;

import cn.htdt.marketprocess.domain.CloudPoolGoodsCommissionConfigDomain;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 云池商品佣金配置表入参
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-21
 */
@Data
public class CloudPoolGoodsCommissionConfigVO extends CloudPoolGoodsCommissionConfigDomain {

    /**
     * 商品编号，包含普通商品、套餐商品等所有商品集合，查询用 新增不用
     */
    private List<String> goodsNoList;
    /**
     * 系列商品主品查询用，查询用 新增不用
     */
    private List<String> parentGoodsNoList;
}