package cn.htdt.marketprocess.vo;

import cn.htdt.marketprocess.domain.CouponCategoryRelationDomain;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <p>
 * 优惠券类目关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CouponCategoryRelationVO对象", description="优惠券类目关联表")
public class CouponCategoryRelationVO  extends CouponCategoryRelationDomain {


    private static final long serialVersionUID = 1L;

    /**
     * 批量优惠券编码
     */
    private List<String> couponNoList;

}
