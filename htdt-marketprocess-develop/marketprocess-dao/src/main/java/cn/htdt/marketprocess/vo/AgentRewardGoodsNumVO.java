package cn.htdt.marketprocess.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-01-28
 * @Description 代理人酬劳管理商品数量DTO
 **/
@Data
public class AgentRewardGoodsNumVO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = 1L;


    /**
     * 代理人的编码
     */
    private String agentNo;

    /**
     * 店铺编号
     */
    private String storeNo;
    /**
     * 营销活动类型，对应枚举MarketTypeEnum
     */
    private String marketType;

    /**
     * 酬劳状态   1 --- 预估  2 --- 已获得
     */
    private Integer rewardStatus;

    /**
     * 商品个数，涉及到商品才有
     */
    private Integer goodsNum;

    /**
     * 店铺编号集合
     */
    private List<String> storeNoList;
    /**
     * 代理人编号集合
     */
    private List<String> agentNoList;
    /**
     * 酬劳
     */
    private BigDecimal rewardValue;

}