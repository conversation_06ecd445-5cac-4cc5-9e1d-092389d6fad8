package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.GoodsPromotionBookingDomain;
import cn.htdt.marketprocess.vo.GoodsPromotionBookingVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 商品活动预约表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Mapper
public interface GoodsPromotionBookingDao extends BaseMapper<GoodsPromotionBookingDomain> {
    /**
     * 条件查询
     */
    List<GoodsPromotionBookingDomain> selectByPrimaryKey(GoodsPromotionBookingVO bookingVO);

    /**
     * 条件查询
     */
    List<GoodsPromotionBookingVO> selectFansBookingInfo(GoodsPromotionBookingVO bookingVO);

    /**
     * 修改
     */
    int updateByPrimaryKeySelective(GoodsPromotionBookingDomain bookingVO);

    /**
     * 查询活动商品预约数
     *
     * @param bookingDomain 查询参数
     * @return 预约数
     * <AUTHOR>
     * @date 2021-10-09
     */
    Integer selectPromotionGoodsBookingNum(GoodsPromotionBookingDomain bookingDomain);

}
