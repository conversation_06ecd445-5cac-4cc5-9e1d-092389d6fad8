package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.AgentTaskRelStoreDomain;
import cn.htdt.marketprocess.vo.AgentTaskRelStoreVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 代理人总部任务和公司关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-19
 */
@Mapper
public interface AgentTaskRelStoreDao extends BaseMapper<AgentTaskRelStoreDomain> {

    /**
      * @param : 根据商家编号查询参与店铺列表
      * @Description : 根据参数查询
      * <AUTHOR> 高繁
      * @date : 2021/1/26 19:16
     */
    List<AgentTaskRelStoreDomain> queryJoinStoreListByMerchantNo(AgentTaskRelStoreDomain agentTaskRelStoreDomain);

    /**
     * @Description 分页查询任务和营销清单列表
     * <AUTHOR>
     * @param agentTaskRelStoreVO
     * @return
     */
    List<AgentTaskRelStoreDomain> selectAgentTaskRelStoreListByPage(AgentTaskRelStoreVO agentTaskRelStoreVO);

    /**
     * @Description 根据taskRelStoreNoList批量删除
     * <AUTHOR>
     * @param agentTaskRelStoreVO
     * @return
     */
    int deleteBatchByTaskRelStoreNo(AgentTaskRelStoreVO agentTaskRelStoreVO);

    /**
     * @Description 查看是否有重复的店铺
     * <AUTHOR>
     * @param agentTaskRelStoreVO
     * @return
     */
    List<String> checkSameTaskRelStoreList(AgentTaskRelStoreVO agentTaskRelStoreVO);
    /**
     * @param agentTaskRelStoreVO
     * @Description : 查询该商家或店铺是否已初始化关系数据
     * <AUTHOR> 高繁
     * @date : 2021/2/22 15:26
     */
    Integer checkTaskStoreExists(AgentTaskRelStoreVO agentTaskRelStoreVO);

    /**
      * @param agentTaskRelStoreVO
      * @Description : 批量保存店铺+任务关系
      * <AUTHOR> 高繁
      * @date : 2021/2/22 19:06
     */
    int saveTaskStoreRel(AgentTaskRelStoreVO agentTaskRelStoreVO);
}
