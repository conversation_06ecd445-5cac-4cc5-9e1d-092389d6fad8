package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.AppletMsgTitleDomain;
import cn.htdt.marketprocess.vo.AppletMsgTitleVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 小程序消息与事件接收表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-17
 */
@Mapper
public interface AppletMsgTitleDao extends BaseMapper<AppletMsgTitleDomain> {

    /**
     * 查询小程序配置标题
     *
     * @param appletMsgTitleDomain
     * @return
     */
    List<AppletMsgTitleVO> selectAppletMsgTitle(AppletMsgTitleDomain appletMsgTitleDomain);
}
