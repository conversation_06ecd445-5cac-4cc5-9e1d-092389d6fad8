package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.PointsConfigOperateLogDomain;
import cn.htdt.marketprocess.vo.PointsConfigLogVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 积分配置操作日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-26
 */
@Mapper
public interface PointsConfigOperateLogDao extends BaseMapper<PointsConfigOperateLogDomain> {

    List<PointsConfigLogVO> selectPointsConfigLog(PointsConfigLogVO pointsConfigLogVO);
}
