package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.PromotionStoreRuleDomain;
import cn.htdt.marketcenter.dto.request.AtomReqPromotionStoreRuleDTO;
import cn.htdt.marketprocess.vo.PromotionStoreRuleVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 活动店铺规则表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-08
 */
@Mapper
public interface PromotionStoreRuleDao extends BaseMapper<PromotionStoreRuleDomain> {

    /**
     * 通过活动编码查询信息
     *
     * @param promotionStoreRuleVo
     * @return
     */
    PromotionStoreRuleVo selectPromotionStoreRule(PromotionStoreRuleVo promotionStoreRuleVo);

    /**
     * 查询类型是全部店铺的平台活动-商家
     *
     * @param promotionStoreRuleVo
     * @return
     */
    List<PromotionStoreRuleVo> selectPromotionStoreRuleStore(PromotionStoreRuleVo promotionStoreRuleVo);

    /**
     * 查询类型是全部店铺的平台活动-店铺
     *
     * @param promotionStoreRuleVo
     * @return
     */
    List<PromotionStoreRuleVo> selectPromotionStoreRulePlatform(PromotionStoreRuleVo promotionStoreRuleVo);

    /**
     * 查询是否存在活动店铺规则记录
     *
     * @param promotionStoreRuleVo
     * @return
     */
    Integer countRuleNum(PromotionStoreRuleVo promotionStoreRuleVo);

    /**
     * 查询单条记录-仅仅包括rule表信息
     *
     * @param domain
     * @return
     */
    void updateByParams(PromotionStoreRuleDomain domain);

    /**
     * @Description :查询报名活动是否被其他的活动引用---->> 此处只先查询一条记录，如果后面被其他功能用到，需要修改sql，将limit 1 去掉即可
     * <AUTHOR>
     * @Date 2021/4/19 11:31
     * @Param promotionStoreRuleDomain-->> enrollNo-->>报名活动编号, storeType = 1002 报名活动
     * @Return
     */
    List<PromotionStoreRuleDomain> selectEnrollIsUsedByOthersList(PromotionStoreRuleDomain promotionStoreRuleDomain);

    /**
     * @Description :批量获取活动店铺类型
     * <AUTHOR>
     * @Param promotionStoreRuleDomain-->> enrollNo-->>报名活动编号, storeType = 1002 报名活动
     * @Return
     */
    List<PromotionStoreRuleDomain> selectPromotionStoreRuleList(AtomReqPromotionStoreRuleDTO dto);


    /**
     * @param promotionNo
     * @Description : 删除活动店铺
     * <AUTHOR> 王磊
     * @date : 2021/4/8 20:04
     */
    void deletePromotionStoreRuleByPromotionNo(@Param("promotionNo") String promotionNo);

    /**
     * 查询促销活动设置的店铺规则以及店铺报名详情
     * <p>
     * 用于判断店铺是否还在报名活动列表中
     *
     * @param dto
     * @return
     * <AUTHOR>
     * @date 2021-07-02
     */
    PromotionStoreRuleDomain selectPromotionStoreRuleAndDetail(AtomReqPromotionStoreRuleDTO dto);

    /**
     * 删除促销活动设置的店铺规则信息
     *
     * @param promotionNo
     * @return
     * <AUTHOR>
     * @date 2021-07-05
     */
    void deletePromotionStoreRule(String promotionNo);

    /**
     * 根据活动编号查询设置的参与店铺的规则
     *
     * @param promotionNo
     * @return
     * <AUTHOR>
     * @date 2021-07-08
     */
    PromotionStoreRuleDomain selectPromotionStoreRuleByPromotionNo(String promotionNo);
}
