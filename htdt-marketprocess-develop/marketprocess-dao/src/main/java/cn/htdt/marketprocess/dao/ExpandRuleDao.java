package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.ExpandRuleDomain;
import cn.htdt.marketprocess.vo.ExpandRuleVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 膨胀红包规则 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Mapper
public interface ExpandRuleDao extends BaseMapper<ExpandRuleDomain> {

    /**
     * 查询记录list
     * @param expandRuleVO
     * @return
     */
    List<ExpandRuleVO> selectExpandListByParams(ExpandRuleVO expandRuleVO);

    /**
     * 查询记录list-商家下店铺和店铺查询
     * @param expandRuleVO
     * @return
     */
    List<ExpandRuleVO> selectExpandMerchantStoreByParams(ExpandRuleVO expandRuleVO);

    /**
     * 查询单条记录-仅仅包括rule表信息
     * @param expandRuleVO
     * @return
     */
    ExpandRuleVO selectExpandRule(ExpandRuleVO expandRuleVO);

    /**
     * 更新单条记录
     * @param expandRuleVO
     * @return
     */
    void updateExpandByParams(ExpandRuleVO expandRuleVO);

    /**
     * 根据promotionNo删除膨胀红包活动
     * @param expandRuleVO
     * @return
     */
    void deleteExpandByPromotionNo(ExpandRuleVO expandRuleVO);

}
