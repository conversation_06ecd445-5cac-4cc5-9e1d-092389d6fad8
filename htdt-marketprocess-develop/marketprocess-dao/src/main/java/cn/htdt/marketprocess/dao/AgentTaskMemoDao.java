package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.AgentTaskMemoDomain;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 代理人任务规则说明表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-14
 */
@Mapper
public interface AgentTaskMemoDao extends BaseMapper<AgentTaskMemoDomain> {

    AgentTaskMemoDomain selectByParams(AgentTaskMemoDomain agentTaskMemoDomain);

    int updateByTaskNo(AgentTaskMemoDomain agentTaskMemoDomain);
}