package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.CouponSettingDomain;
import cn.htdt.marketprocess.domain.PromotionStoreRelationDomain;
import cn.htdt.marketprocess.vo.AtomCouponSettingVo;
import cn.htdt.marketprocess.vo.AtomLotteryRuleVo;
import cn.htdt.marketprocess.vo.AtomVirtualCoinRuleCouponSettingVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 优惠券配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-01
 */
@Mapper
public interface CouponSettingDao extends BaseMapper<CouponSettingDomain> {


    /**
     * 查询记录list
     * @param atomCouponSettingVo
     * @return
     */
    List<AtomCouponSettingVo> selectCouponListByParams(AtomCouponSettingVo atomCouponSettingVo);

    /**
     * 编辑、新增橙豆规则, 查询符合条件的优惠券
     * 20230928蛋品-橙豆规则-赵翔宇-查询店铺规则或者商家规则的优惠券
     * @param atomVirtualCoinRuleCouponSettingVo 查询参数
     * @return 结果
     */
    List<AtomVirtualCoinRuleCouponSettingVo> selectVirtualCoinRuleAvailableCouponList(AtomVirtualCoinRuleCouponSettingVo atomVirtualCoinRuleCouponSettingVo);

    /**
     * @description 20230928蛋品-桑伟杰-会员等级-查询符合会员等级条件的优惠券
     * <AUTHOR>
     * @date 2023-08-08 14:18:00
     * @param atomCouponSettingVo
     * @return
     */
    List<AtomCouponSettingVo> selectMemberLevelAvailableCouponList(AtomCouponSettingVo atomCouponSettingVo);

    /**
     * 查询记录list-商家下店铺和店铺优惠券查询
     * @param atomLotteryRuleVo
     * @return
     */
    List<AtomLotteryRuleVo> selectCouponMerchantStoreByParams(AtomLotteryRuleVo atomLotteryRuleVo);

    /**
     * 查询优惠券记录
     * @param atomCouponSettingVo
     * @return
     */
    List<AtomCouponSettingVo> selectCouponList(AtomCouponSettingVo atomCouponSettingVo);

    /**
     *  hxg-购物车领券列表
     *  @param atomCouponSettingVo
     *  @return
     *  <AUTHOR>
     */
    List<AtomCouponSettingVo> selectHxgCouponInfoList(AtomCouponSettingVo atomCouponSettingVo);
    /**
     * 查询单条记录-仅仅包括rule表信息
     * @param atomCouponSettingVo
     * @return
     */
    AtomCouponSettingVo selectCouponSetting(AtomCouponSettingVo atomCouponSettingVo);

    /**
     * 根据List批量查询记录
     * @param atomCouponSettingVo
     * @return
     */
    List<AtomCouponSettingVo> selectCouponSettingList(AtomCouponSettingVo atomCouponSettingVo);

    /**
     * 更新单条记录
     * @param atomCouponSettingVo
     * @return
     */
    void updateCouponSettingByParams(AtomCouponSettingVo atomCouponSettingVo);

    /**
     * 根据promotionNo删除优惠券活动
     * @param atomCouponSettingVo
     * @return
     */
    void deleteCouponSettingByPromotionNo(AtomCouponSettingVo atomCouponSettingVo);

    /**
     * 优惠券上下架接口
     *
     * @param promotionStoreRelationDomain
     */
    int upOrDownShelvesCoupon(PromotionStoreRelationDomain promotionStoreRelationDomain);

    /**
     * 批量更新优惠券-剩余张数
     * @param atomCouponSettingVo
     */
    int batchUpdateCouponRemain(AtomCouponSettingVo atomCouponSettingVo);

    /**
      * @param atomCouponSettingVos
      * @Description : 批量修改抽奖优惠券时间
      * <AUTHOR> 高繁
      * @date : 2021/9/15 18:41
     */
    void batchUpdateCouponTime(@Param("atomCouponSettingVos") List<AtomCouponSettingVo> atomCouponSettingVos);

    /**
     * 更新记录-剩余张数
     * @param atomCouponSettingVo
     * @return
     */
    void updateCouponSettingResource(AtomCouponSettingVo atomCouponSettingVo);

    /**
     * 查询商家优惠券的分发店铺信息
     */
    List<AtomCouponSettingVo> selectMerchantCouponList(@Param("couponNo") String couponNo);
}
