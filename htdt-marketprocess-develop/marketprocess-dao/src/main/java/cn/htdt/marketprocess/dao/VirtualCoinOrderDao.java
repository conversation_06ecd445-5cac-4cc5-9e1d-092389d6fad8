package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.VirtualCoinOrderDomain;
import cn.htdt.marketcenter.dto.request.AtomReqVirtualCoinOrderDTO;
import cn.htdt.marketcenter.dto.response.AtomResVirtualCoinOrderDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-01-25
 */
@Mapper
public interface VirtualCoinOrderDao extends BaseMapper<VirtualCoinOrderDomain> {

    AtomResVirtualCoinOrderDTO selectOrderByOrderNo(String virtualOrderNo);

    List<AtomResVirtualCoinOrderDTO> selectOrderByStoreNoByPage(AtomReqVirtualCoinOrderDTO queryOrderDto);

    BigDecimal getTotalPayment(AtomReqVirtualCoinOrderDTO queryOrderDto);

    int updateOrder(VirtualCoinOrderDomain orderDomain);

}
