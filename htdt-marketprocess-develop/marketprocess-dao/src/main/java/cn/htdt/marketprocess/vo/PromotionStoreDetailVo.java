package cn.htdt.marketprocess.vo;

import cn.htdt.marketprocess.domain.PromotionStoreDetailDomain;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <p>
 * 活动店铺详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="PromotionStoreDetailVo对象", description="活动店铺详情表")
public class PromotionStoreDetailVo extends PromotionStoreDetailDomain {

    private static final long serialVersionUID = 1L;

    /**
     *  店铺编码集合
     */
    private List<String> merchantNoList;
}
