package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.AppletMsgTemplateDomain;
import cn.htdt.marketprocess.vo.AppletMsgTemplateVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 用户已有小程序消息模板 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-17
 */
@Mapper
public interface AppletMsgTemplateDao extends BaseMapper<AppletMsgTemplateDomain> {


    /**
     * 查询小程序配置模板
     *
     * @param appletMsgTemplateDomain
     * @return
     */
    AppletMsgTemplateVO selectAppletMsgTemplate(AppletMsgTemplateDomain appletMsgTemplateDomain);


    /**
     * 查询小程序配置模板
     *
     * @param appletMsgTemplateVO
     * @return
     */
    List<AppletMsgTemplateDomain> selectAppletMsgTemplates(AppletMsgTemplateVO appletMsgTemplateVO);
}
