package cn.htdt.marketprocess.vo;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 橙豆列表，请求参数
 * <AUTHOR>
 * @date 2023-01-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ReqVirtualCoinAndLatestRecordVo extends ReqComPageDTO {
    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 交易类型类型，参考VirtualCoinTradeTypeEnum枚举
     */
    private List<String> tradeTypeList;

    /**
     * 粉丝编号集合
     */
    private List<String> fanNoList;

    /**
     * 橙豆规则类型, 1001-店铺规则, 1002-商家规则, 参考枚举: VirtualCoinRuleTypeEnum
     */
    private String ruleType;
}
