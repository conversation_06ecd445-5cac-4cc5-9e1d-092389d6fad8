package cn.htdt.marketprocess.vo;

import cn.htdt.marketprocess.domain.PromotionStoreRelationDomain;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <p>
 * 活动店铺关联上下架表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="PromotionStoreRelationVo对象", description="活动店铺关联上下架表")
public class PromotionStoreRelationVo extends PromotionStoreRelationDomain {

    /**
     * 批量店铺编码
     */
    private List<String> storeNoList;

    /**
     * 规则主键
     */
    private List<String> enrollNoList;
    /**
     * 批量店铺编码
     */
    private List<String> promotionNoList;
}
