package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.AgentMarketRewardsetDomain;
import cn.htdt.marketprocess.vo.GoodsNoByCommissionVO;
import cn.htdt.marketprocess.vo.TaskRewardSetVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 代理人酬劳设置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-11
 */
@Mapper
public interface AgentMarketRewardsetDao extends BaseMapper<AgentMarketRewardsetDomain> {
    int deleteByParams(String taskNo);

    int updateByParams(AgentMarketRewardsetDomain agentMarketRewardsetDomain);

    AgentMarketRewardsetDomain selectByParams(AgentMarketRewardsetDomain agentMarketRewardsetDomain);

    List<AgentMarketRewardsetDomain> selectListByParams(AgentMarketRewardsetDomain agentMarketRewardsetDomain);


    /**
     * 根据平台任务编码批量查询酬劳设置聚合
     * @param taskNoList
     * @return
     */
    List<TaskRewardSetVO> selectTaskRewardSetList(@Param("taskNoList") List<String> taskNoList);

    /**
     * 根据平台任务编码批量查询酬劳设置聚合
     * @param goodsNos
     * @return
     */
    List<AgentMarketRewardsetDomain> batchSelectTaskRewardSetList(@Param("goodsNos") List<String> goodsNos);

    Integer physicallyDeleteByTaskOrGoodsNos(@Param("goodsNos")List<String> goodsNos);

    /**
     * <AUTHOR>
     * @Description 通过商品编号查询新上架的店铺分销 获取最近3条
     * @Date 2021/7/1
     * @Param [agentMarketRewardsetDomain]
     * @return java.lang.String
     **/
    AgentMarketRewardsetDomain selectGoodsNoByGoodsNo(AgentMarketRewardsetDomain agentMarketRewardsetDomain);

    /**
     * <AUTHOR>
     * @Description 通过佣金比率高低查询新上架的店铺分销 获取最近3条
     * @Date 2021/7/1
     * @Param [agentMarketRewardsetDomain]
     * @return java.util.List<cn.htdt.marketprocess.vo.GoodsNoByCommissionVO>
     **/
    List<GoodsNoByCommissionVO> selectGoodsNoByCommission(AgentMarketRewardsetDomain agentMarketRewardsetDomain);
}