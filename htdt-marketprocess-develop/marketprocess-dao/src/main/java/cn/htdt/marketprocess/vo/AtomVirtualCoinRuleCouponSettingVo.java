package cn.htdt.marketprocess.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 橙豆规则优惠券配置查询参数
 * <AUTHOR>
 * @since 2023-02-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="AtomVirtualCoinRuleCouponSettingVo", description="橙豆规则优惠券配置对象")
public class AtomVirtualCoinRuleCouponSettingVo extends AtomCouponSettingVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 橙豆规则编号
     */
    private String virtualNo;

    /**
     * 是否选中, 1: 未选中, 2: 选中
     */
    private String selectFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}