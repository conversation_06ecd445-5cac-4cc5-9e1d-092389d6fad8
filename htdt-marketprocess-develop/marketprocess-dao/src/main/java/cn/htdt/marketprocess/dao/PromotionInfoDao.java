package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.PromotionInfoDomain;
import cn.htdt.marketprocess.vo.PromotionInfoVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 促销活动 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-08
 */
@Mapper
public interface PromotionInfoDao extends BaseMapper<PromotionInfoDomain> {

    /**
     * @param promotionInfoDomain
     * @Description : 活动上下架
     * <AUTHOR> 高繁
     * @date : 2021/3/9 10:29
     */
    Integer updatePromotionShelves(PromotionInfoDomain promotionInfoDomain);

    /**
     *
     * @param promotionInfoDomain
     * @Description : 查询促销活动数量
     * <AUTHOR> 张宇
     * @date : 2023/5/24 10:29
     */
    Integer selectPromotionInfoCount(PromotionInfoDomain promotionInfoDomain);

    /**
     * 查询促销信息
     *
     * @param promotionInfoDomain
     * @return
     */
    PromotionInfoDomain selectPromotionInfo(PromotionInfoDomain promotionInfoDomain);

    /**
     * 获取对应活动列表信息
     *
     * @param promotionInfoVO
     * @return
     */
    List<PromotionInfoVO> selectPromotionInfoList(PromotionInfoVO promotionInfoVO);

    /**
     * 获取对应活动标签信息
     *
     * @param promotionInfoVO
     * @return
     */
    List<PromotionInfoVO> selectPromotionTypeList(PromotionInfoVO promotionInfoVO);

    /**
     * 获取过期活动列表信息
     *
     * @param promotionInfoVO
     * @return
     */
    List<PromotionInfoVO> selectPromotionList(PromotionInfoVO promotionInfoVO);

    /**
     * 获取过期活动总数
     *
     * @param promotionInfoVO
     * @return
     */
    Integer selectPromotionCount(PromotionInfoVO promotionInfoVO);

    /**
     * 更新记录-仅仅包括活动表信息
     *
     * @param promotionInfoDomain
     * @return
     * <AUTHOR> 王磊
     */
    void updateByParams(PromotionInfoDomain promotionInfoDomain);


    /**
     * @param promotionInfoDomain
     * @Description : 保存短链接
     * <AUTHOR> 高繁
     * @date : 2021/3/9 16:50
     */
    Integer updatePromotionShortLink(PromotionInfoDomain promotionInfoDomain);

    /**
     * @param promotionNo
     * @Description : 删除主活动
     * <AUTHOR> 高繁
     * @date : 2021/4/8 20:04
     */
    void updateDeleteFlag(@Param("promotionNo") String promotionNo);

    /**
     * 更新活动的基本信息
     *
     * @param promotionInfoVO
     * @return
     * <AUTHOR> 杨子建
     */
    void updatePromotionInfo(PromotionInfoVO promotionInfoVO);

    /**
      * @param promotionInfoDomain
      * @Description : 积分兑换抽奖次数配置关闭，触发已创建活动积分配置为禁用
      * <AUTHOR> 高繁
      * @date : 2021/11/4 17:31
     */
    void updatePromotionPointsConfig(PromotionInfoDomain promotionInfoDomain);

    int selectMerchantOnGoingPromotionCount(@Param("merchantNo")String merchantNo);

    /**
     * 查看活动上架数量
     * @param promotionInfoVO
     * @return
     */
    List<PromotionInfoVO> selectPromotionNum(PromotionInfoVO promotionInfoVO);

    /**
     * 店铺装修活动列表
     * @param promotionInfoDomain
     * @return
     */
    List<PromotionInfoVO> getPromotionInfoForDecorate(PromotionInfoDomain promotionInfoDomain);

    PromotionInfoVO selectNewPromotionCount(PromotionInfoVO promotionInfoVO);
}
