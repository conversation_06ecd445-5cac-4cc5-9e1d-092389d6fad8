package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.ExpandHelpRecordDomain;
import cn.htdt.marketprocess.vo.ExpandHelpRecordVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 膨胀红包助力记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Mapper
public interface ExpandHelpRecordDao extends BaseMapper<ExpandHelpRecordDomain> {

    /**
     * 查询记录list
     * @param expandHelpRecordVO
     * @return
     */
    List<ExpandHelpRecordVO> selectExpandHelpRecordListByParams(ExpandHelpRecordVO expandHelpRecordVO);

    int selectExpandHelpRecordCount(ExpandHelpRecordVO expandHelpRecordVO);

    /**
     * 获取已助力金额
     * @param expandHelpRecordVO
     * @return
     */
    BigDecimal selectExpandHelpHelpMoney(ExpandHelpRecordVO expandHelpRecordVO);

}
