package cn.htdt.marketprocess.domain;

import cn.htdt.common.domain.BaseDomain;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 短信模板表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sms_template")
public class SmsTemplateDomain extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 模板编号
     */
    private String templateNo;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板内容
     */
    private String templateContent;

    /**
     * 是否可用:默认2，1：不可用2:可用
     */
    private Integer disableFlag;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 数据来源类型 1001:平台自建   1002:商家自建  1003:店铺自建
     */
    private String sourceType;

    /**
     * 父级模板编号
     */
    private String parentTemplateNo;

}
