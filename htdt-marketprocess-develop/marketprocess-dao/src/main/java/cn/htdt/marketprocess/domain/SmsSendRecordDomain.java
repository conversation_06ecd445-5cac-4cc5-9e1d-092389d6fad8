package cn.htdt.marketprocess.domain;

import cn.htdt.common.domain.BaseDomain;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 短信发送记录表
 *
 * <AUTHOR>
 * @since 2021-07-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sms_send_record")
public class SmsSendRecordDomain extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 批次编号
     */
    private String batchNo;

    /**
     * 子批次编号
     */
    private String sonBatchNo;

    /**
     * 短信批次类型 1001:主批次 1002:子批次
     */
    private String seriesType;

    /**
     * 渠道Id，系统标识，枚举BusinessSystemEnum。boss：超级老板，shop：汇掌柜，cash：一体机
     */
    private String channelId;

    /**
     * 发送类型（1000:短信营销，2000:优惠券，3000:粉丝导购）
     */
    private String sourceType;

    /**
     * 短信类型（1001:活动预热，1002:潜在人群，1003:常购人群，1004:粉丝分组，1005:自定义，1006:生日关怀，2001:店铺手动发粉丝券，2002:平台手动发粉丝券，2003:平台手动发代理人券，3001:今日成交粉丝场景导购，3002:最有可能成交的顾客场景导购）
     */
    private String smsType;

    /**
     * 发送内容
     */
    private String sendContent;

    /**
     * 发送状态（1001:发送中 1002:发送完成 1003:发送失败）
     */
    private String sendStatus;

    /**
     * 发送人数
     */
    private Integer sendPeopleNum;

    /**
     * 短信单次条数
     */
    private Integer smsSingleNum;

    /**
     * 发送总条数
     */
    private Integer sendTotalNum;

    /**
     * 发送成功条数
     */
    private Integer sendSuccessNum;

    /**
     * 发送失败条数
     */
    private Integer sendFailNum;

    /**
     * 发送对象（1001:店内所有粉丝,1002:有购买行为的粉丝,1003:加入购物车的粉丝,1004:分组粉丝,1005:自定义粉丝）
     */
    private String sendObject;

    /**
     * 发送对象参数
     */
    private String sendObjectParam;

    /**
     * 是否可用:默认2，1：不可用2:可用
     */
    private Integer disableFlag;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 定时发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime timingSendTime;
    /**
     * 发送时间类型:1001-立即发送，1002-定时发送
     */
    private String sendTimeType;

    /**
     * 生日节日配置编号
     */
    private String configNo;
}
