package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.GiftCardSecretKeyDomain;
import cn.htdt.marketprocess.vo.GiftCardSecretKeyVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface GiftCardSecretKeyDao extends BaseMapper<GiftCardSecretKeyDomain> {

    /**
     * 条件查询
     */
    List<GiftCardSecretKeyDomain> selectByParam(GiftCardSecretKeyVo giftCardSecretKeyVo);


    /**
     * 导入卡密列表查询
     */
    List<GiftCardSecretKeyDomain> selectSendListByParam(GiftCardSecretKeyDomain giftCardSecretKeyDomain);

    /**
     * 批量编辑
     *
     * @param giftCardSecretKeyDomainList
     * @return
     */
    int batchUpdateGiftCardSecretKey(@Param("giftCardSecretKeyDomainList") List<GiftCardSecretKeyDomain> giftCardSecretKeyDomainList);

    /**
     * 根据参数修改数据
     *
     * @param domain
     * @return
     */
    int updateByParam(GiftCardSecretKeyDomain domain);

    /**
     * 修改礼品卡卡密状态
     *
     * @param domain
     * @return
     */
    int updateGiftCardSecretKeyStatus(GiftCardSecretKeyDomain domain);

    /**
     * 查询粉丝礼品卡数
     * @param giftCardSecretKeyVo giftCardSecretKeyVo
     * @return 礼品卡数
     */
    int getFansCardCount(GiftCardSecretKeyVo giftCardSecretKeyVo);

}
