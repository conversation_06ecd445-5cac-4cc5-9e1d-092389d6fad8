package cn.htdt.marketprocess.vo;

import cn.htdt.marketprocess.domain.LotteryRewardGoodsRelationDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <p>
 * 抽奖奖品商品关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="LotteryRewardGoodsRelationVo对象", description="抽奖奖品商品关联表")
public class LotteryRewardGoodsRelationVo extends LotteryRewardGoodsRelationDomain {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "来源 1001：平台 1002：商家 1003：店铺")
    private String sourceType;

    /**
     * 批量用
     */
    private List<String> rewardNos;

}
