package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.CloudPoolGoodsCommissionConfigDomain;
import cn.htdt.marketprocess.vo.CloudPoolGoodsCommissionConfigVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 云池商品佣金配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-11
 */
@Mapper
public interface CloudPoolGoodsCommissionConfigDao extends BaseMapper<CloudPoolGoodsCommissionConfigDomain> {

    /**
     * 根据商品编码获取单个云池商品佣金配置信息
     * */
    CloudPoolGoodsCommissionConfigDomain selectByParams(CloudPoolGoodsCommissionConfigDomain configDomain);
    /**
     * 根据条件获取云池商品佣金配置信息
     * */
    List<CloudPoolGoodsCommissionConfigDomain> selectListByParams(CloudPoolGoodsCommissionConfigVO configDTO);
    /**
     * 根据商品编码删除
     * */
    int logicDelete(CloudPoolGoodsCommissionConfigDomain configDomain);

    int updateByParams(CloudPoolGoodsCommissionConfigDomain configDomain);

    int batchUpdateByParams(@Param("list") List<CloudPoolGoodsCommissionConfigDomain> configDomain);

    int batchDeleteAssert(@Param("list") List<CloudPoolGoodsCommissionConfigDomain> configDomain);
}