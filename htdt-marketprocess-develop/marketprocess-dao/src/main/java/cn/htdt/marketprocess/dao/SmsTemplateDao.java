package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.domain.SmsTemplateDomain;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 短信模板表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2021/6/25
 **/
@Mapper
public interface SmsTemplateDao extends BaseMapper<SmsTemplateDomain> {

    /**
     * 根据参数查询
     *
     * @param domain 查询参数
     * @return List<SmsTemplateDomain>
     */
    List<SmsTemplateDomain> selectByParam(SmsTemplateDomain domain);

    /**
     * 根据参数修改
     *
     * @param domain 修改参数
     * @return int
     */
    int updateByParams(SmsTemplateDomain domain);

    /**
     * 逻辑删除
     *
     * @param domain 请求参数
     * @return int
     */
    int logicDelete(SmsTemplateDomain domain);
}
