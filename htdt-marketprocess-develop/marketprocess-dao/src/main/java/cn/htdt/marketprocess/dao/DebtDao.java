package cn.htdt.marketprocess.dao;

import cn.htdt.marketprocess.vo.DebtVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 欠款粉丝查询 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
@Mapper
public interface DebtDao extends BaseMapper {
    /**
     * 根据欠款金额或者欠款天数查询粉丝欠款信息
     * @param debtVo 请求参数
     * @return 粉丝欠款金额
     */
    List<DebtVo> selectArrearsFans(DebtVo debtVo);
}
