#FROM test-dockimgws.htd.cn/b2b_p/centos6v5-ty-msah-b2c:latest
FROM test-dockimgws.htd.cn/b2b_p/centos6v5-ty364-msah-b2c:latest

RUN sed -i '0,/htdty/s//htdt-marketprocess/' /home/<USER>/tingyun/tingyun.properties

EXPOSE 7094

WORKDIR /home/<USER>

RUN rm -rf  tomcat7 tomcat8

ADD ./marketprocess-web/target/marketprocess-web.jar  ./

ENV JAVA_OPTS="-Xms3G -Xmx3G -XX:PermSize=512M -XX:MaxPermSize=512m  -Dfile.encoding=UTF8 -Dsun.jnu.encoding=UTF8  \
-Dregion-id=idc \
-Dzone-id=idc-zone-a \
-Dvpc-id=empty \
-Downer-account-id=**************** \
-javaagent:/home/<USER>/msha-java-agent.jar \
-Dmsha.licence=a4d94903-d810-4f9c-b8e2-e2836d8f00e8 \
-Dmsha.namespaces=0a8d35ca-5393-4b66-80db-c7fa5f21db0b \
-Dmsha.app.name=htdt-idc-marketprocess \
-Dmsha.nacos.namespace=a2075f99-0c7d-475a-8c94-f89ce70aa70a \
-Dmsha.console.ip=************** \
-Dmsha.nacos.server.addr=*************:8848"

ENTRYPOINT java ${JAVA_OPTS}   -javaagent:/home/<USER>/tingyun/tingyun-agent-java.jar  -Dmaven.test.skip=true  -Dspring.profiles.active=prod -Dnacos.server-addr=*************:8848 -Dcom.alibaba.nacos.naming.log.level=error -Dcom.alibaba.nacos.config.log.level=error -jar  marketprocess-web.jar
