package cn.htdt.goodsprocess.dto.request.goodsimeihistory;


import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <p>
 * 商品串码历史查询请求Dto
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@EqualsAndHashCode
@Data
public class ReqGoodsImeiHistoryDTO extends ReqComPageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自定义商品编码
     */
    private String customGoodsNo;

    /**
     * 商品编号，包含普通商品、套餐商品等所有商品
     */
    private String goodsNo;

    /**
     * 商品串码
     */
    private String imei;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 门店名称
     */
    private String storeName;



    /**
     * 自定义店铺编号
     */
    private String customStoreNo;

    /**
     * 是否在途:  1: 不在途 ; 2: 在途
     */
    private Integer transportFlag;

    /**
     * 是否已销售：1=未销售；2已销售。
     **/
    private Integer saleFlag;

    /**
     * 出入库状态:  1表示已入库 ,  2表示已出库
     */
    private Integer inventoryState;

    /**
     * 出入库方式 : 有采购入库、调拨入库、销售退货入库 以及调拨出库、销售出库、P200出库、O2O出库等
     */
    private String inventoryType;

    /**
     * 供应商编号
     */
    private String supplierCode;

    /**
     * 库存日期
     */
    private LocalDate inventoryDate;

    /**
     * 数据逻辑删除标识，默认1未删除，2已删除
     */
    private Integer deleteFlag;


    /**
     * 商品id或名称
     */
    private String goodNoOrName;

    /**
     * 备份开始时间
     */
    @ApiModelProperty(value = "备份开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate inventoryDateStart;

    /**
     * 备份结束日期
     */
    @ApiModelProperty(value = "备份结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate inventoryDateEnd;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
