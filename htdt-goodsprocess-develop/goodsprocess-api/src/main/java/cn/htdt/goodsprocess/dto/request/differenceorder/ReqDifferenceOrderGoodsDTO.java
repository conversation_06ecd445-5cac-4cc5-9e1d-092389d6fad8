package cn.htdt.goodsprocess.dto.request.differenceorder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 差异单商品行请求DRO
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ReqDifferenceOrderGoodsDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 差异商品行编号
     */
    @ApiModelProperty(value = "差异商品行编号")
    private String differenceItemNo;

    @ApiModelProperty(value = "差异单号")
    private String differenceNo;

    /**
     * 源商品编号（商家商品编号）
     */
    @ApiModelProperty(value = "源商品编号（商家商品编号）")
    private String merchantGoodsNo;

    /**
     * 商家商品名称
     */
    @ApiModelProperty(value = "商家商品名称")
    private String merchantGoodsName;

    @ApiModelProperty(value = "店铺商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "条形码")
    private String barcode;

    @ApiModelProperty(value = "助记码")
    private String goodsHelpCode;

    /**
     * 差异金额
     */
    @ApiModelProperty(value = "差异金额")
    private BigDecimal differenceAmount;

    @ApiModelProperty(value = "未入库数量")
    private BigDecimal noStorageCount;

    @ApiModelProperty(value = "已入库数量")
    private BigDecimal inStorageCount;

    @ApiModelProperty(value = "是否入仓 1-否 2-是")
    private Integer warehouseFlag;

    @ApiModelProperty(value = "仓库编号")
    private String warehouseNo;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty("商家编号")
    private String merchantNo;

    @ApiModelProperty("商家名称")
    private String merchantName;

    @ApiModelProperty("店铺ID, 对应orgId")
    private String storeNo;

    @ApiModelProperty("店铺名称, 对应orgName")
    private String storeName;

    @ApiModelProperty("创建开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate startCreateTime;

    @ApiModelProperty("创建结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate endCreateTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
