package cn.htdt.goodsprocess.dto.request.warehouse;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2020-09-02
 * @Description 仓库店铺关系 请求DTO
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ReqStoreWarehouseRelationDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编号
     */
    private String warehouseNo;

    /**
     * 仓库编码，手动输入的
     */
    private String warehouseCode;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 是否可用:默认2，1：不可用 2：可用
     */
    private Integer disableFlag;

    /**
     * 创建人no
     */
    private String createNo;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 修改人no
     */
    private String modifyNo;

    /**
     * 修改人名称
     */
    private String modifyName;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称，对应orgName
     */
    private String storeName;

    /**
     * 平台编号
     */
    private String companyNo;

    /**
     * 平台名称
     */
    private String companyName;

    /**
     * 分部编号
     */
    private String branchNo;

    /**
     * 分部名称
     */
    private String branchName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
