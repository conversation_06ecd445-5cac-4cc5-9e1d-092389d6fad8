package cn.htdt.goodsprocess.dto.request.salecategory;

import cn.htdt.common.dto.request.ReqBaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 销售类目入参实体类
 *
 * <AUTHOR>
 * @date 2020-09-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReqSaleCategoryEditDTO extends ReqBaseDTO {

    private static final long serialVersionUID = -5362705385057034040L;

    /**
     * 类目id
     */
    private String categoryNo;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 父类目节点ID
     */
    private String parentNo;

    /**
     * 数据来源类型(默认1002，1001：MDM同步，1002：平台自建)
     */
    private String categorySource;

    /**
     * 图片URL
     */
    private String pictureUrl;

    /**
     * 是否可用:默认2，1：不可用，2：可用
     */
    private Integer disableFlag;

    /**
     * 引用平台的类目id
     */
    private String mdmCategoryNo;

    /**
     * 移动方向：1001 上移/1002 下移
     */
    private String moveDirection;

    /**
     * 类目id全路径
     */
    private String fullIdPath;

    /**
     * 类目名称全路径
     */
    private String fullNamePath;

    /**
     * 层级(1001:一级 1002：二级 1003：三级 1004：四级)
     */
    private String categoryLevel;

    /**
     * 排序值
     */
    private Integer sortValue;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
