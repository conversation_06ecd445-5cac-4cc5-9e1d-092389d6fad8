package cn.htdt.goodsprocess.dto.request.goodsassociation;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ReqGoodsAssociationRelationDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 组合商品编号
     */
    private String goodsAssociationNo;

    /**
     * 商品编号
     */
    private String goodsNo;

    /**
     * 组合商品数量
     */
    private Integer associationGoodsNum;

    /**
     * 实际售价
     */
    private BigDecimal realSalePrice;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;
}
