package cn.htdt.goodsprocess.dto.request.goodsmedia;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 详细说明.视频请求类
 * <p>
 * Copyright: Copyright (c) 2020/9/27 10:40
 * <p>
 * Company: htd
 * <p>
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReqGoodsVideoDTO extends ReqGoodsMediaDTO implements Serializable {

    private static final long serialVersionUID = -2053214752318993065L;

    /**
     * 视频url
     */
    private String videoUrl;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件大小(单位M)
     */
    private String fileSize;
    
    /**
     * 阿里云视频ID
     */
    private String aliVideoId;
    
    /**
     * 视频商品时长,单位(秒)
     */
    private String videoDuration;
    
    /**
     * 视频审核状态,1001:审核中  1002:审核通过  1003:审核未通过
     */
    private String videoAuditStatus;
    
    /**
     * 审核结果,失败原因
     */
    private String videoAuditResult;
    
}
