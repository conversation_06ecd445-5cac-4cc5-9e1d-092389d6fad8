package cn.htdt.goodsprocess.dto.request.tradeorder;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 对接接口接入控制表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
@Data
public class ReqTradeSignDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 数据来源名
     */
    private String dataSourceName;

    /**
     * 对接环境变量profilesActive：dev、fat、prod
     */
    private String profilesActive;

    /**
     * 加盐
     */
    private String salt;

    /**
     * api版本号
     */
    private String versions;


}
