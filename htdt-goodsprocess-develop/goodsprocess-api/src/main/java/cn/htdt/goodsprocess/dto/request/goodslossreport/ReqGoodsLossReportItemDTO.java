package cn.htdt.goodsprocess.dto.request.goodslossreport;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 商品报损单明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ReqGoodsLossReportItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 报损单号
     */
    private String lossReportItemNo;

    /**
     * 报损单号
     */
    private String lossReportNo;

    /**
     * 源商品编号（商家商品编号）
     */
    private String merchantGoodsNo;

    /**
     * 源商品名称（商家商品名称）
     */
    private String merchantGoodsName;

    /**
     * 店铺商品编号
     */
    private String goodsNo;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 条形码
     */
    private String barcode;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 是否有辅计量单位:1-否;2-是;
     */
    private Integer standardFlag;

    /**
     * 主计量单位对应关系数值
     */
    private BigDecimal mainUnitNum;

    /**
     * 辅计量单位对应关系数值
     */
    private BigDecimal assistUnitNum;

    /**
     * 计量单位ID
     */
    private String calculationUnitNo;

    /**
     * 辅计量单位ID
     */
    private String assistCalculationUnitNo;

    /**
     * 是否有仓 1-否 2-是
     */
    private Integer warehouseType;

    /**
     * 仓库编号
     */
    private String warehouseNo;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 报损出库批次号
     */
    private String batchNo;

    /**
     * 商品单价
     */
    private BigDecimal goodsPrice;

    /**
     * 报损数量
     */
    private BigDecimal lossReportNum;

    /**
     * 报损金额
     */
    private BigDecimal lossReportAmount;

    /**
     * 商品主图片url,缩略图URL
     */
    private String pictureUrl;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称，对应orgName
     */
    private String storeName;

    /**
     * 是否可用:默认2，1：不可用2:可用
     */
    private Integer disableFlag;

    private LocalDateTime modifyTime;
    private String createNo;
    private String createName;
    private LocalDateTime createTime;
    private String modifyNo;
    private String modifyName;
    private Integer deleteFlag;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
