package cn.htdt.goodsprocess.dto.request.goodsmedia;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 详细说明.媒体基础类
 * <p>
 * Copyright: Copyright (c) 2020/9/27 10:40
 * <p>
 * Company: htd
 * <p>
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class ReqGoodsMediaDTO implements Serializable {

    private static final long serialVersionUID = 7451572660332862594L;
    /**
     * 商品编号，包含普通商品、套餐商品等所有商品
     */
    private String goodsNo;
    
    /**
     * 商品媒体编码
     */
    private String mediaNo;

    /**
     * 排序值
     */
    private Integer sortValue;

    /**
     * 资源类型：1001、图片 1002、视频
     */
    private String mediaType;

    /**
     * 商品编码列表
     */
    private List<String> goodsNos;
}
