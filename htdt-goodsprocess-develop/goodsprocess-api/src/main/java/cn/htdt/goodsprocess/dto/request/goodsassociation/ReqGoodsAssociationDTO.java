package cn.htdt.goodsprocess.dto.request.goodsassociation;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class ReqGoodsAssociationDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 组合商品编号
     */
    private String goodsAssociationNo;

    /**
     * 销售渠道：1000-全部，1001-千橙掌柜收银，1002-汇享购微商城
     */
    private String distributionChannel;

    /**
     * 组合商品的总数量
     */
    private Integer associationGoodsTotal;

    /**
     * 组合价
     */
    private BigDecimal associationPrice;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 组合商品关系集合
     */
    private List<ReqGoodsAssociationRelationDTO> list;
}
