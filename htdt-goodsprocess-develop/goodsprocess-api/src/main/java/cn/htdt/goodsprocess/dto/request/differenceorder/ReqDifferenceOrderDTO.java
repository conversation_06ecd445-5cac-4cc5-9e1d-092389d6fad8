package cn.htdt.goodsprocess.dto.request.differenceorder;

import cn.htdt.common.dto.request.ReqComPageDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 差异单请求DRO
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
@Data
public class ReqDifferenceOrderDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 店铺编号
     */
    @ApiModelProperty(value = "多店铺编号")
    private List<String> storeNoList;


    @ApiModelProperty("差异单号")
    private String differenceNo;

    @ApiModelProperty("差异单号集合")
    private List<String> differenceNos;

    @ApiModelProperty("要货单号")
    private String requisitionNo;

    @ApiModelProperty("要货单号集合")
    private List<String> requisitionNos;

    @ApiModelProperty("状态 1001-待收货 1002-已完成")
    private String status;

    @ApiModelProperty("入库人")
    private String inStorageUser;

    @ApiModelProperty("差异品项数")
    private BigDecimal differenceCount;

    @ApiModelProperty("商家编号")
    private String merchantNo;

    @ApiModelProperty("店铺ID, 对应orgId")
    private String storeNo;

    @ApiModelProperty("创建开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate startCreateTime;

    @ApiModelProperty("创建结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate endCreateTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
