package cn.htdt.goodsprocess.dto.request.tradeorder;

import cn.htdt.common.dto.request.ReqBaseDTO;
import lombok.Data;

/**
 * <p>
 * 商品图信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
@Data
public class ReqTradeGoodsMediaDTO extends ReqBaseDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 商品媒体编码
     */
    private String mediaNo;

    /**
     * 商品编号，包含普通商品、套餐商品等所有商品
     */
    private String itemCode;

    /**
     * sku
     */
    private String skuCode;

    /**
     * 排序值
     */
    private Integer sortValue;

    /**
     * 上传到本服图片url,缩略图URL
     */
    private String pictureUrl;

    /**
     * 原图片url,缩略图URL
     */
    private String skuPictureUrl;

    /**
     * 资源类型：1001、图片 1002、视频
     */
    private String mediaType;

    /**
     * 1、不是主图 2、是主图
     */
    private Integer mainPictureFlag;

    /**
     * 图片展示区域 0:轮播 1:详情
     */
    private Integer pictureShowArea;

    /**
     * 视频url
     */
    private String videoUrl;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件大小(单位M)
     */
    private String fileSize;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称，对应orgName
     */
    private String storeName;

    /**
     * 是否可用:默认2，1：不可用2:可用
     */
    private Boolean disableFlag;

    /**
     * 阿里云视频ID标识
     */
    private String aliVideoId;

    /**
     * 视频商品时长,单位(秒)
     */
    private String videoDuration;


}
