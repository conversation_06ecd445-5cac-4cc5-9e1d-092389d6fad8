package cn.htdt.goodsprocess.dto.request.storecategory;

import cn.htdt.common.dto.request.ReqComPageDTO;
import cn.htdt.common.enums.goods.CategoryLevelEnum;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * 店铺类目 数据查询入参实体类
 *
 * <AUTHOR>
 * @date 2020/10/22
 **/
@Data
public class ReqStoreCategoryInquireDTO extends ReqComPageDTO implements Serializable {

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 父类目节点ID
     */
    private String parentNo;

    /**
     * 获取销售类目级别
     * 默认获取三四级销售类目
     */
    private String categoryLevel = CategoryLevelEnum.THREE.getCode();

    /**
     * 用户身份(1运营，2商家，4-店铺；8-单店)
     */
    private Integer loginIdentity;

    /**
     * 展示无分类标识，1不展示 2展示 默认展示
     */
    private Integer exhibitionNoCategoryFlag;

    /**
     * 排序值
     */
    private String orderBy;

    /**
     * 查询类目类型,1001:店铺类目查询,1002:销售类目查询
     */
    private String categoryType;

    /**
     * 商品类型 1001-实物商品;1004-称重商品
     */
    private String goodsType;

    /**
     * 商品编号集合
     */
    private List<String> goodsNoList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
