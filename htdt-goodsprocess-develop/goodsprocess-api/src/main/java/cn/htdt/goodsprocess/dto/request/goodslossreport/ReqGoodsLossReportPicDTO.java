package cn.htdt.goodsprocess.dto.request.goodslossreport;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <p>
 * 商品报损单图片表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ReqGoodsLossReportPicDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 报损单图片号
     */
    private String lossReportPicNo;

    /**
     * 报损单号
     */
    private String lossReportNo;

    /**
     * 排序值
     */
    private Integer sortValue;

    /**
     * 上传本服图片url,缩略图URL
     */
    private String pictureUrl;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }


}
