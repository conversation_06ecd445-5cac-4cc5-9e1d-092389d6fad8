package cn.htdt.goodsprocess.dto.request.freighttemplate;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 商品运费模板响应VO
 *
 * <AUTHOR>
 * @date 2020年11月12日
 */
@Data
public class ReqGoodsFreightTemplateDTO implements Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = 2755013846237733229L;

	/**
	 * 商品编码
	 */
	private String goodsNo;

	/**
	 * 商品编号集合，包含普通商品、套餐商品等所有商品
	 */
	private List<String> goodsNos;

	/**
	 * 商品数量
	 */
	private int goodsNum;

	/**
	 * 模板编号
	 */
	private String templateNo;

	/**
	 * 运送区域
	 */
	private String regionArea;

}
