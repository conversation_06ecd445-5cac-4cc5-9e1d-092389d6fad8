package cn.htdt.goodsprocess.dto.request.purchaseorder;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * 校验采购单和采退中是否存在未出库商品入参
 *
 * <AUTHOR>
 * @date 2020年11月9日
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ReqPurchaseGoodsDTO extends ReqComPageDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 订单状态1001待出库 1002部分出库 1003完成
     */
    private List<String> orderStatusList;

    /**
     *商家ID
     */
    private String merchantNo;
    /**
     *店铺ID，对应orgId
     */
    private String storeNo;
    /**
     *归属平台编号
     */
    private String companyNo;
    /**
     *归属分部编号
     */
    private String branchNo;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
