package cn.htdt.goodsprocess.dto.request.goodsaudit;

import cn.htdt.common.dto.request.ReqBaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 商品审核配置表请求实体
 *
 * <AUTHOR>
 * @date 2020年11月10日
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReqGoodsAuditManageDTO extends ReqBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 审核配置编号
     */
    private String auditManageNo;

    /**
     * 审核类型(1001:分发审核 1002:上架审核 1003:云池审核)
     */
    private String auditType;

    /**
     * 审核后是否纳入商品主数据(1001:是 1002:否)
     */
    private String goodsMainDataFlag;

    /**
     * 商品是否需要审核(1001:是 1002:否)
     */
    private String goodsAuditFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
