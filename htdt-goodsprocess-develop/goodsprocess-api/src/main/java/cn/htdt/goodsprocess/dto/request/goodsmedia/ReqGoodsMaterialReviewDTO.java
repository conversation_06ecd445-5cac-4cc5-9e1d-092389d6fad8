package cn.htdt.goodsprocess.dto.request.goodsmedia;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import lombok.Data;	

/**
 * 商品素材审核记录请求实体
 *
 * <AUTHOR>
 * @date 2020年12月16日
 */
@Data
public class ReqGoodsMaterialReviewDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 媒体素材审核编号
     */
    private String materialReviewNo;

    /**
     * 商品媒体编码
     */
    private String mediaNo;

    /**
     * 请求数据
     */
    private String requestJson;

    /**
     * 返回结果
     */
    private String responseResult;

    /**
     * 状态 1001待审核 1002审核中 1003已审核（包含审核成功和失败）
     */
    private String videoAuditStatus;

    /**
     * 资源类型：1001、图片 1002、视频
     */
    private String mediaType;

    /**
     * 归属平台名称
     */
    private String companyName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称，对应orgName
     */
    private String storeName;

    /**
     * 归属分部编号
     */
    private String branchNo;

    /**
     * 归属分部名称
     */
    private String branchName;

    private String createNo;

    private String modifyNo;

    /**
     * 是否可用，默认2可用，1不可用
     */
    private Boolean disableFlag;
    
    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;
    
    /**
     * 归属平台编码
     */
    private String companyNo;
    
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
