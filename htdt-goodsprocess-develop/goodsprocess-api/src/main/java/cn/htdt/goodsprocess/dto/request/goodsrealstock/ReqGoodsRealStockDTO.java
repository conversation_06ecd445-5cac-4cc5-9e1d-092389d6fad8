package cn.htdt.goodsprocess.dto.request.goodsrealstock;

import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 实体库存表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReqGoodsRealStockDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 店铺编号
     */
    private List<String> storeNoList;


    /**
     * 商品编号，包含普通商品、套餐商品等所有商品
     */
    private String goodsNo;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 总库存数量，即实体库存
     */
    private BigDecimal realStockNum;

    /**
     * 冻结库存数量，即预锁数量
     */
    private BigDecimal freezeStockNum;

    /**
     * 可用库存数量，即可售库存
     */
    private BigDecimal availableStockNum;

    /**
     * 待发货数量
     */
    private BigDecimal deliverStockNum;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 平台编号
     */
    private String companyNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称，对应orgName
     */
    private String storeName;

    /**
     * 是否入仓:(1:否 2:是)
     */
    private Integer warehouseFlag;

    /**
     * 总库存区间开始
     */
    private Integer totalStockBegin;

    /**
     * 总库存区间结束
     */
    private Integer totalStockEnd;


    /**
     * 可售库存区间开始
     */
    private Integer saleStockBegin;

    /**
     * 可售库存区间结束
     */
    private Integer saleStockEnd;

    /**
     * 商品来源 1001-平台自建;1002-商家自建;1003-店铺自建;1004:一键代发;1005:商家分发;1006:云池分发;1007:中台同步;
     */
    private String goodsSourceType;
    /**
     * 是否启用串码(1:否 2:是)
     */
    private Integer imeiFlag;

    /**
     * 是否可用:默认1，1：可用，其余不可用
     */
    private Integer disableFlag;

    /**
     * 用户身份(1运营，2商家，4-店铺；8-单店)
     */
    private Integer loginIdentity;

    /**
     * 父商品编码
     */
    private String parentGoodsNo;

    /**
     * 系列商品类型 1001:主品 1002:子品
     */
    private String childFlag;

    /**
     * 商品编码集合
     */
    private List<String> goodsNoList;

    /**
     * 是否是商家同步商品:(1:否 2:是)
     */
    private Integer syncFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
