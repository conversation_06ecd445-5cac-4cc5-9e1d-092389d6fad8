package cn.htdt.goodsprocess.dto.request.goodsrealstock;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020-12-23
 * @Description 检查、锁定、核减入参的商品公共参数
 **/
@Data
public class ReqCommonGoodsStockByOrderDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品的个数
     */
    private BigDecimal goodsNum;

    /**
     * 从ES查询商品 1否2是，为空或其他值时取nacos配置
     */
    private Integer reqUseEsFlag;

    // 2023-08-18蛋品 lixiang  商品管理 多单位商品下单
    /**
     * 主单位商品编号
     */
    private String rawGoodsNo;

    /**
     * 主单位商品数量
     */
    private BigDecimal rawGoodsNum;

    /**
     * 行编号
     */
    private String itemNo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}