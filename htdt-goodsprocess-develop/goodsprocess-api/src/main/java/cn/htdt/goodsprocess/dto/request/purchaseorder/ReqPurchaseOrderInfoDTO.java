package cn.htdt.goodsprocess.dto.request.purchaseorder;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 采购单查询请求Dto
 *
 * <AUTHOR>
 * @date 2020年10月22日
 */
@Data
public class ReqPurchaseOrderInfoDTO implements Serializable {
    private static final long serialVersionUID = 5757956719471098804L;
    //入库信息必填
    private ReqStorageGoodsDTO reqStorageGoodsDTO;
    //编辑页面 新增供应商数据
    private ReqAddPurchaseOrderDTO reqPurchaseReturnOrderDTO;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
