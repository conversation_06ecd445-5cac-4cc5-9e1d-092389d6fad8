package cn.htdt.goodsprocess.dto.request.tradeorder;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/8/17 17:06
 */
@Data
public class ReqTradeOrdersDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 会员编号
     */
    private String buyerCode;

    /**
     * 会员名称
     */
    private String buyerName;

    /**
     * 卖家类型
     */
    private String sellerType;

    /**
     * 卖家名称
     */
    private String sellerName;

    /**
     * 订单来源 1：商城，2：VMS开单，3：超级老板PC，4：超级老板APP 9 POP转采购订单 20:S2B-江苏移动 , 21:S2B-云链
     */
    private String orderFrom;

    /**
     * 订单商品总数量
     */
    private Integer totalGoodsCount;

    /**
     * 订单商品总金额   所有订单行的商品总金额合计
     */
    private BigDecimal totalGoodsAmount;

    /**
     * 运费总金额
     */
    private BigDecimal totalFreight;

    /**
     * 用券优惠总金额   订单分担优惠券总金额
     */
    private BigDecimal totalDiscountAmount;

    /**
     * 店铺用券优惠总金额   分担优惠券总金额中，店铺优惠总金额
     */
    private BigDecimal shopDiscountAmount;

    /**
     * 平台用券优惠总金额   分担优惠券总金额中，平台优惠总金额
     */
    private BigDecimal platformDiscountAmount;

    /**
     * 使用返利金额 (VMS开单时才会输入）
     */
    private BigDecimal usedRebateAmount;

    /**
     * 议价后商品总价
     */
    private BigDecimal bargainingOrderAmount;

    /**
     * 议价后运费金额
     */
    private BigDecimal bargainingOrderFreight;

    /**
     * 订单总价
     */
    private BigDecimal orderTotalAmount;

    /**
     * 订单实付金额=商品总金额-用券优惠总金额-议价优惠总金额（不含运费）
     */
    private BigDecimal orderPayAmount;

    /**
     * 订单创建时间
     */
    private LocalDateTime createOrderTime;

    /**
     * 采购订单状态:采购单状态1001待出库 1002部分出库 1003完成 1004:撤销
     */
    private String orderStatus;

    /**
     * 是否是取消订单 0:未取消，1：已取消
     */
    private Integer isCancelOrder;

    /**
     * 支付方式：1：余额帐支付，2：平台账户支付，3：在线支付
     */
    private String payType;

    /**
     * 支付时间
     */
    private LocalDateTime payOrderTime;

    /**
     * 是否要发票(0:不要，1:要)
     */
    private Integer isNeedInvoice;

    /**
     * 发票类型 1：普通发票，2：增值税发票 3:线下开票
     */
    private String invoiceType;

    /**
     * 普通发票抬头
     */
    private String invoiceNotify;

    /**
     * 增值税发票公司名称
     */
    private String invoiceCompanyName;

    /**
     * 纳税人识别号
     */
    private String taxManId;

    /**
     * 开户行名称
     */
    private String bankName;

    /**
     * 配送方式1:供应商配送  2:自提
     */
    private String deliveryType;

    /**
     * 收货人姓名
     */
    private String consigneeName;

    /**
     * 收货地址-省
     */
    private String consigneeAddressProvince;

    /**
     * 收货地址-市
     */
    private String consigneeAddressCity;

    /**
     * 收货地址-区
     */
    private String consigneeAddressDistrict;

    /**
     * 收货地址-镇
     */
    private String consigneeAddressTown;

    /**
     * 物流公司
     */
    private String logisticsCompany;

    /**
     * 物流编号
     */
    private String logisticsNo;

    private String consigneePhoneNum;

    private String dsConsigneePhoneNum;

    private String contactPhone;

    private String dsContactPhone;

    private String bankAccount;

    private String dsBankAccount;

    private String consigneeAddress;

    private String dsConsigneeAddress;

    private String consigneeAddressDetail;

    private String dsConsigneeAddressDetail;

    private String invoiceAddress;

    private String dsInvoiceAddress;

    /**
     * 数据来源：1007=千橙采购；3001=羊乃世家
     */
    private String dataSource;


}

