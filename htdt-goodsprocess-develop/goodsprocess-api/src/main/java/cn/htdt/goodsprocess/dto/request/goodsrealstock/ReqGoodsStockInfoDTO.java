package cn.htdt.goodsprocess.dto.request.goodsrealstock;

import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.goodsprocess.dto.request.goodsbase.ResGoodsBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 实体库存表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReqGoodsStockInfoDTO extends ResGoodsBaseDTO {

    private static final long serialVersionUID = 1L;

    /************下面对外参数-start*************/

    /**
     * 商品编号，包含普通商品、套餐商品等所有商品
     */
    private String goodsNo;

    /**
     * 仓库编号
     */
    private String warehouseNo;

    /**
     * 仓库编号
     */
    private String shelfWarehouseNo;

    /**
     * 商品仓库管理表主键
     */
    private String relationNo;

    /**
     * 操作类型(1001:加库 1002：减库)
     */
    private String operType;

    /**
     * 来源单据类型(1001:订单下单 1002:销售退货单 1003:调拨出库 1004:调拨入库 1005:采购入库 1006:采购退货出库 1007:盘点入库 1008:盘点出库 1009:发货出库 1010:手动调整出库 1011:无仓转有仓出库 1012:有仓转无仓出库 1013有仓转无仓并继承原库存入库)
     */
    private String billType;
    /**
     * 是否将原仓库库存转成无仓库存 1-否 2-是，即是否继承原库存: 1-否 2-是(billType部分类型适用，如1011，1011，1012)
     */
    private Integer warehouseTransferFlag;
    /**
     * 退货状态
     */
    private String returnStatus;
    /**
     * 退货商品状态
     */
    private String orderStatus;
    /**
     * 出入库数量
     */
    private BigDecimal stockNum;

    /**
     * 主计量单位对应关系数值
     */
    private BigDecimal mainUnitNum;

    /**
     * 辅计量单位对应关系数值
     */
    private BigDecimal assistUnitNum;

    /**
     * 商品串码list
     */
    private List<String> goodsImeiAddNos;

    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 商品形式 1001-普通商品;1002-系列商品;
     */
    private String goodsFrom;
    /**
     * 商品类型 1001-实物商品;1002-虚拟商品;1003-服务商品（仅供平台使用）;1004-称重商品
     */
    private String goodsType;
    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库名称
     */
    private String shelfWarehouseName;
    /**
     * 仓库类型
     */
    private String warehouseType;

    /**
     * 单据编号
     */
    private String billCode;

    /**
     * 子单据编码
     */
    private String subBillCode;

    /**
     * 来源单据编号（例如：订单退库存时订单编号，采购退单时采购单编号）
     */
    private String sourceBillCode;

    /**
     * 创建ID
     */
    private String createNo;

    /**
     * 创建名称
     */
    private String createName;

    /**
     *  操作人编号
     * */
    private String modifyNo;
    /**
     *  操作人名称
     * */
    private String modifyName;
    /**
     * 用户身份(1运营，2商家，4-店铺；8-单店)
     */
    private Integer loginIdentity;

    /**
     * 允许负卖：1=不允许，默认是1；2=允许
     */
    private Integer allowNegativeSell = WhetherEnum.NO.getCode();

    /**
     * 是否启动效期管理标识(1:否 2:是)
     */
    private Integer validityPeriodManageFlag;

    /**
     * 商品效期批次号
     */
    private String shelfBatchNo;

    /**
     * 效期批次数量（用于生成效期批次）
     */
    private BigDecimal shelfBatchNum;

    /**
     * 保质期,正整形
     */
    private Integer qualityGuaranteePeriod;

    /**
     * 保质期单位,day:日 month:月 year:年,枚举:PlanCycleTypeEnum
     */
    private String shelfLifeUnit;

    /**
     * 生产日期
     */
    private LocalDate productionDate;

    /**
     * 自定义商品编号
     */
    private String customGoodsNo;



    /************下面对外参数-start*************/
    /**
     * 是否入仓(1:否 2:是)，与是否虚拟仓库 1-否 2-是相反----库里目前是否入仓
     */
    private Integer warehouseFlag;
    /**
     * 是否入仓(1:否 2:是)，与是否虚拟仓库 1-否 2-是相反----所传是否入仓
     */
    private Integer outWarehouseFlag;
    /**
     * 是否启用串码(1:否 2:是)
     */
    private Integer imeiFlag;

    /**
     * 商品原始编号（默认是与goods_no一致，分发到店铺下才不一致）
     */
    private String originalGoodsNo;

    /**
     * 商品来源 1001-平台自建;1002-商家自建;1003-店铺自建;1004:一键代发;1005:商家分发;1006:云池分发;1007:中台同步;
     */
    private String goodsSourceType;

    /**
     * 商品仓库关联库数量
     */
    private BigDecimal relationStockNum;

    /**
     * 商品共享库数量
     */
    private BigDecimal goodsStockNum;

    /**
     * 开启效期商品数量
     */
    private BigDecimal shelfGoodsNum;

    /**
     * 是否有辅计量单位:1-否;2-是;
     * 采购单时，是否按辅计量单位采购
     */
    private Integer standardFlag;

    /**
     * 转换率(辅转主)
     */
    private BigDecimal conversionRate;

    /**
     * 主计量单位ID
     */
    private String calculationUnitName;
    /**
     * 辅计量单位名称
     */
    private String assistCalculationUnitName;

    /**
     * 是否虚拟仓库 1-否 2-是，与是否入仓(1:否 2:是)相反
     */
    private Integer virtualWarehouseFlag;

    /**
     * 系列商品父ID
     */
    private String parentGoodsNo;

    /**
     * 云池原型商品商品编号
     */
    private String cloudPoolGoodsNo;

    /**
     * 失效时间
     */
    private long expireTime;

    /**
     * 商品形式 1001-普通商品;1002-系列商品
     */
    private String goodsForm;

    /**
     * 第一属性值编码
     */
    private String firstAttributeValueName;

    /**
     * 第二属性值编码
     */
    private String secondAttributeValueName;

    /**
     * 第三属性值编码
     */
    private String thirdAttributeValueName;

    /**
     * 第一、二、三属性值编码，格式：first-second-third
     */
    private String attributeValueName;

    /**
     * 未变前时出入库数量
     */
    private BigDecimal originalStockNum;

    /**
     * 商品上下架状态1001-未上架;1002:已上架
     */
    private String goodsStatus;

    /**
     *采购单价
     */
    private BigDecimal purchaseUnitPrice;

    /**
     * 非本商品自建的商品信息-原始商品信息
     * @return
     */
    private ReqOriginalGoodsStockInfoDTO reqOriginalGoodsStockInfoDTO;


    /*****original对应的信息******/
    /**
     * 当前库存数量
     */
    @ApiModelProperty("当前库存数量")
    private BigDecimal currentStockNum;

    /**
     * 归属平台编号
     */
    @ApiModelProperty("original归属平台编号")
    private String originalCompanyNo;

    /**
     * 归属平台名称
     */
    @ApiModelProperty("original归属平台名称")
    private String originalCompanyName;

    /**
     * 归属分部编号
     */
    @ApiModelProperty("original归属分部编号")
    private String originalBranchNo;

    /**
     * 归属分部名称
     */
    @ApiModelProperty("original归属分部名称")
    private String originalBranchName;

    /**
     * 商家编号
     */
    @ApiModelProperty("original商家编号")
    private String originalMerchantNo;

    /**
     * 商家名称
     */
    @ApiModelProperty("original商家名称")
    private String originalMerchantName;

    /**
     * 门店编号，对应orgId
     */
    @ApiModelProperty("original门店编号，对应orgId")
    private String originalStoreNo;

    /**
     * 店铺名称，对应orgName
     */
    @ApiModelProperty("original店铺名称，对应orgName")
    private String originalStoreName;

    // 2023-08-18蛋品 lixiang  商品管理 多单位商品下单
    /**
     * 主单位商品编号
     */
    private String rawGoodsNo;

    /**
     * 主单位商品数量
     */
    private BigDecimal rawGoodsNum;
    /**
     * 多单位商品类型 1001:主单位商品 1002:子
     */
    private String multiUnitType;

    /**
     * 行编号
     */
    private String itemNo;

    /**
     * 是否继承库存 1：否 2：是
     */
    private Integer extendsFlag;

    /**
     * 20230928-蛋品-zxy-调拨单-入库, 强制完成
     * 重新生成的来源单据类型(1001:订单下单 1002:销售退货单 1003:调拨出库 1004:调拨入库 1005:采购入库 1006:采购退货出库 1007:盘点入库 1008:盘点出库 1009:发货出库 1010:手动调整出库 1011:无仓转有仓出库 1012:有仓转无仓出库 1013有仓转无仓并继承原库存入库)
     */
    private String regenerateBillType;

    /**
     * 20230928-蛋品-genghao-采购管理-入库
     * 入库数量（未经计算）
     */
    private BigDecimal outStockNum;

    /**
     * 商品标示：0商品。1商品组
     */
    private Integer inventoryType;

    /**
     * 是否删除仓库商品关系表数据：0或者null,删除。1 不删除
     */
    private Integer isDelWarehouseGoodsRelation;

    /**
     * 原商品库存（针对商家商品库存操作时有值）
     */
    private ReqGoodsStockInfoDTO originGoodsStockInfoDTO;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
