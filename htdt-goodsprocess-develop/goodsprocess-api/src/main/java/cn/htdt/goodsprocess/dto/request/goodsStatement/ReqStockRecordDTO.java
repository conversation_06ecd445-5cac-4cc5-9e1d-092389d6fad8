package cn.htdt.goodsprocess.dto.request.goodsStatement;

import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 一体机-经营报表-商品库存记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-20
 */
@Data
public class ReqStockRecordDTO  extends ReqComPageDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "仓库编码")
    private String warehouseNo;

    @ApiModelProperty(value = "库存类型 1001:有货 1002：无货")
    private String stockNumType;

    @ApiModelProperty(value = "商品名称、条形码 条件查询")
    private String goodsStr;

    @ApiModelProperty(value = "商品形式 1001-普通商品;1002-系列商品")
    private String goodsForm;

    @ApiModelProperty(value = "是否启用串码(1:否 2:是)")
    private Integer imeiFlag;

    @ApiModelProperty(value = "商品品牌ID")
    private String brandNo;

    @ApiModelProperty(value = "商品类目ID  目前传的3级类目")
    private String categoryNo;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "店铺编码")
    private String storeNo;

    @ApiModelProperty(value = "类目全路径")
    private String fullIdPath;
}
