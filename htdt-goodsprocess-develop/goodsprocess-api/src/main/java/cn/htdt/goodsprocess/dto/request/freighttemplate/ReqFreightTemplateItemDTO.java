package cn.htdt.goodsprocess.dto.request.freighttemplate;

import java.io.Serializable;
import java.math.BigDecimal;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 运费模板请求实体
 *
 * <AUTHOR>
 * @date 2020年9月7日
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReqFreightTemplateItemDTO extends ReqComPageDTO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -1005818936641114740L;

	/**
	 * 模板详情编号
	 */
	private String templateItemNo;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 归属平台编号
     */
    private String companyNo;

    /**
     * freight_template编号
     */
    private String freightTemplateNo;

    /**
     * 配送方式编码
     */
    private String distributionCode;

    /**
     * 模板类型 {1000: 基本模板类型; 1001:O+O模板类型;}
     */
    private String templateType;

    /**
     * 类型区分 {1001:基本配送方式; 1002:指定条件包邮;}
     */
    private String type;

    /**
     * 计费方式 包邮条件 {1010:按件数; 1030:一口价}
     */
    private String chargeWay;

    /**
     * 时间范围配置
     */
    private String timeRange;

    /**
     * 针对每个配送方式的 运费的规则
     */
    private String freightRule;

    /**
     * 配送区域
     */
    private String distributionRegion;

    /**
     * 是否已经删除，默认1未删除，其余已删除
     */
    private Integer disableFlag;

    /**
     * 归属平台名称
     */
    private String companyName;

    /**
     * 归属分部编号
     */
    private String branchNo;

    /**
     * 归属分部名称
     */
    private String branchName;

    /**
     * 运费区域id
     */
    private String areaId;
    
    /**
     * 首件
     */
    private Integer firstPiece;
    
    /**
     *  首费
     */
    private BigDecimal firstAmount;
    
    /**
     * 续件
     */
    private Integer nextPiece;
    
    /**
     *  续费
     */
    private BigDecimal nextAmount;
    
    /**
     * 一口价
     */
    private BigDecimal fixedAmount;
    
    /**
     * 运费区域名称
     */
	private String distributionRegionName; 
}
