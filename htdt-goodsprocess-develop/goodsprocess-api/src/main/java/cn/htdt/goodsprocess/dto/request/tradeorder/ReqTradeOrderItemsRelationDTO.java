package cn.htdt.goodsprocess.dto.request.tradeorder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;

@Data
public class ReqTradeOrderItemsRelationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private BigInteger id;

    /**
     * B2B订单号
     */
    private String orderNo;

    /**
     * 订单行号
     */
    private String orderItemNo;

    /**
     * 商品SKU编码
     */
    private String skuCode;

    /**
     * 千橙掌柜商品编号
     */
    private String goodsNo;

    /**
     * 是否入库 1否2 是
     */
    private Integer stockFlag;

    /**
     * 计量单位ID
     */
    private String calculationUnitName;

    /**
     * 是否有辅计量单位:1-否;2-是;
     */
    private Integer standardFlag;

    /**
     * 仓库编号
     */
    private String warehouseNo;

    /**
     * 辅计量单位名称
     */
    private String assistCalculationUnitName;

    /**
     * 主计量单位对应关系数值
     */
    private BigDecimal mainUnitNum;

    /**
     * 辅计量单位对应关系数值
     */
    private BigDecimal assistUnitNum;

    /**
     * 转换率(辅转主)
     */
    private BigDecimal conversionRate;

    /**
     * 是否入仓(1:否 2:是)
     */
    private Integer warehouseFlag;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 千橙掌柜goodsName
     */
    private String htdtGoodsName;

    /**
     * 与采购单位对应关系值,0 < 对应关系 <= 999 之间的正整数
     */
    private BigDecimal unitRelationNum;

    /**
     * 与采购单位对应关系
     */
    private String unitRelation;

    /**
     * 买家账号
     */
    private String relationBuyerCode;

    @ApiModelProperty(value = "商品数量")
    private BigDecimal goodsNum;

    @ApiModelProperty(value = "生产日期")
    private LocalDate productionDate;

    /**
     * 多单位商品类型 1001:主单位商品 1002:子
     */
    private String multiUnitType;

    /**
     * 多单位主品编号
     */
    private String multiUnitGoodsNo;

}
