package cn.htdt.goodsprocess.dto.request.goodsrealstock;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 虚拟仓商品判断
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-02
 */
@Data
@EqualsAndHashCode
public class ReqGoodsWarehouseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品编号，包含普通商品、套餐商品等所有商品
     */
    private String goodsNo;

    /**
     * 是否入仓(1:否 2:是)
     */
    private Integer warehouseFlag;

    /**
     * 仓库编号
     */
    private String warehouseNo;

}
