package cn.htdt.goodsprocess.dto.request.purchaseorder;

import cn.htdt.common.dto.request.ReqBaseDTO;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * 采购单强制完成商品行
 *
 * <AUTHOR>
 * @date 2022-05-06
 */
@Data
public class ReqFinishPurchaseOrderGoodsListDTO extends ReqBaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 采购单编码
     */
    private String purchaseCode;

    private List<ReqFinishPurchaseOrderGoodsDTO> purchaseOrderGoodsList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
