package cn.htdt.goodsprocess.dto.request.differenceorder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * 差异单商品行请求DRO
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
@Data
public class ReqDifferenceOrderGoodsBatchEditDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "差异单商品行集合")
    private List<ReqDifferenceOrderGoodsEditDTO> differenceOrderGoodsEditDTOList;

    @ApiModelProperty(value = "请求用户信息json", name = "adminParams")
    private String adminParams;

    @ApiModelProperty(value = "差异单号")
    private String differenceNo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
