package cn.htdt.goodsprocess.dto.request.salecategory;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Map;

/**
 * 一体机类目排序 入参实体类
 *
 * <AUTHOR>
 * @date 2023-02-24
 */
@Data
public class ReqScCategorySortDTO {
    private static final long serialVersionUID = 1L;

    /**
     * 类目类型(1001：商家类目 1002：店铺类目)
     */
    private String categoryType;

    /**
     * 类目排序新增集合
     */
    private Map<String, Integer> addCategorySort;

    /**
     * 类目排序修改集合
     */
    private Map<String, Integer> updateCategorySort;

    /**
     * 店铺编号
     */
    private String storeNo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
