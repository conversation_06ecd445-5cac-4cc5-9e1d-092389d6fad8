package cn.htdt.goodsprocess.dto.request.purchaseorder;

import cn.htdt.common.dto.request.ReqBaseDTO;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 采购单拥有的商品详情接口
 *
 * <AUTHOR>
 * @date 2020年9月9日
 */
@Data
public class ReqPurchaseOrderDetailDTO extends ReqBaseDTO implements Serializable {
    private static final long serialVersionUID = -4931443663160628071L;
    /**
     *采购单编码
     */
    private String purchaseCode;
    /**
     * 区分是否入库 (1:否 2:是) 默认为空查全部
     */
    private Integer warehouseFlag;
    /**
     * 退货单状态
     */
    private Integer orderStatus;
    /**
     *商家ID
     */
    private String merchantNo;
    /**
     *店铺ID，对应orgId
     */
    private String storeNo;
    /**
     * 平台类型
     * */
    private String companyNo;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
