package cn.htdt.goodsprocess.dto.request.goodsrealstock;

import cn.htdt.common.enums.WhetherEnum;
import cn.htdt.common.enums.goods.OperTypeEnum;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020-12-22
 * @Description 下单流程锁定库存、核减库存用
 **/
@Data
public class ReqOperatStockByOrderDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 商品集合
     */
    private List<ReqCommonGoodsStockByOrderDTO> listGoods;

    /**
     * 操作类型(1001:锁库 1002：减库 1003：释放锁库-下单支付前 1004：释放锁库-下单支付后 1005：现金支付下单用（先锁库后减库）)
     */
    private String operType;
    /**
     *  操作人编号
     * */
    private String modifyNo;
    /**
     *  操作人名称
     * */
    private String modifyName;

    /**
     * 失效时间
     */
    private long expireTime;

    /**
     * 允许负卖：1=不允许，默认是1；2=允许
     */
    private Integer allowNegativeSell = WhetherEnum.NO.getCode();

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}