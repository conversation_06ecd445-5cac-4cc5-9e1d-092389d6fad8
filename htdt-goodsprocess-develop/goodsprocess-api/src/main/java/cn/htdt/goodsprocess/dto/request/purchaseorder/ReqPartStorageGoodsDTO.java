package cn.htdt.goodsprocess.dto.request.purchaseorder;

import cn.htdt.common.dto.request.ReqBaseDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 采购单分批入库Dto
 *
 * <AUTHOR>
 * @date 2020年10月19日
 */
@Data
public class ReqPartStorageGoodsDTO extends ReqBaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     *采购单编码
     */
    private String purchaseCode;
    /**
     *采购单商品行编码
     */
    private String purchaseGoodsCode;
    /**
     * 仓库编号
     */
    private String warehouseNo;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * 仓库类型
     */
    private String warehouseType;
    /**
     *商品编码
     */
    private String goodsNo;
    /**
     *商品名称
     */
    private String goodsName;
    /**
     * 出入库数量
     */
    private BigDecimal stockNum;

    /**
     *商家ID
     */
    private String merchantNo;
    /**
     *店铺ID，对应orgId
     */
    private String storeNo;
    /**
     * 平台类型
     * */
    private String companyNo;
    /**
     * 归属分部编号
     */
    private String branchNo;
    /**
     * 归属分部名称
     */
    private String branchName;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 店铺名称，对应orgName
     */
    private String storeName;
    /**
     * 归属平台名称
     */
    private String companyName;
    /**
     * 用户身份(1运营，2商家，4-店铺；8-单店)
     */
    private Integer loginIdentity;

    /**
     * 生产日期
     */
    private LocalDate productionDate;
}