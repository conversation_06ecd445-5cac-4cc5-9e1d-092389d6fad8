package cn.htdt.goodsprocess.dto.request.goodsrealstock;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 根据供应商和仓库分组的实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-02
 */
@Data
@AllArgsConstructor
@EqualsAndHashCode
public class ReqWarehouseSupplierGroupByDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 供应商编号
     */
    private String supplierCode;

    /**
     * 仓库编号
     */
    private String warehouseNo;

}
