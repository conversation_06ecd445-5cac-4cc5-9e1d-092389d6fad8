package cn.htdt.goodsprocess.dto.request.tradeorder;

import cn.htdt.common.dto.request.ReqBaseDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 商品sku信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
@Data
public class ReqTradeGoodsDTO extends ReqBaseDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 商品编码 
     */
    private String itemCode;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品SKU编码
     */
    private String skuCode;

    /**
     * 商品条码
     */
    private String barcode;

    /**
     * 商品图片地址
     */
    private String skuPictureUrl;

    /**
     * 销售单价(区域时保存区域价格，包厢时保存包厢价格，外部供应商商品时本字段为0，商品+商品时为区域销售价）
     */
    private BigDecimal salePrice;

    /**
     * 商品单价种类：销售价，阶梯价，区域价，会员分组价，会员等级价，自定义价格（VMS开单时）等
     */
    private String goodsPriceType;

    /**
     * 商品单价 销售价，阶梯价，区域价，会员分组价，会员等级价
     */
    private BigDecimal goodsPrice;

    /**
     * 成交价 下单时候的销售单价-商家券优惠
     */
    private BigDecimal tradePrice;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 数据来源：1007=千橙采购；3001=羊乃世家，见 DataPurchaseSourceEnum
     */
    private String dataSource;

    /**
     * 商品编码list
     */
    private List<String> goodsNoList;

    /**
     * 商品图片-reqTradeGoodsMediaDTOS
     */
    private List<ReqTradeGoodsMediaDTO> tradeGoodsMediaDTOS;


}
