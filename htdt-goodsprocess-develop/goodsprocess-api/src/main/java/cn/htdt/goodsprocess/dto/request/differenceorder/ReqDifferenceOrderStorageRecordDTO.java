package cn.htdt.goodsprocess.dto.request.differenceorder;

import cn.htdt.goodsprocess.dto.request.requisitionorder.ReqRequisitionOrderStorageRecordDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 差异单商品行入库请求dto
 *
 * <AUTHOR>
 * @since 2023-06-06 11:08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ReqDifferenceOrderStorageRecordDTO extends ReqRequisitionOrderStorageRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 差异商品行编号
     */
    @ApiModelProperty(value = "差异商品行编号")
    private String differenceItemNo;

    /**
     * 差异数量
     */
    @ApiModelProperty(value = "差异数量")
    private BigDecimal differenceCount;

    /**
     * 差异单号
     */
    @ApiModelProperty(value = "差异单号")
    private String differenceNo;

    /**
     * 商品效期批次号
     */
    private String shelfBatchNo;

    /**
     * 效期批次数量（用于生成效期批次）
     */
    private BigDecimal shelfBatchNum;

}
