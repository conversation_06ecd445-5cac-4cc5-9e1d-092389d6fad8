package cn.htdt.goodsprocess.dto.request.tradeorder;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/8/17 16:28
 */
@Data
public class ReqUpdatePurchaseTradeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单状态 10:待审核,20:待支付,21:审核通过待支付,31:已支付待拆单,32:已支付已拆单待下行ERP,40:待发货,50:已发货,61:买家收货,62:到期自动收货
     * <p>
     * 云原生状态 - 100:草稿 ,200:正式,400:卖家取消,401:卖家驳回,500:履约中,501:履约中-已确认收货,502:完成,80:已发起逆向[退款审核中],85:逆向完成[退款完成]
     */
    private String orderStatus;

    /**
     * 是否是取消订单 0:未取消，1：已取消
     */
    private Integer isCancelOrder;

    /**
     * 支付时间
     */
    private LocalDateTime payOrderTime;

    /**
     * 物流公司
     */
    private String logisticsCompany;

    /**
     * 物流编号
     */
    private String logisticsNo;

}

