package cn.htdt.goodsprocess.dto.request.goodsrealstock;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class ReqGoodsStocksDTO extends ReqComPageDTO {

    private BigInteger id;

    /**
     * 店铺编号
     */
    private List<String> storeNoList;
    /**
     * 商品编号，包含普通商品、套餐商品等所有商品
     */

    private String goodsNo;

    /**
     * 第三方ID
     */
    private String thirdGoodsNo;

    /**
     * 是否启用串码(1:否 2:是)
     */
    private Integer imeiFlag;

    /**
     * 商品形式 1001-普通商品;1002-系列商品
     */
    private String goodsForm;

    /**
     * 商品类型 1001-实物商品;1002-虚拟商品;1003-服务商品（仅供平台使用）;1004-称重商品
     */
    private String goodsType;

    /**
     * 商品助记码
     */
    private String goodsHelpCode;

    /**
     * 商品类目ID
     */
    private String categoryNo;

    /**
     * 商品品牌ID
     */
    private String brandNo;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品名称首字母
     */
    private String goodsInitial;

    /**
     * 是否入仓:1-否;2-是;
     */
    private Integer warehouseFlag;

    /**
     * 系列商品父ID
     */
    private String parentGoodsNo;

    /**
     * 云池原型商品商品编号
     */
    private String cloudPoolGoodsNo;

    /**
     * 运费模板ID
     */
    private String freightTemplateNo;

    /**
     * 销售区域ID
     */
    private String salesAreaNo;

    /**
     * 商品来源 1001-平台自建;1002-商家自建;1003-店铺自建;1004:一键代发;1005:商家分发;1006:云池分发
     */
    private String goodsSourceType;

    /**
     * 商品型号
     */
    private String goodsModel;

    /**
     * 卖点描述
     */
    private String goodsSaleDescription;

    /**
     * 计量单位ID
     */
    private String calculationUnitNo;

    /**
     * 是否有辅计量单位:1-否;2-是;
     */
    private Integer standardFlag;

    /**
     * 辅计量单位id
     */
    private String assistCalculationUnitNo;

    /**
     * 主计量单位对应关系数值
     */
    private BigDecimal mainUnitNum;

    /**
     * 辅计量单位对应关系数值
     */
    private BigDecimal assistUnitNum;

    /**
     * (辅转主)
     */
    private BigDecimal conversionRate;

    /**
     * 条形码
     */
    private String barcode;

    /**
     * 商品状态 1001-未上架;1002-已上架;
     */
    private String goodsStatus;

    /**
     * 下架原因
     */
    private String downMessage;

    /**
     * 支付方式(1000:网店支付 1001:到店支付;1000,1001:到店支付+网店支付)
     */
    private String paymentMethod;

    /**
     * 配送方式(1000: 自提 1100：配送;1000,1100:自提+配送)
     */
    private String deliveryWay;

    /**
     * 首次上架时间
     */
    private LocalDateTime firstShelfTime;

    /**
     * 审核原因
     */
    private String auditMessage;

    /**
     * 商品审核状态 2001:等待审核;2002:审核中;2003:审核成功; 2004:审核失败
     */
    private String auditStatus;

    /**
     * 基础信息审核单号
     */
    private String auditRecordNo;

    /**
     * 商品基本审核信息是否变更标识位 1:否 2:是
     */
    private Integer auditDataModifyFlag;

    /**
     * 是否纳入商品主数据库(1:否 2:是)
     */
    private Integer mainDataFlag;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号
     */
    private String storeNo;

    /**
     * 店铺名称，对应orgName
     */
    private String storeName;

    /**
     * 归属平台编码
     */
    private String companyNo;

    /**
     * 归属平台名称
     */
    private String companyName;

    /**
     * 归属分部编码
     */
    private String branchNo;

    /**
     * 归属分部名称
     */
    private String branchName;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 是否可用:默认2，1：不可用2:可用
     */
    private Integer disableFlag;

    /**
     * 数据逻辑删除标识，默认1未删除，2已删除
     */
    private Integer deleteFlag;
    /**
     * 第一属性名称编码
     */
    private String firstAttributeName;
    /**
     * 第一属性值编码
     */
    private String firstAttributeValueName;

    /**
     * 第二属性名称编码
     */
    private String secondAttributeName;
    /**
     * 第二属性值编码
     */
    private String secondAttributeValueName;

    /**
     * 第三属性名称编码
     */
    private String thirdAttributeName;
    /**
     * 第三属性值编码
     */
    private String thirdAttributeValueName;

    /**
     * 商品原始编号（默认是与goods_no一致，分发到店铺下才不一致）
     */
    private String originalGoodsNo;

    /**
     * 系列商品类型 1001:主品 1002:子品
     */
    private String seriesType;
    /**
     * 限价
     */
    private BigDecimal limitPrice;

    /**
     * 市场价
     */
    private BigDecimal marketPrice;

    /**
     * 采购价
     */
    private BigDecimal purchasePrice;

    /**
     * 零售价
     */
    private BigDecimal retailPrice;

    /**
     * 销量
     */
    private BigDecimal salesVolume;

    /**
     * 浏览量
     */
    private Long browseAmount;

    /**
     * 是否视频商品(1:普通商品 2:视频商品)
     */
    private Integer goodsVideo;

    /**
     * 分发状态,3001:未分发,3002:已分发,3003,已失效，3004，等待商品审核
     */
    private String distributionStatus;

    /**
     * 审核类型(1001:分发审核 1002:上架审核 1003:云池审核)';
     */
    private String auditType;

    /**
     * 云池供货价
     */
    private BigDecimal cloudPoolSupplyPrice;
    /**
     * 最新上架时间
     */
    private LocalDateTime latestModifyTime;
    /**
     * 云池供货店铺编码
     */
    private String supplyStoreNo;
    /**
     * 分销商品标记 1:否 2:是
     */
    private Integer distributeGoodsFlag;

    /**
     * 应用程序来源 1001:千橙掌柜PC 1002:千橙掌柜APP 1003:千橙掌柜收银
     */
    private String appChannelSource;

    /********************入参***********************/
    /**
     * 入参条件：商品名称或助记码-模糊查询用
     */
    private String goodsStr;

    /**
     * 入参条件：查询商品类型 1001：全部 1002:平台商品  1003:商家/店铺商品 1004:商家创建商品  1005:店铺创建商品 1006:店铺查询 1007:云池商品查询
     */
    private String queryType;

    /**
     * 入参条件：入仓类型:0-全部；1-不入仓；2-入仓;
     */
    private Integer warehouseFlagType;

    /**
     * 入参条件：最低实体库存数
     */
    private BigDecimal minStockNum;

    /**
     * 入参条件：最高实体库存数
     */
    private BigDecimal maxStockNum;

    /**
     * 入参条件：最低可售库存数
     */
    private BigDecimal minAvailableStockNum;

    /**
     * 入参条件：最高可售库存数
     */
    private BigDecimal maxAvailableStockNum;

    /**
     * 只展示子品标识=1002 其它展示全部
     */
    private String childFlag;

    /**
     * 是否是商家同步商品:(1:否 2:是)
     */
    private Integer syncFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
