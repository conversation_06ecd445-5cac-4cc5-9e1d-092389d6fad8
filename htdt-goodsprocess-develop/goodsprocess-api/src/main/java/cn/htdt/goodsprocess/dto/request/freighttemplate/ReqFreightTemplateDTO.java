package cn.htdt.goodsprocess.dto.request.freighttemplate;

import java.io.Serializable;
import java.math.BigDecimal;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 运费模板请求实体
 *
 * <AUTHOR>
 * @date 2020年9月10日
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ReqFreightTemplateDTO extends ReqComPageDTO implements Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = 4309104607868744311L;

	/**
	 * 模板编号
	 */
	private String templateNo;

	/**
	 * 商家编号
	 */
	private String merchantNo;

	/**
	 * 归属平台编号
	 */
	private String companyNo;

	/**
	 * 运费模板名称
	 */
	private String name;

	/**
	 * 配送方式编码
	 */
	private String distributionCode;

	/**
	 * 配送区域类型 0:默认全国; -1:非默认，自定义区域
	 */
	private String distributionType;

	/**
	 * 模板类型 {1000: 基本模板类型; 1001:O+O模板类型; }
	 */
	private String templateType;

	/**
	 * 类型区分 {1001:自定义运费; 1002:卖家承担运费; }
	 */
	private String type;

	/**
	 * 计费方式 包邮条件 { 计费方式 1010:按件数; 1030:一口价;}
	 */
	private String chargeWay;

	/**
	 * 是否默认 1:默认; 0:非默认
	 */
	private String defaultFlag;

	/**
	 * 渠道模式，保存格式(BBC,S2B,POS,B2B,O+O)
	 */
	private String channelMode;

	/**
	 * 配送区域
	 */
	private String distributionRegion;

	/**
	 * 是否已经删除，默认1未删除，其余已删除
	 */
	private Integer disableFlag;

	/**
	 * 归属平台名称
	 */
	private String companyName;

	/**
	 * 归属分部编号
	 */
	private String branchNo;

	/**
	 * 归属分部名称
	 */
	private String branchName;

	/**
	 * 首件
	 */
	private Integer templateFirstPiece;

	/**
	 * 首费
	 */
	private BigDecimal templateFirstAmount;

	/**
	 * 续件
	 */
	private Integer templateNextPiece;

	/**
	 * 续费
	 */
	private BigDecimal templateNextAmount;

	/**
	 * 一口价
	 */
	private BigDecimal templateFixedAmount;

	/**
	 * 门店编号，对应orgId
	 */
	private String storeNo;

	/**
	 * 模板来源 1001-平台自建;1002-商家自建;1003-店铺自建
	 */
	private String templateSourceType;


    /**
     * 是否默认   1001:默认;  1002:非默认;
     */
    private String templateDefaultFlag;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
	 * 用户身份(1运营，2商家，4-店铺；8-单店)
     */
    private Integer loginIdentity;


	/**
	 * 是否满额免运费，默认1否，2是
	 */
	private Integer templateFreeFlag;

	/**
	 * 免运费金额限制
	 */
	private BigDecimal templateFreeAmount;
}
