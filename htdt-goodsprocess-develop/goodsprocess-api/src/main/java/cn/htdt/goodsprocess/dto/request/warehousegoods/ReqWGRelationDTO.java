package cn.htdt.goodsprocess.dto.request.warehousegoods;

import cn.htdt.common.dto.request.ReqComPageDTO;
import cn.htdt.goodsprocess.dto.request.commondef.ReqProcessComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.ArrayList;
import java.util.List;

/**
 *  仓库商品关联
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReqWGRelationDTO extends ReqComPageDTO {

    /**
     * 归属平台编号
     */
    @ApiModelProperty("归属平台编号")
    private String companyNo;

    /**
     * 归属平台名称
     */
    @ApiModelProperty("归属平台名称")
    private String companyName;

    /**
     * 归属分部编号
     */
    @ApiModelProperty("归属分部编号")
    private String branchNo;

    /**
     * 归属分部名称
     */
    @ApiModelProperty("归属分部名称")
    private String branchName;

    /**
     * 商家编号
     */
    @ApiModelProperty("商家编号")
    private String merchantNo;

    /**
     * 商家名称
     */
    @ApiModelProperty("商家名称")
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    @ApiModelProperty("门店编号，对应orgId")
    private String storeNo;

    /**
     * 店铺名称，对应orgName
     */
    @ApiModelProperty("店铺名称，对应orgName")
    private String storeName;

    /**
     * 仓库编号-要添加
     */
    @ApiModelProperty("仓库编号-要添加")
    private List<String> warehouseAddNos = new ArrayList<>();

    /**
     * 仓库编号-要删除
     */
    @ApiModelProperty("仓库编号-要删除")
    private List<String> warehouseDelNos = new ArrayList<>();

    /**
     * 商品编码-要添加
     */
    @ApiModelProperty("商品编码-要添加")
    private List<String> goodsAddNos = new ArrayList<>();

    /**
     * 商品编码-要删除
     */
    @ApiModelProperty("商品编码-要删除")
    private List<String> goodsDelNos = new ArrayList<>();

    @ApiModelProperty(value = "用户身份(1运营，2商家，4-店铺；8-单店)", name = "loginIdentity")
    private Integer loginIdentity;

    @ApiModelProperty(value = "请求用户信息json", name = "adminParams")
    private String adminParams;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
