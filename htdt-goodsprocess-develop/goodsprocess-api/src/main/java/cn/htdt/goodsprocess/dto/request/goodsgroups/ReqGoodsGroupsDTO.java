package cn.htdt.goodsprocess.dto.request.goodsgroups;

import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
@Data
@EqualsAndHashCode(callSuper = true)
public class ReqGoodsGroupsDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品组编号
     */
    private String goodsGroupsNo;
    private String merchantGoodsGroupsNo;

    /**
     * 商品组编码list
     */
    private List<String> goodsGroupsNoList;
    /**
     * 商品同步店铺列表
     */
    private List<String> goodsSyncStoreNoList;
    /**
     * 商品组名称
     */
    private String goodsGroupsName;

    /**
     * 是否入仓:1-否;2-是;
     */
    private Integer warehouseFlag;

    /**
     * 是否入仓(1:否 2:是)，与是否虚拟仓库 1-否 2-是相反----所传是否入仓
     */
    private Integer outWarehouseFlag;

    /**
     * 是否继承库存 1：否 2：是
     */
    private Integer extendsFlag;

    /**
     * 商品来源 1001-平台自建;1002-商家自建;1003-店铺自建;1004:一键代发;1005:商家分发;1006:云池分发
     */
    private String goodsSourceType;

    /**
     * 总库存数量，即实体库存
     */
    private BigDecimal realStockNum;

    /**
     * 计量单位ID
     */
    private String calculationUnitNo;

    /**
     * 计量单位符号
     */
    private String calculationUnitSymbol;

    /**
     * 计量单位名称
     */
    private String calculationUnitName;

    /**
     * 关联商品名称集合
     */
    private String relatedGoodsNames;

    /**
     * 生效区域集合
     */
    private String effectiveRegion;

    /**
     * 权限内区域集合
     */
    private List<String> ucRegionList;

    /**
     * 生效区域名称集合
     */
    private String effectiveRegionNames;
    /**
     * 管理商品集合
     */
    List<ReqGoodsGroupsRelatedGoodsDTO> groupsRelatedGoodsList;
    private String createNo;
    private String createName;
    private LocalDateTime createTime;
    private String modifyNo;
    private String modifyName;
    private LocalDateTime modifyTime;

    /**
     * 用户身份(1运营，2商家，4-店铺；8-单店)
     */
    private Integer loginIdentity;
    /**
     * 查询商品类型 1001：全部 1002:平台商品 1003:商家/店铺商品 1004:商家创建商品 1005:店铺创建商品 1006:店铺查询 1007:云池商品查询 1010:平台自建+商家自建+店铺自建+中台同步 1011:中台同步
     */
    private String queryType;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 店铺名称，对应orgName
     */
    private String storeName;
    /**
     * 已选择过的采购商品组编码
     */
    private List<String> notExistGoodsGroups;

    /**
     * 分发门店编号集合
     */
    private List<String> storeNoList;
    /**
     * 商家区域下所有门店编号集合 用于过滤商品组是否重复
     */
    private List<String> storeNoAllList;

    /**
     * app 根据关键字搜索
     */
    private String bossAppGoodsSearchKey;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 商品编码列表
     */
    private List<String> goodsNos;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
