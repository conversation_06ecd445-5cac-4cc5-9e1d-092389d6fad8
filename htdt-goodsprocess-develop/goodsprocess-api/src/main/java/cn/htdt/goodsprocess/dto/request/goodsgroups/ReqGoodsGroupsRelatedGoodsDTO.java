package cn.htdt.goodsprocess.dto.request.goodsgroups;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 商品组管理商品请求DTO
 **/
@Data
public class ReqGoodsGroupsRelatedGoodsDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品组编号
     */
    private String goodsNo;

    /**
     * 商品组编号
     */
    private String goodsGroupsNo;

    /**
     * 商品名称
     */

    private String goodsName;

    /**
     * 商品形式 1001-普通商品;1002-系列商品
     */
    private String goodsForm;

    /**
     * 商品类型 1001-实物商品;1002-虚拟商品;1003-服务商品（仅供平台使用）;1004-称重商品
     */
    private String goodsType;

    /**
     * 计量单位ID
     */
    private String calculationUnitNo;


    /**
     * 计量单位符号
     */
    private String calculationUnitSymbol;

    /**
     * 计量单位名称
     */
    private String calculationUnitName;
    /**
     * 商品类目名称
     */
    private String categoryName;
    /**
     * 类目id
     */
    private String categoryNo;

    /**
     * 规格属性
     */
    private String attributeNames;

    /**
     * 库存映射关系:商品库存
     */
    private BigDecimal goodsMappingStockNum;

    /**
     * 库存映射关系:商品组库存
     */
    private BigDecimal goodsGroupsMappingStockNum;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 门店编号
     */
    private String storeNo;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
