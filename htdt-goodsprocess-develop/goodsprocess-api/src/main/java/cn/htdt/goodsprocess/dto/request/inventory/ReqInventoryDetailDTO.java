package cn.htdt.goodsprocess.dto.request.inventory;

import cn.htdt.common.dto.request.ReqComPageDTO;
import cn.htdt.goodsprocess.dto.pub.GoodsValidityPeriodDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020-09-02
 * @Description 盘点详细 请求DTO
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="盘点详细请求DTO")
public class ReqInventoryDetailDTO extends GoodsValidityPeriodDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 盘点单号
     */
    @ApiModelProperty(value = "盘点单号", name = "inventoryCode")
    private String inventoryCode;

    /**
     * 商品编号，包含普通商品、套餐商品等所有商品
     */
    @ApiModelProperty(value = "商品编号", name = "goodsNo")
    private String goodsNo;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称", name = "goodsName")
    private String goodsName;

    /**
     * 总库存数量
     */
    @ApiModelProperty(value = "总库存数量", name = "stockNum")
    private BigDecimal stockNum;

    /**
     * 盘点总库存数量
     */
    @ApiModelProperty(value = "盘点总库存数量", name = "inventoryStockNum")
    private BigDecimal inventoryStockNum;

    /**
     * 仓库编号
     */
    @ApiModelProperty(value = "仓库编号", name = "warehouseNo")
    private String warehouseNo;

    /**
     * 仓库编码，手动输入的
     */
    @ApiModelProperty(value = "仓库编码", name = "warehouseCode")
    private String warehouseCode;

    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "仓库名称", name = "warehouseName")
    private String warehouseName;

    /**
     * 确认差异人编号
     */
    @ApiModelProperty(value = "确认差异人编号", name = "confirmNo")
    private String confirmNo;

    /**
     * 确认差异人姓名
     */
    @ApiModelProperty(value = "确认差异人姓名", name = "confirmName")
    private String confirmName;

    /**
     * 是否可用:默认2，1：不可用 2：可用
     */
    @ApiModelProperty(value = "是否可用:默认2，1：不可用 2：可用", name = "disableFlag")
    private Integer disableFlag;

    /**
     * 创建人no
     */
    @ApiModelProperty(value = "创建人no", name = "createNo")
    private String createNo;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称", name = "createName")
    private String createName;

    /**
     * 修改人no
     */
    @ApiModelProperty(value = "修改人no", name = "modifyNo")
    private String modifyNo;

    /**
     * 修改人名称
     */
    @ApiModelProperty(value = "修改人名称", name = "modifyName")
    private String modifyName;

    /**
     * 商家编号
     */
    @ApiModelProperty(value = "商家编号", name = "merchantNo")
    private String merchantNo;

    /**
     * 商家名称
     */
    @ApiModelProperty(value = "商家名称", name = "merchantName")
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    @ApiModelProperty(value = "门店编号", name = "storeNo")
    private String storeNo;

    /**
     * 店铺名称，对应orgName
     */
    @ApiModelProperty(value = "店铺名称", name = "storeName")
    private String storeName;

    /**
     * 平台编号
     */
    @ApiModelProperty(value = "平台编号", name = "companyNo")
    private String companyNo;

    /**
     * 平台名称
     */
    @ApiModelProperty(value = "平台名称", name = "companyName")
    private String companyName;

    /**
     * 分部编号
     */
    @ApiModelProperty(value = "分部编号", name = "branchNo")
    private String branchNo;

    /**
     * 分部名称
     */
    @ApiModelProperty(value = "分部名称", name = "branchName")
    private String branchName;

    /**
     * 商品删除标识
     */
    @ApiModelProperty(value = "商品删除标识", name = "goodsDeleteFlag")
    private Integer goodsDeleteFlag;

    /**
     * 批量商品编号
     */
    private List<String> goodsList;

    /**
     * 盘点类型：0 商品；1 商品组
     */
    private Integer inventoryType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
