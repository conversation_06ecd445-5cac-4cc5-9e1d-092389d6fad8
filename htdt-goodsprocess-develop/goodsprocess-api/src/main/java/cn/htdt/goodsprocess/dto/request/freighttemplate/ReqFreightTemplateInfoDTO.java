package cn.htdt.goodsprocess.dto.request.freighttemplate;

import java.io.Serializable;
import java.util.List;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ReqFreightTemplateInfoDTO extends ReqComPageDTO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 5008753834440954629L;
	
	/**
	 * 运费模板主表
	 */
	private ReqFreightTemplateDTO reqFreightTemplateDTO;
	
	/**
	 * 运费模板详情子表
	 */
	private List<ReqFreightTemplateItemDTO> reqFreightTemplateItemList;
}
