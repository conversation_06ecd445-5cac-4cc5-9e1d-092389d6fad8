package cn.htdt.goodsprocess.dto.request.calculationunit;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 计量单位 分页查询入参DTO
 *
 * <AUTHOR>
 * @date 2020-09-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReqCalculationUnitComPageDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 4635748653600743350L;

    /**
     * 计量单位名称
     */
    private String calculationUnitName;

    /**
     * 商品类型 1001-实物商品;1002-虚拟商品;1003-服务商品（仅供平台使用）;1004-称重商品
     */
    private String goodsType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
