package cn.htdt.goodsprocess.dto.request.warehouse;

import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 仓库通用设置DTO
 * <AUTHOR>
 * @Date 2020-09-02
 * @Description 仓库通用管理 请求DTO
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ReqWarehouseSetDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 身份1-运营；2-商家；3-店铺
     */
    private String goodsNo;

    /**
     * 商品来源       1001-平台自建;1002-商家自建;1003-店铺自建;1004:一键代发; @TODO 1004一键代发再定
     * 对应仓库类型   1001:平台仓 1002：商家仓 1003：店铺仓
     * 对应身份：     1-运营；2-商家；3-店铺
     */
    @ApiModelProperty(value = "仓库类型1001:平台仓 1002：商家仓 1003：店铺仓，与身份对应", name = "warehouseType")
    private String warehouseType;

    /**
     * 是否有仓库 1:否 2:是
     */
    @ApiModelProperty(value = "是否有仓库 1:否 2:是", name = "warehouseHasFlag")
    private Integer warehouseHasFlag;

    /**
     * 是否将原仓库库存转成无仓库存 1-否 2-是
     */
    @ApiModelProperty(value = "是否将原仓库库存转成无仓库存 1-否 2-是", name = "warehouseTransferFlag")
    private Integer warehouseTransferFlag;

    /**
     * 用户身份(1运营，2商家，4-店铺；8-单店)
     */
    private Integer loginIdentity;

    /**
     * 创建人no
     */
    @ApiModelProperty(value = "创建人no", name = "createNo")
    private String createNo;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称", name = "createName")
    private String createName;

    /**
     * 修改人no
     */
    @ApiModelProperty(value = "修改人no", name = "modifyNo")
    private String modifyNo;

    /**
     * 修改人名称
     */
    @ApiModelProperty(value = "修改人名称", name = "modifyName")
    private String modifyName;

    /**
     * 商家编号
     */
    @ApiModelProperty(value = "商家编号", name = "merchantNo")
    private String merchantNo;

    /**
     * 商家名称
     */
    @ApiModelProperty(value = "商家名称", name = "merchantName")
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    @ApiModelProperty(value = "门店编号", name = "storeNo")
    private String storeNo;

    /**
     * 店铺名称，对应orgName
     */
    @ApiModelProperty(value = "店铺名称", name = "storeName")
    private String storeName;

    /**
     * 平台编号
     */
    @ApiModelProperty(value = "平台编号", name = "companyNo")
    private String companyNo;

    /**
     * 平台名称
     */
    @ApiModelProperty(value = "平台名称", name = "companyName")
    private String companyName;

    /**
     * 分部编号
     */
    @ApiModelProperty(value = "分部编号", name = "branchNo")
    private String branchNo;

    /**
     * 分部名称
     */
    @ApiModelProperty(value = "分部名称", name = "branchName")
    private String branchName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
