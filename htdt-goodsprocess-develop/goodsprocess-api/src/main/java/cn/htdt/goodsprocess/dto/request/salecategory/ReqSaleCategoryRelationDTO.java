package cn.htdt.goodsprocess.dto.request.salecategory;

import cn.htdt.common.dto.request.ReqBaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 商户/店铺销售类目 入参实体类
 *
 * <AUTHOR>
 * @date 2020-09-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReqSaleCategoryRelationDTO extends ReqBaseDTO {
    private static final long serialVersionUID = 3895063285746862306L;

    /**
     * 用户身份(1运营，2商家，4-店铺；8-单店)
     */
    private Integer loginIdentity;

    /**
     * 类目类型(1001：商家类目 1002：店铺类目)
     */
    private String categoryType;

    /**
     * 类目编号
     */
    private String categoryNo;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称，对应orgName
     */
    private String storeName;

    /**
     * 类目编号集合
     */
    private List<String> categoryNoList;

    /**
     * 选择标识。1 取消选中，2 选中
     */
    private Integer selectFlag;

    /**
     * 确认取消销售类目标识。1 未确认取消，2 确认取消
     */
    private Integer confirmCancellationFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
