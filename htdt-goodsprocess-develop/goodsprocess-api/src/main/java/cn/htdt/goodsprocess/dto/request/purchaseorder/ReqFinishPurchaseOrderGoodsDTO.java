package cn.htdt.goodsprocess.dto.request.purchaseorder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 采购单强制完成商品行
 *
 * <AUTHOR>
 * @date 2022-05-06
 */
@Data
public class ReqFinishPurchaseOrderGoodsDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     *采购单编码
     */
    private String purchaseCode;
    /**
     *采购单商品行编码
     */
    private String purchaseGoodsCode;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
