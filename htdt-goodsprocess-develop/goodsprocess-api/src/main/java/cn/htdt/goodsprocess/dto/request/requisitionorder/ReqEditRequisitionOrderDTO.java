package cn.htdt.goodsprocess.dto.request.requisitionorder;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import cn.htdt.common.dto.request.AdminParamsDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description 修改要货单请求dto
 * 
 * <AUTHOR>
 * @date 2023/5/23 15:44
 **/
@Data
public class ReqEditRequisitionOrderDTO implements Serializable {

    private static final long serialVersionUID = -1752673419978867359L;

    @ApiModelProperty(value = "要货单号", required = true)
    // @NotBlank(message = "要货单号不能为空")
    private String requisitionNo;

    @ApiModelProperty(value = "期望送达时间")
    private LocalDate deliveryDate;

    @ApiModelProperty("单据备注")
    private String remark;

    @ApiModelProperty("操作类型 10-保存 11-提交")
    // @NotNull(message = "操作类型不能为空")
    // @EnumValue(intValues = {10, 11}, message = "操作类型只能为10或11")
    private Integer operateType;

    @ApiModelProperty(value = "创建要货单规则配置", required = true, position = 3)
    private String ruleValue;

    @ApiModelProperty(value = "要货商品行列表", required = true)
    // @NotEmpty(message = "要货商品数据不能为空")
    // @Valid
    private List<ReqRequisitionOrderGoodsDTO> orderGoodsList;

    @ApiModelProperty(value = "登录用户信息", hidden = true)
    private AdminParamsDTO basicInfoParams;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
