package cn.htdt.goodsprocess.dto.request.goodsshelf;

import cn.htdt.common.dto.request.ReqBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 功能描述: 编辑效期批次请求dto
 *
 * @author: xm
 * @date: 2023/06/28 14:52
 */
@Data
public class ReqModifyGoodsShelfDTO extends ReqBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "批次号")
    private String batchNo;

    @ApiModelProperty(value = "剩余数量")
    private BigDecimal leftStockNum;

    @ApiModelProperty(value = "用户身份(1运营，2商家，4-店铺；8-单店)")
    private Integer loginIdentity;

    @ApiModelProperty(value = "商品效期批次号")
    private String shelfBatchNo;

    @ApiModelProperty(value = "商品编号")
    private String goodsNo;

    // 2023-08-18蛋品 lixiang  商品管理 多单位商品下单
    @ApiModelProperty(value = "主单位商品编号")
    private String rawGoodsNo;

    @ApiModelProperty(value = "商品编号")
    private String goodsName;

    @ApiModelProperty(value = "是否入仓(1:否 2:是)")
    private Integer warehouseFlag;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseNo;

    @ApiModelProperty(value = "仓库编码")
    private String shelfWarehouseNo;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "仓库名称")
    private String shelfWarehouseName;

    @ApiModelProperty(value = "效期批次数量（用于生成效期批次）")
    private BigDecimal shelfBatchNum;

    @ApiModelProperty(value = "开启效期商品数量")
    private BigDecimal shelfGoodsNum;

    @ApiModelProperty(value = "效期批次数量（主单位商品数量）")
    private BigDecimal rawGoodsNum;

    @ApiModelProperty(value = "是否启动效期管理标识(1:否 2:是)")
    private Integer validityPeriodManageFlag;

    @ApiModelProperty(value = "生产日期")
    private LocalDate productionDate;

    @ApiModelProperty(value = "保质期,正整形")
    private Integer qualityGuaranteePeriod;

    @ApiModelProperty(value = "保质期单位,day:日 month:月 year:年,枚举:PlanCycleTypeEnum")
    private String shelfLifeUnit;

    /**
     * 自定义商品编号
     */
    @ApiModelProperty(value = "自定义商品编号")
    private String customGoodsNo;

    @ApiModelProperty(value = "操作类型(1001:加库 1002：减库)")
    private String operType;

    @ApiModelProperty(value = "来源单据类型(1001:订单下单 1002:销售退货单 1003:调拨出库 1004:调拨入库 1005:采购入库 1006:采购退货出库 1007:盘点入库 1008:盘点出库 1009:发货出库 1010:手动调整出库 1011:无仓转有仓出库 1012:有仓转无仓出库 1013有仓转无仓并继承原库存入库)")
    private String billType;

    @ApiModelProperty(value = "单据编号")
    private String billCode;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "门店编号")
    private String storeNo;

    @ApiModelProperty(value = "店铺名称，对应orgName")
    private String storeName;

    @ApiModelProperty(value = "商品编码集合")
    private List<String> goodsNos;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
