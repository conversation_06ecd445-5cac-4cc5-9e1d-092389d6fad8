package cn.htdt.goodsprocess.dto.request.storecategory;

import cn.htdt.common.dto.request.ReqBaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 店铺类目选择方式 入参实体类
 *
 * <AUTHOR>
 * @date 2020/10/20
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ReqStoreCategoryChooseDTO extends ReqBaseDTO {

    /**
     * 选择方式(1001:自建店铺类目树 1002:引用销售类目树 )
     */
    private String chooseType;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称，对应orgName
     */
    private String storeName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
