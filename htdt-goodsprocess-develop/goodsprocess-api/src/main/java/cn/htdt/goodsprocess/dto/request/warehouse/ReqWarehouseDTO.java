package cn.htdt.goodsprocess.dto.request.warehouse;

import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020-09-02
 * @Description 仓库管理 请求DTO
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ReqWarehouseDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 店铺编号
     */
    @ApiModelProperty(value = "多店铺编号")
    private List<String> storeNoList;



    @ApiModelProperty(value = "请求用户信息json", name = "adminParams")
    private String adminParams;
    /**
     * 仓库编号
     */
    @ApiModelProperty(value = "仓库编号", name = "warehouseNo")
    private String warehouseNo;

    /**
     * 仓库编码，手动输入的
     */
    @ApiModelProperty(value = "仓库编码", name = "warehouseCode")
    private String warehouseCode;

    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "仓库名称", name = "warehouseName")
    private String warehouseName;

    /**
     * 仓库类型1001:平台仓 1002：商家仓 1003：店铺仓
     */
    @ApiModelProperty(value = "仓库类型1001:平台仓 1002：商家仓 1003：店铺仓", name = "warehouseType")
    private String warehouseType;

    /**
     * 是否自建仓库 1:否 2:是
     */
    @ApiModelProperty(value = "是否自建仓库 1:否 2:是", name = "selfWarehouseFlag")
    private Integer selfWarehouseFlag;

    /**
     * 是否虚拟仓库 1-否 2-是
     */
    @ApiModelProperty(value = "是否虚拟仓库 1-否 2-是", name = "virtualWarehouseFlag")
    private Integer virtualWarehouseFlag;

    /**
     * 国家编码
     */
    @ApiModelProperty(value = "国家编码", name = "countryCode")
    private String countryCode;

    /**
     * 国家编码名称
     */
    @ApiModelProperty(value = "国家编码名称", name = "countryName")
    private String countryName;

    /**
     * 省份编码
     */
    @ApiModelProperty(value = "省份编码", name = "provinceCode")
    private String provinceCode;

    /**
     * 省份编码名称
     */
    @ApiModelProperty(value = "省份编码名称", name = "provinceName")
    private String provinceName;

    /**
     * 城市编码
     */
    @ApiModelProperty(value = "城市编码", name = "cityCode")
    private String cityCode;

    /**
     * 城市编码名称
     */
    @ApiModelProperty(value = "城市编码名称", name = "cityName")
    private String cityName;

    /**
     * 区县编码
     */
    @ApiModelProperty(value = "区县编码", name = "districtCode")
    private String districtCode;

    /**
     * 区县编码名称
     */
    @ApiModelProperty(value = "区县编码名称", name = "districtName")
    private String districtName;

    /**
     * 镇编码
     */
    @ApiModelProperty(value = "镇编码", name = "townCode")
    private String townCode;

    /**
     * 镇编码名称
     */
    @ApiModelProperty(value = "镇编码名称", name = "townName")
    private String townName;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址", name = "address")
    private String address;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址加密", name = "address")
    private String dsAddress;

    /**
     * 仓库负责人
     */
    @ApiModelProperty(value = "仓库负责人", name = "warehouseContacter")
    private String warehouseContacter;

    /**
     * 仓库负责人电话
     */
    @ApiModelProperty(value = "仓库负责人电话", name = "warehouseContacterMobile")
    private String warehouseContacterMobile;

    /**
     * 仓库负责人电话
     */
    @ApiModelProperty(value = "仓库负责人电话加密", name = "warehouseContacterMobile")
    private String dsWarehouseContacterMobile;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", name = "warehouseRemark")
    private String warehouseRemark;

    /**
     * 是否可用:默认2，1：不可用 2：可用
     */
    @ApiModelProperty(value = "是否可用:默认2，1：不可用 2：可用", name = "disableFlag")
    private Integer disableFlag;

    /**
     * 创建人no
     */
    @ApiModelProperty(value = "创建人no", name = "createNo")
    private String createNo;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称", name = "createName")
    private String createName;

    /**
     * 修改人no
     */
    @ApiModelProperty(value = "修改人no", name = "modifyNo")
    private String modifyNo;

    /**
     * 修改人名称
     */
    @ApiModelProperty(value = "修改人名称", name = "modifyName")
    private String modifyName;

    /**
     * 商家编号
     */
    @ApiModelProperty(value = "商家编号", name = "merchantNo")
    private String merchantNo;

    /**
     * 商家名称
     */
    @ApiModelProperty(value = "商家名称", name = "merchantName")
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    @ApiModelProperty(value = "门店编号", name = "storeNo")
    private String storeNo;

    /**
     * 店铺名称，对应orgName
     */
    @ApiModelProperty(value = "店铺名称", name = "storeName")
    private String storeName;

    /**
     * 平台编号
     */
    @ApiModelProperty(value = "平台编号", name = "companyNo")
    private String companyNo;

    /**
     * 平台名称
     */
    @ApiModelProperty(value = "平台名称", name = "companyName")
    private String companyName;

    /**
     * 分部编号
     */
    @ApiModelProperty(value = "分部编号", name = "branchNo")
    private String branchNo;

    /**
     * 分部名称
     */
    @ApiModelProperty(value = "分部名称", name = "branchName")
    private String branchName;

    /**
     * 创建开始时间
     */
    @ApiModelProperty(value = "创建开始时间", name = "createTimeStart")
    private LocalDateTime createTimeStart;

    /**
     * 创建结束时间
     */
    @ApiModelProperty(value = "创建结束时间", name = "createTimeEnd")
    private LocalDateTime createTimeEnd;

    /**
     * 仓库编码和仓库名称模糊查询
     */
    @ApiModelProperty(value = "仓库编码和仓库名称模糊查询", name = "warehouseStr")
    private String warehouseStr;

    /**
     * 用户身份(1运营，2商家，4-店铺；8-单店)
     */
    @ApiModelProperty(value = "用户身份(1运营，2商家，4-店铺；8-单店)", name = "loginIdentity")
    private Integer loginIdentity;

    /**
     * 已添加的仓库编码
     */
    @ApiModelProperty(value = "已添加的仓库编码", name = "noList")
    private List<String> noList = new ArrayList<>();

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
