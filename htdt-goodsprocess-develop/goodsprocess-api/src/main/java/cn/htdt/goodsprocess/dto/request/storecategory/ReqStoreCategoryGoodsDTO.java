package cn.htdt.goodsprocess.dto.request.storecategory;

import cn.htdt.common.dto.request.ReqBaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 店铺类目商品入参实体类
 *
 * <AUTHOR>
 * @date 2020/10/20
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ReqStoreCategoryGoodsDTO extends ReqBaseDTO {
    private static final long serialVersionUID = -8653037776841582967L;

    /**
     * 店铺类目id
     */
    private String storeCategoryNo;

    /**
     * 商品编号集合，包含普通商品、套餐商品等所有商品
     */
    private List<String> goodsNoList;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称，对应orgName
     */
    private String storeName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
