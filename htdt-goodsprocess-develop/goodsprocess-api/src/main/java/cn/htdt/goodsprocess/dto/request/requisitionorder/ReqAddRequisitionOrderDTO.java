package cn.htdt.goodsprocess.dto.request.requisitionorder;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.htdt.common.dto.request.AdminParamsDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description 新增要货单请求dto
 * 
 * <AUTHOR>
 * @date 2023/5/23 15:44
 **/
@Data
public class ReqAddRequisitionOrderDTO implements Serializable {

    private static final long serialVersionUID = -1752673419978867359L;

    @ApiModelProperty(value = "期望送达时间", position = 0)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate deliveryDate;

    @ApiModelProperty(value = "单据备注", position = 1)
    private String remark;

    @ApiModelProperty(value = "操作类型 10-保存 11-提交", position = 2)
    // @NotNull(message = "操作类型不能为空")
    // @EnumValue(intValues = {10, 11}, message = "操作类型只能为10或11")
    private Integer operateType;

    @ApiModelProperty(value = "创建要货单规则配置", required = true, position = 3)
    private String ruleValue;

    @ApiModelProperty(value = "要货商品行列表", position = 4)
    // @NotEmpty(message = "要货商品数据不能为空")
    // @Valid
    private List<ReqRequisitionOrderGoodsDTO> orderGoodsList;

    @ApiModelProperty(value = "登录用户信息", hidden = true)
    private AdminParamsDTO basicInfoParams;

    /**
     * 来源：1001：报损：1002或无值=其他
     */
    private String source;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
