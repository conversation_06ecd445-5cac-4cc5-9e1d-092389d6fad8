package cn.htdt.goodsprocess.dto.request.storecategory;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ReqSortNumDTO implements Serializable {
    private static final long serialVersionUID = -8698678906581292441L;
    @ApiModelProperty("店铺编号")
    private String storeNo;

    @ApiModelProperty("店铺类目编号")
    private String storeCategoryNo;

    @ApiModelProperty("类目编号")
    private String categoryNo;

    @ApiModelProperty(value = "商品类型 1001-实物商品;1002-虚拟商品;1003-服务商品（仅供平台使用）;1004-称重商品")
    private String goodsType;

    @ApiModelProperty("商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "初始位置")
    private Integer startSortNum;

    @ApiModelProperty(value = "移动后位置", notes = "移动到哪个前面或者后面,就传哪个sortNum值,例如:从sortNum为2移动到sortNum为8后面,就传8")
    private Integer endSortNum;

    @ApiModelProperty(value = "类目类型。1001:店铺类目,1002:销售类目。默认1001", required = true)
    private String categoryType;
}
