package cn.htdt.goodsprocess.dto.request.tradeorder;

import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 采购交易单
 *
 * <AUTHOR>
 */
@Data
public class ReqPurchaseTradeOrderDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = -3119539895722859575L;

    /**
     * 买家账号
     */
    private String buyerCode;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单明细编号
     */
    private String orderItemNo;

    /**
     * 商品名称-模糊查询
     */
    private String goodsName;

    /**
     * 采购商品名称-模糊查询
     */
    private String purchaseGoodsName;

    /**
     * 关联查询 htdt商品名称或者采购商品名称
     */
    private String relationGoodsName;

    /**
     * 是否入库(1:否 2:是)
     */
    private Integer stockFlag;


    /**
     * 商品SKU编码
     */
    private String skuCode;

    /**
     * 卖家名称
     */
    private String sellerName;

    /**
     * 1007=千橙采购；3001=羊乃世家，见 DataPurchaseSourceEnum
     */
    private String dataSource;

    /**
     * 采购单状态1001待出库 1002部分出库 1003完成 1004:撤销
     */
    private String orderStatus;

    /**
     * 1今日 2:昨日 3:近7日 4:本月
     **/
    private Integer day;

    /**
     * 开始时间
     **/
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startCreateTime;

    /**
     * 结束时间
     **/
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endCreateTime;

    /**
     * 查询开始创建订单时间
     */
    private LocalDate startCreateOrderTime;

    /**
     * 查询结束创建订单时间
     */
    private LocalDate endCreateOrderTime;

    @ApiModelProperty(value = "买家账号")
    private List<String> buyerCodes;

}
