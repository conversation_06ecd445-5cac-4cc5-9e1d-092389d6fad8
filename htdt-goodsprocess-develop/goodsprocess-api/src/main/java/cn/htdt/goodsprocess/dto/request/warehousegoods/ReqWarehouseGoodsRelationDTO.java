package cn.htdt.goodsprocess.dto.request.warehousegoods;

import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.List;

/**
 * 仓库商品关联
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReqWarehouseGoodsRelationDTO extends ReqComPageDTO {

    /**
     * 表的唯一主键
     */
    @ApiModelProperty("表的唯一主键")
    private String relationNo;

    /**
     * 仓库编号
     */
    @ApiModelProperty("仓库编号")
    private String warehouseNo;

    /**
     * 仓库名称
     */
    @ApiModelProperty("仓库名称")
    private String warehouseName;

    /**
     * 商品编号，包含普通商品、套餐商品等所有商品
     */
    @ApiModelProperty("商品编号")
    private String goodsNo;

    /**
     * 库存数量
     */
    @ApiModelProperty("库存数量")
    private BigDecimal stockNum;

    /**
     * 实体库存区间开始
     */
    @ApiModelProperty("实体库存区间开始")
    private Integer wareHouseStockBegin;

    /**
     * 实体库存区间结束
     */
    @ApiModelProperty("实体库存区间结束")
    private Integer wareHouseStockEnd;

    /**
     * 商家编号
     */
    @ApiModelProperty("商家编号")
    private String merchantNo;

    /**
     * 商家名称
     */
    @ApiModelProperty("商家名称")
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    @ApiModelProperty("门店编号")
    private String storeNo;

    /**
     * 店铺名称，对应orgName
     */
    @ApiModelProperty("店铺名称")
    private String storeName;

    /**
     * 计量单位id
     */
    @ApiModelProperty("计量单位id")
    private String calculationUnitNo;

    /**
     * 计量单位名称
     */
    @ApiModelProperty("计量单位名称")
    private String calculationUnitName;

    /**
     * 计量单位符号
     */
    @ApiModelProperty("计量单位符号")
    private String calculationUnitSymbol;

    /**
     * 是否可用:默认1，1：可用，其余不可用
     */
    @ApiModelProperty("是否可用:默认1，1：可用，2不可")
    private Integer disableFlag;

    /**
     * 归属平台编号
     */
    @ApiModelProperty("归属平台编号")
    private String companyNo;

    /**
     * 归属平台名称
     */
    @ApiModelProperty("归属平台名称")
    private String companyName;

    /**
     * 归属分部编号
     */
    @ApiModelProperty("归属分部编号")
    private String branchNo;

    /**
     * 归属分部名称
     */
    @ApiModelProperty("归属分部名称")
    private String branchName;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("数据逻辑删除标识，默认1未删除，其余已删除")
    private Integer deleteFlag;

    @ApiModelProperty("商品ID、商品名称")
    private String goodsStr;

    @ApiModelProperty("商品助记码")
    private String goodsHelpCode;

    @ApiModelProperty("商品类目名称")
    private String categoryName;

    @ApiModelProperty("商品类目ID")
    private String categoryNo;

    @ApiModelProperty(value = "类目全路径")
    private String fullIdPath;

    @ApiModelProperty("商品品牌名称")
    private String brandName;

    @ApiModelProperty("商品品牌ID")
    private String brandNo;

    @ApiModelProperty(value = "用户身份(1运营，2商家，4-店铺；8-单店)", name = "loginIdentity")
    private Integer loginIdentity;

    @ApiModelProperty(value = "请求用户信息json", name = "adminParams")
    private String adminParams;

    @ApiModelProperty(value = "商品删除标识", name = "goodsDeleteFlag")
    private Integer goodsDeleteFlag;

    @ApiModelProperty(value = "是否入仓(1:否 2:是)", name = "warehouseFlag")
    private Integer warehouseFlag;

    @ApiModelProperty(value = "是否虚拟仓(1:否 2：是)", name = "virtualWarehouseFlag")
    private Integer virtualWarehouseFlag;

    @ApiModelProperty(value = "仓库类型1001:平台仓 1002：商家仓 1003：店铺仓", name = "warehouseType")
    private String warehouseType;

    @ApiModelProperty(value = "是否启用串码(1:否 2:是)", name = "imeiFlag")
    private Integer imeiFlag;

    @ApiModelProperty(value = "串码list", name = "imeis")
    private List<String> imeis;

    @ApiModelProperty(value = "商品原始编号（默认是与goods_no一致，分发到店铺下才不一致）", name = "originalGoodsNo")
    private String originalGoodsNo;

    /***** original对应的信息 ******/
    /**
     * 归属平台编号
     */
    @ApiModelProperty("original归属平台编号")
    private String originalCompanyNo;

    /**
     * 归属平台名称
     */
    @ApiModelProperty("original归属平台名称")
    private String originalCompanyName;

    /**
     * 归属分部编号
     */
    @ApiModelProperty("original归属分部编号")
    private String originalBranchNo;

    /**
     * 归属分部名称
     */
    @ApiModelProperty("original归属分部名称")
    private String originalBranchName;

    /**
     * 商家编号
     */
    @ApiModelProperty("original商家编号")
    private String originalMerchantNo;

    /**
     * 商家名称
     */
    @ApiModelProperty("original商家名称")
    private String originalMerchantName;

    /**
     * 门店编号，对应orgId
     */
    @ApiModelProperty("original门店编号，对应orgId")
    private String originalStoreNo;

    /**
     * 店铺名称，对应orgName
     */
    @ApiModelProperty("original店铺名称，对应orgName")
    private String originalStoreName;

    @ApiModelProperty(value = "商品编号List", name = "goodsNos")
    private List<String> goodsNos;

    @ApiModelProperty(value = "条形码", name = "barcode")
    private String barcode;

    @ApiModelProperty(value = "汇享购排序值", name = "hxgSortValue")
    private String hxgSortValue;

    @ApiModelProperty(value = "汇享购排序升降值 desc:降值排序,asc:升值排序", name = "hxgSortType")
    private String hxgSortType;

    /**
     * 是否查询多单位商品:1只查主品（默认）,2查询主品和子品,3主品和子品都不查
     */
    private Integer isAuxiliaryUnit;

    // 2023-08-15 lixiang 蛋品-商品管理-多单位商品
    /**
     * 多单位商品类型 1001:主单位商品 1002:子
     */
    private String multiUnitType;

    /**
     * 多单位主品编号
     */
    private String multiUnitGoodsNo;

    /**
     * 主计量单位对应关系数值
     */
    private BigDecimal mainUnitNum;

    /**
     * 辅计量单位对应关系数值 多单位时为辅单位1
     */
    private BigDecimal assistUnitNum;

    /**
     * 盘点类型：0 商品；1 商品组
     */
    private Integer inventoryType;

    /**
     * 查询商品时排除的商品编码
     */
    private List<String> notExistGoods;

    /**
     * 只展示不存在商品组的商品
     *
     * */
    @ApiModelProperty(value = "是否只展示商品组中的商品:空/1展示全部，2不展示已存在商品组中的商品")
    private String notExistsGroupsFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
