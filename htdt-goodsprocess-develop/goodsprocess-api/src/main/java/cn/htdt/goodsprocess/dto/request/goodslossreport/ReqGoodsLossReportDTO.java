package cn.htdt.goodsprocess.dto.request.goodslossreport;

import cn.htdt.common.dto.request.AdminParamsDTO;
import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 商品报损单表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ReqGoodsLossReportDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 店铺编号
     */
    private List<String> storeNoList;
    /**
     * 报损单号
     */
    private String lossReportNo;

    /**
     * 报损备注
     */
    private String reportRemark;

    /**
     * 报损单状态 11-待审批 12-审核不通过 13-已完成
     */
    private Integer reportStatus;

    /**
     * 补偿方式 1-货物补偿 2-现金补偿 3-不补偿
     */
    private Integer compensateMethod;

    /**
     * 补偿金额
     */
    private BigDecimal compensateAmount;

    /**
     * 审核备注
     */
    private String reportApproveRemark;

    /**
     * 审批是否通过: 1=否；2=是
     */
    private Integer approveFlag;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称，对应orgName
     */
    private String storeName;

    /**
     * 是否可用:默认2，1：不可用2:可用
     */
    private Integer disableFlag;

    private LocalDateTime modifyTime;
    private String createNo;
    private String createName;
    private LocalDateTime createTime;
    private String modifyNo;
    private String modifyName;
    private Integer deleteFlag;
    /*******************以下扩展字段****************************/
    /**
     * 商品名称（模糊）-pc用
     */
    private String goodsName;

    /**
     * 商品名称（模糊）或报损单号（精准）-app用
     */
    private String goodsNameAndLossNo;

    /**
     * 报损明细列表
     */
    private List<ReqGoodsLossReportItemDTO> goodsLossReportItemDTOS;

    /**
     * 报损图片列表
     */
    private List<ReqGoodsLossReportPicDTO> goodsLossReportPicDTOS;

    /**
     * 用户身份
     */
    private Integer loginIdentity;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
