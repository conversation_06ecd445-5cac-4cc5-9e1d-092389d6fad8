package cn.htdt.goodsprocess.dto.request.barcodescaleflag;

import lombok.Data;

import java.io.Serializable;

/**
 * 条码秤标识 请求DTO
 *
 * <AUTHOR>
 * @since 2022-02-24
 */
@Data
public class ReqBarCodeScaleFlagDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 条码标识
     */
    private String barCodeFlag;

    /**
     * 店铺编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;
}
