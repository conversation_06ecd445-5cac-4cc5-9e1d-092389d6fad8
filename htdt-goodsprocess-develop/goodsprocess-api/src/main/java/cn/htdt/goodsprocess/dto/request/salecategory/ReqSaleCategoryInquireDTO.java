package cn.htdt.goodsprocess.dto.request.salecategory;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 销售类目 数据查询入参实体类
 *
 * <AUTHOR>
 * @date 2020-09-11
 */
@Data
public class ReqSaleCategoryInquireDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 类目id
     */
    private String categoryNo;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 类目名称全路径
     */
    private String fullNamePath;

    /**
     * 层级(1001:一级 1002：二级 1003：三级 1004：四级)
     */
    private String categoryLevel;

    /**
     * 父类目节点ID
     */
    private String parentNo;

    /**
     * 二级节点id
     */
    private String secondCategoryNo;

    /**
     * 用户身份(1运营，2商家，4-店铺；8-单店)
     */
    private Integer loginIdentity;

    /**
     * 数据来源类型(默认1002，1001：MDM同步，1002：平台自建)
     */
    private String categorySource;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 商品状态1001-未上架;1002:已上架;
     */
    private String goodsStatus;

    /**
     * queryType=1006 查询自建和商家分发商品
     */
    private String queryType;

    /**
     * 是否可用:默认2，1：不可用，2：可用
     */
    private Integer disableFlag;

    /**
     * 是否过滤云池商品反推 2:过滤云池反推类目 3:店铺商品+云池反推类目
     */
    private Integer isOutCloudPool;

    /**
     * 是否查询满减满折活动信息标识：2-是，其他-否
     */
    private Integer queryFullDiscountFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
