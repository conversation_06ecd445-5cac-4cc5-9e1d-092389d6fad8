package cn.htdt.goodsprocess.dto.request.settlementorder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.htdt.common.dto.request.AdminParamsDTO;
import cn.htdt.common.dto.request.ReqBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 结算单编辑, 添加请求DRO
 *
 * <AUTHOR>
 * @since 2023-05-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ReqSettlementOrderEditDTO extends ReqBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("结算单号")
    private String settlementNo;

    @ApiModelProperty("结算单号集合")
    private List<String> settlementNos;

    @ApiModelProperty("关联单据号")
    private String associatedDocumentNo;

    /**
     * 关联单据类型, 参考枚举: AssociatedDocumentTypeEnum
     */
    @ApiModelProperty("关联单据类型")
    private String associatedDocumentType;

    @ApiModelProperty("状态 1-未结算 2-已结算")
    private Integer status;

    @ApiModelProperty("结算金额")
    private BigDecimal settlementAmount;

    @ApiModelProperty("结算时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime settlementTime;

    @ApiModelProperty("结算人编号")
    private String settlementUserNo;

    @ApiModelProperty("结算人姓名")
    private String settlementUserName;

    @ApiModelProperty("归属平台编号")
    private String companyNo;

    @ApiModelProperty("归属平台名称")
    private String companyName;

    @ApiModelProperty("归属分部编号")
    private String branchNo;

    @ApiModelProperty("归属分部名称")
    private String branchName;

    @ApiModelProperty("归属平台类型, 1001:运营 1002:商家 1003:店铺")
    private String platformType;

    /**
     * 请求用户信息json
     */
    @ApiModelProperty(value = "请求用户信息json", hidden = true)
    private AdminParamsDTO adminParams;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
