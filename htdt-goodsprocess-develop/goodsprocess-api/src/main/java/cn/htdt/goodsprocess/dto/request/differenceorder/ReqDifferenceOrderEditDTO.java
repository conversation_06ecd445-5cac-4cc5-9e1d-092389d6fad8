package cn.htdt.goodsprocess.dto.request.differenceorder;

import cn.htdt.common.dto.request.ReqBaseDTO;
import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 差异单请求DRO
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ReqDifferenceOrderEditDTO extends ReqBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("差异单号")
    private String differenceNo;

    @ApiModelProperty("要货单号")
    private String requisitionNo;

    @ApiModelProperty("状态 1001-待收货 1002-已完成")
    private String status;

    @ApiModelProperty("差异品项数")
    private BigDecimal differenceCount;

    @ApiModelProperty(value = "差异单商品行集合")
    private List<ReqDifferenceOrderGoodsEditDTO> differenceOrderGoodsEditDTOList;

    @ApiModelProperty(value = "请求用户信息json", name = "adminParams")
    private String adminParams;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
