package cn.htdt.goodsprocess.dto.request.goodsaudit;

import java.io.Serializable;
import java.util.Date;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品审核记录响应DTO
 *
 * <AUTHOR>
 * @date 2020年11月16日
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReqGoodsAuditRecordDTO extends ReqComPageDTO implements Serializable{

	/**
	 *
	 */
	private static final long serialVersionUID = -7328379266043214144L;

	/**
	 * 商品编号，包含普通商品、套餐商品等所有商品
	 */
	private String goodsNo;

	/**
	 * 商品审核状态 2001:等待审核;2002:审核中;2003:审核成功; 2004:审核失败
	 */
	private String auditStatus;

	/**
	 * 审核原因
	 */
	private String auditMessage;

	/**
	 * 审核人ID
	 */
	private String auditUserNo;

	/**
	 * 审核人名称
	 */
	private String auditUserName;

	/**
	 * 商家编号
	 */
	private String merchantNo;

	/**
	 * 商家名称
	 */
	private String merchantName;

	/**
	 * 门店编号，对应orgId
	 */
	private String storeNo;

	/**
	 * 商品来源类型(1-平台自建;2-商家自建;3-店铺自建;)
	 */
	private Integer sourceType;

	/**
	 * 店铺名称，对应orgName
	 */
	private String storeName;

	/**
	 * 是否可用:默认1，1：可用，其余不可用
	 */
	private Integer disableFlag;

	/**
	 * 审核类型(1001:分发审核 1002:上架审核 1003:云池审核)
	 */
	private String auditType;
}
