package cn.htdt.goodsprocess.dto.request.purchaseorder;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * <AUTHOR>
 * @date 2020年9月14日
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ReqPurchaseOrderGoodsDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = -2836247001133317635L;

    /**
     * 采购单商品行编码
     */
    private String purchaseGoodsCode;
    /**
     * 采购单编码
     */
    private String purchaseCode;
    /**
     * 数据来源 1001:手动添加 1002：采购商城添加
     */
    private String sourceType;
    /**
     * 约定到货日期
     */
    private LocalDateTime expectReceiveDate;
    /**
     * 实际到货日期
     */
    private LocalDateTime actualReceiveDate;
    /**
     * 商品编码
     */
    private String goodsNo;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 采购单位
     */
    private String purchaseUnit;

    /**
     * 计量单位ID
     */
    private String calculationUnitNo;

    /**
     * 辅计量单位id
     */
    private String assistCalculationUnitNo;

    /**
     * 主计量单位对应关系数值
     */
    private BigDecimal mainUnitNum;

    /**
     * 辅计量单位对应关系数值
     */
    private BigDecimal assistUnitNum;

    /**
     * 是否有辅计量单位:1-否;2-是
     * 采购单时，是否按辅计量单位采购
     */
    private Integer standardFlag;

    /**
     * 商品类目编码
     */
    private String categoryNo;
    /**
     * 商品类目名称
     */
    private String categoryName;
    /**
     * 采购单价
     */
    private BigDecimal purchaseUnitPrice;
    /**
     * 采购数量
     */
    private BigDecimal purchaseNum;
    /**
     * 已入库数量
     */
    private BigDecimal storageCount;
    /**
     * 交易金额
     */
    private BigDecimal purchasePrice;

    /**
     * 是否入仓 1-有实体仓;2-无实体仓
     */
    private Integer warehouseFlag;

    /**
     * 是否入仓 1-有实体仓;2-无实体仓
     */
    private Integer warehouseType;
    /**
     * 仓库编号
     */
    private String warehouseNo;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * 备注
     */
    private String remark;
    /**
     * 订单状态1待入库 2部分入库 3完成
     */
    private String orderStatus;
    /**
     * 商家ID
     */
    private String merchantNo;
    /**
     * 商家名称
     */
    private String merchantName;
    /**
     * 店铺ID，对应orgId
     */
    private String storeNo;
    /**
     * 店铺名称，对应orgName
     */
    private String storeName;
    /**
     * 是否可用:默认2，1：不可用2:可用
     */
    private Integer disableFlag;

    /**
     * 归属平台编号
     */
    private String companyNo;
    /**
     * 归属平台名称
     */
    private String companyName;
    /**
     * 归属分部编号
     */
    private String branchNo;
    /**
     * 归属分部名称
     */
    private String branchName;

    /**
     * 归属平台类型(1.运营 2.商家 3.店铺)
     */
    private String platformType;

    /**
     * 商品ID/条码-条件
     */
    private String barcode;
    /**
     * 供应商名称/编码-条件
     */
    private String supplierName;

    /**
     * 约定到货日期-开始-条件
     */
    private LocalDateTime startExpectReceiveDate;

    /**
     * 约定到货日期-结束-条件
     */
    private LocalDateTime endExpectReceiveDate;

    /**
     * 采购日期-开始-条件
     */
    private LocalDateTime startPurchaseDate;

    /**
     * 采购日期-结束-条件
     */
    private LocalDateTime endPurchaseDate;
    /**
     * 用户身份(1运营，2商家，4-店铺；8-单店)
     */
    private Integer loginIdentity;
    /**
     * 是否入仓(1:否 2:是)，与是否虚拟仓库 1-否 2-是相反----*****所传用于商品是否入仓变化前的状态*******
     */
    private Integer outWarehouseFlag;

    /**
     * 生产日期
     */
    private LocalDate productionDate;

    /**
     * 2023-08-21蛋品-genghao-采购管理-创建采购单新增多单位商品
     *
     * 多单位商品编号
     */
    private String multiUnitGoodsNo;

    /**
     * 2023-08-21蛋品-genghao-采购管理-创建采购单新增多单位商品
     *
     * 主计量单位数量
     */
    private BigDecimal calculationNum;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
