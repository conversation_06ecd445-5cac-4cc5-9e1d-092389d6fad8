package cn.htdt.goodsprocess.dto.request.settlementorder;

import cn.htdt.common.dto.request.ReqComPageDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 结算单请求DRO
 *
 * <AUTHOR>
 * @since 2023-05-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ReqSettlementOrderDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 店铺编号
     */
    @ApiModelProperty(value = "多店铺编号")
    private List<String> storeNoList;



    @ApiModelProperty("结算单号")
    private String settlementNo;

    @ApiModelProperty("结算单号集合")
    private List<String> settlementNos;

    @ApiModelProperty("关联单据号")
    private String associatedDocumentNo;

    /**
     * 关联单据类型, 参考枚举: AssociatedDocumentTypeEnum
     */
    @ApiModelProperty("关联单据类型")
    private String associatedDocumentType;

    @ApiModelProperty("结算状态 1-未结算 2-已结算")
    private Integer status;

    @ApiModelProperty("结算金额")
    private BigDecimal settlementAmount;

    @ApiModelProperty("结算人编号")
    private String settlementUserNo;

    @ApiModelProperty("结算人姓名")
    private String settlementUserName;

    @ApiModelProperty("商家编号")
    private String merchantNo;

    @ApiModelProperty("店铺ID, 对应orgId")
    private String storeNo;

    @ApiModelProperty("创建开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate startCreateTime;

    @ApiModelProperty("创建结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate endCreateTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
