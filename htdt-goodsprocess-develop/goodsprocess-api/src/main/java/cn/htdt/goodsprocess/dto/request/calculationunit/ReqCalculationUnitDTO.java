package cn.htdt.goodsprocess.dto.request.calculationunit;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021-08-12
 * @description 计量单位请求DTO
 **/
@Data
public class ReqCalculationUnitDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 计量单位id
     */
    private String calculationUnitNo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
