package cn.htdt.goodsprocess.dto.request.storecategory;

import cn.htdt.common.dto.request.ReqBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 店铺类目编辑入参实体类
 *
 * <AUTHOR>
 * @date 2020/10/20
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ReqStoreCategoryEditDTO extends ReqBaseDTO {

    /**
     * 店铺类目id
     */
    private String storeCategoryNo;

    /**
     * 店铺类目名称
     */
    private String storeCategoryName;
    /**
     * 子类目名称
     */
    private String childCategoryName;

    /**
     * 父类目节点ID
     */
    private String parentNo;

    /**
     * 图片URL
     */
    private String pictureUrl;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称，对应orgName
     */
    private String storeName;

    /**
     * 移动方向：1001 上移/1002 下移
     */
    private String moveDirection;

    /**
     * 排序值
     */
    private Integer sortValue;

    /**
     * 一体机排序值
     */
    private Integer scSortNum;

    /**
     * 商品编号集合，包含普通商品、套餐商品等所有商品
     */
    private List<String> goodsNoList;
    /**
     * 类目排序集合
     */
    private List<ReqStoreCategoryEditDTO> sortValueList;

    /**
     * 店铺类目编号集合
     */
    private List<String> storeCategoryNoList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
