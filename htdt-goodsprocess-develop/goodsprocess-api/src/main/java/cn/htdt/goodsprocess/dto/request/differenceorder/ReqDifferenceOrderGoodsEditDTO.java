package cn.htdt.goodsprocess.dto.request.differenceorder;

import cn.htdt.common.dto.request.ReqBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 差异单商品行请求DRO
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ReqDifferenceOrderGoodsEditDTO extends ReqBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 差异商品行编号
     */
    @ApiModelProperty(value = "差异商品行编号")
    private String differenceItemNo;

    @ApiModelProperty(value = "差异单号")
    private String differenceNo;

    /**
     * 源商品编号（商家商品编号）
     */
    @ApiModelProperty(value = "源商品编号（商家商品编号）")
    private String merchantGoodsNo;

    /**
     * 商家商品名称
     */
    @ApiModelProperty(value = "源商品名称（商家商品名称）")
    private String merchantGoodsName;

    @ApiModelProperty(value = "店铺商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "条形码")
    private String barcode;

    @ApiModelProperty(value = "助记码")
    private String goodsHelpCode;

    @ApiModelProperty(value = "商家商品单价")
    private BigDecimal itemPrice;

    @ApiModelProperty(value = "要货数量")
    private BigDecimal requisitionNum;

    @ApiModelProperty(value = "要货单位")
    private String requisitionUnit;

    @ApiModelProperty(value = "计量单位ID")
    private String calculationUnitNo;

    @ApiModelProperty(value = "辅计量单位ID")
    private String assistCalculationUnitNo;

    /**
     * 主计量单位对应关系数值
     */
    @ApiModelProperty(value = "主计量单位对应关系数值")
    private BigDecimal mainUnitNum;

    /**
     * 辅计量单位对应关系数值
     */
    @ApiModelProperty(value = "辅计量单位对应关系数值")
    private BigDecimal assistUnitNum;

    /**
     * 是否使用辅计量单位采购:1-否;2-是
     */
    @ApiModelProperty(value = "是否使用辅计量单位采购:1-否;2-是")
    private Integer standardFlag;

    @ApiModelProperty(value = "差异数量")
    private BigDecimal differenceCount;

    /**
     * 差异金额
     */
    @ApiModelProperty(value = "差异金额")
    private BigDecimal differenceAmount;

    @ApiModelProperty(value = "未入库数量")
    private BigDecimal noStorageCount;

    @ApiModelProperty(value = "已入库数量")
    private BigDecimal inStorageCount;

    @ApiModelProperty(value = "归属平台编号")
    private String companyNo;

    @ApiModelProperty(value = "归属平台名称")
    private String companyName;

    @ApiModelProperty(value = "归属分部编号")
    private String branchNo;

    @ApiModelProperty(value = "归属分部名称")
    private String branchName;

    @ApiModelProperty(value = "归属平台类型(1001.运营 1002.商家 1003.店铺)")
    private String platformType;

    @ApiModelProperty("商家编号")
    private String merchantNo;

    @ApiModelProperty("商家名称")
    private String merchantName;

    @ApiModelProperty("店铺ID, 对应orgId")
    private String storeNo;

    @ApiModelProperty("店铺名称, 对应orgName")
    private String storeName;
}
