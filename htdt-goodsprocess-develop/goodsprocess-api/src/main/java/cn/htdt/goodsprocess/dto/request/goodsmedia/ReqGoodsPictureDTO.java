package cn.htdt.goodsprocess.dto.request.goodsmedia;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 详细说明.图片请求类
 * <p>
 * Copyright: Copyright (c) 2020/9/27 10:40
 * <p>
 * Company: htd
 * <p>
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReqGoodsPictureDTO extends ReqGoodsMediaDTO implements Serializable {

    private static final long serialVersionUID = -4388508551687426352L;

    /**
     * 图片url
     */
    private String pictureUrl;

    /**
     * 1、不是主图 2、是主图
     */
    private Integer mainPictureFlag;

    /**
     * 图片展示区域 0:轮播 1:详情
     */
    private Integer pictureShowArea;

}
