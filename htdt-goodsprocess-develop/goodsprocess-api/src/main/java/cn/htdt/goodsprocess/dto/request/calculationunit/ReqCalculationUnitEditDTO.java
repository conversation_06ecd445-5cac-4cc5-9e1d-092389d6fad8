package cn.htdt.goodsprocess.dto.request.calculationunit;

import cn.htdt.common.dto.request.ReqBaseDTO;
import cn.htdt.common.dto.request.ReqComPageDTO;
import cn.htdt.common.enums.goods.DataSourceTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * 计量单位 编辑入参DTO
 *
 * <AUTHOR>
 * @date 2020-09-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReqCalculationUnitEditDTO extends ReqBaseDTO implements Serializable {

    private static final long serialVersionUID = -1722872381039934765L;

    /**
     * 计量单位id
     */
    private String calculationUnitNo;

    /**
     * 计量单位id集合
     */
    private List<String> calculationUnitNoList;

    /**
     * 计量单位名称
     */
    private String calculationUnitName;

    /**
     * 计量单位符号
     */
    private String calculationUnitSymbol;

    /**
     * 数据来源类型(1001:平台自建 1002:MDM同步)
     */
    private String dataSourceType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
