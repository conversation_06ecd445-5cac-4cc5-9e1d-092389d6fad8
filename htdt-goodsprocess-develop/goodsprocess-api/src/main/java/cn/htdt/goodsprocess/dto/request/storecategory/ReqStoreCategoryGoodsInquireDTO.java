package cn.htdt.goodsprocess.dto.request.storecategory;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 店铺类目 数据查询入参实体类
 *
 * <AUTHOR>
 * @date 2020/10/22
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ReqStoreCategoryGoodsInquireDTO extends ReqComPageDTO implements Serializable {
    /**
     * 店铺类目编号
     */
    private String storeCategoryNo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
