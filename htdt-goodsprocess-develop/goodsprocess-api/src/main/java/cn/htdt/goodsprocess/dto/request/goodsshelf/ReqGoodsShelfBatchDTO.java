package cn.htdt.goodsprocess.dto.request.goodsshelf;

import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-06-24
 * @description 效期-临期商品列表
 **/
@Data
public class ReqGoodsShelfBatchDTO extends ReqComPageDTO implements Serializable  {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "门店编号")
    private String storeNo;

    @ApiModelProperty(value = "批次号")
    private String batchNo;

    @ApiModelProperty(value = "商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "是否入仓(1:否 2:是)")
    private Integer warehouseFlag;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseNo;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "到期日期")
    private LocalDate expirationDate;

    @ApiModelProperty(value = "店铺类目编号集合，app需要")
    private List<String> storeCategoryNoList;

    @ApiModelProperty(value = "效期类型，1001临期，1002过期，app需要")
    private String validityPeriodType;

    @ApiModelProperty(value = "到期开始时间")
    private LocalDate expirationDateStart;

    @ApiModelProperty(value = "到期结束时间")
    private LocalDate expirationDateEnd;

    @ApiModelProperty(value = "临期预警设置剩余百分比，换算成小数，此值无需外部传递内部赋值")
    private BigDecimal warningSettings;

    @ApiModelProperty(value = "商品编码list")
    private List<String> goodsNos;

    @ApiModelProperty(value = "单据编号")
    private String billCode;

    @ApiModelProperty(value = "来源单据类型(1001:订单下单 1002:销售退货单 1003:调拨出库 1004:调拨入库 1005:采购入库 1006:采购退货出库 1007:盘点入库 1008:盘点出库 1009:发货出库 1010:手动调整出库 1011:无仓转有仓出库 1012:有仓转无仓出库 1013有仓转无仓并继承原库存入库)")
    private String billType;

    private Integer noCategoryFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}