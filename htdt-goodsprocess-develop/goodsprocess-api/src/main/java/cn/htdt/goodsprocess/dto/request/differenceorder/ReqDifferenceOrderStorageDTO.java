package cn.htdt.goodsprocess.dto.request.differenceorder;

import cn.htdt.common.dto.request.AdminParamsDTO;
import cn.htdt.goodsprocess.dto.request.requisitionorder.ReqRequisitionOrderStorageRecordDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * 差异单入库请求dto
 * 
 * <AUTHOR>
 * @date 2023-06-06 11:02
 **/
@Data
public class ReqDifferenceOrderStorageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "差异单号")
    private String differenceNo;

    @ApiModelProperty(value = "出入库类型 1-出库 2-入库")
    private Integer storageType;

    @ApiModelProperty(value = "操作类型 1001-分批入库 1002-一键全部入库 1003-强制完成入库")
    private String operateType;

    @ApiModelProperty("要货差异单入库商品行列表")
    private List<ReqDifferenceOrderStorageRecordDTO> outOrInStorageList;

    @ApiModelProperty(value = "登录用户信息", hidden = true)
    private AdminParamsDTO basicInfoParams;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
