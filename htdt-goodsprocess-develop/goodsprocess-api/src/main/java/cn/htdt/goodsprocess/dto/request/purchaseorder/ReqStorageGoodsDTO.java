package cn.htdt.goodsprocess.dto.request.purchaseorder;

import cn.htdt.common.dto.request.ReqBaseDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * 采购单入库Dto
 *
 * <AUTHOR>
 * @date 2020年10月19日
 */
@Data
public class ReqStorageGoodsDTO extends ReqBaseDTO implements Serializable {

    private static final long serialVersionUID = 698748426374845995L;
    /**
     *采购单编码
     */
    private String purchaseCode;

    /**
     * 仓库编号
     */
    private String warehouseNo;
    /**
     * 仓库类型
     */
    private String warehouseType;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * 用户身份(1运营，2商家，4-店铺；8-单店)
     */
    private Integer loginIdentity;

    /**
     *商家ID
     */
    private String merchantNo;
    /**
     *店铺ID，对应orgId
     */
    private String storeNo;
    /**
     * 平台类型
     * */
    private String companyNo;
    /**
     * 归属分部编号
     */
    private String branchNo;
    /**
     * 归属分部名称
     */
    private String branchName;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 店铺名称，对应orgName
     */
    private String storeName;
    /**
     * 归属平台名称
     */
    private String companyName;

    private String goodsNo;

    private List<StorageGoods> storageGoodsList;

    @Data
    public static class StorageGoods implements Serializable{

        private static final long serialVersionUID = -5259828732619065531L;

        private String goodsNo;

        /**
         * 生产日期
         */
        private LocalDate productionDate;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
