package cn.htdt.goodsprocess.dto.request.purchaseorder;
/**
 * <AUTHOR>
 * @Date 2020-10-20
 * @Description 请求DTO
 **/

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2020-10-20
 * @Description 采购商品修改状态 请求DTO
 **/
@Data
public class ReqStorageGoodsStatusDTO implements Serializable {


    private static final long serialVersionUID = 2620627044780688764L;
    /**
     * 采购单号
     */
    private String purchaseCode;
    /**
     * 采购单商品行编码 对应流水表sub_bill_code
     */
    private String purchaseGoodsCode;
    /**
     * 出入库数量
     */
    private BigDecimal stockNum;
    /**
     * 出入库数量（按主计量单位计算，实际出入库数量）
     */
    private BigDecimal mainStockNum;
    /**
     * 修改人编号
     */
    private String modifyNo;
    /**
     * 修改人名称
     */
    private String modifyName;

    /**
     * 20230928-蛋品-genghao-采购管理-入库
     * 入库数量（未经计算）
     */
    private BigDecimal outStockNum;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
