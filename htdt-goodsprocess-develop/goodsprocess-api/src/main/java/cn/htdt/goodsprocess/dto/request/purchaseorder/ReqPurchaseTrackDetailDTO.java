package cn.htdt.goodsprocess.dto.request.purchaseorder;

import cn.htdt.common.dto.request.ReqComPageDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 采购单明细跟踪入参
 *
 * <AUTHOR>
 * @date 2020年11月9日
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ReqPurchaseTrackDetailDTO extends ReqComPageDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 供应商名称/编码 关键字
     * */
    private String supplierKeyWord;
    /**
     * 商品名称/编码 关键字
     * */
    private String goodsKeyWord;
    /**
     *采购单编码
     */
    private String purchaseCode;
    /**
     *数据来源 1001:手动添加 1002：采购商城添加
     */
    private String sourceType;
    /**
     * 采购日期-开始-条件
     */
    private String startPurchaseDate;

    /**
     * 采购日期-结束-条件
     */
    private String endPurchaseDate;
    /**
     *商家ID
     */
    private String merchantNo;
    /**
     *商家名称
     */
    private String merchantName;
    /**
     *店铺ID，对应orgId
     */
    private String storeNo;
    /**
     *店铺名称，对应orgName
     */
    private String storeName;
    /**
     *归属平台编号
     */
    private String companyNo;
    /**
     *归属平台名称
     */
    private String companyName;
    /**
     *归属分部编号
     */
    private String branchNo;
    /**
     *归属分部名称
     */
    private String branchName;

    /**
     * 归属平台类型(1001.运营 1002.商家 1003.店铺)
     */
    private String platformType;
    /**
     * 用户身份(1运营，2商家，4-店铺；8-单店)
     */
    private Integer loginIdentity;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
