package cn.htdt.goodsprocess.dto.request.tradeorder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 订单商品行信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ReqTradeOrderItemsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单行号
     */
    private String orderItemNo;

    /**
     * 渠道编码: 10 平台公司商品 20 外部供应商商品 30 商品+  3010 京东+商品 3020 国美商品, 更多看 字典表 PRODUCT_CHANNEL
     */
    private String channelCode;

    /**
     * 商品编码 
     */
    private String itemCode;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品SKU编码
     */
    private String skuCode;

    /**
     * 商品图片地址
     */
    private String skuPictureUrl;

    /**
     * 一级类目ID
     */
    private Long firstCategoryId;

    /**
     * 一级类目名称
     */
    private String firstCategoryName;

    /**
     * 二级类目ID
     */
    private Long secondCategoryId;

    /**
     * 二级类目名称
     */
    private String secondCategoryName;

    /**
     * 三级类目ID
     */
    private Long thirdCategoryId;

    /**
     * 三级类目名称
     */
    private String thirdCategoryName;

    /**
     * 末级类目id
     */
    private Long lastCategoryId;

    /**
     * 末级类目名称
     */
    private String lastCategoryName;

    /**
     * 品牌ID
     */
    private Long brandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 商品数量
     */
    private Integer goodsCount;

    /**
     * 销售单价(区域时保存区域价格，包厢时保存包厢价格，外部供应商商品时本字段为0，商品+商品时为区域销售价）
     */
    private BigDecimal salePrice;

    /**
     * 商品单价 销售价，阶梯价，区域价，会员分组价，会员等级价
     */
    private BigDecimal goodsPrice;

    /**
     * 成交价 下单时候的销售单价-商家券优惠
     */
    private BigDecimal tradePrice;

    /**
     * 商品总价=商品单价*商品数量，有VMS议价时，商品单价*议价数量
     */
    private BigDecimal goodsAmount;

    /**
     * 运费金额
     */
    private BigDecimal goodsFreight;

    /**
     * 优惠总金额  包含店铺优惠、平台优惠和使用返利的合计
     */
    private BigDecimal totalDiscountAmount;

    /**
     * 店铺优惠金额   分担优惠券金额中，店铺优惠金额
     */
    private BigDecimal shopDiscountAmount;

    /**
     * 平台优惠金额   分担优惠券金额中，平台优惠金额
     */
    private BigDecimal platformDiscountAmount;

    /**
     * 使用返利金额 (VMS开单时才会输入）
     */
    private BigDecimal usedRebateAmount;

    /**
     * 议价后商品总价 
     */
    private BigDecimal bargainingGoodsAmount;

    /**
     * 议价后运费金额
     */
    private BigDecimal bargainingGoodsFreight;

    /**
     * 订单行总价
     */
    private BigDecimal orderItemTotalAmount;

    /**
     * 订单行实付金额
     */
    private BigDecimal orderItemPayAmount;

    /**
     * 商品实际单价 商品总金额-用券优惠总金额-议价优惠总金额（不含运费）
     */
    private BigDecimal goodsRealPrice;

    /**
     * 退货/退款状态：0：未退货/退款，1：已退款，2：已退货
     */
    private String refundStatus;


    /**
     * 原关联的商品编号，包含普通商品、套餐商品等所有商品
     */
    private String relationGoodsNo;

    /**
     * 千橙掌柜商品编号
     */
    private String goodsNo;

    /**
     * 是否入库 1否2 是
     */
    private Integer stockFlag;

    /**
     * 是否入仓:(1:否 2:是)
     */
    private Integer warehouseFlag;

    /**
     * 仓库编号
     */
    private String warehouseNo;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 是否有辅计量单位:1-否;2-是;
     */
    private Integer standardFlag;

    /**
     * 计量单位名称
     */
    private String calculationUnitName;

    /**
     * 辅计量单位名称
     */
    private String assistCalculationUnitName;

    /**
     * 主计量单位对应关系数值
     */
    private BigDecimal mainUnitNum;

    /**
     * 辅计量单位对应关系数值
     */
    private BigDecimal assistUnitNum;

    /**
     * 转换率(辅转主)
     */
    private BigDecimal conversionRate;

    private List<String> goodsNos;

    /**
     * 订单编号集合
     */
    private List<String> orderNoList;

    private LocalDateTime modifyTime;
    private String createNo;
    private String createName;
    private LocalDateTime createTime;
    private String modifyNo;
    private String modifyName;
    private Integer deleteFlag;

    /**
     * 采购商品入库库存数量
     */
    private BigDecimal realStockNum;

    /**
     * 1007=千橙采购；3001=羊乃世家，见 DataPurchaseSourceEnum
     */
    private String dataSource;

    /**
     * 与采购单位对应关系值,0 < 对应关系 <= 999 之间的正整数
     */
    private BigDecimal unitRelationNum;

    /**
     * 与采购单位对应关系值
     */
    private String unitRelation;

    /**
     * 买家账号
     */
    private String buyerCode;

    /**
     * 采购单位
     */
    private String unitName;

}
