package cn.htdt.app.bossapp.dto.response.finance;

import cn.htdt.common.dto.BigDecimalSerialize;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021-12-20
 * @description 分页查询销售收入列表响应DTO
 **/
@Data
public class ResBossAppPageSaleDTO extends ResBossAppCommonDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单时间")
    @JsonFormat(pattern = "yyyy.MM.dd")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "商品名称")
    private String goodsNameConcat;

    @ApiModelProperty(value = "订单金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal shouldAmount;

    @ApiModelProperty(value = "商品数")
    private Integer goodsCount;

    @ApiModelProperty(value = "实际收入")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal realAmount;

    @ApiModelProperty(value = "冲减金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal deductAmount;

    @ApiModelProperty(value = "线下返金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal waitAmount;

    @ApiModelProperty(value = "是否有冲减金额 1否 2是")
    private Integer remarkFlag;

    @ApiModelProperty(value = "是否有线下返金额 1否 2是")
    private Integer waitAmountFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
