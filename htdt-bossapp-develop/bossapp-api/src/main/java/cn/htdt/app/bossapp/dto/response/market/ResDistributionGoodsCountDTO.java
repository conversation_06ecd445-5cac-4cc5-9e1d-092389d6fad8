package cn.htdt.app.bossapp.dto.response.market;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021-11-12
 * @description 我要云卖货-统计响应DTO
 **/
@Data
public class ResDistributionGoodsCountDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "累计成交订单数")
    private Integer totalOrderCount;

    @ApiModelProperty(value = "总销售额")
    private String totalSaleCount;

    @ApiModelProperty(value = "实际销售额")
    private String totalRealSaleCount;

    @ApiModelProperty(value = "已入账金额")
    private String totalReceiveSaleCount;

    @ApiModelProperty(value = "待返金额")
    private String totalWaitSaleCount;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
