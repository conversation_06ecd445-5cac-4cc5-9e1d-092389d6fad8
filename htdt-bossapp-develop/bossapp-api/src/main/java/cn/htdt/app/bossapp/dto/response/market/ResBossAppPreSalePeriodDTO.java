package cn.htdt.app.bossapp.dto.response.market;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalTime;
import java.util.List;

/**
 * 商品预售活动的时间段响应实体类
 *
 * <AUTHOR>
 * @date 2022-01-02
 */
@Data
public class ResBossAppPreSalePeriodDTO implements Serializable {

    private static final long serialVersionUID = 5860264671203479466L;
    
    @ApiModelProperty(value = "时间段编号")
    private String periodNo;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "每场开始时间")
    @JsonFormat(pattern = "HH:mm:ss")
    private LocalTime startTime;

    @ApiModelProperty(value = "每场结束时间")
    @JsonFormat(pattern = "HH:mm:ss")
    private LocalTime endTime;

    @ApiModelProperty(value = "活动场次下的商品数据")
    List<ResBossAppGoodsPromotionGoodsDTO> goodsRelationDTOList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
