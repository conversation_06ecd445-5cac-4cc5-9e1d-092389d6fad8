package cn.htdt.app.bossapp.dto.response.market;

import cn.htdt.common.dto.BigDecimalSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;

/**
 * 活动数据统计
 *
 * <AUTHOR>
 * @date 2022-07-20
 */
@Data
public class ResBossAppPromotionDataStatisticsDTO {

    @ApiModelProperty(value = "订单数")
    private Integer orderNum;

    @ApiModelProperty(value = "订单金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "下单人数")
    private Integer orderFansNum;

    @ApiModelProperty(value = "客单价")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal customerUnitPrice;

    @ApiModelProperty("访问人数")
    private int browseFansNum;

    @ApiModelProperty("转化率,整数,如10%返回10")
    private int conversionRate;

    @ApiModelProperty(value = "订单实收金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal realAmount;

    @ApiModelProperty(value = "订单退款金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "新增粉丝数")
    private Integer newFansNum;

    @ApiModelProperty(value = "新下单人数")
    private Integer newOrderFansNum;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
