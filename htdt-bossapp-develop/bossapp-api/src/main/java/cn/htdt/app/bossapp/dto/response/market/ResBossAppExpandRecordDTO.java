package cn.htdt.app.bossapp.dto.response.market;

import cn.htdt.common.dto.DesensitizationSerialize;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 膨胀红包发起记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Data
public class ResBossAppExpandRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "膨胀编号")
    private String expandNo;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "粉丝编号")
    private String fanNo;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "使用门槛阈值（满减时表示满多少元）")
    private BigDecimal discountThreshold;

    @ApiModelProperty(value = "膨胀金额")
    private BigDecimal expandMoney;

    @ApiModelProperty(value = "兑换门槛阈值（满多少元可兑换）")
    private BigDecimal exchangeThreshold;

    @ApiModelProperty(value = " 兑换状态(1:未兑换 2:已兑换)")
    private Integer exchangeStatus;

    @ApiModelProperty(value = "券使用状态(1:未使用 2:已使用)")
    private Integer couponUseStatus;

    @ApiModelProperty(value = "兑换时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime exchangeTime;

    @ApiModelProperty(value = "用户券编号")
    private String userCouponNo;

    @ApiModelProperty(value = "是否可用:默认1，1：可用，其余不可用")
    private Integer disableFlag;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "门店编号，对应orgId")
    private String storeNo;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "是否使用(1:否 2:是)")
    private Integer useFlag;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
}
