package cn.htdt.app.bossapp.dto.response.content;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 消息通知设置响应DTO
 *
 * <AUTHOR>
 * @date 2021/6/9
 **/
@Data
public class ResBossAppMsgNoticeSetSaveDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String adminParams;

    /**
     * 是否开启：1=开启；2未开启。
     */
    @ApiModelProperty(notes = "是否开启：1=开启；2未开启。")
    private Integer setFlag;

    /**
     * key：1000=横幅通知；2000=消息；2001=订单待办消息：销售单；2002=订单待办消息-售后单；2003平台公告消息；3000=通知；3001=云卖货商品审核通知；3002=云卖货商品被平台下架通知；3003=店铺商品被运营下架通知；3004=店铺商品被商家下架通知；3005=平台分销商品预估佣金通知；3006=平台分销商品佣金到账通知
     */
    @ApiModelProperty(notes = "key：1000=横幅通知；2000=消息；2001=订单待办消息：销售单；2002=订单待办消息-售后单；2003平台公告消息；3000=通知；3001=云卖货商品审核通知；3002=云卖货商品被平台下架通知；3003=店铺商品被运营下架通知；3004=店铺商品被商家下架通知；3005=平台分销商品预估佣金通知；3006=平台分销商品佣金到账通知；3007=服务市场订单到期提醒开关")
    private String setKey;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
