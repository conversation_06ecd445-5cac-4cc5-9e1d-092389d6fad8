package cn.htdt.app.bossapp.dto.response.market;

import cn.htdt.common.dto.response.ResOrderMainButtonDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 会员价活动列表响应实体类
 *
 * <AUTHOR>
 * @date 2022-07-22
 */
@Data
public class ResBossAppMemberPriceListDTO implements Serializable {

    private static final long serialVersionUID = -8062957183220115886L;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "活动名称")
    private String promotionName;

    @ApiModelProperty(value = "活动上下架状态（1：未上架 2：上架）")
    private Integer upDownFlag;

    @ApiModelProperty(value = "活动状态 1000:草稿 1001:活动未开始 1002:活动进行中，1003:活动已结束")
    private String status;

    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDateTime effectiveTime;

    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDateTime invalidTime;

    @ApiModelProperty(value = "每场开始时间字符串，格式为HH:mm:ss")
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private LocalTime startTime;

    @ApiModelProperty(value = "每场结束时间字符串，格式为HH:mm:ss")
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private LocalTime endTime;

    @ApiModelProperty(value = "周期模式:1001每天重复,1002每周重复,1003每月重复", required = true)
    private String repectType;

    @ApiModelProperty(value = "周期值:W1-W7周日-周六;M1-M31每月1-31日", required = true)
    private String repectVal;

    @ApiModelProperty(value = "创建来源 1000:PC  2000:APP")
    private String createSource;

    @ApiModelProperty(value = "下单数量")
    private Integer orderNum;

    @ApiModelProperty(value = "活动进行中标识: 2:进行中 其他：非进行中")
    private Integer activateFlag;

    @ApiModelProperty(value = "按钮列表")
    List<ResOrderMainButtonDTO> buttonDTOS;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
