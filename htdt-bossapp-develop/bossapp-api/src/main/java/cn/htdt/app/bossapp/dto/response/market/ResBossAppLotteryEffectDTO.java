package cn.htdt.app.bossapp.dto.response.market;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 抽奖活动效果出差
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Data
public class ResBossAppLotteryEffectDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "总计中奖数量")
    private Integer allDrawNum;

    @ApiModelProperty(value = "总抽奖人数")
    private Integer allLotteryUserNum;

    @ApiModelProperty(value = "待抽奖品数")
    private Integer residueLotteryNum;

    @ApiModelProperty(value = "拉新人数")
    private Integer newFansNum;

    @ApiModelProperty(value = "订单数")
    private Integer orderNum;
}
