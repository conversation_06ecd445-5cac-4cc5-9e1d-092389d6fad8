package cn.htdt.app.bossapp.dto.response.market;

import cn.htdt.app.bossapp.dto.response.order.AppDeliveryOrderItemDTO;
import cn.htdt.app.bossapp.dto.response.order.ResOrderItemsDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 活动中奖记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-03
 */
@Data
public class ResBossAppLotteryDrawRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "记录编号")
    private String recordNo;

    @ApiModelProperty(value = "奖品编号")
    private String rewardNo;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty("活动名称")
    private String promotionName;

    @ApiModelProperty(value = "奖品类型（1001:实物礼品 1002:优惠券 1003:话费券 1004:话费 1005:汇金币 1006:积分  9999:谢谢惠顾）")
    private String rewardType;

    @ApiModelProperty(value = "奖品名称（1001:实物礼品 1002:优惠券 1003:话费券 1004:话费 1005:汇金币 1006:积分  9999:谢谢惠顾）")
    private String rewardTypeName;

    @ApiModelProperty(value = "奖品名称")
    private String rewardName;

    @ApiModelProperty("奖品值 (话费、金币、积分)")
    private String rewardValue;

    @ApiModelProperty("活动类型：1000:抽奖  2000:商品促销 3000:优惠券")
    private String activityType;

    @ApiModelProperty(value = "粉丝编号")
    private String fanNo;

    @ApiModelProperty(value = "昵称")
    private String name;

    @ApiModelProperty(value = "粉丝备注名称（店铺）")
    private String storeFanName;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty("门店编号")
    private String storeNo;
    @ApiModelProperty("店铺名称")
    private String storeName;

    @ApiModelProperty(value = "发放/领取状态(1001:已领取 1002:未领取 1003:已发放 1004:未发放 1005:发放失败 )")
    private String sendReceiveStatus;
    /**
     * 中奖时间
     */
    @ApiModelProperty(value = "中奖时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime createTime;
    /**
     * 促销类型 1001:大转盘 1002:摇奖机1003:拆盲盒1004:套圈圈 1005:小猫钓鱼 1006:膨胀红包 2001:秒杀 2002:拼团 2003:预售 2004:单品促销 2005:满额促销 2006:满量促销 2007:满额赠 2008:套餐 3001:平台-手动发代理人券 3002::平台-手动发粉丝券 3003:平台-网店代理人领券 3004:平台-网店粉丝领券 3005:店铺-手动发粉丝券 3006:店铺-网店粉丝领券 3007:膨胀红包 3100:抽奖券 4001:报名活动
     */
    @ApiModelProperty(value = "促销类型 1001:大转盘 1002:摇奖机1003:拆盲盒1004:套圈圈 1005:小猫钓鱼 1006:膨胀红包")
    private String promotionType;

    @ApiModelProperty(value = "来源 1001：平台 1002：商家 1003：店铺")
    private String sourceType;
    @ApiModelProperty(value = "核销状态:1：待核销 2：已核销 3:已核销（不可核销）")
    private Integer writeOffStatus;

    @ApiModelProperty(value = "是否可核销:1：可核销 2：不可核销")
    private Integer writeOffFlag;

    @ApiModelProperty(value = "核销关联订单商品信息")
    private List<ResOrderItemsDTO> resOrderItemsList;

    @ApiModelProperty(value = "是否启用串码(1:否 2:是)")
    private Integer imeiFlag;

    @ApiModelProperty(value = "是否入仓:(1:否 2:是)")
    private Integer warehouseFlag;

    /**
     * 商品仓库列表
     */
    @ApiModelProperty(value = "商品仓库列表")
    private List<AppDeliveryOrderItemDTO.GoodsWarehouse> goodsWarehouseList;

    @ApiModelProperty(value = "订单应付金额")
    private BigDecimal shouldAmount;

    @ApiModelProperty(value = "订单实付金额")
    private BigDecimal realAmount;

    @ApiModelProperty(value = "订单商品总件数")
    private BigDecimal orderTotalNum;

    @ApiModelProperty("规格属性")
    private String extInfo;

    /**
     * 订单编号，用于涉及到订单的分销商品和团购
     */
    private String orderNo;

    /**
     * 核销码（二期需求）
     */
    private String writeOffCode;

    /**
     * 商品图片
     */
    private String goodsPicPath;
}
