package cn.htdt.app.bossapp.dto.response.market;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 报名活动列表
 * <AUTHOR>
 * @date 2022/3/25 16:16
 */
@Data
public class ResBossAppEnrollRuleListDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 活动编号
     */
    @ApiModelProperty(value = "活动编号")
    private String promotionNo;
    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String promotionName;
    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime effectiveTime;
    /**
     * 上下架状态（1：未上架 2：上架）
     */
    @ApiModelProperty(value = "上下架状态（1：未上架 2：上架）")
    private Integer upDownFlag;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime invalidTime;

    @ApiModelProperty(value = "1-未开始 2-进行中 3-已结束")
    private Integer promotionStatus;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime modifyTime;


    /**
     * 报名说明
     */
    @ApiModelProperty(value = "报名说明")
    private String enrollExplain;
    /**
     * 图片类型(1:默认图片 2:自定义图片)
     */
    @ApiModelProperty(value = "图片类型(1:默认图片 2:自定义图片)")
    private Integer promotionPictureType;

    /**
     * 图片url
     */
    @ApiModelProperty(value = "图片url")
    private String promotionPictureUrl;

    @ApiModelProperty(value = "历史受理记录")
    private List<ResBossAppEnrollDrawRecordDTO> enrollDrawRecordList;

    @ApiModelProperty(value = "详情按钮（1001：敬请期待 1002：我要报名 1003：已报名，受理中 1004：报名成功 1005：已驳回，重新报名 1006：已过期 1007：已下架 1008：已驳回，不可报名 ）")
    private String detailButton;


}
