package cn.htdt.app.bossapp.dto.response.finance;

import cn.htdt.common.dto.BigDecimalSerialize;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021-12-20
 * @description 分页查询套餐购买列表响应DTO
 **/
@Data
public class ResBossAppPageSetmealDTO extends ResBossAppCommonDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单时间")
    @JsonFormat(pattern = "yyyy.MM.dd")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "订单编号")
    private String dealsNo;

    @ApiModelProperty(value = "套餐名称")
    private String packageName;

    @ApiModelProperty(value = "订单金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal payAmount;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
