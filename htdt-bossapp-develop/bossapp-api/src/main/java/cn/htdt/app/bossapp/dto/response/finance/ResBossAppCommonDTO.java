package cn.htdt.app.bossapp.dto.response.finance;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2021-12-20
 * @Description 财务基础响应DTO
 **/
@Data
public class ResBossAppCommonDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonIgnore
    private String operateTime;

    @JsonIgnore
    private LocalDateTime originalTime;

    @JsonIgnore
    private Integer index;

    @JsonIgnore
    private Long changeTime;

    @ApiModelProperty(value = "月度统计")
    private ResBossAppMonthCommonDTO resBossAppMonthCommonDTO;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
