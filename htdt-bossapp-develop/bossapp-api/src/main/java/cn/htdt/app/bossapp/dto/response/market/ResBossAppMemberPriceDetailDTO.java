package cn.htdt.app.bossapp.dto.response.market;

import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.dto.request.ReqBaseDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 会员价活动DTO
 *
 * <AUTHOR>
 * @date 2022-07-20
 */
@Data
public class ResBossAppMemberPriceDetailDTO extends ReqBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "满减满折类目list")
    private List<ResBossAppCouponCategoryRelationDTO> reqCouponCategoryRelationDTOS;

    @ApiModelProperty(value = "1001 一口价  1002 折扣率 旧数据一律一口价计算")
    private String goodsPromotionUserType;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "活动名称")
    private String promotionName;

    @ApiModelProperty(value = "周期模式:1001每天重复,1002每周重复,1003每月重复")
    private String repectType;

    @ApiModelProperty(value = "周期值:W1-W7周日-周六;M1-M31每月1-31日")
    private String repectVal;

    @ApiModelProperty(value = "每场开始时间字符串，格式为HH:mm:ss")
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private LocalTime startTime;

    @ApiModelProperty(value = "每场结束时间字符串，格式为HH:mm:ss")
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private LocalTime endTime;

    @ApiModelProperty(value = "活动对象 1001: 不限制 1002: 限制老用户 1003: 限制新用户 1004:会员")
    private String userScope;

    @ApiModelProperty(value = "活动渠道(1001:汇享购下单 1002：商家版下单 1003：一体机下单)")
    private String promotionChannel;

    @ApiModelProperty(value = "商品活动范围(商品活动范围(1001:店铺全场通用 1002:店铺指定商品 1003:店铺指定类目)")
    private String goodsPromotionScope;

    @ApiModelProperty(value = "优惠折扣(全场商品时必填)")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal discountMoney;

    @ApiModelProperty(value = "活动商品列表")
    private List<ResBossAppPromotionGoodsDTO> goodsList;

    @ApiModelProperty(value = "上下架状态（1：未上架 2：上架） 保存并上架存")
    private Integer upDownFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
