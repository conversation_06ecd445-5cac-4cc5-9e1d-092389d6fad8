package cn.htdt.app.bossapp.dto.response.market;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;

/**
 * 满减满折多档位DTO
 *
 * <AUTHOR>
 * @date 2022-10-25
 */
@Data
public class ResBossAppFullDiscountInfoMultiLevelDTO {

    @ApiModelProperty(value = "档位编号")
    private String multiLevelNo;

    @ApiModelProperty(value = "满额阈值（满减满折时表示满多少元）")
    private BigDecimal fulfilQuotaMoney;

    @ApiModelProperty(value = "优惠折扣(满减时是金额，满折时是折扣)")
    private BigDecimal discountMoney;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
