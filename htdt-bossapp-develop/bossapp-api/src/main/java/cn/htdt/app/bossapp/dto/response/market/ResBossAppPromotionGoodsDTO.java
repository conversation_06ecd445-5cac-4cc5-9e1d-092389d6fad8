package cn.htdt.app.bossapp.dto.response.market;

import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 促销活动商品
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="ResBossAppPromotionGoodsDTO对象", description="活动商品列表")
public class ResBossAppPromotionGoodsDTO implements Serializable {
    private static final long serialVersionUID = 1L;





    @ApiModelProperty(value = "折扣率")
    private String discountRate;



    @ApiModelProperty(value = "商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "活动价")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal promotionPrice;

    @ApiModelProperty(value = "零售价")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal retailPrice;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品主图")
    private String mainPictureUrl;

    @ApiModelProperty(value = "可售库存")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal canSaleStockNum;

    @ApiModelProperty(value = "主计量单位ID")
    private String calculationUnitNo;

    @ApiModelProperty(value = "主计量单位名称")
    private String calculationUnitName;

    @ApiModelProperty(value = "第一属性名称")
    private String firstAttributeName;

    @ApiModelProperty(value = "第一属性值")
    private String firstAttributeValueName;

    @ApiModelProperty(value = "第二属性名称")
    private String secondAttributeName;

    @ApiModelProperty(value = "第二属性值")
    private String secondAttributeValueName;

    @ApiModelProperty(value = "第三属性名称")
    private String thirdAttributeName;

    @ApiModelProperty(value = "第三属性值")
    private String thirdAttributeValueName;

    @ApiModelProperty(value = "商品是否删除 1否2是")
    private Integer goodsDeleteFlag;

}
