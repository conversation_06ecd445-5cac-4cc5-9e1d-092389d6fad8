package cn.htdt.app.bossapp.dto.response.market;

import cn.htdt.common.enums.market.PromotionTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 活动类型
 *
 * <AUTHOR>
 * @date 2022-07-22
 */
@Data
public class ResBossAppPromotionTypeListDTO {

    public ResBossAppPromotionTypeListDTO(PromotionTypeEnum promotionTypeEnum) {
        this.promotionTypeCode = promotionTypeEnum.getCode();
        this.promotionTypeName = promotionTypeEnum.getType();
    }

    @ApiModelProperty(value = "活动类型编号")
    private String promotionTypeCode;

    @ApiModelProperty(value = "活动类型名称")
    private String promotionTypeName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
