package cn.htdt.app.bossapp.dto.response.finance;

import cn.htdt.common.dto.BigDecimalSerialize;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021-12-20
 * @description 分页查询分销返佣列表响应DTO
 **/
@Data
public class ResBossAppPageRewardDTO extends ResBossAppCommonDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单时间")
    @JsonFormat(pattern = "yyyy.MM.dd")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @JsonIgnore
    private String goodsNameConcat;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "订单金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal shouldAmount;

    @JsonIgnore
    private BigDecimal totalRewardValue;

    @ApiModelProperty(value = "佣金金额(元)")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal rewardValue;

    @JsonIgnore
    private Integer distributionRewardStatusFlag;

    @ApiModelProperty(value = "佣金状态 2冻结 3解冻 9失效")
    private Integer rewardStatusFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
