package cn.htdt.app.bossapp.dto.response.finance;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 小橙收款交易流水查询响应结果
 *
 * <AUTHOR>
 * @date 2022-03-18
 **/
@Data
public class ResBossAppXCTradeFlowDTO implements Serializable {

    private static final long serialVersionUID = 2871504348634900427L;

    @ApiModelProperty(value = "交易金额")
    private String amount;

    @ApiModelProperty(value = "交易完成时间")
    private String finishTime;

    @ApiModelProperty(value = "交易类型")
    private String tradeType;

    @ApiModelProperty(value = "交易类型值")
    private String tradeTypeValue;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
