package cn.htdt.app.bossapp.dto.response.market;

import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 橙豆数据报表二级页面, 返回的数据
 *
 * <AUTHOR>
 * @date 2023/02/10
 */
@Data
public class ResBossAppVirtualCoinDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 橙豆总量
     */
    @ApiModelProperty(value = "橙豆到账")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal totalCoins;

    /**
     * 橙豆总收费
     */
    @ApiModelProperty(value = "橙豆收益")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal totalPaymentAmount;

    /**
     * 橙豆消费总数量
     */
    @ApiModelProperty(value = "橙豆消费总数量")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal totalConsumeCoins;

    /**
     * 所选时间段内使用橙豆的订单/所选时间段内支付成功的一体机订单*100%
     */
    @ApiModelProperty(value = "订单占比")
    private String consumeCoinsOrderRate;

    /**
     *  橙豆退回总数量
     */
    @ApiModelProperty(value = "橙豆退回总计")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal totalRefundCoins;

    /**
     * 所选时间段内橙豆退单成功的订单/所选时间段内使用橙豆支付成功的订单*100%
     */
    @ApiModelProperty(value = "退单率")
    private String refundCoinsOrderRate;
}
