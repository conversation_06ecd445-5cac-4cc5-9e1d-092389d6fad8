package cn.htdt.app.bossapp.dto.response.notice;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022-01-14
 * @description 公告响应DTO
 **/
@Data
public class ResBossappReleaseNoticeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(notes = "编号")
    private String noticeNo;

    @ApiModelProperty(notes = "发布时间")
    @JsonFormat(pattern = "yyyy.MM.dd")
    private LocalDateTime releaseTime;

    @ApiModelProperty(notes = "标题")
    private String title;

    @ApiModelProperty(notes = "正文内容")
    private String content;

    @ApiModelProperty(notes = "url")
    private String noticeDetailUrl;

}
