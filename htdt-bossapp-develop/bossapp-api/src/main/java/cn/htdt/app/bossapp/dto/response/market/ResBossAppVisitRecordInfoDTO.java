package cn.htdt.app.bossapp.dto.response.market;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 营销活动访问
 *
 * <AUTHOR>
 */
@Data
public class ResBossAppVisitRecordInfoDTO implements Serializable {

    private static final long serialVersionUID = 2022278094921966438L;

    @ApiModelProperty(value = "抽奖类活动")
    private List<ResBossAppVisitRecordDTO> luckDrawMarket;

    @ApiModelProperty(value = "促销类活动")
    private List<ResBossAppVisitRecordDTO> promotionMarket;

    @ApiModelProperty(value = "优惠券类活动")
    private List<ResBossAppVisitRecordDTO> couponMarket;

    @ApiModelProperty(value = "门店类活动")
    private List<ResBossAppVisitRecordDTO> storeMarket;
}
