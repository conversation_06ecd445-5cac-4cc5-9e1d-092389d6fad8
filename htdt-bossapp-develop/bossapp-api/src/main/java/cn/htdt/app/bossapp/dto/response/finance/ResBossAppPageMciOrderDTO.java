package cn.htdt.app.bossapp.dto.response.finance;

import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-07-13
 * @description 分页查询滴灌通分账列表响应DTO
 **/
@Data
public class ResBossAppPageMciOrderDTO extends ResBossAppCommonDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单时间")
    @JsonFormat(pattern = "yyyy.MM.dd")
    private LocalDateTime completedAt;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "分账金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal transferAmount ;

    @ApiModelProperty(value = "订单金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal turnOver;

    @ApiModelProperty(value = "商品数量")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal tradeCount;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
