package cn.htdt.app.bossapp.dto.response.market;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021-11-10
 * @description 我要赚佣金-统计响应DTO
 **/
@Data
public class ResDistributionRewardCountDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "累计成交订单数")
    private Integer totalOrderCount;

    @ApiModelProperty(value = "累计获得佣金")
    private String totalRewardCount;

    @ApiModelProperty(value = "已入账佣金")
    private String totalReceiveRewardCount;

    @ApiModelProperty(value = "当前预估佣金")
    private String totalEstimateRewardCount;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
