package cn.htdt.app.bossapp.dto.response.market;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.enums.market.PromotionTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 活动访问统计
 *
 * <AUTHOR>
 */
@Data
public class ResBossAppVisitRecordDTO implements Serializable {

    private static final long serialVersionUID = -9220607822422242747L;

    @ApiModelProperty(value = "活动类型")
    @Converter(enumClass = PromotionTypeEnum.class, enumField = "type", fieldName = "promotionTypeDesc")
    private String promotionType;

    @ApiModelProperty(value = "活动类型描述")
    private String promotionTypeDesc;

    @ApiModelProperty(value = "小程序访问次数 pv")
    private Integer pv;

    @ApiModelProperty(value = "小程序访问人数 uv")
    private Integer uv;

    @ApiModelProperty(value = "营销拉新数")
    private Integer pullNewNum;
}
