package cn.htdt.app.bossapp.dto.response.market;

import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 优惠券商品关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="ReqCouponGoodsRelationDTO对象", description="优惠券商品关联表")
public class ResBossAppCouponGoodsRelationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "促销编码")
    private String promotionNo;

    @ApiModelProperty(value = "优惠券编号")
    private String couponNo;

    @ApiModelProperty(value = "商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "是否可用:默认1，1：可用，其余不可用")
    private Integer disableFlag;

    @ApiModelProperty(value = "操作标识:默认1，1：添加，2删除")
    private Integer operatFlag;


    @ApiModelProperty(value = "商品形式 1001-普通商品;1002-系列商品")
    private String goodsForm;

    @ApiModelProperty(value = "商品形式value")
    private String goodsFormValue;

    @ApiModelProperty(value = "酬劳类型，1佣金 2汇金币 3礼品 4专享现金券 5服务券 6话费券，对应枚举RewardTypeEnum")
    private Integer rewardType;

    @ApiModelProperty(value = "佣金元 汇金币个")
    private BigDecimal yjOrHjb;

    @ApiModelProperty(value = "零售价")
    private BigDecimal retailPrice;
    @ApiModelProperty(value = "最小零售价")
    private BigDecimal minRetailPrice;
    @ApiModelProperty(value = "最大零售价")
    private BigDecimal maxRetailPrice;



    @ApiModelProperty(value = "系列商品类型 1001:主品 1002:子品")
    private String seriesType;

    @ApiModelProperty(value = "云池供货价")
    private BigDecimal cloudPoolSupplyPrice;
    @ApiModelProperty(value = "最小云池供货价")
    private BigDecimal minCloudPoolSupplyPrice;
    @ApiModelProperty(value = "最大云池供货价")
    private BigDecimal maxCloudPoolSupplyPrice;

    @ApiModelProperty(value = "代理人佣金")
    private BigDecimal agentCommission;
    @ApiModelProperty(value = "最小代理人佣金")
    private BigDecimal minAgentCommission;
    @ApiModelProperty(value = "最大代理人佣金")
    private BigDecimal maxAgentCommission;

    @ApiModelProperty(value = "分销店佣金")
    private BigDecimal distributionStoreCommission;
    @ApiModelProperty(value = "最小分销店佣金")
    private BigDecimal minDistributionStoreCommission;
    @ApiModelProperty(value = "最大分销店佣金")
    private BigDecimal maxDistributionStoreCommission;

    @ApiModelProperty(value = "平台服务费")
    private BigDecimal platformServiceCommission;
    @ApiModelProperty(value = "最小平台服务费")
    private BigDecimal minPlatformServiceCommission;
    @ApiModelProperty(value = "最大平台服务费")
    private BigDecimal maxPlatformServiceCommission;

    @ApiModelProperty(value = "云池零售价")
    private BigDecimal cloudPoolRetailPrice;
    @ApiModelProperty(value = "最小云池零售价")
    private BigDecimal minCloudPoolRetailPrice;
    @ApiModelProperty(value = "最大云池零售价")
    private BigDecimal maxCloudPoolRetailPrice;

    @ApiModelProperty(value = "云池市场价")
    private BigDecimal cloudPoolMarketPrice;
    @ApiModelProperty(value = "最小云池市场价")
    private BigDecimal minCloudPoolMarketPrice;
    @ApiModelProperty(value = "最大云池市场价")
    private BigDecimal maxCloudPoolMarketPrice;

    @ApiModelProperty(value = "店铺编号")
    private String storeNo;
    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "可售库存")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal canSaleStockNum;

    @ApiModelProperty(value = "商品主图")
    private String mainPictureUrl;

    @ApiModelProperty(value = "销量")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal salesVolume;
}
