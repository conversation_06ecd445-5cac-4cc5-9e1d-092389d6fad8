package cn.htdt.app.bossapp.dto.response.market;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 活动分享
 * <AUTHOR>
 * @date 2021/7/8 10:29
 */
@Data
public class ResBossAppPromotionShareDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动编号",required = true)
    private String promotionNo;
    @ApiModelProperty(value ="促销类型 枚举值:1001-大转盘 1002-摇奖机 1003-拆盲盒 1004-套圈圈 1005-小猫钓鱼 1006-膨胀红包 2001-秒杀 2002-拼团 ",required = true)
    private String promotionType;
    @ApiModelProperty("上下架状态（1：未上架 2：上架）")
    private Integer upDownFlag;
}
