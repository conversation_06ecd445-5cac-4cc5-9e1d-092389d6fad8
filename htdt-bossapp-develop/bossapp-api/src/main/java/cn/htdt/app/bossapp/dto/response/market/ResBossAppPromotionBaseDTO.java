package cn.htdt.app.bossapp.dto.response.market;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 商品活动规则响应的实体类
 *
 * <AUTHOR>
 * @date 2021-07-12
 */
@Data
public class ResBossAppPromotionBaseDTO {

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "活动名称")
    private String promotionName;

    @ApiModelProperty(value = "活动类型")
    private String promotionType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
