package cn.htdt.app.bossapp.dto.response.market;

import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.dto.request.ReqBaseDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 满减满折保存DTO
 *
 * <AUTHOR>
 * @date 2022-02-17
 */
@Data
public class ResBossAppFullDiscountInfoDTO extends ReqBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "活动名称")
    private String promotionName;

    @ApiModelProperty(value = "活动渠道(1001:汇享购下单 1002：商家版下单 1003：一体机下单)")
    private String promotionChannel;

    @ApiModelProperty(value = "商品活动类型，1001：单品团 1002：抽奖团 1003:满额减 1004:满额折 1005:满金额赠送")
    private String goodsPromotionType;

    @ApiModelProperty(value = "活动状态 1001:活动未开始 1002:活动进行中，1003:活动已结束")
    private String status;

    @ApiModelProperty(value = " 促销类型 2001:秒杀 2002:拼团 2003:预售 2004:单品促销 2005:满额促销 2006:满量促销 2007:满额赠 2008:套餐")
    private String promotionType;

    @ApiModelProperty(value = "满额阈值（满减满折时表示满多少元）")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal fulfilQuotaMoney;

    @ApiModelProperty(value = " 优惠折扣(满减时是金额，满折时是折扣)")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal discountMoney;

    @ApiModelProperty(value = "是否可叠加使用优惠券")
    private Integer couponUseFlag;

    @ApiModelProperty(value = "创建来源 1000:PC  2000:APP")
    private String createSource;

    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime effectiveTime;

    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime invalidTime;

    @ApiModelProperty(value = "活动类型 1000:抽奖  2000:商品促销 3000:优惠券")
    private String activityType;

    @ApiModelProperty(value = "来源 1001：平台 1002：商家 1003：店铺")
    private String sourceType;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "门店编号，对应orgId")
    private String storeNo;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "上下架状态（1：未上架 2：上架） 保存并上架存")
    private Integer upDownFlag;

    @ApiModelProperty(value = " 商品活动范围(商品活动范围(1001:店铺全场通用 1002:店铺指定类目 1003:店铺指定类目)")
    private String goodsPromotionScope;

    @ApiModelProperty(value = "商品促销时间段")
    private List<ResBossAppGoodsPromotionPeriodDTO> goodsPromotionPeriodDTOList;

    @ApiModelProperty(value = "满减满折商品list")
    private List<ResBossAppCouponGoodsRelationDTO> reqCouponGoodsRelationDTOS;

    @ApiModelProperty(value = "满减满折类目list")
    private List<ResBossAppCouponCategoryRelationDTO> reqCouponCategoryRelationDTOS;

    @ApiModelProperty(value = "多档位list")
    private List<ResBossAppFullDiscountInfoMultiLevelDTO> multiLevelDTOS;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
