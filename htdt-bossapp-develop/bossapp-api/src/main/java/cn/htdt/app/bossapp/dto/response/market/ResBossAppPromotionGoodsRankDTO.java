package cn.htdt.app.bossapp.dto.response.market;

import cn.htdt.common.dto.BigDecimalSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 促销活动商品
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16
 */
@Data
public class ResBossAppPromotionGoodsRankDTO {

    @ApiModelProperty(value = "排名")
    private String rankNum;

    @ApiModelProperty(value = "排行方式")
    private String sortWith;

    @ApiModelProperty(value = "商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品主图")
    private String mainPictureUrl;

    @ApiModelProperty(value = "第一属性名称")
    private String firstAttributeName;

    @ApiModelProperty(value = "第一属性值")
    private String firstAttributeValueName;

    @ApiModelProperty(value = "第二属性名称")
    private String secondAttributeName;

    @ApiModelProperty(value = "第二属性值")
    private String secondAttributeValueName;

    @ApiModelProperty(value = "第三属性名称")
    private String thirdAttributeName;

    @ApiModelProperty(value = "第三属性值")
    private String thirdAttributeValueName;

    @ApiModelProperty(value = "销量")
    private Integer salesVolume;

    @ApiModelProperty(value = "订单金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "订单数")
    private Integer orderCount;

}
