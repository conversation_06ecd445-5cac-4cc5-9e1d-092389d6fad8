package cn.htdt.app.bossapp.dto.response.market;


import cn.htdt.common.dto.BigDecimalSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 橙豆权益触达效果DTO
 *
 * <AUTHOR>
 * @date 2022-12-05
 **/
@Data
public class ResBossAppOrangeBeanEffectDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "累计橙豆充值")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal orangeBeanRechargeTotal;

    @ApiModelProperty(value = "累计橙豆消费")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal orangeBeanConsumeTotal;

    @ApiModelProperty(value = "橙豆触达粉丝数率")
    private String orangeBeanFansRate;

    @ApiModelProperty(value = "橙豆触达粉丝数")
    private Integer orangeBeanFansNum;

}
