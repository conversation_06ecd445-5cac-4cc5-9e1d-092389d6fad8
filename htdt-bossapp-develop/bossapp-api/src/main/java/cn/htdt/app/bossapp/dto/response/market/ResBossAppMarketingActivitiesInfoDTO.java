package cn.htdt.app.bossapp.dto.response.market;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.enums.market.PromotionTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 统计营销活动明细
 *
 * <AUTHOR>
 */
@Data
public class ResBossAppMarketingActivitiesInfoDTO implements Serializable {

    private static final long serialVersionUID = 8421359589648272730L;

    @ApiModelProperty(value = "抽奖类活动")
    private List<Marketing> luckDrawMarket;

    @ApiModelProperty(value = "网店类活动")
    private List<Marketing> shopMarket;

    @ApiModelProperty(value = "门店类活动")
    private List<Marketing> storeMarket;

    @ApiModelProperty(value = "优惠券类活动")
    private List<Marketing> couponMarket;


    @Data
    public static class Marketing implements Serializable {

        private static final long serialVersionUID = -2762856777349797201L;

        @ApiModelProperty(value = "活动类型")
        @Converter(enumClass = PromotionTypeEnum.class, enumField = "type", fieldName = "promotionTypeDesc")
        private String promotionType;

        @ApiModelProperty(value = "活动类型描述")
        private String promotionTypeDesc;

        @ApiModelProperty(value = "活动数量")
        private Integer promotionCount;
    }
}
