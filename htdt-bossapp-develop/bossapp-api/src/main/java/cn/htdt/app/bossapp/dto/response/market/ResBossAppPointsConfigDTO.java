package cn.htdt.app.bossapp.dto.response.market;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ResBossAppPointsConfigDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "配置编号")
    private String configNo;

    @ApiModelProperty(value = "配置类型(1001:积分获得上限 1002:粉丝下单配置 1003:关注店铺赠积分  2001:积分兑换礼品 2002:积分兑换抽奖机会 2003:积分支付抵扣)")
    private String configType;

    @ApiModelProperty(value = "积分上限")
    private Integer pointsLimit;

    @ApiModelProperty(value = "赠送积分数(个)")
    private Integer presentPoints;

    @ApiModelProperty(value = "订单实付金额每满(元)")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "是否可用(1：启用，2：禁用)")
    private Integer disableFlag;
    /**
     * 积分抵扣比例默认抵扣1分钱
     */
    @ApiModelProperty(value = "积分抵扣比例默认抵扣1分钱")
    private Integer deductionScale;
    /**
     * 订单金额门槛：1001-不限制，1002-限制
     */
    @ApiModelProperty(value = "订单金额门槛：1001-不限制，1002-限制")
    private String orderAmountLimit;
    /**
     * 订单应付金额（元）
     */
    @ApiModelProperty(value = "订单应付金额（元）")
    private BigDecimal orderHandleAmount;
    /**
     * 抵扣金额门槛：1001-不限制，1002-限制
     */
    @ApiModelProperty(value = "抵扣金额门槛：1001-不限制，1002-限制")
    private String deductionAmountLimit;
    /**
     * 积分抵扣上限
     */
    @ApiModelProperty(value = "积分抵扣上限")
    private Integer pointsDeductionLimit;
    /**
     * 积分适用渠道(1400:一体机开单 1200:app及pc开单 1003:汇享购网店订单)
     */
    @ApiModelProperty(value = "积分适用渠道(1400:一体机开单 1200:app及pc开单 1003:汇享购网店订单)")
    private String useChannel;
    @ApiModelProperty(value = "是否初始化 true:是;false:否")
    private boolean initialization=false;
}
