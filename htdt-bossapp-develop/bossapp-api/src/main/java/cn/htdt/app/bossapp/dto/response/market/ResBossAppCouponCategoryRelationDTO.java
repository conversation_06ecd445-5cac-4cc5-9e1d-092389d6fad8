package cn.htdt.app.bossapp.dto.response.market;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 分类关联
 *
 * <AUTHOR>
 */
@Data
public class ResBossAppCouponCategoryRelationDTO implements Serializable {

    private static final long serialVersionUID = 2176162328009549439L;
    // 折扣率 202503
    private String discountRate;
    /**
     * 促销编码
     */
    private String promotionNo;

    /**
     * 优惠券编号
     */
    private String couponNo;

    /**
     * 类目编号
     */
    private String categoryNo;

    @ApiModelProperty(value = " 顶级节点id（第一级）")
    private String firstCategoryNo;

    /**
     * 二级节点id
     */
    @ApiModelProperty(value = " 二级节点id")
    private String secondCategoryNo;

    /**
     * 三级节点id
     */
    @ApiModelProperty(value = " 三级节点id")
    private String thirdCategoryNo;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 操作标识:默认1，1：添加，2删除
     */
    private Integer operatFlag;
}
