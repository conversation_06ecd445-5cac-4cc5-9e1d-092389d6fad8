package cn.htdt.app.bossapp.dto.response.finance;

import cn.htdt.common.dto.BigDecimalSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2021-12-20
 * @Description 财务月度统计响应DTO
 **/
@Data
public class ResBossAppMonthCommonDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "月度")
    private String month;

    @ApiModelProperty(value = "订单数")
    private Integer orderCount;

    @ApiModelProperty(value = "总金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal amountCount;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
