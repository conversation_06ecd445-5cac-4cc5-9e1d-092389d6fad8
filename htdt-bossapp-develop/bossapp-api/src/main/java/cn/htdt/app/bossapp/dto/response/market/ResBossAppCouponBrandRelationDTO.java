package cn.htdt.app.bossapp.dto.response.market;

import lombok.Data;

import java.io.Serializable;

/**
 * 品牌关联
 *
 * <AUTHOR>
 */
@Data
public class ResBossAppCouponBrandRelationDTO implements Serializable {

    private static final long serialVersionUID = 7197757204370998475L;

    /**
     * 促销编码
     */
    private String promotionNo;

    /**
     * 优惠券编号
     */
    private String couponNo;

    /**
     * 品牌编号
     */
    private String brandNo;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 操作标识:默认1，1：添加，2删除
     */
    private Integer operatFlag;
}
