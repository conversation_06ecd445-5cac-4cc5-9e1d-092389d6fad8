package cn.htdt.app.bossapp.dto.response.market;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalTime;

/**
 * 商品促销活动的时间段响应实体类
 *
 * <AUTHOR>
 * @date 2021-06-29
 */
@Data
public class ResBossAppGoodsPromotionPeriodDTO implements Serializable {

    private static final long serialVersionUID = -7261298574665592628L;

    @ApiModelProperty(value = "时间段编号")
    private String periodNo;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "每场开始时间字符串")
    private String startTimeStr;

    @ApiModelProperty(value = "每场结束时间字符串")
    private String endTimeStr;

    @ApiModelProperty(value = "每场开始时间")
    @JsonFormat(pattern = "HH:mm:ss")
    private LocalTime startTime;

    @ApiModelProperty(value = "每场结束时间")
    @JsonFormat(pattern = "HH:mm:ss")
    private LocalTime endTime;

    @ApiModelProperty(value = "周期模式:1001每天重复,1002每周重复,1003每月重复")
    private String repectType;

    @ApiModelProperty(value = "周期值:W1-W7周日-周六;M1-M31每月1-31日")
    private String repectVal;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
