package cn.htdt.app.bossapp.dto.response.market;

import cn.htdt.common.dto.request.ReqBaseDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 特惠促销活动DTO
 *
 * <AUTHOR>
 * @date 2022-07-20
 */
@Data
public class ResBossAppVipPriceDetailDTO extends ReqBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "活动名称")
    private String promotionName;

    @ApiModelProperty(value = "活动渠道(1001:汇享购下单 1002：商家版下单 1003：一体机下单)")
    private String promotionChannel;

    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime effectiveTime;

    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime invalidTime;

    @ApiModelProperty(value = "上下架状态（1：未上架 2：上架） 保存并上架存")
    private Integer upDownFlag;

    @ApiModelProperty(value = "每场开始时间字符串，格式为HH:mm:ss")
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private LocalTime startTime;

    @ApiModelProperty(value = "每场结束时间字符串，格式为HH:mm:ss")
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private LocalTime endTime;

    @ApiModelProperty(value = "活动状态:1001:未开始 1002:进行中，1003:已过期")
    private String status;

    @ApiModelProperty(value = "活动商品列表")
    private List<ResBossAppPromotionGoodsDTO> goodsList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
