package cn.htdt.app.bossapp.dto.response.market;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 券类权益触达效果DTO
 *
 * <AUTHOR>
 * @date 2022-12-05
 **/
@Data
public class ResBossAppCouponEffectDTO implements Serializable {

    private static final long serialVersionUID = -7361369256493178457L;

    @ApiModelProperty(value = "发放券总数")
    private Integer provideCouponTotal;

    @ApiModelProperty(value = "已使用券总数")
    private Integer useCouponTotal;

    @ApiModelProperty(value = "券使用率")
    private String couponUseRate;

    @ApiModelProperty(value = "领券粉丝数")
    private Integer collectCouponFansNum;

    @ApiModelProperty(value = "券触达粉丝率")
    private String couponTouchingFansRate;

    @ApiModelProperty(value = "用券粉丝数")
    private Integer useCouponFansNum;

    @ApiModelProperty(value = "用券粉丝率")
    private String useCouponFansRate;

}
