package cn.htdt.app.bossapp.dto.response.market;

import cn.htdt.common.dto.response.ResOrderMainButtonDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 优惠券配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="ResCouponSettingDTO对象", description="优惠券配置表")
public class ResBossAppCouponSettingDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /***活动基本信息**/
    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "活动类型 1000:抽奖  2000:商品促销 3000:优惠券")
    private String activityType;

    @ApiModelProperty(value = "促销类型 1001:大转盘 1002:套圈圈 1003:打地鼠 1004:扭蛋机 1005:刮刮乐 1006:膨胀红包 2001:秒杀 2002:拼团 2003:预售 2004:单品促销 2005:满额促销 2006:满量促销 2007:满额赠 2008:套餐 3001:平台-手动发代理人券 3002::平台-手动发粉丝券 3003:平台-网店代理人领券 3004:平台-网店粉丝领券 3005:店铺-手动发粉丝券 3006:店铺-网店粉丝领券 3007:膨胀红包 3100:抽奖券 4001:报名活动")
    private String promotionType;

    @ApiModelProperty(value = "活动状态 1001:活动未开始 1002:活动进行中，1003:活动已结束")
    private String status;

    @ApiModelProperty(value = "上下架状态（1：未上架 2：上架）")
    private Integer upDownFlag;

    /***优惠券规则**/

    @ApiModelProperty(value = "促销类型 3001:平台-手动发代理人券 3002::平台-手动发粉丝券 3003:平台-网店代理人领券 3004:平台-网店粉丝领券 3005:店铺-手动发粉丝券 3006:店铺-网店粉丝领券 3007:膨胀红包 3100:抽奖券")
    private String promotionCouponType;

    @ApiModelProperty(value = "券编号")
    private String couponNo;

    @ApiModelProperty(value = "活动名称")
    private String couponName;

    @ApiModelProperty(value = "优惠券类型 （1001:满减券 1002:折扣券 1003:现金券）")
    private String couponType;

    @ApiModelProperty(value = "使用门槛阈值（满减时表示满多少元）")
    private BigDecimal discountThreshold;

    @ApiModelProperty(value = "券面额")
    private BigDecimal couponValue;

    @ApiModelProperty(value = "券标识")
    private String couponIdentity;

    @ApiModelProperty(value = "券有效期（1001：长期有效 1002：指定日期 1003:自领取后天数有效）")
    private String couponPeriodValidity;

    @ApiModelProperty(value = "券有效天数（只有有效期为1003时使用）")
    private Integer couponEffectiveDay;

    @ApiModelProperty(value = "券开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime couponEffectiveTime;

    @ApiModelProperty(value = "券结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime couponInvalidTime;

    @ApiModelProperty(value = "券使用范围(1001:店铺全场通用 1002:云池商品通用 1003:指定云池商品 1004:店铺指定类目 1005:店铺指定品牌 1006:店铺指定商品 1007:指定店铺类目 1008:商家商品通用 1009:商家指定商品)")
    private String couponUseScope;

    @ApiModelProperty(value = "券使用说明")
    private String couponUseExplain;

    @ApiModelProperty("领取量")
    private java.lang.Integer receiveNum;

    @ApiModelProperty("使用量")
    private java.lang.Integer useNum;

    @ApiModelProperty(value = "创建来源 1000:PC  2000:APP")
    private String createSource;

    @ApiModelProperty(value = "按钮列表")
    List<ResOrderMainButtonDTO> buttonDTOS;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "门店编号，对应orgId")
    private String storeNo;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "店铺自主上下架状态（1：未上架 2：上架）")
    private Integer storeRelationUpDownFlag;

    @ApiModelProperty(value = "是否可用:默认1，1：可用，其余不可用")
    private Integer storeRelationDisableFlag;

    @ApiModelProperty(value = "活动上下架状态（1：未上架 2：上架）")
    private Integer originUpDownFlag;

    @ApiModelProperty("来源 1001：平台 1002：商家 1003：店铺")
    private String sourceType;

}
