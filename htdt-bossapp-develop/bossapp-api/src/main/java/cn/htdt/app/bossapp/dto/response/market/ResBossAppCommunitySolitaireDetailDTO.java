package cn.htdt.app.bossapp.dto.response.market;

import cn.htdt.app.bossapp.dto.common.BossAppPromotionImageDTO;
import cn.htdt.common.dto.request.ReqBaseDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 社群接龙活动DTO
 *
 * <AUTHOR>
 * @date 2022-07-20
 */
@Data
public class ResBossAppCommunitySolitaireDetailDTO extends ReqBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "活动名称")
    private String promotionName;

    @ApiModelProperty(value = "活动渠道(1001:汇享购下单 1002：商家版下单 1003：一体机下单)")
    private String promotionChannel;

    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime effectiveTime;

    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime invalidTime;

    @ApiModelProperty(value = "上下架状态（1：未上架 2：上架） 保存并上架存")
    private Integer upDownFlag;

    @ApiModelProperty(value = "活动状态:1001:未开始 1002:进行中，1003:已过期")
    private String status;

    @ApiModelProperty(value = "活动介绍(废弃)")
    private String promotionExplain;

    @ApiModelProperty(value = "活动介绍Json")
    private String promotionExplainJson;

    @ApiModelProperty(value = "配送方式(1000:自提 1100：配送;1000,1100:自提+配送)")
    private String deliveryWay;

    @ApiModelProperty(value = "活动通知推送开关1关闭2打开3已发送(1,2可编辑,3不可编辑)")
    private Integer sendMsgFlag;

    @ApiModelProperty(value = "活动图片")
    private List<String> promotionImageList;

    @ApiModelProperty(value = "活动商品列表")
    private List<ResBossAppPromotionGoodsDTO> goodsList;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "公众号粉丝数")
    private Integer gzhFansNumber;
    
    @ApiModelProperty(value = "促销类型 1001:大转盘 1002:套圈圈 1003:打地鼠 1004:扭蛋机 1005:刮刮乐 1006:膨胀红包 1007：砸金蛋 2001:秒杀 2002:拼团 2003:预售 2004:单品促销 2005:满额促销 2006:满量促销 2007:满额赠 2008:套餐 2010:特惠促销 2011:社群接龙 2015:商家社群接龙 3001:平台-手动发代理人券 3002::平台-手动发粉丝券 3003:平台-网店代理人领券 3004:平台-网店粉丝领券 3005:店铺-手动发粉丝券 3006:店铺-网店粉丝领券 3007:膨胀红包 3100:抽奖券 4001:报名活动")
    private String promotionType;
    
    @ApiModelProperty(value = "创建来源 1000:PC 2000:APP")
    private String createSource;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
