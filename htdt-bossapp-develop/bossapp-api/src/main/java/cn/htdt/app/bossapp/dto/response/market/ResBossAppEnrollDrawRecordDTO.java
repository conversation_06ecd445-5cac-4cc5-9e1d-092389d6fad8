package cn.htdt.app.bossapp.dto.response.market;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.enums.market.ApplyStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 报名活动记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-01
 */
@Data
public class ResBossAppEnrollDrawRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录编号
     */
    @ApiModelProperty(value = "记录编号")
    private String recordNo;

    /**
     * 活动编号
     */
    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String promotionName;


    /**
     * 门店编号，对应orgId
     */
    @ApiModelProperty(value = "门店编号，对应orgId")
    private String storeNo;

    /**
     * 店铺名称
     */
    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    /**
     * 报名时间
     */
    @ApiModelProperty(value = "报名时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    /**
     * 报名状态（1000：不用受理记录 1001：待报名
     * 报名状态（1000：不用受理记录 1001：待报名
     */
    @ApiModelProperty(value = "   * 报名状态（1000：不用受理记录 1001：待报名 1002：待受理 1003：已受理 1004：不受理）")
    @Converter(enumClass = ApplyStatusEnum.class, fieldName = "applyStatusName", enumField = "type")
    private String applyStatus;
    /**
     * 报名状态（1000：不用受理记录 1001：待报名
     * 1002：待受理 1003：已受理 1004：不受理）
     */
    @ApiModelProperty(value = "报名状态名称")
    private String applyStatusName;
    /**
     * 受理备注
     */
    @ApiModelProperty(value = "受理备注")
    private String applyExplain;
    /**
     * 店铺报名备注
     */
    @ApiModelProperty(value = "店铺报名备注")
    private String storeEnrollExplain;
    /**
     * 受理时间
     */
    @ApiModelProperty(value = "受理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime modifyTime;

    @ApiModelProperty(value = "活动交付状态(1: 活动已交付成功 0：——)")
    private String taskStatus;

}
