package cn.htdt.app.bossapp.dto.response.market;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021-11-12
 * @description 我要云卖货-进度条响应DTO
 **/
@Data
public class ResDistributionGoodsProgressDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "店铺有效商品数")
    private Integer totalGoodsCount;

    @ApiModelProperty(value = "申请云池商品数")
    private Integer totalApplyGoodsCount;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
