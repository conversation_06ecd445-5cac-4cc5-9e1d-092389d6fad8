package cn.htdt.app.bossapp.dto.response.market;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 商品预售活动详情响应的实体类
 *
 * <AUTHOR>
 * @date 2022-01-02
 */
@Data
public class ResBossAppPreSaleDetailDTO implements Serializable {

    private static final long serialVersionUID = -7062986780396597830L;
    
    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "活动名称")
    private String promotionName;

    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime effectiveTime;

    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime invalidTime;

    @ApiModelProperty(value = "活动时间段信息")
    private List<ResBossAppPreSalePeriodDTO> goodsPromotionPeriodDTOList;

    @ApiModelProperty(value = "活动对象 1001: 不限制 1002: 仅限老用户使用 1003: 仅限新用户使用")
    private String userScope;

    @ApiModelProperty(value = "来源 1001：平台 1002：商家 1003：店铺")
    private String sourceType;

    @ApiModelProperty(value = "每人限购总数量")
    private Integer limitBuyTotalNum;

    @ApiModelProperty(value = "活动说明")
    private String promotionExplain;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
