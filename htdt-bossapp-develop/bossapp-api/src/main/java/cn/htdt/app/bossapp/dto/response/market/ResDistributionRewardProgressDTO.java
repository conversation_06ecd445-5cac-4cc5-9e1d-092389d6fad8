package cn.htdt.app.bossapp.dto.response.market;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021-11-10
 * @description 我要赚佣金-进度条响应DTO
 **/
@Data
public class ResDistributionRewardProgressDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "平台分销商品数")
    private Integer totalGoodsCount;

    @ApiModelProperty(value = "上架平台分销商品数")
    private Integer totalUpGoodsCount;

    @ApiModelProperty(value = "招募代理人数")
    private Integer totalAgentCount;

    @ApiModelProperty(value = "已招募代理人数")
    private Integer totalHasAgentCount;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
