package cn.htdt.app.bossapp.dto.response.market;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 积分权益触达效果DTO
 *
 * <AUTHOR>
 * @date 2022-12-05
 **/
@Data
public class ResBossAppPointEffectDTO implements Serializable {

    private static final long serialVersionUID = -7361369256493178457L;

    @ApiModelProperty(value = "累计积分发放")
    private Integer providePointsTotal;

    @ApiModelProperty(value = "累计积分使用")
    private Integer usePointsTotal;

    @ApiModelProperty(value = "积分触达粉丝数率")
    private String pointsFansRate;

    @ApiModelProperty(value = "积分触达粉丝数")
    private Integer pointsFansNum;
}
