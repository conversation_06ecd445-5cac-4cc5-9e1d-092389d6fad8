package cn.htdt.app.bossapp.dto.response.market;

import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.dto.response.ResOrderMainButtonDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 社群接龙活动列表响应实体类
 *
 * <AUTHOR>
 * @date 2022-07-22
 */
@Data
public class ResBossAppCommunitySolitaireListDTO implements Serializable {

    private static final long serialVersionUID = -8062957183220115886L;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "活动名称")
    private String promotionName;

    @ApiModelProperty(value = "促销类型 1000:网店抽奖 1001:大转盘 1002:摇奖机1003:拆盲盒1004:套圈圈 1005:小猫钓鱼 "
            + "1006:膨胀红包抽奖 1007:砸金蛋 2001:秒杀 2002:拼团 2003:预售 2004:单品促销 2005:满额促销 2006:满量促销 "
            + "2007:满额赠 2008:套餐 2009:限时购 2010:特惠促销 2011:社群接龙 2012:橙豆营销 2013:计次卡 2014:会员价 2015:商家社群接龙"
            + "3001:平台-手动发代理人券 3002::平台-手动发粉丝券 3003:平台-网店代理人领券 3004:平台-网店粉丝领券 "
            + "3005:店铺-手动发粉丝券 3006:店铺-网店粉丝领券 3007:膨胀红包 3100:抽奖券 3200:代金券 3201:礼品卡 4001:报名活动")
    private String promotionType;

    @ApiModelProperty(value = "活动上下架状态（1：未上架 2：上架）")
    private Integer upDownFlag;

    @ApiModelProperty(value = "活动状态 1000:草稿 1001:活动未开始 1002:活动进行中，1003:活动已结束")
    private String status;

    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern = "yyyy.MM.dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime effectiveTime;

    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern = "yyyy.MM.dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime invalidTime;

    @ApiModelProperty(value = "创建来源 1000:PC  2000:APP")
    private String createSource;

    @ApiModelProperty(value = "下单数量")
    private Integer orderNum;

    @ApiModelProperty(value = "活动收入")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "按钮列表")
    List<ResOrderMainButtonDTO> buttonDTOS;

    @ApiModelProperty(value = "分发店铺设置是否上架 1上架 其他下架")
    private Integer storeRelationDisableFlag;

    @ApiModelProperty(value = "商家上下架接龙活动状态（1：未上架 2：上架）")
    private Integer merchantUpDownFlag;

    @ApiModelProperty(value = "分发店铺商家设置上下架状态（1：未上架 2：上架）")
    private Integer storeRelationUpDownFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
