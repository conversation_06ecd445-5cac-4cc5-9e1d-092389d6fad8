package cn.htdt.app.bossapp.dto.response.market;

import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 活动奖品商品关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="PromotionRewardGoodsRelationDomain对象", description="活动奖品商品关联表")
public class LotteryBossAppRewardGoodsRelationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "奖品编号")
    private String rewardNo;

    @ApiModelProperty(value = "奖品类型（1001:实物礼品 1002:优惠券 1003:话费券 1004:话费 1005:汇金币 1006:积分  9999:谢谢惠顾）")
    private String rewardType;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "是否可用:默认1，1：可用，其余不可用")
    private Integer disableFlag;

    @ApiModelProperty(value = "可售库存")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal canSaleStockNum;

    @ApiModelProperty(value = "商品主图")
    private String mainPictureUrl;

    @ApiModelProperty(value = "销量")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal salesVolume;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;
}
