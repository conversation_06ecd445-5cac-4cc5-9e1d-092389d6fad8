package cn.htdt.app.platform.dto.response.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021-07-28
 * @description 成交订单数详情响应DTO
 **/
@Data
public class ResPlatformDetailOrderCountDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "总部分销成交订单数")
    private Integer orderCount;

    @ApiModelProperty(value = "环比%")
    private String roundRate;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
