package cn.htdt.app.platform.dto.response.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 订单核销查询DTO
 *
 * <AUTHOR>
 */
@Data
public class ResPlatformOrderWriteOffDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = -4466979953808687520L;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "订单状态")
    private String orderStatus;

    @ApiModelProperty(value = "订单类型")
    private String orderType;

    @ApiModelProperty(value = "核销状态（1：待核销  2：已核销）（二期需求）")
    private Integer writeOffStatus;

    @ApiModelProperty(value = "核销码（二期需求）")
    private String writeOffCode;

    @ApiModelProperty(value = "订单商品明细")
    private List<ResPlatformOrderItemWriteOffDTO> listOrderItem;

}
