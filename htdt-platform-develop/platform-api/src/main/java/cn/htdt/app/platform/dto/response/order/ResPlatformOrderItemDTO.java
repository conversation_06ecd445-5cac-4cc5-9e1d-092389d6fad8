package cn.htdt.app.platform.dto.response.order;

import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单明细参数返回
 *
 * <AUTHOR>
 *
 *
 *
 *
 * 商品单位 unit
 * 202503 东启
 */
@Data
public class ResPlatformOrderItemDTO implements Serializable {

    private static final long serialVersionUID = -8298093504539938391L;

    @ApiModelProperty("商品单位")
    private String unit;


    @ApiModelProperty("商品编号")
    private String goodsNo;

    @ApiModelProperty("商品类目")
    private String categoryName;

    @ApiModelProperty("商品品牌")
    private String brandName;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("订单行状态")
    private String itemStatus;

    @ApiModelProperty("商品条码")
    private String barCode;

    @ApiModelProperty("规格属性")
    private String extInfo;

    @ApiModelProperty("发货仓库")
    private String warehouseName;

    @ApiModelProperty("已发货数量")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal hasDelivery = BigDecimal.ZERO;

    @ApiModelProperty("销售单价")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal goodsPriceSale;

    @ApiModelProperty("订购数量")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal goodsItemNum;

    @ApiModelProperty("销售总价")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal goodsTotalAmount;

    @ApiModelProperty("优惠金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal discountAmount;

    @ApiModelProperty("成交价")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal goodsItemRealAmount;

    @ApiModelProperty("菜品类型 1001:主菜 1002:加料")
    private String foodsType;

    @ApiModelProperty("订单行备注(买家)")
    private String orderRemarkBuyer;

    @ApiModelProperty(value = "商品标签，字典GoodsLabelEnum多个则用“,”分割")
    private String goodsLabel;

    /**
     * 商品类型 1001-实物商品;1002-虚拟商品;1003-服务商品（仅供平台使用）;1004-称重商品
     */
    @ApiModelProperty(value = "商品类型 1001-实物商品;1002-虚拟商品;1003-服务商品（仅供平台使用）;1004-称重商品")
    private String goodsType;


    /**
     *  商品重量展示
     */
    @ApiModelProperty(value = " 商品重量展示")
    private BigDecimal goodsItemNumTable;

    /**
     *商品重量（称重商品需求新增）
     */
    @ApiModelProperty(value = "商品重量（称重商品需求新增）")
    private String goodsWeight;


}
