package cn.htdt.app.platform.dto.response.order;

import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单商品明细
 *
 * <AUTHOR>
 */
@Data
public class ResPlatformOrderGoodsItemDTO implements Serializable {

    private static final long serialVersionUID = 1304233146762320112L;

    @ApiModelProperty(value = "商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "订单明细编号")
    private String itemNo;

    @ApiModelProperty(value = "商品主图")
    private String goodsPicPath;

    @ApiModelProperty(value = "商品扩展信息")
    private String extInfo;

    @ApiModelProperty(value = "商品购买数量")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal goodsItemNum;

    @ApiModelProperty(value = "申请售后商品数量")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal returnProductItemNum;

    @ApiModelProperty(value = "申请售后商品金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal applyItemReturnAmount;

    @ApiModelProperty(value = "单个商品销售价格")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal singleGoodsPriceSale;

    @ApiModelProperty(value = "是否参与优惠券分摊 1-否 2-是")
    private Integer couponFlag;
}
