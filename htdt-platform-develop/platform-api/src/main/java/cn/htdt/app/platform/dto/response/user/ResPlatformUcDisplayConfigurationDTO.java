package cn.htdt.app.platform.dto.response.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 千橙页面展示配置请求参数
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-31
 */
@Data
public class ResPlatformUcDisplayConfigurationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页签图片(千橙掌柜PC)
     */
    @ApiModelProperty(value = "页签图片(千橙掌柜PC)")
    private String tabImage;

    /**
     * 菜单栏LOGO(千橙掌柜PC)
     */
    @ApiModelProperty(value = "菜单栏LOGO(千橙掌柜PC)")
    private String menuLogo;

    @ApiModelProperty(value = "APP频道：进货频道是否展示(千橙掌柜APP) 1否 2是  0自定义")
    private Integer purchaseExhibition;

    /**
     * APP频道：进货(千橙掌柜APP)
     */
    @ApiModelProperty(value = "APP频道：进货(千橙掌柜APP)")
    private String purchase;

    /**
     * APP频道：生意经(千橙掌柜APP)
     */
    @ApiModelProperty(value = "APP频道：生意经(千橙掌柜APP) 1否 2是")
    private Integer businessSenseExhibition;


    /**
     * 网页端采购商城是否展示(千橙掌柜PC)
     */
    @ApiModelProperty(value = "网页端采购商城是否展示(千橙掌柜PC) 1否 2是  0自定义")
    private Integer purchasingMallExhibition;

    /**
     * 网页端采购商城(千橙掌柜PC)
     */
    @ApiModelProperty(value = "网页端采购商城(千橙掌柜PC)")
    private String purchasingMall;

    /**
     * 商品默认图(千橙掌柜pc/汇享购/千橙掌柜收银/千橙掌柜APP)
     */
    @ApiModelProperty(value = "商品默认图(千橙掌柜pc/汇享购/千橙掌柜收银/千橙掌柜APP)")
    private String productDefaultChart;

    /**
     * 登录页图片(千橙掌柜PC 配置过域名的商家)
     */
    @ApiModelProperty(value = "登录页图片(千橙掌柜PC 配置过域名的商家)")
    private String loginImage;

    /**
     * 版权信息(千橙掌柜PC/千橙掌柜收银 配置过域名的商家)
     */
    @ApiModelProperty(value = "版权信息(千橙掌柜PC/千橙掌柜收银 配置过域名的商家)")
    private String copyrightInformation;

    /**
     * 是否自定义域名
     */
    @ApiModelProperty(value = "是否自定义域名 1否 2是")
    private Integer domainNameExhibition;

    /**
     * 配置域名
     */
    @ApiModelProperty(value = "配置域名")
    private String domainName;

    /**
     * 商家id
     */
    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    /**
     * 数据是否完整 1否  2是
     **/
    @ApiModelProperty(value = "数据是否完整 1否  2是")
    private Integer complete;
}
