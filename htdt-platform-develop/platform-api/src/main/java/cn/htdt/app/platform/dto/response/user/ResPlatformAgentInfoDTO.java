package cn.htdt.app.platform.dto.response.user;

import cn.htdt.common.dto.annon.PrivateData;
import cn.htdt.common.dto.enums.DecipherScene;
import cn.htdt.common.dto.enums.HandlerPolicy;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 代理人信息
 * <AUTHOR>
 */
@Data
public class ResPlatformAgentInfoDTO implements Serializable {

    private static final long serialVersionUID = -5904907823453230128L;

    /**
     * 代理人编号
     */
    @ApiModelProperty(notes = "代理人编号")
    private String agentNo;

    /**
     * 代理人编号
     */
    @ApiModelProperty(notes = "粉丝编号")
    private String fanNo;

    /**

     /**
     * 状态 0待审核 1已加入 2已退出
     */
    @ApiModelProperty(notes = "状态 0待审核 1已加入 2已退出")
    private Integer status;

    /**
     * 拉新人数
     */
    @ApiModelProperty(notes = "拉新人数")
    private Integer fanNum;

    /**
     * 锁粉人数
     */
    @ApiModelProperty(notes = "锁粉人数")
    private Integer lockNum;

    /**
     * 成为代理人天数
     */
    @ApiModelProperty(notes = "成为代理人天数")
    private Double agentDays;

    /**
     * 店铺编号
     */
    @ApiModelProperty(notes = "店铺编号")
    private String storeNo;

    /**
     * 店铺名称
     */
    @ApiModelProperty(notes = "店铺名称")
    private String storeName;

    /**
     * 商家编号
     */
    @ApiModelProperty(notes = "商家编号")
    private String merchantNo;

    /**
     * 商家名称
     */
    @ApiModelProperty(notes = "商家名称")
    private String merchantName;

    /**
     * 会员编码
     */
    @ApiModelProperty(value = "会员编码")
    private String merberCode;

    /**
     * 姓名
     */
    @ApiModelProperty(notes = "姓名")
    private String name;

    /**
     * 电话
     */
    @ApiModelProperty(notes = "电话")
    private String phone;

    /**
     * 电话-加密
     */
    @ApiModelProperty(notes = "电话-加密")
    @PrivateData(handlerPolicy = HandlerPolicy.DECIPHER, decipherScene = DecipherScene.COMMON, fieldName = "phone")
    private String dsPhone;

    /**
     * 性别 0 男 1 女 2保密
     */
    @ApiModelProperty(notes = "性别 0 男 1 女 2保密")
    private Integer sex;

    /**
     * 出生日期
     */
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    @ApiModelProperty(notes = "出生日期")
    private LocalDate birthday;

    /**
     * 头像地址
     */
    @ApiModelProperty(notes = "头像地址")
    private String headImg;

    /**
     * 所在地区
     */
    @ApiModelProperty(notes = "所在地区")
    private String address;

/*    *//**
     * 详细地址
     *//*
    @ApiModelProperty(notes = "详细地址")
    private String detailAddress;*/

    /**
     * 加入时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(notes = "加入时间")
    private LocalDateTime joinTime;

    /**
     * 退出时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(notes = "退出时间")
    private LocalDateTime outTime;

    /**
     * 历史代理店铺
     */
    @ApiModelProperty(notes = "历史代理店铺")
    private String historyStore;

    @ApiModelProperty(notes = "支付账号")
    private String accountNo;


    /**
     * 是否开户 1否 2是
     */
    @ApiModelProperty(notes = "开户状态")
    private Integer openFlag;

    /**
     * 是否实名 1否 2是
     */
    @ApiModelProperty(notes = "实名状态")
    private Integer realFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
