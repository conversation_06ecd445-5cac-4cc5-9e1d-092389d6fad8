package cn.htdt.app.platform.dto.response.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ResPlatformCategoryNumDTO implements Serializable {

    private static final long serialVersionUID = -8331006406008370946L;

    /**
     * 类目id
     */
    @ApiModelProperty(value = "类目id")
    private String categoryNo;

    /**
     * 类目名称
     */
    @ApiModelProperty(value = "类目名称")
    private String categoryName;


    /**
     * 数据来源类型(默认1002，1001：MDM同步，1002：平台自建)
     */
    @ApiModelProperty(value = "数据来源类型(默认1002，1001：MDM同步，1002：平台自建)")
    private String categorySource;

    /**
     * 上架商品数量
     */
    @ApiModelProperty(value = "上架商品数量")
    private Integer goodsNum;

    @ApiModelProperty(value = "店铺类目选择类型 1001自建店铺类目树 1002引用销售类目树")
    private String categoryType;

    @ApiModelProperty(value = "商品来源 1001-平台自建;1002-商家自建;1003-店铺自建;1004:一键代发;1005:商家分发;1006:云池分发")
    private String goodsSourceType;


    @ApiModelProperty(value = "商品类型 1001：全部 1002:平台商品  1003:商家/店铺商品 1004:商家创建商品  1005:店铺创建商品 1006:店铺查询 1007:云池商品查询 1008：云池商品+本店分销商品查询 1009：店铺自建+商家分发+云池分发\"")
    private String queryType;


}
