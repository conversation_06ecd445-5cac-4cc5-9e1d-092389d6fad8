package cn.htdt.app.platform.dto.response.user;

import cn.htdt.common.dto.annon.PrivateData;
import cn.htdt.common.dto.enums.DecipherScene;
import cn.htdt.common.dto.enums.HandlerPolicy;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class ResPlatformOperateFancStoreInfoDTO implements Serializable {

    private static final long serialVersionUID = -5039045767372999598L;

    /**
     * id
     */
    private BigInteger id;
    /**
     * 编号
     */
    private String fanNo;

    /**
     * 姓名
     */
    private String name;

    /**
     * "粉丝备注名称（店铺/商家）"
     */
    private String storeFanName;

    /**
     * 密码
     */
    private String password;

    /**
     * 性别 0 男 1 女 2保密
     */
    private Integer sex;

    /**
     * 电话
     */
    private String phone;

    /**
     * 电话加密
     */
    @PrivateData(handlerPolicy = HandlerPolicy.DECIPHER, decipherScene = DecipherScene.COMMON, fieldName = "phone")
    private String dsPhone;

    /**
     * 后端盐值
     */
    private String bSalt;

    /**
     * 出生日期
     */
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    private LocalDate birthday;

    /**
     * 微信昵称
     */
    private String nickName;

    /**
     * 头像地址
     */
    private String headImg;

    /**
     * 首次归属商家编号
     */
    private String ascriptionMerchantNumber;

    /**
     * 首次归属商家名称
     */
    private String ascriptionMerchantName;

    /**
     * 首次关注店铺编号
     */
    private String followStoreNumber;

    /**
     * 首次关注店铺名称
     */
    private String followStoreName;

    /**
     * 首次注册来源
     */
    private String registeredSource;

    /**
     * 注册时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime registeredTime;

    /**
     * 注册终端 1,android 2,ios 3,小程序 4,H5
     */
    private Integer registeredTerminal;

    /**
     * 首次加入店铺来源
     */
    private String joinStoreSource;

    /**
     * 首次加入店铺时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime joinStoreTime;

    /**
     * 状态 0停用 1启用
     */
    private Integer status;

    /**
     * 推荐人手机号
     */
    private String referrerPhone;

    /**
     * 推荐人编号
     */
    private String referrerNumber;

    /**
     * 状态 1粉丝 2代理人
     */
    private Integer type;

    /**
     * 是否黑名单 1否 2是
     */
    private Integer isBlacklist;

    /**
     * 创建人ID
     */
    private String createNo;

    /**
     * 最后一次修改人ID
     */
    private String modifyNo;

    /**
     * openid
     */
    private String openid;

    /**
     * 微信开放平台标识
     */
    private String unionid;

    /**
     * 省id
     */
    private String provinceCode;

    /**
     * 省名
     */
    private String provinceName;

    /**
     * 市id
     */
    private String cityCode;

    /**
     * 市名
     */
    private String cityName;

    /**
     * 区id
     */
    private String regionCode;

    /**
     * 区名
     */
    private String regionName;

    /**
     * 详细地址
     */
    private String detailAddress;

    /**
     * 街道code
     */
    private String streetCode;

    /**
     * 街道名称
     */
    private String streetName;

    /**
     * 街道名称
     */
    private Integer homeNum;

    /**
     * 家庭收入
     */
    private Integer income;

    /**
     * 最后一次进入的店
     */
    private String lastLoginStoreNo;

    private String lastLoginStoreName;

    /**
     * 最后登录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastLoginTime;

    private String appleid;

    /**
     * 备注
     */
    private String remark;

    /**
     * 粉丝分组list
     */
    private List<ResPlatformFancGroupDTO> resFancGroupDTOList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
