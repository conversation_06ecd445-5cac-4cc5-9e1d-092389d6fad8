package cn.htdt.app.platform.dto.response.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;


/**
 * @Purpose 区域信息响应实体类

 * <AUTHOR>
 * @since 2025-04
 */
@Data
@ApiModel(description = "区域信息响应实体类")
public class ResUcRegionInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 创建时间周期
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(notes = "创建时间起始时间")
    private LocalDateTime createTimeStart;
    /**
     * 通用
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(notes = "创建时间结束时间")
    private LocalDateTime createTimeEnd;
    /**
     * 通用
     */
    @ApiModelProperty(value = "每页条数")
    private int pageSize = 10;
    /**
     * 通用
     */
    @ApiModelProperty(value = "总页数")
    private int pageNum = 1;
    /**
     * 通用
     */
    @ApiModelProperty(value = "汇通达编号")
    private String htdNo;

    /**
     * 通用
     */
    private String adminParams;

    /**
     * 通用
     */
    @ApiModelProperty(value = "店铺编码或者名称")
    private String storeNoOrName;

    /**
     * 通用
     */
    @ApiModelProperty(notes = "自定义店铺编码")
    private String customStoreNo;

    /**
     * 通用
     */
    @ApiModelProperty(notes = "省id")
    private String provinceCode;

    /**
     * 通用
     */
    @ApiModelProperty(notes = "省名")
    private String provinceName;


    /**
     * 区域标识
     */
    @ApiModelProperty(notes = "区域标识")
    private Long id;

    /**
     * 区域编码
     */
    @ApiModelProperty(notes = "区域编码")
    private String regionNo;

    /**
     * 类型
     */
    @ApiModelProperty(notes = "类型")
    private String type;

    /**
     * 状态
     */
    @ApiModelProperty(notes = "状态")
    private String status;

    /**
     * 区域名称
     */
    @ApiModelProperty(notes = "区域名称")
    private String regionName;

    /**
     * 创建人名称
     */
    @ApiModelProperty(notes = "创建人名称")
    private String createName;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(notes = "操作时间")
    private LocalDateTime createTime;

    /**
     * 最后一次修改人ID
     */
    @ApiModelProperty(notes = "最后一次修改人ID")
    private String modifyNo;

    /**
     * 最后一次修改人名称
     */
    @ApiModelProperty(notes = "最后一次修改人名称")
    private String modifyName;

    /**
     * 最后一次修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(notes = "最后一次修改时间")
    private LocalDateTime modifyTime;

    /**
     * 是否已经删除，默认1未删除，其余已删除
     */
    @ApiModelProperty(notes = "是否已经删除，默认1未删除，其余已删除")
    private Integer deleteFlag;

    /**
     * 所属商家编号
     */
    @ApiModelProperty(notes = "所属商家编号")
    private String merchantNo;
    /**
     * 所属商家 id
     */
    @ApiModelProperty(notes = "所属商家 id")
    private String merchantId;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
