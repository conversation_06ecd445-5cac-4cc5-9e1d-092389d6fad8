package cn.htdt.app.platform.dto.response.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 本店成交粉丝数返回值
 * @Date 2021/7/26
 * @Param 
 * @return 
 **/
@Data
public class ResPlatformBuyerCountDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = 741087081635029349L;


    @ApiModelProperty("成交粉丝总数")
    private int buyerNum;

    @ApiModelProperty("成交粉丝老客")
    private int oldBuyerNum;

    @ApiModelProperty("成交粉丝新客")
    private int newBuyerNum;

    /**
     * 成交粉丝老总数
     **/
    private int buyerOldNum;


    @ApiModelProperty("成交粉丝数比")
    private String buyerNumThan;

}


