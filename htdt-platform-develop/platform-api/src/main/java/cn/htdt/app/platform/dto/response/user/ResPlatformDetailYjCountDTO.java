package cn.htdt.app.platform.dto.response.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021-07-28
 * @description 获得佣金详情响应DTO
 **/
@Data
public class ResPlatformDetailYjCountDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "总部分销获得佣金")
    private BigDecimal yjCount;

    @ApiModelProperty(value = "环比%")
    private String roundRate;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
