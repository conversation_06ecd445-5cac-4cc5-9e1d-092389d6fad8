package cn.htdt.app.platform.dto.response.data;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023-07-10
 * @description 统计响应DTO
 **/
@Data
public class ResPlatformChannelCountDTO {

    @ApiModelProperty("渠道名称")
    private String channelName;

    @ApiModelProperty("开单渠道CODE")
    private String appChannelSource;

    @ApiModelProperty("支付渠道CODE")
    private String paymentChannel;

    @ApiModelProperty("数值")
    private BigDecimal number = BigDecimal.ZERO;

    @ApiModelProperty("百分比")
    private BigDecimal percentage = BigDecimal.ZERO;

    public ResPlatformChannelCountDTO(String channelName, String appChannelSource, String paymentChannel) {
        this.channelName = channelName;
        this.appChannelSource = appChannelSource;
        this.paymentChannel = paymentChannel;
    }
}
