package cn.htdt.app.platform.dto.response.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description 本店成交商品数返回值
 * @Date 2021/7/26
 * @Param 
 * @return 
 **/
@Data
public class ResPlatformGoodsSoItemCountDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = 741087081635029349L;


    @ApiModelProperty("商品数")
    private int goodsNum;

    /**
     * 老商品数
     **/
    private int goodsOldNum;


    @ApiModelProperty("商品数比")
    private String goodsNumThan;

}


