package cn.htdt.app.platform.dto.response.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 功能描述: 云池佣金响应dto
 *
 * <AUTHOR>
 * @date 2021/3/25
 **/
@Data
public class ResPlatformCloudPoolCommissionConfigDTO implements Serializable {

    @ApiModelProperty(value = "代理人佣金比例（%）")
    private BigDecimal agentCommissionRatio;

    @ApiModelProperty(value = "代理人佣金")
    private BigDecimal agentCommission;

    @ApiModelProperty(value = "分销店佣金比例（%）")
    private BigDecimal distributionStoreCommissionRatio;

    @ApiModelProperty(value = "分销店佣金")
    private BigDecimal distributionStoreCommission;
}
