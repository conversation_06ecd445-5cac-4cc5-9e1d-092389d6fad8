package cn.htdt.app.platform.dto.response.goods;

import cn.htdt.app.platform.dto.response.salecategory.ResPlatformSaleCategoryInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * 店铺展示类目信息 响应DTO
 *
 * <AUTHOR>
 * @date 2022/05/28
 **/
@Data
public class ResPlatformStoreChooseCategoryInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "1001:自建店铺类目树 1002:引用销售类目树")
    private String chooseType;

    @ApiModelProperty(value = "展示类目集合")
    private List<ResPlatformSaleCategoryInfoDTO> categoryInfoList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
