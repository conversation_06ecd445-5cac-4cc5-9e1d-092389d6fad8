package cn.htdt.app.platform.dto.response.goods;

import cn.htdt.app.platform.dto.common.PlatformGoodsValidityPeriodDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 详细说明.子品dto
 * <p>
 * Copyright: Copyright (c) 2020/11/4 13:55
 * <p>
 * Company: htd
 * <p>
 *
 * <AUTHOR>
 * @version 1.0.0
 *
 *
 * 修改:新增字段 channelSales
 * 修改原因 渠道销售
 * <AUTHOR>
 * @since 2025-03
 */
@Data
public class ResPlatformSubGoodsDTO extends PlatformGoodsValidityPeriodDTO implements Serializable {

    @ApiModelProperty(value = "渠道销售 1001 零售，1002 普通小批，1003 精品小批，1004 渠道")
    private String channelSales;

    /**
     * 系列商品id
     */
    @ApiModelProperty(value = "系列商品id")
    private String goodsNo;

    @ApiModelProperty(value = "系列商品自定义商品编号")
    private String customGoodsNo;

    @ApiModelProperty(value = "第一属性名称")
    private String firstAttributeName;

    @ApiModelProperty(value = "第一属性值")
    private String firstAttributeValueName;

    @ApiModelProperty(value = "第二属性名称")
    private String secondAttributeName;

    @ApiModelProperty(value = "第二属性值")
    private String secondAttributeValueName;

    @ApiModelProperty(value = "第三属性名称")
    private String thirdAttributeName;

    @ApiModelProperty(value = "第三属性值")
    private String thirdAttributeValueName;

    @ApiModelProperty(value = "条形码")
    private String barcode;

    @ApiModelProperty(value = "子品图片")
    private String mainPictureUrl;

    @ApiModelProperty(value = "可售库存")
    private BigDecimal canSaleStockNum;

    @ApiModelProperty(value = "实际库存")
    private BigDecimal realStockNum;

    @ApiModelProperty(value = "限价")
    private BigDecimal limitPrice;

    @ApiModelProperty(value = "市场价")
    private BigDecimal marketPrice;

    @ApiModelProperty(value = "采购价")
    private BigDecimal purchasePrice;

    @ApiModelProperty(value = "零售价")
    private BigDecimal retailPrice;

    @ApiModelProperty(value = "云池供货价")
    private BigDecimal cloudPoolSupplyPrice;

    @ApiModelProperty(value = "云池佣金配置")
    private ResPlatformCloudPoolCommissionConfigDTO commissionConfig;

    /**
     * //20250513 是否关联商品组 0否,1是
     */
    private Integer isRelatedGoods;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
