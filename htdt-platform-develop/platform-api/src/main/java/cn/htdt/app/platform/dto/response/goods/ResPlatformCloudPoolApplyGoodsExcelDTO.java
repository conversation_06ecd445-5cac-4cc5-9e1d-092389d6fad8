package cn.htdt.app.platform.dto.response.goods;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/7/11 14:45
 */
@Data
public class ResPlatformCloudPoolApplyGoodsExcelDTO {
    @ExcelProperty(value = "商品ID",index = 0)
    @ColumnWidth(15)
    private String goodsNo;

    @ExcelProperty(value = "商品名称",index = 1)
    @ColumnWidth(30)
    private String goodsName;

    @ExcelProperty(value = "销售类目",index = 2)
    @ColumnWidth(15)
    private String categoryName;

    @ExcelProperty(value = "商品品牌",index = 3)
    @ColumnWidth(15)
    private String brandName;

    @ExcelProperty(value = "商品可售库存",index = 4)
    @ColumnWidth(10)
    private BigDecimal canSaleStockNum;

    @ExcelProperty(value = "计量单位",index = 5)
    @ColumnWidth(10)
    private String calculationUnitName;

    @ExcelProperty(value = "云卖货售价（元）",index = 6)
    @ColumnWidth(10)
    private String cloudPoolRetailPrice;

    @ExcelProperty(value = "云卖货市场价（元）",index = 7)
    @ColumnWidth(10)
    private String cloudPoolMarketPrice;

    @ExcelProperty(value = "商品形式",index = 8)
    @ColumnWidth(15)
    private String goodsFormValue;

    @ExcelProperty(value = "商品类型",index = 9)
    @ColumnWidth(15)
    private String goodsTypeValue;

    @ExcelProperty(value = "商品来源",index = 10)
    @ColumnWidth(10)
    private String goodsSourceTypeValue;
}
