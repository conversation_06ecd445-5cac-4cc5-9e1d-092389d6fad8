package cn.htdt.app.platform.dto.response.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022-08-24
 * @Description 商家组织之皖付通信息
 **/
@Data
@ApiModel(description = "商家组织之皖付通信息")
public class ResPlatformMerchantWFTDTO implements Serializable {
    private static final long serialVersionUID = 6633728267582022649L;

    @ApiModelProperty(value = "商家名称")
    private String merchantNo;

    @ApiModelProperty(value = "皖付通-门店ID")
    private String wftStoreId;

    @ApiModelProperty(value = "皖付通-收银员ID")
    private String wftMerchantId;

    @ApiModelProperty(value = "皖付通-设备ID")
    private String wftDeviceId;

    @ApiModelProperty(value = "皖付通-设备类型")
    private String wftDeviceType;

    @ApiModelProperty(value = "皖付通-代理商ID")
    private String wftUserId;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}