package cn.htdt.app.platform.dto.response.order;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.dto.annon.PrivateData;
import cn.htdt.common.dto.enums.DecipherScene;
import cn.htdt.common.dto.enums.HandlerPolicy;
import cn.htdt.common.enums.goods.DeliveryWayEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 打印票据的返回参数
 *
 * <AUTHOR>
 */
@Data
public class ResPlatformOrderBillDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = -3896904011910954684L;


    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /**
     * 店铺编码，对应orgId
     */
    //private String storeNo;

    @ApiModelProperty(notes = "详细地址")
    private String detailAddress;

    @ApiModelProperty(notes = "店铺联系人姓名")
    private String contactName;

    @ApiModelProperty(notes = "店铺联系人手机号")
    private String contactPhone;

    @ApiModelProperty(notes = "店铺联系人手机号")
    @PrivateData(handlerPolicy = HandlerPolicy.DECIPHER, decipherScene= DecipherScene.COMMON, fieldName = "contactPhone")
    private String dsContactPhone;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "订单总金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "订单应收总金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal shouldAmount;

    @ApiModelProperty(value = "订单实付金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal realAmount;

    @ApiModelProperty(value = "订单欠款金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal arrearsAmount;

    @ApiModelProperty(value = "最近一次补款金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal lastArrearsAmount;

    @ApiModelProperty(value = "最近一次补款金额大写")
    private String lastArrearsAmountGreate;

    @ApiModelProperty(value = "订单实付金额(大写)")
    private String realAmountGreat;

    @ApiModelProperty(value = "下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime orderCreateTime;

    @ApiModelProperty(value = "订单已优惠金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal orderPromotionDiscount;

    @ApiModelProperty(value = "配送方式类型")
    @Converter(enumClass = DeliveryWayEnum.class, fieldName = "orderDeliveryWayName")
    private String orderDeliveryWay;

    @ApiModelProperty(value = "配送方式类型Name")
    private String orderDeliveryWayName;

    /**
     * 订单备注(买家)
     */
    @ApiModelProperty(value = "订单备注")
    private String orderRemarkBuyer;

    @ApiModelProperty("收货人省份")
    private String goodReceiverProvince;

    @ApiModelProperty("收货人城市")
    private String goodReceiverCity;

    @ApiModelProperty("收货人县区")
    private String goodReceiverCounty;

    @ApiModelProperty("收货人区镇")
    private String goodReceiverArea;

    @ApiModelProperty(value = "收货人地址")
    private String goodReceiverAddress;

    @ApiModelProperty(value = "收货人姓名")
    private String goodReceiverName;

    @ApiModelProperty(value = "收货人手机")
    private String goodReceiverMobile;

    @ApiModelProperty(value = "收货人手机")
    @PrivateData(handlerPolicy = HandlerPolicy.DECIPHER, decipherScene= DecipherScene.COMMON, fieldName = "goodReceiverMobile")
    private String dsGoodReceiverMobile;

   /* @ApiModelProperty(value = "收货人国家code")
    private String goodReceiverCountryCode;

    @ApiModelProperty(value = "收货人省份code")
    private String goodReceiverProvinceCode;

    @ApiModelProperty(value = "收货人城市code")
    private String goodReceiverCityCode;

    @ApiModelProperty(value = "收货人地区code")
    private String goodReceiverCountyCode;

    @ApiModelProperty(value = "收货人四级区域code")
    private String goodReceiverAreaCode;*/

    @ApiModelProperty(value = "订单商品明细")
    private List<ResPlatformOrderItemBillDTO> listOrderItem;

    @ApiModelProperty(value = "橙豆")
    private BigDecimal virtualCoinsDiscount = BigDecimal.ZERO;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}