package cn.htdt.app.platform.dto.response.order;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import cn.htdt.common.dto.annon.PrivateData;
import cn.htdt.common.dto.enums.DecipherScene;
import cn.htdt.common.dto.enums.HandlerPolicy;
import cn.htdt.common.enums.OrderChannelSourceEnum;
import cn.htdt.common.enums.OrderFlagEnum;
import cn.htdt.common.enums.OrderPaymentStatusEnum;
import cn.htdt.common.enums.OrderStatusEnum;
import cn.htdt.common.enums.OrderTypeEnum;
import cn.htdt.common.enums.goods.DeliveryWayEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 获取商品详情
 *
 * <AUTHOR>
 */
@Data
public class ResPlatformGetOrderDetailsDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "父订单编号")
    private String parentOrderNo;

    @ApiModelProperty(value = "订单类型")
    @Converter(enumClass = OrderTypeEnum.class, fieldName = "orderTypeName", enumField = "type", fieldValue = false)
    private String orderType;

    @ApiModelProperty(value = "订单类型名称")
    private String orderTypeName;

    @ApiModelProperty(value = "订单状态")
    @Converter(enumClass = OrderStatusEnum.class, fieldName = "orderStatusName")
    private String orderStatus;

    @ApiModelProperty(value = "订单状态描述")
    private String orderStatusName;

    @ApiModelProperty(value = "订单支付状态")
    @Converter(enumClass = OrderPaymentStatusEnum.class, fieldName = "orderPaymentStatusName")
    private String orderPaymentStatus;

    @ApiModelProperty(value = "订单支付状态描述")
    private String orderPaymentStatusName;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "店铺编号")
    private String storeNo;

    @ApiModelProperty(value = "分销店对应商户编号")
    private String distributionMerchantNo;

    @ApiModelProperty(value = "分销店对应商户名称")
    private String distributionMerchantName;

    @ApiModelProperty(value = "分销店铺编号-对应老系统的orgId")
    private String distributionStoreNo;

    @ApiModelProperty(value = "分销店铺名称")
    private String distributionStoreName;

    @ApiModelProperty(value = "下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime orderCreateTime;

    @ApiModelProperty(value = "下单人账号")
    private String buyerNo;

    @ApiModelProperty(value = "下单人手机号")
    private String buyerMobile;

    @ApiModelProperty(value = "下单人手机号加密")
    @PrivateData(handlerPolicy = HandlerPolicy.DECIPHER, decipherScene = DecipherScene.PLATFORM_ORDER_DETAIL, fieldName = "buyerMobile")
    private String dsBuyerMobile;

    @ApiModelProperty(value = "下单人姓名")
    private String buyerName;

    @ApiModelProperty(value = "收货人姓名")
    private String goodReceiverName;

    @ApiModelProperty(value = "收货人手机号")
    private String goodReceiverMobile;

    @ApiModelProperty(value = "收货人手机号加密")
    @PrivateData(handlerPolicy = HandlerPolicy.DECIPHER, decipherScene = DecipherScene.PLATFORM_ORDER_DETAIL, fieldName = "goodReceiverMobile")
    private String dsGoodReceiverMobile;

    @ApiModelProperty(value = "收货人地址(完整)")
    private String goodReceiverWholeAddress;

    @ApiModelProperty(value = "收货人地址")
    private String goodReceiverAddress;

    @ApiModelProperty(value = "收货人地址")
    @PrivateData(handlerPolicy = HandlerPolicy.DECIPHER, decipherScene = DecipherScene.PLATFORM_ORDER_DETAIL, fieldName = "goodReceiverAddress")
    private String dsGoodReceiverAddress;

    @ApiModelProperty(value = "开单人账号")
    private String sellerNo;

    @ApiModelProperty(value = "开单人姓名")
    private String sellerName;

    @ApiModelProperty(value = "配送方式")
    @Converter(enumClass = DeliveryWayEnum.class, fieldName = "orderDeliveryWayName")
    private String orderDeliveryWay;

    @ApiModelProperty(value = "配送方式名称")
    private String orderDeliveryWayName;

    @ApiModelProperty(value = "粉丝端+分销店维度配送方式类型")
    @Converter(enumClass = DeliveryWayEnum.class, fieldName = "distributionOrderDeliveryWayName")
    private String distributionOrderDeliveryWay;

    @ApiModelProperty(value = "粉丝端+分销店维度配送方式类型名称")
    private String distributionOrderDeliveryWayName;

    @ApiModelProperty(value = "订单总金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal productAmount;

    @ApiModelProperty(value = "订单应付金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal shouldAmount;

    @ApiModelProperty(value = "订单实付金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal realAmount;

    @ApiModelProperty(value = "订单欠款金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal arrearsAmount;

    @ApiModelProperty(value = "是否欠款 1 否 2 是")
    private Integer arrearsFlag;

    @ApiModelProperty(value = "订单渠道来源")
    @Converter(enumClass = OrderChannelSourceEnum.class, fieldName = "orderChannelSourceName", enumField = "type")
    private String orderChannelSource;

    @ApiModelProperty(value = "订单渠道来源名称")
    private String orderChannelSourceName;

    @ApiModelProperty(value = "收货人国家code")
    private String goodReceiverCountryCode;

    @ApiModelProperty(value = "收货人省份code")
    private String goodReceiverProvinceCode;

    @ApiModelProperty(value = "收货人省份")
    private String goodReceiverProvince;

    @ApiModelProperty(value = "收货人城市code")
    private String goodReceiverCityCode;

    @ApiModelProperty(value = "收货人城市")
    private String goodReceiverCity;

    @ApiModelProperty(value = "收货人地区code")
    private String goodReceiverCountyCode;

    @ApiModelProperty(value = "收货人地区")
    private String goodReceiverCounty;

    @ApiModelProperty(value = "收货人四级区域code")
    private String goodReceiverAreaCode;

    @ApiModelProperty(value = "收货人四级区域")
    private String goodReceiverArea;

    @ApiModelProperty(value = "商品名称")
    private String goodsNameConcat;

    @ApiModelProperty(value = "商户订单号")
    private String outTradeNoConcat;

    /**
     * 1000 网店支付
     * 1001 到店支付
     */
    @ApiModelProperty(value = "支付方式")
    private String orderPaymentMethod;

    @ApiModelProperty(value = "订单商品总件数")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal orderTotalNum;

    @ApiModelProperty(value = "订单标识，1 普通，2 云池，3 分销，9 中奖 字典OrderFlagEnum")
    @Converter(enumClass = OrderFlagEnum.class, fieldName = "orderFlagName")
    private Integer orderFlag;

    @ApiModelProperty(value = "订单标识名称")
    private String orderFlagName;

    @ApiModelProperty(value = "券是否自用 1否 2是")
    private Integer selfUseFlag;

    @ApiModelProperty("平台店铺订单标识，1 平台单 2 店铺单, 字典WhetherEnum")
    private Integer headFlag;

    @ApiModelProperty(value = "售后标签")
    private String returnLabel;

    @ApiModelProperty(value = "可发起售后 1000：售后中，不显示发起售后按钮")
    private String returnButtonDisplay;

    @ApiModelProperty(value = "自定义自提点编码")
    private String customAddressNo;

    @ApiModelProperty(value = "自定义自提点名称")
    private String customAddressName;

}
