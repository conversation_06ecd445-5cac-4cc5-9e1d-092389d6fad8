package cn.htdt.app.platform.dto.response.goods;


import cn.htdt.app.platform.converter.base.LocalDateTimeConverter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 *
 * 功能描述: 商品列表导出dto(店铺商品/单店商品)
 * @author: 邢会强
 *

 *
 * 商品单位 销售渠道
 * 202503 东启
 *
 */
@Data
public class ResStorePlatformGoodsExcelDTO implements Serializable {

    /**
     * 商品状态value
     */
    @ExcelProperty(value = "商品状态",index = 0)
    @ColumnWidth(10)
    private String goodsStatusValue;

    /**
     * 商品主图url
     */
    @ExcelProperty(value = "商品主图",index = 1)
    @ColumnWidth(10)
    private String mainPictureUrl;

    /**
     * 商品编号，包含普通商品、套餐商品等所有商品
     */
    @ExcelProperty(value = "商品ID",index = 2)
    @ColumnWidth(15)
    private String goodsNo;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称",index = 3)
    @ColumnWidth(30)
    private String goodsName;
    /**
     * 销售渠道
     */
    @ExcelProperty(value = "销售渠道",index = 4)
    @ColumnWidth(30)
    private String channelSalesName;

    /**
     * 商品类目名称
     */
    @ExcelProperty(value = "商品类目",index = 5)
    @ColumnWidth(15)
    private String categoryName;
    /**
     * 商品品牌名称
     */
    @ExcelProperty(value = "商品品牌",index = 6)
    @ColumnWidth(10)
    private String brandName;
    /**
     * 零售价
     */
    @ExcelProperty(value = "零售价",index = 7)
    @ColumnWidth(10)
    private BigDecimal retailPrice;

    /**
     * 条形码
     */
    @ExcelProperty(value = "商品条形码",index = 8)
    @ColumnWidth(15)
    private String barcode;

    /**
     * 商品来源类型1001-平台自建;1002-商家自建;1003-店铺自建;1004:一键代发;
     */
    @ExcelProperty(value = "商品来源",index = 9)
    @ColumnWidth(10)
    private String goodsSourceTypeValue;
    /**
     * 店铺类目名称
     */
    @ExcelProperty(value = "店铺类目名称",index = 10)
    @ColumnWidth(10)
    private String storeCategoryName;
    /**
     * 是否入仓:1-否;2-是;
     */
    @ExcelProperty(value = "是否入仓",index = 11)
    @ColumnWidth(10)
    private String warehouseFlagValue;

    /**
     * 可售库存
     */
    @ExcelProperty(value = "可售库存",index = 12)
    @ColumnWidth(10)
    private Integer canSaleStockNum;

    /**
     * 实际库存
     */
    @ExcelProperty(value = "实际库存",index = 13)
    @ColumnWidth(10)
    private Integer realStockNum;


    /**
     * 操作人
     */
    @ExcelProperty(value = "最后操作人",index = 14)
    @ColumnWidth(10)
    private String modifyName;

    @ExcelProperty(value = "更新时间",index = 15,converter = LocalDateTimeConverter.class)
    @ColumnWidth(10)
    private LocalDateTime modifyTime;

    /**
     * 商品助记码
     */
    @ExcelProperty(value = "助记码",index = 16)
    @ColumnWidth(10)
    private String goodsHelpCode;

    /**
     * 计量单位名称
     */
    @ExcelProperty(value = "计量单位",index = 17)
    @ColumnWidth(10)
    private String calculationUnitName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
