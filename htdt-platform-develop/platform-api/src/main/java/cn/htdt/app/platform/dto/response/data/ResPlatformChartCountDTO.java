package cn.htdt.app.platform.dto.response.data;

import cn.htdt.common.dto.BigDecimalSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-07-10
 * @description 统计响应DTO
 **/
@Data
public class ResPlatformChartCountDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("订单金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal orderAmount = BigDecimal.ZERO;

    @ApiModelProperty("订单笔数")
    private Integer orderNum;

    @ApiModelProperty("退款金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal returnOrderAmount = BigDecimal.ZERO;

    @ApiModelProperty("退款笔数")
    private Integer returnOrderNum;

    private List<ResPlatformChartItemCountDTO> resPlatformChartItemCountDTOList;
}


