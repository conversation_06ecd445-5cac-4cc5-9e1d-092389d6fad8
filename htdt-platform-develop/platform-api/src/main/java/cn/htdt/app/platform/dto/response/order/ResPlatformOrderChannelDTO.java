package cn.htdt.app.platform.dto.response.order;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.enums.OrderChannelSourceEnum;
import cn.htdt.common.enums.OrderStatusEnum;
import cn.htdt.common.utils.excel.dto.RowRangeDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单渠道核销数据
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ResPlatformOrderChannelDTO extends RowRangeDto {

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "拼接商品编号")
    private String goodsNoConcat;

    @ApiModelProperty(value = "拼接商品名称")
    private String goodsNameConcat;

    @ApiModelProperty(value = "下单日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime orderCreateTime;

    @ApiModelProperty(value = "下单人账号")
    private String buyerMobile;

    @ApiModelProperty("下单人联系号码加密")
    private String dsBuyerMobile;

    @ApiModelProperty(value = "商品总金额")
    private BigDecimal goodsTotalAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "核销渠道")
    private String extendChannelName;

    @ApiModelProperty(value = "核销金额")
    private BigDecimal amountSharePromotion = BigDecimal.ZERO;

    @ApiModelProperty(value = "其他优惠")
    private BigDecimal otherAmountSharePromotion = BigDecimal.ZERO;

    @ApiModelProperty(value = "订单实付款")
    private BigDecimal realAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "订单状态 1010:待支付  1030:待确认 1050:待发货 1060:待收货  1061:部分发货  1999:交易成功  9000:交易关闭")
    @Converter(enumClass = OrderStatusEnum.class, fieldName = "orderStatusName")
    private String orderStatus;

    @ApiModelProperty(value = "订单状态描述")
    private String orderStatusName;

    @ApiModelProperty(value = "订单渠道来源")
    @Converter(enumClass = OrderChannelSourceEnum.class, fieldName = "orderChannelSourceName", enumField = "type")
    private String orderChannelSource;

    @ApiModelProperty(value = "订单渠道来源名称")
    private String orderChannelSourceName;

}
