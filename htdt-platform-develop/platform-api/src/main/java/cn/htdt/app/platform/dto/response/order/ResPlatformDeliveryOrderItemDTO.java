package cn.htdt.app.platform.dto.response.order;

import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 批量发货导出使用
 *
 * <AUTHOR>
 */
@Data
public class ResPlatformDeliveryOrderItemDTO implements Serializable {

    private static final long serialVersionUID = 3881047742613519924L;


    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单行序号
     */
    private String itemNo;

    /**
     * 收货人姓名
     */
    private String goodReceiverName;

    /**
     * 收货人地址
     */
    private String goodReceiverAddress;

    /**
     * 收货人手机号
     */
    private String goodReceiverMobile;

    /**
     * 配送方式
     */
    private String orderDeliveryWay;

    /**
     * 商品编号
     */
    private String goodsNo;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品图片
     */
    private String goodsPicPath;

    /**
     * 商品购买数量
     */
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal goodsItemNum;

    /**
     * 待发货商品数量
     */
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal waitDeliveryNum;

    /**
     * 本次发货数量
     */
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal goodsNumShip;

    /**
     * 扩展信息（规格属性）
     */
    private String extInfo;

    /**
     * 订单标识，字典OrderFlagEnum
     */
    private Integer orderFlag;

    /**
     * 是否启用串码
     */
    private Integer imeiFlag;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 商品仓库列表
     */
    private List<ResPlatformDeliveryOrderItemDTO.GoodsWarehouse> goodsWarehouseList;

    /**
     * 商品仓库信息
     */
    @Data
    public class GoodsWarehouse implements Serializable {

        /**
         * 序列化
         */
        private static final long serialVersionUID = -5126001901462640729L;

        /**
         * 仓库编码
         */
        private String warehouseNo;

        /**
         * 仓库名称
         */
        private String warehouseName;
    }
}
