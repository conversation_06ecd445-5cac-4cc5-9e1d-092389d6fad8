package cn.htdt.app.platform.dto.response.data;

import cn.htdt.common.enums.constants.NumConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023-07-10
 * @description 统计响应DTO
 **/
@Data
public class ResPlatformChartItemCountDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("分组时间")
    private String groupTime;

    @ApiModelProperty("订单笔数")
    private Integer orderNum = NumConstant.ZERO;

    @ApiModelProperty("订单金额")
    private BigDecimal orderAmount = BigDecimal.ZERO;
}
