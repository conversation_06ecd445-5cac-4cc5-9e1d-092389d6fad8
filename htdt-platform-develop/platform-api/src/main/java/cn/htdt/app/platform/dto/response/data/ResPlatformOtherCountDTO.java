package cn.htdt.app.platform.dto.response.data;

import cn.htdt.common.dto.BigDecimalSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-07-10
 * @description 统计响应DTO
 **/
@Data
public class ResPlatformOtherCountDTO {

    @ApiModelProperty("开单渠道分布")
    private List<ResPlatformChannelCountDTO> orderChannelList;

    @ApiModelProperty("支付渠道分布")
    private List<ResPlatformChannelCountDTO> payChannelList;

    @ApiModelProperty("进店人次")
    private int browseStoreNum;

    @ApiModelProperty("进店粉丝")
    private int browseFanNum;

    @ApiModelProperty("成交粉丝")
    private int buyerNum;

    @ApiModelProperty("新增粉丝")
    private int fanFollowNum;

    @ApiModelProperty("商品采购数量")
    private int purchaseGoodsNum;

    @ApiModelProperty("商品采购金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal purchaseAmount = BigDecimal.ZERO;

    @ApiModelProperty("采购单数量")
    private int purchaseOrderNum;
}
