package cn.htdt.app.platform.dto.response.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 详细说明.图片基础类
 * <p>
 * Copyright: Copyright (c) 2020/9/28 9:41
 * <p>
 * Company: htd
 * <p>
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ResPlatformGoodsPictureDTO implements Serializable {

    private static final long serialVersionUID = -1780466296290881203L;

    @ApiModelProperty(value = "排序值")
    private Integer sortValue;

    @ApiModelProperty(value = "图片url")
    private String pictureUrl;

    @ApiModelProperty(value = "1、不是主图 2、是主图")
    private Integer mainPictureFlag;

}
