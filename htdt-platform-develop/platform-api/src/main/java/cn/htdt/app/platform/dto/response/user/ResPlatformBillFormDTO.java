package cn.htdt.app.platform.dto.response.user;

import cn.htdt.app.platform.dto.response.commonSetting.ResPlatformCommonSettingDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/11 10:22
 */
@Data
public class ResPlatformBillFormDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "设置值集合")
    private List<ResPlatformCommonSettingDTO> commonSettingDTOList;

    @ApiModelProperty(value = "模板内容json")
    private String templateJson;
}
