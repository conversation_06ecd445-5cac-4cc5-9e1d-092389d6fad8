package cn.htdt.app.platform.dto.response.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName ResPlatformAgentDTO
 * <AUTHOR>
 * @create 2023/7/10 13:58
 */
@Data
public class ResPlatformAgentDTO implements Serializable {
    /**
     * 代理人编号
     */
    @ApiModelProperty(value = "代理人编号")
    private String fanNo;

    /**
     * 代理人姓名
     */
    @ApiModelProperty(value = "代理人昵称")
    private String name;

    /**
     * 代理人手机号
     */
    @ApiModelProperty(value = "代理人手机号")
    private String phone;

    /**
     * 代理人头像
     */
    @ApiModelProperty(value = "代理人头像")
    private String headImg;

    /**
     * 代理人归属店铺
     */
    @ApiModelProperty(value = "代理人归属店铺")
    private String storeName;

    /**
     * 代理人归属商家
     */
    @ApiModelProperty(value = "代理人归属商家")
    private String merchantName;

    /**
     * 加入店铺时间
     */
    @ApiModelProperty(value = "加入店铺时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime createTime;

    /**
     * 代理人申请时间
     */
    @ApiModelProperty(value = "代理人申请时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime joinTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 首次加入店铺来源
     */
    @ApiModelProperty(value = "首次加入店铺来源")
    private String joinStoreSource;

    /**
     * "粉丝备注名称（店铺/商家）"
     */
    @ApiModelProperty(value = "备注名")
    private String storeFanName;
}
