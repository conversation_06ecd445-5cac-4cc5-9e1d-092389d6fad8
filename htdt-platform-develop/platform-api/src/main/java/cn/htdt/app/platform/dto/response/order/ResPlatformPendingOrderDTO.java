package cn.htdt.app.platform.dto.response.order;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021-8-3
 * @Description 订单待办响应DTO
 **/
@Data
public class ResPlatformPendingOrderDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("订单编号/售后单编号")
    private String no;

    @ApiModelProperty("下单人编号")
    private String buyerNo;

    @ApiModelProperty(value = "订单描述/售后单描述")
    private String message;

    @ApiModelProperty(value = "订单状态/售后单状态 1030订单待办-待确认 1050订单待办-待发货 1000售后单待办-待审核 1004售后单待办-待验货 1002售后单待办-退款失败")
    private String status;

    @ApiModelProperty("核销码")
    private String writeOffCode;

    @JsonIgnore
    private String orderNo;

    @JsonIgnore
    private String soReturnNo;

    @JsonIgnore
    private String orderStatus;

    @JsonIgnore
    private String returnStatus;

    @JsonIgnore
    private String refundmentStatus;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}