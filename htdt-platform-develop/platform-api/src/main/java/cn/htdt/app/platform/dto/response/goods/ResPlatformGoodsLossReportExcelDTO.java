package cn.htdt.app.platform.dto.response.goods;

import cn.htdt.app.platform.converter.base.LocalDateTimeConverter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 功能描述: 报损单列表导出dto
 *
 * @author: xm
 */
@Data
public class ResPlatformGoodsLossReportExcelDTO implements Serializable {

    /**
     * 报损单号
     */
    @ExcelProperty(value = "报损单号", index = 0)
    @ColumnWidth(15)
    private String lossReportNo;

    /**
     * 店铺名称
     */
    @ExcelProperty(value = "店铺名称", index = 1)
    @ColumnWidth(30)
    private String storeName;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称", index = 2)
    @ColumnWidth(30)
    private String concatGoodsName;

    /**
     * 报损数量
     */
    @ExcelProperty(value = "报损数量", index = 3)
    @ColumnWidth(10)
    private Integer lossReportTotalNum;

    /**
     * 报损金额
     */
    @ExcelProperty(value = "报损金额", index = 4)
    @ColumnWidth(15)
    private BigDecimal lossReportTotalAmount;

    /**
     * 报损人
     */
    @ExcelProperty(value = "报损人", index = 5)
    @ColumnWidth(20)
    private String createName;

    /**
     * 报损时间
     */
    @ExcelProperty(value = "报损时间", index = 6, converter = LocalDateTimeConverter.class)
    @ColumnWidth(10)
    private LocalDateTime createTime;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", index = 7)
    @ColumnWidth(10)
    private String reportStatusName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
