package cn.htdt.app.platform.dto.response.goods;


import cn.htdt.app.platform.converter.base.LocalDateTimeConverter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 *
 * 功能描述: 商品列表导出dto(平台/平台商品)
 * @author: 张宇
 * @date: 2020/9/11 14:51
 */
@Data
public class ResPlatformGoodsExcelDTO implements Serializable {
    /**
     * 商品编号，包含普通商品、套餐商品等所有商品
     */
    @ExcelProperty(value = "商品ID",index = 0)
    @ColumnWidth(15)
    private String goodsNo;

    /**
     * 商品主图url
     */
    @ExcelProperty(value = "商品主图",index = 1)
    @ColumnWidth(10)
    private String mainPictureUrl;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称",index = 2)
    @ColumnWidth(30)
    private String goodsName;

    /**
     * 商品类目名称
     */
    @ExcelProperty(value = "商品类目",index = 3)
    @ColumnWidth(15)
    private String categoryName;

    /**
     * 零售价
     */
    @ExcelProperty(value = "零售价",index = 4)
    @ColumnWidth(10)
    private BigDecimal retailPrice;
    /**
     * 采购价
     */
    @ExcelProperty(value = "采购价",index = 5)
    @ColumnWidth(10)
    private BigDecimal purchasePrice;

    /**
     * 可售库存
     */
    @ExcelProperty(value = "可售库存",index = 6)
    @ColumnWidth(10)
    private Integer canSaleStockNum;

    /**
     * 实际库存
     */
    @ExcelProperty(value = "实际库存",index = 7)
    @ColumnWidth(10)
    private Integer realStockNum;

    /**
     * 条形码
     */
    @ExcelProperty(value = "商品条形码",index = 8)
    @ColumnWidth(15)
    private String barcode;

    /**
     * 操作人
     */
    @ExcelProperty(value = "最后操作人",index = 9)
    @ColumnWidth(10)
    private String modifyName;

    @ExcelProperty(value = "更新时间",index = 10,converter = LocalDateTimeConverter.class)
    @ColumnWidth(10)
    private LocalDateTime modifyTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
