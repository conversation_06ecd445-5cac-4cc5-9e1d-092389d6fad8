package cn.htdt.app.platform.dto.response.order;

import cn.htdt.app.platform.dto.response.market.ResPlatformAgentRewardWriteOffDTO;
import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 为统一核销建的实体类
 */
@Data
public class ResPlatformWriteOffCodeDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = 1L;

    /**
     * 公共
     **/
    @ApiModelProperty("类型区分：1=门店商品自提；2=总部商品自提；3=门店奖品核销；4=平台奖品核销；5=代理人酬劳核销")
    private Integer type;

    @ApiModelProperty("门店商品自提")
    private ResPlatformOrderWriteOffDTO resOrderDTO;

    @ApiModelProperty("总部商品自提")
    private ResPlatformOrderWriteOffDTO resDistributionOrderDTO;

    @ApiModelProperty("代理人酬劳核销")
    private ResPlatformAgentRewardWriteOffDTO resAgentRewardDTO;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
