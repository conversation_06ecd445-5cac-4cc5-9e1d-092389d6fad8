package cn.htdt.app.platform.dto.response.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021-07-26
 * @description 进店粉丝数响应DTO
 **/
@Data
public class ResPlatformDetailFansBrowseStoreDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "进店粉丝数")
    private Integer browseFanCount;

    @ApiModelProperty(value = "环比%")
    private String roundRate;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
