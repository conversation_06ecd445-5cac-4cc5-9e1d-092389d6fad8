package cn.htdt.app.platform.dto.response.goods;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.enums.market.RewardStatusFlagEnum;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单详情-分销信息
 * <AUTHOR>
 * @date 2021/3/11
 */
@Data
public class ResPlatformDistributionGoodsDTO implements Serializable {
    @ApiModelProperty(value = "商品名称")
    @ColumnWidth(30)
    private String goodsName;

    @ApiModelProperty(value = "商品规格")
    private String extInfo;

    @ApiModelProperty(value = "商品金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal goodsItemShouldAmount;

    @ApiModelProperty(value = "酬劳状态，对应枚举RewardStatusFlagEnum")
    @Converter(enumClass = RewardStatusFlagEnum.class,fieldName = "rewardStatusStr",enumField = "type")
    private Integer rewardStatusFlag;

    @ApiModelProperty(value = "酬劳状态字符，对应枚举RewardStatusFlagEnum")
    private String rewardStatusStr;

    @ApiModelProperty(value = "代理人姓名")
    private String agentName;

    @ApiModelProperty(value = "代理人手机号")
    private String agentMobile;

    @ApiModelProperty(value = "代理人佣金")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal rewardValue;

    @ApiModelProperty(value = "分销店佣金")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal distributionRewardValue;

    @ApiModelProperty(value = "数科最终分账")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal htdtRewardValue;

    @ApiModelProperty(value = "数科净利润")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal platformNetProfit;

    @ApiModelProperty(value = "供货店分账")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal storeRewardValue;

    @ApiModelProperty(value = "供货店后返")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal returnSupplyShopMoney;

    @ApiModelProperty(value = "酬劳类型")
    private Integer rewardType;

    @ApiModelProperty(value = "酬劳内容名称")
    private String rewardName;

}
