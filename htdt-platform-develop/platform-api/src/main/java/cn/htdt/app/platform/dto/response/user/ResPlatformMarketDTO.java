package cn.htdt.app.platform.dto.response.user;

import cn.htdt.common.enums.constants.NumConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021-09-26
 * @description 营销分析详情活动列表响应DTO
 **/
@Data
public class ResPlatformMarketDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "秒杀 1否 2是")
    private Integer seckillFlag = NumConstant.ONE;

    @ApiModelProperty(value = "优惠券 1否 2是")
    private Integer couponFlag = NumConstant.ONE;

    @ApiModelProperty(value = "抽奖 1否 2是")
    private Integer drawFlag= NumConstant.ONE;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
