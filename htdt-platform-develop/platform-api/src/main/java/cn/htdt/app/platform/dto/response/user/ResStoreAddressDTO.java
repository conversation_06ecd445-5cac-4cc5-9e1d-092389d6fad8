package cn.htdt.app.platform.dto.response.user;

import cn.htdt.common.dto.annon.PrivateData;
import cn.htdt.common.dto.config.DecipherSceneHelper;
import cn.htdt.common.dto.enums.DecipherScene;
import cn.htdt.common.dto.enums.HandlerPolicy;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

import static cn.htdt.common.dto.enums.DecipherScene.COMMON;

@Data
public class ResStoreAddressDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(notes = "姓名")
    private String name;


    @ApiModelProperty(notes = "手机号")
    private String phone;

    @PrivateData(handlerPolicy = HandlerPolicy.DECIPHER, decipherScene= DecipherScene.COMMON, fieldName = "phone")
    @ApiModelProperty(notes = "手机号-加密")
    private String dsPhone;
    /**
     * 店铺编号
     */
    @ApiModelProperty(notes = "店铺编号")
    private String storeNo;

    /**
     * 店铺名称
     */
    @ApiModelProperty(notes = "店铺名称")
    private String storeName;

    /**
     * 省编号
     */
    @ApiModelProperty(notes = "省编号")
    private String provinceCode;

    /**
     * 省名
     */
    @ApiModelProperty(notes = "省名")
    private String provinceName;

    /**
     * 市编号
     */
    @ApiModelProperty(notes = "市编号")
    private String cityCode;

    /**
     * 市名
     */
    @ApiModelProperty(notes = "市名")
    private String cityName;

    /**
     * 区域编号
     */
    @ApiModelProperty(notes = "区域编号")
    private String regionCode;

    /**
     * 区域名称
     */
    @ApiModelProperty(notes = "区域名称")
    private String regionName;

    /**
     * 详细地址
     */

    @ApiModelProperty(notes = "详细地址")
    private String detailAddress;

    @PrivateData(handlerPolicy = HandlerPolicy.DECIPHER, decipherScene= DecipherScene.COMMON, fieldName = "detailAddress")
    @ApiModelProperty(notes = "详细地址-加密")
    private String dsDetailAddress;
    /**
     * 街道编号
     */
    @ApiModelProperty(notes = "街道编号")
    private String streetCode;

    /**
     * 街道名称
     */
    @ApiModelProperty(notes = "街道名称")
    private String streetName;

    /**
     * 地址编号
     */
    @ApiModelProperty(notes = "地址编号")
    private String addressNo;

    /**
     * 地址类型 0 退货地址 1自提地址
     */
    @ApiModelProperty(notes = "地址类型 0 退货地址 1门店自提地址2自定义自提地址")
    private Integer type;

    @ApiModelProperty(
            notes = "自定义自提地址名称"
    )
    private String customAddressName;
}
