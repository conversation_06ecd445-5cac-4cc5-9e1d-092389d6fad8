package cn.htdt.app.platform.dto.response.order;

import cn.htdt.app.platform.converter.DeliveryTypeConverter;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单明细导出
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ResPlatformOrderItemExcelDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = -2023630089365344104L;

    @ExcelProperty(value = "*订单编号", index = 0)
    @ColumnWidth(30)
    @HeadFontStyle(color = 10)
    private String orderNo;

    @ExcelProperty(value = "*订单行序号", index = 1)
    @ColumnWidth(0)
    @HeadFontStyle(color = 10)
    private String itemNo;

    @ExcelProperty(value = "*收货人", index = 2)
    @ColumnWidth(20)
    @HeadFontStyle(color = 10)
    private String goodReceiverName;

    @ExcelProperty(value = "*收货人手机号", index = 3)
    @ColumnWidth(20)
    @HeadFontStyle(color = 10)
    private String goodReceiverMobile;

    @ExcelProperty(value = "*收货地址", index = 4)
    @ColumnWidth(20)
    @HeadFontStyle(color = 10)
    private String goodReceiverAddress;

    @ExcelProperty(value = "*商品编号", index = 5)
    @ColumnWidth(30)
    @HeadFontStyle(color = 10)
    private String goodsNo;

    @ExcelProperty(value = "*商品名称", index = 6)
    @ColumnWidth(20)
    @HeadFontStyle(color = 10)
    private String goodsName;

    @ExcelProperty(value = "规格属性", index = 7)
    @ColumnWidth(20)
    @HeadFontStyle
    private String extInfo;

    @ExcelProperty(value = "*待发货数量", index = 8)
    @ColumnWidth(20)
    @HeadFontStyle(color = 10)
    private BigDecimal waitDeliveryNum;

    @ExcelProperty(value = "*本次发货数量", index = 9)
    @ColumnWidth(20)
    @HeadFontStyle(color = 10)
    private BigDecimal goodsNumShip;

    @ExcelProperty(value = "*配送方式", index = 10, converter = DeliveryTypeConverter.class)
    @ColumnWidth(20)
    @HeadFontStyle(color = 10)
    private String deliveryType;

    @ExcelProperty(value = "物流公司", index = 11)
    @ColumnWidth(20)
    @HeadFontStyle
    private String expressDeliveryName;

    @ExcelProperty(value = "物流单号", index = 12)
    @ColumnWidth(20)
    @HeadFontStyle
    private String deliveryNumber;

    @ExcelProperty(value = "发货仓库", index = 13)
    @ColumnWidth(20)
    @HeadFontStyle
    private String warehouseName;

    @ExcelProperty(value = "商品串码(多个以“,”分隔)", index = 14)
    @ColumnWidth(30)
    @HeadFontStyle
    private String imei;

    @ExcelProperty(value = "错误信息", index = 15)
    @ExcelIgnore
    @ColumnWidth(20)
    @HeadFontStyle
    private String errorMsg;

    /**
     * 物流公司编码
     */
    @ExcelIgnore
    @HeadFontStyle
    private String expressDeliveryCode;
}
