package cn.htdt.app.platform.dto.response.goods;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 *
 * 功能描述: 商品响应dto
 * @author: 张宇
 * @date: 2020/9/11 14:51
 */
@Data
public class ResPlatformPageGoodsDTO implements Serializable {

    @ApiModelProperty(value = "商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "商品形式value")
    private String goodsFormValue;

    @ApiModelProperty(value = "商品形式 1001-普通商品;1002-系列商品;")
    private String goodsForm;

    @ApiModelProperty(value = "商品类型value")
    private String goodsTypeValue;

    @ApiModelProperty(value = "商品类型 1001-实物商品;1002-虚拟商品;1003-服务商品（仅供平台使用）;1004-称重商品")
    private String goodsType;

    @ApiModelProperty(value = "商品主图")
    private String mainPictureUrl;

    @ApiModelProperty(value = "商品来源类型值")
    private String goodsSourceTypeValue;
    @ApiModelProperty(value = "商品来源类型1001-平台自建;1002-商家自建;1003-店铺自建;1004:一键代发;1005:商家分发")
    private String goodsSourceType;

    @ApiModelProperty(value = "商品状态value")
    private String goodsShowStatusValue;

    @ApiModelProperty(value = "商品展示状态1001-未上架;1002-已上架;2002-审核中;2003-审核成功;2004-审核失败;3001:未分发;3003:分发成功;3004:已失效;")
    private String goodsShowStatus;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品类目名称")
    private String categoryName;

    @ApiModelProperty(value = "商品品牌名称")
    private String brandName;

    @ApiModelProperty(value = "零售价")
    private BigDecimal retailPrice;

    @ApiModelProperty(value = "采购价")
    private BigDecimal purchasePrice;

    @ApiModelProperty(value = "限价")
    private BigDecimal limitPrice;

    @ApiModelProperty(value = "是否入仓值")
    private String warehouseFlagValue;

    @ApiModelProperty(value = "是否入仓:1-否;2-是;")
    private Integer warehouseFlag;

    /**
     * 商品组是否入仓:1-否;2-是;（商品组）
     */
    @ApiModelProperty(value = "商品组是否入仓:1-否;2-是;")
    private Integer warehouseFlagOfGoodsGroups;


    @ApiModelProperty(value = "可售库存")
    private BigDecimal canSaleStockNum;

    @ApiModelProperty(value = "实际库存")
    private BigDecimal realStockNum;

    @ApiModelProperty(value = "关联仓库")
    private String collectWarehouseName;

    @ApiModelProperty(value = "条形码")
    private String barcode;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "门店编号")
    private String storeNo;

    @ApiModelProperty(value = "操作人名称")
    private String modifyName;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime modifyTime;

    @ApiModelProperty(value = "计量单位名称")
    private String calculationUnitName;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "商品助记码")
    private String goodsHelpCode;

    @ApiModelProperty(value = "已分发店铺")
    private String distributionStoreNames;

    @ApiModelProperty(value = "是否启用串码(1:否 2:是)")
    private Integer imeiFlag;

    @ApiModelProperty(value = "第一属性值名称")
    private String firstAttributeValueName;

    @ApiModelProperty(value = "第二属性值名称")
    private String secondAttributeValueName;

    @ApiModelProperty(value = "第三属性值名称")
    private String thirdAttributeValueName;

    @ApiModelProperty(value = "系列商品")
    private ResPlatformSeriesGoodsDTO seriesGoods;

    @ApiModelProperty(value = "仓库信息")
    private List<ResPlatformWarehouseGoodsRelationDTO> warehouseList;

    // 20230928蛋品 lixiang  商品管理 多单位商品
    @ApiModelProperty(value = "多单位商品类型 1001:主单位商品 1002:子")
    private String multiUnitType;

    @ApiModelProperty(value = "多单位主品编号")
    private String multiUnitGoodsNo;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
