package cn.htdt.app.platform.dto.response.data;

import cn.htdt.common.dto.BigDecimalSerialize;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-07-10
 * @description 统计响应DTO
 **/
@Data
public class ResPlatformOutlineCountDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty("成交订单金额")
    private BigDecimal shouldAmount = BigDecimal.ZERO;


    @ApiModelProperty("增值收入")
    private BigDecimal valueAddAmount = BigDecimal.ZERO;

    @ApiModelProperty("今日橙豆收益")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal coinPayAmount = BigDecimal.ZERO;

    @ApiModelProperty("营业额总金额")
    private BigDecimal totalAmount = BigDecimal.ZERO;

    @ApiModelProperty("比值1")
    private BigDecimal compareOne = BigDecimal.ZERO;

    @ApiModelProperty("比值2")
    private BigDecimal compareTwo = BigDecimal.ZERO;

    @ApiModelProperty("今日成交订单数(总部分销)")
    private int distributionOrderNum;

    @ApiModelProperty("今日获得佣金(总部分销)")
    private BigDecimal distributionYjNum = BigDecimal.ZERO;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime = LocalDateTime.now();




}
