package cn.htdt.app.platform.dto.response.user;

import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.dto.annon.PrivateData;
import cn.htdt.common.dto.enums.DecipherScene;
import cn.htdt.common.dto.enums.HandlerPolicy;
import cn.htdt.common.enums.constants.NumConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class ResPlatformFancStoreInfoDTO implements Serializable {
    private static final long serialVersionUID = 6511682328446059364L;

    /**
     * id
     */
    private Long id;
    /**
     * 编号
     */
    private String fanNo;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别 0 男 1 女 2保密
     */
    private Integer sex;

    /**
     * 电话
     */
    private String phone;

    /**
     * 电话-密文
     */
    @PrivateData(handlerPolicy = HandlerPolicy.DECIPHER, decipherScene = DecipherScene.COMMON, fieldName = "phone")
    private String dsPhone;

    /**
     * 出生日期
     */
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private LocalDate birthday;

    /**
     * 微信昵称
     */
    private String nickName;

    /**
     * 粉丝备注名称（店铺）
     */
    private String storeFanName;

    /**
     * 头像地址
     */
    private String headImg;
    /**
     * 首次加入店铺来源
     */
    private String joinStoreSource;

    /**
     * 首次加入店铺时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime joinStoreTime;
    /**
     * 状态 1粉丝 2代理人
     */
    private Integer type;


    /**
     * 省id
     */
    private String provinceCode;

    /**
     * 省名
     */
    private String provinceName;

    /**
     * 市id
     */
    private String cityCode;

    /**
     * 市名
     */
    private String cityName;

    /**
     * 区id
     */
    private String regionCode;

    /**
     * 区名
     */
    private String regionName;

    /**
     * 收货地址
     */
    private String detailAddress;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 店铺粉丝展示后续详细地址密文 在应用层解密
     **/
    private String dsSubsequentAddress;

    /**
     * 街道code
     */
    private String streetCode;

    /**
     * 街道名称
     */
    private String streetName;

    private Integer homeNum;

    private String groupName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 粉丝分组list
     */
    private List<ResPlatformFancGroupDTO> resFancGroupDTOList;

    /**
     * 店铺分组list
     */
    private List<ResPlatformFancGroupDTO> resFansStoreDTOList;

    /**
     * 归属店铺名称list
     */
    private List<String> ascriptionStoreList;

    /**
     * 粉丝积分
     */
    private Integer fansPoint = NumConstant.ZERO;

    /**
     * 粉丝可用券数量
     */
    private Integer couponNum = NumConstant.ZERO;

    /**
     * 橙豆余额
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal totalCoin = BigDecimal.ZERO;

    /**
     * 其他备注
     */
    private String otherRemarks;
    /**
     * 会员有效开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDateTime memberEffectiveTime;
    /**
     * 会员结束开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDateTime memberInvalidTime;
    /**
     * 是否关注：0-否，1-是
     */
    private Integer followFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
