package cn.htdt.app.platform.dto.response.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021-08-05
 * @description 待办数量统计响应DTO
 **/
@Data
public class ResPlatformPendingCountDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "销售单-待确认")
    private Long totalWaitingConfirmOrderCount;

    @ApiModelProperty(value = "销售单-待发货")
    private Long totalWaitingSendOrderCount;

    @ApiModelProperty(value = "售后单-待审核")
    private Long totalWaitingCheckReturnOrderCount;

    @ApiModelProperty(value = "售后单-待验货")
    private Long totalWaitingInspectionReturnOrderCount;

    @ApiModelProperty(value = "售后单-退款失败")
    private Long totalRefundLoseReturnOrderCount;

    @ApiModelProperty(value = "门店商品-待核销")
    private Long totalStoreWriteOffCount;

    @ApiModelProperty(value = "平台商品-待核销")
    private Long totalDistributionWriteOffCount;

    @ApiModelProperty(value = "代理人酬劳-待核销")
    private Long totalRewardWriteOffCount;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
