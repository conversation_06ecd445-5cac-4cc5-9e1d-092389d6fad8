package cn.htdt.app.platform.dto.response.goods;


import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.enums.goods.GoodsFormEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 * 功能描述: 商品响应dto
 * @author: 张宇
 * @date: 2020/9/11 14:51
 */
@Data
public class ResPlatformGoodsPromotionDTO implements Serializable {

    @ApiModelProperty(value = "商品编号，包含普通商品、套餐商品等所有商品")
    private String goodsNo;

    @ApiModelProperty(value = "商品形式 1001-普通商品;1002-系列商品")
    @Converter(enumClass = GoodsFormEnum.class, fieldName = "goodsFormValue", enumField = "name")
    private String goodsForm;

    @ApiModelProperty(value = "商品形式value")
    private String goodsFormValue;

    @ApiModelProperty(value = "商品类目ID")
    private String categoryNo;

    @ApiModelProperty(value = "商品类目名称")
    private String categoryName;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品品牌ID")
    private String brandNo;

    @ApiModelProperty(value = "商品品牌名称")
    private String brandName;

    @ApiModelProperty(value = "商品主图")
    private String mainPictureUrl;

    @ApiModelProperty(value = "零售价")
    private BigDecimal retailPrice;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    @ApiModelProperty(value = "供货店铺编码")
    private String supplyStoreNo;

    @ApiModelProperty(value = "供应店铺名称")
    private String storeName;

    @ApiModelProperty(value = "可售库存")
    private BigDecimal canSaleStockNum;
    @ApiModelProperty(value = "实际库存")
    private BigDecimal realStockNum;

    @ApiModelProperty(value = "第一属性值编码")
    private String firstAttributeValueName;
    @ApiModelProperty(value = "第二属性值编码")
    private String secondAttributeValueName;
    @ApiModelProperty(value = "第三属性值编码")
    private String thirdAttributeValueName;


    @ApiModelProperty(value = "酬劳类型，1佣金 2汇金币 3礼品 4专享现金券 5服务券 6话费券，对应枚举RewardTypeEnum")
    private Integer rewardType;

    @ApiModelProperty(value = "佣金元 汇金币个")
    private BigDecimal yjOrHjb;

    @ApiModelProperty(value = "最小零售价")
    private BigDecimal minRetailPrice;
    @ApiModelProperty(value = "最大零售价")
    private BigDecimal maxRetailPrice;


    @ApiModelProperty(value = "系列商品类型 1001:主品 1002:子品")
    private String seriesType;

    @ApiModelProperty(value = "云池供货价")
    private BigDecimal cloudPoolSupplyPrice;
    @ApiModelProperty(value = "最小云池供货价")
    private BigDecimal minCloudPoolSupplyPrice;
    @ApiModelProperty(value = "最大云池供货价")
    private BigDecimal maxCloudPoolSupplyPrice;

    @ApiModelProperty(value = "云池零售价")
    private BigDecimal cloudPoolRetailPrice;
    @ApiModelProperty(value = "最小云池零售价")
    private BigDecimal minCloudPoolRetailPrice;
    @ApiModelProperty(value = "最大云池零售价")
    private BigDecimal maxCloudPoolRetailPrice;

    @ApiModelProperty(value = "云池市场价")
    private BigDecimal cloudPoolMarketPrice;
    @ApiModelProperty(value = "最小云池市场价")
    private BigDecimal minCloudPoolMarketPrice;
    @ApiModelProperty(value = "最大云池市场价")
    private BigDecimal maxCloudPoolMarketPrice;

    @ApiModelProperty(value = "代理人佣金")
    private BigDecimal agentCommission;
    @ApiModelProperty(value = "最小代理人佣金")
    private BigDecimal minAgentCommission;
    @ApiModelProperty(value = "最大代理人佣金")
    private BigDecimal maxAgentCommission;

    @ApiModelProperty(value = "分销店佣金")
    private BigDecimal distributionStoreCommission;
    @ApiModelProperty(value = "最小分销店佣金")
    private BigDecimal minDistributionStoreCommission;
    @ApiModelProperty(value = "最大分销店佣金")
    private BigDecimal maxDistributionStoreCommission;

    @ApiModelProperty(value = "平台服务费")
    private BigDecimal platformServiceCommission;
    @ApiModelProperty(value = "最小平台服务费")
    private BigDecimal minPlatformServiceCommission;
    @ApiModelProperty(value = "最大平台服务费")
    private BigDecimal maxPlatformServiceCommission;

    //20230928蛋品-吴鑫鑫-商品管理-商品创建
    @ApiModelProperty(value = "多单位商品类型 1001:主单位商品 1002:子")
    private String multiUnitType;

    //20230928蛋品-吴鑫鑫-商品管理-商品创建
    @ApiModelProperty(value = "多单位主品编号")
    private String multiUnitGoodsNo;

    @ApiModelProperty(value = "计量单位ID")
    private String calculationUnitNo;

    @ApiModelProperty(value = "计量单位名称")
    private String calculationUnitName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
