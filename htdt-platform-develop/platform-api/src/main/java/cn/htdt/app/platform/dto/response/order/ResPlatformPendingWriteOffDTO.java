package cn.htdt.app.platform.dto.response.order;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021-08-04
 * @description 粉丝提货待办响应DTO
 **/
@Data
public class ResPlatformPendingWriteOffDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("核销码")
    private String writeOffCode;

    @ApiModelProperty(value = "核销描述")
    private String message;

    @JsonIgnore
    private String orderNo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
