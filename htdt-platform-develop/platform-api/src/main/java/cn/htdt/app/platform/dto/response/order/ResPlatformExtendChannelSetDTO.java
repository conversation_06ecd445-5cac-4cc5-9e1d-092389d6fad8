package cn.htdt.app.platform.dto.response.order;

import cn.htdt.common.dto.request.ReqBaseDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 订单扩展渠道设置表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-02
 */
@Data
public class ResPlatformExtendChannelSetDTO extends ReqBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 扩展渠道编号
     */
    private String extendChannelCode;

    /**
     * 扩展渠道名称
     */
    private String extendChannelName;


}
