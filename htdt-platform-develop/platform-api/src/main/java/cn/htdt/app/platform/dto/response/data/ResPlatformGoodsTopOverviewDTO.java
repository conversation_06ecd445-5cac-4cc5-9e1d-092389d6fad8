package cn.htdt.app.platform.dto.response.data;

import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品概览
 *
 * <AUTHOR>
 */
@Data
public class ResPlatformGoodsTopOverviewDTO implements Serializable {

    private static final long serialVersionUID = -3617378143681705006L;

    @ApiModelProperty("销售数量")
    List<GoodsOverview> saleNumGoodsOverviews;

    @ApiModelProperty("销售金额")
    List<GoodsOverview> saleAmountGoodsOverviews;

    @ApiModelProperty("滞销商品")
    List<GoodsOverview> unsalableGoodsOverviews;


    @Data
    public static class GoodsOverview implements Serializable{

        private static final long serialVersionUID = -4715890208941933642L;

        @ApiModelProperty("商品名称")
        private String goodsName;

        @ApiModelProperty("销售数量")
        private Integer saleNum;

        @ApiModelProperty("销售金额")
        @JsonSerialize(using = BigDecimalSerializeNoZero.class)
        private BigDecimal saleAmount;
    }
}
