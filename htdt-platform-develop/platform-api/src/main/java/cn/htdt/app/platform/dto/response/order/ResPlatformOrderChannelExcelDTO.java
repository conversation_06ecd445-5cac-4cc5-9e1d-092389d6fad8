package cn.htdt.app.platform.dto.response.order;

import cn.htdt.app.platform.converter.OrderChannelSourceConverter;
import cn.htdt.app.platform.converter.OrderStatusConverter;
import cn.htdt.app.platform.converter.base.LocalDateTimeConverter;
import cn.htdt.common.utils.excel.dto.RowRangeDto;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单渠道核销导出
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ResPlatformOrderChannelExcelDTO extends RowRangeDto {

    @ExcelProperty(value = "订单编号", index = 0)
    @ColumnWidth(30)
    private String orderNo;

    @ExcelProperty(value = "商品名称", index = 1)
    @ColumnWidth(40)
    private String goodsNameConcat;

    @ExcelProperty(value = "下单日期", index = 2, converter = LocalDateTimeConverter.class)
    @ColumnWidth(20)
    private LocalDateTime orderCreateTime;

    @ExcelProperty(value = "下单人账号", index = 3)
    @ColumnWidth(20)
    private String buyerMobile;

    @ExcelProperty(value="商品总金额", index = 4)
    @ColumnWidth(20)
    private BigDecimal goodsTotalAmount = BigDecimal.ZERO;

    @ExcelProperty(value = "核销渠道", index = 5)
    @ColumnWidth(20)
    private String extendChannelName;

    @ExcelProperty(value = "核销金额", index = 6)
    @ColumnWidth(20)
    private BigDecimal amountSharePromotion = BigDecimal.ZERO;

    @ExcelProperty(value = "其他优惠", index = 7)
    @ColumnWidth(20)
    private BigDecimal otherAmountSharePromotion = BigDecimal.ZERO;

    @ExcelProperty(value = "订单实付款", index = 8)
    @ColumnWidth(20)
    private BigDecimal realAmount = BigDecimal.ZERO;

    @ExcelProperty(value = "订单状态", index = 9, converter = OrderStatusConverter.class)
    @ColumnWidth(20)
    private String orderStatus;

    @ExcelProperty(value = "渠道来源", index = 10, converter = OrderChannelSourceConverter.class)
    @ColumnWidth(20)
    private String orderChannelSource;

}
