package cn.htdt.app.platform.dto.response.brand;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 品牌查询响应DTO
 *
 * <AUTHOR>
 * @date 2020/9/29
 **/
@Data
public class ResPlatformBrandInfoDTO implements Serializable {
    private static final long serialVersionUID = -8723505154974098167L;

    /**
     * 品牌编码
     */
    @ApiModelProperty(value = "品牌编码")
    private String brandNo;

    /**
     * 品牌名称
     */
    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    /**
     * 英文名
     */
    @ApiModelProperty(value = "英文名")
    private String englishName;

    /**
     * 商标url
     */
    @ApiModelProperty(value = "商标url")
    private String trademarkUrl;

    /**
     * 数据来源类型(默认1002。1001:同步MDM、1002:平台自创、1003:商家自创、1004:店铺自创)
     */
    @ApiModelProperty(value = "数据来源类型(默认1002。1001:同步MDM、1002:平台自创、1003:商家自创、1004:店铺自创)")
    private String dataSourceType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
