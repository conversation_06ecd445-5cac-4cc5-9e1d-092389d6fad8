package cn.htdt.app.platform.dto.response.order;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import cn.htdt.common.enums.OrderChannelSourceEnum;
import cn.htdt.common.enums.OrderStatusEnum;
import cn.htdt.common.enums.OrderTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会员->粉丝管理-平台->订单记录DTO
 *
 * <AUTHOR>
 */
@Data
public class ResPlatformFanOrderDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = -3197889257683106301L;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "订单类型")
    @Converter(enumClass = OrderTypeEnum.class, fieldName = "orderTypeName", enumField = "type")
    private String orderType;

    @ApiModelProperty(value = "订单类型名称")
    private String orderTypeName;

    @ApiModelProperty(value = "订单状态")
    @Converter(enumClass = OrderStatusEnum.class, fieldName = "orderStatusName")
    private String orderStatus;

    @ApiModelProperty(value = "订单状态描述")
    private String orderStatusName;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime orderCreateTime;

    @ApiModelProperty(value = "订单总金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal productAmount;

    @ApiModelProperty(value = "订单应付金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal shouldAmount;

    @ApiModelProperty(value = "订单实付金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal realAmount;

    @ApiModelProperty(value = "订单渠道来源")
    @Converter(enumClass = OrderChannelSourceEnum.class, fieldName = "orderChannelSourceName", enumField = "type")
    private String orderChannelSource;

    @ApiModelProperty(value = "订单渠道来源名称")
    private String orderChannelSourceName;

    @ApiModelProperty(value = "商品名称")
    private String goodsNameConcat;

    @ApiModelProperty(value = "订单商品总件数")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal orderTotalNum;
}
