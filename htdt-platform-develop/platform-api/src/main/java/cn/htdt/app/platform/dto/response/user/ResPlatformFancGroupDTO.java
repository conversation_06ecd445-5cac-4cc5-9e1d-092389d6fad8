package cn.htdt.app.platform.dto.response.user;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

@Data
public class ResPlatformFancGroupDTO implements Serializable {

    private static final long serialVersionUID = -8146920055297853973L;
    /**
     * 粉丝编号
     */
    private String fanNo;

    /**
     * 分组编号
     */
    private String groupNumber;

    /**
     * 分组编号
     */
    private String groupName;


    /**
     * 创建人ID
     */
    private String createNo;

    /**
     * 最后一次修改人ID
     */
    private String modifyNo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
