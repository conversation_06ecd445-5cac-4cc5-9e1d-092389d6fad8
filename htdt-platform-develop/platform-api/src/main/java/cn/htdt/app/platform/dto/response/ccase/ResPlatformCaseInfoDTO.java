package cn.htdt.app.platform.dto.response.ccase;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 案例 响应实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-1
 */
@Data
@ApiModel(description = "案例返回实体类")
public class ResPlatformCaseInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(notes = "案例编号")
    private String caseNo;

    @ApiModelProperty(notes = "标题")
    private String title;

    @ApiModelProperty(notes = "封面图")
    private String coverUrl;

    @ApiModelProperty(notes = "正文内容")
    private String content;

}
