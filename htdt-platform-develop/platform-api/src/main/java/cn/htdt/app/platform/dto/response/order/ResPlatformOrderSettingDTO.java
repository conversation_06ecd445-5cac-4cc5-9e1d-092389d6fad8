package cn.htdt.app.platform.dto.response.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022-08-04
 * @Description 订单设置
 **/
@Data
public class ResPlatformOrderSettingDTO {

    @ApiModelProperty(value = "订单是否自动确认。默认2，1：否，2：是")
    private Integer orderConfirmFlag;

    @ApiModelProperty(value = "订单发货支持的物流方,0:全部;1:店铺配送;2:快递配送")
    private Integer logisticsType;

    @ApiModelProperty(value = "是否支持分开发货 1否2是")
    private Integer allowPartSend;

    @ApiModelProperty(value = "是否支持语音播报 1否2是")
    private Integer voiceFlag;
}