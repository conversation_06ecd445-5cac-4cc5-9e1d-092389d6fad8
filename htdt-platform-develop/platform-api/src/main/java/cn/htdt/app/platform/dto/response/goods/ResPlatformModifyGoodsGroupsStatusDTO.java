package cn.htdt.app.platform.dto.response.goods;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 *
 * 功能描述: 商品修改返回状态

 */
@Data
public class ResPlatformModifyGoodsGroupsStatusDTO implements Serializable {

    @ApiModelProperty(value = "商品组修改返回状态 1001：不需要提示 1002:商品基础信息已变更，商品会自动下架，是否重新申请上架？ 1003:商品基础信息已变更，分发至店铺中的该商品将自动失效（店铺商品状态为失效），是否重新分发？\n" +
            "1004:当前商品为有仓商品，若转为无仓商品，当前商品库存是否选择继承原库存？否则库存默认0\n" +
            "1005:当前商品为无仓商品，若转为有仓商品，当前商品库存自动扣减为0，请走“采购单”流程重新维护库存\n" +
            "1006:打开“商品分发”弹窗")
    private String goodsGroupsModifyStatus;

    @ApiModelProperty(value = "商品组修改后提示信息")
    private String goodsGroupsModifyMessage;
    @ApiModelProperty(value = "商品组编码")
    private String goodsGroupsNo;
    @ApiModelProperty(value = "商品组编码集合（批量更新使用）")
    private List<String> goodsGroupsNos;
    @ApiModelProperty(value = "商品组名称（批量使用）")
    private List<String> goodsGroupsNames;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
