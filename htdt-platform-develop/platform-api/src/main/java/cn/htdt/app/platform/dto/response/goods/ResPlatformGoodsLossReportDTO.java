package cn.htdt.app.platform.dto.response.goods;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 商品报损单表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ResPlatformGoodsLossReportDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "报损单号")
    private String lossReportNo;

    @ApiModelProperty(value = "报损备注")
    private String reportRemark;

    @ApiModelProperty(value = "报损单状态 11-待审批 12-审核不通过 13-已完成")
    private Integer reportStatus;

    @ApiModelProperty(value = "补偿方式 1-货物补偿 2-现金补偿 3-不补偿")
    private Integer compensateMethod;

    @ApiModelProperty(value = "补偿金额")
    private BigDecimal compensateAmount;

    @ApiModelProperty(value = "审核备注")
    private String reportApproveRemark;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "门店编号，对应orgId")
    private String storeNo;

    @ApiModelProperty(value = "店铺名称，对应orgName")
    private String storeName;

    @ApiModelProperty(value = "是否可用:默认2，1：不可用2:可用")
    private Integer disableFlag;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime modifyTime;
    @ApiModelProperty(value = "创建人编号")
    private String createNo;
    @ApiModelProperty(value = "创建人名称")
    private String createName;
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    @ApiModelProperty(value = "修改人编号")
    private String modifyNo;
    @ApiModelProperty(value = "修改人名称")
    private String modifyName;
    @ApiModelProperty(value = "删除标识")
    private Integer deleteFlag;
    /*******************以下扩展字段****************************/
    @ApiModelProperty(value = "商品名称（逗号隔开）-pc用")
    private String concatGoodsName;

    @ApiModelProperty(value = "商家同步商品编号（逗号隔开）-pc用")
    private String concatMerchantGoodsNo;

    @ApiModelProperty(value = "报损合计数量")
    private Integer lossReportTotalNum;

    @ApiModelProperty(value = "报损合计金额")
    private BigDecimal lossReportTotalAmount;

    @ApiModelProperty(value = "报损明细列表")
    private List<ResPlatformGoodsLossReportItemDTO> goodsLossReportItemDTOS;

    @ApiModelProperty(value = "报损图片列表")
    private List<ResPlatformGoodsLossReportPicDTO> goodsLossReportPicDTOS;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
