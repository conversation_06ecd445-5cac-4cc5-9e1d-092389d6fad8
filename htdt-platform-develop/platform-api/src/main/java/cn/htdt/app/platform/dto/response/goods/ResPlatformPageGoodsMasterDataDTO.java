package cn.htdt.app.platform.dto.response.goods;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.enums.goods.GoodsFormEnum;
import cn.htdt.common.enums.goods.GoodsSourceTypeEnum;
import cn.htdt.common.enums.goods.GoodsTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 *
 * 功能描述: 商品主数据响应dto
 * @author: 张宇
 * @date: 2020/12/21 14:51
 */
@Data
public class ResPlatformPageGoodsMasterDataDTO implements Serializable {

    @ApiModelProperty(value = "商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "商品形式value")
    private String goodsFormValue;

    @ApiModelProperty(value = "商品形式 1001-普通商品;1002-系列商品;")
    @Converter(enumClass = GoodsFormEnum.class, fieldName = "goodsFormValue", enumField = "name")
    private String goodsForm;

    @ApiModelProperty(value = "商品类型value")
    private String goodsTypeValue;

    @ApiModelProperty(value = "商品类型 1001-实物商品;1002-虚拟商品;1003-服务商品（仅供平台使用）;1004-称重商品")
    @Converter(enumClass = GoodsTypeEnum.class, fieldName = "goodsTypeValue", enumField = "name")
    private String goodsType;

    @ApiModelProperty(value = "商品来源类型值")
    private String goodsSourceTypeValue;

    @ApiModelProperty(value = "商品来源类型1001-平台自建;1002-商家自建;1003-店铺自建;1004:一键代发;1005:商家分发")
    @Converter(enumClass = GoodsSourceTypeEnum.class, fieldName = "goodsSourceTypeValue", enumField = "name")
    private String goodsSourceType;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品类目名称")
    private String categoryName;

    @ApiModelProperty(value = "商品品牌名称")
    private String brandName;

    @ApiModelProperty(value = "条形码")
    private String barcode;

    @ApiModelProperty(value = "商品主图")
    private String mainPictureUrl;

    @ApiModelProperty(value = "计量单位名称")
    private String calculationUnitName;

    @ApiModelProperty(value = "商品文描")
    private String content;

    @ApiModelProperty("类目名称全路径")
    private String fullNamePath;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
