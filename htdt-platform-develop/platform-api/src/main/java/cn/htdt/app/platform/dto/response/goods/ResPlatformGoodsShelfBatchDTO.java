package cn.htdt.app.platform.dto.response.goods;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-06-24
 * @description 效期-临期商品列表
 **/
@Data
public class ResPlatformGoodsShelfBatchDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "批次号")
    private String batchNo;

    @ApiModelProperty(value = "商品ID")
    private String goodsNo;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "是否入仓(1:否 2:是)")
    private Integer warehouseFlag;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseNo;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "入库时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "入库数量")
    private BigDecimal inStockNum;

    @ApiModelProperty(value = "剩余数量")
    private BigDecimal leftStockNum;

    @ApiModelProperty(value = "生产日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate productionDate;

    @ApiModelProperty(value = "保质期")
    private Integer qualityGuaranteePeriod;

    @ApiModelProperty(value = "保质期单位,day日,month月,year年")
    private String shelfLifeUnit;

    @ApiModelProperty(value = "到期日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate expirationDate;

    @ApiModelProperty(value = "剩余天数或者过期天数")
    private BigDecimal expiringDayNum;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}