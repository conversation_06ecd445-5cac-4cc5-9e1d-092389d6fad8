package cn.htdt.app.platform.dto.response.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2020-12-23
 * @Description 支付回调轮询出参
 **/
@Data
public class ResPlatformYjfNotifyPollingDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("实付金额")
    private String amount;

    @ApiModelProperty("支付状态")
    private String tradeStatus;

    @ApiModelProperty("支付成功与否编码")
    private String status;

    @ApiModelProperty("响应消息")
    private String resultMessage;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}