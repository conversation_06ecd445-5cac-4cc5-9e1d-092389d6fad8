package cn.htdt.app.platform.dto.response.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description 本店成交金额返回值
 * @Date 2021/7/26
 * @Param 
 * @return 
 **/
@Data
public class ResPlatformSoChannelAmountDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = 741087081635029349L;


    @ApiModelProperty("订单应收总金额")
    private BigDecimal shouldAmount = BigDecimal.ZERO;

    @ApiModelProperty("欠款金额")
    private BigDecimal arrearsAmount = BigDecimal.ZERO;

    @ApiModelProperty("现金支付")
    private BigDecimal cashPaymentAmount = BigDecimal.ZERO;

    @ApiModelProperty("线上支付")
    private BigDecimal onlinePaymentAmount = BigDecimal.ZERO;


    /**
     * 欠款老金额
     **/
    private BigDecimal arrearsOldAmount = BigDecimal.ZERO;

    /**
     * 现金老支付
     **/
    private BigDecimal cashPaymentOldAmount = BigDecimal.ZERO;

    /**
     * 线上老支付
     **/
    private BigDecimal onlinePaymentOldAmount = BigDecimal.ZERO;


    @ApiModelProperty("欠款金额比")
    private String arrearsAmountThan;

    @ApiModelProperty("现金支付比")
    private String cashPaymentAmountThan;

    @ApiModelProperty("线上支付比")
    private String onlinePaymentAmountThan;

}


