package cn.htdt.app.platform.dto.response.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021-07-26
 * @description 今日进店人次响应DTO
 **/
@Data
public class ResPlatformDetailBrowseStoreDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "线下门店%")
    private String offlineRate;

    @ApiModelProperty(value = "一体机%")
    private String otcRate;

    @ApiModelProperty(value = "汇享购%")
    private String hxgRate;

    @ApiModelProperty(value = "环比-线下门店%")
    private String roundOfflineRate;

    @ApiModelProperty(value = "环比-一体机%")
    private String roundOtcRate;

    @ApiModelProperty(value = "环比-汇享购%")
    private String roundHxgRate;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
