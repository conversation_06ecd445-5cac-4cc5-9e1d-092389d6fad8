package cn.htdt.app.platform.dto.response.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 代办统计响应DTO
 *
 * <AUTHOR>
 */
@Data
public class ResPendingCountExtendDTO implements Serializable {

    private static final long serialVersionUID = -5697035704615386826L;
    @ApiModelProperty("订单代办标题")
    private String title;

    @ApiModelProperty("订单代办统计数量")
    private Long count;

    @ApiModelProperty("跳转url")
    private String url;
}
