package cn.htdt.app.platform.dto.response.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description 本店成交订单数统计返回值
 * @Date 2021/7/26
 * @Param 
 * @return 
 **/
@Data
public class ResPlatformSoSourceDealDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = 741087081635029349L;

    /**
     * 订单渠道来源 字典ORDER_CHANNEL_SOURCE
     */
    private String orderChannelSource;

    /**
     * 对应的成交数量
     */
    @ApiModelProperty("订单成交总数")
    private int orderNum;

    @ApiModelProperty("门店订单数")
    private int storeOrderNum;

    @ApiModelProperty("汇享购订单数")
    private int hxgOrderNum;

    @ApiModelProperty("一体机订单数")
    private int integratedMachineOrderNum;


    /**
     * 门店订单老数
     **/
    private int storeOrderOldNum;

    /**
     * 汇享购订单老数
     **/
    private int hxgOrderOldNum;

    /**
     * 一体机订单老数
     **/
    private int integratedMachineOrderOldNum;


    @ApiModelProperty("门店订单比")
    private String storeOrderThan;

    @ApiModelProperty("汇享购订单比")
    private String hxgOrderThan;

    @ApiModelProperty("一体机订单比")
    private String integratedMachineOrderThan;

    /**
     * 应收订单金额
     */
    private BigDecimal shouldAmount;

}


