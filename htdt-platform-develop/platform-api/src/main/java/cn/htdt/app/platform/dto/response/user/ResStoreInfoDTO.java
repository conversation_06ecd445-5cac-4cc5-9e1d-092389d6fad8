package cn.htdt.app.platform.dto.response.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalTime;
import java.util.List;


/**
 * @Purpose 店铺信息响应实体类


 * 修改:新增字段 temporaryProductConfigStatus
 * 修改原因 新增临时商品配置功能
 * <AUTHOR>
 * @since 2025-03
 */
@Data
@ApiModel(description = "店铺信息响应实体类")
public class ResStoreInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(
            notes = "临时商品配置状态 0关闭,1开启"
    )
    private String temporaryProductConfigStatus;

    @ApiModelProperty(notes = "主键")
    private BigInteger id;

    @ApiModelProperty(notes = "店铺编号")
    private String storeNo;

    @ApiModelProperty(notes = "店铺名称")
    private String storeName;

    /**
     * 组织id
     */
    @ApiModelProperty(notes = "组织id")
    private String orgNo;

    /**
     * 组织编码
     */
    @ApiModelProperty(notes = "组织编码")
    private String orgCode;

    /**
     * 所属商家id
     */
    @ApiModelProperty(notes = "所属商家id")
    private String merchantNo;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    /**
     * 审核状态 0待审核,1通过,2不通过
     */
    @ApiModelProperty(notes = "审核状态 0待审核,1通过,2不通过")
    private Integer auditStatus;

    /**
     * 门店类型
     */
    @ApiModelProperty(notes = "门店类型")
    private String storeType;

    /**
     * 省id
     */
    @ApiModelProperty(notes = "省id")
    private String provinceCode;

    /**
     * 省名
     */
    @ApiModelProperty(notes = "省名")
    private String provinceName;

    /**
     * 市id
     */
    @ApiModelProperty(notes = "市id")
    private String cityCode;

    /**
     * 市名
     */
    @ApiModelProperty(notes = "市名")
    private String cityName;

    /**
     * 区id
     */
    @ApiModelProperty(notes = "区id")
    private String regionCode;

    /**
     * 区名
     */
    @ApiModelProperty(notes = "区名")
    private String regionName;

    /**
     * 详细地址
     */
    @ApiModelProperty(notes = "详细地址")
    private String detailAddress;

    /**
     * 详细地址
     */
    @ApiModelProperty(notes = "详细地址加密")
    private String dsDetailAddress;

    /**
     * 门店描述
     */
    @ApiModelProperty(notes = "门店描述")
    private String description;

    /**
     * 店铺的经度
     */
    @ApiModelProperty(notes = "店铺的经度")
    private BigDecimal longitude;

    /**
     * 店铺的纬度
     */
    @ApiModelProperty(notes = "店铺的纬度")
    private BigDecimal latitude;

    /**
     * 详细地址
     */
    @ApiModelProperty(notes = "详细地址")
    private String contactDetailAddress;

    /**
     * 店铺简介
     */
    @ApiModelProperty(notes = "店铺简介")
    private String shortDesc;

    /**
     * ；联系人email
     */
    @ApiModelProperty(notes = "联系人email")
    private String contactEmail;

    /**
     * 店铺状态 0停用，1启用
     */
    @ApiModelProperty(notes = "店铺状态 0停用，1启用")
    private Integer storeStatus;

    /**
     * 公司ID
     */
    @ApiModelProperty(notes = "公司ID")
    private String companyNo;

    /**
     * 创建人ID
     */
    @ApiModelProperty(notes = "创建人ID")
    private String createNo;

    /**
     * 最后一次修改人ID
     */
    @ApiModelProperty(notes = "最后一次修改人ID")
    private String modifyNo;

    /**
     * 街道code
     */
    @ApiModelProperty(notes = "街道code")
    private String streetCode;

    /**
     * 街道名称
     */
    @ApiModelProperty(notes = "街道名称")
    private String streetName;

    /**
     * 营业开始时间
     */
    @JsonFormat(pattern="HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(notes = "营业开始时间")
    private LocalTime businessStart;

    /**
     * 营业结束时间
     */
    @JsonFormat(pattern="HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(notes = "营业结束时间")
    private LocalTime businessEnd;

    /**
     * 店内照片url(多)
     */
    @ApiModelProperty(notes = "店内照片url(多)")
    private String inStorePhotos;

    /**
     * 门头照片url
     */
    @ApiModelProperty(notes = "门头照片url")
    private String storePhoto;

    /**
     * 证照照片url(多)
     */
    @ApiModelProperty(notes = "证照照片url(多)")
    private String licensePhotos;

    /**
     * 微信
     */
    @ApiModelProperty(notes = "微信")
    private String wx;

    /**
     * 微信二维码
     */
    @ApiModelProperty(notes = "微信二维码")
    private String wxCode;

    /**
     * 店铺微信二维码
     */
    @ApiModelProperty(notes = "店铺微信二维码")
    private String merchantWxCode;

    /**
     * 是否有仓库 0否 1是
     */
    @ApiModelProperty(notes = "是否有仓库 0否 1是")
    private Integer warehouseFlag;


    @ApiModelProperty(notes = "联系人姓名")
    private String contactName;

    @ApiModelProperty(notes = "联系人手机号")
    private String contactPhone;

    @ApiModelProperty(notes = "联系人手机号-加密")
    private String dsContactPhone;

    @ApiModelProperty(notes = "退换货地址信息")
    private ResStoreAddressDTO returnAddress;

    @ApiModelProperty(notes = "门店自提地址信息")
    private ResStoreAddressDTO  ztAddress;

    @ApiModelProperty(notes = "自定义自提地址信息")
    private List<ResStoreAddressDTO> customAddressList;

    @ApiModelProperty(notes = "店长编号")
    private String adminNo;

    @ApiModelProperty(notes = "店长姓名")
    private String adminName;

    @ApiModelProperty(notes = "店长手机号")
    private String adminPhone;

    @ApiModelProperty(notes = "店长手机号-加密")
    private String dsAdminPhone;

    @ApiModelProperty(notes = "多店状态 0否 1是")
    private Integer moreStore;

    @ApiModelProperty(notes = "店铺是否打烊 0否 1是")
    private Integer isShut;

    @ApiModelProperty(notes = "店铺是否有汇赚钱权限 1否 2是")
    private Integer privilegeFlag;

    @ApiModelProperty(notes = "是否首店 0否 1是")
    private Integer firstStoreFlag;

    @ApiModelProperty(value = "当前收款账户的会员编码")
    private String currentMerberCode;

    @ApiModelProperty(value = "当前收款账户的公司名称")
    private String currentCompanyName;

    @ApiModelProperty(value = "当前收款账户的支付账户")
    private String currentPayAccount;

    @ApiModelProperty(value = "是否是实体店铺 1-虚拟店铺 2-实体店铺")
    private Integer isPhysicalStore;

    @ApiModelProperty(value = "是否允许自定义商品价格 1-不允许 2-允许")
    private Integer allowCustomPrice;

    @ApiModelProperty("是否审核报损单，1=人工审核；2=自动审核通过")
    private Integer verifyLossReport;

    /**
     * 自定义标签
     */
    @ApiModelProperty(notes = "自定义标签，数组")
    private List<String> customTagName;


    /**
     * 排名标签类型（1：按省排名 2：按市排名 3：按区排名 4：排名不展示）
     */
    @ApiModelProperty(notes = "排名标签类型（1：按省排名 2：按市排名 3：按区排名 4：排名不展示）")
    private Integer rankTagType;

    @ApiModelProperty(notes = "排名值")
    private String rankValue;

    @ApiModelProperty(notes = "是否参与社群接龙活动 1：否 2：是")
    private Integer communitySolitaireFlag;

    @ApiModelProperty(notes = "是否会员共享：1否 2是")
    private Integer memberSharing;

    /**
     * 店铺经营模式, 1001-直营模式, 1002-加盟模式, 1003-联营模式, 参考枚举: StoreBusinessModelEnum
     */
    @ApiModelProperty(value = "店铺经营模式: 1001-直营模式, 1002-加盟模式, 1003-联营模式")
    private String businessModel;

    /**
     * 参考枚举: StoreRuleOptionsValueEnum
     */
    @ApiModelProperty(value = "要货单商品来源, 1001-商家同步的商品; 1002-和商家所有的商品")
    private String requisitionOrderGoodsSource;

    /**
     * 参考枚举: StoreRuleOptionsValueEnum
     */
    @ApiModelProperty(value = "要货单商家审核类型, 1001-人工审核; 1002-自动审核")
    private String requisitionOrderGoodsMerchantAudit;

    @ApiModelProperty(value = "自定义店铺编号")
    private String customStoreNo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
