package cn.htdt.app.platform.dto.response.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021-07-19
 * @description 微信小程序响应DTO
 **/
@Data
public class ResPlatformWeChatMiniProgramQRCodeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "店铺编号")
    private String storeNo;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "小程序名称")
    private String miniAppName;

    @ApiModelProperty(notes = "详细地址")
    private String detailAddress;

    @ApiModelProperty(notes = "店铺二维码")
    private String miniUrl;

    @ApiModelProperty(notes = "门头照片url")
    private String storePhoto;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
