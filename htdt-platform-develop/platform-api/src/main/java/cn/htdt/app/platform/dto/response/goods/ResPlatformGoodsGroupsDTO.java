package cn.htdt.app.platform.dto.response.goods;


import cn.htdt.goodsprocess.dto.response.goodsgroups.ResGoodsGroupsDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 *
 * 功能描述: 商品响应dto
 *
 */
@Data
public class ResPlatformGoodsGroupsDTO extends ResGoodsGroupsDTO implements Serializable {


    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime modifyTime;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
