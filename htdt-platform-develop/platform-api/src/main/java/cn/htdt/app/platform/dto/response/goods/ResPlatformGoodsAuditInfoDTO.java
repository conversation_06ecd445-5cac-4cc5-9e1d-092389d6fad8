package cn.htdt.app.platform.dto.response.goods;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品审核信息 响应实体
 *
 * <AUTHOR>
 * @date 2020/12/21
 **/
@Data
public class ResPlatformGoodsAuditInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商品审核状态 2001:等待审核;2002:审核中;2003:审核成功; 2004:审核失败", name = "auditStatus")
    private String auditStatus;

    @ApiModelProperty(value = "商品审核状态 Value值", name = "auditStatusValue")
    private String auditStatusValue;

    @ApiModelProperty(value = "审核原因", name = "auditMessage")
    private String auditMessage;

    @ApiModelProperty(value = "审核类型(1001:分发审核 1002:上架审核 1003:云池审核)'", name = "auditType")
    private String auditType;

    @ApiModelProperty(value = "审核类型 Value值", name = "auditTypeValue")
    private String auditTypeValue;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "门店编号")
    private String storeNo;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "商品编号，包含普通商品、套餐商品等所有商品")
    private String goodsNo;

    @ApiModelProperty(value = "商品形式 1001-普通商品;1002-系列商品")
    private String goodsForm;

    @ApiModelProperty(value = "商品形式 Value值")
    private String goodsFormValue;

    @ApiModelProperty(value = "商品类型 1001-实物商品;1002-虚拟商品;1003-服务商品（仅供平台使用）;1004-称重商品")
    private String goodsType;

    @ApiModelProperty(value = "商品类型value")
    private String goodsTypeValue;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品类目ID")
    private String categoryNo;

    @ApiModelProperty(value = "商品类目名称")
    private String categoryName;

    @ApiModelProperty(value = "商品品牌ID")
    private String brandNo;

    @ApiModelProperty(value = "商品品牌名称")
    private String brandName;

    @ApiModelProperty(value = "计量单位ID")
    private String calculationUnitNo;

    @ApiModelProperty(value = "计量单位名称")
    private String calculationUnitName;

    @ApiModelProperty(value = "采购价")
    private BigDecimal purchasePrice;

    @ApiModelProperty(value = "最小采购价")
    private BigDecimal minPurchasePrice;

    @ApiModelProperty(value = "最大采购价")
    private BigDecimal maxPurchasePrice;

    @ApiModelProperty(value = "零售价")
    private BigDecimal retailPrice;

    @ApiModelProperty(value = "最小零售价")
    private BigDecimal minRetailPrice;

    @ApiModelProperty(value = "最大零售价")
    private BigDecimal maxRetailPrice;

    @ApiModelProperty(value = "审核提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime auditTime;

    @ApiModelProperty(value = "审核人ID")
    private String auditUserNo;

    @ApiModelProperty(value = "审核人名称")
    private String auditUserName;

    @ApiModelProperty(value = "商品主图")
    private String mainPictureUrl;

    @ApiModelProperty(value = "商品来源 1001-平台自建;1002-商家自建;1003-店铺自建;1004:一键代发;")
    private String goodsSourceType;
    @ApiModelProperty(value = "商家账号")
    private String merberCode;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
