package cn.htdt.app.platform.dto.response.goods;

import cn.htdt.app.platform.converter.base.LocalDateConverter;
import cn.htdt.app.platform.converter.base.LocalDateTimeConverter;
import cn.htdt.app.platform.converter.base.WhetherConverter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 功能描述: 效期批次列表导出dto
 *
 * @author: 邢会强
 */
@Data
public class ResPlatformGoodsValidityPeriodExcelDTO implements Serializable {

    /**
     * 批次号
     */
    @ExcelProperty(value = "批次号", index = 0)
    @ColumnWidth(15)
    private String batchNo;

    /**
     * 商品ID
     */
    @ExcelProperty(value = "商品ID", index = 1)
    @ColumnWidth(15)
    private String goodsNo;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称", index = 2)
    @ColumnWidth(30)
    private String goodsName;

    /**
     * 入库时间
     */
    @ExcelProperty(value = "入库时间", index = 3, converter = LocalDateTimeConverter.class)
    @ColumnWidth(17)
    private LocalDateTime createTime;

    @ExcelProperty(value = "是否入仓", index = 4, converter = WhetherConverter.class)
    @ColumnWidth(10)
    private Integer warehouseFlag;

    @ExcelProperty(value = "仓库名称", index = 5)
    @ColumnWidth(30)
    private String warehouseName;

    /**
     * 入库数量
     */
    @ExcelProperty(value = "入库数量", index = 6)
    @ColumnWidth(10)
    private BigDecimal inStockNum;

    /**
     * 剩余数量
     */
    @ExcelProperty(value = "剩余数量", index = 7)
    @ColumnWidth(10)
    private BigDecimal leftStockNum;

    /**
     * 生产日期
     */
    @ExcelProperty(value = "生产日期", index = 8, converter = LocalDateConverter.class)
    @ColumnWidth(10)
    private LocalDate productionDate;

    /**
     * 保质期
     */
    @ExcelProperty(value = "保质期", index = 9)
    @ColumnWidth(10)
    private Integer qualityGuaranteePeriod;

    /**
     * 保质期单位,day日,month月,year年
     */
    @ExcelProperty(value = "保质期单位,day日,month月,year年", index = 10)
    @ColumnWidth(10)
    private String shelfLifeUnit;

    /**
     * 到期日期
     */
    @ExcelProperty(value = "到期日期", index = 11, converter = LocalDateConverter.class)
    @ColumnWidth(10)
    private LocalDate expirationDate;

    /**
     * 剩余天数或者过期天数
     */
    @ExcelProperty(value = "剩余天数或者过期天数", index = 12)
    @ColumnWidth(10)
    private BigDecimal expiringDayNum;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
