package cn.htdt.app.platform.dto.response.goods;

import cn.htdt.app.platform.converter.base.LocalDateTimeConverter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/7/8 16:48
 */
@Data
public class ResPlatformCloudPoolGoodsExcelDTO {

    @ExcelProperty(value = "商品ID",index = 0)
    @ColumnWidth(15)
    private String goodsNo;

    @ExcelProperty(value = "商品名称",index = 1)
    @ColumnWidth(30)
    private String goodsName;

    @ExcelProperty(value = "销售类目",index = 2)
    @ColumnWidth(15)
    private String categoryName;

    @ExcelProperty(value = "商品品牌",index = 3)
    @ColumnWidth(15)
    private String brandName;

    @ExcelProperty(value = "商品可售库存",index = 4)
    @ColumnWidth(10)
    private BigDecimal canSaleStockNum;

    @ExcelProperty(value = "计量单位",index = 5)
    @ColumnWidth(10)
    private String calculationUnitName;

    @ExcelProperty(value = "云卖货售价（元）",index = 6)
    @ColumnWidth(10)
    private String cloudPoolRetailPrice;

    @ExcelProperty(value = "云卖货市场价（元）",index = 7)
    @ColumnWidth(10)
    private String cloudPoolMarketPrice;

    @ExcelProperty(value = "云卖货供货价（元）",index = 8)
    @ColumnWidth(10)
    private String cloudPoolSupplyPrice;

    @ExcelProperty(value = "商品形式",index = 9)
    @ColumnWidth(15)
    private String goodsFormValue;

    @ExcelProperty(value = "商品类型",index = 10)
    @ColumnWidth(15)
    private String goodsTypeValue;

    @ExcelProperty(value = "云卖货状态",index = 11)
    @ColumnWidth(10)
    private String cloudPoolGoodsStatusValue;

    @ExcelProperty(value = "售卖状态",index = 12)
    @ColumnWidth(10)
    private String goodsSaleStatusValue;

    @ExcelProperty(value = "供货店铺ID",index = 13)
    @ColumnWidth(15)
    private String storeNo;

    @ExcelProperty(value = "供货店铺名称",index = 14)
    @ColumnWidth(30)
    private String storeName;

    @ExcelProperty(value = "代理人佣金情况",index = 15)
    @ColumnWidth(15)
    private String agentCommissionRatio;

    @ExcelProperty(value = "分销店佣金情况",index = 16)
    @ColumnWidth(15)
    private String distributionStoreCommissionRatio;

    @ExcelProperty(value = "平台服务费",index = 17)
    @ColumnWidth(10)
    private BigDecimal platformServiceCommission;

    @ExcelProperty(value = "分销门店数",index = 18)
    @ColumnWidth(10)
    private String distributionStoreTotal;

    @ExcelProperty(value = "商品总销量",index = 19)
    @ColumnWidth(10)
    private BigDecimal salesVolume;

    @ExcelProperty(value = "更新时间",index = 20,converter = LocalDateTimeConverter.class)
    @ColumnWidth(10)
    private LocalDateTime modifyTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
