package cn.htdt.app.platform.dto.response.goods;


import cn.htdt.common.dto.BigDecimalSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 功能描述: 店铺云池分销商品响应dto
 *
 * @author: 张宇
 * @date: 2021/01/21 14:51
 */
@Data
public class ResPlatformCloudPoolDistributeGoodsDTO implements Serializable {

    @ApiModelProperty(value = "商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品形式 1001-普通商品;1002-系列商品")
    private String goodsForm;

    @ApiModelProperty(value = "商品上下架状态 1001：未上架 1002：已上架")
    private String goodsStatus;

    @ApiModelProperty(value = "零售价")
    private BigDecimal retailPrice;

    @ApiModelProperty(value = "最小零售价")
    private BigDecimal minRetailPrice;

    @ApiModelProperty(value = "最大零售价")
    private BigDecimal maxRetailPrice;

    @ApiModelProperty(value = "本店可获得佣金")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal storeCanGetCommission;

    @ApiModelProperty(value = "本店可获得最大佣金")
    private BigDecimal maxStoreCanGetCommission;

    @ApiModelProperty(value = "本店可获得最小佣金")
    private BigDecimal minStoreCanGetCommission;

    @ApiModelProperty(value = "代理人可获得佣金")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal agentCanGetCommission;

    @ApiModelProperty(value = "代理人可获得最大佣金")
    private BigDecimal maxAgentCanGetCommission;

    @ApiModelProperty(value = "代理人可获得最小佣金")
    private BigDecimal minAgentCanGetCommission;

    @ApiModelProperty(value = "代理人预计获得佣金")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal agentExpectGetCommission;

    @ApiModelProperty(value = "代理人已获得佣金")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal agentRealGetCommission;

    @ApiModelProperty(value = "本店预计获得佣金")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal storeExpectGetCommission;

    @ApiModelProperty(value = "本店实际获得佣金")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal storeRealGetCommission;

    @ApiModelProperty(value = "商品售卖状态  1：不可售 2：可售")
    private Integer disableFlag;

    @ApiModelProperty(value = "可售库存")
    private BigDecimal canSaleStockNum;

    @ApiModelProperty(value = "本店预计分销量")
    private Integer storeExpectDistributeAmount;

    @ApiModelProperty(value = "本店分销量")
    private Integer storeDistributeAmount;

    @ApiModelProperty(value = "总分销量")
    private Integer totalDistributeAmount;

    @ApiModelProperty(value = "商品总浏览次数")
    private Integer goodsPV;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "店铺编号")
    private String storeNo;

    @ApiModelProperty(value = "销量")
    private BigDecimal salesVolume;

    @ApiModelProperty(value = "总销量")
    private BigDecimal totalSalesVolume;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
