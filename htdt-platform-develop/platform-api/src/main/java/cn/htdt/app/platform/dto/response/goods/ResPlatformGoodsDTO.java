package cn.htdt.app.platform.dto.response.goods;


import cn.htdt.app.platform.dto.common.PlatformGoodsValidityPeriodDTO;
import cn.htdt.app.platform.dto.common.PlatformMultiUnitGoodsDTO;
import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import cn.htdt.goodsprocess.dto.response.calculationunit.ResCalculationUnitGoodsDTO;
import cn.htdt.goodsprocess.dto.response.goods.ResMultiUnitGoodsDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 *
 * 功能描述: 商品响应dto
 * @author: 张宇
 * @date: 2020/9/11 14:51

 *
 * 修改:新增字段 channelSales
 * 修改原因 渠道销售
 * <AUTHOR>
 * @since 2025-03
 *
 */
@Data
public class ResPlatformGoodsDTO extends PlatformGoodsValidityPeriodDTO implements Serializable {

    @ApiModelProperty(value = "渠道销售 1001 零售，1002 普通小批，1003 精品小批，1004 渠道")
    private String channelSales;

    @ApiModelProperty(value = "商品编号，包含普通商品、套餐商品等所有商品")
    private String goodsNo;

    @ApiModelProperty(value = "自定义商品编号，包含普通商品、套餐商品等所有商品")
    private String customGoodsNo;

    @ApiModelProperty(value = "商品形式 1001-普通商品;1002-系列商品")
    private String goodsForm;

    @ApiModelProperty(value = "商品形式value")
    private String goodsFormValue;

    @ApiModelProperty(value = "商品类型 1001-实物商品;1002-虚拟商品;1003-服务商品（仅供平台使用）;1004-称重商品")
    private String goodsType;

    @ApiModelProperty(value = "商品类型value")
    private String goodsTypeValue;

    @ApiModelProperty(value = "商品类目ID")
    private String categoryNo;

    @ApiModelProperty(value = "商品类目名称")
    private String categoryName;

    @ApiModelProperty(value = "商品一级销售类目")
    private String firstCategoryNo;

    @ApiModelProperty(value = "商品一级销售类目名称")
    private String firstCategoryName;

    @ApiModelProperty(value = "商品二级销售类目")
    private String secondCategoryNo;

    @ApiModelProperty(value = "商品二级销售类目名称")
    private String secondCategoryName;

    @ApiModelProperty(value = "商品三级销售类目")
    private String thirdCategoryNo;

    @ApiModelProperty(value = "商品三级销售类目名称")
    private String thirdCategoryName;

    @ApiModelProperty(value = "类目名称全路径")
    private String fullNamePath;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "店铺类目编号集合")
    private List<String> storeCategoryNoList;

    @ApiModelProperty(value = "店铺类目名称集合")
    private List<String> storeCategoryNameList;

    @ApiModelProperty(value = "商品品牌ID")
    private String brandNo;

    @ApiModelProperty(value = "商品品牌名称")
    private String brandName;

    @ApiModelProperty(value = "零售价")
    private BigDecimal retailPrice;

    @ApiModelProperty(value = "计量单位ID")
    private String calculationUnitNo;

    @ApiModelProperty(value = "计量单位符号")
    private String calculationUnitSymbol;

    @ApiModelProperty(value = "计量单位名称")
    private String calculationUnitName;

    @ApiModelProperty(value = "是否有辅计量单位:1-否;2-是;")
    private Integer standardFlag;

    @ApiModelProperty(value = "辅计量单位id")
    private String assistCalculationUnitNo;

    @ApiModelProperty(value = "辅计量单位符号")
    private String assistCalculationUnitSymbol;

    @ApiModelProperty(value = "辅计量单位名称")
    private String assistCalculationUnitName;

    //20230809蛋品-wxb-库存管理
    @ApiModelProperty(notes = "辅计量单位2id")
    private String assistCalculationUnitNoTwo;

    //20230809蛋品-wxb-库存管理
    @ApiModelProperty(notes = "辅计量单位2名称")
    private String assistCalculationUnitNameTwo;

    @ApiModelProperty(value = "主计量单位对应关系数值")
    private BigDecimal mainUnitNum;

    @ApiModelProperty(value = "辅计量单位对应关系数值")
    private BigDecimal assistUnitNum;

    @ApiModelProperty(value = "转换率(辅转主)")
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "是否入仓:1-否;2-是;")
    private Integer warehouseFlag;

    @ApiModelProperty(value = "是否入仓value")
    private String wareHouseFlagValue;

    @ApiModelProperty(value = "商品来源类型值")
    private String goodsSourceTypeValue;

    @ApiModelProperty(value = "商品来源类型1001-平台自建;1002-商家自建;1003-店铺自建;1004:一键代发;1005:商家分发")
    private String goodsSourceType;

    @ApiModelProperty(value = "是否纳入商品主数据库(1:否 2:是)")
    private Integer mainDataFlag;

    @ApiModelProperty(value = "保修期（天）")
    private Integer guaranteeDays;

    @ApiModelProperty(value = "商品型号")
    private String goodsModel;

    @ApiModelProperty(value = "商品文描")
    private String content;

    @ApiModelProperty(value = "服务标签编码")
    private String serviceTagNo;

    @ApiModelProperty(value = "角标编码")
    private String superscriptNo;

    @ApiModelProperty(value = "卖点描述")
    private String goodsSaleDescription;

    @ApiModelProperty(value = "商品助记码")
    private String goodsHelpCode;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "供应商手机号")
    private String supplierTelphone;

    @ApiModelProperty(value = "限价")
    private BigDecimal limitPrice;

    @ApiModelProperty(value = "市场价")
    private BigDecimal marketPrice;

    @ApiModelProperty(value = "采购价")
    private BigDecimal purchasePrice;

    @ApiModelProperty(value = "最小市场价")
    private BigDecimal minMarketPrice;

    @ApiModelProperty(value = "最大市场价")
    private BigDecimal maxMarketPrice;

    @ApiModelProperty(value = "最小采购价")
    private BigDecimal minPurchasePrice;

    @ApiModelProperty(value = "最大采购价")
    private BigDecimal maxPurchasePrice;

    @ApiModelProperty(value = "最小零售价")
    private BigDecimal minRetailPrice;

    @ApiModelProperty(value = "最大零售价")
    private BigDecimal maxRetailPrice;

    @ApiModelProperty(value = "运费模板ID")
    private String freightTemplateNo;

    @ApiModelProperty(value = "销售区域ID")
    private String salesAreaNo;

    @ApiModelProperty(value = "条形码")
    private String barcode;

    @ApiModelProperty(value = "商品状态1001-未上架;1002:已上架;")
    private String goodsStatus;

    @ApiModelProperty(value = "商品状态value")
    private String goodsStatusValue;

    @ApiModelProperty(value = "商品状态value")
    private String goodsShowStatusValue;

    @ApiModelProperty(value = "商品展示状态1001-未上架;1002-已上架;2002-审核中;2003-审核成功;2004-审核失败;3001:未分发;3003:分发成功;3004:已失效;")
    private String goodsShowStatus;

    @ApiModelProperty(value = "操作人名称")
    private String modifyName;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime modifyTime;

    @ApiModelProperty(value = "支付方式(1000:网店支付 1001:到店支付;1000,1001:到店支付+网店支付)")
    private String paymentMethod;

    @ApiModelProperty(value = "配送方式(1000:自提 1100：配送;1000,1100:自提+配送)")
    private String deliveryWay;

    @ApiModelProperty(value = "实际库存(主)")
    private BigDecimal realStockNum;

    @ApiModelProperty(value = "可售库存(主)")
    private BigDecimal canSaleStockNum;

    //20230809蛋品-wxb-库存管理
    @ApiModelProperty(value = "多单位商品使用 可售库存 (辅1)")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal canSaleStockAssistOneNum;

    //20230809蛋品-wxb-库存管理
    @ApiModelProperty(value = "多单位商品使用 实际库存 (辅1)")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal realStockAssistOneNum;

    //20230809蛋品-wxb-库存管理
    @ApiModelProperty(value = "多单位商品使用 可售库存 (辅2)")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal canSaleStockAssistTwoNum;

    //20230809蛋品-wxb-库存管理
    @ApiModelProperty(value = "多单位商品使用 实际库存 (辅2)")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal realStockAssistTwoNum;

    @ApiModelProperty(value = "关联仓库")
    private String collectWarehouseName;

    @ApiModelProperty(value = "是否可用:默认2，1：不可用2:可用")
    private Integer disableFlag;

    @ApiModelProperty(value = "商品图片列表")
    private List<ResPlatformGoodsPictureDTO> pictureList;

    @ApiModelProperty(value = "商品详情图片集合")
    private List<ResPlatformGoodsPictureDTO> detailPictureList;

    @ApiModelProperty(value = "商品主图")
    private String mainPictureUrl;

    @ApiModelProperty(value = "视频url")
    private String videoUrl;

    @ApiModelProperty(value = "视频时长")
    private String videoDuration;

    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "文件大小(单位M)")
    private String fileSize;

    @ApiModelProperty(value = "是否启用串码(1:否 2:是)")
    private Integer imeiFlag;

    @ApiModelProperty(value = "系列商品")
    private ResPlatformSeriesGoodsDTO seriesGoods;

    @ApiModelProperty(value = "属性集合")
    private List<ResPlatformSeriesGoodsAttributeDTO> attributeList;

    @ApiModelProperty(value = "多单位商品信息")
    private List<PlatformMultiUnitGoodsDTO> multiUnitGoods;

    @ApiModelProperty(value = "商品审核状态 2001:等待审核;2002:审核中;2003:审核成功; 2004:审核失败", name = "auditStatus")
    private String auditStatus;

    @ApiModelProperty(value = "已分发店铺")
    private String distributionStoreNames;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "门店编号")
    private String storeNo;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "角标图片地址")
    private String superscriptPictureUrl;

    @ApiModelProperty(value = "服务标签名称")
    private String serviceTagName;

    @ApiModelProperty(value = "云池供货价")
    private BigDecimal cloudPoolSupplyPrice;

    @ApiModelProperty(value = "仓库信息")
    private List<ResPlatformWarehouseGoodsRelationDTO> warehouseList;
    
    @ApiModelProperty(value = "商家账号")
    private String merberCode;
    
    @ApiModelProperty(value = "第一属性名称编码")
    private String firstAttributeName;
    
    @ApiModelProperty(value = "第一属性值编码")
    private String firstAttributeValueName;
    
    @ApiModelProperty(value = "第二属性名称编码")
    private String secondAttributeName;
    
    @ApiModelProperty(value = "第二属性值编码")
    private String secondAttributeValueName;
    
    @ApiModelProperty(value = "第三属性名称编码")
    private String thirdAttributeName;
    
    @ApiModelProperty(value = "第三属性值编码")
    private String thirdAttributeValueName;

    @ApiModelProperty(value = "商家商品编号(商家同步商品原型商品编号)")
    private String merchantGoodsNo;

    @ApiModelProperty(value = "是否允许自定义商品价格 1-不允许 2-允许")
    private Integer allowCustomPrice;
    /**
     * 是否启动效期管理标识(1:否 2:是)
     */
    private Integer validityPeriodManageFlag;

    /**
     * 保质期,正整形
     */
    private Integer qualityGuaranteePeriod;

    /**
     * 保质期单位,day:日 month:月 year:年,枚举:PlanCycleTypeEnum
     */
    private String shelfLifeUnit;

    @ApiModelProperty(value = "规格属性")
    private String attributeNames;

    //20230928蛋品-吴鑫鑫-商品管理-商品创建
    @ApiModelProperty(value = "多单位商品类型 1001:主单位商品 1002:子")
    private String multiUnitType;

    //20230928蛋品-吴鑫鑫-商品管理-商品创建
    @ApiModelProperty(value = "多单位主品编号")
    private String multiUnitGoodsNo;

    /**
     * //20230809蛋品-wxb-要货单管理
     */
    @ApiModelProperty(value = "多单位下拉框")
    private List<ResCalculationUnitGoodsDTO> calculationUnitGoodsDTOS;

    /**
     * //20250513 是否关联商品组 0否,1是
     */
    @ApiModelProperty(value = "是否关联商品组:默认0，1")
    private Integer isRelatedGoods;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }
}