package cn.htdt.app.platform.dto.response.order;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import cn.htdt.common.dto.response.ResEnumsDTO;
import cn.htdt.common.enums.OrderStatusEnum;
import cn.htdt.common.enums.PaymentChannelEnum;
import cn.htdt.common.enums.constants.NumConstant;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 查询订单列表（关联售后）
 *
 * <AUTHOR>
 */
@Data
public class ResPlatformReturnOrderItemDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = -928662700660381520L;

    @ApiModelProperty(value = "订单状态")
    @Converter(enumClass = OrderStatusEnum.class, fieldName = "orderStatusName")
    private String orderStatus;

    @ApiModelProperty(value = "订单状态Name")
    private String orderStatusName;

    /**
     * 订单是否欠款
     * 1 否 2 是
     */
    @ApiModelProperty(value = "订单是否欠款")
    private Integer arrearsFlag;

    /**
     * 退款渠道
     * PaymentChannelEnum
     */
    @ApiModelProperty(value = "退款渠道")
    @Converter(enumClass = PaymentChannelEnum.class, fieldName = "refundChannelName")
    private String refundChannel;

    @ApiModelProperty(value = "退款渠道Name")
    private String refundChannelName;

    @ApiModelProperty(value = "运费")
    private BigDecimal orderDeliveryFee;

    @ApiModelProperty(value = "已经发生售后商品数量")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal returnNum;

    /**
     * 订单商品明细
     */
    private List<ResPlatformOrderGoodsItemDTO> resAppOrderGoodsItemDTOList;

    @ApiModelProperty(value = "退款原因")
    private List<ResEnumsDTO> refundReviewReasonMap;

    @ApiModelProperty(value = "售后类型")
    private List<ResEnumsDTO> returnTypeMap;

    @ApiModelProperty(value = "退款方式")
    private List<ResEnumsDTO> refundWayMap;

    @ApiModelProperty(value = "订单标识 1 普通 2 云卖货 3 分销 9 中奖")
    private Integer orderFlag;

    @ApiModelProperty(value = "渠道优惠金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal orderExtendChannelDiscount = BigDecimal.ZERO;
    @ApiModelProperty(value = "渠道优惠编码，如：美团")
    private String orderExtendChannelCode;
    @ApiModelProperty(value = "渠道优惠名称，如：美团")
    private String orderExtendChannelName;
    @ApiModelProperty(value = "渠道优惠类型：0=无需核销 1=整单核销 2=按金额核销")
    private Integer orderExtendChannelFlag = NumConstant.ZERO;

    @ApiModelProperty(value = "橙豆")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal virtualCoinsDiscount;

    @ApiModelProperty(value = "积分抵扣")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal orderDeductionPointDiscount;

}
