package cn.htdt.app.platform.dto.response.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
public class ResHomePageButton implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "采购商城标识：0不展示，1展示")
    private Integer purchasingMallFlag = 0;
    @ApiModelProperty(value = "服务市场标识：0不展示，1展示")
    private Integer servicesMarketingFlag = 0;
    @ApiModelProperty(value = "升级会员店标识：0不展示，1展示")
    private Integer upgradeMembershipStore = 0;
    @ApiModelProperty(value = "采购管理平台：0不展示，1展示")
    private Integer purchasingPlatformFlag = 0;
}
