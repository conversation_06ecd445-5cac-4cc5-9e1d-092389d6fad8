package cn.htdt.app.platform.dto.response.user;

import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021-08-11
 * @description 查询商品分析详情响应DTO
 **/
@Data
public class ResPlatformGoodsAnalysisDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "排行")
    private Integer rank;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品销量")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal goodsItemNumCount;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
