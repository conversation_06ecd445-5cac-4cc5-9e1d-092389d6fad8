package cn.htdt.app.platform.dto.response.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 分销商品分销效果
 *
 * <AUTHOR>
 * @date 2021/1/27
 **/
@Data
public class ResPlatformAgentDistributeEffectDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "代理人手机号")
    private String agentPhone;

    @ApiModelProperty(value = "代理人姓名")
    private String agentName;

    @ApiModelProperty(value = "代理人编号")
    private String agentNo;

    @ApiModelProperty(value = "预计成交分销商品数")
    private int expectDistributeAmount;

    @ApiModelProperty(value = "实际成交分销商品数")
    private int distributeAmount;

    @ApiModelProperty(value = "总分销次数")
    private int totalDistributeAmount;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
