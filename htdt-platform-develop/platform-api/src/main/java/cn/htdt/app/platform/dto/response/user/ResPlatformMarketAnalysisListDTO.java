package cn.htdt.app.platform.dto.response.user;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021-08-09
 * @description 营销分析详情响应DTO
 **/
@Data
public class ResPlatformMarketAnalysisListDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动名称")
    private String marketName;

    @ApiModelProperty(value = "活动时间")
    private String marketTime;

    @ApiModelProperty(value = "活动类型 1秒杀 2优惠券 3抽奖")
    private Integer marketType;

    @JsonIgnore
    private String storeNo;

    @JsonIgnore
    private String storeName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
