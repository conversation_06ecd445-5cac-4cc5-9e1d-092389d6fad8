package cn.htdt.app.platform.dto.response.order;

import cn.htdt.app.platform.converter.OrderChannelSourceConverter;
import cn.htdt.app.platform.converter.OrderDeliveryWayConverter;
import cn.htdt.app.platform.converter.OrderStatusConverter;
import cn.htdt.app.platform.converter.OrderTypeConverter;
import cn.htdt.app.platform.converter.base.LocalDateTimeConverter;
import cn.htdt.app.platform.converter.base.WhetherConverter;
import cn.htdt.common.utils.excel.dto.RowRangeDto;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 店铺订单数据导出
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ResPlatformOrderExcel4StoreDTO extends RowRangeDto {

    @ExcelProperty(value = "订单编号", index = 0)
    @ColumnWidth(30)
    private String orderNo;

    @ExcelProperty(value = "商品名称", index = 1)
    @ColumnWidth(20)
    private String goodsName;

    @ExcelProperty(value = "销售总价", index = 2)
    @ColumnWidth(20)
    private BigDecimal goodsTotalAmount;

    @ExcelProperty(value = "下单时间", index = 3, converter = LocalDateTimeConverter.class)
    @ColumnWidth(20)
    private LocalDateTime orderCreateTime;

    @ExcelProperty(value = "支付时间", index = 4, converter = LocalDateTimeConverter.class)
    @ColumnWidth(20)
    private LocalDateTime orderPaymentConfirmDate;

    @ExcelProperty(value = "优惠金额", index = 5)
    @ColumnWidth(20)
    private BigDecimal discountAmount;

    @ExcelProperty(value = "物流信息", index = 6)
    @ColumnWidth(20)
    private String deliveryInfo;

    @ExcelProperty(value = "支付方式", index = 7)
    @ColumnWidth(20)
    private String orderPaymentMethod;

    @ExcelProperty(value = "支付渠道", index = 8)
    @ColumnWidth(20)
    private String paymentChannel;


    @ExcelProperty(value = "订单应付款", index = 9)
    @ColumnWidth(20)
    private BigDecimal shouldAmount;

    @ExcelProperty(value = "订单实付款", index = 10)
    @ColumnWidth(20)
    private BigDecimal realAmount;

    @ExcelProperty(value = "下单人账号", index = 11)
    @ColumnWidth(20)
    private String buyerMobile;


    @ExcelProperty(value = "订单状态", index = 12, converter = OrderStatusConverter.class)
    @ColumnWidth(20)
    private String orderStatus;


    @ExcelProperty(value = "订单类型", index = 13, converter = OrderTypeConverter.class)
    @ColumnWidth(20)
    private String orderType;

    @ExcelProperty(value = "配送方式", index = 14, converter = OrderDeliveryWayConverter.class)
    @ColumnWidth(20)
    private String orderDeliveryWay;

    @ExcelProperty(value = "运费", index = 15)
    @ColumnWidth(20)
    private BigDecimal orderDeliveryFee;

    @ExcelProperty(value = "收货地址", index = 16)
    @ColumnWidth(20)
    private String goodReceiverAddress;

    @ExcelProperty(value = "收货人手机号", index = 17)
    @ColumnWidth(20)
    private String goodReceiverMobile;

    @ExcelProperty(value = "收货人姓名", index = 18)
    @ColumnWidth(20)
    private String goodReceiverName;

    @ExcelProperty(value = "备注", index = 19)
    @ColumnWidth(20)
    private String orderRemark;

    @ExcelProperty(value = "来源终端", index = 20, converter = OrderChannelSourceConverter.class)
    @ColumnWidth(20)
    private String orderChannelSource;

    @ExcelProperty(value = "是否存在售后", index = 21, converter = WhetherConverter.class)
    @ColumnWidth(20)
    private Integer returnFlag;


    @ExcelProperty(value = "开单员账号", index = 22)
    @ColumnWidth(32)
    private String sellerNo;

    @ExcelProperty(value = "开单员姓名", index = 23)
    @ColumnWidth(34)
    private String sellerName;

}
