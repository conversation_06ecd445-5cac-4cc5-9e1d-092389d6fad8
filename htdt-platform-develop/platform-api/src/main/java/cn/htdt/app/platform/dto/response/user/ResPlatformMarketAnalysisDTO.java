package cn.htdt.app.platform.dto.response.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-08-09
 * @description 营销分析详情活动列表响应DTO
 **/
@Data
public class ResPlatformMarketAnalysisDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "活动列表")
    private List<ResPlatformMarketAnalysisListDTO> resPlatformMarketAnalysisListDTOList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
