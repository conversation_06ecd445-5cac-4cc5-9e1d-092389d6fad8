server:
  port: 7091
spring:
  main:
    allow-bean-definition-overriding: true
  application:
    name: htdt-orderprocess
  profiles:
    active: dev
    include: dubbo
  cloud:
    nacos:
      # nacos配置中心
      config:
        extension-configs[0]:
          data-id: application-orderprocess-dubbo.yml
          refresh: true
        extension-configs[1]:
          data-id: application-orderprocess-custom.yml
          refresh: true
        extension-configs[2]:
          data-id: application-orderprocess.yml
          refresh: true
        extension-configs[3]:
          data-id: application-common.yml
          refresh: true
        extension-configs[4]:
          data-id: application-orderprocess-datasource.yml
          refresh: true
        # 配置中心
        server-addr: ${nacos.server-addr}
        # 配置文件后缀名
        file-extension: yml
        # 支持动态刷新
        refresh-enabled: true
        # 命名空间
        namespace: ${nacos.namespace}
        # 是否启用nacos配置
        enabled: true
#        username: nacos
#        password: nacos
      # nacos服务注册发现
      discovery:
        server-addr: ${nacos.server-addr}
        namespace: ${nacos.dev}
        enabled: true
nacos:
  server-addr: 192.168.10.35:8848
  namespace: dp_prod
  dev: dev-lc
feign:
  httpclient:
    enabled: true
