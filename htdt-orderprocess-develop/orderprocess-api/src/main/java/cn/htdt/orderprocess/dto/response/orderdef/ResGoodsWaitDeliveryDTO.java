package cn.htdt.orderprocess.dto.response.orderdef;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * 商品信息响应DTO
 *
 * <AUTHOR>
 */
@Data
public class ResGoodsWaitDeliveryDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = -4238176093615810763L;

    @ApiModelProperty(value = "商品集合")
    List<ResGoodsConfigDTO> listWaitGoods;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
