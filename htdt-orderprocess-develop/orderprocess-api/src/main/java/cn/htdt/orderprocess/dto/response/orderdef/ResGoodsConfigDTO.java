package cn.htdt.orderprocess.dto.response.orderdef;

import cn.htdt.orderprocess.dto.response.ResOrderGoodsDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 物流 -> 待发货商品和店铺配送商品信息响应DTO
 *
 * <AUTHOR>
 */
@Data
public class ResGoodsConfigDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = -4238176093615810763L;

    /**
     * 商品数量
     */
    @ApiModelProperty(value = "商品数量")
    private BigDecimal goodsNum;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String itemStatus;
    /**
     * 店铺配送文案
     */
    @ApiModelProperty(value = "店铺配送文案")
    private String statusMsg;

    /**
     * 1 ：待发货 2：店铺配送
     */
    @ApiModelProperty(value = "1 ：待发货 2：店铺配送")
    private Integer falg;

    @ApiModelProperty(value = "商品详情信息")
    List<ResOrderGoodsDTO> listResGoods;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
