package cn.htdt.orderprocess.dto.response.shoppingcart;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020-10-12
 * @Description 购物车表出参
 **/
@Data
public class ResOrderShoppingCartDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "下单人编号")
    private String buyerNo;

    @ApiModelProperty(value = "分销店集合")
    private List<ResOrderShoppingCartStoreDTO> listStore;

    @ApiModelProperty(value = "失效的商品集合")
    private List<ResOrderShoppingCartGoodsDTO> listFailGoods;

    @ApiModelProperty(value = "删除的商品集合，清除无用商品用")
    private List<ResOrderShoppingCartGoodsDTO> listDeleteGoods;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}