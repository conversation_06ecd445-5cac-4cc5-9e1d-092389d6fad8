package cn.htdt.orderprocess.dto.response.orderdef;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 经营数据二级页面, 各应用程序订单数和占比返回数据
 *
 * <AUTHOR>
 * @since 2023-02-10
 */
@Data
public class ResAppChannelSourceOrderDTO implements Serializable {

    @ApiModelProperty("全渠道订单, 成交总订单数")
    private int totalOrderNum;

    @ApiModelProperty("汇享购订单数")
    private int hxgOrderNum;

    @ApiModelProperty("汇享购订单占比")
    private String hxgOrderRate;

    @ApiModelProperty("千橙掌柜APP订单数")
    private int bossAppOrderNum;

    @ApiModelProperty("千橙掌柜APP订单占比")
    private String bossAppOrderRate;

    @ApiModelProperty("千橙掌柜PC订单数")
    private int bossPcOrderNum;

    @ApiModelProperty("千橙掌柜PC订单占比")
    private String bossPcOrderRate;

    @ApiModelProperty("千橙掌柜收银订单数")
    private int smartCashierOrderNum;

    @ApiModelProperty("千橙掌柜收银订单占比")
    private String smartCashierOrderRate;
}
