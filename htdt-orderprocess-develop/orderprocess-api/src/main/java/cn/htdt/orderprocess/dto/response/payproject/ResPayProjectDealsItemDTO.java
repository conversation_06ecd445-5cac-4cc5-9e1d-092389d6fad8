package cn.htdt.orderprocess.dto.response.payproject;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021-08-10
 * @description 付费产品订单产品详情响应DTO
 **/
@Data
public class ResPayProjectDealsItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("产品名称")
    private String projectName;

    @ApiModelProperty("产品原价格")
    private BigDecimal originalPrice;

    @ApiModelProperty("产品价格")
    private BigDecimal payAmount;

    @ApiModelProperty(value="产品服务生效时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate projectStart;

    @ApiModelProperty(value="产品服务失效时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate projectEnd;

    @ApiModelProperty("订单产品状态")
    private String dealsProjectStatus;

    /**
     * 产品类型：1:会员套餐 2:增值服务  枚举:ProjectTypeEnum
     */
    private Integer projectType;

    /**
     * 产品唯一编码
     */
    private String projectKitNo;

    /**
     * 订单编号
     */
    private String dealsNo;

    /**
     * 订单状态(对应主表订单状态)
     */
    private Integer dealsStatus;

    @ApiModelProperty("产品图片地址")
    private String projectImg;

    @ApiModelProperty("创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty("产品简介")
    private String projectIntroduction;

    @ApiModelProperty("特殊场景关联code")
    private String sceneAssociationCode;

    @ApiModelProperty(value = "是否试用账户 1:(默认)正常账户  2:试用账户")
    private Integer trialAccount;

    @ApiModelProperty("核销码")
    private String activationCode;

    /**
     * 赠送权益内容
     **/
    @ApiModelProperty(value="赠送权益内容")
    private String giftRightsContent;

    /**
     * 赠送权益状态
     **/
    @ApiModelProperty(value="赠送权益状态")
    private String giftRightsStatus;

    /**
     * 收回短信条数
     **/
    @ApiModelProperty(value="收回短信条数")
    private Integer regainSmsNum;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
