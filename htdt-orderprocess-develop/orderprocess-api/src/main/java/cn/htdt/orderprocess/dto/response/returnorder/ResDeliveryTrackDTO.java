package cn.htdt.orderprocess.dto.response.returnorder;

import cn.htdt.orderprocess.dto.response.delivery.ResOrderDeliveryDataDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020-12-04
 * @Description 物流跟踪信息
 **/
@Data
public class ResDeliveryTrackDTO implements Serializable {

    /**
     * 物流寄回方式
     */
    private Integer goodsReturnWay;

    /**
     * 物流公司
     */
    private String logisticsCompany;

    /**
     * 物流单号
     */
    private String courierNumber;

    /**
     * 用户填写物流公司Id
     */
    private String logisticsCompanyNo;

    /**
     * 物流详情
     */
    private List<ResOrderDeliveryDataDTO> remark;
}
