package cn.htdt.orderprocess.dto.response.payproject;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 产品订单退款记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-23
 */
@Data
public class ResPayProjectDealsRefundDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 退款订单号
     */
    private String dealsRefundNo;

    /**
     * 订单编号
     */
    private String dealsNo;

    /**
     * 退款价格
     */
    private BigDecimal refundPrice;

    /**
     * 签报url
     */
    private String reportUrl;


    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 归属平台公司名称
     */
    private String companyName;

    /**
     * 归属分部名称
     */
    private String divisionName;

    /**
     * 订单标签：1->订阅套餐，2-> 增值套餐
     */
    private Integer productLabell;

    /**
     * 付款时间
     */
    private LocalDateTime payTime;

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 操作人手机号
     */
    private String userTel;

    /**
     * 退款操作人编号
     */
    private String createNo;

    /**
     * 退款操作人名称
     */
    private String createName;

    /**
     * 退款时间
     */
    private LocalDateTime createTime;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 会员编码
     */
    private String merberCode;

}
