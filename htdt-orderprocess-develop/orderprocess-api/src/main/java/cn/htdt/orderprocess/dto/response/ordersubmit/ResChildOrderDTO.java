package cn.htdt.orderprocess.dto.response.ordersubmit;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2020-11-07
 * @Description 下单出参
 *
 *
 * *  * 202503东启修改
 *  *  * 组合支付
 **/
@Data
public class ResChildOrderDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 组合支付 剩余扫描金额 待付金额 扫描成功之后归零
     */
    private BigDecimal pendingAmount;

    /**
     * 组合支付  -收到金额（找零使用）
     */
    private BigDecimal changePrice;

    /**
     * 组合支付状态 1009  默认是空
     */
    private String payStatu;

    @ApiModelProperty("订单类型，字典ORDER_TYPE")
    private String orderType;

    @ApiModelProperty("订单标识，字典OrderFlagEnum")
    private Integer orderFlag;

    @ApiModelProperty("促销标识，字典WhetherEnum")
    private Integer promotionsFlag;

    @ApiModelProperty("订单编号")
    private String orderNo;

    @ApiModelProperty("商户订单号")
    private String outTradeNo;

    @ApiModelProperty("分销店铺编号")
    private String distributionStoreNo;

    @ApiModelProperty("分销店铺名称")
    private String distributionStoreName;

    @ApiModelProperty("分销商家编号")
    private String distributionMerchantNo;

    @ApiModelProperty("分销商家名称")
    private String distributionMerchantName;

    @ApiModelProperty("店铺编号")
    private String storeNo;

    @ApiModelProperty("店铺名称")
    private String storeName;

    @ApiModelProperty("商家编号")
    private String merchantNo;

    @ApiModelProperty("商家名称")
    private String merchantName;

    @ApiModelProperty("归属平台编号")
    private String companyNo;

    @ApiModelProperty("归属平台名称")
    private String companyName;

    @ApiModelProperty("归属分部编号")
    private String branchNo;

    @ApiModelProperty("归属分部名称")
    private String branchName;

    @ApiModelProperty("订单需要去支付金额")
    private BigDecimal needAmount;

    @ApiModelProperty("最后支付时间")
    private LocalDateTime endPayTime;

    @ApiModelProperty(value = "橙豆抵扣金额")
    private BigDecimal virtualCoinDiscount;

    @ApiModelProperty(value = "橙豆余额")
    private BigDecimal totalCoin;

    @ApiModelProperty(value = "商品总金额")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "整单优惠金额")
    private BigDecimal orderTotalDiscount;

    @ApiModelProperty(value = "小票流水号")
    private String ticketCode;
}