package cn.htdt.orderprocess.dto.response;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/3/6 14:36
 */
@Data
public class ResSaleAmountDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 实付金额
     */
    private BigDecimal realAmount;

    /**
     * 应付金额
     */
    private BigDecimal shouldAmount;
}
