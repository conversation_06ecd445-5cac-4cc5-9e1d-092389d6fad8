package cn.htdt.orderprocess.dto.response.shoppingguide;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/4/4 13:58
 */
@Data
public class ResShoppingGuidePerformanceDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "导购员姓名")
    private String shoppingGuideName;

    @ApiModelProperty(value = "商品总额")
    private BigDecimal totalGoodsAmount;

    @ApiModelProperty(value = "整单优惠")
    private BigDecimal discountAmount;

    @ApiModelProperty("应付金额")
    private BigDecimal shouldAmount;

    @ApiModelProperty("已付金额")
    private BigDecimal realAmount;

    @ApiModelProperty("橙豆支付")
    private BigDecimal coinAmount;

    @ApiModelProperty("欠款金额")
    private BigDecimal arrearsAmount;
}
