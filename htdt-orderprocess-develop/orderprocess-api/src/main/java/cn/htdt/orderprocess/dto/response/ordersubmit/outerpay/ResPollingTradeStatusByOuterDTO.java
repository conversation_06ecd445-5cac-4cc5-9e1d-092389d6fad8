package cn.htdt.orderprocess.dto.response.ordersubmit.outerpay;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022-08-23
 * @Description 第三方支付状态轮询反参
 **/
@Data
@ApiModel
public class ResPollingTradeStatusByOuterDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("订单状态")
    private String tradeStatus;
}