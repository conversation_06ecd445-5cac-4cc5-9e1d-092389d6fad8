package cn.htdt.orderprocess.dto.response;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.enums.OrderPaymentStatusEnum;
import cn.htdt.common.enums.OrderStatusEnum;
import cn.htdt.common.enums.constants.NumConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 查询订单支付信息
 *
 * 202503东启修改
 * 组合支付
 *
 * <AUTHOR>
 */
@Data
public class ResOrderPayInfoDTO implements Serializable {

    private static final long serialVersionUID = 5767317748376772306L;

    @ApiModelProperty(value = "找零金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal changePrice;

    @ApiModelProperty(value = "待付金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal pendingAmount;

    @ApiModelProperty(value = "组合支付状态:1009 默认是空")
    private String payStatu;


    /**
     * 支付确认时间
     */
    @ApiModelProperty("支付确认时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime orderPaymentConfirmDate;

    @ApiModelProperty(value = "支付状态")
    @Converter(enumClass = OrderPaymentStatusEnum.class, fieldName = "orderPaymentStatusName")
    private String orderPaymentStatus;

    @ApiModelProperty(value = "支付状态Name")
    private String orderPaymentStatusName;

    @ApiModelProperty(value = "网店订单 1 否 2 是")
    private Integer onlineFlag;

    @ApiModelProperty(value = "支付方式 1000 网店支付；1001 到店支付")
    private String orderPaymentMethod;

    @ApiModelProperty(value = "应付金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal shouldAmount;

    @ApiModelProperty(value = "商品总金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "已付金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal realAmount;

    @ApiModelProperty(value = "订单欠款金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal arrearsAmount;

    @ApiModelProperty(value = "运费")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal orderDeliveryFee;

    @ApiModelProperty(value = "整单优惠")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal orderAllDiscount = BigDecimal.ZERO;

    @ApiModelProperty(value = "改价优惠")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal orderChangeDiscount = BigDecimal.ZERO;

    @ApiModelProperty(value = "优惠券优惠")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal orderCouponDiscount = BigDecimal.ZERO;

    @ApiModelProperty(value = "代金券优惠")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal orderVoucherDiscount = BigDecimal.ZERO;

    @ApiModelProperty(value = "橙豆")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal virtualCoinsDiscount = BigDecimal.ZERO;

    @ApiModelProperty(value = "购物币（橙豆）余额")
    private BigDecimal accountRemainCoins;

    @ApiModelProperty(value = "积分扣减")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal orderPointsDiscount;

    @ApiModelProperty(value = "满折优惠")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal orderFullDiscount = BigDecimal.ZERO;

    @ApiModelProperty(value = "满减优惠")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal orderReduceDiscount = BigDecimal.ZERO;

    @ApiModelProperty(value = "积分抵扣优惠")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal orderDeductionPointDiscount = BigDecimal.ZERO;

    @ApiModelProperty(value = "店铺抵扣积分")
    private Integer depPoints = NumConstant.ZERO;

    @ApiModelProperty(value = "渠道优惠金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal orderExtendChannelDiscount = BigDecimal.ZERO;
    @ApiModelProperty(value = "渠道优惠编码，如：美团")
    private String orderExtendChannelCode;
    @ApiModelProperty(value = "渠道优惠名称，如：美团")
    private String orderExtendChannelName;
    @ApiModelProperty(value = "渠道优惠类型：0=无需核销 1=整单核销 2=按金额核销")
    private Integer orderExtendChannelFlag = NumConstant.ZERO;

    @ApiModelProperty(value = "礼品卡优惠金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal giftCardDiscount = BigDecimal.ZERO;

    @ApiModelProperty(value = "订单支付流水")
    private List<ResOrderPayFlowDTO> resOrderPayFlowDTOList;

    @ApiModelProperty(value = "订单标识，1 普通，2 云池，3 分销，9 中奖 字典OrderFlagEnum")
    private Integer orderFlag;

    @ApiModelProperty(value = "订单状态")
    @Converter(enumClass = OrderStatusEnum.class, fieldName = "orderStatusName")
    private String orderStatus;

    @ApiModelProperty(value = "订单状态名称")
    private String orderStatusName;
    @ApiModelProperty("订单类型，1000:汇享购订单,1020:秒杀订单,1021:拼团订单,1022:预售订单,1301:中奖订单,1500:门店订单,1010:云池订单")
    private String orderType;

    @ApiModelProperty(value = "订单渠道来源  对应枚举OrderChannelSourceEnum")
    private String orderChannelSource;

    /**
     * 订单赠送的积分
     */
    @ApiModelProperty("订单赠送的积分")
    private Integer orderGivePoints;

    @ApiModelProperty(value = "特惠促销优惠金额-会员节省")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal vipDiscountAmount;

    @ApiModelProperty(value = "会员价优惠金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal memberPriceDiscountAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "代金券优惠金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal voucherDiscount;

    @ApiModelProperty(value = "虚拟订单（即商品）类型 1001-代金券,1002-计次卡")
    private String virtualGoodsType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
