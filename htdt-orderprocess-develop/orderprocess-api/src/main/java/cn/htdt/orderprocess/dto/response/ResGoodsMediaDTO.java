package cn.htdt.orderprocess.dto.response;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/9/22 16:44
 */
@Data
public class ResGoodsMediaDTO implements Serializable {

    /**
     * 媒体编号
     */
    private String mediaNo;
    /**
     * 商品编号，包含普通商品、套餐商品等所有商品
     */
    private String goodsNo;

    /**
     * 排序值
     */
    private Integer sortValue;

    /**
     * 资源类型：1001、图片 1002、视频
     */
    private String mediaType;

    /**
     * 图片url,缩略图URL
     */
    private String pictureUrl;

    /**
     * 0、不是主图 1、是主图
     */
    private Integer mainPictureFlag;

    /**
     * 图片展示区域 0:轮播 1:详情
     */
    private Integer pictureShowArea;

    /**
     * 视频url
     */
    private String videoUrl;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件大小(单位M)
     */
    private String fileSize;

    /**
     * 视频商品时长,单位(秒)
     */
    private String videoDuration;

    /**
     * 视频审核状态,1001:审核中  1002:审核通过  1003:审核未通过
     */
    private String videoAuditStatus;

    /**
     * 审核结果,失败原因
     */
    private String videoAuditResult;
}
