package cn.htdt.orderprocess.dto.response;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.enums.market.PromotionDetailTypeEnum;
import cn.htdt.common.enums.market.PromotionOrderTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 订单促销活动信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-28
 */
@Data
public class ResSoPromotionDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /**
     * 父订单编号
     */
    @ApiModelProperty(value = "父订单编号")
    private String parentOrderNo;
    @ApiModelProperty(value = "so_promotion唯一编码")
    private String soPromotionCode;

    /**
     * 所属平台
     */
    @ApiModelProperty(value = "所属平台")
    private String companyNo;

    /**
     * 促销活动编号
     */
    @ApiModelProperty(value = "促销活动编号")
    private String promotionNo;

    /**
     * 促销活动类型  1001 优惠券 1002 议价 1003 满减
     */
    @ApiModelProperty(value = "促销活动类型  1001 优惠券 1002 议价 1003 满减")
    @Converter(enumClass = PromotionOrderTypeEnum.class, fieldName = "promotionTypeName", enumField = "type")
    private String promotionType;

    @ApiModelProperty(value = "促销活动明细类型  1001 优惠券 1002 议价 1003 满减 1004 膨胀红包优惠券 1005 满折")
    @Converter(enumClass = PromotionDetailTypeEnum.class, fieldName = "promotionDetailTypeName", enumField = "type")
    private String promotionDetailType;

    @ApiModelProperty(value = "促销活动类型名称  1001 优惠券 1002 议价 1003 满减")
    private String promotionTypeName;

    @ApiModelProperty(value = "促销活动明细类型  1001 优惠券 1002 议价 1003 满减 1004 膨胀红包优惠券 1005 满折")
    private String promotionDetailTypeName;

    /**
     * 促销活动名称
     */
    @ApiModelProperty(value = "促销活动名称")
    private String promotionName;

    @ApiModelProperty(value = "用户优惠券编号（礼品卡卡密）")
    private String userCouponNo;

    /**
     * 订单分摊金额
     */
    @ApiModelProperty(value = "订单分摊金额")
    private BigDecimal amountSharePromotion;

    /**
     * 店铺抵扣积分
     */
    private BigDecimal depPoints;

    /**
     * 扩展渠道名称
     */
    private String extendChannelName;

    /**
     * 活动描述
     */
    @ApiModelProperty(value = "活动描述")
    private String promotionDesc;

    /**
     * toB标识，默认0未同步，1已同步
     */
    @ApiModelProperty(value = "toB标识，默认0未同步，1已同步")
    private String tobFlag;

    @ApiModelProperty(value = "使用促销商品信息列表")
    private List<ResSoPromotionItemDTO> resSoPromotionItemDTOList;

    /**
     * 套餐分组编号
     */
    private String packageGroupingNo;

}
