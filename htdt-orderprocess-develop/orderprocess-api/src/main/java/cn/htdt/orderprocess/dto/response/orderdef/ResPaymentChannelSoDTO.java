package cn.htdt.orderprocess.dto.response.orderdef;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description 支付流水表支付渠道对应的付款金额
 * @Date 2021/7/26
 * @Param 
 * @return 
 **/
@Data
public class ResPaymentChannelSoDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = 741087081635029349L;

    /**
     * 支付渠道 字典PAYMENT_CHANNEL
     */
    private String paymentChannel;

    /**
     * 实付金额
     */
    private BigDecimal realAmount;
}


