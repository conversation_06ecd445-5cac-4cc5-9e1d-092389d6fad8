package cn.htdt.orderprocess.dto.response.returnorder;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 售后订单数据
 */
@Data
public class ResReturnOrderDTO implements Serializable {

    /**
     * 退单申请时间
     */
    private LocalDateTime applyTime;

    /**
     *
     * 退款单状态 字典REFUNDMENT_STATUS
     */
    private String refundmentStatus;

    /**
     * 售后单状态
     *  退单状态，字典SO_RETURN_STATUS
     */
    private String returnStatus;

    /**
     * 售后编号
     */
    private String soReturnNo;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 售后类型 1仅退款未发货 2仅退款已发货 3退款退货
     */
    private String returnType;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 订单渠道来源 字典ORDER_CHANNEL_SOURCE
     */
    private String orderChannelSource;

    /**
     * 退款方式
     */
    private String refundmentWay;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 用户申请退款金额
     */
    private BigDecimal applyReturnAmount;

    /**
     * 申请来源
     */
    private String source;

    /**
     * 商品编号
     */
    private String goodsNo;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 订单明细编号
     */
    private String orderItemNo;

    /**
     * 商品数量
     */
    private BigDecimal returnProductItemNum;

    /**
     * 实际退款金额（不受申请金额干扰、含运费）
     */
    private BigDecimal actualReturnAmount;

    /**
     * 下单人编码
     */
    private String buyerNo;

    /**
     * 下单人名称
     */
    private String buyerName;

    /**
     * 原扩展信息
     */
    private String extInfo;

    /**
     * 汇通数科编号
     */
    private String htskNo;

    /**
     * 汇通达编号
     */
    private String htdNo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
