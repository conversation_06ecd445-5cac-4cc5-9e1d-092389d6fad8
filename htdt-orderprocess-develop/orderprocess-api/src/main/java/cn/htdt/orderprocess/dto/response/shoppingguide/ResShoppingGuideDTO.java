package cn.htdt.orderprocess.dto.response.shoppingguide;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/4/3 9:41
 */
@Data
public class ResShoppingGuideDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "导购员编号")
    private String shoppingGuideNo;

    @ApiModelProperty(value = "导购员姓名")
    private String shoppingGuideName;

    @ApiModelProperty(value = "排序值")
    private Integer sortValue;

    @ApiModelProperty(value = "提成比例")
    private BigDecimal commissionRatio;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime modifyTime;
}
