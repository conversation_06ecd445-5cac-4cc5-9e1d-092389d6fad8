package cn.htdt.orderprocess.dto.response.payproject;

import cn.htdt.common.dto.BigDecimalSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 产品表
 * @Date 2021/8/2
 * @Param
 * @return
 **/
@Data
public class ResPayProjectKitDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("产品唯一编码")
    private String projectKitNo;

    @ApiModelProperty("类型： 1:会员套餐 2:增值服务")
    private Integer projectType;

    @ApiModelProperty("产品名称")
    private String projectName;

    @ApiModelProperty("产品简介")
    private String projectIntroduction;

    @ApiModelProperty("对应角色id")
    private String projectRoleNo;

    @ApiModelProperty("产品图片地址")
    private String projectImg;

    @ApiModelProperty("外层排序")
    private Integer showOrderL;

    @ApiModelProperty("内层排序")
    private Integer showOrderR;

    @ApiModelProperty("产品价格")
    private String projectPrice;

    @ApiModelProperty("实物邮寄 1:是：2否")
    private Integer physicalMailing;

    @ApiModelProperty("详情介绍")
    private String projectDetails;

    @ApiModelProperty("app图文介绍")
    private String imageApp;

    @ApiModelProperty("pc图文介绍")
    private String imagePc;

    @ApiModelProperty("产品上下架 1：上架 2：下架")
    private Integer projectOnline;

    @ApiModelProperty("合同地址")
    private String projectContract;

    @ApiModelProperty("产品状态 0：删除 1：正常")
    private Integer projectStatus;

    @ApiModelProperty("同步给中台1：未同步 2：已经同步")
    private Integer updateFlag;

    @ApiModelProperty("产品有效天数")
    private Integer projectDays;

    @ApiModelProperty("是否试用 ：1否 2是")
    private Integer tryUseFlag;

    @ApiModelProperty("是否试用 ：1否 2是 手工录单选择下拉框")
    private List<Integer> tryUseFlagList;

    @ApiModelProperty("运维服务费")
    private String servicePrice;

    @ApiModelProperty("智能硬件押金")
    private String depositPrice;

    @ApiModelProperty("是否关联其他产品1：否 2：是")
    private Integer tryJoinFlag;

    @ApiModelProperty("是否有搭配产品 1:否 2:是")
    private Integer tryCollocationFlag;

    @ApiModelProperty("原价")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal originalPrice;

    @ApiModelProperty("产品类别")
    private String productCode;

    @ApiModelProperty("产品类别名称")
    private String productName;

    @ApiModelProperty("产品标签：1->订阅套餐，2-> 增值套餐")
    private Integer productLabel;

    @ApiModelProperty("付款身份 2-多店 8-单店")
    private Integer payIdentity;

    @ApiModelProperty("是否存在免费试用订单 ：1否 2是")
    private Integer haveFreeDealsFlag;

    @ApiModelProperty("特殊关联场景code")
    private String sceneAssociationCode;

    @ApiModelProperty("产品聚合唯一编码")
    private String polymerizationNo;

    @ApiModelProperty("是否是聚合服务 1否 2是")
    private Integer isPolymerization;

    @ApiModelProperty("是否为滴灌通套餐 1：否 2：是")
    private Integer mciFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
