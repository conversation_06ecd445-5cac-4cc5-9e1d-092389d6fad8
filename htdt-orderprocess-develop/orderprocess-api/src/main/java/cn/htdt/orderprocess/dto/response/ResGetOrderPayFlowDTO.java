package cn.htdt.orderprocess.dto.response;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2020-11-18
 * @Description 获取交易流水
 **/
@Data
public class ResGetOrderPayFlowDTO implements Serializable {
    /**
     * 序列化
     */
    private static final long serialVersionUID = 132753560677747037L;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 支付交易号
     */
    private String outTradeNo;

    /**
     * 交易状态,枚举TradeStatusEnum,默认待支付
     */
    private String tradeStatus;

    /**
     * 付款帐号
     * 如果是粉丝购买则是buyerNo
     */
    private String buyerAccount;

    /**
     * 收款帐号
     */
    private String collectionAccount;

    /**
     * 支付渠道 字典PAYMENT_CHANNEL
     */
    private String paymentChannel;

    /**
     * 支付渠道详情 字典PAYMENT_CHANNEL_DETAILS
     */
    private String paymentChannelDetails;

    /**
     * 支付方式 PAYMENT_METHOD
     */
    private String paymentMethod;

    /**
     * 支付金额
     */
    private BigDecimal amount;

    /**
     * 实付金额
     */
    private BigDecimal realAmount;

    /**
     * 支付优惠金额
     * 暂时不用
     */
    private BigDecimal promotionAmount;

    /**
     * 付款时间
     */
    private LocalDateTime payTime;

    /**
     * 付款截止时间
     */
    private LocalDateTime endPayTime;

    /**
     * 付款成功回调时间
     */
    private LocalDateTime successTime;

    /**
     * 备注
     */
    private String remark;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}