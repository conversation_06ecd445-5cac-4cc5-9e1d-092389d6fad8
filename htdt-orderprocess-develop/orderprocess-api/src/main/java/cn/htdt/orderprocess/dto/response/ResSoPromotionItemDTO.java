package cn.htdt.orderprocess.dto.response;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.enums.market.PromotionDetailTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 订单使用促销记录分摊表
 * </p>
 *
 */
@Data
public class ResSoPromotionItemDTO implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * so_promotion唯一编码
     */
    @ApiModelProperty(value = "so_promotion唯一编码")
    private String soPromotionCode;
    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /**
     * 父订单编号
     */
    @ApiModelProperty(value = "父订单编号")
    private String parentOrderNo;

    /**
     * 归属平台编号
     */
    @ApiModelProperty(value = "归属平台编号")
    private String companyNo;

    /**
     * 促销id
     */
    @ApiModelProperty(value = "促销id")
    private String promotionNo;

    /**
     * 促销类型 1001 优惠券 1002 议价 1003 满减
     */
    @ApiModelProperty(value = "促销类型 1001 优惠券 1002 议价 1003 满减")
    private String promotionType;

    /**
     * 促销活动明细类型  1001 优惠券 1002 议价 1003 满减 1004 膨胀红包优惠券
     */
    @ApiModelProperty(value = "促销活动明细类型  1001 优惠券 1002 议价 1003 满减 1004 膨胀红包优惠券")
    @Converter(enumClass = PromotionDetailTypeEnum.class, fieldName = "promotionDetailTypeName", enumField = "type")
    private String promotionDetailType;
    /**
     * 促销活动明细类型  1001 优惠券 1002 议价 1003 满减 1004 膨胀红包优惠券
     */
    @ApiModelProperty(value = "促销活动明细类型  1001 优惠券 1002 议价 1003 满减 1004 膨胀红包优惠券")
    private String promotionDetailTypeName;

    /**
     * 商品编号
     */
    @ApiModelProperty(value = "商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品主图")
    private String mainPictureUrl;

    /**
     * 商品购买数量
     */
    @ApiModelProperty(value = "商品购买数量")
    private BigDecimal goodsNum;

    /**
     * 促销分摊金额
     */
    @ApiModelProperty(value = "促销分摊金额")
    private BigDecimal amountSharePromotion;

    /**
     * 优惠券编号
     */
    private String userCouponNo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
