package cn.htdt.orderprocess.dto.response;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 代金券列表
 *
 * <AUTHOR>
 * @Date 2022-07-26
 **/
@Data
@ApiModel
public class ResVoucherListDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("可用的代金券集合")
    private List<ResCouponCanUserRecordDTO> listAvailable = Lists.newArrayList();

    @ApiModelProperty("不可用的代金券集合")
    private List<ResCouponCanUserRecordDTO> listNotAvailable = Lists.newArrayList();

}
