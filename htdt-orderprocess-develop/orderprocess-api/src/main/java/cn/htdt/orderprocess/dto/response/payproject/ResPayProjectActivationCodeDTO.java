package cn.htdt.orderprocess.dto.response.payproject;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/2/23 16:08
 */
@Data
public class ResPayProjectActivationCodeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("核销码")
    private String activationCode;

    @ApiModelProperty("核销订单编号")
    private String dealsNo;

    @ApiModelProperty("是否已经核销，默认1未使用，其余已使用")
    private Integer disableFlag;

}
