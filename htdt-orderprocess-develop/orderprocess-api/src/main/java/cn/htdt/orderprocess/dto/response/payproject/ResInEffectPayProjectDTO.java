package cn.htdt.orderprocess.dto.response.payproject;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 生效中的套餐信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-31
 */
@Data
public class ResInEffectPayProjectDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("产品名称")
    private String projectName;

    @ApiModelProperty("产品图片地址")
    private String projectImg;


}
