package cn.htdt.orderprocess.dto.response.payproject;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <p>
 * 付费产品订单表详情记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021/8/10
 */
@Data
public class ResPayProjectDealsDetailsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商户类型
     */
    private String traderType;

    /**
     * 发票类型
     */
    private String invoiceType;

    /**
     * 发票抬头
     */
    private String invoiceNotify;

    /**
     * 纳税人识别号
     */
    private String taxManid;

    /**
     * 开户行名称
     */
    private String bankName;

    /**
     * 开户行账号
     */
    private String bankAccount;

    /**
     * 开户行账号，密文
     */
    private String dsBankAccount;

    /**
     * 发票地址
     */
    private String invoiceAddress;

    /**
     * 发票地址，密文
     */
    private String dsInvoiceAddress;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 联系电话-密文
     */
    private String dsContactPhone;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
