package cn.htdt.orderprocess.dto.response.returnorder;

import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.enums.constants.NumConstant;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
  *@className:  ResOrderReturnOrderItemDTO
  * @Description : 售后详情
  * <AUTHOR>
  *@date : 2020/9/4/ 13:34
  */
@Data
public class ResOrderReturnOrderItemDTO implements Serializable {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 售后编号
     */
    private String soReturnNo;

    /**
     * 售后单来源
     */
    private String source;

    /**
     * 售后类型
     */
    private String returnType;

    /**
     * 分销店铺编号
     */
    private String distributionStoreNo;

    /**
     * 分销店铺名称
     */
    private String distributionStoreName;

    /**
     * 门店编号
     */
    private String storeNo;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品图片角标URL
     */
    private String superscriptPictureUrl;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 商品购买数量
     */
    private BigDecimal productItemNum;

    /**
     * 售后商品数量
     */
    private BigDecimal returnProductItemNum;

    /**
     * 商品规格属性
     */
    private String extInfo;

    /**
     * 商品销售单价
     */
    private BigDecimal productPriceSale;

    /**
     * 售后商品购买金额
     */
    private BigDecimal returnGoodsAmount;

    /**
     * 申请退款金额
     */
    private BigDecimal applyReturnAmount;

    /**
     * 退单申请时间
     */
    private LocalDateTime applyTime;

    /**
     * 返回优惠券
     */
    private BigDecimal discount;

    /**
     * 退代金券金额
     */
    private BigDecimal returnVoucherAmount;

    /**
     * 返回积分
     */
    private int orderDepletePoints;

    /**
     * 返回橙豆
     */
    private BigDecimal virtualCoinDiscount;

    /**
     * 扣减积分
     */
    private Integer orderGivePoints = 0;
    /**
     * 返回积分抵扣金额
     */
    private BigDecimal orderDeductionPointDiscount = BigDecimal.ZERO;

    /**
     * 返回抵扣积分
     */
    private Integer depPoints = NumConstant.ZERO;

    /**
     * 渠道优惠金额
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal orderExtendChannelDiscount = BigDecimal.ZERO;
    /**
     * 渠道优惠编码，如：美团
     */
    private String orderExtendChannelCode;
    /**
     * 渠道优惠名称，如：美团
     */
    private String orderExtendChannelName;
    /**
     * 渠道优惠类型：0=无需核销 1=整单核销 2=按金额核销
     */
    private Integer orderExtendChannelFlag = NumConstant.ZERO;

    /**
     * 退单原因
     */
    private String returnReason;

    /**
     * 退单申请描述
     */
    private String returnRemark;

    /**
     * 申请图片
     */
    private List<String> picPath;

    /**
     *  退款单状态 字典REFUNDMENT_STATUS
     */
    private String refundmentStatus;

    /**
     * 退单状态，字典SO_RETURN_STATUS
     */
    private String returnStatus;

    /**
     * 货物返回方式 1.快递 2.送回门店
     */
    private Integer goodsReturnWay;

    /**
     * 物流公司
     */
    private String logisticsCompany;

    /**
     * 快递单号
     */
    private String courierNumber;

    /**
     * 用户填写物流公司Id
     */
    private String logisticsCompanyNo;

    /**
     * 主图片URL
     */
    private String productPicPath;

    /**
     * 审核原因
     */
    private String auditReason;

    /**
     * 验货描述
     */
    private String inspectionDesc;

    /**
     * 申请售后次数
     */
    private int returnCount;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 订单渠道来源
     */
    private String orderChannelSource;

    //************最新一条快递信息start**************

    /**
     * 快递描述信息
     */
    private String remark;

    /**
     * 快递记录对应的时间
     */
    private LocalDateTime logisticsTime;

    /**
     * 快递单当前签收状态，包括0在途中、1已揽收、2疑难、3已签收、4退签、5同城派送中、6退回、7转单等7个状态，其中4-7需要另外开通才有效  (快递100接口提供)
     */
    private String status;

    //************最新一条快递信息end**************

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
