package cn.htdt.orderprocess.dto.response.delivery;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/7/19
 * @Param
 * @return
 **/
@Data
public class ResSoDeliveryDataAndStatusDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 快递单号
     */
    @ApiModelProperty(value = "快递单号")
    private String deliveryNumber;

    @ApiModelProperty(value = "快递公司编码")
    private String expressDeliveryCode;

    @ApiModelProperty(value = "1000:自提 1101:店铺配送 1102:快递配送")
    private String deliveryType;

    @ApiModelProperty(value = "商品图片")
    private String goodsPicPath;

    /**
     * 快递描述信息
     */
    @ApiModelProperty(value = "快递描述信息")
    private String remark;

    /**
     * 快递记录对应的时间
     */
    @ApiModelProperty(value = "快递记录对应的时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime logisticsTime;

    /**
     * 快递单当前签收状态，包括0在途中、1已揽收、2疑难、3已签收、4退签、5同城派送中、6退回、7转单等7个状态，其中4-7需要另外开通才有效  (快递100接口提供)
     */
    @ApiModelProperty(value = "快递单当前签收状态，包括0在途中、1已揽收、2疑难、3已签收、4退签、5同城派送中、6退回、7转单等7个状态，其中4-7需要另外开通才有效  (快递100接口提供)")
    private String status;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
