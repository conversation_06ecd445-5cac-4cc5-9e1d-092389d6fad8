package cn.htdt.orderprocess.dto.response.orderdef;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2020-08-06
 * @Description
 **/
@Data
public class ResOrderProcessDefDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 流程定义编号
     */
    private String orderProcessNo;

    /**
     * 订单类型，字典ORDER_TYPE
     */
    private String orderType;

    /**
     * 1：提交订单后处理
     * 2：支付前校验（订单列表、订单详情 APP“去支付”按钮时校验）
     * 3：支付成功后处理
     */
    private Integer orderProcessType;

    /**
     * 流程节点排序
     */
    private Integer orderProcessSort;

    /**
     * 事件类型名称
     */
    private String eventTypeName;

    /**
     * 事件类型编码
     */
    private String eventTypeCode;

    /**
     * 事件名称
     */
    private String eventName;

    /**
     * 事件编码
     */
    private String eventCode;

    /**
     * 是否需要回滚 1：否 2：是
     */
    private Integer callBackFlag;

    /**
     * 锁定时长，分钟数
     */
    private Integer lockTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}