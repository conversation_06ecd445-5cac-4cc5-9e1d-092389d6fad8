package cn.htdt.orderprocess.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021-12-03
 * @description 查询冲减明细响应DTO
 **/
@Data
public class ResDeductDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "佣金")
    private BigDecimal rewardValue;

    @ApiModelProperty(value = "退款金额")
    private BigDecimal returnAmount;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
