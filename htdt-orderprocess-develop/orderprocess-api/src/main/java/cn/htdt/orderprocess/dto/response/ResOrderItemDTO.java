package cn.htdt.orderprocess.dto.response;

import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import cn.htdt.orderprocess.dto.request.market.ReqGoodsVipPriceDTO;
import cn.htdt.orderprocess.dto.response.delivery.ResOrderDeliveryDTO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 订单商品行
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-31
 *

 *
 * 商品单位 unit
 * 202503 东启
 */
@Data
@ApiModel
public class ResOrderItemDTO implements Serializable {





    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private String orderNo;

    /**
     * 订单明细编号
     */
    private String orderItemNo;

    /**
     * 行编号
     */
    private String itemNo;

    /**
     * 父行编号
     */
    private String parentItemNo;

    /**
     * 下单人编号
     */
    private String buyerNo;

    /**
     * 代理人编号
     */
    private String agentNo;

    /**
     * 分销店对应商户编号
     */
    private String distributionMerchantNo;

    /**
     * 分销店铺编号
     */
    private String distributionStoreNo;

    /**
     * 门店编号
     */
    private String storeNo;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 归属平台编号
     */
    private String companyNo;

    /**
     * 产品ID
     */
    private String productNo;

    /**
     * 商品编号
     */
    private String goodsNo;

    /**
     * 商品类型 1001-实物商品;1002-虚拟商品;1003-服务商品（仅供平台使用）;1004-称重商品
     */
    private String goodsType;

    /**
     * 虚拟商品类型 1001-代金券，1002-计次卡
     */
    private String virtualGoodsType;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 菜品类型 1001:主菜 1002:加料
     */
    private String foodsType;

    /**
     * 订单商品状态 字典ITEM_STATUS
     */
    private String itemStatus;

    /**
     * 仓库编号
     */
    private String warehouseNo;

    /**
     * 仓库类型
     */
    private Integer warehouseType;

    /**
     * 仓库名
     */
    private String warehouseName;

    /**
     * 仓库编码，手动输入的
     */
    private String warehouseCode;

    /**
     * 合同编码
     */
    private String contractCode;

    /**
     * 结算方式
     */
    private String settleMethod;

    /**
     * 订单标识，字典OrderFlagEnum
     */
    private Integer orderFlag;

    /**
     * 商品标签，字典GoodsLabelEnum
     * 多个则用“,”分割
     */
    private String goodsLabel;

    /**
     * 促销编号
     * 多个则用“,”分割
     */
    private String promotionsNo;

    /**
     * 商品总金额
     */
    private BigDecimal goodsItemAmount;

    /**
     * 商品购买数量
     */
    private BigDecimal goodsItemNum;

    /**
     * 商品重量展示
     */
    private BigDecimal goodsItemNumTable;


    /**
     * 商品重量（称重商品需求新增）
     */
    private String goodsWeight;

    /**
     * 商品应收总金额
     */
    private BigDecimal goodsItemShouldAmount;

    /**
     * 商品实付金额
     */
    private BigDecimal goodsItemRealAmount;

    /**
     * 产品中文名
     */
    private String productName;

    /**
     * 商品图片URL
     */
    private String goodsPicPath;

    /**
     * 商品图片角标URL
     */
    private String superscriptPictureUrl;

    /**
     * 商品销售类型 1普通、2海购、3精品、4赠品
     */
    private Integer goodsSaleType;

    /**
     * 商品原始价
     */
    private BigDecimal goodsPriceOriginal;

    /**
     * 商品市场价
     */
    private BigDecimal goodstPriceMarket;

    /**
     * 商品零售价
     */
    private BigDecimal goodsPriceSale;

    /**
     * 行费税
     */
    private BigDecimal goodsTaxAmount;

    /**
     * 0,普通 2积分兑换 3 抽奖 4 满赠
     */
    private Integer buyType;

    /**
     * 产品类型 0:普通产品 1:主系列产品 2:子系列产品 3:捆绑产品 4:实物礼品卡 5:虚拟商品 7:电子礼品卡
     */
    private Integer productType;

    /**
     * 商品赠送对应总积分
     */
    private BigDecimal pmGivePoints;

    /**
     * 冻结的虚拟库存数量
     */
    private BigDecimal frozenVirtalStockNum;

    /**
     * 产品毛重
     */
    private BigDecimal productGrossWeight;

    /**
     * 父订单编号
     */
    private String parentOrderNo;

    /**
     * 改价前商品总金额
     */
    private BigDecimal productItemBeforeAmount;

    /**
     * 商品编码
     */
    private String code;

    /**
     * 第三方商品编码
     */
    private String thirdMerchantProductCode;

    /**
     * 计量单位
     */
    private String unit;

    /**
     * 销售单位
     */
    private String saleUnit;

    /**
     * 产地(国)
     */
    private String placeOfOrigin;

    /**
     * 规格
     */
    private String standard;

    /**
     * 材质
     */
    private String material;

    /**
     * 扩展信息，以json形式存储
     */
    private String extInfo;

    /**
     * 改价前商品单价
     */
    private BigDecimal productPriceBeforeFinal;

    /**
     * 评论状态 1:未评论 2:已评论
     */
    private Integer commentStatus;

    /**
     * 虚品ID（mpId此时是子品）
     */
    private String seriesParentNo;

    /**
     * 品牌id
     */
    private String brandNo;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 类目id
     */
    private String categoryNo;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 全局类目id，使用-分割
     */
    private String wholeCategoryNo;

    /**
     * 全局类目名称，使用%%分割
     */
    private String wholeCategoryName;

    /**
     * 组合商品id
     */
    private String relationMpNo;

    /**
     * 商品类型    (1普通商品、2卡券、5生鲜产品、6增值服务、7其他)
     */
    private Integer type;

    /**
     * 条码
     */
    private String barCode;

    /**
     * 货号
     */
    private String artNo;

    /**
     * 未转do数量
     */
    private BigDecimal unDoNum;

    /**
     * 未转so_package数量
     */
    private BigDecimal unDeliveryNum;

    /**
     * 行号
     */
    private Integer lineNum;

    /**
     * 商品销售单价
     */
    private BigDecimal productPriceFinal;

    /**
     * 套餐编码
     */
    private String setmealCode;

    /**
     * 服务员编码
     */
    private String waiterCode;

    /**
     * 套餐名称
     */
    private String setmealName;

    /**
     * 是否支持开票
     */
    private Integer supportInvoice;

    /**
     * 套餐数量
     */
    private Integer setmealNum;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    //hxg 订单详情用
    /**
     * 退单状态 字典SO_RETURN_STATUS
     */
    private String returnStatus;

    /**
     * 退款单状态，字典REFUNDMENT_STATUS
     */
    private String refundmentStatus;

    /**
     * 售后商品数量
     */
    private BigDecimal returnProductItemNum;

    /**
     * 申请售后次数
     */
    private int returnCount;

    /**
     * 发货信息
     */
    private List<ResOrderDeliveryDTO> soDeliveryList;

    /**
     * 是否启用串码(1:否 2:是)
     */
    private Integer imeiFlag;

    /**
     * 是否入仓:(1:否 2:是)
     */
    private Integer warehouseFlag;

    /**
     * 商品仓库列表
     */
    private List<ResOrderGoodsDTO.GoodsWarehouse> goodsWarehouseList;

    /**
     * 商品仓库信息
     */
    @Data
    public class GoodsWarehouse implements Serializable {

        private static final long serialVersionUID = 3452193286745066611L;

        /**
         * 仓库编码
         */
        private String warehouseNo;

        /**
         * 仓库名称
         */
        private String warehouseName;
    }

    /**
     * 商品销售量
     */
    private BigDecimal goodsItemNumCount;

    /**
     * 汇通数科编号
     */
    private String htskNo;

    /**
     * 汇通达编号
     */
    private String htdNo;

    /**
     * 餐饮订单行备注
     */
    private String foodsRemarks;

    /**
     * 商品已售数量
     **/
    private Integer goodsSaleNum;

    /**
     * 特惠促销活动信息
     */
    private ReqGoodsVipPriceDTO goodsVipPriceDTO;

    /**
     * vip会员活动信息
     */
    private ReqGoodsVipPriceDTO goodsMemberPriceDTO;

    /**
     * 物流发货总件数
     */
    @ApiModelProperty(value = "物流发货总件数")
    private BigDecimal goodsNumShip;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}