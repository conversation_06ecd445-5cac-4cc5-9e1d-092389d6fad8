package cn.htdt.orderprocess.dto.response.payproject;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@Builder
public class ResPayProjectBuyTips implements Serializable {
    private static final long serialVersionUID = 2330530160285289662L;
    @ApiModelProperty("订单编号")
    private String dealsNo;

    @ApiModelProperty("产品编码")
    private String projectKitNo;

    @ApiModelProperty("场景码")
    private String code;

    @ApiModelProperty("提示信息")
    private String showMsg;

    @ApiModelProperty("按钮显示文案")
    private String buttonText;
}
