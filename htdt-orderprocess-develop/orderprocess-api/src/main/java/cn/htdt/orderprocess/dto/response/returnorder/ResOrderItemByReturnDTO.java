package cn.htdt.orderprocess.dto.response.returnorder;

import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *  hxg订单数据回显
 * <AUTHOR>
 */
@Data
public class ResOrderItemByReturnDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "行编号")
    private String itemNo;

    @ApiModelProperty(value = "商品图片URL")
    private String goodsPicPath;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "规格")
    private String standard;

    @ApiModelProperty(value = "材质")
    private String material;

    @ApiModelProperty(value = "扩展信息，以json形式存储")
    private String extInfo;

    @ApiModelProperty(value = "商品零售价")
    private BigDecimal goodsPriceSale;

    @ApiModelProperty(value = "商品实付金额")
    private BigDecimal goodsItemRealAmount;

    @ApiModelProperty(value = "商品购买数量")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal goodsItemNum;

    @ApiModelProperty(value = "售后商品数量")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal returnProductItemNum;

    @ApiModelProperty(value = "订单商品状态code")
    private String itemStatus;

    @ApiModelProperty(value = "订单商品状态name")
    private String itemStatusName;

    @ApiModelProperty(value = "运费")
    private BigDecimal deliveryFee;

    @ApiModelProperty(value = "是否支持发起售后 1.不支持 2 支持")
    private Integer returnFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
