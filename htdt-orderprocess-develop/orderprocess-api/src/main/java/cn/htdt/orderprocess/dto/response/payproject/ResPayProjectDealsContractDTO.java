package cn.htdt.orderprocess.dto.response.payproject;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 产品订单合同表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-23
 */
@Data
public class ResPayProjectDealsContractDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品订单编号
     */
    private String dealsNo;

    /**
     * 产品唯一编号
     */
    private String projectKitNo;

    /**
     * 合同编码(合同系统)
     */
    private String contractNo;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 流程ID（合同系统）
     */
    private String requestId;

    /**
     * 合同链接地址
     */
    private String contractUrl;

    /**
     * 合同类型  1:套餐 2:增值
     */
    private Integer contractType;


}
