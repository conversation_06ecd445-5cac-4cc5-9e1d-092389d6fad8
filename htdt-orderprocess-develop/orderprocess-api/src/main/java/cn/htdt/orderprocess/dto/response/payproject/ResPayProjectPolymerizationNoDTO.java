package cn.htdt.orderprocess.dto.response.payproject;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 产品表入参
 * @Date 2021/8/2
 * @Param
 * @return
 **/
@Data
public class ResPayProjectPolymerizationNoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("产品唯一编码")
    private String projectKitNo;

    @ApiModelProperty("产品名称")
    private String projectName;

    @ApiModelProperty("产品聚合唯一编码")
    private String polymerizationNo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
