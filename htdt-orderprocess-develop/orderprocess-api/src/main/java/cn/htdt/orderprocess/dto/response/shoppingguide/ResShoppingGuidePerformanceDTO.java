package cn.htdt.orderprocess.dto.response.shoppingguide;

import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/4/4 13:59
 */
@Data
public class ResShoppingGuidePerformanceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "导购员姓名")
    private String shoppingGuideName;

    @ApiModelProperty(value = "订单数")
    private Integer orderNum;

    @ApiModelProperty(value = "商品总额")
    private BigDecimal totalGoodsAmount;

    @ApiModelProperty(value = "优惠总额")
    private BigDecimal totalDiscountAmount;

    @ApiModelProperty("应付总额")
    private BigDecimal totalShouldAmount;

    @ApiModelProperty("已付总额")
    private BigDecimal totalRealAmount;

    @ApiModelProperty("橙豆支付总额")
    private BigDecimal totalCoinAmount;

    @ApiModelProperty("欠款总额")
    private BigDecimal totalArrearsAmount;

    @ApiModelProperty("提成比例")
    private BigDecimal commissionRatio;

    @ApiModelProperty("提成金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal commissionAmount;
}
