package cn.htdt.orderprocess.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/8/12
 */
@Data
public class ResOrderAmountDTO implements Serializable {

    @ApiModelProperty(value = "总应付金额")
    private BigDecimal totalShouldAmount;

    @ApiModelProperty(value = "总欠款金额")
    private BigDecimal totalArrearsAmount;
}
