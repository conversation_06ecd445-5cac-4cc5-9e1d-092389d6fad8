package cn.htdt.orderprocess.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单评价响应DTo
 *
 * <AUTHOR>
 */
@Data
public class ResOrderAssessDTO extends ResOrderGoodsDTO implements Serializable {

    private static final long serialVersionUID = 1728952824179366925L;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "商品评分")
    private Integer score;

    @ApiModelProperty(value = "商品评价内容")
    private String assessContent;

    @ApiModelProperty(value = "商品追加评价内容")
    private String addAssessContent;

    @ApiModelProperty(value = "评论状态 1:未评论 2:已评论 3已追评")
    private Integer commentStatus;

    @ApiModelProperty(value = "评价创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "追评时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modifyTime;

    @ApiModelProperty(value = "下单人编码")
    private String buyerNo;

    @ApiModelProperty(value = "订单明细评价媒体")
    private List<ResOrderAssessMediaDTO> orderAssessMediaDTOList;

    @ApiModelProperty(value = "追加评价媒体")
    private List<ResOrderAssessMediaDTO> orderAddAssessMediaDTOList;
}
