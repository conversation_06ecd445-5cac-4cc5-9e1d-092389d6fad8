package cn.htdt.orderprocess.dto.response.payproject;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Description 关联搭配产品
 * @Date 2021/8/2
 * @Param
 * @return
 **/
@Data
public class ResPayProjectKitJoinDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("产品唯一编码")
    private String projectKitNo;

    @ApiModelProperty("类型： 1:会员套餐 2:增值服务")
    private Integer projectTypeJoin;

    @ApiModelProperty("关联的唯一编码")
    private String projectKitNoJoin;

    @ApiModelProperty("是否已经删除，默认1未删除，其余已删除")
    private Integer disableFlag;

    @ApiModelProperty("1:关联产品  2:搭配产品  JoinStatusEnum")
    private Integer joinStatus;

    @ApiModelProperty("关联产品对应的合同")
    private String projectContract;

    @ApiModelProperty("对应产品的排序值")
    private Integer sortNum;

    @ApiModelProperty("搭配专属优惠价")
    private BigDecimal packageDiscountAmount;

    @ApiModelProperty("搭配产品对应的原价")
    private BigDecimal originalPrice;

    @ApiModelProperty("搭配产品对应的图片")
    private String projectImg;

    @ApiModelProperty("产品名称(回显时使用)")
    private String projectName;

    private String createNo;
    private String createName;
    private String modifyNo;
    private String modifyName;

    @ApiModelProperty("开始日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate projectStart;

    @ApiModelProperty("结束日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate projectEnd;

    @ApiModelProperty("产品简介")
    private String projectIntroduction;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
