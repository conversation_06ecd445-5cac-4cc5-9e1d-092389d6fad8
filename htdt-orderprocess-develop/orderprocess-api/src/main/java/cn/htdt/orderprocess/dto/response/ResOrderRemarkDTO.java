package cn.htdt.orderprocess.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 订单备注信息
 *
 * <AUTHOR>
 */
@Data
public class ResOrderRemarkDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = 6759300644278944424L;

    /**
     * 订单备注(买家)
     */
    @ApiModelProperty(value = "粉丝备注")
    private String orderRemarkBuyer;

    /**
     * 订单备注(商家或店铺自己看的)
     */
    @ApiModelProperty(value = "商家备注")
    private String orderRemarkMerchant;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
