package cn.htdt.orderprocess.dto.response;

import cn.htdt.common.dto.response.ExecutePageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 分页查询销量报表响应DTO
 *
 * <AUTHOR>
 * @date 2022-10-25
 **/
@Data
public class ResSaleReportFormDTO implements Serializable {

    private static final long serialVersionUID = 1;

    @ApiModelProperty(value = "开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endTime;

    @ApiModelProperty(value = "销售报表明细")
    private ExecutePageDTO<ResSaleReportFormListDTO> saleReportFormList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
