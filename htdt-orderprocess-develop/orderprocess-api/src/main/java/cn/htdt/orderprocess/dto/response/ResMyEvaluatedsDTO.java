package cn.htdt.orderprocess.dto.response;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 我的评价
 * @Date 2021/7/5
 * @Param
 * @return
 **/
@Data
public class ResMyEvaluatedsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 商家编号
     **/
    private String merchantNo;

    /**
     * 店铺编号
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 购买人编号
     */
    private String buyerNo;

    /**
     * 商品明细
     */
    private List<ResMyEvaluatedsItemDTO> itemDTOS;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }


}
