package cn.htdt.orderprocess.dto.response.orderdef;

import cn.htdt.common.dto.BigDecimalSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 经营数据二级页面返回数据
 *
 * <AUTHOR>
 * @since 2023-02-10
 */
@Data
public class ResOrderSourceDealAndAmount implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("各个渠道的订单数")
    private int totalOrderNum = 0;

    @ApiModelProperty("汇享购订单数")
    private int hxgOrderNum = 0;

    @ApiModelProperty("汇享购订单占比")
    private String hxgOrderRate = "0";

    @ApiModelProperty("千橙掌柜APP订单数")
    private int bossAppOrderNum = 0;

    @ApiModelProperty("千橙掌柜APP订单占比")
    private String bossAppOrderRate = "0";

    @ApiModelProperty("千橙掌柜PC订单数")
    private int bossPcOrderNum = 0;

    @ApiModelProperty("千橙掌柜PC订单占比")
    private String bossPcOrderRate = "0";

    @ApiModelProperty("千橙掌柜收银订单数")
    private int smartCashierOrderNum = 0;

    @ApiModelProperty("千橙掌柜收银订单占比")
    private String smartCashierOrderRate = "0";

    @ApiModelProperty("成交金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal shouldAmount = BigDecimal.ZERO;

    @ApiModelProperty("现金支付")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal cashPaymentAmount = BigDecimal.ZERO;

    @ApiModelProperty("现金支付金额占比")
    private String cashPaymentAmountRate = "0";

    @ApiModelProperty("线上支付")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal onlinePaymentAmount = BigDecimal.ZERO;

    @ApiModelProperty("线上支付金额占比")
    private String onlinePaymentAmountRate = "0";

    @ApiModelProperty("欠款")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal arrearsAmount = BigDecimal.ZERO;

    @ApiModelProperty("欠款支付金额占比")
    private String arrearsAmountRate = "0";

    @ApiModelProperty("各sku的购买数量的汇总")
    private int goodsItemNum = 0;

    @ApiModelProperty("动销商品数")
    private int distinctGoodsNum = 0;

    @ApiModelProperty("动销商品率")
    private String distinctGoodsRate = "0";

    @ApiModelProperty("退款成功金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal refundmentAmount = BigDecimal.ZERO;

    @ApiModelProperty("退款成功的订单占比")
    private String refundmentAmountRate = "0";
}
