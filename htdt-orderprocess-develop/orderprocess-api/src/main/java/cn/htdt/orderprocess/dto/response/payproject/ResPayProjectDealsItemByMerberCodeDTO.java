package cn.htdt.orderprocess.dto.response.payproject;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 企微 商家详情返回值
 * @Date 2022/6/14
 * <AUTHOR>
 **/
@Data
public class ResPayProjectDealsItemByMerberCodeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("订单编号")
    private String dealsNo;

    @ApiModelProperty("产品名称")
    private String projectName;

    @ApiModelProperty("下单时间")
    private LocalDateTime createTime;

    @ApiModelProperty("所属平台")
    private String companyName;

    @ApiModelProperty("订单类型")
    private Integer dealsType;

    @ApiModelProperty("订单标签")
    private Integer productLabell;

    @ApiModelProperty("是否试用")
    private Integer packageRetail;

    @ApiModelProperty("订单状态")
    private Integer dealsStatus;

    @ApiModelProperty("推荐No")
    private String recommendNo;

    @ApiModelProperty("推荐人")
    private String recommendName;

    @ApiModelProperty("产品单价")
    private BigDecimal originalPrice;

    @ApiModelProperty("支付金额")
    private BigDecimal payAmount;

    @ApiModelProperty("支付时间")
    private LocalDateTime payTime;

    @ApiModelProperty("产品状态")
    private String dealsProjectStatus;

    @ApiModelProperty("产品有效期开始")
    private LocalDate projectStart;

    @ApiModelProperty("产品有效期结束")
    private LocalDate projectEnd;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
