package cn.htdt.orderprocess.dto.response.payproject;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description 产品表
 * @Date 2021/8/2
 * @Param
 * @return
 **/
@Data
public class ResPayProjectKitNoDTO implements Serializable {

    @ApiModelProperty("产品唯一编码")
    private String projectKitNo;

    @ApiModelProperty("类型： 1:会员套餐 2:增值服务")
    private Integer projectType;

    @ApiModelProperty("产品名称")
    private String projectName;

    @ApiModelProperty("产品图片")
    private String projectImg;

    @ApiModelProperty("产品聚合唯一编码")
    private String polymerizationNo;

    @ApiModelProperty("产品聚合排序")
    private Integer polymerizationSort;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
