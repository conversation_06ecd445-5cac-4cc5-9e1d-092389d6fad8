package cn.htdt.orderprocess.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品信息响应DTO
 *
 * <AUTHOR>
 */
@Data
public class ResOrderGoodsDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = -4238176093615810763L;

    /**
     * 行编号
     */
    @ApiModelProperty(value = "行编号")
    private String itemNo;

    /**
     * 商品编号
     */
    @ApiModelProperty("商品编号")
    private String goodsNo;

    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String goodsName;

    /**
     * 商品图片
     */
    @ApiModelProperty("商品图片")
    private String goodsPicPath;

    /**
     * 商品购买数量
     */
    @ApiModelProperty("商品购买数量")
    private BigDecimal goodsItemNum;

    /**
     * 待发货商品数量
     */
    @ApiModelProperty("待发货商品数量")
    private BigDecimal waitDeliveryNum;

    /**
     * 扩展信息（规格属性）
     */
    @ApiModelProperty("扩展信息（规格属性）")
    private String extInfo;

    /**
     * 是否启用串码
     */
    @ApiModelProperty("是否启用串码1:否 2:是")
    private Integer imeiFlag;
    /*
    * 是否入库
    * */
    @ApiModelProperty("是否入库")
    private Integer goodsWarehouseFlag;
    /**
     * 当前发货的商品件数
     */
    @ApiModelProperty("当前发货的商品件数")
    private BigDecimal goodsNumShip;

    /**
     * 商品零售价
     */
    @ApiModelProperty("商品零售价")
    private BigDecimal goodsPriceSale;

    /**
     * 商品图片角标URL
     */
    @ApiModelProperty("商品图片角标URL")
    private String tagPictureUrl;

    /**
     * 商品仓库列表
     */
    @ApiModelProperty("商品仓库列表")
    private List<GoodsWarehouse> goodsWarehouseList;

    /**
     * 商品类型
     */
    @ApiModelProperty("商品类型,2:临时商品")
    private Integer type;

    /**
     * 商品仓库信息
     */
    @Data
    public class GoodsWarehouse implements Serializable {

        private static final long serialVersionUID = 3452193286745066611L;

        /**
         * 仓库编码
         */
        @ApiModelProperty("仓库编码")
        private String warehouseNo;

        /**
         * 仓库名称
         */
        @ApiModelProperty("仓库名称")
        private String warehouseName;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
