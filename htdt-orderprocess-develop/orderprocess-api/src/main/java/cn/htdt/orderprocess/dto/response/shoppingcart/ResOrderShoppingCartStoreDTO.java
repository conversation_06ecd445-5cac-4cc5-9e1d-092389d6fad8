package cn.htdt.orderprocess.dto.response.shoppingcart;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020-10-12
 * @Description 购物车表出参
 **/
@Data
public class ResOrderShoppingCartStoreDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "分销店编码")
    private String distributionStoreNo;

    @ApiModelProperty(value = "分销店名称")
    private String distributionStoreName;

    @ApiModelProperty(value = "是否可领券 1否 2是")
    private Integer receiveCouponFlag;

    @ApiModelProperty(value = "是否选中 1否 2是")
    private Integer checkFlag;

    @ApiModelProperty(value = "商品集合")
    private List<ResOrderShoppingCartGoodsDTO> listGoods;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}