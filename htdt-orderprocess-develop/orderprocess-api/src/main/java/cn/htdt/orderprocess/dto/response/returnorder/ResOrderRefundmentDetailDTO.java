package cn.htdt.orderprocess.dto.response.returnorder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 退款明细
 */
@Data
public class ResOrderRefundmentDetailDTO implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 售后单编号
     */
    private String soReturnNo;

    /**
     * 退款单号
     */
    private String refundmentNo;

    /**
     * 退款金额
     */
    private BigDecimal amount;

    /**
     * 退款渠道 字典PAYMENT_CHANNEL
     */
    private String refundmentChannel;

    /**
     * 退款单状态 字典REFUNDMENT_STATUS
     */
    private String refundmentStatus;

    /**
     * 退单方式 字典REFUNDMENT_WAY
     */
    private String refundmentWay;

    /**
     * 退款时间
     */
    private LocalDateTime refundmentTime;

    /**
     * 1:订单取消退款 2:退货退款 3:仅退款 4:删除商品 5:换货
     */
    private String refundmentType;

    /**
     * 支付交易号
     */
    private String outTradeNo;

    //老板手动退款图片
    private List<String> returnPics;

    @ApiModelProperty(value = "售后类型 1000仅退款未发货 1001仅退款已发货 1002退款退货")
    private String returnType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
