package cn.htdt.orderprocess.dto.response.returnorder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 查询订单明细DTO
 *
 * <AUTHOR>
 */
@Data
public class ResReturnOrderItemDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = 3558137594954766470L;

    /**
     * 行编号
     */
    private String itemNo;

    /**
     * 商品编号
     */
    private String goodsNo;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品主图
     */
    private String goodsPicPath;

    /**
     * 商品扩展信息
     */
    private String extInfo;

    /**
     * 商品购买数量
     */
    private BigDecimal goodsItemNum;

    /**
     * 售后商品数量
     */
    private BigDecimal returnProductItemNum;

    /**
     *  退单状态,字典SO_RETURN_STATUS
     */
    private String returnStatus;

    /**
     * 商品实付金额
     */
    private BigDecimal goodsItemRealAmount;

    /**
     * 商品销售单价
     */
    private BigDecimal goodsPriceSale;

    /**
     * 汇通数科编号
     */
    private String htskNo;

    /**
     * 汇通达编号
     */
    private String htdNo;

}
