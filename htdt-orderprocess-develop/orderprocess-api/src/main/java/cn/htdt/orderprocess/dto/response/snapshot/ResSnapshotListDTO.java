package cn.htdt.orderprocess.dto.response.snapshot;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/9/20 15:23
 */
@Data
public class ResSnapshotListDTO implements Serializable {

    @ApiModelProperty(value = "商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "商品类型 1001-实物商品;1002-虚拟商品;1003-服务商品;1004-称重商品")
    private String goodsType;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品购买数量")
    private BigDecimal goodsItemNum;

    @ApiModelProperty(value = "商品重量")
    private String goodsWeight;

    @ApiModelProperty(value = "商品图片URL")
    private String goodsPicPath;

    @ApiModelProperty(value = "商品市场价")
    private BigDecimal goodstPriceMarket;

    @ApiModelProperty(value = "商品销售价")
    private BigDecimal goodsPriceSale;

    @ApiModelProperty(value = "规格/型号")
    private String extInfo;

}

