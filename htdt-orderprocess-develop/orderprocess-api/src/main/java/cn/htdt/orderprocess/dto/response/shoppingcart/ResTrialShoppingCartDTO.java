package cn.htdt.orderprocess.dto.response.shoppingcart;

import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 购物车试算
 *
 * <AUTHOR>
 */
@Data
public class ResTrialShoppingCartDTO implements Serializable {

    private static final long serialVersionUID = -144366835841957968L;

    @ApiModelProperty("合计金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal totalPrice = BigDecimal.ZERO;

    @ApiModelProperty("优惠金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal discountPrice = BigDecimal.ZERO;

    @ApiModelProperty("选中数量")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal checkedNum = BigDecimal.ZERO;

    @ApiModelProperty("优惠明细")
    private DiscountDetail discountDetail;

    /**
     * 优惠明细
     */
    @Data
    public static class DiscountDetail implements Serializable{

        private static final long serialVersionUID = -2476548328448707240L;

        @ApiModelProperty("商品金额")
        @JsonSerialize(using = BigDecimalSerialize.class)
        private BigDecimal goodsPrice;

        @ApiModelProperty("运费")
        @JsonSerialize(using = BigDecimalSerialize.class)
        private BigDecimal deliveryPrice;

        @ApiModelProperty("优惠券优惠金额")
        @JsonSerialize(using = BigDecimalSerialize.class)
        private BigDecimal couponDiscountPrice;
    }
}
