package cn.htdt.orderprocess.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2021-7-1
 * @Description 代办订单信息
 **/
@Data
public class ResPendingOrderDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 订单编号
     */
    @ApiModelProperty("订单编号")
    private String orderNo;

    /**
     * 下单人编号
     */
    @ApiModelProperty("下单人编号")
    private String buyerNo;

    /**
     * 售后编号
     */
    @ApiModelProperty("售后编号")
    private String soReturnNo;

    /**
     * 拼接商品名称
     */
    @ApiModelProperty("拼接商品名称")
    private String goodsNameConcat;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 商品图片URL
     */
    @ApiModelProperty("商品图片URL")
    private String goodsPicPath;

    /**
     * 订单状态 字典ORDER_STATUS
     */
    @ApiModelProperty("订单状态")
    private String orderStatus;

    /**
     * 退单状态 字典SO_RETURN_STATUS
     */
    @ApiModelProperty("退单状态")
    private String returnStatus;

    /**
     *  退款单状态,字典REFUNDMENT_STATUS
     */
    @ApiModelProperty("退款单状态")
    private String refundmentStatus;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);}
}