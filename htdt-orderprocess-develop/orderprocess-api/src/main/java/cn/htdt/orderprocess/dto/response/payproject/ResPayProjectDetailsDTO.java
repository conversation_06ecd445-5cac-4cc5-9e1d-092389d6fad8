package cn.htdt.orderprocess.dto.response.payproject;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-08-10
 * @description 付费产品订单详情响应DTO
 **/
@Data
public class ResPayProjectDetailsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    private String dealsNo;

    /**
     * 下单时间时间
     */
    //@ApiModelProperty("创建时间")
    //@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 产品服务生效时间
     */
    private LocalDate serviceStart;

    /**
     * 产品服务失效时间
     */
    private LocalDate serviceEnd;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * htd账号
     */
    private String merberCode;

    /**
     * 归属平台公司名称
     */
    private String companyName;

    /**
     * 归属分部名称
     */
    private String divisionName;

    /**
     * 订单类型1：线上 2：线下 3：赠送
     */
    private Integer dealsType;

    /**
     * 推荐人类型
     */
    private String recommendType;

    /**
     * 推荐人编号
     */
    private String recommendNo;

    /**
     * 推荐人名称
     */
    private String recommendName;

    /**
     * 订单状态 0：未支付 1：支付成功 2：支付失败 4：已关闭
     */
    private Integer dealsStatus;

    /**
     * 是否零售版 0：零售 1：免费
     */
    private Integer packageRetail;

    /**
     * 付款时间
     */
    private LocalDateTime payTime;
    /**
     * 实付金额
     */
    private BigDecimal payAmount;

    /**
     * 支付返回类型 (code)对应PaymentChannelDetailsEnum
     */
    private String payType;

    //支付状态

    /**
     * 订单商品详情
     */
    private List<ResPayProjectDealsItemDTO> projectDealsItemDTOS;

    /**
     * 订单操作记录
     */
    private List<ResPayProjectDealsRecordDTO> recordDTOS;

    /**
     * 发票信息
     */
    private ResPayProjectDealsDetailsDTO payProjectDealsDetailsDTO;

    /**
     * 返回成功的合同列表
     */
    List<ResPayProjectDealsContractDTO> resPayProjectDealsContractDTOS;


    /**
     * 合同状态 12:线上两方合同 13:线上3方合同 2:线下
     **/
    private Integer contractState;

    /**
     * 是否是滴灌通套餐 1:否 2：是
     */
    private Integer mciFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
