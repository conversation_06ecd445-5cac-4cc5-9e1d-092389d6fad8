package cn.htdt.orderprocess.dto.response.payproject;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 产品订单操作记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-10
 */
@Data
public class ResPayProjectDealsRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 操作人手机号
     */
    private String userTel;

    /**
     * 操作内容
     */
    private String content;

    /**
     * 附件
     */
    private String enclosure;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 订单编号
     */
    private String dealsNo;

    /**
     * 操作人姓名
     */
    private String createName;

    /**
     * 操作人编号
     */
    private String createNo;

    /**
     * 操作时间
     */
    private LocalDateTime createTime;
    /**
     * 退款金额
     */
    private BigDecimal refundPrice;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
