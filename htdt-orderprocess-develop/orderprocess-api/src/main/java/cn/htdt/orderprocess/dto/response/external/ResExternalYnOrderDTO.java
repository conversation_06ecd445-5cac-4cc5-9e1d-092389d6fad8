package cn.htdt.orderprocess.dto.response.external;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-07-28
 * @Description 一体机羊乃粉丝订单查看，目前是KA-羊乃项目使用
 **/
@Data
public class ResExternalYnOrderDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 对应内部系统的商户编号
     */
    private String inMerchantNo;

    /**
     * 对应内部系统的商户名称
     */
    private String inMerchantName;

    /**
     * 对应内部系统的店铺编号
     */
    private String inStoreNo;

    /**
     * 对应内部系统的店铺名称
     */
    private String inStoreName;

    /**
     * 订单编码
     */
    private String orderCode;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 订单创建时间
     */
    private Date orderCreateTime;

    /**
     * 订单实收金额
     */
    private BigDecimal orderPayAmount;

    /**
     * 订单行
     */
    List<ResExternalYnOrderItemDTO> listItemVO;
}