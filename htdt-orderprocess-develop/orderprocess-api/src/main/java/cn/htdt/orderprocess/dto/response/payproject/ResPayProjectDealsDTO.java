package cn.htdt.orderprocess.dto.response.payproject;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021-08-05
 * @description 付费产品订单响应DTO
 **/
@Data
public class ResPayProjectDealsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品类型：1:会员套餐 2:增值服务  枚举:ProjectTypeEnum
     */
    private Integer payProjectType;

    /**
     * 付费产品唯一标识，对应PAY_PROJECT_KIT表中的PROJECT_KIT_NO字段
     */
    private String payProjectKitNo;

    /**
     * 订单编号
     */
    private String dealsNo;

    /**
     * 订单类型1：线上 2：线下 3：赠送
     */
    private Integer dealsType;

    /**
     * 是否续费1：否 2：是
     */
    private Integer renewFee;

    /**
     * 订单状态 1：未支付 2：支付成功 3：支付失败 4：已关闭
     */
    private Integer dealsStatus;

    /**
     * 套餐生效时间
     */
    private LocalDate packageStart;

    /**
     * 套餐失效时间
     */
    private LocalDate packageEnd;

    /**
     * 套餐类型 此字段作废
     */
    private Integer packageType;

    /**
     * 套餐金额
     */
    private BigDecimal packageAmount;

    /**
     * 是否零售版 2：零售 1：免费
     */
    private Integer packageRetail;

    /**
     * 实付金额
     */
    private BigDecimal payAmount;

    /**
     * 支付返回类型 (code)对应PaymentChannelDetailsEnum
     */
    private String payType;

    /**
     * 付款时间
     */
    private LocalDateTime payTime;

    /**
     * 合同地址
     */
    private String contract;

    /**
     * 会员编码
     */
    private String memberNo;

    /**
     * 店铺编码
     */
    private String storeNo;

    /**
     * 会员店名称
     */
    private String orgName;

    /**
     * 收货人姓名
     */
    private String represeNtative;

    /**
     * 收货人手机号
     */
    private String represeNtativePhone;

    /**
     * 归属平台公司
     */
    private String CompanyNo;

    /**
     * 归属分布
     */
    private String DivisionNo;

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 下单设备 1：PC 2：IOS 3：Android
     */
    private Integer sourceType;

    /**
     * 短信条数
     */
    private Integer smsCount;

    /**
     * 是否已经更新1：未更新 2：已经更新
     */
    private Integer updateFlag;

    /**
     * 是否开发票：1：不需要发票，2：需要开发票
     */
    private Integer needInvoiceFlag;

    /**
     * 会员服务费
     */
    private BigDecimal payMemberService;

    /**
     * 运维服务费
     */
    private BigDecimal payYwService;

    /**
     * 智能硬件押金
     */
    private BigDecimal payDepositService;

    /**
     * 产品服务生效时间
     */
    private LocalDate serviceStart;

    /**
     * 产品服务失效时间
     */
    private LocalDate serviceEnd;

    /**
     * 是否签订合同  2 ：是 1否
     */
    private String contractFlag;

    /**
     * 支付成功订单号
     */
    private String tradeNo;

    /**
     * 是否已发货 1:已发货 2:已签收
     */
    private Integer goodsStatus;

    /**
     * 是否已经删除，默认1未删除，其余已删除
     */
    private Integer disableFlag;

    /**
     * 原价
     */
    private BigDecimal originalPrice;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 产品标签：1->订阅套餐，2-> 增值套餐
     */
    private Integer productLabell;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 归属平台公司名称
     */
    private String companyName;

    /**
     * 归属分部名称
     */
    private String divisionName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 会员编码
     */
    private String merberCode;

    /**
     * 前端页面需要显示的内容
     */
    private Set<String> sameValueAdded;

    /**
     * 分组时间
     */
    private String groupTime;

    /**
     * 月度订单总数
     */
    private Integer totalOrder;

    /**
     * 月度金额总数
     */
    private BigDecimal totalAmount;

    /**
     * 推荐人名称
     */
    private String recommendName;

    /**
     * 产品生效时间
     */
    private LocalDate projectStart;

    /**
     * 产品失效时间
     */
    private LocalDate projectEnd;

    /**
     * 产品状态
     */
    private String projectStatusStr;

    /**
     * 是否试用
     */

    private Integer tryUseFlag;

    /**
     * 产品编码
     */
    private String projectKitNo;

    /**
     * 产品名称
     */
    private String projectName;

    /**
     * 产品类型
     */
    private Integer projectType;


    /**
     * 协议是否确认 1：否 2：是
     **/
    private Integer agreementConfirmFlag;

    /**
     * 协议确认时间
     **/
    private LocalDateTime confirmTime;

    /**
     * 协议确认人账号
     **/
    private String confirmOperatorid;

    /**
     * 合同状态 12:线上两方合同 13:线上3方合同 2:线下
     **/
    private Integer contractState;

    @ApiModelProperty(value = "是否试用账户 1:(默认)正常账户  2:试用账户")
    private Integer trialAccount;

    /**
     * 核销码
     **/
    private String activationCode;

    /**
     * 赠送权益内容
     **/
    private String giftRightsContent;

    /**
     * 赠送权益状态
     **/
    private String giftRightsStatus;

    /**
     * 活动开始时间
     **/
    private LocalDateTime effectiveTime;

    /**
     * 活动结束时间
     **/
    private LocalDateTime invalidTime;

    /**
     * 短信条数
     **/
    private Integer smsNum;

    /**
     * 天数发放
     **/
    private Integer fateProvide;

    /**
     * 收回短信条数
     **/
    private Integer regainSmsNum;

    /**
     * 是否为滴灌通套餐 1：否 2：是
     */
    private Integer mciFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
