package cn.htdt.orderprocess.dto.response;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2020-12-26
 * @Description 订单设置
 **/
@Data
public class ResOrderSettingDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 店铺编号
     */
    private String storeNo;

    /**
     * 订单是否自动确认。默认2，1：否，2：是 枚举:WhetherEnum
     */
    private Integer orderConfirmFlag;

    /**
     * 订单发货支持的物流方,0:全部;1:店铺配送;2:快递配送
     */
    private Integer logisticsType;

    /**
     * 是否支持分开发货 1否2是
     */
    private Integer allowPartSend;

    /**
     * 是否支持语音播报 1否2是,默认否
     */
    private Integer voiceFlag;
}