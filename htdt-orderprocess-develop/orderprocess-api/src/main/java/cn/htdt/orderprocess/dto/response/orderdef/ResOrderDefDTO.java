package cn.htdt.orderprocess.dto.response.orderdef;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020-08-03
 * @Description
 **/
@Data
public class ResOrderDefDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    public ResOrderDefDTO(String eventTypeName, String eventTypeCode){
        this.setEventTypeName(eventTypeName);
        this.setEventTypeCode(eventTypeCode);
    }

    public ResOrderDefDTO(String eventTypeName, String eventTypeCode, List<ResOrderDefEventDTO> orderDefEvent){
        this.setEventTypeName(eventTypeName);
        this.setEventTypeCode(eventTypeCode);
        this.setOrderDefEvent(orderDefEvent);
    }
    public ResOrderDefDTO(String eventTypeName, String eventTypeCode,int sort){
        this.setEventTypeName(eventTypeName);
        this.setEventTypeCode(eventTypeCode);
        this.setSort(sort);
    }
    private String eventTypeName;

    private String eventTypeCode;

    private int sort;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<ResOrderDefEventDTO> orderDefEvent;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}