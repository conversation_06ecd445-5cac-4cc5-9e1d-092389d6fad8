package cn.htdt.orderprocess.dto.response.returnorder;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class ResExportReturnOrderDTO implements Serializable {

    /**
     * 退单申请时间
     */
    private LocalDateTime applyTime;

    /**
     *
     * 退款单状态 字典REFUNDMENT_STATUS
     */
    private String refundmentStatus;

    /**
     *  售后单状态 ，字典SO_RETURN_STATUS
     */
    private String returnStatus;

    /**
     * 售后编号
     */
    private String soReturnNo;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 售后类型 1000仅退款未发货 1001仅退款已发货 1002退款退货
     */
    private String returnType;

    /**
     * 退款类型 1:订单取消退款 2:退货退款 3:仅退款 4:删除商品 5:换货
     */
    private String refundmentType;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 订单渠道来源 字典ORDER_CHANNEL_SOURCE
     */
    private String orderChannelSource;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店名称
     */
    private String storeName;


    /**
     * 用户申请退款金额
     */
    private BigDecimal applyReturnAmount;

    /**
     * 申请来源
     */
    private String source;

    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 商品购买数量
     */
    private BigDecimal productItemNum;

    /**
     * 售后商品数量
     */
    private BigDecimal returnProductItemNum;

    /**
     * 规格属性
     */
    private String extInfo;

    /**
     * 退还优惠券
     */
    private BigDecimal discount;

    /**
     * 退还积分
     */
    private int orderDepletePoints;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
