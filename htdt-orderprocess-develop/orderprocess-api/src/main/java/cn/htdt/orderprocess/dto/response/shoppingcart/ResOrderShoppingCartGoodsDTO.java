package cn.htdt.orderprocess.dto.response.shoppingcart;

import cn.htdt.common.dto.BigDecimalSerialize;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020-10-12
 * @Description 购物车表出参
 **/
@Data
public class ResOrderShoppingCartGoodsDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("分销店编码")
    private String distributionStoreNo;

    @ApiModelProperty("分销店名称")
    private String distributionStoreName;

    @ApiModelProperty("供货店编码")
    private String storeNo;

    @ApiModelProperty("供货店名称")
    private String storeName;

    @ApiModelProperty("商品编号")
    private String goodsNo;

    @ApiModelProperty("商品零售价")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal retailPrice;

    @ApiModelProperty("商品市场价")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal marketPrice;

    @ApiModelProperty(value = "活动价格")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal promotionPrice;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("商品主图地址")
    private String mainPictureUrl;

    @ApiModelProperty("商品购买数量")
    private BigDecimal goodsItemNum;

    @ApiModelProperty("该区域是否可以销售标识，1：不可销售 2：或者其余都可以销售")
    private Integer sellFlag;

    @ApiModelProperty("商品可销售库存")
    private BigDecimal canSaleStockNum;

    @ApiModelProperty("商品属性集合")
    private String extInfo;

    @ApiModelProperty("商品角标")
    private String superscriptPictureUrl;

    /**
     * 商品状态1001-未上架;1002-审核中;1003-审核失败;1004:已上架;1005:未分发;1006:分发成功;1007:已失效;
     */
    @ApiModelProperty("商品状态，1：未上架，2：或者其余都是已上架")
    private Integer goodsStatus;

    @ApiModelProperty("商品标签，字典GoodsLabelEnum（二期需求，1006：云池）")
    private List<String> listGoodsLabel = new ArrayList<>();

    @JsonProperty("shareAgentNo")
    @ApiModelProperty("代理人编号（二期需求），已中转shareAgentNo")
    private String agentNo;

    @ApiModelProperty("促销活动编号，暂时不用")
    private String promotionsNo;

    @ApiModelProperty(value = "是否选中 1否 2是")
    private Integer checkFlag;

    @ApiModelProperty("商品来源")
    private String goodsSourceType;

    @ApiModelProperty(value = "商家商品编号(商家同步商品原型商品编号)")
    private String merchantNo;

    @ApiModelProperty(value = "商家商品编号(商家同步商品原型商品编号)")
    private String merchantGoodsNo;

    // 2023-08-18蛋品 lixiang  商品管理 多单位商品下单
    @ApiModelProperty(value = "多单位商品类型 1001:主单位商品 1002:子")
    private String multiUnitType;

    @ApiModelProperty(value = "多单位主品编号")
    private String multiUnitGoodsNo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}