package cn.htdt.orderprocess.dto.response.payproject;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 确认下单页返回值
 * @Date 2021/8/9
 * @Param 
 * @return 
 **/
@Data
public class ResPayProjectDealsDetailsConfirmOrderDTO implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    private String dealsNo;

    /**
     * 商户类型
     */
    private String traderType;

    /**
     * 发票抬头
     */
    private String invoiceNotify;

    /**
     * 纳税人识别号
     */
    private String taxManid;

    /**
     * 开户行名称
     */
    private String bankName;

    /**
     * 开户行账号
     */
    private String bankAccount;

    /**
     * 开户行账号
     */
    private String dsBankAccount;

    /**
     * 发票地址
     */
    private String invoiceAddress;

    /**
     * 发票地址
     */
    private String dsInvoiceAddress;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 联系电话
     */
    private String dsContactPhone;

    /**
     * 付款人姓名
     */
    private String receiveName;

    /**
     * 付款人手机号
     */
    private String receivePhone;

    /**
     * 付款人手机号
     */
    private String dsReceivePhone;

    /**
     * 收货门店名称
     */
    private String receiveShopname;

    /**
     * 收货地址
     */
    private String receiveAddress;

    /**
     * 收货地址
     */
    private String dsReceiveAddress;

    /**
     * 推荐会员店名称
     */
    private String recommendNo;

    /**
     * 发票类型
     */
    private String invoiceType;

    /**
     * 备注
     */
    private String bz;

    /**
     * 铁军服务
     */
    private String armynumber;

    /**
     * 是否已经删除，默认1未删除，其余已删除
     */
    private Integer disableFlag;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 发票地址(省)编码
     */
    private String invoiceAddressProvinceCode;

    /**
     * 发票地址(市)编码")
     *
     */
    private String invoiceAddressCityCode;

    /**
     * 发票地址(区)编码")
     *
     */
    private String invoiceAddressAreaCode;

    /**
     * 发票地址(街道)编码
     */
    private String invoiceAddressStreetCode;

    /**
     * 发票地址(省)
     */
    private String invoiceAddressProvince;

    /**
     * 发票地址(市)
     */
    private String invoiceAddressCity;

    /**
     * 发票地址(区)
     */
    private String invoiceAddressArea;

    /**
     * 发票地址(街道)
     */
    private String invoiceAddressStreet;

    /**
     * 发票地址(街道后)
     */
    private String invoiceAddressSuffix;

    /**
     * 发票地址(街道后)
     */
    private String dsInvoiceAddressSuffix;

    /**
     * 合同模板返回值
     */
    List<ResContractUrlListDTO> resPayProjectContractDTOS = Collections.EMPTY_LIST;

    /**
     * 身份 1运营 2商家 4店铺 8单店
     **/
    private Integer identity;

    @ApiModelProperty("试用账户 1-(默认)正常账户；2-试用账户")
    private Integer trialAccount;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
