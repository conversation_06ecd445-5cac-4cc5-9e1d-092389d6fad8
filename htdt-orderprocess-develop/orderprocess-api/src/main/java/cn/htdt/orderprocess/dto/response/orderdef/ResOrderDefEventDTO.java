package cn.htdt.orderprocess.dto.response.orderdef;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2020-08-03
 * @Description
 **/
@Data
public class ResOrderDefEventDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    public ResOrderDefEventDTO(String eventName, String eventCode){
        this.setEventName(eventName);
        this.setEventCode(eventCode);
    }

    private String eventName;

    private String eventCode;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}