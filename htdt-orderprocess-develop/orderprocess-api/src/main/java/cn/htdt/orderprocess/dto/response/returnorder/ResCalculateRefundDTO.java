package cn.htdt.orderprocess.dto.response.returnorder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 退款信息
 *
 * <AUTHOR>
 */
@Data
public class ResCalculateRefundDTO implements Serializable {

    private static final long serialVersionUID = -7546094405569141447L;

    /**
     * 应退金额
     */
    @ApiModelProperty(value = "应退金额")
    private BigDecimal shouldRefundAmount;

    @ApiModelProperty(value = "退款明细")
    private List<ApplyGooodsItem> applyGooodsItemList;

    /**
     * 申请的商品信息
     */
    @Data
    public static class ApplyGooodsItem implements Serializable{

        private static final long serialVersionUID = 6732325487090110969L;

        /**
         * 商品编号
         */
        @ApiModelProperty(value = "商品编号")
        private String goodsNo;

        /**
         * 退款金额
         */
        @ApiModelProperty(value = "退款明细金额")
        private BigDecimal refundAmount;
    }


}
