package cn.htdt.orderprocess.dto.response;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.enums.PaymentChannelEnum;
import cn.htdt.common.enums.goods.PaymentMethodEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单支付流水DTO
 *
 * <AUTHOR>
 *
 *
 * 商品单位 unit
 * 202503 东启
 */
@Data
public class ResOrderPayFlowDTO implements Serializable {

    /**
     * 计量单位
     */
    @ApiModelProperty("计量单位")
    private String unit;

    /**
     * 序列化
     */
    private static final long serialVersionUID = -3896904011910954683L;

    @ApiModelProperty("订单编号")
    private String orderNo;

    @ApiModelProperty("交易流水号")
    private String outTradeNo;

    @ApiModelProperty("交易状态,枚举TradeStatusEnum  1010:待支付  1011:交易成功  1020:交易失败  1021:交易关闭  1030:支付中,待回调")
    private String tradeStatus;

    @ApiModelProperty("付款帐号,如果是粉丝购买则是buyerNo")
    private String buyerAccount;

    @ApiModelProperty("收款帐号")
    private String collectionAccount;

    @ApiModelProperty("支付渠道，字典PAYMENT_CHANNEL  1000:现金  1001:支付宝  1002:微信")
    @Converter(enumClass = PaymentChannelEnum.class, fieldName = "paymentChannelName")
    private String paymentChannel;

    @ApiModelProperty("支付渠道描述")
    private String paymentChannelName;

    @ApiModelProperty("支付渠道详情，字典PAYMENT_CHANNEL_DETAILS")
    private String paymentChannelDetails;

    @ApiModelProperty("支付方式，字典PAYMENT_METHOD")
    @Converter(enumClass = PaymentMethodEnum.class, fieldName = "paymentMethodName")
    private String paymentMethod;

    @ApiModelProperty("支付方式描述")
    private String paymentMethodName;

    @ApiModelProperty("支付金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal amount;

    @ApiModelProperty("实付金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal realAmount;

    @ApiModelProperty("支付优惠金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal promotionAmount;

    @ApiModelProperty("付款时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime payTime;

    @ApiModelProperty("付款截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endPayTime;

    @ApiModelProperty("付款成功回调时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime successTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("店铺编号")
    private String storeNo;

    /**
     * 订单类型，字典ORDER_TYPE
     */
    private String orderType;

    /**
     * 分销店对应商户编号
     */
    private String distributionMerchantNo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}