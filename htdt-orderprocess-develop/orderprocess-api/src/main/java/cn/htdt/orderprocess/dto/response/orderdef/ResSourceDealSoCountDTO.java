package cn.htdt.orderprocess.dto.response.orderdef;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.enums.OrderChannelSourceEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description 
 * @Date 2021/7/26
 * @Param 
 * @return 
 **/
@Data
public class ResSourceDealSoCountDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = 741087081635029349L;

    @ApiModelProperty(value = "1000:汇享购  1001:千橙掌柜PC  1002:千橙掌柜app  1003:千橙掌柜收银")
    private String appChannelSource;

    /**
     * 对应的成交数量
     */
    private int orderNum;

    /**
     * 应收订单金额
     */
    private BigDecimal shouldAmount;

    /**
     * 对应的成交商品数量
     */
    private BigDecimal goodsNum;

    /**
     * 欠款金额
     */
    private BigDecimal arrearsAmount;
}


