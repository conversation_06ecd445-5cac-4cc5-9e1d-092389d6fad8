package cn.htdt.orderprocess.dto.response.ordersubmit;

import cn.htdt.common.enums.PaymentCompanyEnum;
import cn.htdt.common.enums.TradeStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020-11-07
 * @Description 下单出参
 **/
@Data
public class ResOrderSubmitDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("父订单编号")
    private String parentOrderNo;

    @ApiModelProperty("子订单集合")
    private List<ResChildOrderDTO> listChildOrder;

    @ApiModelProperty("支付公司，默认汇通数科")
    private String paymentCompany = PaymentCompanyEnum.HTSK.getCode();

    @ApiModelProperty("第三方支付，返回状态")
    private String tradeStatus = TradeStatusEnum.SUCCESS.getCode();
}