package cn.htdt.orderprocess.dto.response.returnorder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 订单运费信息
 * <AUTHOR>
 */
@Data
public class ResOrderDeliveryFeeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "运费(实收)")
    private BigDecimal orderDeliveryFee;

    @ApiModelProperty(value = "改价前运费(实收)")
    private BigDecimal orderBeforeDeliveryFee;

    @ApiModelProperty(value = "是否退运费 1.不退 2 可以退")
    private Integer returnDeliveryFeeFlag;

    @ApiModelProperty(value = "申请售后金额")
    private BigDecimal applyReturnAmount;

    @ApiModelProperty(value = "退单原因")
    private String returnReason;

    @ApiModelProperty(value = "退货用户描述")
    private String returnRemark;

    @ApiModelProperty(value = "凭证图片List")
    private List<String> vouchers;

    @ApiModelProperty(value = "订单状态")
    private String orderStatus;

    @ApiModelProperty(value = "售后类型 1000仅退款未发货 1001仅退款已发货 1002退款退货")
    private String returnType;

    @ApiModelProperty(value = "订单回显数据")
    List<ResOrderItemByReturnDTO> orderItemByReturnDTOS;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
