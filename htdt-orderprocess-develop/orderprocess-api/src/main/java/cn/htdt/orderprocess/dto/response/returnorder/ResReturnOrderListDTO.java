package cn.htdt.orderprocess.dto.response.returnorder;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *  hxg售后单列表
 * <AUTHOR>
 */
@Data
public class ResReturnOrderListDTO implements Serializable {

    /**
     * 售后编号
     */
    private String soReturnNo;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 分销店铺名称
     */
    private String distributionStoreName;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 实际退款金额（不受申请金额干扰、含运费）
     */
    private BigDecimal actualReturnAmount;

    /**
     * 用户申请退款金额
     */
    private BigDecimal applyReturnAmount;

    /**
     * 退单状态 字典SO_RETURN_STATUS
     */
    private String returnStatus;

    /**
     * 订单渠道来源 字典ORDER_CHANNEL_SOURCE
     */
    private String orderChannelSource;

    /**
     * 退款方式 字典REFUNDMENT_WAY
     */
    private String refundmentWay;

    /**
     * 退款单状态，字典REFUNDMENT_STATUS
     */
    private String refundmentStatus;

    /**
     * 商品编号
     */
    private String goodsNo;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品图片角标URL
     */
    private String superscriptPictureUrl;

    /**
     * 商品主图
     */
    private String productPicPath;

    /**
     * 商品属性
     */
    private String extInfo;

    /**
     * 订单应收总金额
     */
    private BigDecimal shouldAmount;

    /**
     * 订单实付金额
     */
    private BigDecimal realAmount;

    /**
     * 订单是否欠款
     * 1 否 2 是
     */
    private Integer arrearsFlag;

    /**
     * 快递单号/运单号
     */
    private String courierNumber;

    /**
     * 修改次数
     */
    private Integer modifyNumber;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
