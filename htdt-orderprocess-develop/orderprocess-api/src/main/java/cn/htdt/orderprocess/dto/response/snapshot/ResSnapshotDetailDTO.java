package cn.htdt.orderprocess.dto.response.snapshot;

import cn.htdt.orderprocess.dto.response.ResGoodsMediaDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/20 15:22
 */
@Data
public class ResSnapshotDetailDTO implements Serializable {

    @ApiModelProperty(value = "商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "商品类型 1001-实物商品;1002-虚拟商品;1003-服务商品;1004-称重商品")
    private String goodsType;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品购买数量")
    private BigDecimal goodsItemNum;

    @ApiModelProperty(value = "商品重量")
    private String goodsWeight;

    @ApiModelProperty(value = "商品图片URL")
    private String goodsPicPath;

    @ApiModelProperty(value = "视频url")
    private String videoUrl;

    @ApiModelProperty(value = "商品主图")
    private String mainPictureUrl;

    @ApiModelProperty(value = "卖点描述")
    private String goodsSaleDescription;

    @ApiModelProperty(value = "商品市场价")
    private BigDecimal goodstPriceMarket;

    @ApiModelProperty(value = "商品销售价")
    private BigDecimal goodsPriceSale;

    @ApiModelProperty(value = "规格/型号")
    private String extInfo;

    @ApiModelProperty(value = "多媒体信息集合，以json形式存储")
    private String mediaInfo;

    @ApiModelProperty(value = "轮播图片集合")
    private List<ResGoodsMediaDTO> pictureList;

    @ApiModelProperty(value = "详情图片集合")
    private List<ResGoodsMediaDTO> detailPictureList;

    @ApiModelProperty(value = "商品文描")
    private String content;

    @ApiModelProperty(value = "店铺编号")
    private String storeNo;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "店铺地址")
    private String detailAddress;

    @ApiModelProperty(value = "店铺地址加密")
    private String dsDetailAddress;

    @ApiModelProperty(value = "门头照片url")
    private String storePhoto;

}
