package cn.htdt.orderprocess.dto.response.returnorder;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 售后状态
 * <AUTHOR>
 */
@Data
public class ResReturnStatusDTO implements Serializable {
    /**
     * 退单状态 字典SO_RETURN_STATUS
     */
    private String returnStatus;

    /**
     * 退款单状态，字典REFUNDMENT_STATUS
     */
    private String refundmentStatus;

    /**
     * 商品购买数量
     */
    private BigDecimal productItemNum;

    /**
     * 售后商品数量
     */
    private BigDecimal returnProductItemNum;
}
