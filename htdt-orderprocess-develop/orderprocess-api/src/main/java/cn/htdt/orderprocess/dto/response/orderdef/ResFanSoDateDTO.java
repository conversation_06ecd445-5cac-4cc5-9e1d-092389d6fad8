package cn.htdt.orderprocess.dto.response.orderdef;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 粉丝数据报表二级页面, 粉丝进店趋势柱状图, 进店日期和订单号
 * <AUTHOR>
 * @since 2023-02-08
 */
@Data
public class ResFanSoDateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("进店日期")
    private LocalDateTime createTime;

    @ApiModelProperty("订单编号")
    private String orderNo;

    @ApiModelProperty("订单创建日期, y.M格式")
    private String time;
}
