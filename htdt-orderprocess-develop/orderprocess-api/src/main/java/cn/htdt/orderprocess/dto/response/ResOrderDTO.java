package cn.htdt.orderprocess.dto.response;

import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import cn.htdt.common.enums.constants.NumConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单分页查询响应类
 *
 * <AUTHOR>
 *
 *
 *
 * 商品单位 unit
 * 202503 东启
 */
@Data
public class ResOrderDTO implements Serializable {

    /**
     * 商品单位
     */
    private String unit;

    /**
     * 序列化
     */
    private static final long serialVersionUID = -3896904011910954684L;

    /**
     * 父订单编号
     */
    @ApiModelProperty("父订单编号")
    private String parentOrderNo;

    /**
     * 订单编号
     */
    @ApiModelProperty("订单编号")
    private String orderNo;

    /**
     * 1 子单 2 父单
     */
    @ApiModelProperty("1 子单 2 父单")
    private Integer leafFlag;

    /**
     * 下单人编号
     */
    @ApiModelProperty("订单编号")
    private String buyerNo;

    /**
     * 下单人名称
     */
    @ApiModelProperty("下单人名称")
    private String buyerName;

    /**
     * 下单人联系号码
     */
    @ApiModelProperty("下单人联系号码")
    private String buyerMobile;

    /**
     * 下单人联系号码
     */
    @ApiModelProperty("下单人联系号码加密")
    private String dsBuyerMobile;

    /**
     * 分销店对应商户编号
     */
    @ApiModelProperty("分销店对应商户编号")
    private String distributionMerchantNo;

    /**
     * 分销店对应商户名称
     */
    @ApiModelProperty("分销店对应商户名称")
    private String distributionMerchantName;

    /**
     * 分销店铺编号
     */
    @ApiModelProperty("分销店铺编号")
    private String distributionStoreNo;

    /**
     * 分销店铺名称
     */
    @ApiModelProperty("分销店铺名称")
    private String distributionStoreName;

    /**
     * 商家编号
     */
    @ApiModelProperty("商家编号")
    private String merchantNo;

    /**
     * 归属平台编号
     */
    @ApiModelProperty("归属平台编号")
    private String companyNo;

    /**
     * 归属平台名称
     */
    @ApiModelProperty("归属平台名称")
    private String companyName;

    /**
     * 归属分部编号
     */
    @ApiModelProperty("归属分部编号")
    private String branchNo;

    /**
     * 归属分部名称
     */
    @ApiModelProperty("归属分部名称")
    private String branchName;

    /**
     * 店铺编码
     */
    @ApiModelProperty("店铺编码")
    private String storeNo;

    /**
     * 店铺名称
     */
    @ApiModelProperty("店铺名称")
    private String storeName;

    /**
     * 订单金额(不含运费/运费险)
     */
    @ApiModelProperty("订单金额(不含运费/运费险)")
    private BigDecimal orderAmount;

    /**
     * 订单总金额
     */
    @ApiModelProperty("订单总金额")
    private BigDecimal productAmount;

    /**
     * 订单应收总金额
     */
    @ApiModelProperty("订单应收总金额")
    private BigDecimal shouldAmount;

    /**
     * 订单实付金额
     */
    @ApiModelProperty("订单实付金额")
    private BigDecimal realAmount;

    /**
     * 币别码
     */
    @ApiModelProperty("币别码")
    private String currency;

    /**
     * 币别名称
     */
    @ApiModelProperty("币别名称")
    private String currencyName;

    /**
     * 币种汇率
     */
    @ApiModelProperty("币种汇率")
    private BigDecimal currencyRate;

    /**
     * 币种符号
     */
    @ApiModelProperty("币种符号")
    private String currencySymbol;

    /**
     * 税费
     */
    @ApiModelProperty("税费")
    private BigDecimal taxAmount;

    /**
     * 订单状态 字典ORDER_STATUS
     */
    @ApiModelProperty("订单状态 1010:待支付  1030:待确认 1050:待发货 1060:待收货  1061:部分发货  1999:交易成功  9000:交易关闭")
    private String orderStatus;

    /**
     * 支付方式，字典PAY_METHOD
     */
    @ApiModelProperty("支付方式，字典PAY_METHOD")
    private String orderPaymentMethod;

    /**
     * 支付状态，字典ORDER_PAYMENT_STATUS
     */
    @ApiModelProperty("订单支付状态 1000:待支付  1001:部分支付  1002:已支付")
    private String orderPaymentStatus;

    /**
     * 订单类型，字典ORDER_TYPE
     */
    @ApiModelProperty("订单类型 1000:汇享购订单 1020:秒杀订单 1021:拼团订单  1022:预售订单  1301:中奖订单  1500:门店订单 1010:云池订单")
    private String orderTye;

    /**
     * 订单渠道来源 字典ORDER_CHANNEL_SOURCE
     */
    @ApiModelProperty("订单渠道来源,字典ORDER_CHANNEL_SOURCE  千橙掌柜app:1200  千橙掌柜pc:1100,1101    千橙掌柜收银:1300,1400     汇享购:1000,1001,1002,1003")
    private String orderChannelSource;


    /**
     * 订单应用程序来源 字典AppChannelSourceEnum
     */
    private String appChannelSource;

    /**
     * 下单时间
     */
    @ApiModelProperty("下单时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime orderCreateTime;

    /**
     * 最后支付时间
     */
    @ApiModelProperty("最后支付时间")
    private LocalDateTime orderPaymentLastDate;

    /**
     * 支付确认时间
     */
    @ApiModelProperty("支付确认时间")
    private LocalDateTime orderPaymentConfirmDate;

    /**
     * 最后确认收货时间
     */
    @ApiModelProperty("最后确认收货时间")
    private LocalDateTime orderReceiptLastDate;

    /**
     * 改价优惠
     */
    @ApiModelProperty("改价优惠")
    private BigDecimal orderAlterDiscount;

    /**
     * 运费(实收)
     */
    @ApiModelProperty("运费(实收)")
    private BigDecimal orderDeliveryFee;

    /**
     * 欠款金额
     */
    @ApiModelProperty("欠款金额")
    private BigDecimal arrearsAmount;

    /**
     * 支付-抵用券支付的金额
     */
    @ApiModelProperty("支付-抵用券支付的金额")
    private BigDecimal orderPaidByCoupon;

    /**
     * 订单已优惠金额(满立减)
     */
    @ApiModelProperty("订单已优惠金额(满立减)")
    private BigDecimal orderPromotionDiscount;

    /**
     * 订单消耗的积分
     */
    @ApiModelProperty("订单消耗的积分")
    private Integer orderDepletePoints;

    /**
     * 订单赠送的积分
     */
    @ApiModelProperty("订单赠送的积分")
    private Integer orderGivePoints;

    //账户剩余积分
    @ApiModelProperty(value = "会员账号积分")
    private Integer accountRemainPoints;

    @ApiModelProperty(value = "可用积分")
    private Integer canUsePoint;

    @ApiModelProperty(value = "可抵扣金额")
    private BigDecimal canDeductionAmount;

    @ApiModelProperty(value = "抵扣还差金额")
    private BigDecimal canDeductionLessAmount;

    /**
     * ********蛋品-zxy-积分, 积分类型
     * 积分类型, 1-店铺, 2-商家
     */
    @ApiModelProperty(value = "积分类型, 1-店铺, 2-商家")
    private String pointType;

    @ApiModelProperty(value = "渠道优惠金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal orderExtendChannelDiscount = BigDecimal.ZERO;
    @ApiModelProperty(value = "渠道优惠编码，如：美团")
    private String orderExtendChannelCode;
    @ApiModelProperty(value = "渠道优惠名称，如：美团")
    private String orderExtendChannelName;
    @ApiModelProperty(value = "渠道优惠类型：0=无需核销 1=整单核销 2=按金额核销")
    private Integer orderExtendChannelFlag = NumConstant.ZERO;

    @ApiModelProperty(value = "会员价优惠金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal memberPriceDiscountAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "礼品卡优惠金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal giftCardDiscount = BigDecimal.ZERO;

    /**
     * 是否可用:默认1，1：启用，2：禁用
     * 1、老板开启积分支付（抵扣）功能
     * 2、积分抵扣使用渠道包含
     */
    @ApiModelProperty(value = "是否可用:1：启用，2：禁用")
    private Integer disableFlag = NumConstant.TWO;

    /**
     * 订单商品总件数
     */
    @ApiModelProperty("订单商品总件数")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal orderTotalNum;

    /**
     * 取消原因ID
     */
    @ApiModelProperty("取消原因ID")
    private String orderCancelReasonType;

    /**
     * 取消时间
     */
    @ApiModelProperty("取消时间")
    private LocalDateTime orderCancelDate;

    /**
     * 订单取消原因
     */
    @ApiModelProperty("订单取消原因")
    private String orderCsCancelReason;

    /**
     * 取消操作人类型 字典ORDER_CANCEL_OPERATOR_TYPE
     */
    @ApiModelProperty("取消操作人类型  1000:粉丝发起取消  1100:客服发起取消  1200:系统自动取消")
    private String orderCancelOperateType;

    /**
     * 取消操作人用户名
     */
    @ApiModelProperty("取消操作人用户名")
    private String orderCancelOperateNo;

    @ApiModelProperty("粉丝端+分销店维度配送方式类型，字典DELIVERY_WAY")
    private String distributionOrderDeliveryWay;

    @ApiModelProperty("供货店维度配送方式类型，字典DELIVERY_WAY")
    private String orderDeliveryWay;

    @ApiModelProperty("订单备注(用户)")
    private String orderRemarkBuyer;

    @ApiModelProperty("餐饮整单备注")
    private String wholeFoodsRemarks;

    @ApiModelProperty("订单行业类型 1001普通类型 1002餐饮类型")
    private String orderIndustry;

    /**
     * 订单备注(商家给用户看的)
     */
    @ApiModelProperty("订单备注(商家给用户看的)")
    private String orderRemarkMerchant2user;

    /**
     * 订单备注(商家自己看的)
     */
    @ApiModelProperty("订单备注(商家自己看的)")
    private String orderRemarkMerchant;

    /**
     * 收货人地址
     */
    @ApiModelProperty("收货人地址")
    private String goodReceiverAddress;

    /**
     * 收货人地址-加密
     */
    @ApiModelProperty("收货人地址-加密")
    private String dsGoodReceiverAddress;

    /**
     * 收货人地址邮编
     */
    @ApiModelProperty("收货人地址邮编")
    private String goodReceiverPostcode;

    /**
     * 收货人姓名
     */
    @ApiModelProperty("收货人姓名")
    private String goodReceiverName;

    /**
     * 收货人手机
     */
    @ApiModelProperty("收货人手机")
    private String goodReceiverMobile;

    /**
     * 收货人手机-加密
     */
    @ApiModelProperty("收货人手机-加密")
    private String dsGoodReceiverMobile;

    /**
     * 收货人国家
     */
    @ApiModelProperty("收货人国家")
    private String goodReceiverCountry;

    /**
     * 收货人省份
     */
    @ApiModelProperty("收货人省份")
    private String goodReceiverProvince;

    /**
     * 收货人城市
     */
    @ApiModelProperty("收货人城市")
    private String goodReceiverCity;

    /**
     * 收货人县区
     */
    @ApiModelProperty("收货人县区")
    private String goodReceiverCounty;

    /**
     * 收货人区镇
     */
    @ApiModelProperty("收货人区镇")
    private String goodReceiverArea;

    /**
     * 身份证号码
     */
    @ApiModelProperty("身份证号码")
    private String identityCardNumber;

    /**
     * 订单出库时间
     */
    @ApiModelProperty("订单出库时间")
    private LocalDateTime orderLogisticsTime;

    /**
     * 订单收货时间
     */
    @ApiModelProperty("订单收货时间")
    private LocalDateTime orderReceiveDate;

    /**
     * 0：未删除1：回收站-用户可恢复到02：用户完全删除(客服可协助恢复到0或1)
     */
    @ApiModelProperty("0：未删除1：回收站-用户可恢复到02：用户完全删除(客服可协助恢复到0或1)")
    private Integer orderDeleteStatus;

    /**
     * 改价前订单金额(不含运费/运费险)
     */
    @ApiModelProperty("改价前订单金额(不含运费/运费险)")
    private BigDecimal orderBeforeAmount;

    /**
     * 改价前运费(实收)
     */
    @ApiModelProperty("改价前运费(实收)")
    private BigDecimal orderBeforeDeliveryFee;

    /**
     * 订单优惠
     */
    @ApiModelProperty("订单优惠")
    private BigDecimal orderTotalDiscount;

    /**
     * 特惠促销优惠金额
     */
    @ApiModelProperty("特惠促销优惠金额")
    private BigDecimal vipDiscountAmount;

    /**
     * 订单来源系统
     */
    @ApiModelProperty("订单来源系统")
    private String sysSource;

    /**
     * 外部系统订单编号
     */
    @ApiModelProperty("外部系统订单编号")
    private String outOrderNo;

    /**
     * 评论状态 1:未评论 2:已评论
     */
    @ApiModelProperty("评论状态 1:未评论 2:已评论")
    private Integer commentStatus;

    /**
     * 商家名称
     */
    @ApiModelProperty("商家名称")
    private String merchantName;

    /**
     * 平台备注
     */
    @ApiModelProperty("平台备注")
    private String orderRemarkCompany;

    /**
     * 订单完成时间
     */
    @ApiModelProperty("订单完成时间")
    private LocalDateTime orderCompleteDate;

    /**
     * 订单类型，对应so_type
     */
    @ApiModelProperty("订单类型，对应so_type")
    private String orderType;

    @ApiModelProperty(value="订单标识，字典OrderFlagEnum")
    private Integer orderFlag;

    @ApiModelProperty("平台店铺订单标识，1 平台单 2 店铺单, 字典WhetherEnum")
    private Integer headFlag;

    @ApiModelProperty(value="促销标识，字典WhetherEnum")
    private Integer promotionsFlag;
    @ApiModelProperty(value="促销活动编码")
    private String promotionNo;
    @ApiModelProperty(value="促销活动名称")
    private String promotionName;
    /**
     * 收货人国家code
     */
    @ApiModelProperty("收货人国家code")
    private String goodReceiverCountryCode;

    /**
     * 收货人省份code
     */
    @ApiModelProperty("收货人省份code")
    private String goodReceiverProvinceCode;

    /**
     * 收货人城市code
     */
    @ApiModelProperty("收货人城市code")
    private String goodReceiverCityCode;

    /**
     * 收货人地区code
     */
    @ApiModelProperty("收货人地区code")
    private String goodReceiverCountyCode;

    /**
     * 收货人四级区域code
     */
    @ApiModelProperty("收货人四级区域code")
    private String goodReceiverAreaCode;

    /**
     * 订单标签
     */
    @ApiModelProperty("订单标签")
    private String orderLabel;

    /**
     * 发货日期
     */
    @ApiModelProperty("发货日期")
    private LocalDateTime expectDeliverDate;

    /**
     * 开单人
     */
    @ApiModelProperty("开单人")
    private String sellerNo;

    /**
     * 开单人姓名
     */
    @ApiModelProperty("开单人姓名")
    private String sellerName;

    /**
     * 收银员-如果是POS渠道的订单，会同步
     */
    @ApiModelProperty("收银员-如果是POS渠道的订单，会同步")
    private String cashier;

    /**
     * 扩展信息，以json形式存储
     */
    @ApiModelProperty("扩展信息，以json形式存储")
    private String extInfo;

    /**
     * 是否发生过改价标识 1：未发生 2：已发生
     */
    @ApiModelProperty("是否发生过改价标识 1：未发生 2：已发生")
    private Integer changePriceFlag;

    /**
     * 是否发生过售后标识 1：未发生 2：已发生
     */
    @ApiModelProperty("是否发生过售后标识 1：未发生 2：已发生")
    private Integer returnFlag;

    /**
     * 订单商品明细
     */
    @ApiModelProperty(value = "订单商品明细")
    private List<ResOrderItemDTO> listOrderItem;

    /**
     * 订单商品明细
     */
    @ApiModelProperty("订单商品明细")
    private List<ResOrderPayFlowDTO> listOrderPayFlow;


    //物流信息
    /**
     * 快递单号
     */
    @ApiModelProperty("快递单号")
    private String deliveryNumber;
    /**
     * 物流公司
     */
    @ApiModelProperty("物流公司")
    private String expressDeliveryName;
    /**
     * 快递描述信息
     */
    @ApiModelProperty("快递描述信息")
    private String remark;

    /**
     * 快递记录对应的时间
     */
    @ApiModelProperty("快递记录对应的时间")
    private LocalDateTime logisticsTime;

    /**
     * 拼接商品编号
     */
    @ApiModelProperty("拼接商品编号")
    private String goodsNoConcat;

    /**
     * 拼接商品名称
     */
    @ApiModelProperty("拼接商品名称")
    private String goodsNameConcat;

    /**
     * 拼接支付流水名称
     */
    @ApiModelProperty("拼接支付流水名称")
    private String outTradeNoConcat;

    /**
     * 拼接支付渠道
     */
    @ApiModelProperty("拼接支付渠道")
    private String paymentChannelConcat;

    /**
     * 是否欠款
     */
    @ApiModelProperty("是否欠款")
    private Integer arrearsFlag;

    /**
     * 核销日期（二期需求）
     */
    @ApiModelProperty("核销日期（二期需求）")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime writeOffTime;

    /**
     * 核销状态（1：待核销  2：已核销）（二期需求）
     */
    @ApiModelProperty("核销状态（1：待核销  2：已核销）（二期需求）")
    private Integer writeOffStatus;

    /**
     * 核销码（二期需求）
     */
    @ApiModelProperty(" 核销码（二期需求）")
    private String writeOffCode;

    /**
     * 核销二维码URL
     */
    @ApiModelProperty("核销二维码URL")
    private String writeOffQrCodeUrl;

    /**
     * 云池订单物流状态，自提商品生产核销码使用 1：订单商品未签收 2：订单商品已签收
     */
    @ApiModelProperty("云池订单物流状态，自提商品生产核销码使用 1：订单商品未签收 2：订单商品已签收")
    private Integer cloudLogisticsStatus;

    /**
     * 店铺自提联系人
     */
    @ApiModelProperty("店铺自提联系人")
    private String cancelName;


    /**
     * 店铺自提联系电话
     */
    @ApiModelProperty("店铺自提联系电话")
    private String cancelPhone;


    /**
     * 店铺自提联系电话
     */
    @ApiModelProperty("店铺自提联系电话")
    private String dsCancelPhone;

    /**
     * 自提地址
     */
    @ApiModelProperty("自提地址")
    private String cancelAddress;

    /**
     * 券是否自用
     */
    @ApiModelProperty("券是否自用")
    private Integer selfUseFlag;

    /**
     * 商品图片URL
     */
    @ApiModelProperty("商品图片URL")
    private String goodsPicPath;

    /**
     * 网店商品成交总数
     */
    @ApiModelProperty("网店商品成交总数")
    private Integer onlineGoodsCount;

    /**
     * 门店商品成交总数
     */
    @ApiModelProperty("门店商品成交总数")
    private Integer offlineGoodsCount;

    /**
     * 商品成交总数
     */
    @ApiModelProperty("商品成交总数")
    private Integer totalGoodsCount;

    /**
     * 商品编号
     */
    @ApiModelProperty("商品编号")
    private String goodsNo;

    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String goodsName;

    /**
     * 商品来源
     */
    @ApiModelProperty("商品来源")
    private String goodsSourceType;

    /**
     * 商品主图
     */
    @ApiModelProperty("商品主图")
    private String mainPictureUrl;

    /**
     * 汇享购订单
     */
    private int hxgOrderNum;

    /**
     * 千橙掌柜APP订单
     */
    private int bossAppOrderNum;

    /**
     * 千橙掌柜PC订单
     */
    private int bossPcOrderNum;

    /**
     * 千橙掌柜收银订单
     */
    private int smartCashierOrderNum;


    /**
     * 欠款总金额
     */
    private BigDecimal arrearsAmountCount;

    /**
     * 分组时间
     */
    private String groupTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     *订单状态名称
     */
    private String orderStatusValue;

    /**
     * 订单支付方式
     */
    private String paymentChannelName;

    /**
     * 冲减金额
     */
    private BigDecimal deductAmount;

    /**
     * 备注 1否 2是
     */
    private Integer remarkFlag;

    /**
     * 汇通数科编号
     */
    private String htskNo;

    /**
     * 汇通达编号
     */
    private String htdNo;
    /**
     * 头像
     */
    private String headImg;

    /**
     * 微信昵称
     */
    private String nickName;

    /**
     * 查询结束-成交订单数量
     */
    private Integer orderCount;

    /**
     * 月度订单总数
     */
    private Integer totalOrder;

    /**
     * 月度金额总数
     */
    private BigDecimal totalAmount;

    /**
     * 条码
     */
    private String barCode;
    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * 销售总价
     */
    private BigDecimal goodsTotalAmount;
    /**
     * 商品数量 订购数量
     */
    private BigDecimal goodsItemNum;


    /**
     * 销售单价
     */
    private BigDecimal goodsPriceSale;

    /**
     * 成交价
     */
    private BigDecimal goodsItemRealAmount;
    /**
     * 物流信息
     */
    @ApiModelProperty("物流信息")
    private String deliveryInfo;
    /**
     * 支付状态
     */
    private String payType;
    @ApiModelProperty(value = "满折优惠")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal orderFullDiscount = BigDecimal.ZERO;

    @ApiModelProperty(value = "满减优惠")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal orderReduceDiscount = BigDecimal.ZERO;

    @ApiModelProperty(value = "橙豆")
    private BigDecimal virtualCoinsDiscount = BigDecimal.ZERO;

    @ApiModelProperty(value = "店铺抵扣积分")
    private Integer depPoints;

    @ApiModelProperty(value = "积分抵扣")
    private BigDecimal orderDeductionPointDiscount = BigDecimal.ZERO;

    @ApiModelProperty(value = "购物币（橙豆）余额")
    private BigDecimal accountRemainCoins;

    @ApiModelProperty(value = "优惠券")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal orderCouponDiscount = BigDecimal.ZERO;

    @ApiModelProperty(value = "代金券优惠金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal voucherDiscount = BigDecimal.ZERO;

    @ApiModelProperty(value = "显示按钮集合 select：查看 confirm：确认 modify：修改 print：打印 send：发货 return发起售后")
    private List<String> buttons;

    @ApiModelProperty(value = "售后标签")
    private String returnLabel;

    @ApiModelProperty(value = "可发起售后 1000：售后中，不显示发起售后按钮")
    private String returnButtonDisplay;

    /**
     * 退款单状态,字典REFUNDMENT_STATUS
     */
    @ApiModelProperty(value = "退款单状态")
    private String refundmentStatus;

    /**
     * 退单状态 字典SO_RETURN_STATUS
     */
    @ApiModelProperty(value = "退单状态")
    private String returnStatus;

    @ApiModelProperty(value = "流水号（暂用于餐饮小票流水号&接龙订单接龙号）")
    private String serialNumber;

    /**
     * 小票流水号
     */
    @ApiModelProperty(value = "小票流水号")
    private String ticketCode;

    /**
     * 用券数量
     */
    private Integer useVoucherNum;

    @ApiModelProperty(value = "核销渠道")
    private String extendChannelName;

    @ApiModelProperty(value = "核销金额")
    private BigDecimal amountSharePromotion = BigDecimal.ZERO;

    @ApiModelProperty(value = "其他优惠")
    private BigDecimal otherAmountSharePromotion = BigDecimal.ZERO;

    @ApiModelProperty(value = "虚拟订单（即商品）类型 1001-代金券,1002-计次卡")
    private String virtualGoodsType;

    @ApiModelProperty(value = "导购员姓名")
    private String shoppingGuideName;

    @ApiModelProperty(value = "礼品卡卡密")
    private String secretKey;

    /**
     * 自定义自提点编码
     */
    private String customAddressNo;

    /**
     * 自定义自提点名称
     */
    private String customAddressName;

    @ApiModelProperty(value = "核销商品分类,0或空:全部,1:门店商品,2:平台商品,3:总部商品")
    private String writeOffGoodsType;

    @ApiModelProperty(value = "核销商品分类:1:门店商品,2:平台商品,3:总部商品")
    private String writeOffGoodsTypeName;

    @ApiModelProperty(value = "退款笔数")
    private int returnOrderNum;

    @ApiModelProperty(value = "退款金额")
    private BigDecimal returnOrderAmount;

    @ApiModelProperty("数值")
    private Integer number;

    @ApiModelProperty("订单粉丝数量")
    private Integer totalOrderFans;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}