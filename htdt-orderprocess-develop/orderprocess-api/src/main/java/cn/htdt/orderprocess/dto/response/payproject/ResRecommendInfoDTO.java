package cn.htdt.orderprocess.dto.response.payproject;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 推荐人信息响应类
 *
 * <AUTHOR>
 */
@Data
public class ResRecommendInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("推荐人姓名")
    private String recommendName;

    @ApiModelProperty("推荐人编号")
    private String recommendNo;

    @ApiModelProperty("推荐人类型 1 服务商 2 员工账号 3 服务商子账号")
    private Integer recommendType;
}
