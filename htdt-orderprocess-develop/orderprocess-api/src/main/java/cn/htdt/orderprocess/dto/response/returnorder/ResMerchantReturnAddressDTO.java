package cn.htdt.orderprocess.dto.response.returnorder;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2020-09-06
 * @Description 退货商家收货地址信息
 **/
@Data
public class ResMerchantReturnAddressDTO implements Serializable {

    /**
     * 退货商家收货人姓名
     */
    private String returnName;

    /**
     * 退货商家收货人手机
     */
    private String returnMobile;

    /**
     * 退货商家收货人手机
     */
    private String dsReturnMobile;

    /**
     * 退货商家收货人地址
     */
    private String returnAddress;

    /**
     * 退货商家收货人地址
     */
    private String dsReturnAddress;

    /**
     * 退货商家收货人省份
     */
    private String returnProvince;

    /**
     * 退货商家收货人省份code
     */
    private String returnProvinceCode;

    /**
     * 退货商家收货人城市
     */
    private String returnCity;

    /**
     * 退货商家收货人城市code
     */
    private String returnCityCode;

    /**
     * 退货商家收货人县区
     */
    private String returnCounty;

    /**
     * 退商家货收货人县区code
     */
    private String returnCountyCode;

    /**
     * 退货商家收货人区镇
     */
    private String returnArea;

    /**
     * 退货商家收货人区镇code
     */
    private String returnAreaCode;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
