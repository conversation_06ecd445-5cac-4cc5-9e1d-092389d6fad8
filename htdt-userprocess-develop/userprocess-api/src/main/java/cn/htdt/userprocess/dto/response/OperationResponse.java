package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <p>
 * 功能操作表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-31
 */
@Data
@ApiModel(description = "按钮实体类")
public class OperationResponse implements Serializable {


    private static final long serialVersionUID = 2681319160281951353L;

    private String id;
    /**
     * 操作名称
     */
    @ApiModelProperty(notes = "操作名称")
    private String operationName;

    /**
     * 操作编码
     */
    @ApiModelProperty(notes = "操作编码")
    private String operationCode;

    /**
     * 操作方法
     */
    @ApiModelProperty(notes = "操作方法")
    private String operationMethod;

    /**
     * 拦截URL
     */
    @ApiModelProperty(notes = "拦截URL")
    private String operationUrl;

    @ApiModelProperty(notes = "菜单编号")
    private String menuNo;

    @ApiModelProperty(notes = "菜单名称")
    private String menuName;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
