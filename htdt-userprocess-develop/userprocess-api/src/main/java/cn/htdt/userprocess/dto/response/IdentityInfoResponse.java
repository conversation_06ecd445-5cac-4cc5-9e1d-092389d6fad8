package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * @program: userprocess
 * @ClassName: IdentityInfoResponse
 * @description: 用户的身份信息
 * @author: Jacky
 * @create: 2020-09-09 16:07
 **/
@Data
@ApiModel(description = "商家信息响应实体类")
public class IdentityInfoResponse implements Serializable {

    @ApiModelProperty(notes = "用户标识")
    private String userNo;

    /**
     * 用户身份：
     * 1、系统身份: system；
     * 2、运营身份: operation；
     * 3、商家身份: merchant；
     * 4、店铺身份: store；
     */
    @ApiModelProperty(notes = "用户身份 system:系统身份; operation:运营身份; merchant:商家身份; store:店铺身份;")
    private String identity;

    @ApiModelProperty(notes = "身份具体名称")
    private String name;

    @ApiModelProperty(notes = "链接商家、店铺标识")
    private String linkId;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
