package cn.htdt.userprocess.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-03
 **/
@Data
@ApiModel(description = "区域响应实体类")
public class UcRegionStoreInfoResponse implements Serializable {

    private static final long serialVersionUID = -8779977275595383659L;


    @ApiModelProperty(notes = "区域标识")
    private Long id;

    @ApiModelProperty(notes = "区域编码")
    private String regionNo;

    @ApiModelProperty(notes = "类型")
    private String type;

    @ApiModelProperty(notes = "状态")
    private String status;

    @ApiModelProperty(notes = "区域名称")
    private String regionName;

    @ApiModelProperty(notes = "创建人名称")
    private String createName;

    @ApiModelProperty(notes = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(notes = "最后一次修改人ID")
    private String modifyNo;

    @ApiModelProperty(notes = "最后一次修改人名称")
    private String modifyName;

    @ApiModelProperty(notes = "最后一次修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime modifyTime;

    @ApiModelProperty(notes = "是否已经删除，默认1未删除，其余已删除")
    private Integer deleteFlag;

    /**
     * 所属商家编号
     */
    @ApiModelProperty(notes = "所属商家编号")
    private String merchantNo;

    /**
     * 所属商家 id
     */
    @ApiModelProperty(notes = "所属商家 id")
    private String merchantId;

    /**
     * 组织id
     */
    @ApiModelProperty(notes = "组织id")
    private String branchNo;

    /**
     * 组织编码
     */
    @ApiModelProperty(notes = "组织编码")
    private String branchName;

    /**
     * 商家的店铺信息id
     */
    @ApiModelProperty(notes = "商家的店铺信息id")
    private String storeId;

    /**
     * 商家的店铺信息编码
     */
    @ApiModelProperty(notes = "商家的店铺信息编码")
    private String storeNo;

    /**
     * 商家的店铺信息名称
     */
    @ApiModelProperty(notes = "商家的店铺信息名称")
    private String storeName;


    /**
     * 是否已经被区域选中
     */
    @ApiModelProperty(notes = "是否已经选中，默认1未选中，其余已选中")
    private String falg_selected;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
