package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <p>
 * 分组信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-24
 */
@Data
@ApiModel(description = "分组关联粉丝列表")
public class GetFancGroupResponse implements Serializable {


    @ApiModelProperty(notes = "粉丝编号")
    private String fanNo;

    /**
     * 姓名
     */
    @ApiModelProperty(notes = "姓名")
    private String name;

    /**
     * 手机号
     */
    @ApiModelProperty(notes = "手机号")
    private String phone;

    /**
     * 生日
     */
    @ApiModelProperty(notes = "生日")
    private LocalDate birthday;

    /**
     * 来源
     */
    @ApiModelProperty(notes = "来源")
    private String registeredSource;

    @ApiModelProperty(notes = "关联粉丝")
    private String fanGroup;

    @ApiModelProperty(notes = "是否关联当前分组（0 否 1是）")
    private Integer status;
    /**
     * 是否关注：0-否，1-是
     */
    @ApiModelProperty(notes = "是否关注：0-否，1-是")
    private Integer followFlag;

    /**
     * 关注店铺编号
     */
    @ApiModelProperty(notes = "关注店铺编号")
    private String storeNo;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
