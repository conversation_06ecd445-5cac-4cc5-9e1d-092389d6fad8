package cn.htdt.userprocess.dto.response;


import cn.htdt.usercenter.dto.response.ResUUcRoleDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.List;

@Data
@ApiModel(description = "员工功能权限响应实体类")
public class GetStaffPrivilegeResponse implements Serializable {

    @ApiModelProperty(notes = "主键")
    private BigInteger id;

    @ApiModelProperty(notes = "用户编号")
    private String userNo;

    @ApiModelProperty(notes = "手机")
    private String mobile;

    @ApiModelProperty(notes="待分配角色")
    private List<ResUUcRoleDTO> noRoleList;

    @ApiModelProperty(notes="已分配角色")
    private List<ResUUcRoleDTO> yesRoleList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}