package cn.htdt.userprocess.dto.publicDto.response;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * <p>
 * 商家组织信息
 * </p>
 *
 */
@Data
@ApiModel(description = "粉丝返回实体类")
public class GetFanListResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    private String adminParams;
    /**
     * id
     */
    private BigInteger id;
    /**
     * 编号
     */
    private String fanNo;

    /**
     * 姓名
     */
    private String name;

    /**
     * 密码
     */
    private String password;

    /**
     * 性别 0 男 1 女 2保密
     */
    private Integer sex;

    /**
     * 电话
     */
    private String phone;

    /**
     * 后端盐值
     */
    private String bSalt;

    /**
     * 出生日期
     */
    private LocalDate birthday;

    /**
     * 微信昵称
     */
    private String nickName;

    /**
     * 头像地址
     */
    private String headImg;

    /**
     * 首次归属商家编号
     */
    private String ascriptionMerchantNumber;

    /**
     * 首次归属商家名称
     */
    private String ascriptionMerchantName;

    /**
     * 首次关注店铺编号
     */
    private String followStoreNumber;

    /**
     * 首次关注店铺名称
     */
    private String followStoreName;

    /**
     * 首次注册来源
     */
    private String registeredSource;

    /**
     * 注册时间
     */
    private LocalDateTime registeredTime;

    /**
     * 注册终端 1,android 2,ios 3,小程序 4,H5
     */
    private Integer registeredTerminal;

    /**
     * 首次加入店铺来源
     */
    private String joinStoreSource;

    /**
     * 首次加入店铺时间
     */
    private LocalDateTime joinStoreTime;

    /**
     * 状态 0停用 1启用
     */
    private Integer status;

    /**
     * 推荐人手机号
     */
    private String referrerPhone;

    /**
     * 推荐人编号
     */
    private String referrerNumber;

    /**
     * 状态 1粉丝 2代理人
     */
    private Integer type;

    /**
     * 是否黑名单 1否 2是
     */
    private Integer isBlacklist;


    /**
     * 创建人ID
     */
    private String createNo;

    /**
     * 最后一次修改人ID
     */
    private String modifyNo;

    /**
     * openid
     */
    private String openid;

    /**
     * 微信开放平台标识
     */
    private String unionid;

    /**
     * 备注名
     */
    private String storeFanName;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
