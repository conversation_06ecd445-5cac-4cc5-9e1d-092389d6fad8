package cn.htdt.userprocess.dto.response;

import cn.htdt.common.enums.constants.NumConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * <p>
 * 商家组织信息
 * </p>
 *
 */
@Data
@ApiModel(description = "平台返回实体类")
public class GetFancStorePageResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    @ApiModelProperty(value = "编号")
    private String fanNo;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(notes = "昵称")
    private String nickName;

    @ApiModelProperty(value = "粉丝备注名称（店铺）")
    private String storeFanName;

    @ApiModelProperty(value = "电话")
    private String phone;

    @ApiModelProperty(value = "出生日期")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private LocalDate birthday;

    @ApiModelProperty(value = "头像地址")
    private String headImg;

    @ApiModelProperty(value = "是否欠款 1：否 2：是")
    private Integer arrearsFlag;

    @ApiModelProperty(value = "分组名称")
    private String groupName;

    @ApiModelProperty(value = "首次加入店铺来源")
    private String joinStoreSource;

    @ApiModelProperty(value = "首次加入店铺时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime joinStoreTime;

    @ApiModelProperty(value = "优惠券数量")
    private long couponCount;

    @ApiModelProperty(value = "成交订单数")
    private long dealsCount;

    @ApiModelProperty(value = "账户剩余橙豆")
    private BigDecimal accountRemainCoins;

    @ApiModelProperty(notes = "状态 1粉丝 2代理人")
    private Integer type;

    /**
     * 粉丝积分
     */
    @ApiModelProperty(notes = "粉丝积分")
    private Integer fansPoint = NumConstant.ZERO;

    @ApiModelProperty(notes = "拼接完整地址")
    private String splicingAddress;

    @ApiModelProperty(notes = "所属店铺编号")
    private String storeNo;

    @ApiModelProperty(notes = "所属店铺名称")
    private String storeName;

    /**
     * 店铺粉丝展示后续详细地址密文 在应用层解密
     **/
    private String dsSubsequentAddress;

    /**
     * 是否会员：0-否，1-是
     */
    private Integer memberFlag;
    /**
     * 是否关注：0-否，1-是
     */
    private Integer followFlag;

    /**
     * 商家会员有效开始时间
     */
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private LocalDateTime businessMemberEffectiveTime;
    /**
     * 商家会员有效结束时间
     */
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private LocalDateTime businessMemberInvalidTime;

    @ApiModelProperty(notes = "等级编号")
    private String memberLevelNo;

    @ApiModelProperty(notes = "等级名称")
    private String memberLevelName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
