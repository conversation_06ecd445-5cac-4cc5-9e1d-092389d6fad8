package cn.htdt.userprocess.dto.publicDto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDate;


/**
 * <p>
 * 粉丝个人信息请求实体类
 * </p>
 *
 */
@Data
@ApiModel(description = "粉丝个人信息请求实体类")
public class GetFanInfoResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @ApiModelProperty(notes = "编号")
    private String fanNo;

    /**
     * 姓名
     */
    @ApiModelProperty(notes = "姓名")
    private String name;


    /**
     * 性别 0 男 1 女 2保密
     */
    @ApiModelProperty(notes = "性别 0 男 1 女 2保密")
    private Integer sex;

    /**
     * 电话
     */
    @ApiModelProperty(notes = "电话")
    private String phone;

    /**
     * 电话-加密
     */
    @ApiModelProperty(notes = "电话-加密")
    private String dsPhone;


    /**
     * 出生日期
     */
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    @ApiModelProperty(notes = "出生日期")
    private LocalDate birthday;

    /**
     * 微信昵称
     */
    @ApiModelProperty(notes = "微信昵称")
    private String nickName;

    /**
     * 头像地址
     */
    @ApiModelProperty(notes = "头像地址")
    private String headImg;

    /**
     * //20230928蛋品-wh-是否首次关注共享店铺下的粉丝（1.是，2.否）
     */
    private String firstFollow;



    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
