package cn.htdt.userprocess.dto.response;

import cn.htdt.common.dto.DesensitizationSerialize;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * <p>
 * 粉丝个人信息请求实体类
 * </p>
 *
 */
@Data
@ApiModel(description = "粉丝个人信息请求实体类")
public class FanInfoResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @ApiModelProperty(notes = "编号")
    private String fanNo;
    /**
     * 编号
     */
    @ApiModelProperty(notes = "代理人编号")
    private String agentNo;

    /**
     * 姓名
     */
    @ApiModelProperty(notes = "姓名")
    private String name;


    /**
     * 性别 0 男 1 女 2保密
     */
    @ApiModelProperty(notes = "性别 0 男 1 女 2保密")
    private Integer sex;

    /**
     * 电话
     */
    @ApiModelProperty(notes = "电话")
    private String phone;

    @ApiModelProperty(notes = "电话-密文")
    private String dsPhone;

    /**
     * 出生日期
     */
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    @ApiModelProperty(notes = "出生日期")
    private LocalDate birthday;

    /**
     * 微信昵称
     */
    @ApiModelProperty(notes = "微信昵称")
    private String nickName;

    /**
     * 头像地址
     */
    @ApiModelProperty(notes = "头像地址")
    private String headImg;

    /**
     * 省id
     */
    @ApiModelProperty(notes = "省id")
    private String provinceCode;

    /**
     * 省名
     */
    @ApiModelProperty(notes = "省名")
    private String provinceName;

    /**
     * 市id
     */
    @ApiModelProperty(notes = "市id")
    private String cityCode;

    /**
     * 市名
     */
    @ApiModelProperty(notes = "市名")
    private String cityName;

    /**
     * 区id
     */
    @ApiModelProperty(notes = "区id")
    private String regionCode;

    /**
     * 区名
     */
    @ApiModelProperty(notes = "区名")
    private String regionName;

    /**
     * 详细地址
     */
    @ApiModelProperty(notes = "详细地址")
    private String detailAddress;


    /**
     * 街道code
     */
    @ApiModelProperty(notes = "街道code")
    private String streetCode;

    /**
     * 街道名称
     */
    @ApiModelProperty(notes = "街道名称")
    private String streetName;

    /**
     * 家庭人数
     */
    @ApiModelProperty(notes = "家庭人数")
    private Integer homeNum;

    /**
     * 家庭收入
     */
    @ApiModelProperty(notes = "头像地址")
    private Integer income;

    /**
     * 注册日期
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(notes = "注册日期")
    private LocalDateTime registeredTime;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
