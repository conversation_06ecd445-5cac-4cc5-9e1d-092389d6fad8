package cn.htdt.userprocess.dto.response;

import cn.htdt.common.enums.constants.NumConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-03
 **/
@Data
@ApiModel(description = "区域响应实体类")
public class UcRegionInfoResponse implements Serializable {

    private static final long serialVersionUID = -8779977275595383659L;


    @ApiModelProperty(notes = "区域店铺信息")
    private List<UcRegionStoreInfoResponse> ucRegionStoreInfoResponsesList;

    @ApiModelProperty(notes = "区域店铺列表")
    private List<MerchantStoreResponse> merchantStoreResponseList;


    @ApiModelProperty(notes = "区域店铺字段")
    private String ucRegionStoreInfoResponseStr;

    @ApiModelProperty(notes = "区域标识")
    private Long id;

    @ApiModelProperty(notes = "区域编码")
    private String regionNo;

    @ApiModelProperty(notes = "类型")
    private String type;

    @ApiModelProperty(notes = "状态 1商家 2 区域")
    private String status;

    @ApiModelProperty(notes = "区域名称")
    private String regionName;

    @ApiModelProperty(notes = "创建人名称")
    private String createName;

    @ApiModelProperty(notes = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(notes = "最后一次修改人ID")
    private String modifyNo;

    @ApiModelProperty(notes = "最后一次修改人名称")
    private String modifyName;

    @ApiModelProperty(notes = "最后一次修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime modifyTime;

    @ApiModelProperty(notes = "是否已经删除，默认1未删除，其余已删除")
    private Integer deleteFlag;


    /**
     * 所属商家编号
     */
    @ApiModelProperty(notes = "所属商家编号")
    private String merchantNo;
    /**
     * 所属商家 id
     */
    @ApiModelProperty(notes = "所属商家 id")
    private String merchantId;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
