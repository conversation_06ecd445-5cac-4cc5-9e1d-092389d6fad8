package cn.htdt.userprocess.dto.response;

import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * @program: htdt-userprocess
 * @ClassName: IdentityResponse
 * @description: 用户身份
 * @author: <PERSON><PERSON>
 * @create: 2020-09-15 10:30
 **/
@Data
@Accessors(chain = true)
public class IdentityResponse implements Serializable {


    private static final long serialVersionUID = 6633728267582022649L;
    private String userNo;
    private String identity;
    private String name;
    private String linkId;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
