package cn.htdt.userprocess.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName ReqAgentStoreDTO
 * @Description 代理人粉丝关系
 * <AUTHOR>
 * @Date 2021/1/15 16:11
 */
@Data
@ApiModel(description = "代理人粉丝请求实体类")
public class AgentLockFanResponse implements Serializable {


    /**
     * 类型 0普通粉丝 1锁粉
     */
    @ApiModelProperty(notes = "类型 0普通粉丝 1锁粉")
    private Integer type;

    /**
     * 来源
     */
    @ApiModelProperty(notes = "来源")
    private String source;

    /**
     * 粉丝姓名
     */
    @ApiModelProperty(notes = "粉丝姓名")
    private String name;

    /**
     * 粉丝手机号
     */
    @ApiModelProperty(notes = "粉丝手机号")
    private String phone;

    /**
     * 粉丝归属店铺
     */
    @ApiModelProperty(notes = "粉丝归属店铺")
    private String storeName;

    /**
     * 粉丝归属商家
     */
    @ApiModelProperty(notes = "粉丝归属商家")
    private String merchantName;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(notes = "创建时间")
    private LocalDateTime createTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
