package cn.htdt.userprocess.dto.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 支付账户响应
 *
 * <AUTHOR>
 */
@Data
public class PayAccountResponse implements Serializable {

    private static final long serialVersionUID = -2633685368203060243L;

    /**
     * 卖家账户(对应支付卖家UserId)
     */
    private String sellerUserId;

    /**
     * 收款账户(对应收款人UserId)
     */
    private String payeeUserId;

    /**
     * 商家名称
     */
    private String merchantName;
}
