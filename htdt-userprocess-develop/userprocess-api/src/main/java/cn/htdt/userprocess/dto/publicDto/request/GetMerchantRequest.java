package cn.htdt.userprocess.dto.publicDto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <p>
 * 商家组织信息
 * </p>
 *
 */
@Data
@ApiModel(description = "商家请求实体类")
public class GetMerchantRequest implements Serializable {


    private static final long serialVersionUID = 6633728267582022649L;


    @ApiModelProperty(notes = "商家编号")
    private String merchantNo;

    @ApiModelProperty(notes = "店铺编号")
    private String storeNo;

    @ApiModelProperty(notes = "支付账户")
    private String payAccount;

    @ApiModelProperty(notes = "独立小程序appId")
    private String appId;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
