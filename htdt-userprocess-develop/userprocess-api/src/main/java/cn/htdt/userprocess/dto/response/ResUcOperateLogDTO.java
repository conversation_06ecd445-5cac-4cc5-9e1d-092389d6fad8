package cn.htdt.userprocess.dto.response;

import cn.htdt.common.dto.request.ReqComPageDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户操作日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-05
 */
@Data
@ApiModel(description = "用户操作日志返回值")
public class ResUcOperateLogDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 操作系统
     */
    @ApiModelProperty("操作系统")
    private Integer operateSystem;

    /**
     * 菜单名称
     */
    @ApiModelProperty("菜单名称")
    private String menuName;

    /**
     * 功能模块
     */
    @ApiModelProperty("功能模块")
    private String functionModule;

    /**
     * 商家id
     */
    @ApiModelProperty("商家id")
    private String merchantNo;

    /**
     * 店铺id
     */
    @ApiModelProperty("店铺id")
    private String storeNo;

    /**
     * 操作账号
     */
    @ApiModelProperty("操作账号")
    private String account;

    /**
     * 操作结果 1失败 2成功
     */
    @ApiModelProperty("操作结果 1失败 2成功")
    private Integer result;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String notes;

    @ApiModelProperty("操作时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty("操作人")
    private String createName;

}
