package cn.htdt.userprocess.dto.response;

import lombok.Data;

import java.io.Serializable;
import java.math.BigInteger;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-06-26
 **/
@Data
public class ResFansStoreSubscribeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String subscribeNo;
    private String fanNo;
    private String storeNo;
    private String miniappId;
    private String miniappOpenid;
    private Integer bizSubscribeFlag;

    private BigInteger id;
    private String createNo;
    private String createName;
    private LocalDateTime createTime;
    private String modifyNo;
    private String modifyName;
    private LocalDateTime modifyTime;
    private Integer deleteFlag = 1;

}
