package cn.htdt.userprocess.dto.publicDto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * @ClassName FanCodeLoginRequest
 * @Description 粉丝注册请求实体
 * <AUTHOR>
 * @Date 2020/9/1 下午2:40
 */
@Data
@ApiModel(description = "粉丝注册请求实体")
public class RegisterFanRequest implements Serializable {


    private static final long serialVersionUID = 6633728267582022649L;

    @ApiModelProperty(notes = "手机号",required = true)
    private String phone;

    @ApiModelProperty(notes = "姓名")
    private String name;

    @ApiModelProperty(notes = "门店编号")
    private String storeNo;
    /**
     * 注册终端 1,android 2,ios 3,小程序 4,H5
     */
    @ApiModelProperty(notes = "注册终端 1,android 2,ios 3,小程序 4,H5" )
    private Integer registeredTerminal;

    @ApiModelProperty(notes = "粉丝来源")
    private String source;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
