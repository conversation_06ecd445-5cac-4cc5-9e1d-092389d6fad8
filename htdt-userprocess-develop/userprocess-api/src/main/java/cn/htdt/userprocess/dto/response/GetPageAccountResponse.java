package cn.htdt.userprocess.dto.response;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@ApiModel(description = "账号响应实体类")
public class GetPageAccountResponse implements Serializable {

    @ApiModelProperty(notes = "主键")
    private Long id;

    @ApiModelProperty(notes = "用户编号")
    private String userNo;

    @ApiModelProperty(notes = "手机")
    private String mobile;
    /**
     * 用户名
     */
    @ApiModelProperty(notes = "用户名")
    private String username;

    /**
     * 用户身份（逗号拼接）：0-系统；1-运营；2-商家；3-店铺
     */
    @ApiModelProperty(notes = "用户身份（逗号拼接）：0-系统；1-运营；2-商家；3-店铺")
    private String type;

    /**
     * 注册来源：1-注册会员 2-手工录入 4-试用会员 8-试用转正式
     */
    @ApiModelProperty(notes = "注册来源：1-注册会员 2-手工录入 4-试用会员 8-试用转正式")
    private Integer source;

    /**
     * 状态 0停用 1启用
     */
    @ApiModelProperty(notes = "状态 1停用 2启用")
    private Integer status;

    /**
     * 冻结状态 0冻结 1解冻
     */
    @ApiModelProperty(notes = "冻结状态 1冻结 2解冻")
    private Integer frezenStatus;

    /**
     * 冻结开始时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(notes = "冻结开始时间")
    private LocalDateTime frezenStart;

    /**
     * 冻结结束时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(notes = "冻结结束时间")
    private LocalDateTime frezenEnd;

    /**
     * 异地登录状态 0关闭 1开启
     */
    @ApiModelProperty(notes = "异地登录状态 1关闭 2开启")
    private Integer remoteLogin;


    @ApiModelProperty(notes = "账户类型 1-运营；2-商家；3-店铺")
    private Integer accountType;

    @ApiModelProperty(notes = " 所属平台")
    private String platforms;

    @ApiModelProperty(notes = " 所属商家")
    private String merchants;

    @ApiModelProperty(notes = " 所属店铺")
    private String stores;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(notes = "冻结结束时间")
    private LocalDateTime createTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
