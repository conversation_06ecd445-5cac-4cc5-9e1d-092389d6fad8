package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * @ClassName ReqAgentStoreDTO
 * @Description 代理人信息
 * <AUTHOR>
 * @Date 2021/1/15 16:11
 */
@Data
@ApiModel(description = "代理人统计实体类")
public class AgentCountResponse implements Serializable {

    @ApiModelProperty(notes = "粉丝总数")
    private Integer total;

    @ApiModelProperty(notes = "锁粉粉丝数")
    private Integer lockNum;

    @ApiModelProperty(notes = "普通粉丝数")
    private Integer fanNum;

    @ApiModelProperty(notes = "今日新增")
    private Integer today;

    @ApiModelProperty(notes = "昨日新增")
    private Integer yesterday;


    @ApiModelProperty(notes = "本月新增")
    private Integer month;


    @ApiModelProperty(notes = "上月新增")
    private Integer lastMonth;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
