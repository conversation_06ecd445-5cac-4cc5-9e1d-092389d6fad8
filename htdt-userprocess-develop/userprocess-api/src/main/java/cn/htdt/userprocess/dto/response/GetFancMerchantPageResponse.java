package cn.htdt.userprocess.dto.response;

import cn.htdt.common.enums.constants.NumConstant;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * <p>
 * 商家组织信息
 * </p>
 *
 */
@Data
@ApiModel(description = "平台返回实体类")
public class GetFancMerchantPageResponse implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 编号
     */
    private String fanNo;

    /**
     * 姓名
     */
    private String name;

    /**
     * 昵称
     */
    private String nickName;
    /**
     * 电话
     */
    private String phone;
    /**
     * 出生日期
     */
    private LocalDate birthday;

    /**
     * 首次关注店铺名称
     */
    private String followStoreName;

    /**
     * 关注店铺编号
     */
    private String storeNo;
    
    /**
     * 关注店铺名称
     */
    private String storeName;

    /**
     * 粉丝积分
     */
    private Integer fansPoint = NumConstant.ZERO;

    /**
     * 是否会员：0-否，1-是
     */
    private Integer memberFlag;

    /**
     * 商家会员有效开始时间
     */
    private LocalDateTime businessMemberEffectiveTime;
    /**
     * 商家会员有效结束时间
     */
    private LocalDateTime businessMemberInvalidTime;

    /**
     * 20230928蛋品-桑伟杰-会员等级-等级编号
     */
    private String memberLevelNo;

    /**
     * 20230928蛋品-桑伟杰-会员等级-等级名称
     */
    private String memberLevelName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
