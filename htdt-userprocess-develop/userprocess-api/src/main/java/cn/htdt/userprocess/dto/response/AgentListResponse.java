package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * @ClassName ReqAgentStoreDTO
 * @Description 代理人信息
 * <AUTHOR>
 * @Date 2021/1/15 16:11
 */
@Data
@ApiModel(description = "代理人分页请求实体类")
public class AgentListResponse implements Serializable {

    /**
     * 代理人编号
     */
    @ApiModelProperty(notes = "代理人编号")
    private String agentNo;


    /**
     * 姓名
     */
    @ApiModelProperty(notes = "姓名")
    private String name;

    /**
     * 电话
     */
    @ApiModelProperty(notes = "电话")
    private String phone;


    @ApiModelProperty(notes = "头像")
    private String headImg;

    @ApiModelProperty(notes = "粉丝编号")
    private String fanNo;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
