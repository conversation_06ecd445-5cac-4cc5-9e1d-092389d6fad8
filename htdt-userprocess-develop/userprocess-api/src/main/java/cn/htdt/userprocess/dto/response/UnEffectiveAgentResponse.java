package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * @ClassName UnEffectiveAgentResponse
 * @Description 失效代理人信息
 * <AUTHOR>
 * @Date 2021/2/4 10:13
 */
@Data
@ApiModel(description = "失效代理人信息")
public class UnEffectiveAgentResponse implements Serializable {

    @ApiModelProperty(notes = "店铺编号")
    private String storeNo;

    @ApiModelProperty(notes = "店铺名称")
    private String storeName;

    @ApiModelProperty(notes = "代理人名")
    private String name;

    @ApiModelProperty(notes = "代理人编号")
    private String agentNo;

    @ApiModelProperty(notes = "代理人手机号")
    private String phone;


    @ApiModelProperty(notes = "失效代理人时间集合")
    private String timelist;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
