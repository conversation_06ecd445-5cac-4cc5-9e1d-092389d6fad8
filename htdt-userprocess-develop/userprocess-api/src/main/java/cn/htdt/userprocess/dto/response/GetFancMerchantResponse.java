package cn.htdt.userprocess.dto.response;

import cn.htdt.usercenter.dto.response.ResFancGroupDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;


/**
 * <p>
 * 商家组织信息
 * </p>
 *
 */
@Data
@ApiModel(description = "商家返回实体类")
public class GetFancMerchantResponse implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * id
     */
    private Long id;
    /**
     * 编号
     */
    private String fanNo;

    /**
     * 姓名
     */
    private String name;
    /**
     * 电话
     */
    private String phone;
    /**
     * 出生日期
     */
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private LocalDate birthday;
    /**
     * 头像地址
     */
    private String headImg;

    /**
     * 性别 0 男 1 女 2保密
     */
    private Integer sex;

    /**
     * 首次关注店铺编号
     */
    private String followStoreNumber;

    /**
     * 首次关注店铺名称
     */
    private String followStoreName;
    /**
     * 状态 1粉丝 2代理人
     */
    private Integer type;


    /**
     * 省id
     */
    private String provinceCode;

    /**
     * 省名
     */
    private String provinceName;

    /**
     * 市id
     */
    private String cityCode;

    /**
     * 市名
     */
    private String cityName;

    /**
     * 区id
     */
    private String regionCode;

    /**
     * 区名
     */
    private String regionName;

    /**
     * 详细地址
     */
    private String detailAddress;


    /**
     * 街道code
     */
    private String streetCode;

    /**
     * 街道名称
     */
    private String streetName;

    /**
     * 街道名称
     */
    private Integer homeNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 粉丝分组list
     */
    private List<ResFancGroupDTO> resFancGroupDTOList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
