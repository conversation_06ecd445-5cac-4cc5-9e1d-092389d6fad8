package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * @program: htdt-userprocess
 * @ClassName: StoreInfoResponse
 * @description: 店铺信息响应实体
 * @author: <PERSON><PERSON>
 * @create: 2020-10-09 16:09
 **/
@Data
@ApiModel(description = "店铺响应基础实体类")
public class StoreBaseInfoResponse implements Serializable {

    @ApiModelProperty(notes = "店铺编号")
    private String storeNo;

    @ApiModelProperty(notes = "店铺名称")
    private String storeName;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}