package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 商家组织信息
 * </p>
 *
 */
@Data
@ApiModel(description = "商家信息响应实体类")
public class MerchantInfoResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(notes = "主键")
    private Long id;


    @ApiModelProperty(notes = "商家编号")
    private String merchantNo;

    /**
     * 商家名称
     */
    @ApiModelProperty(notes = "商家名称")
    private String merchantName;

    /**
     * 商家状态   -1：新引进  0：停用   1：正常   2：清退中
     */
    @ApiModelProperty(notes = "商家状态   1:启用 2:停用 3:注销")
    private String businessStatus;


    /**
     * 登录账户
     */
    @ApiModelProperty(notes = "登录账户")
    private String loginAccount;

    /**
     * 法人姓名
     */
    @ApiModelProperty(notes = "法人姓名")
    private String legalName;

    /**
     * 法人手机号
     */
    @ApiModelProperty(notes = "法人手机号")
    private String legalPhone;
    private String dsLegalPhone;

    /**
     * 经营人姓名
     */
    @ApiModelProperty(notes = "经营人姓名")
    private String operatorName;

    /**
     * 经营人手机号
     */
    @ApiModelProperty(notes = "经营人手机号")
    private String operatorPhone;

    /**
     * 经营人手机号
     */
    @ApiModelProperty(notes = "经营人手机号加密")
    private String dsOperatorPhone;

    /**
     * 公司名称
     */
    @ApiModelProperty(notes = "公司名称")
    private String companyName;

    /**
     * 企业注册地址
     */
    @ApiModelProperty(notes = "企业注册地址")
    private String regeisterAddress;

    /**
     * 实际经营地址
     */
    @ApiModelProperty(notes = "实际经营地址")
    private String operateAddress;

    /**
     * 法人身份证号
     */
    @ApiModelProperty(notes = "法人身份证号")
    private String legalCard;

    /**
     * 所属行业
     */
    @ApiModelProperty(notes = "所属行业")
    private String industry;

    @ApiModelProperty(notes = "所属行业名称")
    private String industryName;

    /**
     * 会员编号
     */
    @ApiModelProperty(notes = "会员编号")
    private String memberNo;

    /**
     * 会员编码
     */
    @ApiModelProperty(notes = "会员编码")
    private String merberCode;

    /**
     * erp编码
     */
    @ApiModelProperty(notes = "erp编码")
    private String erpCode;

    /**
     * 所属公司编号
     */
    @ApiModelProperty(notes = "所属公司编号")
    private String companyNo;

    /**
     * 经理人编号
     */
    @ApiModelProperty(notes = "经理人编号")
    private String managerNo;

    /**
     * 支付账户
     */
    @ApiModelProperty(notes = "支付账户")
    private String payAccount;

    /**
     * 会员标签
     */
    @ApiModelProperty(notes = "会员标签")
    private String merberLaber;


    @ApiModelProperty(notes = "是否展示")
    private String showFlag;

    /**
     * 营业执照
     */
    @ApiModelProperty(notes = "营业执照")
    private String businessLicense;

    @ApiModelProperty(notes = "来源")
    private String source;


    /**
     * 归属分布名称
     */
    @ApiModelProperty(notes = "归属分布名称")
    private String branchName;


    @ApiModelProperty(notes = "归属分布编号")
    private String branchNo;

    @ApiModelProperty(notes = "是否实名 : 0:未实名  1已实名")
    private Integer authFlag;

    /**
     * 电签完成状态 1 未完成 2 完成 3 同步机构信息完成（需要继续调用机构管理员账号同步）
     */
    @ApiModelProperty(notes = "电签完成状态 1 未完成 2 完成 3 同步机构信息完成（需要继续调用机构管理员账号同步）")
    private Integer elecStatus;

    @ApiModelProperty(notes = "首个店铺的店铺编码")
    private String firstStoreNo;

    @ApiModelProperty(notes = "是否单店 : 0：否 1：是")
    private Integer singleFlag;

    @ApiModelProperty(notes = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(notes = "所属系统 枚举：BelongSystemEnum")
    private Integer belongSystem;

    @ApiModelProperty(notes = "归属公司 枚举：BelongCompanyEnum 1000-汇通数科 1001-汇通达")
    private Integer belongCompany;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
