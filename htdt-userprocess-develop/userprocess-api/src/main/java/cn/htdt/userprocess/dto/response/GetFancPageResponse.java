package cn.htdt.userprocess.dto.response;

import cn.htdt.common.enums.constants.NumConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * <p>
 * 商家组织信息
 * </p>
 *
 */
@Data
@ApiModel(description = "平台返回实体类")
public class GetFancPageResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    private String fanNo;

    /**
     * 姓名
     */
    private String name;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 电话
     */
    private String phone;

    /**
     * 头像地址
     */
    private String headImg;

    /**
     * 出生日期
     */
    private LocalDate birthday;

    /**
     * 首次归属商家编号
     */
    private String ascriptionMerchantNumber;

    /**
     * 首次归属商家名称
     */
    private String ascriptionMerchantName;

    /**
     * 首次关注店铺编号
     */
    private String followStoreNumber;

    /**
     * 首次关注店铺名称
     */
    private String followStoreName;

    /**
     * 首次注册来源
     */
    private String registeredSource;

    /**
     * 注册时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime registeredTime;
    /**
     * 状态 1粉丝 2代理人
     */
    private Integer type;

    /**
     * 粉丝积分
     */
    private Integer fansPoint = NumConstant.ZERO;
    /**
     * 商家账号
     */
    private String merberCode;

    @ApiModelProperty(notes = "所属店铺编号")
    private String storeNo;

    @ApiModelProperty(notes = "所属店铺名称")
    private String storeName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
