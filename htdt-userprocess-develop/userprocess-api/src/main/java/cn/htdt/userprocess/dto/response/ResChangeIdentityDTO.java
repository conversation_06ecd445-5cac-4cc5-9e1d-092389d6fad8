package cn.htdt.userprocess.dto.response;

import cn.htdt.usercenter.dto.response.ResUOperationDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName ResChangeIdentityDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/4/2 9:20
 */
@Data
@ApiModel(description = "切换平台身份")
public class ResChangeIdentityDTO implements Serializable {

    private static final long serialVersionUID = 3225927999271440415L;

    @ApiModelProperty(notes = "菜单树")
    List<PrivilegeTreeResponse> menuTreeDTOS;

    @ApiModelProperty(notes = "操作")
    List<ResUOperationDTO> operationDTOS;

    @ApiModelProperty(notes = "菜单编号")
    List<String> menuNos;

    @ApiModelProperty(notes = "用户账号")
    private String userNo;

    @ApiModelProperty(notes = "用户名称")
    private String userName;

    @ApiModelProperty(notes = "名称")
    private String name;

    @ApiModelProperty(notes = "商家编号")
    private String merchantNo;

    @ApiModelProperty(notes = "店铺编号")
    private String storeNo;

    @ApiModelProperty(value = "是否商家主账号 1-是；0-不是")
    private Integer isMerchantUser;

    @ApiModelProperty(value = "试用账号 1-(默认)正常账号；2-试用账号")
    private Integer trialAccount;

    @ApiModelProperty(value = "是否是实体店铺 1-(默认)虚拟店铺 2-实体店铺")
    private Integer isPhysicalStore;

    @ApiModelProperty(notes = "是否含有独立小程序 1-未开通独立小程序 2-已开通独立小程序")
    private Integer isHaveApplet;

    @ApiModelProperty(notes = "是否首次登录")
    private Integer isFirstLogin;

    @ApiModelProperty(notes = "是否含有小程序权限 1-否 2-是")
    private Integer isMiniPrograme;

    @ApiModelProperty(notes = "归属公司 1000-汇通数科 1001-汇通达")
    private Integer belongCompany;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
