package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @program: userprocess
 * @ClassName: ResUUserResponse
 * @description: 用户响应实体
 * @author: <PERSON><PERSON>
 * @create: 2020-09-09 15:55
 **/

@Data
@ApiModel(description = "用户响应实体类")
public class  ResUUserResponse implements Serializable {


    private static final long serialVersionUID = -8352362517019216702L;
    @ApiModelProperty(notes = "主键")
    private Long id;

    @ApiModelProperty(notes = "用户帐户")
    private String userNo;

    @ApiModelProperty(notes = "手机")
    private String mobile;
    /**
     * 用户名
     */
    @ApiModelProperty(notes = "用户名")
    private String username;
    /**
     * 密码
     */
    @ApiModelProperty(notes = "密码")
    private String password;

    /**
     * 后端盐值
     */
    @ApiModelProperty(notes = "后端盐值")
    private String bSalt;


    /**
     * 备注
     */
    @ApiModelProperty(notes = "备注")
    private String remarks;

    /**
     * 工号
     */
    @ApiModelProperty(notes = "工号")
    private String employeNum;

    /**
     * 用户身份（逗号拼接）：0-系统；1-运营；2-商家；3-店铺
     */
    @ApiModelProperty(notes = "用户身份（逗号拼接）：0-系统；1-运营；2-商家；3-店铺")
    private String type;

    /**
     * 公司ID
     */
    @ApiModelProperty(notes = "公司编号")
    private String companyNo;

    /**
     * 注册来源：1-注册会员 2-手工录入 4-试用会员 8-试用转正式
     */
    @ApiModelProperty(notes = "注册来源：1-注册会员 2-手工录入 4-试用会员 8-试用转正式")
    private Integer source;

    /**
     * 前端盐值
     */
    @ApiModelProperty(notes = "前端盐值")
    private String fSalt;

    /**
     * 状态 0停用 1启用
     */
    @ApiModelProperty(notes = "状态 0停用 1启用")
    private Integer status;

    /**
     * 部门
     */
    @ApiModelProperty(notes = "部门")
    private String departName;

    /**
     * 冻结状态 0冻结 1解冻
     */
    @ApiModelProperty(notes = "冻结状态 0冻结 1解冻")
    private Integer frezenStatus;

    /**
     * 冻结开始时间
     */
    @ApiModelProperty(notes = "冻结开始时间")
    private LocalDateTime frezenStart;

    /**
     * 冻结结束时间
     */
    @ApiModelProperty(notes = "冻结结束时间")
    private LocalDateTime frezenEnd;

    /**
     * 异地登录状态 0关闭 1开启
     */
    @ApiModelProperty(notes = "异地登录状态 0关闭 1开启")
    private Integer remoteLogin;


    /**
     * 微信开放平台标识
     */
    @ApiModelProperty(notes = "微信开放平台标识")
    private String unionid;

    @ApiModelProperty(notes = "头像")
    private String headImg;

    @ApiModelProperty(notes = "商家或者店铺名称")
    private String entityName;

    @ApiModelProperty(notes = "商家或者店铺编号")
    private String entityNo;

    @ApiModelProperty(notes = "账户类型 1-运营；2-商家；3-店铺")
    private Integer accountType;

    /**
     * 用户子类型 0：运营主账户 、1：运营员工   2：商家主账号  3：商家员工  4：店铺主账号  5：店铺员工
     */
    @ApiModelProperty(notes = " 用户子类型")
    private Integer identitySubType;

    /**
     * 性别 0 男 1 女 2保密
     */
    @ApiModelProperty(notes = " 性别 0 男 1 女 2保密")
    private Integer sex;

    /**
     * 出生日期
     */
    @ApiModelProperty(notes = "出生日期" )
    private LocalDateTime birthday;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
