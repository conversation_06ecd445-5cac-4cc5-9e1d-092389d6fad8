package cn.htdt.userprocess.dto.publicDto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @program: htdt-userprocess
 * @ClassName: StoreInfoResponse
 * @description: 店铺信息响应实体
 * @author: Jacky
 * @create: 2020-10-09 16:09
 **/
@Data
@ApiModel(description = "店铺响应实体类")
public class SearchStoreResponse implements Serializable {

    private static final long serialVersionUID = -8779977275595383659L;


    @ApiModelProperty(notes = "店铺编号")
    private String storeNo;


    @ApiModelProperty(notes = "店铺名称")
    private String storeName;

    /**
     * 门头照片url
     */
    @ApiModelProperty(notes = "门头照片url")
    private String storePhoto;

    /**
     * 详细地址
     */
    @ApiModelProperty(notes = "详细地址")
    private String detailAddress;


    @ApiModelProperty(notes = "是否关注 0否 1是")
    private Integer attentionFlag;


    @ApiModelProperty(notes = "店铺精度")
    private BigDecimal longitude;


    @ApiModelProperty(notes = "店铺维度")
    private BigDecimal latitude;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}