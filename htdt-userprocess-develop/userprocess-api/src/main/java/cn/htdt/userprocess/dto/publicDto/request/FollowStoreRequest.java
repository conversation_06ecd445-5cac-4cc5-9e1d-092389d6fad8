package cn.htdt.userprocess.dto.publicDto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
/**
  *
  * @Description : 粉丝注册请求实体
  * <AUTHOR> 高繁
  * @date : 2021/11/24 15:22
 */
@Data
@ApiModel(description = "粉丝关注店铺请求实体")
public class FollowStoreRequest implements Serializable {


    private static final long serialVersionUID = 6633728267582022649L;

    @ApiModelProperty(notes = "粉丝编号")
    private String fanNo;

    @ApiModelProperty(notes = "门店编号")
    private String storeNo;

    @ApiModelProperty(notes = "粉丝来源")
    private String source;

    @ApiModelProperty(notes = "活动编码")
    private String promotionNo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
