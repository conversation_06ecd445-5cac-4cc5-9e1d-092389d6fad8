package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 菜单表
 * </p>
 *
 * @since 2020-08-31
 */
@Data
@ApiModel(description = "权限树状实体类")
public class PrivilegeTreeResponse implements Serializable {



    private static final long serialVersionUID = -8209329404473874417L;

    @ApiModelProperty(notes = "菜单编号")
    private String id;

    @ApiModelProperty(notes = "父菜单编号")
    private String pid;

    @ApiModelProperty(notes = "菜单编号")
    private String menuNo;
    /**
     * 菜单CODE
     */
    @ApiModelProperty(notes = "菜单编码")
    private String menuCode;
    /**
     * 菜单名称
     */
    @ApiModelProperty(notes = "菜单名称")
    private String menuName;
    /**
     * 访问路径
     */
    @ApiModelProperty(notes = "访问路径")
    private String path;

    /**
     * 菜单图片地址
     */
    @ApiModelProperty(notes = "菜单图片地址")
    private String imgPath;

    /**
     * 父菜单CODE
     */
    @ApiModelProperty(notes = "父菜单编码")
    private String parentMenuCode;

    @ApiModelProperty(notes = "菜单类型 （类型   0：目录   1：菜单）")
    private Integer type;

    /**
     * 父菜单名称
     */
    @ApiModelProperty(notes = "父菜单名称")
    private String parentMenuName;

    /**
     * 显示图标
     */
    @ApiModelProperty(notes = "显示图标")
    private String icon;
    /**
     * 排序
     */
    @ApiModelProperty(notes = "排序")
    private Integer sortValue;


    @ApiModelProperty(notes = "是否菜单 0 否 1是")
    private Integer isMenu;


    @ApiModelProperty(notes = "子菜单")
    private List<PrivilegeTreeResponse> children;


    @ApiModelProperty(notes = "按钮")
    private List<OperationResponse> operationList;

    @ApiModelProperty(notes = "菜单所属角色")
    private List<String> roles;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
