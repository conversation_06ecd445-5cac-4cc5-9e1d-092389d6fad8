package cn.htdt.userprocess.dto.response;


import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <p>
 * 分组信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-24
 */
@Data
@ApiModel(description = "分组详细信息")
public class GetFancGroupInfoResponse extends ReqComPageDTO implements Serializable {


    /**
     * 编号
     */
    @ApiModelProperty(notes = "编号")
    private String groupNo;

    /**
     * 店铺编号
     */
    @ApiModelProperty(notes = "店铺编号")
    private String storeNumber;

    /**
     * 店铺名称
     */
    @ApiModelProperty(notes = "店铺名称")
    private String storeName;

    /**
     * 分组名称
     */
    @ApiModelProperty(notes = "分组名称")
    private String groupName;

    /**
     * 粉丝人数
     */
    @ApiModelProperty(notes = "粉丝人数")
    private Integer fanNum;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
