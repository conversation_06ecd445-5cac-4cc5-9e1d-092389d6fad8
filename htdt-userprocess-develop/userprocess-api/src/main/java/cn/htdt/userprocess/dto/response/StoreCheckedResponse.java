package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 商家的店铺信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-4
 */
@Data
@ApiModel(description = "店铺响应实体类")
public class StoreCheckedResponse implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(notes = "树形店铺")
    private List<StoreResponse> storeResponses;

    @ApiModelProperty(notes = "选中店铺")
    private List<String> checked;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
