package cn.htdt.userprocess.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-03
 **/
@Data
@ApiModel(description = "区域响应实体类")
public class UcregionRoleInfoResponse implements Serializable {

    private static final long serialVersionUID = -8779977275595383659L;


    /**
     * 角色与区域关联记录的标识
     */
    @ApiModelProperty(notes = "标识")
    private Long id;

    /**
     * 角色编号
     */
    @ApiModelProperty(notes = "角色 id")
    private String roleNo;

    /**
     * 区域编码
     */
    @ApiModelProperty(notes = "区域编码")
    private String regionNo;

    /**
     * 创建人名称
     */
    @ApiModelProperty(notes = "创建人名称")
    private String createName;

    /**
     * 操作时间
     */
    @ApiModelProperty(notes = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 最后一次修改人编号
     */
    @ApiModelProperty(notes = "最后一次修改人 ID")
    private String modifyNo;

    /**
     * 最后一次修改人名称
     */
    @ApiModelProperty(notes = "最后一次修改人名称")
    private String modifyName;

    /**
     * 最后一次修改时间
     */
    @ApiModelProperty(notes = "最后一次修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime modifyTime;

    /**
     * 删除标志，默认 1 表示未删除，其他值表示已删除
     */
    @ApiModelProperty(notes = "是否已经删除，默认 1 未删除，其余已删除")
    private Integer deleteFlag;

    /**
     * 类型
     */
    @ApiModelProperty(notes = "类型")
    private String type;

    /**
     * 状态
     */
    @ApiModelProperty(notes = "状态")
    private String status;

    /**
     * 所属商家编号
     */
    @ApiModelProperty(notes = "所属商家编号")
    private String merchantNo;
    /**
     * 所属商家 id
     */
    @ApiModelProperty(notes = "所属商家 id")
    private String merchantId;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
