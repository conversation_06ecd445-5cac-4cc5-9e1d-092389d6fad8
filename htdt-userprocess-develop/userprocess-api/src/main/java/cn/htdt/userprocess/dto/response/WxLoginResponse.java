package cn.htdt.userprocess.dto.response;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;


@Data
@ApiModel(description = "微信登录返回值")
public class WxLoginResponse  implements Serializable {


    private static final long serialVersionUID = 6633728267582022649L;


    @ApiModelProperty(notes = "微信unionid")
    private String unionid;

    @ApiModelProperty(notes = "登录token")
    private String token;

    @ApiModelProperty(notes = "用户账号")
    private String userNo;

    @ApiModelProperty(notes = "手机")
    private String mobile;

    @ApiModelProperty(notes = "手机-密文")
    private String dsMobile;
    /**
     * 用户名
     */
    @ApiModelProperty(notes = "用户名")
    private String username;

    @ApiModelProperty(notes = "商家编号")
    private String merchantNo;

    @ApiModelProperty(notes = "店铺编号")
    private String storeNo;

    @ApiModelProperty(value = "名称（商家名称/店铺名称）")
    private String name;

    @ApiModelProperty(notes = "头像")
    private String headImg;

    @ApiModelProperty(value = "用户身份 1-运营；2-商家；3-店铺")
    private Integer loginIdentity;

    @ApiModelProperty(value = "用户身份 1-运营；2-商家；3-店铺")
    private String type;

    @ApiModelProperty(value = "用户身份列表 1-运营；2-商家；3-店铺")
    private List<String> userIdentity;

    @ApiModelProperty(value = "异常登录场景 正常-空字符串；多账户-moreAccount；未绑定-noBind；密码错误3次-pwdError；异地登录-remoteLogin")
    private String loginErrorScene;

    @ApiModelProperty(value = "是否多账号 1-是；0-不是")
    private Integer isMoreAccount;

    @ApiModelProperty(value = "是否商家主账号 1-是；0-不是")
    private Integer isMerchantUser;

    @ApiModelProperty(notes = "是否首次登录")
    private Integer isFirstLogin;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
