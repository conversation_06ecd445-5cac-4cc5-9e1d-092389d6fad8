package cn.htdt.userprocess.dto.publicDto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * @ClassName IndexChangeStoreRequest
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/9/4 下午5:20
 */
@Data
@ApiModel(description = "首页切换店铺")
public class SearchStoreRequest implements Serializable {

    private static final long serialVersionUID = -8899043633460941697L;

    @ApiModelProperty(value = "粉丝编号")
    private String fanNo;

    @ApiModelProperty(value = "店铺名称",required = true)
    private String storeName;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "每页条数")
    private int pageSize = 10;

    @ApiModelProperty(value = "总页数")
    private int pageNum = 1;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
