package cn.htdt.userprocess.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 商家组织信息
 * </p>
 *
 */
@Data
@ApiModel(description = "商家响应实体类")
public class GetPageMerchantResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(notes = "主键")
    private Long id;

    @ApiModelProperty(notes = "商家编号")
    private String merchantNo;

    /**
     * 商家名称
     */
    @ApiModelProperty(notes = "商家名称")
    private String merchantName;

    /**
     * 商家状态    0：停用   1：正常
     */
    @ApiModelProperty(notes = "商家状态    0：停用   1：正常   ")
    private String businessStatus;

    /**
     * 法人姓名
     */
    @ApiModelProperty(notes = "法人姓名")
    private String legalName;

    /**
     * 法人手机号
     */
    @ApiModelProperty(notes = "法人手机号")
    private String legalPhone;

    /**
     * 经营人姓名
     */
    @ApiModelProperty(notes = "经营人姓名")
    private String operatorName;

    /**
     * 经营人手机号
     */
    @ApiModelProperty(notes = "经营人手机号")
    private String operatorPhone;

    /**
     * 归属分布名称
     */
    @ApiModelProperty(notes = "归属分布名称")
    private String branchName;


    @ApiModelProperty(notes = "公司名称")
    private String companyName;

    /**
     * 来源
     */
    @ApiModelProperty(notes = "来源")
    private String source;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(notes = "创建时间")
    private LocalDateTime createTime;

    /**
     * 商家账号
     */
    @ApiModelProperty(value = "商家账号")
    private String merberCode;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
