package cn.htdt.userprocess.dto.response;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021-07-26
 * @description 粉丝进店记录响应DTO
 **/
@Data
public class ResUcFansBrowseRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 浏览编号
     */
    private String browseNo;

    /**
     * 粉丝编号
     */
    private String fanNo;

    /**
     * 店铺编号
     */
    private String storeNo;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 应用类型
     */
    private Integer appType;

    /**
     * 粉丝浏览总量
     */
    private Integer totalFansBrowseRecordCount;

}
