package cn.htdt.userprocess.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName ReqAgentStoreDTO
 * @Description 代理人信息
 * <AUTHOR>
 * @Date 2021/1/15 16:11
 */
@Data
@ApiModel(description = "代理人分页请求实体类")
public class AgentPageResponse implements Serializable {

    /**
     * 代理人编号
     */
    @ApiModelProperty(notes = "代理人编号")
    private String agentNo;

    /**

    /**
     * 状态 0待审核 1已加入 2已退出
     */
    @ApiModelProperty(notes = "状态 0待审核 1已加入 2已退出")
    private Integer status;

    /**
     * 拉新人数
     */
    @ApiModelProperty(notes = "拉新人数")
    private Integer fanNum;

    /**
     * 锁粉人数
     */
    @ApiModelProperty(notes = "锁粉人数")
    private Integer lockNum;

    /**
     * 成为代理人天数
     */
    @ApiModelProperty(notes = "成为代理人天数")
    private Double agentDays;

    /**
     * 店铺编号
     */
    @ApiModelProperty(notes = "店铺编号")
    private String storeNo;

    /**
     * 店铺名称
     */
    @ApiModelProperty(notes = "店铺名称")
    private String storeName;

    /**
     * 商家编号
     */
    @ApiModelProperty(notes = "商家编号")
    private String merchantNo;

    /**
     * 商家名称
     */
    @ApiModelProperty(notes = "商家名称")
    private String merchantName;

    /**
     * 姓名
     */
    @ApiModelProperty(notes = "姓名")
    private String name;

    /**
     * 电话
     */
    @ApiModelProperty(notes = "电话")
    private String phone;

    /**
     * 头像地址
     */
    @ApiModelProperty(notes = "头像地址")
    private String headImg;

    /**
     * 加入时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(notes = "加入时间")
    private LocalDateTime joinTime;

    /**
     * 退出时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(notes = "退出时间")
    private LocalDateTime outTime;


    /**
     * 代理人分组编码
     */
    @ApiModelProperty(notes = "代理人分组名称")
    private String groupName;

    @ApiModelProperty(notes = "最后登录时间")
    private LocalDateTime lastLoginTime;

    @ApiModelProperty(notes = "会员编码")
    private String merberCode;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
