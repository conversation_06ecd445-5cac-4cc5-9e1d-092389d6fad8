package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * 千橙掌柜PC -> AI识别管理 -> 设备管理 -> 设备列表, 添加设备, 根据商家的htd账号, 查询商家信息和店铺数据
 *
 * <AUTHOR>
 * @since 2023-03-08
 */
@Data
@ApiModel(description = "商家和店铺息响应实体类")
public class ManyMerchantAndStoreSelectResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "会员编码")
    private String merberCode;

    @ApiModelProperty(value = "店铺数据")
    private List<StoreListResponse> storeList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
