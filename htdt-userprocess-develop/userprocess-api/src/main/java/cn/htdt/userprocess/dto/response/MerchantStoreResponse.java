package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalTime;

/**
 * @ClassName CurrentUserMerchantRequest
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/9/4 下午5:20
 */
@Data
@ApiModel(description = "商家的全部店铺")
public class MerchantStoreResponse implements Serializable {

    @ApiModelProperty(notes = "店铺开通会员的小程序二维码地址")
    private String memberOpeningWxCodeUrl;

    private static final long serialVersionUID = -8899043633460941697L;
    @ApiModelProperty(notes = "主键")
    private Long id;

    @ApiModelProperty(notes = "店铺编号")
    private String storeNo;

    @ApiModelProperty(notes = "店铺状态：1-正常；2-停用；3-已注销")
    private Integer storeStatus;

    @ApiModelProperty(notes = "联系人姓名")
    private String contactName;

    @ApiModelProperty(notes = "联系人手机号")
    private String contactPhone;

    @ApiModelProperty(notes = "店铺名称")
    private String storeName;

    @ApiModelProperty(notes = "门头照片url")
    private String storePhoto;

    @ApiModelProperty(notes = "详细地址")
    private String detailAddress;

    @ApiModelProperty(notes = "营业开始时间")
    private LocalTime businessStart;

    @ApiModelProperty(notes = "营业结束时间")
    private LocalTime businessEnd;
    
    @ApiModelProperty(value = "店铺经营模式: 1001-直营模式, 1002-加盟模式, 1003-联营模式")
    private String businessModel;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
