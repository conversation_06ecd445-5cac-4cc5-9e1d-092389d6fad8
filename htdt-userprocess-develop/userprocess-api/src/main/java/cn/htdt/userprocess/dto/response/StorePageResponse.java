package cn.htdt.userprocess.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @program: htdt-userprocess
 * @ClassName: StoreInfoResponse
 * @description: 店铺信息响应实体
 * @author: Jacky
 * @create: 2020-10-09 16:09
 *
 * 修改:新增字段 temporaryProductConfigStatus
 * 修改原因 新增临时商品配置功能
 * <AUTHOR>
 * @since 2025-03
 **/
@Data
@ApiModel(description = "店铺响应实体类")
public class StorePageResponse implements Serializable {

    private static final long serialVersionUID = -8779977275595383659L;

    /**
     * 临时商品配置状态 0 关闭  1 开启  默认 1开启
     */
    @ApiModelProperty(notes = "临时商品配置状态 0关闭,1开启")
    private String temporaryProductConfigStatus;


    @ApiModelProperty(notes = "店铺编号")
    private String storeNo;

    @ApiModelProperty(notes = "店铺名称")
    private String storeName;

    /**
     * 所属商家id
     */
    @ApiModelProperty(notes = "所属商家id")
    private String merchantNo;


    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    /**
     * 店铺状态 0停用，1启用
     */
    @ApiModelProperty(notes = "店铺状态 0停用，1启用")
    private Integer storeStatus;

    @ApiModelProperty(notes = "联系人姓名")
    private String contactName;

    @ApiModelProperty(notes = "门头照片url")
    private String storePhoto;

    @ApiModelProperty(notes = "用户登录身份")
    private Integer loginIdentity;

    @ApiModelProperty(notes = "联系人手机号")
    private String contactPhone;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(notes = "创建时间")
    private LocalDateTime createTime;

    /**
     * 详细地址
     */
    @ApiModelProperty(notes = "详细地址")
    private String detailAddress;
    /**
     * 商家账号
     */
    @ApiModelProperty(value = "商家账号")
    private String merberCode;

    /**
     * uc_store表里的商家账号(实体店铺原商家编号)
     */
    @ApiModelProperty(value = "实体店铺原商家编号")
    private String storeMerberCode;

    @ApiModelProperty(value = "是否是实体店铺 1-虚拟店铺 2-实体店铺")
    private Integer isPhysicalStore;

    @ApiModelProperty(value = "是否会员共享：1否 2是")
    private Integer memberSharing;

    @ApiModelProperty(value = "单店标记 1 单店，0 多店")
    private Integer singleFlag;

    @ApiModelProperty(value = "0单店店铺 1-虚拟店铺 2-实体店铺 枚举:UserStoreTypeEnum  作为前端出入参使用")
    private Integer userStoreType;

    /**
     * 店铺经营模式, 参考枚举: StoreBusinessModelEnum
     */
    @ApiModelProperty(value = "店铺经营模式: 1001-直营模式, 1002-加盟模式, 1003-联营模式")
    private String businessModel;

    @ApiModelProperty(notes = "自定义店铺编码")
    private String customStoreNo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}