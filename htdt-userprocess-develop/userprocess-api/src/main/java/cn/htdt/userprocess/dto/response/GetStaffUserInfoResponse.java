package cn.htdt.userprocess.dto.response;


import cn.htdt.usercenter.dto.request.ReqUcRegionStoreRegionDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(description = "员工详情响应实体类")
public class GetStaffUserInfoResponse implements Serializable {



    @ApiModelProperty(notes = "区域状态  1 全部区域 2 否区域")
    private String regionStatus ;// 1 全部区域 2 否区域

    // 区域列表
    @ApiModelProperty(notes = "区域选择列表")
    private  List<ReqUcRegionStoreRegionDTO> ucRegionPageRequestList ;

    @ApiModelProperty(notes = "主键")
    private Long id;

    @ApiModelProperty(notes = "用户编号")
    private String userNo;

    @ApiModelProperty(notes = "手机")
    private String mobile;

    @ApiModelProperty(notes = "手机-密文")
    private String dsMobile;
    /**
     * 用户名
     */
    @ApiModelProperty(notes = "用户名")
    private String username;
    /**
     * 密码
     */
    @ApiModelProperty(notes = "密码")
    private String password;
    /**
     * 工号
     */
    @ApiModelProperty(notes = "工号")
    private String employeNum;
    /**
     * 角色
     */
    @ApiModelProperty(notes="角色")
    private List<String> roleList;

    /**
     * 角色
     */
    @ApiModelProperty(notes="角色名称")
    private List<String> roleNameList;

    /**
     * 是否管理员
     */
    @ApiModelProperty(notes = "是否管理员1否2是")
    private Integer isAdministrator;
    /**
     * 部门
     */
    @ApiModelProperty(notes = "部门")
    private String departName;

    /**
     * 备注
     */
    @ApiModelProperty(notes = "备注")
    private String remarks;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
