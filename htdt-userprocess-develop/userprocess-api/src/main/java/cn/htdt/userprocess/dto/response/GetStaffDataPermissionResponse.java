package cn.htdt.userprocess.dto.response;


import cn.htdt.usercenter.dto.response.ResUMerchantAccountInfoStoreDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(description = "员工数据权限响应实体类")
public class GetStaffDataPermissionResponse implements Serializable {

    @ApiModelProperty(notes = "主键")
    private Long id;

    @ApiModelProperty(notes = "手机号")
    private String mobile;

    @ApiModelProperty(notes = "用户账号")
    private String userNo;

    @ApiModelProperty(notes = "商家名称")
    private String merchantName;

    @ApiModelProperty(notes = "商家编号")
    private String merchantNo;

    @ApiModelProperty(notes = "待分配数据")
    private List<ResUMerchantAccountInfoStoreDTO> noStoreList;

    @ApiModelProperty(notes = "已分配数据")
    private List<ResUMerchantAccountInfoStoreDTO> haveStoreList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
