package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * @program: userprocess
 * @ClassName: ResUUcMerchantResponse
 * @description: 商家响应实体
 * @author: Jacky
 * @create: 2020-09-09 15:50
 **/
@Data
@ApiModel(description = "商家响应实体类")
public class ResUUcMerchantResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(notes = "主键")
    private Long id;

    @ApiModelProperty(notes = "商家编号")
    private String merchantNo;

    /**
     * 组织id
     */
    @ApiModelProperty(notes = "组织id")
    private String orgNo;

    /**
     * 商家类型 11：自营 12：入驻
     */
    @ApiModelProperty(notes = "商家类型 11：自营 12：入驻")
    private Integer merchantType;

    /**
     * 商家名称
     */
    @ApiModelProperty(notes = "商家名称")
    private String merchantName;

    /**
     * 组织编码
     */
    @ApiModelProperty(notes = "组织编码")
    private String orgCode;

    /**
     * 审核状态  0:待审核, 1:通过, 2:不通过
     */
    @ApiModelProperty(notes = "审核状态  0:待审核, 1:通过, 2:不通过")
    private String auditStatus;

    /**
     * 商家状态   -1：新引进  0：停用   1：正常   2：清退中
     */
    @ApiModelProperty(notes = "商家状态   -1：新引进  0：停用   1：正常   2：清退中")
    private String businessStatus;

    /**
     * 详细地址
     */
    @ApiModelProperty(notes = "详细地址")
    private String contactDetailAddress;

    /**
     * 备注
     */
    @ApiModelProperty(notes = "备注")
    private String contactRemark;

    /**
     * 区名
     */
    @ApiModelProperty(notes = "区名")
    private String contactRegionName;

    /**
     * 区code
     */
    @ApiModelProperty(notes = "区code")
    private Long contactRegionCode;

    /**
     * 市名
     */
    @ApiModelProperty(notes = "市名")
    private String contactCityName;

    /**
     * 市code
     */
    @ApiModelProperty(notes = "市code")
    private Long contactCityCode;

    /**
     * 省名
     */
    @ApiModelProperty(notes = "省名")
    private String contactProvinceName;

    /**
     * 省code
     */
    @ApiModelProperty(notes = "省code")
    private Long contactProvinceCode;

    /**
     * 联系人手机号
     */
    @ApiModelProperty(notes = "联系人手机号")
    private String contactMobileNo;

    /**
     * 联系人姓名
     */
    @ApiModelProperty(notes = "联系人姓名")
    private String contactName;

    /**
     * 公司ID
     */
    @ApiModelProperty(notes = "公司ID")
    private String companyNo;

    /**
     * 创建人ID
     */
    @ApiModelProperty(notes = "创建人ID")
    private String createNo;

    /**
     * 最后一次修改人ID
     */
    @ApiModelProperty(notes = "最后一次修改人ID")
    private String modifyNo;

    /**
     * 街道code
     */
    @ApiModelProperty(notes = "街道编码")
    private Long streetCode;

    @ApiModelProperty(notes = "街道名称")
    private String streetName;

    /**
     * 身份证正面照
     */
    @ApiModelProperty(notes = "身份证正面照")
    private String cardPhotoFront;

    /**
     * 身份证背面照
     */
    @ApiModelProperty(notes = "身份证背面照")
    private String cardPhotoBack;

    /**
     * 身份证手持照
     */
    @ApiModelProperty(notes = "身份证手持照")
    private String cardPhotoHand;

    /**
     * 公司名称
     */
    @ApiModelProperty(notes = "公司名称")
    private String companyName;

    /**
     * 所属行业
     */
    @ApiModelProperty(notes = "所属行业")
    private String industry;

    /**
     * 所属部门
     */
    @ApiModelProperty(notes = "所属部门")
    private String departName;

    /**
     * 所属平台
     */
    @ApiModelProperty(notes = "所属平台")
    private String plateform;

    /**
     * 经营范围
     */
    @ApiModelProperty(notes = "经营范围")
    private String businessScope;

    /**
     * 营业执照url
     */
    @ApiModelProperty(notes = "营业执照url")
    private String businessLicenseUrl;

    /**
     * 营业执照号码
     */
    @ApiModelProperty(notes = "营业执照号码")
    private String businessLicense;

    /**
     * 是否有门店 0否 1是
     */
    @ApiModelProperty(notes = "是否有门店 0否 1是")
    private Integer merchantFlag;

    /**
     * 来源
     */
    @ApiModelProperty(notes = "来源")
    private Integer source;

    /**
     * 门头照片url
     */
    @ApiModelProperty(notes = "门头照片url")
    private String storePhoto;

    /**
     * 店内照片url(多)
     */
    @ApiModelProperty(notes = "店内照片url")
    private String inStorePhotos;

    /**
     * 法人身份证号
     */
    @ApiModelProperty(notes = "法人身份证号")
    private String cardNo;

    @ApiModelProperty(notes = "是否实名 : 0:未实名  1已实名")
    private Integer authFlag;

    /**
     * 电签完成状态 1 未完成 2 完成 3 同步机构信息完成（需要继续调用机构管理员账号同步）
     */
    @ApiModelProperty(notes = "电签完成状态 1 未完成 2 完成 3 同步机构信息完成（需要继续调用机构管理员账号同步）")
    private Integer elecStatus;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
