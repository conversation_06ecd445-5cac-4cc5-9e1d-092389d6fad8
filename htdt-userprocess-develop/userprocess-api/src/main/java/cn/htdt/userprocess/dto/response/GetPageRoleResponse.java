package cn.htdt.userprocess.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@ApiModel(description = "角色响应实体类")
public class GetPageRoleResponse implements Serializable {

    @ApiModelProperty(notes = "主键")
    private Long id;

    @ApiModelProperty(notes = "角色id")
    private String roleNo;

    /**
     * 角色编码,方便业务系统赋权限
     */
    @ApiModelProperty(notes = "角色编码")
    private String roleCode;

    /**
     * 所属平台编码
     */
    @ApiModelProperty(notes = "所属平台编码")
    private String platformCode;


    /**
     * 角色实体类型 默认平台 1商家 2分销
     */
    @ApiModelProperty(notes = "角色实体类型 默认平台 1商家 2分销")
    private Integer entityType;


    /**
     * 角色名称
     */
    @ApiModelProperty(notes = "角色名称")
    private String roleName;

    /**
     * 角色描述
     */
    @ApiModelProperty(notes = "角色描述")
    private String roleDesc;

    @ApiModelProperty(notes = "角色状态  1启用 2停用")
    private Integer roleStatus;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(notes = "创建时间")
    private LocalDateTime createTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
