package cn.htdt.userprocess.dto.response.user;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class ResMemberOpeningTimeDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 有效开始日期
     */
    private LocalDateTime effectiveTime;

    /**
     * 有效结束日期
     */
    private LocalDateTime invalidTime;

    /**
     * 记录流水的有效开始日期
     */
    private LocalDateTime effectiveTimeRecord;
}
