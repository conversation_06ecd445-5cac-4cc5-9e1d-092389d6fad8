package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(description = "获取多账户信息返回实体")
public class MoreAccountResponse  implements Serializable {


    private static final long serialVersionUID = 6633728267582022649L;

    @ApiModelProperty(notes = "平台账户")
    private List<Account> platformList;

    @ApiModelProperty(notes = "商家账户")
    private List<Account> merchantList;

    @ApiModelProperty(notes = "店铺账户")
    private List<Account> storeList;

    @Data
    public  class Account implements Serializable {

        @ApiModelProperty(notes = "账户")
        private String userNo;

        @ApiModelProperty(notes = "账户身份")
        private Integer accountType;

        @ApiModelProperty(notes = "商家编号")
        private String merchantNo;

        @ApiModelProperty(notes = "商家名称")
        private String merchantName;

        @ApiModelProperty(notes = "店铺编号")
        private String storeNo;

        @ApiModelProperty(notes = "店铺名称")
        private String storeName;

        @ApiModelProperty(notes = "归属公司 1000-汇通数科 1001-汇通达")
        private Integer belongCompany;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
