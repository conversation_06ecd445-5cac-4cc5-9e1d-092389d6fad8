package cn.htdt.userprocess.dto.publicDto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * @ClassName FanChangeInfoRequest
 * @Description 粉丝修改个人信息请求实体
 * <AUTHOR>
 * @Date 2020/9/1 下午2:40
 */
@Data
@ApiModel(description = "粉丝修改个人信息请求实体")
public class ChangeFanAddressRequest implements Serializable {


    private static final long serialVersionUID = 6633728267582022649L;

    @ApiModelProperty(notes = "粉丝编号",required = true)
    private String fanNo;

    /**
     * 省id
     */
    @ApiModelProperty(notes = "省id")
    private String provinceCode;

    /**
     * 省名
     */
    @ApiModelProperty(notes = "省名")
    private String provinceName;

    /**
     * 市id
     */
    @ApiModelProperty(notes = "市id")
    private String cityCode;

    /**
     * 市名
     */
    @ApiModelProperty(notes = "市名")
    private String cityName;

    /**
     * 区id
     */
    @ApiModelProperty(notes = "区id")
    private String regionCode;

    /**
     * 区名
     */
    @ApiModelProperty(notes = "区名")
    private String regionName;

    /**
     * 详细地址
     */
    @ApiModelProperty(notes = "详细地址")
    private String detailAddress;


    /**
     * 街道code
     */
    @ApiModelProperty(notes = "街道code")
    private String streetCode;

    /**
     * 街道名称
     */
    @ApiModelProperty(notes = "街道名称")
    private String streetName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
