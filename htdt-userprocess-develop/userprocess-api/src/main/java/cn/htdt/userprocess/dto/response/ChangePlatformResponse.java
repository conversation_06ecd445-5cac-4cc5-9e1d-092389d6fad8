package cn.htdt.userprocess.dto.response;

import cn.htdt.usercenter.dto.response.ResUOperationDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName CurrentUserMerchantRequest
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/9/4 下午5:20
 */
@Data
@ApiModel(description = "切换商家返回权限")
public class ChangePlatformResponse implements Serializable {

    private static final long serialVersionUID = 3225927999271440415L;

    @ApiModelProperty(notes = "操作",required = true)
    List<ResUOperationDTO> operationDTOS;

    @ApiModelProperty(notes = "菜单编号",required = true)
    List<String> menuNos;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
