package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <p>
 * 代理人注册分享实体类
 * </p>
 *
 */
@Data
@ApiModel(description = "代理人注册分享实体类")
public class ResAgentRegisterResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商家名称
     */
    @ApiModelProperty(notes = "粉丝编号")
    private String fanNo;

    @ApiModelProperty(notes = "粉丝姓名")
    private String fanName;

    @ApiModelProperty(notes = "粉丝手机号")
    private String fanPhone;

    @ApiModelProperty(notes = "粉丝手机号 加密")
    private String dsFanPhone;

    @ApiModelProperty(notes = "代理人编号")
    private String agentNo;

    @ApiModelProperty(notes = "代理人姓名")
    private String agentName;

    @ApiModelProperty(notes = "代理人手机号")
    private String agentMobile;

    @ApiModelProperty(notes = "代理人手机号 加密")
    private String dsAgentMobile;

    @ApiModelProperty(notes = "商家编号")
    private String merchantNo;

    @ApiModelProperty(notes = "店铺编号")
    private String storeNo;

    @ApiModelProperty(notes = "1否 2是")
    private String newFansFlag;

    @ApiModelProperty(notes = "token")
    private String token;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
