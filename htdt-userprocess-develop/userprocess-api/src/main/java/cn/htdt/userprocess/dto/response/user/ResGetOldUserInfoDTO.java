package cn.htdt.userprocess.dto.response.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

@Data
@ApiModel(description = "老用户信息返回实体类")
public class ResGetOldUserInfoDTO implements Serializable {

    private static final long serialVersionUID = -80646377811894596L;

    @ApiModelProperty(notes = "手机号")
    private String phone;

    @ApiModelProperty(notes = "账户")
    private String userNo;

    @ApiModelProperty(notes = "用户名")
    private String username;

    @ApiModelProperty(notes = "账户身份 实际身份1-平台，2-商家，4-店铺，8-单店")
    private Integer accountType;

    @ApiModelProperty(value = "是否商家主账号 1-是；0-不是")
    private Integer isMerchantUser;

    @ApiModelProperty(value = "试用账户 1-(默认)正常账户；2-试用账户")
    private Integer trialAccount;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
