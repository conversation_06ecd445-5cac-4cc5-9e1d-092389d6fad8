package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 店铺支付账户绑定记录响应类
 *
 * <AUTHOR>
 */
@Data
public class StorePayAccountRecordResponse implements Serializable {

    private static final long serialVersionUID = -4562582532186922254L;

    @ApiModelProperty(notes = "店铺编号")
    private String storeNo;

    @ApiModelProperty(notes = "店铺名称")
    private String storeName;

    @ApiModelProperty(notes = "公司名称")
    private String companyName;

    @ApiModelProperty(notes = "商家编号")
    private String merchantNo;

    @ApiModelProperty(notes = "会员编号")
    private String merberCode;

    @ApiModelProperty(notes = "支付收款账户")
    private String payAccount;

    @ApiModelProperty(notes = "店铺支付收款账户")
    private String storePayAccount;

    @ApiModelProperty(notes = "绑定时间")
    private LocalDateTime bindingTime;

    @ApiModelProperty(notes = "是否是原生账户 1 否 2 是")
    private Integer originalAccountFlag = 1;

}
