package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(description = "角色响应实体类")
public class GetRoleInfoResponse implements Serializable {

    @ApiModelProperty(notes = "主键")
    private Long id;

    @ApiModelProperty(notes = "角色编号")
    private String roleNo;


    /**
     * 所属平台编码
     */
    @ApiModelProperty(notes = "所属平台编码")
    private String platformCode;

    /**
     * 角色名称
     */
    @ApiModelProperty(notes = "角色名称")
    private String roleName;

    /**
     * 角色描述
     */
    @ApiModelProperty(notes = "角色描述")
    private String roleDesc;

    /**
     * 平台id
     */
    @ApiModelProperty(notes = "平台id")
    private String platformNo;

    @ApiModelProperty(notes = "商家编号名称")
    private String merchantNo;

    @ApiModelProperty(notes = "商家名称")
    private String merchantName;

    @ApiModelProperty(notes = "角色实体类型 默认平台 2商家 4店铺 8单店")
    private Integer entityType;

    @ApiModelProperty(notes = "菜单权限")
    private List<PrivilegeTreeResponse> resUMenuDTOList;

    @ApiModelProperty(notes = "菜单权限-APP")
    private List<PrivilegeTreeResponse> appMenuDTOList;

    @ApiModelProperty(notes = "菜单权限-一体机")
    private List<PrivilegeTreeResponse> sccMenuDTOList;

    @ApiModelProperty(notes = "商家店铺")
    private List<StoreResponse> storeResponses;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
