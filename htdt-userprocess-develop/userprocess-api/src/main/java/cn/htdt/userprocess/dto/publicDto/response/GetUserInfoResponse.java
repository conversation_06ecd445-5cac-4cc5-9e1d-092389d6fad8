package cn.htdt.userprocess.dto.publicDto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName GetUserInfoRequest
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/20 14:54
 */
@Data
public class GetUserInfoResponse implements Serializable {


    private static final long serialVersionUID = -2854660849718535304L;

    @ApiModelProperty(notes = "用户账号")
    private String userNo;

    @ApiModelProperty(notes = "手机")
    private String mobile;
    /**
     * 用户名
     */
    @ApiModelProperty(notes = "用户名")
    private String username;

    /**
     * 状态 0绑定 1未绑定
     */
    @ApiModelProperty(notes = "状态 0绑定 1未绑定")
    private Integer status;


    @ApiModelProperty(notes = "头像")
    private String headImg;

    /**
     * 性别 0 男 1 女 2保密
     */
    @ApiModelProperty(notes = " 性别 0 男 1 女 2保密")
    private Integer sex;

    /**
     * 出生日期
     */
    @ApiModelProperty(notes = "出生日期" )
    private LocalDateTime birthday;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
