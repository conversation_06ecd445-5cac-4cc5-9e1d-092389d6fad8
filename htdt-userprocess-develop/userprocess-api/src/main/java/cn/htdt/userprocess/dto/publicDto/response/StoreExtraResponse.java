package cn.htdt.userprocess.dto.publicDto.response;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <p>
 * 商家的店铺信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-31
 */
@Data
public class StoreExtraResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    private String storeNo;

    private String storeName;
    /**
     * 门头照片url
     */
    private String storePhoto;

    private Integer attentionNum;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
