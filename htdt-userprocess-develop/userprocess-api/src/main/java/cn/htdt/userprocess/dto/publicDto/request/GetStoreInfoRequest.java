package cn.htdt.userprocess.dto.publicDto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * @ClassName GetStoreInfoRequest
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/20 14:54
 */
@Data
public class GetStoreInfoRequest implements Serializable {

    private static final long serialVersionUID = 6642761974840442812L;

    @ApiModelProperty(notes = "店铺账户",required = true)
    private String storeNo;

    @ApiModelProperty(notes = "店铺支付账户 查询条件二选一")
    private String payAccount;

    public GetStoreInfoRequest(String storeNo) {
        this.storeNo = storeNo;
    }

    public GetStoreInfoRequest() {
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
