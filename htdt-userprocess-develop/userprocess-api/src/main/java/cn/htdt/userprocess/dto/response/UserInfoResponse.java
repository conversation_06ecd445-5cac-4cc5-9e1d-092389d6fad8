package cn.htdt.userprocess.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@ApiModel(description = "用户请求实体类")
public class UserInfoResponse implements Serializable {


    private static final long serialVersionUID = 6389215920585329204L;


    @ApiModelProperty(notes = "用户账号")
    private String userNo;

    @ApiModelProperty(notes = "手机")
    private String mobile;

    @ApiModelProperty(notes = "手机")
    private String dsMobile;
    /**
     * 用户名
     */
    @ApiModelProperty(notes = "用户名")
    private String username;

    /**
     * 状态 0绑定 1未绑定
     */
    @ApiModelProperty(notes = "状态 0绑定 1未绑定")
    private Integer status;


    @ApiModelProperty(notes = "头像")
    private String headImg;

    /**
     * 性别 0 男 1 女 2保密
     */
    @ApiModelProperty(notes = " 性别 0 男 1 女 2保密")
    private Integer sex;

    /**
     * 出生日期
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(notes = "出生日期" )
    private LocalDateTime birthday;

    /**
     * 用户子类型 0：运营主账户 、1：运营员工   2：商家主账号  3：商家员工  4：店铺主账号  5：店铺员工
     */
    @ApiModelProperty(notes = " 用户子类型")
    private Integer identitySubType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
