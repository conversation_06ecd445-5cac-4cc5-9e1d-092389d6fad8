package cn.htdt.userprocess.dto.response;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <p>
 * 独立小程序信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-4
 */
@Data
public class AppletPrivilegeResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * storeNo
     **/
    private String storeNo;

    /**
     * merchantNo
     **/
    private String merchantNo;

    /**
     * 独立小程序appId
     **/
    private String appId;

    /**
     * 独立小程序名称
     **/
    private String appName;

    /**
     * 独立小程序状态
     **/
    private Integer appStatus;

    /**
     * 独立小程序图标
     **/
    private String appHeadImageUrl;

    /**
     * 独立小程序原始id
     **/
    private String appOriginalId;

    /**
     * 公众号二维码
     **/
    private String publicQrcodeUrl;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
