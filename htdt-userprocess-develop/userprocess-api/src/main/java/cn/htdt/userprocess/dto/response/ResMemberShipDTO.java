package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 详细说明.
 * <p>
 * Copyright: Copyright (c) 2020/10/19 18:37
 * <p>
 * Company: htd
 * <p>
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@ApiModel
public class ResMemberShipDTO implements Serializable {

    private static final long serialVersionUID = 4632147583115599502L;

    @ApiModelProperty(notes = "会员ID", example = "251")
    private Long memberId;

    @ApiModelProperty(notes = "会员编码", example = "htd20199090902")
    private String memberCode;

    @ApiModelProperty(notes = "会员类型 1-非会员 2-产业会员 3-标准会员，9:注销会员", example = "1")
    private String memberFlag;

    @ApiModelProperty(notes = "公司名称", example = "江苏峰华空调设备有限公司")
    private String companyName;

    @ApiModelProperty(notes = "公司编号", example = "926411")
    private String companyCode;

    @ApiModelProperty(notes = "所在地-省", example = "32")
    private String locationProvince;

    @ApiModelProperty(notes = "所在地-市", example = "3212")
    private String locationCity;

    @ApiModelProperty(notes = "所在地-区", example = "321282")
    private String locationCounty;

    @ApiModelProperty(notes = "所在地-镇", example = "*********")
    private String locationTown;

    @ApiModelProperty(notes = "所在地-详细", example = "详细地址")
    private String locationDetail;

    @ApiModelProperty(notes = "所在地-详细 密文", example = "详细地址")
    private String dsLocationDetail;

    @ApiModelProperty(notes = "所在地", example = "所在地")
    private String locationAddr;

    @ApiModelProperty(notes = "所在地 密文", example = "所在地")
    private String dsLocationAddr;

    @ApiModelProperty(notes = "所在地-省")
    private String actualBusinessProvince;

    @ApiModelProperty(notes = "实际经营地址-市")
    private String actualBusinessCity;

    @ApiModelProperty(notes = "实际经营地址-区")
    private String actualBusinessCounty;

    @ApiModelProperty(notes = "实际经营地址-镇")
    private String actualBusinessTown;

    @ApiModelProperty(notes = "实际经营地址-详细地址")
    private String actualBusinessDetail;

    @ApiModelProperty(notes = "实际经营地址-详细地址 密文")
    private String dsActualBusinessDetail;

    @ApiModelProperty(notes = "实际经营地址-省市区镇详细地址")
    private String actualBusinessAddress;

    @ApiModelProperty(notes = "实际经营地址-省市区镇详细地址 密文")
    private String dsActualBusinessAddress;

    @ApiModelProperty(notes = "法人姓名", example = "范宝华")
    private String artificialPersonName;

    @ApiModelProperty(notes = "法人手机号码", example = "***********")
    private String artificialPersonMobile;

    @ApiModelProperty(notes = "法人手机号码 密文", example = "***********")
    private String dsArtificialPersonMobile;

    @ApiModelProperty(notes = "是否有效状态 默认为空 有效为1，无效为0,", example = "1")
    private String isStatus;

    @ApiModelProperty(notes = "会员/商家类型 1：会员，2：商家", example = "1")
    private Integer buyerSellerType;

    @ApiModelProperty(notes = "密码", example = "561234")
    private String passWord;

    @ApiModelProperty(notes = "账号", example = "12")
    private String loginId;

    @ApiModelProperty(notes = "当前归属id", example = "13")
    private String curBelongSellerId;

    @ApiModelProperty(notes = "当前归属客户经理", example = "32")
    private String curBelongManagerId;

    @ApiModelProperty(notes = "支付账号", example = "17000005260201900000")
    private String accountNo;

    @ApiModelProperty(notes = "会员类型，1：非会员，3：担保会员 ，2：正式会员", example = "2")
    private String memberType;

    @ApiModelProperty(notes = "发展行业 1：酒水，2：3C数码，3：交通出行", example = "2")
    private String industryCategory;

    @ApiModelProperty(notes = "法人身份证")
    private String artificialPersonIdcard;

    @ApiModelProperty(notes = "法人身份证 密文")
    private String dsArtificialPersonIdcard;

    @ApiModelProperty(notes = "会员营业执照电子版图片地址")
    private String buyerBusinessLicensePicSrc;

    @ApiModelProperty(notes = "商家标签，0：普通商家，1：超威商家")
    private String sellerLabel;

    @ApiModelProperty(notes = "经营人姓名")
    private String businessPersonName;

    @ApiModelProperty(notes = "经营人手机号码")
    private String businessPersonMobile;

    @ApiModelProperty(notes = "经营人手机号码 密文")
    private String dsBusinessPersonMobile;

    @ApiModelProperty(notes = "黑名单，0：不显示，1：显示")
    private Integer show;
}