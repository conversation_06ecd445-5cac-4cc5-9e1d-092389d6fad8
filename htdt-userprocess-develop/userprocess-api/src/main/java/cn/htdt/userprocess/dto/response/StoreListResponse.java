package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <p>
 * 商家组织信息
 * </p>
 *
 * 修改:新增字段 temporaryProductConfigStatus
 * 修改原因 新增临时商品配置功能
 * <AUTHOR>
 * @since 2025-03
 */
@Data
@ApiModel(description = "商家信息响应实体类")
public class StoreListResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 临时商品配置状态 0 关闭  1 开启  默认 1开启
     */
    @ApiModelProperty(notes = "临时商品配置状态 0关闭,1开启")
    private String temporaryProductConfigStatus;

    /**
     * 商家名称
     */
    @ApiModelProperty(notes = "店铺名称")
    private String storeName;


    @ApiModelProperty(notes = "店铺编号")
    private String storeNo;
    /**
     * 省id
     */
    @ApiModelProperty(notes = "省id")
    private String provinceCode;

    /**
     * 省名
     */
    @ApiModelProperty(notes = "省名")
    private String provinceName;

    /**
     * 市id
     */
    @ApiModelProperty(notes = "市id")
    private String cityCode;

    /**
     * 市名
     */
    @ApiModelProperty(notes = "市名")
    private String cityName;

    /**
     * 区id
     */
    @ApiModelProperty(notes = "区id")
    private String regionCode;

    /**
     * 区名
     */
    @ApiModelProperty(notes = "区名")
    private String regionName;

    /**
     * 详细地址
     */
    @ApiModelProperty(notes = "详细地址")
    private String detailAddress;

    @ApiModelProperty(notes = "详细地址-加密")
    private String dsDetailAddress;

    /**
     * 详细地址
     */
    @ApiModelProperty(notes = "详细地址")
    private String contactDetailAddress;

    /**
     * 店铺简介
     */
    @ApiModelProperty(notes = "店铺简介")
    private String shortDesc;


    /**
     * 街道code
     */
    @ApiModelProperty(notes = "街道code")
    private String streetCode;

    /**
     * 街道名称
     */
    @ApiModelProperty(notes = "街道名称")
    private String streetName;

    /**
     * 店铺状态 0停用，1启用
     */
    @ApiModelProperty(notes = "店铺状态 0停用，1启用")
    private Integer storeStatus;

    @ApiModelProperty(value = "是否会员共享：1否 2是")
    private Integer memberSharing;

    @ApiModelProperty(notes = "所属商家id")
    private String merchantNo;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(notes = "自定义店铺编码")
    private String customStoreNo;

    @ApiModelProperty(notes = "联系人手机号")
    private String contactPhone;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
