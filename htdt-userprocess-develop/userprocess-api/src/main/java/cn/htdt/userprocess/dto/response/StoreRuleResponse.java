package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigInteger;

/**
 *
 * 店铺规则响应DTO
 *
 * <AUTHOR>
 * @since 2023-06-05
 */
@Data
@ApiModel(description = "店铺规则响应实体类")
public class StoreRuleResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(notes = "主键")
    private BigInteger id;

    /**
     * 规则编号, 唯一标识
     */
    @ApiModelProperty(notes="规则编号")
    private String ruleNo;

    /**
     * 规则分类code, 参考枚举: StoreRuleCategoryEnum
     */
    @ApiModelProperty(notes="规则分类")
    private String ruleCategoryCode;

    /**
     * 规则名称, 参考枚举: StoreRuleOptionsNameEnum
     */
    @ApiModelProperty(notes="规则名称")
    private String ruleName;

    /**
     * 规则的key, 参考枚举: StoreRuleOptionsNameEnum
     */
    @ApiModelProperty(notes="规则的键")
    private String ruleKey;

    /**
     * 规则的值, 参考枚举: StoreRuleOptionsDetailsEnum
     */
    @ApiModelProperty(notes="规则的值")
    private String ruleValue;

    @ApiModelProperty(notes = "商家编号")
    private String merchantNo;

    @ApiModelProperty(notes = "商家名称")
    private String merchantName;

    @ApiModelProperty(notes = "门店编号，对应orgId")
    private String storeNo;

    @ApiModelProperty(notes = "店铺名称")
    private String storeName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}