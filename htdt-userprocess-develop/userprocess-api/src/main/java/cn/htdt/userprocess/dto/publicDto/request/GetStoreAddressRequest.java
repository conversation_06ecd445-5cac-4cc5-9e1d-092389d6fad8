package cn.htdt.userprocess.dto.publicDto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * @ClassName GetStoreAddressRequest
 * @Description 店铺
 * <AUTHOR>
 * @Date 2020/11/20 14:54
 */
@Data
public class GetStoreAddressRequest implements Serializable {

    private static final long serialVersionUID = 6642761974840442812L;


    @ApiModelProperty(notes = "店铺编号",required = true)
    private String storeNo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
