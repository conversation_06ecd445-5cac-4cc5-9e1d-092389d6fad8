package cn.htdt.userprocess.dto.publicDto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;


/**
 * <p>
 * 代理人详情请求实体类
 * </p>
 *
 */
@Data
@ApiModel(description = "代理人详情请求实体类")
public class AgentPayRequest implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(notes = "代理人编号")
    private String agentNo;

    @ApiModelProperty(notes = "粉丝编号")
    private String fanNo;

    /**
     * 支付用户id
     */
    @ApiModelProperty(notes = "支付用户id")
    private String userId;

    /**
     * 用户账户
     */
    @ApiModelProperty(notes = "用户账号")
    private String accountNo;

    /**
     * 是否开户 1否 2是
     */
    @ApiModelProperty(notes = "是否开户 1否 2是")
    private Integer openFlag;

    /**
     * 是否实名 1否 2是
     */
    @ApiModelProperty(notes = "是否实名 1否 2是")
    private Integer realFlag;

    /**
     * 状态 1粉丝 2代理人
     */
    private Integer type;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
