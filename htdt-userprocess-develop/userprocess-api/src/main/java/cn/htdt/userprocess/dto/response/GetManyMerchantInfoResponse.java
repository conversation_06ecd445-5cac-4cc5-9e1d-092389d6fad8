package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <p>
 * 多店铺商家
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-31
 */
@Data
@ApiModel(description = "多店铺商家响应实体类")
public class GetManyMerchantInfoResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(notes = "主键")
    private Long id;

    @ApiModelProperty(notes = "商家编号")
    private String merchantNo;

    /**
     * 商家名称
     */
    @ApiModelProperty(notes = "商家名称")
    private String merchantName;

    /**
     * 允许创建最大数目
     */
    @ApiModelProperty(notes = "允许创建最大数目")
    private Integer maxNum;

    /**
     * 已经创建数目
     */
    @ApiModelProperty(notes = "已经创建数目")
    private Integer storeNum;

    /**
     * 剩余创建数目
     */
    @ApiModelProperty(notes = "剩余创建数目")
    private Integer surplusNum;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
