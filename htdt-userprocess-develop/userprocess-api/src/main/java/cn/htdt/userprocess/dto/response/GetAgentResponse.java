package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022-06-20
 * @description 代理人请求实体类
 **/
@Data
@ApiModel(description = "代理人请求实体类")
public class GetAgentResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(notes = "代理人数量")
    private Integer totalAgentCount;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
