package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 商家的店铺信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-4
 */
@Data
@ApiModel(description = "店铺响应实体类")
public class StoreResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    @ApiModelProperty(notes = "店铺编号")
    private String storeNo;

    @ApiModelProperty(notes = "店铺名称")
    private String storeName;

    @ApiModelProperty(notes = "下属店铺")
    private List<StoreResponse> children;

    @ApiModelProperty(notes = "选中店铺")
    private List<String> checked;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
