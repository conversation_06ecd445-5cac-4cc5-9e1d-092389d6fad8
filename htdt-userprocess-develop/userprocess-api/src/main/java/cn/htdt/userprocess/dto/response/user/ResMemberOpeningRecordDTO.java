package cn.htdt.userprocess.dto.response.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


@Data
@ApiModel(description = "会员开通返回实体类")
public class ResMemberOpeningRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 会员开通记录编号
     */
    private String memberRecordNo;

    /**
     * 粉丝昵称
     */
    private String name;

    /**
     * 微信昵称
     */
    private String nickName;

    /**
     * 头像地址
     */
    private String headImg;

    private String phone;

    private String dsPhone;

    /**
     * 开通方式：1001-免费;1002-付费
     */
    private String openingMethod;

    /**
     * 开通时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime openingTime;

    /**
     * 有效开始日期
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime effectiveTime;

    /**
     * 有效结束日期
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime invalidTime;


    /**
     * 付费金额
     */
    private BigDecimal paymentAmount;

    /**
     *有效期限:1001-30天;1002-60天;1003-90天;1004-一年;1005-终身
     */
    private String validityPeriod;

    /**
     * 备注名
     */
    private String storeFanName;

    /**
     * 店铺编号
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 粉丝编号
     */
    private String fanNo;

    /**
     * 店铺数量
     */
    private Integer storeNum;


    /**
     * 会员类型：1-店铺；2-商家
     */
    private Integer memberType;

    /**
     * 等级编号
     */
    private String memberLevelNo;

    /**
     * 等级名称
     */
    private String memberLevelName;

    /**
     * 会员等级
     */
    private Integer memberLevelNum;
}
