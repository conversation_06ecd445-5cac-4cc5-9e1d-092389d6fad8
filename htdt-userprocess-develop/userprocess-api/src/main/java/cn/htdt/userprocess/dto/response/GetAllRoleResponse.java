package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

@Data
@ApiModel(description = "角色响应实体类")
public class GetAllRoleResponse implements Serializable {

    @ApiModelProperty(notes = "角色编号")
    private String roleNo;

    @ApiModelProperty(notes = "角色名称")
    private String roleName;

    @ApiModelProperty(notes = "角色状态  1启用 2停用")
    private Integer roleStatus;

    @ApiModelProperty(notes = "角色描述")
    private String roleDesc;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
