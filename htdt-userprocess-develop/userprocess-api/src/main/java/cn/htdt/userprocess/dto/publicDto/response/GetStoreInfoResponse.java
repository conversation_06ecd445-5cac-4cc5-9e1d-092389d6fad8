package cn.htdt.userprocess.dto.publicDto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @program: htdt-userprocess
 * @ClassName: StoreInfoResponse
 * @description: 店铺信息响应实体
 * @author: Jacky
 * @create: 2020-10-09 16:09
 **/
@Data
@ApiModel(description = "店铺响应实体类")
public class GetStoreInfoResponse implements Serializable {

    private static final long serialVersionUID = -8779977275595383659L;


    @ApiModelProperty(notes = "店铺编号")
    private String storeNo;

    @ApiModelProperty(notes = "店铺名称")
    private String storeName;

    /**
     * 所属商家id
     */
    @ApiModelProperty(notes = "所属商家id")
    private String merchantNo;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    /**
     * 审核状态 0待审核,1通过,2不通过
     */
    @ApiModelProperty(notes = "审核状态 0待审核,1通过,2不通过")
    private Integer auditStatus;

    /**
     * 门店类型
     */
    @ApiModelProperty(notes = "门店类型")
    private String storeType;

    /**
     * 省id
     */
    @ApiModelProperty(notes = "省id")
    private String provinceCode;

    /**
     * 省名
     */
    @ApiModelProperty(notes = "省名")
    private String provinceName;

    /**
     * 市id
     */
    @ApiModelProperty(notes = "市id")
    private String cityCode;

    /**
     * 市名
     */
    @ApiModelProperty(notes = "市名")
    private String cityName;

    /**
     * 区id
     */
    @ApiModelProperty(notes = "区id")
    private String regionCode;

    /**
     * 区名
     */
    @ApiModelProperty(notes = "区名")
    private String regionName;

    /**
     * 详细地址
     */
    @ApiModelProperty(notes = "详细地址")
    private String detailAddress;

    /**
     * 详细地址
     */
    @ApiModelProperty(notes = "详细地址加密")
    private String dsDetailAddress;

    /**
     * 门店描述
     */
    @ApiModelProperty(notes = "门店描述")
    private String description;

    /**
     * 店铺的经度
     */
    @ApiModelProperty(notes = "店铺的经度")
    private BigDecimal longitude;

    /**
     * 店铺的纬度
     */
    @ApiModelProperty(notes = "店铺的纬度")
    private BigDecimal latitude;

    /**
     * 详细地址
     */
    @ApiModelProperty(notes = "详细地址")
    private String contactDetailAddress;

    /**
     * 店铺简介
     */
    @ApiModelProperty(notes = "店铺简介")
    private String shortDesc;

    /**
     * ；联系人email
     */
    @ApiModelProperty(notes = "联系人email")
    private String contactEmail;

    /**
     * 店铺状态 0停用，1启用
     */
    @ApiModelProperty(notes = "店铺状态 0停用，1启用")
    private Integer storeStatus;

    /**
     * 公司ID
     */
    @ApiModelProperty(notes = "公司ID")
    private String companyNo;

    /**
     * 公司名称
     */
    @ApiModelProperty(notes = "公司名称")
    private String companyName;


    @ApiModelProperty(notes = "分布名称")
    private String branchNo;


    @ApiModelProperty(notes = "分布编号")
    private String branchName;

    /**
     * 创建人ID
     */
    @ApiModelProperty(notes = "创建人ID")
    private String createNo;

    /**
     * 最后一次修改人ID
     */
    @ApiModelProperty(notes = "最后一次修改人ID")
    private String modifyNo;

    /**
     * 街道code
     */
    @ApiModelProperty(notes = "街道code")
    private String streetCode;

    /**
     * 街道名称
     */
    @ApiModelProperty(notes = "街道名称")
    private String streetName;

    /**
     * 营业开始时间
     */
    @ApiModelProperty(notes = "营业开始时间")
    private LocalDateTime businessStart;

    /**
     * 营业结束时间
     */
    @ApiModelProperty(notes = "营业结束时间")
    private LocalDateTime businessEnd;

    /**
     * 店内照片url(多)
     */
    @ApiModelProperty(notes = "店内照片url(多)")
    private String inStorePhotos;

    /**
     * 门头照片url
     */
    @ApiModelProperty(notes = "门头照片url")
    private String storePhoto;

    /**
     * 微信
     */
    @ApiModelProperty(notes = "微信")
    private String wx;

    /**
     * 微信二维码
     */
    @ApiModelProperty(notes = "微信二维码")
    private String wxCode;

    /**
     * 店铺微信二维码
     */
    @ApiModelProperty(notes = "店铺微信二维码")
    private String merchantWxCode;

    /**
     * 独立小程序二维码
     */
    @ApiModelProperty(notes = "独立小程序二维码")
    private String independenceAppletUrl;

    /**
     * 是否有仓库 0否 1是
     */
    @ApiModelProperty(notes = "是否有仓库 0否 1是")
    private Integer warehouseFlag;

    @ApiModelProperty(notes = "联系人姓名")
    private String contactName;

    @ApiModelProperty(notes = "联系人手机号")
    private String contactPhone;

    @ApiModelProperty(notes = "联系人手机号-加密")
    private String dsContactPhone;


    @ApiModelProperty(notes = "店长编号")
    private String adminNo;

    @ApiModelProperty(notes = "店长姓名")
    private String adminName;

    @ApiModelProperty(notes = "店长手机号")
    private String adminPhone;

    @ApiModelProperty(notes = "店长手机号")
    private String dsAdminPhone;

    @ApiModelProperty(value = "当前收款账户的会员编码")
    private String currentMerberCode;

    /**
     * 当前收款账户的公司名称
     */
    @ApiModelProperty(value = "当前收款账户的公司名称")
    private String currentCompanyName;

    /**
     * 当前收款账户的支付账户
     */
    @ApiModelProperty(value = "当前收款账户的支付账户")
    private String currentPayAccount;

    @ApiModelProperty(value = "商家账号")
    private String merberCode;

    /**
     * 支付账户
     */
    @ApiModelProperty(value = "支付账户")
    private String payAccount;

    /**
     * 是否是实体店铺 1-虚拟店铺 2-实体店铺
     */
    @ApiModelProperty(value = "是否是实体店铺 1-虚拟店铺 2-实体店铺")
    private Integer isPhysicalStore;

    /**
     * 法人姓名
     */
    @ApiModelProperty(value = "法人姓名")
    private String legalName;

    /**
     * 经营人姓名
     */
    @ApiModelProperty(value = "经营人姓名")
    private String operatorName;

    /**
     * 营业执照
     */
    @ApiModelProperty(value = "营业执照")
    private String businessLicense;

    /**
     * 所属行业
     */
    @ApiModelProperty(notes = "所属行业")
    private String industry;

    /**
     * 所属行业
     */
    @ApiModelProperty(notes = "所属行业名称")
    private String industryName;

    /**
     * 法人手机号
     */
    @ApiModelProperty(value = "法人手机号")
    private String legalPhone;

    /**
     * 法人手机号-加密
     */
    @ApiModelProperty(value = "法人手机号-加密")
    private String dsLegalPhone;

    /**
     * 经营人手机号
     */
    @ApiModelProperty(value = "经营人手机号")
    private String operatorPhone;

    /**
     * 经营人手机号-加密
     */
    @ApiModelProperty(value = "经营人手机号-加密")
    private String dsOperatorPhone;

    /**
     * 法人身份证号
     */
    @ApiModelProperty(value = "法人身份证号")
    private String legalCard;

    /**
     * 法人身份证号-加密
     */
    @ApiModelProperty(value = "法人身份证号-加密")
    private String dsLegalCard;

    /**
     * 经营地址
     */
    @ApiModelProperty(value = "经营地址")
    private String operateAddress;

    /**
     * 经营地址-加密
     */
    @ApiModelProperty(value = "经营地址-加密")
    private String dsOperateAddress;

    /**
     * 注册地址
     */
    @ApiModelProperty(value = "注册地址")
    private String regeisterAddress;

    /**
     * 注册地址-加密
     */
    @ApiModelProperty(value = "注册地址-加密")
    private String dsRegeisterAddress;

    @ApiModelProperty(value = "是否审核报损单，1=人工审核；2=自动审核通过")
    private Integer verifyLossReport;

    /**
     * 是否会员共享店铺：1否 2是
     */
    private Integer memberSharing;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}