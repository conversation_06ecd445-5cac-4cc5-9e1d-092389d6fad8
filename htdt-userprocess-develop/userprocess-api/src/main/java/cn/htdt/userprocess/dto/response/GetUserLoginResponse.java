package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022-06-20
 * @description 店铺登录请求实体类
 **/
@Data
@ApiModel(description = "店铺登录请求实体类")
public class GetUserLoginResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(notes = "登录数量")
    private Integer totalLoginCount;

    @ApiModelProperty(notes = "登录IP")
    private String loginIp;

    @ApiModelProperty(notes = "登录时间")
    private LocalDateTime loginTime;

    @ApiModelProperty(notes = "商家编号")
    private String merchantNo;

    @ApiModelProperty(notes = "店铺编号")
    private String storeNo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
