package cn.htdt.userprocess.dto.publicDto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <p>
 * 商家组织信息
 * </p>
 *
 */
@Data
@ApiModel(description = "商家信息响应实体类")
public class GetMerchantResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(notes = "主键")
    private Long id;

    @ApiModelProperty(notes = "商家编号")
    private String merchantNo;

    /**
     * 商家名称
     */
    @ApiModelProperty(notes = "商家名称")
    private String merchantName;

    /**
     * 商家状态   -1：新引进  0：停用   1：正常   2：清退中
     */
    @ApiModelProperty(notes = "商家状态   -1：新引进  0：停用   1：正常   2：清退中")
    private String businessStatus;

    @ApiModelProperty(value = "试用账号 1-(默认)正常账号；2-试用账号")
    private Integer trialAccount;

    /**
     * 登录账户
     */
    @ApiModelProperty(notes = "登录账户")
    private String loginAccount;

    /**
     * 法人姓名
     */
    @ApiModelProperty(notes = "法人姓名")
    private String legalName;

    /**
     * 法人手机号
     */
    @ApiModelProperty(notes = "法人手机号")
    private String legalPhone;

    /**
     * 法人手机号
     */
    @ApiModelProperty(notes = "法人手机号(加密)")
    private String dsLegalPhone;

    /**
     * 经营人姓名
     */
    @ApiModelProperty(notes = "经营人姓名")
    private String operatorName;

    /**
     * 经营人手机号
     */
    @ApiModelProperty(notes = "经营人手机号")
    private String operatorPhone;

    /**
     * 经营人手机号
     */
    @ApiModelProperty(notes = "经营人手机号(加密)")
    private String dsOperatorPhone;

    /**
     * 公司名称
     */
    @ApiModelProperty(notes = "公司名称")
    private String companyName;

    /**
     * 企业注册地址
     */
    @ApiModelProperty(notes = "企业注册地址")
    private String regeisterAddress;

    /**
     * 实际经营地址
     */
    @ApiModelProperty(notes = "实际经营地址")
    private String operateAddress;

    private String dsOperateAddress;

    /**
     * 法人身份证号
     */
    @ApiModelProperty(notes = "法人身份证号")
    private String legalCard;

    /**
     * 所属行业
     */
    @ApiModelProperty(notes = "所属行业")
    private String industry;


    @ApiModelProperty(notes = "所属行业，用于区分行业版本应用，枚举：IndustryEnum，默认为BZ标准版")
    private String industryExpand;

    /**
     * 会员编号
     */
    @ApiModelProperty(notes = "会员编号")
    private String memberNo;

    /**
     * 会员编码
     */
    @ApiModelProperty(notes = "会员编码")
    private String merberCode;

    /**
     * erp编码
     */
    @ApiModelProperty(notes = "erp编码")
    private String erpCode;

    /**
     * 所属公司编号
     */
    @ApiModelProperty(notes = "所属公司编号")
    private String companyNo;

    /**
     * 经理人编号
     */
    @ApiModelProperty(notes = "经理人编号")
    private String managerNo;

    /**
     * 支付账户
     */
    @ApiModelProperty(notes = "支付账户")
    private String payAccount;

    @ApiModelProperty(notes = "原始支付账户")
    private String originalPayAccount;

    /**
     * 会员标签
     */
    @ApiModelProperty(notes = "会员标签")
    private String merberLaber;


    @ApiModelProperty(notes = "是否展示")
    private String showFlag;

    /**
     * 营业执照
     */
    @ApiModelProperty(notes = "营业执照")
    private String businessLicense;

    @ApiModelProperty(notes = "来源")
    private String source;


    /**
     * 归属分布名称
     */
    @ApiModelProperty(notes = "归属分布名称")
    private String branchName;


    @ApiModelProperty(notes = "归属分布编号")
    private String branchNo;

    @ApiModelProperty(notes = "所在地-省")
    private String locationProvince;

    @ApiModelProperty(notes = "所在地-市")
    private String locationCity;

    @ApiModelProperty(notes = "所在地-区")
    private String locationCounty;

    @ApiModelProperty(notes = "所在地-镇")
    private String locationTown;

    @ApiModelProperty(notes = "实际经营地-省")
    private String actualBusinessProvince;

    @ApiModelProperty(notes = "实际经营地-市")
    private String actualBusinessCity;

    @ApiModelProperty(notes = "实际经营地-区")
    private String actualBusinessCounty;

    @ApiModelProperty(notes = "实际经营地-镇")
    private String actualBusinessTown;

    /**
     * 是否单店 : 0：否 1：是
     */
    private Integer singleFlag;

    /**
     * 1:否(未绑定) 2:是(已绑定)
     **/
    private Integer isBind;
    /**
     * 独立小程序appId
     **/
    private String appId;

    /**
     * 独立小程序名称
     **/
    private String appName;

    /**
     * 独立小程序状态
     **/
    private Integer appStatus;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
