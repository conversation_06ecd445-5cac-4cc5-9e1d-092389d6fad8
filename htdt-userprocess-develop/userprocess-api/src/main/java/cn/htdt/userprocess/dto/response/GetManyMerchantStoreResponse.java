package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 商家的店铺信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-4
 */
@Data
@ApiModel(description = "店铺响应实体类")
public class GetManyMerchantStoreResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(notes = "主键")
    private Long id;

    @ApiModelProperty(notes = "店铺编号")
    private String storeNo;

    @ApiModelProperty(notes = "店铺名称")
    private String storeName;

    /**
     * 组织id
     */
    @ApiModelProperty(notes = "组织id")
    private String orgNo;

    /**
     * 组织编码
     */
    @ApiModelProperty(notes = "组织编码")
    private String orgCode;

    /**
     * 所属商家id
     */
    @ApiModelProperty(notes = "所属商家id")
    private String merchantNo;

    /**
     * 审核状态 0待审核,1通过,2不通过
     */
    @ApiModelProperty(notes = "审核状态 0待审核,1通过,2不通过")
    private Integer auditStatus;

    /**
     * 门店类型
     */
    @ApiModelProperty(notes = "门店类型")
    private String storeType;

    /**
     * 省id
     */
    @ApiModelProperty(notes = "省id")
    private Long provinceCode;

    /**
     * 省名
     */
    @ApiModelProperty(notes = "省名")
    private String provinceName;

    /**
     * 市id
     */
    @ApiModelProperty(notes = "市id")
    private Long cityCode;

    /**
     * 市名
     */
    @ApiModelProperty(notes = "市名")
    private String cityName;

    /**
     * 区id
     */
    @ApiModelProperty(notes = "区id")
    private Long regionCode;

    /**
     * 区名
     */
    @ApiModelProperty(notes = "区名")
    private String regionName;

    /**
     * 详细地址
     */
    @ApiModelProperty(notes = "详细地址")
    private String detailAddress;

    /**
     * 门店描述
     */
    @ApiModelProperty(notes = "门店描述")
    private String description;

    /**
     * 店铺的经度
     */
    @ApiModelProperty(notes = "店铺的经度")
    private BigDecimal longitude;

    /**
     * 店铺的纬度
     */
    @ApiModelProperty(notes = "店铺的纬度")
    private BigDecimal latitude;

    /**
     * 详细地址
     */
    @ApiModelProperty(notes = "详细地址")
    private String contactDetailAddress;

    /**
     * 店铺简介
     */
    @ApiModelProperty(notes = "店铺简介")
    private String shortDesc;

    /**
     * ；联系人email
     */
    @ApiModelProperty(notes = "联系人email")
    private String contactEmail;

    /**
     * 店铺状态 0停用，1启用
     */
    @ApiModelProperty(notes = "店铺状态 0停用，1启用")
    private Integer storeStatus;

    /**
     * 公司ID
     */
    @ApiModelProperty(notes = "公司ID")
    private String companyNo;

    /**
     * 创建人ID
     */
    @ApiModelProperty(notes = "创建人ID")
    private String createNo;

    /**
     * 最后一次修改人ID
     */
    @ApiModelProperty(notes = "最后一次修改人ID")
    private String modifyNo;

    /**
     * 街道code
     */
    @ApiModelProperty(notes = "街道code")
    private Long streetCode;

    /**
     * 街道名称
     */
    @ApiModelProperty(notes = "街道名称")
    private String streetName;

    /**
     * 营业开始时间
     */
    @ApiModelProperty(notes = "营业开始时间")
    private LocalDateTime businessStart;

    /**
     * 营业结束时间
     */
    @ApiModelProperty(notes = "营业结束时间")
    private LocalDateTime businessEnd;

    /**
     * 店内照片url(多)
     */
    @ApiModelProperty(notes = "店内照片url(多)")
    private String inStorePhotos;

    /**
     * 门头照片url
     */
    @ApiModelProperty(notes = "门头照片url")
    private String storePhoto;

    /**
     * 微信
     */
    @ApiModelProperty(notes = "微信")
    private String wx;

    /**
     * 微信二维码
     */
    @ApiModelProperty(notes = "微信二维码")
    private String wxCode;

    /**
     * 店铺微信二维码
     */
    @ApiModelProperty(notes = "店铺微信二维码")
    private String merchantWxCode;

    /**
     * 是否有仓库 0否 1是
     */
    @ApiModelProperty(notes = "是否有仓库 0否 1是")
    private Integer warehouseFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
