package cn.htdt.userprocess.dto.response;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 千橙页面展示配置请求参数
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-26
 */
@Data
public class ResUcDisplayConfigurationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页签图片(千橙掌柜PC)
     */
    private String tabImage;

    /**
     * 菜单栏LOGO(千橙掌柜PC)
     */
    private String menuLogo;


    /**
     * APP频道：进货频道是否展示(千橙掌柜APP)
     **/
    private Integer purchaseExhibition;

    /**
     * APP频道：进货(千橙掌柜APP)
     */
    private String purchase;

    /**
     * APP频道：生意经(千橙掌柜APP)
     */
    private Integer businessSenseExhibition;

    /**
     * 网页端采购商城是否展示(千橙掌柜PC)
     */
    private Integer purchasingMallExhibition;

    /**
     * 网页端采购商城(千橙掌柜PC)
     */
    private String purchasingMall;

    /**
     * 商品默认图(千橙掌柜pc/汇享购/千橙掌柜收银/千橙掌柜APP)
     */
    private String productDefaultChart;

    /**
     * 登录页图片(千橙掌柜PC 配置过域名的商家)
     */
    private String loginImage;

    /**
     * 版权信息(千橙掌柜PC/千橙掌柜收银 配置过域名的商家)
     */
    private String copyrightInformation;

    /**
     * 是否自定义域名
     */
    private Integer domainNameExhibition;

    /**
     * 配置域名
     */
    private String domainName;

    /**
     * 1-运营；2-商家；4-店铺；8-单店
     */
    private Integer loginIdentity;

    /**
     * 商家id
     */
    private String merchantNo;

    /**
     * 店铺id
     */
    private String storeNo;

    /**
     * 数据是否完整 1否  2是
     **/
    private Integer complete;

    /**
     * APP频道：展示千橙采购 1否(展示云场)  2是 （展示千橙采购）
     */
    private Integer showQCPurchase;

}
