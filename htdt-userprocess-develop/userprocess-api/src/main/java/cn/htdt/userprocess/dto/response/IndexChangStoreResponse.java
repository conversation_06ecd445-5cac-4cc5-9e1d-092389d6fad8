package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 店铺组织信息
 * </p>
 *
 */
@Data
@ApiModel(description = "店铺信息响应实体类")
public class IndexChangStoreResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商家名称
     */
    @ApiModelProperty(notes = "店铺名称")
    private String storeName;

    @ApiModelProperty(notes = "店铺编号")
    private String storeNo;

    @ApiModelProperty(notes = "门头照片url")
    private String storePhoto;

    @ApiModelProperty(notes = "店长姓名")
    private String adminName;

    @ApiModelProperty(notes = "店长手机号")
    private String adminPhone;

    @ApiModelProperty(notes = "联系人姓名")
    private String contactName;

    @ApiModelProperty(notes = "联系人手机号")
    private String contactPhone;

    @ApiModelProperty(notes = "省id")
    private String provinceCode;

    @ApiModelProperty(notes = "省名")
    private String provinceName;

    @ApiModelProperty(notes = "市id")
    private String cityCode;

    @ApiModelProperty(notes = "市名")
    private String cityName;

    @ApiModelProperty(notes = "区id")
    private String regionCode;

    @ApiModelProperty(notes = "区名")
    private String regionName;

    @ApiModelProperty(notes = "街道code")
    private String streetCode;

    @ApiModelProperty(notes = "街道名称")
    private String streetName;

    @ApiModelProperty(notes = "详细地址")
    private String detailAddress;

    @ApiModelProperty(notes = "店铺的经度")
    private BigDecimal longitude;

    @ApiModelProperty(notes = "店铺的纬度")
    private BigDecimal latitude;

    @ApiModelProperty(notes = "距离")
    private BigDecimal distance;

    @ApiModelProperty(notes = "店铺简介")
    private String shortDesc;

    @ApiModelProperty(notes = "门店描述")
    private String description;

    @ApiModelProperty(notes = "店铺是否打烊 0否 1是")
    private Integer isShut;

    @ApiModelProperty(notes = "店铺是否有汇赚钱权限 1否 2是")
    private Integer privilegeFlag;

    @ApiModelProperty(notes = "独立小程序店铺是否打烊 0否 1是")
    private Integer isAppletShut;

    @ApiModelProperty(notes = "独立小程序店铺是否有汇赚钱权限 1否 2是")
    private Integer isAppletAgentReward;

    @ApiModelProperty(notes = "角色实体类型 1-平台 2-商家 4-店铺 8-单店")
    private Integer entityType;

    @ApiModelProperty(notes = "活动列表")
    private List<String> promotionList;

    @ApiModelProperty(notes = "自定义标签列表")
    private List<String> customTagName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
