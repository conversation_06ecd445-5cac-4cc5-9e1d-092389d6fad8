package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <p>
 * 商家组织信息
 * </p>
 *
 */
@Data
@ApiModel(description = "商家信息响应实体类")
public class ManyMerchantSelectResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商家名称
     */
    @ApiModelProperty(notes = "商家名称")
    private String merchantName;

    @ApiModelProperty(notes = "商家编号")
    private String merchantNo;

    @ApiModelProperty(notes = "会员编码")
    private String merberCode;

    @ApiModelProperty(notes = "是否单店 : 0：否 1：是")
    private Integer singleFlag;

    @ApiModelProperty(notes = "1:否(未绑定) 2:是(已绑定)")
    private Integer isBind;

    @ApiModelProperty(value = "试用账号 1-(默认)正常账号；2-试用账号")
    private Integer trialAccount;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
