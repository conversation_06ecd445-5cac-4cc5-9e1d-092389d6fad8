package cn.htdt.userprocess.dto.response.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDateTime;

@Data
public class ResMemberOpeningOrderDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private BigInteger id;

    /**
     * 粉丝编号
     */
    private String fanNo;

    /**
     * 会员配置no
     */
    private String memberCongfigNo;

    /**
     * 支付类型 1000 现金；1002 扫码支付
     */
    private String payType;

    /**
     * 开通方式：1001-免费;1002-付费
     */
    @ApiModelProperty(value = "开通方式：1001-免费;1002-付费")
    private String openingMethod;
    /**
     * 付费金额
     */
    @ApiModelProperty(value = "付费金额")
    private BigDecimal paymentAmount;

    /**
     * 有效期限:1001-30天;1002-60天;1003-90天;1004-一年;1005-终身
     */
    @ApiModelProperty(notes = "有效期限:1001-30天;1002-60天;1003-90天;1004-一年;1005-终身")
    private String validityPeriod;
    /**
     * 支付金额
     */
    private BigDecimal payAmount;
    /**
     * 交易流水号
     */
    private String outTradeNo;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 是否可用:默认2，1：不可用2:可用
     */
    private Integer disableFlag;

    private String createNo;

    private String createName;

    private LocalDateTime createTime;

    private String modifyNo;

    private String modifyName;

    private LocalDateTime modifyTime;
}
