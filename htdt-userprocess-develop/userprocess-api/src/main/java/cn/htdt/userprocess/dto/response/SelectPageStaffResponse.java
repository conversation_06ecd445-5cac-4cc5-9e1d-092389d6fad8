package cn.htdt.userprocess.dto.response;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(description = "员工列表响应实体类")
public class SelectPageStaffResponse implements Serializable {


    private static final long serialVersionUID = -5435267675866499074L;



    @ApiModelProperty(notes = "管辖区域")
    private String ucRegionStrs;

    @ApiModelProperty(notes = "主键")
    private Long id;

    @ApiModelProperty(notes = "用户编号")
    private String userNo;

    @ApiModelProperty(notes = "手机")
    private String mobile;
    /**
     * 用户名
     */
    @ApiModelProperty(notes = "用户名")
    private String username;

    @ApiModelProperty(notes = "头像")
    private String headImg;

    /**
     * 工号
     */
    @ApiModelProperty(notes = "工号")
    private String employeNum;

    /**
     * 是否管理员
     */
    @ApiModelProperty(notes = "是否管理员1否2是")
    private Integer isAdministrator;


    /**
     * 是否管理员 1否 2是
     */
    @ApiModelProperty(notes = "是否店长 1否 2是")
    private Integer isStoreAdmin;

    @ApiModelProperty(notes = "备注")
    private String remarks;

    @ApiModelProperty(notes = "角色名称列表")
    private List<String> roleNameList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
