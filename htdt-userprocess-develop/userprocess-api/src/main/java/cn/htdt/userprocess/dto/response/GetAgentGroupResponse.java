package cn.htdt.userprocess.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 分组信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-24
 */
@Data
@ApiModel(description = "分组关联代理人列表")
public class GetAgentGroupResponse implements Serializable {


    @ApiModelProperty(notes = "代理人编号")
    private String agentNo;

    @ApiModelProperty(notes = "粉丝编号")
    private String fanNo;

    /**
     * 姓名
     */
    @ApiModelProperty(notes = "姓名")
    private String name;

    /**
     * 手机号
     */
    @ApiModelProperty(notes = "手机号")
    private String phone;


    @ApiModelProperty(notes = "关联粉丝")
    private String fanGroup;

    @ApiModelProperty(notes = "是否关联当前分组（0 否 1是）")
    private Integer status;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(notes = "创建时间")
    private LocalDateTime createTime;

    /**
     * 拉新人数
     */
    @ApiModelProperty(notes = "拉新人数")
    private Integer fanNum;

    /**
     * 锁粉人数
     */
    @ApiModelProperty(notes = "锁粉人数")
    private Integer lockNum;

    /**
     * 加入时间
     */
    @ApiModelProperty(notes = "加入时间")
    private LocalDateTime joinTime;

    /**
     * 退出时间
     */
    private LocalDateTime outTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
