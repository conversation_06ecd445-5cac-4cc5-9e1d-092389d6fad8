package cn.htdt.userprocess.dto.response.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

@Data
@ApiModel(description = "用户信息请求实体")
public class ResAnalysisTokenDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(notes = "用户账号")
    private String userNo;

    @ApiModelProperty(notes = "用户名称")
    private String userName;

    @ApiModelProperty(notes = "商家编号")
    private String merchantNo;

    @ApiModelProperty(notes = "商家名称")
    private String merchantName;

    @ApiModelProperty(notes = "门店编号")
    private String storeNo;

    @ApiModelProperty(notes = "门店名称")
    private String storeName;

    @ApiModelProperty(notes = "用户身份")
    private Integer loginIdentity;

    @ApiModelProperty(notes = "登录渠道")
    private Integer loginChannel;

    @ApiModelProperty(notes = "token")
    private String token;

    @ApiModelProperty(notes = "试用账号")
    private Integer trialAccount;

    @ApiModelProperty(value = "是否是实体店铺")
    private Integer isPhysicalStore;

    @ApiModelProperty(notes = "是否含有独立小程序")
    private Integer isHaveApplet;

    @ApiModelProperty(notes = "是否含有小程序权限 1-否 2-是")
    private Integer isMiniPrograme;

    @ApiModelProperty(notes = "归属公司 1000-汇通数科 1001-汇通达")
    private Integer belongCompany;

    @ApiModelProperty(value = "是否商家主账号 1-是 0-不是")
    private Integer isMerchantUser;

    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
