package cn.htdt.userprocess.dto.response.store;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * @ClassName ResFanFollowStoreDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/4/28 下午5:20
 */
@Data
@ApiModel(description = "粉丝关注店铺信息")
public class ResFollowStoreDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(notes = "店铺编号")
    private String storeNo;

    @ApiModelProperty(notes = "店铺名称")
    private String storeName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
