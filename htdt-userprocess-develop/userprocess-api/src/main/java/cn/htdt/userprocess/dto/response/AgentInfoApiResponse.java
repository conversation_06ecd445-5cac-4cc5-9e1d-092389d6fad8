package cn.htdt.userprocess.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * @ClassName AgentInfoResponse
 * @Description 代理人详情返回实体
 * <AUTHOR>
 * @Date 2021/1/15 16:11
 */
@Data
@ApiModel(description = "代理人详情返回实体")
public class AgentInfoApiResponse implements Serializable {


    /**
     * 代理人编号
     */
    @ApiModelProperty(notes = "代理人编号")
    private String agentNo;


    /**
     * 普通粉丝
     */
    @ApiModelProperty(notes = "普通")
    private Integer normalNum;

    /**
     * 锁粉人数
     */
    @ApiModelProperty(notes = "锁粉人数")
    private Integer lockNum;

    /**
     * 店铺编号
     */
    @ApiModelProperty(notes = "店铺编号")
    private String storeNo;

    /**
     * 店铺名称
     */
    @ApiModelProperty(notes = "店铺名称")
    private String storeName;

    /**
     * 姓名
     */
    @ApiModelProperty(notes = "姓名")
    private String name;

    /**
     * 电话
     */
    @ApiModelProperty(notes = "电话")
    private String phone;

    /**
     * 性别 0 男 1 女 2保密
     */
    @ApiModelProperty(notes = "性别 0 男 1 女 2保密")
    private Integer sex;

    /**
     * 出生日期
     */
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    @ApiModelProperty(notes = "出生日期")
    private LocalDate birthday;

    /**
     * 头像地址
     */
    @ApiModelProperty(notes = "头像地址")
    private String headImg;

    /**
     * 省id
     */
    @ApiModelProperty(notes = "省id")
    private String provinceCode;

    /**
     * 省名
     */
    @ApiModelProperty(notes = "省名")
    private String provinceName;

    /**
     * 市id
     */
    @ApiModelProperty(notes = "市id")
    private String cityCode;

    /**
     * 市名
     */
    @ApiModelProperty(notes = "市名")
    private String cityName;

    /**
     * 区id
     */
    @ApiModelProperty(notes = "区id")
    private String regionCode;

    /**
     * 区名
     */
    @ApiModelProperty(notes = "区名")
    private String regionName;

    /**
     * 详细地址
     */
    @ApiModelProperty(notes = "详细地址")
    private String detailAddress;


    /**
     * 街道code
     */
    @ApiModelProperty(notes = "街道code")
    private String streetCode;

    /**
     * 街道名称
     */
    @ApiModelProperty(notes = "街道名称")
    private String streetName;

    /**
     * 文化程度
     */
    @ApiModelProperty(notes = "文化程度  0：未受过教育，1：小学，2：初中，3：高中，4：大专，5：本科，硕士及以上")
    private Integer education;

    /**
     * 职业
     */
    @ApiModelProperty(notes = "职业")
    private String occupation;

    /**
     * 网购经验 0无 1有
     */
    @ApiModelProperty(notes = "网购经验 0无 1有")
    private Integer onlineShopping;

    /**
     * 家庭人数
     */
    @ApiModelProperty(notes = "家庭人数 ，1：1人，2：2人，3：3人，4：4人，5：5及以上")
    private Integer homeNum;

    /**
     * 是否完整
     */
    @ApiModelProperty(notes = "是否完整")
    private Boolean isComplete;

    @ApiModelProperty(notes = "会员编码")
    private String merberCode;
    /**
     * 支付用户id
     */
    @ApiModelProperty(notes = "支付用户id")
    private String userId;

    /**
     * 用户账户
     */
    @ApiModelProperty(notes = "用户账户")
    private String accountNo;

    /**
     * 是否开户 1否 2是
     */
    @ApiModelProperty(notes = "是否开户 1否 2是")
    private Integer openFlag;

    /**
     * 是否实名 1否 2是
     */
    @ApiModelProperty(notes = "是否实名 1否 2是")
    private Integer realFlag;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
