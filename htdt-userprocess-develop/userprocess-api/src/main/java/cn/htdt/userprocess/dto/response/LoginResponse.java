package cn.htdt.userprocess.dto.response;


import cn.htdt.common.enums.WhetherEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;


@Data
@ApiModel(description = "登录返回值")
public class LoginResponse implements Serializable {


    private static final long serialVersionUID = 6633728267582022649L;

    @ApiModelProperty(notes = "登录token")
    private String token;

    @ApiModelProperty(notes = "用户账号")
    private String userNo;

    @ApiModelProperty(notes = "手机")
    private String mobile;

    @ApiModelProperty(notes = "手机-密文")
    private String dsMobile;

    @ApiModelProperty(notes = "用户名")
    private String username;

    @ApiModelProperty(notes = "商家编号")
    private String merchantNo;

    @ApiModelProperty(notes = "店铺编号")
    private String storeNo;

    @ApiModelProperty(value = "名称（商家名称/店铺名称）")
    private String name;

    @ApiModelProperty(notes = "头像")
    private String headImg;

    @ApiModelProperty(value = "当前登录用户身份 1-运营；2-商家；4-店铺；8-单店")
    private Integer loginIdentity;

    @ApiModelProperty(value = "用户身份 1-运营；2-商家；4-店铺；8-单店")
    private String type;

    @ApiModelProperty(value = "用户身份列表 1-运营；2-商家；4-店铺；8-单店")
    private List<String> userIdentity;

    @ApiModelProperty(value = "异常登录场景 正常-空字符串；多账户-moreAccount；未绑定-noBind；密码错误3次-pwdError；异地登录-remoteLogin；弱密码-pwdWeak；初始化账户-initAccount")
    private String loginErrorScene;

    @ApiModelProperty(value = "是否多账号 1-是；0-不是")
    private Integer isMoreAccount;

    @ApiModelProperty(value = "是否商家主账号 1-是；0-不是")
    private Integer isMerchantUser;

    @ApiModelProperty(value = "试用账号 1-(默认)正常账号；2-试用账号")
    private Integer trialAccount;

    @ApiModelProperty(value = "是否是实体店铺 1-(默认)虚拟店铺 2-实体店铺")
    private Integer isPhysicalStore;

    @ApiModelProperty(notes = "是否首次登录")
    private Integer isFirstLogin;

    @ApiModelProperty(notes = "是否含有独立小程序 1-未开通独立小程序 2-已开通独立小程序")
    private Integer isHaveApplet;

    @ApiModelProperty(notes = "独立小程序appId")
    private String appId;

    @ApiModelProperty(notes = "独立小程序原始Id")
    private String originalId;

    @ApiModelProperty(notes = "广告图片地址")
    private String adPicUrl;

    @ApiModelProperty("链接URL")
    private String linkAddress;

    @ApiModelProperty("一体机上次登录版本")
    private String scLastLoginVersion;

    @ApiModelProperty(notes = "是否含有小程序权限 1-否 2-是")
    private Integer isMiniPrograme;

    @ApiModelProperty(notes = "归属公司 1000-汇通数科 1001-汇通达")
    private Integer belongCompany;

    @ApiModelProperty(notes = "是否是羊乃客户，用于一体机展示羊乃订单，默认：1-否")
    private Integer isYn = WhetherEnum.NO.getCode();

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


