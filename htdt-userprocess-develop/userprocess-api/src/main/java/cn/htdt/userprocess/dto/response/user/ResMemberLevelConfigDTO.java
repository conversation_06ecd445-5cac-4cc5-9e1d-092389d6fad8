package cn.htdt.userprocess.dto.response.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

@Data
@ApiModel(description = "会员等级响应实体")
public class ResMemberLevelConfigDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(notes = "会员等级配置编号")
    private String memberLevelConfigNo;

    @ApiModelProperty(notes = "会员等级配置是否开启：1开启 2未开启")
    private Integer memberLevelConfigSwitch;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
