package cn.htdt.userprocess.dto.response;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;


@Data
@ApiModel(description = "苹果登录返回值")
public class FanAppleLoginResponse implements Serializable {


    private static final long serialVersionUID = 6633728267582022649L;


    @ApiModelProperty(notes = "苹果标识")
    private String appleid;

    @ApiModelProperty(notes = "登录token")
    private String token;

    @ApiModelProperty(notes = "用户名")
    private String username;

    @ApiModelProperty(notes = "头像")
    private String headImg;

    @ApiModelProperty(notes = "绑定状态 true已绑定 false 未绑定")
    private boolean bindStatus;

    @ApiModelProperty(notes = "店铺是否打烊 0否 1是")
    private Integer isShut;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
