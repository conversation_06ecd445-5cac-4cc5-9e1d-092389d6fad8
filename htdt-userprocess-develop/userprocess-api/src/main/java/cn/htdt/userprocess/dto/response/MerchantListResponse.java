package cn.htdt.userprocess.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <p>
 * 商家组织信息
 * </p>
 *
 */
@Data
@ApiModel(description = "商家信息响应实体类")
public class MerchantListResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商家名称
     */
    @ApiModelProperty(notes = "商家名称")
    private String merchantName;


    @ApiModelProperty(notes = "商家编号")
    private String merchantNo;

    /**
     * 商家状态   -1：新引进  0：停用   1：正常   2：清退中
     */
    @ApiModelProperty(notes = "商家状态   -1：新引进  0：停用   1：正常   2：清退中")
    private String businessStatus;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
