package cn.htdt.app.smartcashier.dto.response.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * 销售类目响应DTO
 *
 * <AUTHOR>
 * @date 2020-09-11
 */
@Data
public class ResScSaleGoodsCategoryDTO implements Serializable {

    private static final long serialVersionUID = -8331006406008370946L;

    @ApiModelProperty(value = "类目编号")
    private String categoryNo;

    @ApiModelProperty(value = "类目名称")
    private String categoryName;

    @ApiModelProperty(value = "类目名称全路径")
    private String fullNamePath;

    @ApiModelProperty(value = "类目id全路径")
    private String fullIdPath;

    @ApiModelProperty(value = "层级(1001:一级 1002：二级 1003：三级 1004：四级)")
    private String categoryLevel;

    @ApiModelProperty(value = "父类目节点ID")
    private String parentNo;

    @ApiModelProperty(value = "数据来源类型(默认1002，1001：MDM同步，1002：平台自建)")
    private String categorySource;

    @ApiModelProperty(value = "图片URL")
    private String pictureUrl;

    @ApiModelProperty(value = "排序值")
    private Integer sortValue;

    @ApiModelProperty(value = "是否可用:默认2，1：不可用，2：可用")
    private Integer disableFlag;

    @ApiModelProperty(value = "子目录")
    private List<ResScSaleGoodsCategoryDTO> childCategory;

    @ApiModelProperty(value = "顶级节点id（第一级）")
    private String firstCategoryNo;

    @ApiModelProperty(value = "二级节点id")
    private String secondCategoryNo;

    @ApiModelProperty(value = "三级节点id")
    private String thirdCategoryNo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
