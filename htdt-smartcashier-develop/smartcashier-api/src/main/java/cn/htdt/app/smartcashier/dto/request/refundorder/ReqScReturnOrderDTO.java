package cn.htdt.app.smartcashier.dto.request.refundorder;


import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
  * @Description : 售后订单管理传参
  * <AUTHOR>
  *@date : 2020/9/2/ 17:34
  */
@EqualsAndHashCode(callSuper = true)
@Data
public class ReqScReturnOrderDTO extends ReqComPageDTO implements Serializable {

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "售后编号")
    private String soReturnNo;

     @ApiModelProperty(value = "售后类型")
    private String returnType;

    @ApiModelProperty(value = "退款单状态")
    private String refundmentStatus;

    @ApiModelProperty(value = "退单状态")
    private String returnStatus;

    @ApiModelProperty(value = "退款方式")
    private String refundmentWay;

    @ApiModelProperty(value = "退款渠道来源")
    private String source;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "申请查询开始时间 yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applyStartTime;

    @ApiModelProperty(value = "申请查询结束时间 yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applyEndTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
 }