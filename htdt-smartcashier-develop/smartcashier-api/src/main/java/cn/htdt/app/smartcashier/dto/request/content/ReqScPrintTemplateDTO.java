package cn.htdt.app.smartcashier.dto.request.content;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "商品、订单标签打印模板请求实体")
public class ReqScPrintTemplateDTO implements Serializable {
    /**
     * 模板编码
     */
    @ApiModelProperty("模板编码")
    private String templateNo;
}
