package cn.htdt.app.smartcashier.dto.request.points;

import cn.htdt.app.smartcashier.dto.base.ReqAppComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class ReqScGetUserConvertDTO extends ReqAppComPageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "手机号")
    @NotBlank(message = "手机号不能为空")
    private String phone;
}
