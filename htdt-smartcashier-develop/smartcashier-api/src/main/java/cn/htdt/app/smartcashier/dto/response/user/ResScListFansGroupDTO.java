package cn.htdt.app.smartcashier.dto.response.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021-07-06
 * @description 查询粉丝分组响应DTO
 **/
@Data
public class ResScListFansGroupDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "分组编号")
    private String groupNumber;

    @ApiModelProperty(value = "分组名称")
    private String groupName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
