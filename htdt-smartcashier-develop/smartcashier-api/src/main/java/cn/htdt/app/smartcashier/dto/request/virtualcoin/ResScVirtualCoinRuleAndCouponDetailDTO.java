package cn.htdt.app.smartcashier.dto.request.virtualcoin;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import cn.htdt.common.enums.market.CouponUseScopeEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 橙豆规则对应的优惠券配置
 *
 * <AUTHOR>
 * @since 2023-01-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="ResScVirtualCoinRuleAndCouponDetailDTO对象", description="橙豆规则对应的优惠券配置")
public class ResScVirtualCoinRuleAndCouponDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "券编号")
    private String couponNo;

    @ApiModelProperty("活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "活动名称")
    private String couponName;

    @ApiModelProperty(value = "优惠券类型 （1001:满减券 1002:折扣券 1003:现金券）")
    private String couponType;

    @ApiModelProperty(value = "使用门槛阈值（满减时表示满多少元）")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal discountThreshold;

    @ApiModelProperty(value = "券类型为1001满减券时, 为券面额, 为1002折扣券时, 为对应的几折, 7折的话, 为数字7, 保留两位小数")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal couponValue;

    @ApiModelProperty(value = "券有效天数（只有有效期为1003时使用）")
    private Integer couponEffectiveDay;

    @ApiModelProperty(value = "券使用渠道 (1001:汇享购下单 1002:门店下单)")
    private String couponUseChannel;

    @Converter(enumClass = CouponUseScopeEnum.class, fieldName = "couponUseScopeValue", enumField = "type")
    @ApiModelProperty(value = "券使用范围(1001:店铺全场通用 1002:云池商品通用 1003:指定云池商品 1004:店铺指定类目 1005:店铺指定品牌 1006:店铺指定商品 1007:指定店铺类目)")
    private String couponUseScope;

    @ApiModelProperty(value = "券使用范围value")
    private String couponUseScopeValue;

    /**
     * 是否选中, 1: 未选中, 2: 选中
     */
    @ApiModelProperty(value = "券是否选中（1:未选中 2:选中）")
    private String selectFlag;
}
