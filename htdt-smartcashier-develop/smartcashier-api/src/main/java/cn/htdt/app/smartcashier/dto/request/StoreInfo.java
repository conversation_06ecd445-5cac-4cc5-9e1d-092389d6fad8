package cn.htdt.app.smartcashier.dto.request;

import lombok.Data;

@Data
public class StoreInfo {
    /**
     * 商家编号
     */
    private String merchantNo;
    /**
     * 商家名称
     */
    private String merchantName;
    /**
     * 门店编号，对应orgId
     */
    private String storeNo;
    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 当前登录人ID
     */
    private String createNo;

    /**
     * 当前登录人名字
     */
    private String createName;

    /**
     * 当前登录用户身份 1-运营 2-商家 4-店铺 8-单店
     */
    private Integer loginIdentity;
}
