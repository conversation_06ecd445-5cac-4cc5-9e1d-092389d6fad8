package cn.htdt.app.smartcashier.dto.response.market.cardcounting;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.enums.market.CardPeriodValidityEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 计次卡设置表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-11
 */
@Data
public class ResScCardCountingConfigDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "促销编码")
    private String promotionNo;

    @ApiModelProperty(value = "计次卡编号")
    private String cardCountingNo;

    @ApiModelProperty(value = "计次卡名称")
    private String cardCountingName;

    @ApiModelProperty(value = "次数（次）")
    private Integer counts;

    @ApiModelProperty(value = "售价（元）")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "原价（元）")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "有效类型（1001：长期有效 1002：指定日期 1003:自领取后天数有效）")
    @Converter(enumClass = CardPeriodValidityEnum.class, fieldName = "cardPeriodValidityName", enumField = "type")
    private String cardPeriodValidity;

    @ApiModelProperty(value = "有效类型名称")
    private String cardPeriodValidityName;

    @ApiModelProperty(value = "有效天数（天）（只有有效期为1003时使用）")
    private Integer cardEffectiveDay;

    @ApiModelProperty(value = "指定日期开始时间")
    @JsonFormat(pattern = "yyyy.MM.dd", timezone = "GMT+8")
    private LocalDateTime cardEffectiveTime;

    @ApiModelProperty(value = "开始时间结束时间")
    @JsonFormat(pattern = "yyyy.MM.dd", timezone = "GMT+8")
    private LocalDateTime cardInvalidTime;

    @ApiModelProperty(value = "总库存（份）")
    private Integer totalStockNum;

    @ApiModelProperty(value = "购买渠道 (1001:一体机开单)")
    private String useChannel;

    @ApiModelProperty(value = "使用须知")
    private String useExplain;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "门店编号，对应orgId")
    private String storeNo;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "是否可用:默认1，1：可用，其余不可用")
    private Integer disableFlag;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime modifyTime;

    @ApiModelProperty(value = "可用库存数量，即可售库存")
    private BigDecimal availableStockNum;


}
