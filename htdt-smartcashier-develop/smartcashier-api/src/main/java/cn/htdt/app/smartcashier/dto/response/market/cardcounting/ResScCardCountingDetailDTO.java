package cn.htdt.app.smartcashier.dto.response.market.cardcounting;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 计次卡活动保存DTO
 *
 * <AUTHOR>
 * @date 2022-09-21
 */
@Data
public class ResScCardCountingDetailDTO {

    @ApiModelProperty(value = "活动编号,编辑活动时必填")
    private String cardCountingNo;

    @ApiModelProperty(value = "活动名称", required = true)
    private String cardCountingName;

    @ApiModelProperty(value = "售价")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "原价")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "次数")
    private Integer counts;

    @ApiModelProperty(value = "券有效期（1001：长期有效 1002：指定日期 1003:自购买之日起计算有效期）")
    private String cardPeriodValidity;

    @ApiModelProperty(value = "券有效天数（只有有效期为1003时使用）")
    private Integer cardEffectiveDay;

    @ApiModelProperty(value = "开始日期(yyyy-MM-dd)，有效期为1002时使用")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDateTime cardEffectiveTime;

    @ApiModelProperty(value = "结束日期(yyyy-MM-dd)，有效期为1002时使用")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDateTime cardInvalidTime;

    @ApiModelProperty(value = "购买渠道 (1001:一体机开单)")
    private String useChannel;

    @ApiModelProperty(value = "总库存")
    private Integer totalStockNum;

    @ApiModelProperty(value = "剩余库存")
    private Integer remainNum;

    @ApiModelProperty(value = "使用说明")
    private String useExplain;

    @ApiModelProperty(value = "上下架状态（1：未上架 2：上架） 保存并上架存", required = true)
    private Integer upDownFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
