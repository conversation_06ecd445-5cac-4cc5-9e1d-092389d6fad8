package cn.htdt.app.smartcashier.dto.response.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * 类目响应DTO
 *
 * <AUTHOR>
 * @date 2023-02-23
 */
@Data
public class ResScCategoryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "类目编号")
    private String categoryNo;

    @ApiModelProperty(value = "类目名称")
    private String categoryName;

    @ApiModelProperty(value = "类目名称全路径")
    private String fullNamePath;

    @ApiModelProperty(value = "类目id全路径")
    private String fullIdPath;

    @ApiModelProperty(value = "层级(1001:一级 1002：二级 1003：三级 1004：四级)")
    private String categoryLevel;

    @ApiModelProperty(value = "父类目节点ID")
    private String parentNo;

    @ApiModelProperty(value = "子目录")
    private List<ResScCategoryDTO> childCategory;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
