package cn.htdt.app.smartcashier.dto.request.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ReqGioInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "类型 1000:功能 1001:千橙掌柜使用时长 1002:汇享购使用时长")
    private String type;

    @ApiModelProperty(value = "上传数据json")
    private String requestJson;

    @ApiModelProperty(value = "基础数据")
    private String basics;
}
