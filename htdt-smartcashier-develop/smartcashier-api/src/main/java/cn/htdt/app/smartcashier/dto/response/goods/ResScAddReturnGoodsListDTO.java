package cn.htdt.app.smartcashier.dto.response.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * 新增退货单下的可退商品数据和不可退商品数据 集合
 *
 * <AUTHOR>
 * @Date 2021-08-23
 **/
@Data
public class ResScAddReturnGoodsListDTO implements Serializable {

    private static final long serialVersionUID = 1009413141391302622L;

    @ApiModelProperty(value = "可退商品数据")
    private List<ResScAddReturnGoodsDTO> returnGoodsList;

    @ApiModelProperty(value = "不可退商品数据")
    private List<ResScAddReturnGoodsDTO> notReturnGoodsList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
