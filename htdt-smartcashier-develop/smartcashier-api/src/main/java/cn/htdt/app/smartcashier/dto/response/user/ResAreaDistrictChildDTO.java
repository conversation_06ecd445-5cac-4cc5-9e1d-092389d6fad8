package cn.htdt.app.smartcashier.dto.response.user;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

@Data
public class ResAreaDistrictChildDTO implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 8043288254146387128L;

	/**
     * 县镇市省编码，省、直辖市两位；普通市、直辖（区、县）两位；普通县两位；镇、乡3位；村委会3位；共12位'
     */
    private String code;

    /**
     * 地址名称
     */
    private String name;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }
}