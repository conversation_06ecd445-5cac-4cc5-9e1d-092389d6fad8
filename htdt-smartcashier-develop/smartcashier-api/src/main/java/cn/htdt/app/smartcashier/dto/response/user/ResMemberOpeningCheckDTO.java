package cn.htdt.app.smartcashier.dto.response.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 会员开通校验结果DTO
 *
 * <AUTHOR>
 * @date 2023/04/10
 **/
@Data
public class ResMemberOpeningCheckDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "会员套餐权限校验是否通过：1-是；2-否")
    private Integer memberPurviewCheckFlag;
    @ApiModelProperty(value = "会员配置开关：1-开启；2-关闭")
    private Integer memberConfigSwitchFlag;
}
