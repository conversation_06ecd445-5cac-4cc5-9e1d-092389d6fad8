package cn.htdt.app.smartcashier.dto.request.virtualcoin;

import cn.htdt.app.smartcashier.dto.base.ReqAppComPageDTO;
import cn.htdt.app.smartcashier.dto.base.ValidatedModify;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;

/**
 * 查看橙豆规则, 对应的优惠券信息
 * <AUTHOR>
 * @since 2023-01-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ReqVirtualCoinRuleCouponDTO extends ReqAppComPageDTO {

    @ApiModelProperty(value = "橙豆规则编号", required = true)
    @NotBlank(message = "橙豆规则编号不能为空", groups = {ValidatedModify.class})
    private String virtualNo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
