package cn.htdt.app.smartcashier.dto.response.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 商品串码列表响应DTO
 *
 * <AUTHOR>
 * @date 2021-08-26
 */
@Data
public class ResScGoodsImeiDTO implements Serializable {

    private static final long serialVersionUID = -5034141676770181670L;

    @ApiModelProperty(value = "商品串码主键")
    private String imeiNo;

    @ApiModelProperty(value = "商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseNo;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "商品串码")
    private String imei;

    @ApiModelProperty(value = "采购单编码")
    private String purchaseCode;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
