package cn.htdt.app.smartcashier.dto.request.statement;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 一体机-经营报表-出入库记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-19
 */
@Data
public class ReqScOutPutRecordDTO extends ReqSoSourceDealDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "出入库类型 1001:入库 1002：出库")
    private String operType;

    @ApiModelProperty(value = "商品名称、条形码 条件查询")
    private String goodsStr;

    @ApiModelProperty(value = "商品形式 1001-普通商品;1002-系列商品")
    private String goodsForm;

    @ApiModelProperty(value = "是否启用串码(1:否 2:是)")
    private Integer imeiFlag;

    @ApiModelProperty(value = "商品品牌ID")
    private String brandNo;

    @ApiModelProperty(value = "商品类目ID  目前传的3级类目")
    private String categoryNo;

    @ApiModelProperty(value = "类目全路径")
    private String fullIdPath;

    /**
     * 查询类目类型,1001:店铺类目查询,1002:销售类目查询
     */
    @ApiModelProperty(value = "查询类目类型,1001:店铺类目查询,1002:销售类目查询")
    private String categoryType;
}
