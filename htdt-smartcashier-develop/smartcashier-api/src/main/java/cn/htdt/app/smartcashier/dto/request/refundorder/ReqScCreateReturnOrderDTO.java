package cn.htdt.app.smartcashier.dto.request.refundorder;

import cn.htdt.common.dto.request.AdminParamsDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 创建售后单
 *
 * <AUTHOR>
 */
@Data
public class ReqScCreateReturnOrderDTO extends AdminParamsDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = 6509630984547847248L;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "售后类型")
    private String returnType;

    @ApiModelProperty(value = "退款方式")
    private String refundmentWay;

    @ApiModelProperty(value = "退款渠道（线下退款）")
    private String refundmentChannel;

    @ApiModelProperty(value = "退单原因")
    private String returnReason;

    @ApiModelProperty(value = "退单申请描述")
    private String returnRemark;

    @ApiModelProperty(value = "凭证图片列表")
    private List<String> vouchers;

    @ApiModelProperty(value = "申请商品信息")
    private List<ReqScReturnItemDTO> returnGoodsList;
}