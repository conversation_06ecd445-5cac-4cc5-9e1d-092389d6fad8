package cn.htdt.app.smartcashier.dto.response.goods;


import cn.htdt.app.smartcashier.dto.request.goods.ReqScSeriesGoodsDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 功能描述: 商品响应dto
 *
 * <AUTHOR>
 * @date 2021/8/16
 */
@Data
public class ResScGoodsInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商品编号，包含普通商品、套餐商品等所有商品")
    private String goodsNo;

    @ApiModelProperty(value = "商品形式 1001-普通商品;1002-系列商品")
    private String goodsForm;

    @ApiModelProperty(value = "商品形式value")
    private String goodsFormValue;

    @ApiModelProperty(value = "商品类型 1001-实物商品;1002-虚拟商品;1003-服务商品（仅供平台使用）;1004-称重商品")
    private String goodsType;

    @ApiModelProperty(value = "商品类型value")
    private String goodsTypeValue;

    @ApiModelProperty(value = "商品类目ID")
    private String categoryNo;

    @ApiModelProperty(value = "商品类目名称")
    private String categoryName;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品品牌ID")
    private String brandNo;

    @ApiModelProperty(value = "商品品牌名称")
    private String brandName;

    @ApiModelProperty(value = "零售价")
    private BigDecimal retailPrice;

    @ApiModelProperty(value = "计量单位ID")
    private String calculationUnitNo;

    @ApiModelProperty(value = "计量单位符号")
    private String calculationUnitSymbol;

    @ApiModelProperty(value = "计量单位名称")
    private String calculationUnitName;

    @ApiModelProperty(value = "是否有辅计量单位:1-否;2-是;")
    private Integer standardFlag;

    @ApiModelProperty(value = "辅计量单位id")
    private String assistCalculationUnitNo;

    @ApiModelProperty(value = "辅计量单位符号")
    private String assistCalculationUnitSymbol;

    @ApiModelProperty(value = "辅计量单位名称")
    private String assistCalculationUnitName;

    @ApiModelProperty(value = "转换率(辅转主)")
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "是否入仓:1-否;2-是;")
    private Integer warehouseFlag;

    @ApiModelProperty(value = "是否入仓value")
    private String wareHouseFlagValue;

    @ApiModelProperty(value = "商品来源类型值")
    private String goodsSourceTypeValue;

    @ApiModelProperty(value = "商品来源类型1001-平台自建;1002-商家自建;1003-店铺自建;1004:一键代发;1005:商家分发")
    private String goodsSourceType;

    @ApiModelProperty(value = "商品型号")
    private String goodsModel;

    @ApiModelProperty(value = "商品助记码")
    private String goodsHelpCode;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "供应商手机号")
    private String supplierTelphone;

    @ApiModelProperty(value = "限价")
    private BigDecimal limitPrice;

    @ApiModelProperty(value = "市场价")
    private BigDecimal marketPrice;

    @ApiModelProperty(value = "采购价")
    private BigDecimal purchasePrice;

    @ApiModelProperty(value = "条形码")
    private String barcode;

    @ApiModelProperty(value = "实际库存")
    private BigDecimal realStockNum;

    @ApiModelProperty(value = "商品主图")
    private String mainPictureUrl;

    @ApiModelProperty(value = "系列商品")
    private ReqScSeriesGoodsDTO seriesGoods;

    @ApiModelProperty(value = "商品文描")
    private String content;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
