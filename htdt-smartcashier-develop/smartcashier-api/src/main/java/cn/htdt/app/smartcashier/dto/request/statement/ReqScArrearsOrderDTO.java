package cn.htdt.app.smartcashier.dto.request.statement;

import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 欠款订单查询请求参数DTO
 *
 * <AUTHOR>
 * @date 2021-08-27
 */
@Data
public class ReqScArrearsOrderDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 5091868491600114091L;

    @ApiModelProperty(value = "1:今日 2:昨天 3近一周 4:近30天 5:自定义", required = true)
    private Integer dayType;

    @ApiModelProperty(value = "模糊查询字段 下单人手机号或姓名")
    private String keyWord;

    @ApiModelProperty(value = "5:自定义-开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate countStartTime;

    @ApiModelProperty(value = "5:自定义-结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate countEndTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
