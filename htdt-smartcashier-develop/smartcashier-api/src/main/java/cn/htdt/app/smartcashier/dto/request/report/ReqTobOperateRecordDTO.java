package cn.htdt.app.smartcashier.dto.request.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
@Data
public class ReqTobOperateRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编码")
    private String operateNo;

    @ApiModelProperty(value = "行业")
    private String industryNo;

    @ApiModelProperty(value = "应用程序来源 字典AppChannelSourceEnum")
    private String appChannelSource;

    @ApiModelProperty(value = "模块类型")
    private String moduleType;

    @ApiModelProperty(value = "功能类型")
    private String functionType;

    @ApiModelProperty(value = "商家编码")
    private String merchantNo;

    @ApiModelProperty(value = "门店编码")
    private String storeNo;

    @ApiModelProperty(value = "会员编码")
    private String merberCode;

    @ApiModelProperty(value = "测试店标识 1:非测试店,2:测试店")
    private Integer testFlag;

    @ApiModelProperty(value = "操作次数")
    private Integer operateNum;

    @ApiModelProperty(value = "操作时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime operateTime;
}
