package cn.htdt.app.smartcashier.dto.response.order;

import cn.htdt.common.dto.annon.PrivateData;
import cn.htdt.common.dto.enums.DecipherScene;
import cn.htdt.common.dto.enums.HandlerPolicy;
import cn.htdt.common.dto.response.ResEnumsDTO;
import cn.htdt.orderprocess.dto.response.ResOrderGoodsDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 获取待发货订单明细
 *
 * <AUTHOR>
 */
@Data
public class ResScDeliverOrderItemDTO implements Serializable {

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /**
     * 收货人姓名
     */
    @ApiModelProperty(value = "收货人姓名")
    private String goodReceiverName;

    /**
     * 收货人国家
     */
    @ApiModelProperty(value = "收货人国家")
    private String goodReceiverCountry;

    /**
     * 收货人省份
     */
    @ApiModelProperty(value = "收货人省份")
    private String goodReceiverProvince;

    /**
     * 收货人城市
     */
    @ApiModelProperty(value = "收货人城市")
    private String goodReceiverCity;

    /**
     * 收货人县区
     */
    @ApiModelProperty(value = "收货人县区")
    private String goodReceiverCounty;

    /**
     * 收货人区镇
     */
    @ApiModelProperty(value = "收货人区镇")
    private String goodReceiverArea;

    /**
     * 收货人地址
     */
    @ApiModelProperty(value = "收货人地址")
    private String goodReceiverAddress;

    /**
     * 收货人地址-加密
     */
    @ApiModelProperty(value = "收货人地址-加密")
    private String dsGoodReceiverAddress;

    /**
     * 收货人手机号
     */
    @ApiModelProperty(value = "收货人手机号")
    private String goodReceiverMobile;

    /**
     * 收货人手机号-加密
     */
    @ApiModelProperty(value = "收货人手机号-加密")
    @PrivateData(handlerPolicy = HandlerPolicy.DECIPHER, decipherScene= DecipherScene.SMARTCASHIER_ORDER_DETAIL, fieldName = "goodReceiverMobile")
    private String dsGoodReceiverMobile;

    /**
     * 配送方式
     */
    @ApiModelProperty(value = "配送方式")
    private String orderDeliveryWay;

    /**
     * 商品信息
     */
    @ApiModelProperty(value = "商品信息")
    private List<ResOrderGoodsDTO> resOrderGoodsDTOList;

    @ApiModelProperty(value = "配送方式枚举")
    private List<ResEnumsDTO> deliveryWayEnums;
    /**
     * 订单标识，1 普通，2 云池，3 分销，4 中奖 字典OrderFlagEnum
     */
    @ApiModelProperty(value = "订单标识，1 普通，2 云池，3 分销，4 中奖 字典OrderFlagEnum")
    private Integer orderFlag;

}
