package cn.htdt.app.smartcashier.dto.response.goods;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 商品串码列表 响应DTO
 *
 * <AUTHOR>
 * @date 2021/8/25
 **/
@Data
public class ResScGoodsImeiListDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商品串码主键")
    private String imeiNo;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseNo;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "商品串码")
    private String imei;

    @ApiModelProperty(value = "是否已销售:1=否;2=是")
    private Integer saleFlag;

    @ApiModelProperty(value = "是否已销售:1=否;2=是")
    private String saleFlagStr;

    @ApiModelProperty(value = "商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "采购单编码")
    private String purchaseCode;

    @ApiModelProperty(value = "操作时间（修改时间）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime modifyTime;
}
