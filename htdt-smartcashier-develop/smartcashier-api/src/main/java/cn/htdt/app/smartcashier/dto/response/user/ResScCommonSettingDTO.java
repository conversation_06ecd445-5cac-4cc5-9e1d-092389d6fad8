package cn.htdt.app.smartcashier.dto.response.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 公共设置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-22
 */
@Data
public class ResScCommonSettingDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "设置类型")
    private String settingType;

    @ApiModelProperty(value = "设置值")
    private String settingValue;

    public ResScCommonSettingDTO(String settingType, String settingValue) {
        this.settingType = settingType;
        this.settingValue = settingValue;
    }
}
