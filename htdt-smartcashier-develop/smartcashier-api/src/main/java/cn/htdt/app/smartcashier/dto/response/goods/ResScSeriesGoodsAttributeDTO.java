package cn.htdt.app.smartcashier.dto.response.goods;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 *
 * 功能描述: 生成系列商品属性dto
 * @author: 张宇
 * @date: 2020/10/19 14:52
 */
@Data
public class ResScSeriesGoodsAttributeDTO implements Serializable {

    @ApiModelProperty(value = "属性名称")
    private String attributeName;

    @ApiModelProperty(value = "属性值名称")
    private List<String> attributeValueNameList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
