package cn.htdt.app.smartcashier.dto.response.user;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

@Data
public class ResAreaDistrictDTO implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 8043288254146387128L;

    private String district;

    private List<ResAreaDistrictChildDTO> listArea;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }
}