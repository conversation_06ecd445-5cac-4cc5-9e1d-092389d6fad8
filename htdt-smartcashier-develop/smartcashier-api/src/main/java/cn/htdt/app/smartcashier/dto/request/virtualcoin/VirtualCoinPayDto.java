package cn.htdt.app.smartcashier.dto.request.virtualcoin;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022-1-20 10:08
 * @description
 */
@Data
public class VirtualCoinPayDto {

    @ApiModelProperty(value = "粉丝编号", required = true)
    private String fanNo;

    @ApiModelProperty(value = "橙豆规则编号", required = true)
    private String virtualNo;

    @ApiModelProperty(value = "支付类型 1000 现金；1002 扫码支付", required = true)
    private String payType;


    @ApiModelProperty(required = true, value = "支付金额")
    private BigDecimal payAmount;

    @ApiModelProperty(required = true, value = "橙豆金额")
    private BigDecimal availableAmount;

}
