package cn.htdt.app.smartcashier.dto.response.goods;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 *
 * 功能描述: 系列商品返回dto
 * @author: 张宇
 * @date: 2020/9/11 14:51
 */
@Data
public class ResScSeriesGoodsDTO implements Serializable {

    @ApiModelProperty(value = "属性名称集合")
    private List<String> attributeNameList;

    @ApiModelProperty(value = "子品集合")
    private List<ResScSubGoodsDTO> subGoodsList;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
