package cn.htdt.app.smartcashier.dto.request.refundorder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *  退款明细DTO
 *
 * <AUTHOR>
 */
@Data
public class ReqScReturnItemDTO implements Serializable {

    private static final long serialVersionUID = 2796756250040890993L;

    @ApiModelProperty(value = "商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "订单明细编号")
    private String orderItemNo;

    @ApiModelProperty(value = "申请售后数量")
    private BigDecimal returnProductItemNum;

    @ApiModelProperty(value = "申请售后金额")
    private BigDecimal applyReturnAmount;
}