package cn.htdt.app.smartcashier.dto.request.statement;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 一体机-经营报表-商品库存记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-20
 */
@Data
public class ReqScStockRecordDTO  extends ReqSoSourceDealDTO  implements Serializable {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseNo;

    @ApiModelProperty(value = "库存类型 1001:有货 1002：无货")
    private String stockNumType;

    @ApiModelProperty(value = "商品名称、条形码 条件查询")
    private String goodsStr;

    @ApiModelProperty(value = "商品形式 1001-普通商品;1002-系列商品")
    private String goodsForm;

    @ApiModelProperty(value = "是否启用串码(1:否 2:是)")
    private Integer imeiFlag;

    @ApiModelProperty(value = "商品品牌ID")
    private String brandNo;

    @ApiModelProperty(value = "商品类目ID  目前传的3级类目")
    private String categoryNo;

    @ApiModelProperty(value = "类目全路径")
    private String fullIdPath;

    /**
     * 查询类目类型,1001:店铺类目查询,1002:销售类目查询
     */
    @ApiModelProperty(value = "查询类目类型,1001:店铺类目查询,1002:销售类目查询")
    private String categoryType;
}
