package cn.htdt.app.smartcashier.dto.response.goods;

import cn.htdt.app.smartcashier.dto.base.ScGoodsValidityPeriodDTO;
import cn.htdt.app.smartcashier.dto.response.market.ResScGoodsVipPriceInfoDTO;
import cn.htdt.common.dto.BigDecimalSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 采购入库列表响应DTO
 *
 * <AUTHOR>
 * @date 2021-08-16
 */
@Data
public class ResScPurchaseOrderStorageDTO extends ScGoodsValidityPeriodDTO implements Serializable {

    private static final long serialVersionUID = 6297464505995260412L;

    @ApiModelProperty(value = "采购单编码")
    private String purchaseCode;

    @ApiModelProperty(value = "采购单商品行编码")
    private String purchaseGoodsCode;

    @ApiModelProperty(value = "商品编码")
    private String goodsNo;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品名称")
    private String showGoodsName;

    @ApiModelProperty(value = "计量单位ID")
    private String calculationUnitNo;

    @ApiModelProperty(value = "计量单位名称")
    private String calculationUnitName;

    @ApiModelProperty(value = "采购单位")
    private String purchaseUnit;

    @ApiModelProperty(value = "是否有辅计量单位：1-否;2-是")
    private Integer standardFlag;

    @ApiModelProperty(value = "辅计量单位id")
    private String assistCalculationUnitNo;

    @ApiModelProperty(value = "辅计量单位名称")
    private String assistCalculationUnitName;

    @ApiModelProperty(value = "主计量单位对应关系数值")
    private BigDecimal mainUnitNum;

    @ApiModelProperty(value = "辅计量单位对应关系数值")
    private BigDecimal assistUnitNum;

    @ApiModelProperty(value = "转换率(辅转主)")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "是否入仓：1-否;2-是")
    private Integer warehouseFlag;
    /**
     * 是否入仓:1-否;2-是;
     */
    @ApiModelProperty(value = "商品组是否入仓：1-否;2-是")
    private Integer warehouseFlagOfGoodsGroups;

    @ApiModelProperty(value = "是否入仓")
    private String warehouseFlagStr;

    @ApiModelProperty(value = "采购数量")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal purchaseNum;

    @ApiModelProperty(value = "待入库数量")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal waitStorageCount;

    @ApiModelProperty(value = "已入库数量")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal storageCount;

    @ApiModelProperty(value = "订单状态 1001-待入库 1002-部分入库 1003-完成")
    private String orderStatus;

    @ApiModelProperty(value = "是否启用串码标识（1:否，2:是）")
    private Integer imeiFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
