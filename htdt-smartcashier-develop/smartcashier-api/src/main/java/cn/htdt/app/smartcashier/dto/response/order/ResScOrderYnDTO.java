package cn.htdt.app.smartcashier.dto.response.order;

import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.dto.request.ReqComPageDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023-07-31
 * @Description 羊乃-订单响应DTO
 **/
@Data
public class ResScOrderYnDTO extends ReqComPageDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = -3197889257683106301L;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "订单状态描述")
    private String orderStatusName;

    @ApiModelProperty(value = "下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderCreateTime;

    @ApiModelProperty(value = "商品名称")
    private String goodsNameConcat;

    @ApiModelProperty(value = "订单实付金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal realAmount;
}