package cn.htdt.app.smartcashier.dto.request.virtualcoin;

import cn.htdt.app.smartcashier.dto.base.ValidatedModify;
import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-1-20 10:08
 * @description
 */
@Data
public class VirtualCoinRuleDto {
    @ApiModelProperty(value = "橙豆规则编号", required = true)
    @NotBlank(message = "橙豆规则编号不能为空",groups = {ValidatedModify.class})
    private String virtualNo;

    @ApiModelProperty(required = true, value = "支付金额")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    @NotNull(message = "请输入支付金额")
    @Digits(integer = 9, fraction = 2,message = "金额格式不正确")
    @DecimalMin(value = "0.01",message = "请输入正确的金额")
    private BigDecimal payAmount;

    @ApiModelProperty(required = true, value = "橙豆金额")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    @NotNull(message = "请输入橙豆数量")
    @Digits(integer = 9, fraction = 2,message = "橙豆数量格式不正确")
    @DecimalMin(value = "0.01",message = "请输入正确的橙豆数量")
    private BigDecimal availableAmount;

    @ApiModelProperty(value = "优惠券和活动编码集合")
    private List<ReqScVirtualCoinRuleCouponDTO> virtualCoinRuleAndCouponList;

    @ApiModelProperty(required = true, value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(required = true, value = "商家名称")
    private String merchantName;

    @ApiModelProperty(required = true, value = "门店编号")
    private String storeNo;

    @ApiModelProperty(required = true, value = "店铺名称")
    private String storeName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
