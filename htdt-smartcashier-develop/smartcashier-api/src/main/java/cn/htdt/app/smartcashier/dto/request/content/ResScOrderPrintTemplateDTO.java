package cn.htdt.app.smartcashier.dto.request.content;

import cn.htdt.common.dto.request.ReqBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 订单标签打印模板返回实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "订单标签打印模板返回实体类")
public class ResScOrderPrintTemplateDTO extends ReqBaseDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 模板编码
     */
    @ApiModelProperty(value = "模板编码")
    private String templateNo;

    /**
     * 模板名称
     */
    @ApiModelProperty(value = "模板名称")
    private String templateName;

    /**
     * 商品标签模板宽度, 单位: mm
     */
    @ApiModelProperty(value = "商品标签模板宽度, 单位: mm")
    private String templateWidth;

    /**
     * 商品标签模板高度, 单位: mm
     */
    @ApiModelProperty(value = "商品标签模板高度, 单位: mm")
    private String templateHeight;

    /**
     * 是否默认: 1001-默认; 1002或者为空:非默认;
     */
    @ApiModelProperty(value = "是否默认: 1001-默认; 1002:非默认;")
    private String templateDefaultFlag;

    /**
     * 缩略图url
     */
    @ApiModelProperty(value = "缩略图地址")
    private String thumbnailUrl;

    /**
     * 大图url
     */
    @ApiModelProperty(value = "大图url")
    private String bigPictureUrl;

    /**
     * 排序值
     */
    @ApiModelProperty(value = "排序值")
    private Integer sortValue;

    @ApiModelProperty(value = "颜色类型: 1-黑白，2-彩色, 默认黑白")
    private String colorType;

    @ApiModelProperty(value = "模板来源 1001-平台自建;1002-商家自建;1003-店铺自建;1004-系统创建")
    private String templateSourceType;

    /**
     * 店铺编号
     */
    @ApiModelProperty(value = "店铺编号")
    private String storeNo;

    /**
     * 店铺名称
     */
    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    /**
     * 商家编号
     */
    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    /**
     * 商家名称
     */
    @ApiModelProperty(value = "商家名称")
    private String merchantName;
}
