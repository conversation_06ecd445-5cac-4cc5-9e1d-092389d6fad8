package cn.htdt.app.smartcashier.dto.response.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * 销售类目 响应DTO
 *
 * <AUTHOR>
 * @date 2021/8/18
 **/
@Data
public class ResScSaleCategoryListDTO implements Serializable {

    private static final long serialVersionUID = -1L;

    @ApiModelProperty(value = "类目编号")
    private String categoryNo;

    @ApiModelProperty(value = "类目名称")
    private String categoryName;

    @ApiModelProperty(value = "类目id全路径")
    private String fullIdPath;

    @ApiModelProperty(value = "类目名称全路径")
    private String fullNamePath;

    @ApiModelProperty(value = "排序值")
    private Integer sortValue;

    @ApiModelProperty(value = "子目录")
    private List<ResScSaleCategoryListDTO> childCategory;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
