package cn.htdt.app.smartcashier.dto.response.goods;

import cn.htdt.app.smartcashier.dto.response.market.ResScGoodsVipPriceInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 详细说明.子品dto
 * <p>
 * Copyright: Copyright (c) 2020/11/4 13:55
 * <p>
 * Company: htd
 * <p>
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class ResScSubGoodsDTO implements Serializable {

    /**
     * 系列商品id
     */
    @ApiModelProperty(value = "系列商品id")
    private String goodsNo;

    @ApiModelProperty(value = "系列商品名称")
    private String goodsName;

    @ApiModelProperty(value = "第一属性名称")
    private String firstAttributeName;

    @ApiModelProperty(value = "第一属性值")
    private String firstAttributeValueName;

    @ApiModelProperty(value = "第二属性名称")
    private String secondAttributeName;

    @ApiModelProperty(value = "第二属性值")
    private String secondAttributeValueName;

    @ApiModelProperty(value = "第三属性名称")
    private String thirdAttributeName;

    @ApiModelProperty(value = "第三属性值")
    private String thirdAttributeValueName;

    @ApiModelProperty(value = "条形码")
    private String barcode;

    @ApiModelProperty(value = "子品图片")
    private String mainPictureUrl;

    @ApiModelProperty(value = "可售库存")
    private BigDecimal canSaleStockNum;

    @ApiModelProperty(value = "实际库存")
    private BigDecimal realStockNum;

    @ApiModelProperty(value = "限价")
    private BigDecimal limitPrice;

    @ApiModelProperty(value = "市场价")
    private BigDecimal marketPrice;

    @ApiModelProperty(value = "采购价")
    private BigDecimal purchasePrice;

    @ApiModelProperty(value = "零售价")
    private BigDecimal retailPrice;

    @ApiModelProperty(value = "云池供货价")
    private BigDecimal cloudPoolSupplyPrice;

    @ApiModelProperty(value = "云池佣金配置")
    private ResScCloudPoolCommissionConfigDTO commissionConfig;

    @ApiModelProperty(value = "仓库信息")
    private List<ResScWarehouseGoodsRelationDTO> warehouseList;

    @ApiModelProperty(value = "特惠促销信息")
    private ResScGoodsVipPriceInfoDTO vipPriceInfoDTO;

    @ApiModelProperty(value = "会员价活动信息")
    private ResScGoodsVipPriceInfoDTO memberPriceInfoDTO;

    /**
     * //20250513 是否关联商品组 0否,1是
     */
    @ApiModelProperty(value = "是否关联商品组:默认0，1")
    private Integer isRelatedGoods;

    /**
     * 商品组编码
     */
    private String goodsGroupsNo;


    /**
     * 是否入仓:1-否;2-是;（商品组）
     */
    private Integer warehouseFlagOfGoodsGroups;

    @ApiModelProperty(value = "是否入仓:1-否;2-是;")
    private Integer warehouseFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
