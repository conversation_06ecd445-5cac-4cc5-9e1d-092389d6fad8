package cn.htdt.app.smartcashier.dto.response.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-07-01
 * @description 查看粉丝详情响应DTO
 **/
@Data
public class ResDetailFansDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "编号")
    private String fanNo;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty("粉丝备注名称（店铺）")
    private String storeFanName;
    @ApiModelProperty(notes = "昵称")
    private String nickName;

    @ApiModelProperty(value = "性别 0 男 1 女 2保密")
    private Integer sex;

    @ApiModelProperty(value = "手机号码")
    private String phone;

    @ApiModelProperty(value = "手机号码")
    private String dsPhone;

    @ApiModelProperty(value = "可用优惠券数量")
    private Long couponCount;

    @ApiModelProperty(value = "成交订单数量")
    private Long dealsCount;

    @ApiModelProperty("总应付金额")
    private BigDecimal totalShouldAmount;

    @ApiModelProperty("总欠款金额")
    private BigDecimal totalArrearsAmount;

    @ApiModelProperty("橙豆余额")
    private String accountRemainCoins;

    @JsonFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(notes = "出生日期")
    private LocalDate birthday;

    @ApiModelProperty(value = "头像地址")
    private String headImg;

    @ApiModelProperty(value = "省code")
    private String provinceCode;

    @ApiModelProperty(value = "省名")
    private String provinceName;

    @ApiModelProperty(value = "市code")
    private String cityCode;

    @ApiModelProperty(value = "市名")
    private String cityName;

    @ApiModelProperty(value = "区code")
    private String regionCode;

    @ApiModelProperty(value = "区名")
    private String regionName;

    @ApiModelProperty(value = "街道code")
    private String streetCode;

    @ApiModelProperty(value = "街道名称")
    private String streetName;

    @ApiModelProperty(value = "详细地址")
    private String detailAddress;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "分组list")
    private List<ResScListFansGroupDTO> resScListFansGroupDTOList;

    @ApiModelProperty(value = "状态 1粉丝 2代理人")
    private Integer type;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "粉丝积分")
    private Integer fansPoint;

    @ApiModelProperty(value = "加入店铺时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime joinStoreTime;

    @ApiModelProperty("对接省市区后续地址 密文 解密后为明文")
    private String dsSubsequentAddress;

    @ApiModelProperty("对接省市区后续地址脱敏文 作为入参时传此字段")
    private String subsequentAddress;

    @ApiModelProperty("其他备注")
    private String otherRemarks;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
