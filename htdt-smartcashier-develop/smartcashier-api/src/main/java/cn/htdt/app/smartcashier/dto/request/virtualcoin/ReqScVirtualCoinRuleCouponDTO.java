package cn.htdt.app.smartcashier.dto.request.virtualcoin;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 橙豆规则-优惠券和活动关联请求数据
 *
 * <AUTHOR>
 * @since 2023-01-28
 */
@Data
public class ReqScVirtualCoinRuleCouponDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    /**
     * 优惠券编码集合
     */
    @ApiModelProperty(value = "优惠券编码")
    private String couponNo;
}
