package cn.htdt.app.smartcashier.dto.response.order;

import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.enums.PaymentCompanyEnum;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2021-08-16
 * @Description 门店订单|网店订单 去支付出参
 **/
@Data
@ApiModel
public class ResStoreBillingToPayDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty("订单流水交易状态,1011:交易成功 1020:交易失败 1030:支付中")
    private String tradeStatus;

    @ApiModelProperty("订单流水交易状态,1011:交易成功 1020:交易失败 1030:支付中")
    private String tradeStatusMsg;

    @ApiModelProperty(value = "订单实收金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal realAmount;

    @ExcelProperty("支付交易号")
    private String outTradeNo;

    @ApiModelProperty("支付公司，默认汇通数科，字典PaymentCompanyEnum")
    private String paymentCompany = PaymentCompanyEnum.HTSK.getCode();

    @ApiModelProperty(value = "小票流水号")
    private String ticketCode;
}