package cn.htdt.app.smartcashier.dto.response.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 开单收银页右侧边框类目列表 响应DTO
 *
 * <AUTHOR>
 * @date 2023/2/22
 **/
@Data
public class ResScRightBorderCategoryListDTO implements Serializable {

    private static final long serialVersionUID = -1L;

    @ApiModelProperty(value = "类目编号")
    private String categoryNo;

    @ApiModelProperty(value = "类目名称")
    private String categoryName;

    @ApiModelProperty(value = "类目id全路径")
    private String fullIdPath;

    @ApiModelProperty(value = "一体机排序值")
    private Integer scSortNum;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
