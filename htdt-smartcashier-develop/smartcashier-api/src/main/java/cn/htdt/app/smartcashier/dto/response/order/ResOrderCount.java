package cn.htdt.app.smartcashier.dto.response.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/7/29
 */
@Data
public class ResOrderCount implements Serializable {
    @ApiModelProperty(value = "支付类型 PaymentChannelEnum 1000 现金，1001 支付宝，1002 微信，1003 云闪付")
    private String PaymentChannel;

    @ApiModelProperty(value = "支付类型 PaymentChannelEnum 1000 现金，1001 支付宝，1002 微信，1003 云闪付")
    private String paymentChannelName;

    @ApiModelProperty(value = "交易订单数")
    private Integer tradeOrderCount;

    @ApiModelProperty(value = "交易金额")
    private BigDecimal tradeAmount;

    @ApiModelProperty(value = "整单优惠金额")
    private BigDecimal orderTotalDiscount;

    @ApiModelProperty(value = "实收金额")
    private BigDecimal realAmount;

    @ApiModelProperty(value = "退款订单数")
    private Integer refundOrderCount;

    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundAmount;
}
