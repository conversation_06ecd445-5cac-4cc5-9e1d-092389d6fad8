package cn.htdt.app.smartcashier.dto.request.content;

import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 问答列表 请求DTO
 *
 * <AUTHOR>
 * @date 2021/8/24
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ReqScQuestionsProcessListDTO extends ReqComPageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "一级编号")
    private String firstCategory;

    @ApiModelProperty(value = "标题")
    private String title;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
