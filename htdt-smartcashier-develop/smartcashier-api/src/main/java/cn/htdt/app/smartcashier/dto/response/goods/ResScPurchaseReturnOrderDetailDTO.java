package cn.htdt.app.smartcashier.dto.response.goods;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 采购退货单详情响应dto
 *
 * @author: 杨子建
 * @date: 2020-08-19
 */
@Data
public class ResScPurchaseReturnOrderDetailDTO implements Serializable {

    private static final long serialVersionUID = -2518911507591230033L;

    @ApiModelProperty(value = "退货单编码")
    private String returnCode;

    @ApiModelProperty(value = "数据来源 1001:手动添加 1002：采购商城添加")
    private String sourceType;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "退货日期")
    private LocalDateTime returnDate;

    @ApiModelProperty(value = "采购单编码")
    private String purchaseCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty("创建人")
    private String createName;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "可退商品数据")
    private List<ResScPurchaseReturnOrderGoodsDTO> returnGoodsList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
