package cn.htdt.app.smartcashier.dto.response.order.payflow;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.enums.order.TradeSceneEnum;
import cn.htdt.common.enums.order.TradeTypeEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/3/2 10:44
 */
@Data
public class ResScPayFlowListDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 商户订单号
     */
    @ApiModelProperty(value = "交易流水号")
    private String merchantOrderNo;

    /**
     * 交易场景
     */
    @ApiModelProperty("交易场景,见枚举TradeSceneEnum")
    @Converter(enumClass = TradeSceneEnum.class, fieldName = "tradeSceneName")
    private String tradeScene;

    @ApiModelProperty("交易场景名称,见枚举TradeSceneEnum")
    private String tradeSceneName;


    @ApiModelProperty("交易类型,见枚举TradeTypeEnum")
    @Converter(enumClass = TradeTypeEnum.class, fieldName = "tradeTypeName")
    private String tradeType;

    @ApiModelProperty("交易类型名称,见枚举TradeTypeEnum")
    private String tradeTypeName;
    /**
     * 交易金额
     */
    @ApiModelProperty("交易金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal totalAmount;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "交易时间")
    private String completeTime;

}
