package cn.htdt.app.smartcashier.dto.request.content;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 问答详情 请求DTO
 *
 * <AUTHOR>
 * @date 2021/8/24
 **/
@Data
public class ReqScQuestionsProcessInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "问答编号不能为空")
    @ApiModelProperty(value = "问答编号")
    private String questionsNo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
