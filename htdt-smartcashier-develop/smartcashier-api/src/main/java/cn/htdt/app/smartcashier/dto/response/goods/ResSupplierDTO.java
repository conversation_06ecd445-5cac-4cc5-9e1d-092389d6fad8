package cn.htdt.app.smartcashier.dto.response.goods;

import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 供应商列表出参Dto
 *
 * <AUTHOR>
 * @date 2021-08-09
 */
@Data
public class ResSupplierDTO implements Serializable {

    private static final long serialVersionUID = 4468354457321545475L;

    @ApiModelProperty(value = "供应商编号")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "供应商助记码")
    private String supplierHelpCode;

    @ApiModelProperty(value = "联系人手机号")
    private String linkManTelphone;

    @ApiModelProperty(value = "数据来源 1001:手动添加 1002：采购商城添加")
    private String sourceType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
