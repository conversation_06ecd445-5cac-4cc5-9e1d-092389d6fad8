package cn.htdt.app.smartcashier.dto.request.warehouse;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2020-09-02
 * @Description 仓库管理 请求DTO
 **/
@Data
public class ReqScWarehouseDTO implements Serializable {


    @ApiModelProperty(value = "仓库编号", name = "warehouseNo")
    private String warehouseNo;

    @ApiModelProperty(value = "仓库编码", name = "warehouseCode")
    private String warehouseCode;

    @ApiModelProperty(value = "是否虚拟仓库 1-否 2-是", name = "virtualWarehouseFlag")
    private Integer virtualWarehouseFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
