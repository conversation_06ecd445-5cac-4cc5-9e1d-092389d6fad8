package cn.htdt.app.smartcashier.dto.response.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 新增退货单下的商品数据出参对象
 *
 * <AUTHOR>
 * @date 2021-08-23
 **/
@Data
public class ResScAddReturnGoodsDTO implements Serializable {

    private static final long serialVersionUID = 6850811424392759422L;

    @ApiModelProperty(value = "采购单商品行编码")
    private String purchaseGoodsCode;

    @ApiModelProperty(value = "商品id")
    private String goodsNo;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "单位")
    private String purchaseUnit;

    @ApiModelProperty(value = "采购数量")
    private BigDecimal purchaseNum;

    @ApiModelProperty(value = "单价")
    private BigDecimal purchaseUnitPrice;

    @ApiModelProperty(value = "采购价")
    private BigDecimal purchasePrice;

    @ApiModelProperty(value = "是否入仓 1-否；2-是")
    private Integer warehouseType;

    @ApiModelProperty(value = "是否入仓 1-否；2-是")
    private String warehouseTypeValue;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseNo;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "总入库数量")
    private BigDecimal storageCount;

    @ApiModelProperty(value = "剩余可退货数量")
    private BigDecimal applyStorageNum;

    @ApiModelProperty(value = "退货中数量")
    private BigDecimal returningCount;

    @ApiModelProperty(value = "已退货数量")
    private BigDecimal returnedCount;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
