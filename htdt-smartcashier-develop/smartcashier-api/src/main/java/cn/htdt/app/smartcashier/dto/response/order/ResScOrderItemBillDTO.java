package cn.htdt.app.smartcashier.dto.response.order;

import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 打印票据商品的返回参数
 *
 * <AUTHOR>
 */
@Data
public class ResScOrderItemBillDTO implements Serializable {

        @ApiModelProperty(value = "商品名称")
        private String goodsName;

        @ApiModelProperty(value = "商品规格")
        private String extInfo;

        @ApiModelProperty(value = "商品数量")
        @JsonSerialize(using = BigDecimalSerializeNoZero.class)
        private BigDecimal goodsItemNum;

        @ApiModelProperty(value = "单价")
        @JsonSerialize(using = BigDecimalSerialize.class)
        private BigDecimal goodsPriceSale;

        @ApiModelProperty(value = "金额")
        @JsonSerialize(using = BigDecimalSerialize.class)
        private BigDecimal goodsItemRealAmount;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}