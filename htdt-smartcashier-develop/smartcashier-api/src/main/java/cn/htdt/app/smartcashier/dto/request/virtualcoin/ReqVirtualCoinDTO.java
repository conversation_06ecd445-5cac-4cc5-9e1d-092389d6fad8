package cn.htdt.app.smartcashier.dto.request.virtualcoin;

import cn.htdt.common.dto.request.ReqBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 修改橙豆余额请求参数
 * <AUTHOR>
 * @since 2023-01-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ReqVirtualCoinDTO extends ReqBaseDTO {
    /**
     * 粉丝编号
     */
    @ApiModelProperty(required = true, value = "粉丝编号")
    private String fanNo;

    /**
     * 账户剩余橙豆
     */
    @ApiModelProperty(required = true, value = "修改后的账户剩余橙豆")
    private BigDecimal accountRemainCoins;
}
