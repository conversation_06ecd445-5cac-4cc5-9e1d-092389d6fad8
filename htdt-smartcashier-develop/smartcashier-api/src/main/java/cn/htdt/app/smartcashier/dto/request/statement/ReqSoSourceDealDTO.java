package cn.htdt.app.smartcashier.dto.request.statement;

import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2021-08-23
 * @description 销售报表请求DTO
 **/
@Data
public class ReqSoSourceDealDTO  extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "1:今日 2:昨天 3近一周 4:近30天 5:自定义", required = true)
    private Integer dayType;

    @ApiModelProperty(value = "5:自定义-开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate countStartTime;

    @ApiModelProperty(value = "5:自定义-结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate countEndTime;

    @ApiModelProperty(value = "0:全部 1000:汇享购 1001:超级老板PC 1002:超级老板APP 1003:一体机")
    private String appChannelSource;

}
