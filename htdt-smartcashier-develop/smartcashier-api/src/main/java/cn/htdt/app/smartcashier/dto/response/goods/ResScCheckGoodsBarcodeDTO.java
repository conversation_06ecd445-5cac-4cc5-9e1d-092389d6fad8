package cn.htdt.app.smartcashier.dto.response.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 获取商品详情 请求DTO
 *
 * <AUTHOR>
 * @date 2021/8/13
 **/
@Data
public class ResScCheckGoodsBarcodeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "称重商品标识。1：否，2：是")
    private Integer weighGoodsFlag;

    @ApiModelProperty(value = "商品条码")
    private String barcode;

    @ApiModelProperty(value = "零售价")
    private BigDecimal retailPrice;
}
