package cn.htdt.app.smartcashier.dto.response.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 采购退货单商品信息响应dto
 *
 * @author: 杨子建
 * @date: 2021-08-19
 */
@Data
public class ResScPurchaseReturnOrderGoodsDTO implements Serializable {

    private static final long serialVersionUID = -4344797317347697753L;

    @ApiModelProperty(value = "退货单商品行编码")
    private String returnGoodsCode;

    @ApiModelProperty(value = "商品id")
    private String goodsNo;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "单位")
    private String purchaseUnit;

    @ApiModelProperty(value = "单价")
    private BigDecimal purchaseUnitPrice;

    @ApiModelProperty(value = "是否入仓 1-否;2-是")
    private Integer warehouseType;

    @ApiModelProperty(value = "是否入仓 1-否;2-是")
    private String warehouseTypeValue;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseNo;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "已退货数量")
    private BigDecimal returnedCount;

    @ApiModelProperty(value = "本次退货数量")
    private BigDecimal returnRequestCount;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
