package cn.htdt.app.smartcashier.dto.response.order;

import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "打印订单, 订单商品行数据")
public class ResScPrintOrderItemsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品购买数量")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal goodsItemNum;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "扩展信息，以json形式存储")
    private String extInfo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}