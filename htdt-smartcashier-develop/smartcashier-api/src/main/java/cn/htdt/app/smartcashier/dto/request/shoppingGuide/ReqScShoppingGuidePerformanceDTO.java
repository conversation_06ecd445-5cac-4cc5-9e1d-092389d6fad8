package cn.htdt.app.smartcashier.dto.request.shoppingGuide;

import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/3/30 14:09
 */
@Data
public class ReqScShoppingGuidePerformanceDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "1:今日 2:昨天 3近一周 4:近30天 5:自定义", required = true)
    private Integer dayType;

    @ApiModelProperty(value = "5:自定义-开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate countStartTime;

    @ApiModelProperty(value = "5:自定义-结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate countEndTime;

    @ApiModelProperty(value = "导购员编码")
    private String shoppingGuideNo;
}
