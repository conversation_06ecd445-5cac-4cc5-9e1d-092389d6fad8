package cn.htdt.app.smartcashier.dto.response.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 品牌查询响应DTO
 *
 * <AUTHOR>
 * @date 2020/9/29
 **/
@Data
public class ResScBrandInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "品牌编码")
    private String brandNo;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "英文名")
    private String englishName;

    @ApiModelProperty(value = "商标url")
    private String trademarkUrl;

    @ApiModelProperty(value = "数据来源类型(默认1002。1001:同步MDM、1002:平台自创、1003:商家自创、1004:店铺自创)")
    private String dataSourceType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
