package cn.htdt.app.smartcashier.dto.response.order;

import cn.htdt.app.smartcashier.dto.request.market.ReqScGoodsVipPriceDTO;
import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单明细参数返回
 *
 * <AUTHOR>
 */
@Data
public class ResScOrderItemDTO implements Serializable {

    private static final long serialVersionUID = -8298093504539938391L;

    @ApiModelProperty("商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "商品类型 1001-实物商品;1002-虚拟商品;1003-服务商品（仅供平台使用）;1004-称重商品")
    private String goodsType;

    @ApiModelProperty("商品类目")
    private String categoryName;

    @ApiModelProperty("行编号")
    private String itemNo;

    @ApiModelProperty("父编号")
    private String parentItemNo;

    @ApiModelProperty("商品品牌")
    private String brandName;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("订单行状态")
    private String itemStatus;

    @ApiModelProperty("商品条码")
    private String barCode;

    @ApiModelProperty("规格属性")
    private String extInfo;

    @ApiModelProperty("发货仓库")
    private String warehouseName;

    @ApiModelProperty("已发货数量")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal hasDelivery = BigDecimal.ZERO;

    @ApiModelProperty("销售单价")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal goodsPriceSale;

    @ApiModelProperty(value = "商品销售单价")
    private BigDecimal productPriceFinal;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty("订购数量")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal goodsItemNum;

    @ApiModelProperty(value = "商品重量（称重商品需求新增）")
    private String goodsWeight;

    @ApiModelProperty("销售总价")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal goodsTotalAmount;

    @ApiModelProperty("优惠金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal discountAmount;

    @ApiModelProperty("成交价")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal goodsItemRealAmount;

    @ApiModelProperty("已退货数量")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal refundedGoodsNum;

    @ApiModelProperty("菜品类型 1001:主菜 1002:加料")
    private String foodsType;

    @ApiModelProperty("餐饮订单行备注")
    private String foodsRemarks;

    @ApiModelProperty("餐饮需求前端处理用")
    private String joinGoodsNo;

    @ApiModelProperty("餐饮需求前端处理用")
    private String goodsSequence;

    @ApiModelProperty(value = "商品参与的特惠促销活动信息")
    private ReqScGoodsVipPriceDTO goodsVipPriceDTO;

    @ApiModelProperty(value = "商品参与的会员价活动信息")
    private ReqScGoodsVipPriceDTO goodsMemberPriceDTO;

    /**
     * 商品标签，字典GoodsLabelEnum
     * 多个则用“,”分割
     */
    @ApiModelProperty(value = "商品标签，字典GoodsLabelEnum多个则用“,”分割")
    private String goodsLabel;

}
