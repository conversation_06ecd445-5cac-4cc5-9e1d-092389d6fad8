package cn.htdt.app.smartcashier.dto.response.order;

import cn.htdt.app.smartcashier.dto.request.market.ReqScGoodsVipPriceDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ResOrderItemsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "扩展信息，以json形式存储")
    private String extInfo;

    @ApiModelProperty(value = "商品总金额")
    private BigDecimal goodsItemAmount;

    @ApiModelProperty(value = "商品应收总金额")
    private BigDecimal goodsItemShouldAmount;

    @ApiModelProperty(value = "商品实付金额")
    private BigDecimal goodsItemRealAmount;

    @ApiModelProperty(value = "商品购买数量")
    private BigDecimal goodsItemNum;

    @ApiModelProperty(value = "商品重量（称重商品需求新增）")
    private String goodsWeight;

    @ApiModelProperty(value = "商品销售单价")
    private BigDecimal productPriceFinal;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "商品类型 1001-实物商品;1002-虚拟商品;1003-服务商品（仅供平台使用）;1004-称重商品")
    private String goodsType;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "订单明细编号")
    private String orderItemNo;


    @ApiModelProperty(value = "商品图片URL")
    private String goodsPicPath;

    @ApiModelProperty(value = "商品图片角标URL")
    private String superscriptPictureUrl;

    @ApiModelProperty("行编号")
    private String itemNo;

    @ApiModelProperty("父编号")
    private String parentItemNo;

    @ApiModelProperty("菜品类型 1001:主菜 1002:加料")
    private String foodsType;

    @ApiModelProperty("餐饮订单行备注")
    private String foodsRemarks;

    @ApiModelProperty("餐饮需求前端处理用")
    private String joinGoodsNo;

    @ApiModelProperty("餐饮需求前端处理用")
    private String goodsSequence;

    @ApiModelProperty(value = "商品参与的特惠促销活动信息")
    private ReqScGoodsVipPriceDTO goodsVipPriceDTO;

    @ApiModelProperty(value = "商品参与的会员价活动信息")
    private ReqScGoodsVipPriceDTO goodsMemberPriceDTO;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}