package cn.htdt.app.smartcashier.dto.response.order;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.dto.annon.PrivateData;
import cn.htdt.common.dto.enums.DecipherScene;
import cn.htdt.common.dto.enums.HandlerPolicy;
import cn.htdt.common.dto.request.ReqComPageDTO;
import cn.htdt.common.dto.response.ResEnumsDTO;
import cn.htdt.common.dto.response.ResOrderMainButtonDTO;
import cn.htdt.common.enums.*;
import cn.htdt.common.enums.goods.DeliveryWayEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单响应DTO
 *
 *
 * 202503东启修改
 * 组合支付
 */
@Data
public class ResScOrderDTO extends ReqComPageDTO implements Serializable {


    /**
     * 组合支付 剩余扫描金额 待付金额 扫描成功之后归零
     */
    @ApiModelProperty(value = "组合支付 剩余扫描金额 待付金额 扫描成功之后归零", required = false)
    private BigDecimal pendingAmount;

    /**
     * 组合支付  -收到金额（找零使用）
     */
    @ApiModelProperty(value = "组合支付  -收到金额（找零使用）", required = false)
    private BigDecimal changePrice;

    /**
     * 组合支付状态 1009  默认是空
     */
    @ApiModelProperty(value = "组合支付状态 1009  默认是空)", required = false)
    private String payStatu;


    /**
     * 序列化
     */
    private static final long serialVersionUID = -3197889257683106301L;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "父订单编号")
    private String parentOrderNo;

    @ApiModelProperty(value = "订单类型")
    @Converter(enumClass = OrderTypeEnum.class, fieldName = "orderTypeName", enumField = "type")
    private String orderType;

    @ApiModelProperty(value = "订单类型名称")
    private String orderTypeName;

    @ApiModelProperty(value = "订单状态 1010:待支付  1030:待确认 1050:待发货 1060:待收货  1061:部分发货  1999:交易成功  9000:交易关闭")
    @Converter(enumClass = OrderStatusEnum.class, fieldName = "orderStatusName")
    private String orderStatus;

    @ApiModelProperty(value = "订单状态描述")
    private String orderStatusName;

    @ApiModelProperty(value = "订单支付状态 1000:待支付  1001:部分支付  1002:已支付")
    @Converter(enumClass = OrderPaymentStatusEnum.class, fieldName = "orderPaymentStatusName")
    private String orderPaymentStatus;

    @ApiModelProperty(value = "订单支付状态描述")
    private String orderPaymentStatusName;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "店铺编号")
    private String storeNo;

    @ApiModelProperty(value = "分销店对应商户编号")
    private String distributionMerchantNo;

    @ApiModelProperty(value = "分销店对应商户名称")
    private String distributionMerchantName;

    @ApiModelProperty(value = "分销店铺编号-对应老系统的orgId")
    private String distributionStoreNo;

    @ApiModelProperty(value = "分销店铺名称")
    private String distributionStoreName;

    @ApiModelProperty(value = "下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime orderCreateTime;

    @ApiModelProperty(value = "下单人账号")
    private String buyerNo;

    @ApiModelProperty(value = "下单人手机号")
    private String buyerMobile;

    @ApiModelProperty("下单人手机号-加密")
    @PrivateData(handlerPolicy = HandlerPolicy.DECIPHER, decipherScene= DecipherScene.COMMON, fieldName = "buyerMobile")
    private String dsBuyerMobile;

    @ApiModelProperty(value = "下单人姓名")
    private String buyerName;

    @ApiModelProperty(value = "收货人姓名")
    private String goodReceiverName;

    @ApiModelProperty(value = "收货人手机号")
    private String goodReceiverMobile;

    @ApiModelProperty(value = "收货人手机号-加密")
    @PrivateData(handlerPolicy = HandlerPolicy.DECIPHER, decipherScene= DecipherScene.COMMON, fieldName = "goodReceiverMobile")
    private String dsGoodReceiverMobile;

    @ApiModelProperty(value = "收货人地址(完整)")
    private String goodReceiverWholeAddress;

    @ApiModelProperty(value = "收货人地址")
    private String goodReceiverAddress;

    @ApiModelProperty("收货人地址-加密")
    @PrivateData(handlerPolicy = HandlerPolicy.DECIPHER, decipherScene= DecipherScene.COMMON, fieldName = "goodReceiverAddress")
    private String dsGoodReceiverAddress;

    @ApiModelProperty(value = "开单人账号")
    private String sellerNo;

    @ApiModelProperty(value = "开单人姓名")
    private String sellerName;

    @ApiModelProperty(value = "配送方式 1000:门店自提 1101:店铺配送 1102:快递配送 1200:自提点自提")
    @Converter(enumClass = DeliveryWayEnum.class, fieldName = "orderDeliveryWayName")
    private String orderDeliveryWay;

    @ApiModelProperty(value = "配送方式名称")
    private String orderDeliveryWayName;

    @ApiModelProperty(value = "粉丝端+分销店维度配送方式类型 1100:配送  1000:门店自提 1200:自提点自提")
    @Converter(enumClass = DeliveryWayEnum.class, fieldName = "distributionOrderDeliveryWayName")
    private String distributionOrderDeliveryWay;

    @ApiModelProperty(value = "粉丝端+分销店维度配送方式类型名称")
    private String distributionOrderDeliveryWayName;

    @ApiModelProperty(value = "订单总金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal productAmount;

    @ApiModelProperty(value = "订单应付金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal shouldAmount;

    @ApiModelProperty(value = "订单实付金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal realAmount;

    @ApiModelProperty(value = "订单欠款金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal arrearsAmount;

    @ApiModelProperty(value = "是否欠款 1 否 2 是")
    private Integer arrearsFlag;

    @ApiModelProperty(value = "订单渠道来源")
    @Converter(enumClass = OrderChannelSourceEnum.class, fieldName = "orderChannelSourceName", enumField = "type")
    private String orderChannelSource;

    @ApiModelProperty(value = "订单渠道来源名称")
    private String orderChannelSourceName;

    @ApiModelProperty(value = "收货人国家code")
    private String goodReceiverCountryCode;

    @ApiModelProperty(value = "收货人省份code")
    private String goodReceiverProvinceCode;

    @ApiModelProperty(value = "收货人省份")
    private String goodReceiverProvince;

    @ApiModelProperty(value = "收货人城市code")
    private String goodReceiverCityCode;

    @ApiModelProperty(value = "收货人城市")
    private String goodReceiverCity;

    @ApiModelProperty(value = "收货人地区code")
    private String goodReceiverCountyCode;

    @ApiModelProperty(value = "收货人地区")
    private String goodReceiverCounty;

    @ApiModelProperty(value = "收货人四级区域code")
    private String goodReceiverAreaCode;

    @ApiModelProperty(value = "收货人四级区域")
    private String goodReceiverArea;

    @ApiModelProperty(value = "商品名称")
    private String goodsNameConcat;

    @ApiModelProperty(value = "商户订单号")
    private String outTradeNoConcat;

    /**
     * 1000 汇享购支付
     * 1001 门店支付
     */
    @ApiModelProperty(value = "支付方式")
    private String orderPaymentMethod;

    @ApiModelProperty(value = "订单商品总件数")
    private BigDecimal orderTotalNum;

    @ApiModelProperty(value = "订单标识，1 普通，2 云池，3 分销，9 中奖 字典OrderFlagEnum")
    @Converter(enumClass = OrderFlagEnum.class, fieldName = "orderFlagName")
    private Integer orderFlag;

    @ApiModelProperty(value = "订单标识名称")
    private String orderFlagName;

    @ApiModelProperty(value = "券是否自用 1否 2是")
    private Integer selfUseFlag;

    @ApiModelProperty(value = "订单备注(用户)")
    private String orderRemarkBuyer;

    @ApiModelProperty(value = "餐饮订单整单备注")
    private String wholeFoodsRemarks;

    @ApiModelProperty(value = "订单行业 1001：普通行业 1002：餐饮行业")
    private String orderIndustry;

    @ApiModelProperty(value = "评论状态 1:未评论 2:已评论")
    private Integer commentStatus;

    @ApiModelProperty(value = "最后支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime orderPaymentLastDate;

    @ApiModelProperty(value = "最后确认收货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime orderReceiptLastDate;

    @ApiModelProperty(value = "订单详情商品列表")
    List<ResOrderItemsDTO> resOrderItemsDTOS;

    @ApiModelProperty("橙豆")
    private BigDecimal virtualCoinsDiscount;

    @ApiModelProperty(value = "按钮列表")
    List<ResOrderMainButtonDTO> buttonDTOS;

    @ApiModelProperty(value = "是否发生过售后标识 1：未发生 2：已发生")
    private Integer returnFlag;

    @ApiModelProperty(value = "流水号（暂用于餐饮小票流水号）")
    private String serialNumber;

    //**********最新一条快递信息start***********
    /**
     * 快递单号
     */
    @ApiModelProperty(value = "最新快递单号")
    private String deliveryNumber;

    /**
     * 快递描述信息
     */
    @ApiModelProperty(value = "最新快递描述信息")
    private String remark;

    /**
     * 快递记录对应的时间
     */
    @ApiModelProperty(value = "最新快递记录对应的时间")
    private LocalDateTime logisticsTime;

    /**
     * 快递单当前签收状态，包括0在途中、1已揽收、2疑难、3已签收、4退签、5同城派送中、6退回、7转单等7个状态，其中4-7需要另外开通才有效  (快递100接口提供)
     */
    @ApiModelProperty(value = "最新快递单当前签收状态，包括0在途中、1已揽收、2疑难、3已签收、4退签、5同城派送中、6退回、7转单等7个状态，其中4-7需要另外开通才有效  (快递100接口提供)")
    private String status;

    /**
     * 配送方式枚举
     */
    private List<ResEnumsDTO> deliveryWayEnums;

    @ApiModelProperty(value = "显示按钮集合 select：查看 confirm：确认 modify：修改 print：打印 send：发货 return发起售后")
    private List<String> buttons;

    @ApiModelProperty(value = "售后标签")
    private String returnLabel;

    @ApiModelProperty(value = "小票流水号")
    private String ticketCode;

    @ApiModelProperty("订单备注(商家自己看的)")
    private String orderRemarkMerchant;

    @ApiModelProperty(value = "导购员姓名")
    private String shoppingGuideName;

    //**********最新一条快递信息end***********
}
