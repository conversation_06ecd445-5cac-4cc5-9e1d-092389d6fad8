package cn.htdt.app.smartcashier.dto.response.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 20230928蛋品-吴鑫鑫-商品管理-商品创建
 * 功能描述: 多单位商品返回dto
 * @author: 吴鑫鑫
 * @date: 2023-08-15
 */
@Data
public class ResScMultiUnitGoodsDTO implements Serializable {

    @ApiModelProperty(value = "商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "单位类型, 1计件，2称重，枚举:CalcUnitTypeEnum")
    private String calcUnitType;

    @ApiModelProperty(value = "计量单位ID")
    private String calculationUnitNo;

    @ApiModelProperty(value = "计量单位名称")
    private String calculationUnitName;

    @ApiModelProperty(value = "辅计量单位id")
    private String assistCalculationUnitNo;

    @ApiModelProperty(value = "辅计量单位名称")
    private String assistCalculationUnitName;

    @ApiModelProperty(value = "主计量单位对应关系数值")
    private BigDecimal mainUnitNum;

    @ApiModelProperty(value = "辅计量单位对应关系数值")
    private BigDecimal assistUnitNum;

    @ApiModelProperty(value = "条形码")
    private String barcode;

    @ApiModelProperty(value = "零售价")
    private BigDecimal retailPrice;

    @ApiModelProperty(value = "限价")
    private BigDecimal limitPrice;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
