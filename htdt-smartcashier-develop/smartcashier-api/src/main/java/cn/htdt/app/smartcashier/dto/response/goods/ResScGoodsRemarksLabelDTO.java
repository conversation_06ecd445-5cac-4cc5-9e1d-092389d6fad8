package cn.htdt.app.smartcashier.dto.response.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 商品备注标签响应DTO
 *
 * <AUTHOR>
 * @date 2022-03-28
 */
@Data
public class ResScGoodsRemarksLabelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "标签编号")
    private String labelNo;

    @ApiModelProperty(value = "标签内容")
    private String labelContent;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
