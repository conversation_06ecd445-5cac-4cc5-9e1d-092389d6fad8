package cn.htdt.app.smartcashier.dto.response.order.payflow;

import cn.htdt.common.dto.BigDecimalSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 支付信息
 *
 * <AUTHOR>
 */
@Data
public class ResScOrderPayInfoDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = -5226198887035909407L;

    @ApiModelProperty(value = "订单应收金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal shouldAmount;

    @ApiModelProperty(value = "订单实收金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal realAmount;

    @ApiModelProperty(value = "订单欠款金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal arrearsAmount;

    @ApiModelProperty(value = "订单交易流水")
    private String outTradeNo;
}
