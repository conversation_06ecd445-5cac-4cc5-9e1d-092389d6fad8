package cn.htdt.app.smartcashier.dto.response.goods;

import cn.htdt.common.dto.BigDecimalSerialize;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 采购单商品已入库列表
 *
 * <AUTHOR>
 * @date 2021-08-18
 */
@Data
public class ResScPurchaseOrderAlreadyStorageDTO implements Serializable {

    private static final long serialVersionUID = 7290774092947586973L;

    @ApiModelProperty(value = "采购单编码")
    private String purchaseCode;

    @ApiModelProperty(value = "采购单商品行编码")
    private String purchaseGoodsCode;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseNo;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "入库时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "商品编码")
    private String goodsNo;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "是否入仓商品：1-否;2-是")
    private Integer warehouseGoodsFlag;

    @ApiModelProperty(value = "是否入仓商品：1-否;2-是")
    private Integer warehouseGoodsFlagStr;

    @ApiModelProperty(value = "是否入仓:1-否;2-是")
    private Integer warehouseFlag;

    @ApiModelProperty(value = "是否入仓:1-否;2-是")
    private String warehouseFlagStr;

    @ApiModelProperty(value = "计量单位名称")
    private String calculationUnitName;

    @ApiModelProperty(value = "采购单位")
    private String purchaseUnit;

    @ApiModelProperty(value = "是否有辅计量单位：1-否;2-是")
    private Integer standardFlag;

    @ApiModelProperty(value = "辅计量单位id")
    private String assistCalculationUnitNo;

    @ApiModelProperty(value = "辅计量单位名称")
    private String assistCalculationUnitName;

    @ApiModelProperty(value = "主计量单位对应关系数值")
    private BigDecimal mainUnitNum;

    @ApiModelProperty(value = "辅计量单位对应关系数值")
    private BigDecimal assistUnitNum;

    @ApiModelProperty(value = "转换率(辅转主)")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "采购数量")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal purchaseNum;

    @ApiModelProperty(value = "操作库存数量")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal stockNum;

    @ApiModelProperty(value = "是否启用串码(1:否 2:是)，当启用串码维护时，串码列展示 维护串码")
    private Integer imeiFlag;

    @ApiModelProperty(value = "维护的串码数")
    private Integer imeiNum;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }


}
