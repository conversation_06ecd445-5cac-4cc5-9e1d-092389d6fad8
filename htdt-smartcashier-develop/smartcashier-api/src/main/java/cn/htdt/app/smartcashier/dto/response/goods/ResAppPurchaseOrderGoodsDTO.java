package cn.htdt.app.smartcashier.dto.response.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 采购单商品详情返回结果Dto
 *
 * <AUTHOR>
 * @date 2021-08-11
 */
@Data
public class ResAppPurchaseOrderGoodsDTO implements Serializable {

    private static final long serialVersionUID = 2807809197640040634L;

    @ApiModelProperty(value = "商品编码")
    private String goodsNo;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "是否入仓(1:否 2:是)")
    private Integer warehouseFlag;

    @ApiModelProperty(value = "是否入仓字符串(1:否 2:是)")
    private String warehouseFlagStr;

    @ApiModelProperty(value = "采购数量")
    private BigDecimal purchaseNum;

    @ApiModelProperty(value = "采购单位")
    private String purchaseUnit;

    @ApiModelProperty(value = "采购金额")
    private BigDecimal purchasePrice;

    @ApiModelProperty(value = "已入库数量")
    private BigDecimal storageCount;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
