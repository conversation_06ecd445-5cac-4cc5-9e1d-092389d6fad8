package cn.htdt.app.smartcashier.dto.response.order;

import cn.htdt.common.dto.request.ReqBaseDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 打印订单响应DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "打印订单响应实体")
public class ResScOrderPrintDTO extends ReqBaseDTO {
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "父订单编号")
    private String parentOrderNo;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "店铺编号")
    private String storeNo;

    @ApiModelProperty(value = "下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime orderCreateTime;

    @ApiModelProperty(value = "订单商品总件数")
    private BigDecimal orderTotalNum;

    @ApiModelProperty(value = "小票流水号")
    private String ticketCode;

    @ApiModelProperty(value = "订单详情商品列表")
    private List<ResScPrintOrderItemsDTO> resOrderItemsDTOS;
}
