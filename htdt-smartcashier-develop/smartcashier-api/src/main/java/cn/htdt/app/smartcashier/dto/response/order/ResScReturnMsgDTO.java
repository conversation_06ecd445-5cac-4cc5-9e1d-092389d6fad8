package cn.htdt.app.smartcashier.dto.response.order;

import cn.htdt.common.enums.constants.NumConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 售后校验返回（关联售后）
 *
 * <AUTHOR>
 */
@Data
public class ResScReturnMsgDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = -928662700660381520L;

    @ApiModelProperty(value = "返回的错误状态码: 0=成功；1=均被使用；2=部分被使用；0=成功；1=失败")
    private Integer status = NumConstant.ZERO;

    @ApiModelProperty(value = "失败编码")
    private Integer errorCode;

    @ApiModelProperty(value = "错误信息")
    private String errorMsg;

}
