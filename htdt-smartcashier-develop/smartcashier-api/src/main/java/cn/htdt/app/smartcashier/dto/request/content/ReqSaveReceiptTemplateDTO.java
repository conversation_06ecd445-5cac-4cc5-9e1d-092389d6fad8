package cn.htdt.app.smartcashier.dto.request.content;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/9/23 14:21
 */
@Data
public class ReqSaveReceiptTemplateDTO  implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "行业 1001:普通行业 1002:餐饮行业")
    private String industryNo;

    @ApiModelProperty(value = "模板内容json")
    private String templateJson;

    @ApiModelProperty(value = "应用场景，默认1前台，2后厨")
    private Integer sceneType;

    @ApiModelProperty(value = "打印方式，默认1分行打印，2已聚合打印")
    private Integer printType;

    @ApiModelProperty(value = "使用标识，默认1未使用，2已使用")
    private Integer useFlag;
}
