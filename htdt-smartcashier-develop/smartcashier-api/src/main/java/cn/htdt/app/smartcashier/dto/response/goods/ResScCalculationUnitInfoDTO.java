package cn.htdt.app.smartcashier.dto.response.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 计量单位信息
 *
 * <AUTHOR>
 * @date 2021-08-17
 */
@Data
public class ResScCalculationUnitInfoDTO implements Serializable {

    @ApiModelProperty(value = "计量单位id")
    private String calculationUnitNo;

    @ApiModelProperty(value = "计量单位名称")
    private String calculationUnitName;

    @ApiModelProperty(value = "计量单位符号")
    private String calculationUnitSymbol;

    @ApiModelProperty(value = "数据来源类型(1001:MDM同步 1002:平台自建)")
    private String dataSourceType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
