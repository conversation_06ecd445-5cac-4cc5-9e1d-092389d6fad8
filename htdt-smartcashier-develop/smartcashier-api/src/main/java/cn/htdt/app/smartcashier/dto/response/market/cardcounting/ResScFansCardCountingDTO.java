package cn.htdt.app.smartcashier.dto.response.market.cardcounting;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 计次卡活动保存DTO
 *
 * <AUTHOR>
 * @date 2022-09-21
 */
@Data
public class ResScFansCardCountingDTO extends ResScCardCountingDTO {

    @ApiModelProperty(value = "粉丝计次卡编号")
    private String cardFansNo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
