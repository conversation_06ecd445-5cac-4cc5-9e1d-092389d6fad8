package cn.htdt.app.smartcashier.dto.request.refundorder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 退款异步回调入参
 *
 * <AUTHOR>
 */
@Data
public class ReqScRefundNotifyDto implements Serializable {

    /**
     * 序列化
     */

    private static final long serialVersionUID = 1L;

    /**
     * 商户订单号
     */
    @ApiModelProperty(name = "商户订单号")
    private String merchOrderNo;

    /**
     * 原商户订单号
     */
    @ApiModelProperty(name = "原商户订单号")
    private String origMerchOrderNo;

    /**
     * 支付交易号
     */
    @ApiModelProperty(name = "支付交易号")
    private String tradeNo;

    /**
     * 交易金额
     */
    @ApiModelProperty(name = "交易金额")
    private String amount;

    /**
     * 交易类型
     */
    @ApiModelProperty(name = "交易类型")
    private String tradeType;

    /**
     * 交易状态
     */
    @ApiModelProperty(name = "交易状态")
    private String tradeStatus;

    /**
     * 响应状态
     */
    @ApiModelProperty(name = "响应状态")
    private String resultCode;

    /**
     * 响应消息
     */
    @ApiModelProperty(name = "响应消息")
    private String resultMessage;

    /**
     * 会话参数
     */
    @ApiModelProperty(name = "会话参数")
    private String context;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
