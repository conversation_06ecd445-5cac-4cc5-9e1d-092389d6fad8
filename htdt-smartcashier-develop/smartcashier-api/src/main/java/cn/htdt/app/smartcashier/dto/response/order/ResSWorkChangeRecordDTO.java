package cn.htdt.app.smartcashier.dto.response.order;

import cn.htdt.app.smartcashier.dto.response.market.ResScVirtualCoinRecord4WorkChangeDto;
import cn.htdt.common.dto.request.ReqBaseDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 交接班记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ResSWorkChangeRecordDTO extends ReqBaseDTO {

    private static final long serialVersionUID = 1L;

    private BigInteger id;

    @ApiModelProperty(value = "用户编号")
    private String userNo;

    @ApiModelProperty(value = "用户姓名")
    private String userName;

    @ApiModelProperty(value = "电话")
    private String userPhone;

    @ApiModelProperty(value = "营收总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "现金总额")
    private BigDecimal cashAmount;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "交接班状态 1：进行中 2：已完成")
    private Integer recordStatus;

    @ApiModelProperty(value = "店铺编号，对应orgId",required = true)
    private String storeNo;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "店铺地址")
    private String detailAddress;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "收银台统计订单信息")
    List<ResOrderCount> orderCounts;

    @ApiModelProperty(value = "收银台统计橙豆信息")
    private ResScVirtualCoinRecord4WorkChangeDto virtualCoinInfo;

}
