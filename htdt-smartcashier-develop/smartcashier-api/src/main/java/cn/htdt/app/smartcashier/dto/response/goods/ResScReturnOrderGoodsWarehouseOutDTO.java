package cn.htdt.app.smartcashier.dto.response.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 采购退货单出库商品数据响应DTO
 *
 * <AUTHOR>
 * @date 2021-08-23
 */
@Data
public class ResScReturnOrderGoodsWarehouseOutDTO implements Serializable {

    private static final long serialVersionUID = -4263794515653319573L;

    @ApiModelProperty(value = "退货单商品行编码")
    private String returnGoodsCode;

    @ApiModelProperty(value = "退货单编码")
    private String returnCode;

    @ApiModelProperty(value = "商品编码")
    private String goodsNo;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "采购单位")
    private String purchaseUnit;

    @ApiModelProperty(value = "是否入仓")
    private String warehouseTypeValue;

    @ApiModelProperty(value = "采购数量")
    private BigDecimal purchaseNum;

    @ApiModelProperty(value = "本次退货数量")
    private BigDecimal returnRequestCount;

    @ApiModelProperty(value = "已退货数量")
    private BigDecimal returnedCount;

    @ApiModelProperty(value = "待出库数量")
    private BigDecimal waitingWarehouseOut;

    @ApiModelProperty(value = "出库数量")
    private BigDecimal warehouseOutCount;

    @ApiModelProperty(value = "采购单价")
    private BigDecimal purchaseUnitPrice;

    @ApiModelProperty(value = "可售库存")
    private BigDecimal availableStockNum;

    @ApiModelProperty(value = "仓库编号")
    private String warehouseNo;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
