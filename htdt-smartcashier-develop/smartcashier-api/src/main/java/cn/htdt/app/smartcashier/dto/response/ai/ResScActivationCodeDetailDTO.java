package cn.htdt.app.smartcashier.dto.response.ai;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 一体机激活AI识别, 需要的数据
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
public class ResScActivationCodeDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "激活码")
    private String activationCode;

    @ApiModelProperty(value = "激活AI识别时的鉴权信息")
    private String sdkAppAuth;

    @ApiModelProperty(value = "激活AI识别时的账号")
    private String sdkAppUser;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
