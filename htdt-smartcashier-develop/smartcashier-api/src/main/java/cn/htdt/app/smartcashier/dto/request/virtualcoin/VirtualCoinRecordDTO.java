package cn.htdt.app.smartcashier.dto.request.virtualcoin;

import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <p>
 * 橙豆收入支出记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class VirtualCoinRecordDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 交易类型类型，字典, 参考枚举: VirtualCoinTradeTypeEnum
     */
    @ApiModelProperty(value = "交易类型类型 1001:购买 1002:消费 1003:退回, 2001: 手动增加, 2002: 手动扣减")
    private String tradeType;

    /**
     * 手机
     */
    @ApiModelProperty(value = "手机号")
    private String mobile;

    /**
     * 粉丝备注名
     */
    @ApiModelProperty(value = "粉丝备注名")
    private String storeFanName;

    /**
     * 商家编号
     */
    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    /**
     * 门店编号，对应orgId
     */
    @ApiModelProperty(value = "门店编号")
    private String storeNo;

    @ApiModelProperty(value = "1:今日 2:昨天 3近一周 4:近30天 5:自定义", required = true)
    private Integer dayType;

    @ApiModelProperty(value = "5:自定义-开始时间")
    private LocalDate countStartTime;

    @ApiModelProperty(value = "5:自定义-结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate countEndTime;

    @ApiModelProperty(value = "0:全部 1000:汇享购 1001:超级老板PC 1002:超级老板APP 1003:一体机")
    private String appChannelSource;

}
