package cn.htdt.app.smartcashier.dto.response.user;


import cn.htdt.common.dto.annon.PrivateData;
import cn.htdt.common.dto.enums.DecipherScene;
import cn.htdt.common.dto.enums.HandlerPolicy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-20
 */
@Data
@ApiModel(description = "店铺地址实体")
public class ResScStoreAddressDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(notes = "姓名")
    private String name;

    @ApiModelProperty(notes = "手机号")
    private String phone;

    @ApiModelProperty(notes = "手机号-加密")
    @PrivateData(handlerPolicy = HandlerPolicy.DECIPHER, decipherScene= DecipherScene.SMARTCASHIER_MY_CASHIER, fieldName = "phone")
    private String dsPhone;

    /**
     * 店铺编号
     */
    @ApiModelProperty(notes = "店铺编号")
    private String storeNo;

    /**
     * 店铺名称
     */
    @ApiModelProperty(notes = "店铺名称")
    private String storeName;

    /**
     * 省编号
     */
    @ApiModelProperty(notes = "省编号")
    private String provinceCode;

    /**
     * 省名
     */
    @ApiModelProperty(notes = "省名")
    private String provinceName;

    /**
     * 市编号
     */
    @ApiModelProperty(notes = "市编号")
    private String cityCode;

    /**
     * 市名
     */
    @ApiModelProperty(notes = "市名")
    private String cityName;

    /**
     * 区域编号
     */
    @ApiModelProperty(notes = "区域编号")
    private String regionCode;

    /**
     * 区域名称
     */
    @ApiModelProperty(notes = "区域名称")
    private String regionName;

    /**
     * 详细地址
     */
    @ApiModelProperty(notes = "详细地址")
    private String detailAddress;

    @ApiModelProperty(notes = "详细地址-加密")
    @PrivateData(handlerPolicy = HandlerPolicy.DECIPHER, decipherScene= DecipherScene.SMARTCASHIER_MY_CASHIER, fieldName = "detailAddress")
    private String dsDetailAddress;

    /**
     * 街道编号
     */
    @ApiModelProperty(notes = "街道编号")
    private String streetCode;

    /**
     * 街道名称
     */
    @ApiModelProperty(notes = "街道名称")
    private String streetName;

    /**
     * 地址编号
     */
    @ApiModelProperty(notes = "地址编号")
    private String addressNo;

    /**
     * 地址类型 0 退货地址 1自提地址
     */
    @ApiModelProperty(notes = "地址类型 0 退货地址 1自提地址")
    private Integer type;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
