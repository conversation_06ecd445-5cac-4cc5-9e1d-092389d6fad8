package cn.htdt.app.smartcashier.dto.response.user;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.enums.user.MemberOpeningMethodEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会员响应响应实体类
 *
 * <AUTHOR>
 * @since 2023-04-23
 */
@Data
@ApiModel(description = "会员列表响应实体类")
public class ResScFanMemberInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 会员记录no
     */
    @ApiModelProperty(notes = "会员开通记录编号")
    private String memberRecordNo;

    @ApiModelProperty(notes = "粉丝编号")
    private String fanNo;

    @ApiModelProperty(notes = "店铺编号")
    private String storeNo;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "店铺图片")
    private String storePhoto;

    /**
     * 开通方式：1001-免费;1002-付费
     * 参考枚举: MemberOpeningMethodEnum
     */
    @ApiModelProperty(value = "开通方式：1001-免费;1002-付费")
    @Converter(enumClass = MemberOpeningMethodEnum.class, fieldName = "openingMethodValue")
    private String openingMethod;

    @ApiModelProperty(value = "开通方式值")
    private String openingMethodValue;

    @ApiModelProperty(notes = "开通时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime openingTime;

    /**
     * 有效开始日期
     */
    @ApiModelProperty(value = "有效开始日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDateTime effectiveTime;

    /**
     * 有效结束日期
     */
    @ApiModelProperty(value = "有效结束日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDateTime invalidTime;

    /**
     * 有效期限, 参考枚举: MemberValidityPeriodEnum
     */
    @ApiModelProperty(notes = "1001-30天; 1002-60天; 1003-90天; 1004-一年; 1005-终身")
    private String validityPeriod;

    @ApiModelProperty(notes = "付费金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal paymentAmount;

    @ApiModelProperty(notes = "等级编号")
    private String memberLevelNo;

    @ApiModelProperty(notes = "等级名称")
    private String memberLevelName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
