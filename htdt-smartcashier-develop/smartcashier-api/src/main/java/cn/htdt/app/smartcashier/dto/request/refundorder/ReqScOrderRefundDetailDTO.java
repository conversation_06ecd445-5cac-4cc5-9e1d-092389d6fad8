package cn.htdt.app.smartcashier.dto.request.refundorder;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 退款详情
 *
 * <AUTHOR>
 */
@Data
public class ReqScOrderRefundDetailDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = 1L;

    /**
     * 售后单号
     */
    private String soReturnNo;

    /**
     * 退款单号
     */
    private String refundmentNo;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 退款金额
     */
    private BigDecimal amount;

    /**
     * 支付交易号
     */
    private String outTradeNo;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 退单方式 字典REFUNDMENT_WAY
     */
    private String refundmentWay;

    /**
     * 退款渠道 字典PAYMENT_CHANNEL
     */
    private String refundmentChannel;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
