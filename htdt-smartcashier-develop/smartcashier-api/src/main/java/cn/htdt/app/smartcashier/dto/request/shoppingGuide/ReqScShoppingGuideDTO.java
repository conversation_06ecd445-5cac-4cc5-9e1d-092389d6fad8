package cn.htdt.app.smartcashier.dto.request.shoppingGuide;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/3/29 15:47
 */
@Data
public class ReqScShoppingGuideDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "导购员编号")
    private String shoppingGuideNo;

    @ApiModelProperty(value = "导购员名称")
    private String shoppingGuideName;

    @ApiModelProperty(value = "排序值")
    private Integer sortValue;

    @ApiModelProperty("数据逻辑删除标识，默认1未删除，2已删除")
    private Integer deleteFlag = 1;

    @ApiModelProperty(value = "提成比例")
    private BigDecimal commissionRatio;
}
