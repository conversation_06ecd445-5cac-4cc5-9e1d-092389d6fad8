package cn.htdt.app.smartcashier.dto.request.points;

import cn.htdt.app.smartcashier.dto.base.ReqAppComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ReqScGetUserPointsDetail extends ReqAppComPageDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "粉丝编号", required = true)
    private String fansNo;

    @ApiModelProperty(value = "变动类型")
    private String operateType;

    @ApiModelProperty(value = "开始时间")
    private String startTimeStr;

    @ApiModelProperty(value = "结束时间")
    private String endTimeStr;

    @ApiModelProperty(value = "积分变动事件(1001:粉丝下单得积分(门店),1002:粉丝下单得积分(网店),1003:关注店铺赠积分,2001:积分兑换礼品,2002:大转盘抽奖,2003:摇奖机抽奖,2004:小猫钓鱼抽奖,2005:售后订单退积分,3001:积分调整)")
    private String pointChangeEvent;
}
