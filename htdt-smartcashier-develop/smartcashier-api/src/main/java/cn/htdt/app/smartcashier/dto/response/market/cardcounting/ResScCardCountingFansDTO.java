package cn.htdt.app.smartcashier.dto.response.market.cardcounting;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.dto.annon.PrivateData;
import cn.htdt.common.dto.enums.DecipherScene;
import cn.htdt.common.dto.enums.HandlerPolicy;
import cn.htdt.common.enums.market.CardPeriodValidityEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 计次卡活动保存DTO
 *
 * <AUTHOR>
 * @date 2022-09-21
 */
@Data
public class ResScCardCountingFansDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "粉丝计次卡编号")
    private String cardFansNo;

    @ApiModelProperty(value = "促销编码")
    private String promotionNo;

    @ApiModelProperty(value = "计次卡编号")
    private String cardCountingNo;

    @ApiModelProperty(value = "计次卡名称")
    private String cardCountingName;

    @ApiModelProperty(value = "粉丝编号")
    private String fanNo;

    @ApiModelProperty(value = "店铺给粉丝的备注名")
    private String fanName;

    @ApiModelProperty(value = "粉丝手机号码")
    private String phone;

    @ApiModelProperty(value = "粉丝手机号码")
    private String dsPhone;

    @ApiModelProperty(value = "计次卡卡号")
    private String cardNo;

    @ApiModelProperty(value = "计次卡次数")
    private Integer cardCounts;

    @ApiModelProperty(value = "计次卡剩余可用次数")
    private Integer cardRemainCounts;

    @ApiModelProperty(value = "计次卡状态 1000可使用 1001已使用 1002已过期 1003已失效")
    private String cardStatus;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "有效类型（1001：长期有效 1002：指定日期 1003:自领取后天数有效）")
    @Converter(enumClass = CardPeriodValidityEnum.class, fieldName = "cardPeriodValidityName", enumField = "type")
    private String cardPeriodValidity;

    @ApiModelProperty(value = "有效类型名称")
    private String cardPeriodValidityName;

    @ApiModelProperty(value = "指定日期开始时间")
    @JsonFormat(pattern = "yyyy.MM.dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime cardEffectiveTime;

    @ApiModelProperty(value = "开始时间结束时间")
    @JsonFormat(pattern = "yyyy.MM.dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime cardInvalidTime;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "门店编号，对应orgId")
    private String storeNo;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "是否可用:默认1，1：可用，其余不可用")
    private Integer disableFlag;

    @ApiModelProperty(value = "使用须知")
    private String useExplain;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime modifyTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
