package cn.htdt.app.smartcashier.dto.response.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单的配送信息响应DTO
 *
 * <AUTHOR>
 * @date 2022-03-01
 */
@Data
public class ResScOrderDeliveryInfoDTO implements Serializable {

    private static final long serialVersionUID = 6532624778145602015L;

    @ApiModelProperty(value = "运费")
    private BigDecimal deliveryFee = BigDecimal.ZERO;

    @ApiModelProperty(value = "收货人姓名")
    private String goodReceiverName;

    @ApiModelProperty(value = "收货人联系号码")
    private String goodReceiverMobile;

    @ApiModelProperty(value = "收货人省份编码")
    private String goodReceiverProvinceCode;

    @ApiModelProperty(value = "收货人省份")
    private String goodReceiverProvince;

    @ApiModelProperty(value = "收货人城市编码")
    private String goodReceiverCityCode;

    @ApiModelProperty(value = "收货人城市")
    private String goodReceiverCity;

    @ApiModelProperty(value = "收货人县区编码")
    private String goodReceiverCountyCode;

    @ApiModelProperty(value = "收货人县区")
    private String goodReceiverCounty;

    @ApiModelProperty(value = "收货人区镇编码")
    private String goodReceiverAreaCode;

    @ApiModelProperty(value = "收货人区镇")
    private String goodReceiverArea;

    @ApiModelProperty(value = "收货人详细地址")
    private String goodReceiverAddress;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}