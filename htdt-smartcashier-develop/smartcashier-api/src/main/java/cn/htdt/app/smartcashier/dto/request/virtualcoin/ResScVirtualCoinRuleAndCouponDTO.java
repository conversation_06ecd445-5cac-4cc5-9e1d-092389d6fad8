package cn.htdt.app.smartcashier.dto.request.virtualcoin;

import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 橙豆规则和优惠券信息返回数据
 *
 * <AUTHOR>
 * @date 2023-1-28 17:39
 */
@Data
public class ResScVirtualCoinRuleAndCouponDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "橙豆规则编号", required = true)
    private String virtualNo;

    @ApiModelProperty(required = true, value = "支付金额")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal payAmount;

    @ApiModelProperty(required = true, value = "橙豆金额")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal availableAmount;

    @ApiModelProperty(required = true, value = "券总数量")
    private Integer couponTotalCount;

    @ApiModelProperty(required = true, value = "券总面额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal couponTotalValue;

    @ApiModelProperty(required = true, value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(required = true, value = "商家名称")
    private String merchantName;

    @ApiModelProperty(required = true, value = "门店编号")
    private String storeNo;

    @ApiModelProperty(required = true, value = "店铺名称")
    private String storeName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
