package cn.htdt.app.smartcashier.dto.response.market.cardcounting;

import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 计次卡活动保存DTO
 *
 * <AUTHOR>
 * @date 2022-09-21
 */
@Data
public class ResScCardCountingDTO {

    @ApiModelProperty(value = "活动编号,编辑活动时必填")
    private String cardCountingNo;

    @ApiModelProperty(value = "活动名称", required = true)
    private String cardCountingName;

    @ApiModelProperty(value = "售价")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal salePrice;

    @ApiModelProperty(value = "原价")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "次数")
    private Integer counts;

    @ApiModelProperty(value = "券有效期（1001：长期有效 1002：指定日期 1003:自购买之日起计算有效期）")
    private String cardPeriodValidity;

    @ApiModelProperty(value = "券有效天数（只有有效期为1003时使用）")
    private Integer cardEffectiveDay;

    @ApiModelProperty(value = "开始日期(yyyy-MM-dd)，有效期为1002时使用")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy.MM.dd", timezone = "GMT+8")
    private LocalDateTime cardEffectiveTime;

    @ApiModelProperty(value = "结束日期(yyyy-MM-dd)，有效期为1002时使用")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy.MM.dd", timezone = "GMT+8")
    private LocalDateTime cardInvalidTime;

    @ApiModelProperty(value = "购买渠道 (1001:一体机开单)")
    private String useChannel;

    @ApiModelProperty(value = "总发行量")
    private Integer totalStockNum;

    @ApiModelProperty(value = "已售数量")
    private Integer salesVolume;

    @ApiModelProperty(value = "已核销（次）")
    private Integer usedCount;

    @ApiModelProperty(value = "使用说明")
    private String useExplain;

    @ApiModelProperty(value = "上下架状态（1：未上架 2：上架） 保存并上架存", required = true)
    private Integer upDownFlag;

    @ApiModelProperty(value = "活动状态文本: 售卖中、已过期、已售罄、未上架")
    private String showStatusMsg;

    @ApiModelProperty(value = "活动状态编码: S001:售卖中、S002:已过期、S003:已售罄、S004:未上架")
    private String showStatusCode;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "店铺编号")
    private String storeNo;

    @ApiModelProperty(value = "可用库存数量，即可售库存")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal availableStockNum;

    /**************商品信息-start*****************/
    @ApiModelProperty(value = "商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "商品原始编号（默认是与goods_no一致，分发到店铺下才不一致）")
    private String originalGoodsNo;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "第三方ID")
    private String thirdGoodsNo;

    @ApiModelProperty(value = "商品形式 1001-普通商品;1002-系列商品")
    private String goodsForm;

    @ApiModelProperty(value = "商品类型 1001-实物商品;1002-虚拟商品;1003-服务商品（仅供平台使用）;1004-称重商品")
    private String goodsType;

    @ApiModelProperty(value = "商品类目ID")
    private String categoryNo;

    @ApiModelProperty(value = "商品品牌ID")
    private String brandNo;

    @ApiModelProperty(value = "商品助记码")
    private String goodsHelpCode;

    @ApiModelProperty(value = "商品来源类型(1001-平台自建;1002-商家自建;1003-店铺自建;1004-一键代发)")
    private String goodsSourceType;

    @ApiModelProperty(value = "计量单位ID")
    private String calculationUnitNo;

    @ApiModelProperty(value = "计量单位符号")
    private String calculationUnitSymbol;

    @ApiModelProperty(value = "计量单位名称")
    private String calculationUnitName;

    @ApiModelProperty(value = "是否有辅计量单位:1-否;2-是;")
    private Integer standardFlag;

    @ApiModelProperty(value = "辅计量单位id")
    private String assistCalculationUnitNo;

    @ApiModelProperty(value = "辅计量单位符号")
    private String assistCalculationUnitSymbol;

    @ApiModelProperty(value = "辅计量单位名称")
    private String assistCalculationUnitName;

    @ApiModelProperty(value = "转换率(辅转主)")
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "是否入仓:1-否;2-是;")
    private Integer warehouseFlag;

    @ApiModelProperty(value = "零售价")
    private BigDecimal retailPrice;
    /**************商品信息-end*******************/
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
