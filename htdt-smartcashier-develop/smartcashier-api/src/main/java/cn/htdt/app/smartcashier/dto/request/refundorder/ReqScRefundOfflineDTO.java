package cn.htdt.app.smartcashier.dto.request.refundorder;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 线下退款
 * <AUTHOR>
 */
@Data
public class ReqScRefundOfflineDTO implements Serializable {


    /**
     * 平台退款单号  对应退款明细编码
     */
    @ApiModelProperty(value = "退款明细编码", required = true)
    @NotBlank(message = "退款明细编码")
    private String refundmentNo;

    /**
     * 线下退单渠道
     */
    @ApiModelProperty(value = "线下退单渠道", required = true)
    private String refundmentChannel;
    /**
     * 上传图片凭证
     */
    @ApiModelProperty(value = "上传图片凭证")
    private List<String> picList;

    /**
     * 售后编号
     */
    @ApiModelProperty(value = "售后编号", required = true)
    @NotBlank(message = "售后编号不能为空")
    private String soReturnNo;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
