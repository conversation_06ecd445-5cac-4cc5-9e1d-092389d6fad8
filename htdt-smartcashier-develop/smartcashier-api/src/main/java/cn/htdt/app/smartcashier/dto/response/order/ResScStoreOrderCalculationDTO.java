package cn.htdt.app.smartcashier.dto.response.order;

import cn.htdt.app.smartcashier.dto.response.market.ResScCouponInfoDTO;
import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.orderprocess.dto.response.ResOrderDTO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 店铺订单计算响应DTO
 *
 * <AUTHOR>
 * @date 2022-12-27
 */
@Data
public class ResScStoreOrderCalculationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("商品总金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal goodsTotalPrice;

    @ApiModelProperty("运费")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal deliveryFee;

    @ApiModelProperty("订单应收总金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal shouldAmount;

    @ApiModelProperty("订单总优惠金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal orderTotalDiscount;

    @ApiModelProperty("满减优惠金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal fullDecrementDiscount;

    @ApiModelProperty("满折优惠金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal fullFoldingDiscount;

    @ApiModelProperty("优惠券集合")
    private List<ResScCouponInfoDTO> listCoupon;

    @ApiModelProperty("优惠券优惠金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal couponDiscountValue;

    @ApiModelProperty("选择的店铺优惠券编码")
    private String userCouponNo;

    @ApiModelProperty("代金券集合")
    private ResScOrderVoucherListDTO listVoucher;

    @ApiModelProperty("代金券优惠金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal voucherDiscountValue;

    @ApiModelProperty("选择的代金券编码集合")
    private List<String> userVoucherNoList;

    @ApiModelProperty("店铺抵扣积分")
    private Integer depPoints;

    @ApiModelProperty(value = "店铺积分抵扣金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal canDeductionAmount;

    @ApiModelProperty(value = "会员积分信息")
    private ResScOrderPointDTO userPoint;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}