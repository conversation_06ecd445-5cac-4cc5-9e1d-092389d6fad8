package cn.htdt.app.smartcashier.dto.response.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * <p>
 * 商家组织信息
 * </p>
 *
 */
@Data
@ApiModel(description = "平台返回实体类")
public class ResFansPageResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(name = "编号")
    private String fanNo;

    @ApiModelProperty(name = "姓名")
    private String name;

    @ApiModelProperty(name = "电话")
    private String phone;

    @ApiModelProperty(name = "头像地址")
    private String headImg;

    @ApiModelProperty(name = "出生日期")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDate birthday;

    @ApiModelProperty(name = "首次归属商家编号")
    private String ascriptionMerchantNumber;

    @ApiModelProperty(name = "首次归属商家名称")
    private String ascriptionMerchantName;

    @ApiModelProperty(name = "首次关注店铺编号")
    private String followStoreNumber;

    @ApiModelProperty(name = "首次关注店铺名称")
    private String followStoreName;

    @ApiModelProperty(name = "首次注册来源")
    private String registeredSource;

    @ApiModelProperty(name = "注册时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime registeredTime;

    @ApiModelProperty(name = "状态 1粉丝 2代理人")
    private Integer type;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
