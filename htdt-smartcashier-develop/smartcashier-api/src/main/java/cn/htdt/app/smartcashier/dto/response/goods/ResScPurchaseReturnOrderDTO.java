package cn.htdt.app.smartcashier.dto.response.goods;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.enums.goods.PurchaseReturnOrderStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 采购退货单列表响应dto
 *
 * @author: 杨子建
 * @date: 2021-08-19
 */
@Data
public class ResScPurchaseReturnOrderDTO implements Serializable {

    private static final long serialVersionUID = 419013478677795949L;

    @ApiModelProperty(value = "退货单编码")
    private String returnCode;

    @ApiModelProperty(value = "采购单编码")
    private String purchaseCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "退货状态21:待提交 22:待审核 23:待出库24:部分出库25:已完成 26:已作废 27:审核不通过")
    @Converter(enumClass = PurchaseReturnOrderStatusEnum.class, fieldName = "returnStatusValue")
    private Integer returnStatus;

    @ApiModelProperty(value = "退货状态展示的值")
    private String returnStatusValue;

    @ApiModelProperty(value = "退货日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime returnDate;

    @ApiModelProperty(value = "商品数量")
    private Integer goodsNum;

    @ApiModelProperty(value = "商品名称")
    private String goodName;

    @ApiModelProperty(value = "可退商品数据")
    private List<ResScPurchaseReturnOrderGoodsDTO> returnGoodsList;

    @ApiModelProperty(value = "不可退商品数据")
    private List<ResScPurchaseReturnOrderGoodsDTO> notReturnGoodsList;
}
