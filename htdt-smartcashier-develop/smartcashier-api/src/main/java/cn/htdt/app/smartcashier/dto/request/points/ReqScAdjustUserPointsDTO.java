package cn.htdt.app.smartcashier.dto.request.points;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class ReqScAdjustUserPointsDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "粉丝号", required = true)
    @NotBlank(message = "粉丝号不能为空")
    private String fansNo;

    @ApiModelProperty(value = "变动数量", required = true)
    @NotNull(message = "调整积分数量不能为空")
    private Integer changeNum;

    @ApiModelProperty(value = "调整原因")
    private String adjustReason;

}
