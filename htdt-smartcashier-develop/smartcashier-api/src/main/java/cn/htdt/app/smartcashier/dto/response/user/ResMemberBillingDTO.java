package cn.htdt.app.smartcashier.dto.response.user;


import cn.htdt.common.dto.BigDecimalSerialize;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2023-04-20
 * @Description 会员开通出参
 **/
@Data
@ApiModel
public class ResMemberBillingDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "购买人编号")
    private String buyerNo;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ExcelProperty("支付交易号")
    private String outTradeNo;

    @ApiModelProperty(value = "订单实收金额,实付为0则无需弹出扫码框")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal realAmount;

}
