package cn.htdt.app.smartcashier.dto.request.virtualcoin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 橙豆优惠券配置
 *
 * <AUTHOR>
 * @since 2022-02-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="ResScVirtualCoinRuleCouponSettingDTO对象", description="橙豆优惠券配置")
public class ResScVirtualCoinRuleCouponSettingDTO extends ResScVirtualCoinRuleAndCouponDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否选中, 1: 未选中, 2: 选中
     */
    @ApiModelProperty(value = "券是否选中（1:未选中 2:选中）")
    private String selectFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
