package cn.htdt.app.smartcashier.dto.request.points;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class ReqScConvertGiftDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "礼品编号", required = true)
    @NotBlank(message = "礼品编号不能为空")
    private String giftNo;

    @ApiModelProperty(value = "手机号")
    @NotBlank(message = "手机号不能为空")
    private String phone;

    @ApiModelProperty(value = "兑换数量")
    @NotNull(message = "兑换数量不能为空")
    private Integer convertNum;
}
