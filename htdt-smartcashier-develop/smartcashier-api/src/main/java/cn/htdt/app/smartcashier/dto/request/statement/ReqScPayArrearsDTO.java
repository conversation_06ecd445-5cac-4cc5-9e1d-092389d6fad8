package cn.htdt.app.smartcashier.dto.request.statement;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 一体机-经营报表-谁欠我钱-补款请求参数DTO
 *
 * <AUTHOR>
 * @since 2021-08-27
 */
@Data
public class ReqScPayArrearsDTO implements Serializable {

    private static final long serialVersionUID = -3432132518524591861L;

    @ApiModelProperty(value = "订单编号", required = true)
    private String orderNo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
